#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/ansi-color-constants.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash

shopt -s nullglob

SCRIPT_DIR="$(cd $(dirname $0) && pwd)"

WORK_DIR="$HOME/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp"
ZIP_WORK_DIR="$WORK_DIR"/zip-temp
INVOICE_WORK_DIR="$WORK_DIR"/invoice-temp
PROCESSING_MODE='none'
FILE_TO_PROCESS=''
PERFORM_ZIP='false'
PERFORM_PROXY_RO_BUILD='false'
INPUT_BUNDLE_ZIP=''
INPUT_ZIP_FILE_NAME=''
INPUT_BUNDLE_DIR=''
DO_ZAP_INPUT='false'

OUTPUT_FILE_PREFIX=''

INPUT_STORE_NAME=''

SINGLE_STORE_FLAG='false'
CUSTOM_BRANCH_NAME=''
PORSCHE_STORE='false'
FOPC_STORE='false'
HALT_OVER_RIDE='false'
BRAND=''
STATE=''
UUID=''
EXCEPTION_REPORT=''
PERFORMED_BY=''
COMPANY_IDS=''
PRE_IMPORT_HALT='false'
SOLVE_DB='true'
SCHEDULER_DB='true'

TRANSFORMS_DIR="$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/runtime/transforms
REPO_DIR="$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI

DEAD_LETTER_DIR=

source "$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/ReynoldsRCI.env

source "$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-library.bash-mixin
source "$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-zip.bash-mixin

source "$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/filetype/closed-ro.bash-mixin
source "$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-proxyinvoice.bash-mixin

# Initialize SRC_SCHEMA for ReynoldsRCI processor
export SRC_SCHEMA="du_dms_reynoldsrci"

function psql_local() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}_model'" \
         --set=DU_ETL_HOME="$DU_ETL_HOME" \
         --set=ON_ERROR_STOP=1 \
         "$@"

    return $?
}

function psql_etl_proxy() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${PROXY_SCHEMA},${SRC_PROXY_SCHEMA}'" \
         --set=ON_ERROR_STOP=1 "$@"
}

function psql_exception_analysis() {
    psql "service=$DU_ETL_SCHEDULER_SERVICE options='-c search_path=process_data'" \
         --set=DU_ETL_HOME="$DU_ETL_HOME" \
         --set=ON_ERROR_STOP=1 \
         "$@"

    return $?
}

function main() {
    parse_options "$@"
    initialize
    if execute_processing_mode "$PROCESSING_MODE"; then
         cleanup "$PROCESSING_MODE"
    else
        move_input_to_deadletter_and_abort "$PROCESSING_MODE"
    fi
}

function execute_processing_mode() {
    proc_mode="${1:?Processing Mode Specification Required}"
    (
        case "$proc_mode" in
                zip) process_zip_file ;;
                post-processed-proxies)
    echo "Checking proxy generation prerequisites..."
    echo " PERFORM_PROXY_RO_BUILD='$PERFORM_PROXY_RO_BUILD'"
    echo "COMPANY_IDS='$COMPANY_IDS'"

    if [[ "$PERFORM_PROXY_RO_BUILD" = 'true' && -n "$COMPANY_IDS" ]]; then
        echo "Starting parallel proxy generation for post-processed data"
        IFS=',' read -ra ALL_COMPANY_IDS <<< "$COMPANY_IDS"
        echo "Processing ${#ALL_COMPANY_IDS[@]} companies: $COMPANY_IDS"

        for COMPANY_ID in "${ALL_COMPANY_IDS[@]}"; do
            say "🔧 Forking proxy generation for COMPANY_ID=$COMPANY_ID"
            fork_company_job "$ZIP_WORK_DIR" "$COMPANY_ID" generate_proxies_from_zip_for_company
        done

        echo "Waiting for all ${#ALL_COMPANY_IDS[@]} proxy generation jobs to complete..."
        wait
        echo " All proxy generation jobs completed successfully!"
    else
        echo " Prerequisites not met for parallel proxy generation:"
        echo "   - PERFORM_PROXY_RO_BUILD must be 'true' (current: '$PERFORM_PROXY_RO_BUILD')"
        echo "   - COMPANY_IDS must not be empty (current: '$COMPANY_IDS')"
        die "COMPANY_IDS is empty or proxy build not enabled"
    fi
    ;;
        esac
    )
    return $?
}

function initialize() {
    mkdir -p "$WORK_DIR"
    clear_dir "$WORK_DIR"

    [[ -d "${DEAD_LETTER_DIR:?Please Pass a Dead-Letter Directory Path}" ]] \
        || die "Dead Letter Directory Must Exist"
}

function move_input_to_deadletter_and_abort() {
    proc_mode="${1:?Processing Mode Specification Required}"
    yell "Moving input to dead-letter bin: $DEAD_LETTER_DIR"

    case "$proc_mode" in
        zip)
            cp "$INPUT_BUNDLE_ZIP" "$DEAD_LETTER_DIR"/
            cleanup "$proc_mode"
            ;;
    esac
}

function cleanup() {
    proc_mode="${1:?Processing Mode Specification Required}"

    progress "Clearing working directory and remove input if requested"
    clear_dir "$WORK_DIR"

    if [[ "$DO_ZAP_INPUT" = 'true' ]]; then
        if [[ -f "$INPUT_BUNDLE_ZIP" ]]; then
            remove_input_zip
        else
            yell "Zap input not implemented for non-zip files"
        fi
    fi
}

function remove_input_zip() {
    [[ -f "$INPUT_BUNDLE_ZIP" ]] || die "Input zip must exist to be removed: $INPUT_BUNDLE_ZIP"
    say "Zapping Input Zip File As Requested"
    rm "$INPUT_BUNDLE_ZIP"
}

function parse_options() {
    OPT_ENV=$(getopt --options c:w: --long output-prefix:,build-proxies-using:,dead-letter:,porsche-store,fopc-store,no-build-proxies,build-proxies,work-dir:,brand:,state:,company_ids:,bundle-dir:,bundle-id:,no-zip,zip,input-dir:,input-zip:,input-store-name:,single-store-flag:,custom-branch-name:,halt-over-ride:,uuid:,exception-report:,performed-by:,pre-import-halt:,solve-db:,scheduler-db:,zap-input -n 'process-json' -- "$@")
    if [[ ! $? = '0' ]]; then
        die "Option Parsing Failed"
    fi

    eval set -- "$OPT_ENV"
    while true; do
        case "$1" in

            --dead-letter)
                DEAD_LETTER_DIR="$2"
                shift 2
                ;;
            --bundle-dir)
                BUNDLE_OUTPUT_DIRECTORY="$2"
                shift 2
                ;;
            --bundle-id)
                BUNDLE_IDENTIFIER="$2"
                shift 2
                ;;
            --input-dir)
                INPUT_BUNDLE_DIR="$2"
                PROCESSING_MODE='full'
                shift 2
                ;;
            --input-zip)
                INPUT_BUNDLE_ZIP="$2"
                INPUT_ZIP_FILE_NAME="$(basename $INPUT_BUNDLE_ZIP)"
                PROCESSING_MODE='zip'
                PERFORM_ZIP='true'
                shift 2
                ;;
            --input-store-name)
                INPUT_STORE_NAME="$2"
                shift 2
                ;;
             --single-store-flag)
                SINGLE_STORE_FLAG="$2"
                shift 2
                ;;
             --custom-branch-name)
                CUSTOM_BRANCH_NAME="$2"
                shift 2
                ;;
            --halt-over-ride)
               HALT_OVER_RIDE=$2
               shift 2
               ;;
            --output-prefix)
                OUTPUT_FILE_PREFIX="$2"
                shift 2
                ;;
            --zap-input)
                DO_ZAP_INPUT='true'
                shift
                ;;
            --porsche-store)
                PORSCHE_STORE='true'
                shift
                ;;
            --fopc-store)
                FOPC_STORE='true'
                shift
                ;;
            --brand)
                BRAND="$2"
                shift 2
                ;;
            --build-proxies)
                PERFORM_PROXY_RO_BUILD='true'
                shift
                ;;
            --build-proxies-using)
                POST_PROCESSED_ZIP_FILE="$2"
                PROCESSING_MODE="post-processed-proxies"
                shift 2
                ;;
            --no-build-proxies)
                PERFORM_PROXY_RO_BUILD='false'
                shift
                ;;
            --no-zip)
                PERFORM_ZIP='false'
                shift
                ;;
            --zip)
                PERFORM_ZIP='true'
                shift
                ;;
            --work-dir)
                say "Work Dir Change: $2"
                WORK_DIR="$2"
                shift 2
                ;;
            --state)
                STATE="$2"
                shift 2
                ;;
            --uuid)
                UUID="$2"
                echo "UUID: $UUID"
                shift 2
                ;;
            --company_ids)
                COMPANY_IDS="$2"
                shift 2
                ;;
            --exception-report)
                EXCEPTION_REPORT="$2"
                echo "EXCEPTION_REPORT: $EXCEPTION_REPORT"
                shift 2
                ;;
            --performed-by)
                PERFORMED_BY="$2"
                echo "PERFORMED_BY: $PERFORMED_BY"
                shift 2
                ;;
            --pre-import-halt)
                PRE_IMPORT_HALT="$2"
                echo "PRE_IMPORT_HALT: $PRE_IMPORT_HALT"
                shift 2
                ;;   
            --solve-db)
                SOLVE_DB="$2"
                echo "SOLVE_DB: $SOLVE_DB" 
                shift 2
                ;;
            --scheduler-db)
                SCHEDULER_DB="$2"
                echo "SCHEDULER_DB: $SCHEDULER_DB"
                shift 2
                ;;    
            --) shift ; break ;;
            *) die "Unrecognized Argument $1"
        esac
    done
}

main "$@"

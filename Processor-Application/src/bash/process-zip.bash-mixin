# Sourced by process-json; provides processing implementation functions for iterating
# through the provided zip file.

NAME_JSONCONV=jsonconversions
NAME_EXT_ARC=extraction-archive
NAME_PROC_LOG=processing-log
NAME_EXTR_LOG=extraction-log
NAME_PROC_RST=processing-result
NAME_IMPORT_FILES=import-files


WORK_DIR_EXTRACTION_LOG_DIR="$ZIP_WORK_DIR"/"$NAME_EXTR_LOG"
WORK_DIR_EXTRACTION_ARCHIVE_DIR="$ZIP_WORK_DIR"/"$NAME_EXT_ARC"
WORK_DIR_PROCESSING_LOG_DIR="$ZIP_WORK_DIR"/"$NAME_PROC_LOG"
WORK_DIR_PROCESSING_RESULTS_DIR="$ZIP_WORK_DIR"/"$NAME_PROC_RST"
WORK_DIR_DEAD_LETTER="$ZIP_WORK_DIR"/dead-letter
WORK_DIR_IMPORT_FILES_DIR="$ZIP_WORK_DIR"/"$NAME_IMPORT_FILES"

CCC_EXCEPTION_FILE_DIR="$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/filetype
INVALID_MISC_PAYTYPE_EXCEPTION='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv'
ESTIMATE='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/estimate.csv'
PUNCH_TIME_MISSING='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv'
PUNCH_TIME_MISSING_PERCENTAGE='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing_percentage.txt'
SUFFIXED_INVOICES_FILEPATH='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/suffixed-invoices.csv'
GROUPED_SUFFIX_INVOICE_FILEPATH='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/grouped_suffixinvoice.csv'
CLOSED_IN_EXCEPTION='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/exception-closed-invoices.csv'
EXTRAROS_IN_XML_EXCEPTION='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv'
IM_OPEN_CLOSED_RCI_EXCEPTION='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/im_opened_closed_rci_ros.csv'
DELETED_ROS_CSV_FILEPATH='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/removed_ros.csv'
CORE_MISMATCH_CSV_FILE_PATH='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/core-mismatch.csv' 

source "$DU_ETL_HOME"/config-generation/process.bash
source "$DU_ETL_HOME"/scheduler_halt/scheduler_halt.sh
INPUT_TYPE="json"
SAVE_FILE=false
function calculate_CCC_exception(){
    # echo "Processor status: 5/21 Calculate CCC exception Started"
    # sleep 1
    SINGLE_RO_JSON_DIR="$JSON_CONVERSION_WORK_DIR"/single-ro-json
    python3 "$CCC_EXCEPTION_FILE_DIR"/"predict_ccc_exception.py" "$SINGLE_RO_JSON_DIR"/  "$WORK_DIR_PROCESSING_RESULTS_DIR"/3C_Exception_Report.csv
    # echo "Processor status: 5/21 Calculate CCC exception Completed"
    # sleep 1
}


function process_zip_file() {
    echo "Processor status: Processing Started"
    sleep 1
    echo "HALT_OVER_RIDE:${HALT_OVER_RIDE}"
    JSON_CONVERSION_WORK_DIR="$ZIP_WORK_DIR"/"$NAME_JSONCONV"
    SINGLE_RO_JSON_DIR="$JSON_CONVERSION_WORK_DIR"/single-ro-json
    COMMON_RO_JSON_DIR="$JSON_CONVERSION_WORK_DIR"/common-ro-json
    
    [[ -f "$INPUT_BUNDLE_ZIP" ]] || die "Zip file required for zip mode: $INPUT_BUNDLE_ZIP"
    say "Processing Input Zip Archive: $INPUT_BUNDLE_ZIP"
    echo "Processor status: 1/16 Unzipping Input to Work Started"
    sleep 1
    prepAndUnzipGlobalInputZipFile
    echo "Processor status: 1/16 Unzipping Input to Work Completed"
    sleep 1    
    echo "Processor status: 2/16 Creating Schema from Model Started"
    sleep 1 
    create_schema_from_model                              || die "Could not initialize schema"
    echo "Processor status: 2/16 Creating Schema from Model Completed"
    sleep 1
    save_scheduler_id                                     || die "Could not Save UUID"
    generate_jq_transforms                                || die "Failed to create jq transforms"
    echo "Processor status: 3/16 Iterating Over Zip File Contents Started"
    sleep 1    
    processZipFileContents                                || die "Content Processing Failed"
    echo "Processor status: 4/16 Loading Individual ROs Started"
    sleep 1      
    load_individual_json_files                            || die "Could not load at least one json repair order"
    echo "Processor status: 4/16 Loading Individual ROs Completed"
    sleep 1
       
    calculate_CCC_exception                               || die "Exception Processing Failed"
    echo "Processor status: 5/16 Detecting Problematic ROs Started"
    sleep 1     
    isolate_problematic_ros                               || die "Problematic RO isolation failed"
    echo "Processor status: 5/16 Detecting Problematic ROs Completed"
    sleep 1    
    isolate_problematic_ros                               || die "Problematic RO isolation failed"

    if [[ "$SCHEDULER_DB" = 'true' ]]; then
        report_excluded_results                           || die "Excluded report generation failed"
    else
        dummy_report_excluded_results                     || die "Dummy Excluded report generation failed"
    fi

    generate_exception_report                             || die "Exception report generation failed"
    
    enumerate_present_ro                                  || die "RO Number Enumeration Failed"
    
    findMissingROFromInvoiceMaster                        || die "Could not load Missing RO  with respective to Invoice master CSV file"

    persist_and_export_data_and_schema "$ZIP_WORK_DIR"    || die "Persistence and exporting failed"
    
    export_job_data_to_csv                                || die "Exporting job data Failed"
    echo "Processor status: 6/16 Detecting Open/Void RO data and reporting Started"
    sleep 1    
    report_open_or_void_ros                               || die "Open/Void RO report generation failed"
    echo "Processor status: 6/16 Detecting Open/Void RO data and reporting Completed"
    sleep 1
    
    check_missing_against_extraction                      || die "Failed to check missing against original extraction"
    
    generate_scheduler_id_file                            || die "Scheduler id file generation failed"

    echo "Processor status: 7/16 Generate Config File Started"
    sleep 1
    if [[ "$SOLVE_DB" = 'true' ]]; then
        if [[ "$COMPANY_IDS" ]]; then
            IFS=',' read -ra ALL_COMPANY_IDS <<< "$COMPANY_IDS"
            for config_company_id in "${ALL_COMPANY_IDS[@]}"; do
                echo "Processing config for COMPANY_ID: $config_company_id"
                # Skip invalid company IDs (0 or empty)
                if [[ -z "$config_company_id" || "$config_company_id" == "0" ]]; then
                    echo "Skipping invalid COMPANY_ID: '$config_company_id'"
                    continue
                fi
                # Process valid company IDs here if needed
                wait
            done
            # Always generate mock config file when COMPANY_IDS is present
            generate_mock_config_file
        fi
    else
        generate_mock_config_file
        echo "Processor status: 7/16 Generate Config File Completed"
    sleep 1
    fi

    # Auto-detect multiple company IDs for parallel proxy generation
    detect_and_setup_parallel_proxy_generation

    echo "Processor status: 8/16 Load Fron Scheduler DB Started"
    sleep 1
    load_data_from_scheduler_database                     || die "Load to scheduler database Failed"
    echo "Processor status: 8/16 Load Fron Scheduler DB Completed"
    sleep 1

    echo "Processor status: 9/16 Compressing Directory Started"
    sleep 1
    compress_extraction_directory
    compress_jsonconversion_directory
    compress_extractionlog_directory
    compress_processinglog_directory
    compress_or_remove_deadletter_directory
    compress_import_files_directory
    echo "Processor status: 9/16 Compressing Directory Completed"
    sleep 1
    
    if [[ "$EXCEPTION_REPORT" = 'true' ]]; then
        echo "Processor status: 10/16 Generate Exception Analysis Started"
        sleep 1
        exception_report_analysis "$WORK_DIR_PROCESSING_RESULTS_DIR" "$WORK_DIR_PROCESSING_RESULTS_DIR" || echo "Exception report analysis generation failed"
        echo "Processor status: 10/16 Generate Exception Analysis Completed"
        sleep 1
        echo "Processor status: 11/16 Loading Exception Analysis Started"
        sleep 1
        load_exception_analysis_report                              || die "Exception analysis report loading failed"
        echo "Processor status: 11/16 Loading Exception Analysis Completed"
        sleep 1
    fi

    if [[ "$PERFORM_PROXY_RO_BUILD" = 'true' ]]; then
        echo "Processor status: 12/16 Generating Proxy Repair Orders per Request Started"
        sleep 1

        # Debug: Check variable states before proxy generation
        echo "🔍 DEBUG: Variable states before proxy generation:"
        echo "   COMPANY_IDS='$COMPANY_IDS'"
        echo "   COMPANY_ID='$COMPANY_ID'"
        echo "   PERFORM_PROXY_RO_BUILD='$PERFORM_PROXY_RO_BUILD'"

        if [[ -n "$COMPANY_IDS" ]]; then
            echo " Starting parallel proxy generation for multiple companies"
            echo " Company IDs: $COMPANY_IDS"
            IFS=',' read -ra ALL_COMPANY_IDS <<< "$COMPANY_IDS"
            echo " Launching ${#ALL_COMPANY_IDS[@]} parallel proxy generation jobs..."

            # Set BASE_SRC_SCHEMA for parallel processing
            # For ReynoldsRCI, the base schema is always du_dms_reynoldsrci
            export BASE_SRC_SCHEMA="du_dms_reynoldsrci"

            echo " Using BASE_SRC_SCHEMA: $BASE_SRC_SCHEMA"

            for current_company_id in "${ALL_COMPANY_IDS[@]}"; do
                echo "🔹 Launching proxy generation job for COMPANY_ID=$current_company_id"
                # 🔹 launch each job in the background
                fork_company_job "$ZIP_WORK_DIR" "$current_company_id" generate_proxies_from_zip_for_company
            done

            echo " Waiting for all ${#ALL_COMPANY_IDS[@]} proxy generation jobs to complete..."
            wait   #  wait until all background jobs finish
            echo " All proxy generation jobs completed successfully!"
        else
            echo " Single company proxy generation mode"
            echo "🔍 DEBUG: Reached single company fallback"
            echo "   Original COMPANY_IDS was: '$COMPANY_IDS'"
            echo "   Current COMPANY_ID is: '${COMPANY_ID:-empty}'"

            if [[ -n "$COMPANY_ID" && "$COMPANY_ID" != "0" ]]; then
                echo " Using COMPANY_ID=$COMPANY_ID"
                generate_proxies_from_zip_for_company "$COMPANY_ID"
            else
                echo " ERROR: No valid COMPANY_ID found (current: '${COMPANY_ID:-empty}')"
                echo " This indicates a critical issue with company ID processing"
                echo " Expected either COMPANY_IDS for parallel processing or valid COMPANY_ID for single processing"
                die "Invalid COMPANY_ID state - cannot proceed with proxy generation"
            fi
        fi

        echo "Processor status: 12/16 Generating Proxy Repair Orders per Request Completed"
        sleep 1
        echo "Processor status: 13/16 Extracting Text ROs to TSV Started"
        sleep 1
        extract_proxy_to_tsv "$ZIP_WORK_DIR"/proxy-invoice/text "$WORK_DIR_PROCESSING_RESULTS_DIR" || echo "Proxy extraction to tsv failed"
        echo "Processor status: 13/16 Extracting Text ROs to TSV Completed"
        sleep 1
        echo "Processor status: 14/16 Compressing Proxy Directory Started"
        sleep 1
        compress_proxy_output "$ZIP_WORK_DIR"
        compress_dual_proxy_output "$ZIP_WORK_DIR"
        echo "Processor status: 14/16 Compressing Directory Completed"
        sleep 1
    fi

    echo "Processor status: 15/16 Pre-import Halt Detection Started"
    sleep 1    
    pre_import_halt_detection  || die "Pre-import halt detection failed"
    echo "Processor status: 15/16 Pre-import Halt Detection Completed"
    sleep 1
    alter_schema "$INPUT_TYPE" || die "Altering schema failed"
    echo "Processor status: 16/16 Moving Work to Bundle Directory Started"
    sleep 1
    distribute_and_clear_work
    echo "Processor status: 16/16 Moving Work to Bundle Directory Completed"
    sleep 1
    echo "Processor status: Processing Completed"
    sleep 1
}

function save_scheduler_id() {
progress "Saving UUID to model"
    psql_local --quiet \
               --set=UUID="$UUID" \
               >/dev/null \
<<'SQL'
CREATE TABLE etl_uuid_detail (
    unique_id       text NOT NULL
);
INSERT INTO etl_uuid_detail(unique_id) VALUES(:'UUID');
SQL

}

function detect_and_setup_parallel_proxy_generation() {
    #  Auto-detect multiple company IDs from config files and enable parallel proxy generation
    local config_dir="$WORK_DIR_PROCESSING_RESULTS_DIR"
    local detected_company_ids=()

    echo " Scanning for configuration files in: $config_dir"

    # Look for config files in the processing results directory
    if [[ -d "$config_dir" ]]; then
        echo " Config directory exists, searching for config_*.bash files..."

        # List all config files for debugging
        local config_files=($(find "$config_dir" -name "config_*.bash" 2>/dev/null))
        echo " Found ${#config_files[@]} config files: ${config_files[*]}"

        # Extract company IDs from all config_*.bash files
        while IFS= read -r -d '' config_file; do
            if [[ -f "$config_file" ]]; then
                echo " Examining config file: $(basename "$config_file")"
                # Extract COMPANY_ID from the config file
                local company_id=$(grep '^COMPANY_ID=' "$config_file" | cut -d'"' -f2 | head -1)
                echo "   Found COMPANY_ID: '$company_id'"
                if [[ -n "$company_id" && "$company_id" != "0" ]]; then
                    detected_company_ids+=("$company_id")
                    echo " Added COMPANY_ID: $company_id from $(basename "$config_file")"
                else
                    echo "  Skipping invalid COMPANY_ID: '$company_id'"
                fi
            fi
        done < <(find "$config_dir" -name "config_*.bash" -print0 2>/dev/null)
    else
        echo " Config directory does not exist: $config_dir"
    fi

    echo " Total detected company IDs: ${#detected_company_ids[@]}"
    echo " Company IDs: ${detected_company_ids[*]}"

    # If multiple company IDs detected, set up parallel processing
    if [[ ${#detected_company_ids[@]} -gt 1 ]]; then
        # Remove duplicates and create comma-separated list
        local unique_company_ids=($(printf '%s\n' "${detected_company_ids[@]}" | sort -u))
        export COMPANY_IDS=$(IFS=','; echo "${unique_company_ids[*]}")
        export PERFORM_PROXY_RO_BUILD='true'

        echo " Multiple company IDs detected: ${#unique_company_ids[@]} companies"
        echo " Setting COMPANY_IDS='$COMPANY_IDS'"
        echo " Setting PERFORM_PROXY_RO_BUILD='true'"
        echo " Parallel proxy generation enabled for companies: $COMPANY_IDS"
    elif [[ ${#detected_company_ids[@]} -eq 1 ]]; then
        echo " Single company ID detected: ${detected_company_ids[0]}"
        export COMPANY_ID="${detected_company_ids[0]}"
        # Keep existing PERFORM_PROXY_RO_BUILD setting
        echo " single proxy generation enabled for companies: $COMPANY_IDS"
    else
        echo "  No valid company IDs detected in config files"
        echo " This may be normal if config files haven't been generated yet"
    fi
}

function generate_scheduler_id_file(){
    # echo "Processor status: 11/21 Generating scheduler id Started"
    # sleep 1
    cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
    echo "${UUID}" >> "scheduler-id.txt"
    # echo "Processor status: 11/21 Generating scheduler id Completed"
    # sleep 1
}


function processZipFileContents() {
    # echo "Processor status: 3/21 Iterating Over Zip File Contents Started"
    sleep 3
    progress "Iterating Over Zip File Contents"
    export DU_ETL_DEBUG_TARGET="$WORK_DIR_PROCESSING_LOG_DIR"/Processing.log
    (
        cd "$ZIP_WORK_DIR"
        mkdir "$WORK_DIR_EXTRACTION_LOG_DIR"
        mkdir "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"
        mkdir "$WORK_DIR_PROCESSING_LOG_DIR"
        mkdir "$WORK_DIR_PROCESSING_RESULTS_DIR"
        mkdir "$WORK_DIR_DEAD_LETTER"
        
        for file in ./*.csv ; do 
            mv "$file" "${file// /_}"
	    done

        for file in ./*.csv ; do 
            filename="$(basename $file)"
            filename="${filename%.*}"
            upper_filename="${filename^^}"
            mv -- "$filename.csv" "${upper_filename}.csv"
        done

        for filename in ./*; do
            case "$(basename $filename)" in
                extraction-log)     : ;;
                extraction-archive) : ;;
                processing-log)     : ;;
                processing-result)  : ;;
                dead-letter)        : ;;
                Session*.log)               process_session_log                 "$filename" ;;
                config_*.bash)              relocate_to_config_dir              "$filename" ;;
                job*.txt)                   relocate_to_config_dir              "$filename" ;;
                
                *.xml)                      process_closed_ro_json              "$filename" ;;
                
                *_INVOICE_MASTER*.csv)      process_invoice_master_csv          "$filename" ;;

                *_INVOICE_MASTER*.CSV)      process_invoice_master_csv          "$filename" ;;
                
                *_LABOR_DETAIL*.csv)        process_labor_detail_csv            "$filename" ;;
                
                *_PART_DETAIL*.csv)         process_parts_detail_csv            "$filename" ;;
                
                *.csv)                      copy_to_archive_directory           "$filename" ;;
                
                *.err)              die  "Error File Present: $filename"                    ;;
                
                *)                  yell "File $filename Not Recognized"                    ;;
            esac
            proc_result=$?
            if [[ "$proc_result" != 0 ]]; then
                die "File Processing Failed"
            fi
        done
    ) || die "Zip File Processing Failed"
    export DU_ETL_DEBUG_TARGET=/dev/null
    return $?
    # echo "Processor status: 3/21 Iterating Over Zip File Contents Completed"
    # sleep 1
}
function relocate_to_config_dir() {
    mv "$1" "$WORK_DIR_PROCESSING_RESULTS_DIR"/
}
function relocate_to_log_dir() {
    mv "$1" "$WORK_DIR_EXTRACTION_LOG_DIR"/
}

function prepAndUnzipGlobalInputZipFile() {
    # echo "Processor status: 1/21 Unzipping Input to Work Started"
    # sleep 1
    progress "Unzipping Input to Work $ZIP_WORK_DIR"
    mkdir -p "$ZIP_WORK_DIR"
    clear_dir "$ZIP_WORK_DIR"
    unzip -j -q "$INPUT_BUNDLE_ZIP" -d "$ZIP_WORK_DIR"
    # echo "Processor status: 1/21 Unzipping Input to Work Completed"
    # sleep 1
}

function process_invoice_master_csv() {
    psql_local  -c "\copy etl_invoicemaster FROM $1 WITH (FORMAT csv, HEADER true)"
    cp "$1" processing-result/
    mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/
   psql_local <<SQL
   \copy (SELECT * FROM etl_invoicemaster) TO /home/<USER>/reynoldsrci_invoice_master.csv
SQL
}


function process_labor_detail_csv() {
    psql_local  -c "\copy etl_source_labor FROM $1 WITH (FORMAT csv, HEADER true)"
    mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/
   psql_local <<SQL
   \copy (SELECT * FROM etl_source_labor) TO /home/<USER>/reynoldsrci_labor_detail.csv
SQL
}


function process_parts_detail_csv() {
    psql_local  -c "\copy etl_source_parts FROM $1 WITH (FORMAT csv, HEADER true)"
    mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/
   psql_local <<SQL
   \copy (SELECT * FROM etl_source_parts) TO /home/<USER>/reynoldsrci_parts_detail.csv
SQL
}


function copy_to_archive_directory() {
    mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/
}

function compress_extraction_directory() {
    (
        cd "$ZIP_WORK_DIR"
        zip -q -mr "$NAME_EXT_ARC".zip ./"$NAME_EXT_ARC"
    )
}

function compress_jsonconversion_directory() {
    (
        if [[ -d "$JSON_CONVERSION_WORK_DIR" ]]; then
            cd "$ZIP_WORK_DIR"
            zip -q -mr "$NAME_JSONCONV".zip ./"$NAME_JSONCONV"
        fi
    )
}

function compress_processinglog_directory() {
    (
        cd "$ZIP_WORK_DIR"
        zip -q -mr "$NAME_PROC_LOG".zip ./"$NAME_PROC_LOG"
    )
}

function compress_extractionlog_directory() {
    (
        cd "$ZIP_WORK_DIR"
        zip -q -mr "$NAME_EXTR_LOG".zip ./"$NAME_EXTR_LOG"
    )
}

function compress_or_remove_deadletter_directory() {
    (
        if has_files "$WORK_DIR_DEAD_LETTER"; then
            cd "$ZIP_WORK_DIR"
            zip -q -mr 'dead-letter.zip' ./'dead-letter'
        else
            rmdir "$WORK_DIR_DEAD_LETTER"
        fi
    )
}

function compress_import_files_directory() {
    (
        cd "$ZIP_WORK_DIR"
        zip -q -jmr "$NAME_IMPORT_FILES".zip ./"$NAME_IMPORT_FILES"
        rmdir ./"$NAME_IMPORT_FILES"
    )
}

function distribute_and_clear_work() {
    # echo "Processor status: 21/21 Moving Work to Bundle Directory Started"    
    # sleep 1
    if [[ "${FOPC_STORE:-}" != "true" ]]; then
        if [[ -z "$INPUT_STORE_NAME" ]]; then
            echo "No webhook input file found!"
        else
            move_input_store_files_to_archive
        fi
    fi    
    progress "Moving Work to Bundle Directory"
    if [[ "$PERFORM_ZIP" = 'true' ]]; then
        distribute_as_zip_file
    else
        distribute_as_directory
    fi
    progress "Distribute Function Result: $?"
    clear_dir "$ZIP_WORK_DIR"
    # echo "Processor status: 21/21 Moving Work to Bundle Directory Completed"
    # sleep 1
}

function distribute_as_zip_file() {
    (
        cd "$ZIP_WORK_DIR"
        replaceString=".zip"
        newInputFileName=$(echo "${INPUT_ZIP_FILE_NAME/.gz/$replaceString}")
        zip -qr "$WORK_DIR"/"$INPUT_ZIP_FILE_NAME" ./*
        mv "$WORK_DIR"/"$INPUT_ZIP_FILE_NAME" "${BUNDLE_OUTPUT_DIRECTORY:?Specify bundle directory first}"/"${OUTPUT_FILE_PREFIX}""$(basename $INPUT_ZIP_FILE_NAME)"
    )
    return $?
}

function distribute_as_directory() {
    (
        cd "$ZIP_WORK_DIR"/..
        mv ./"$(basename $ZIP_WORK_DIR)" \
        "${BUNDLE_OUTPUT_DIRECTORY:?Specify bundle directory first}"/"$(basename $INPUT_ZIP_FILE_NAME .zip)"
    )
    return $?
}

function process_session_log() {
    mv "$1" "$WORK_DIR_EXTRACTION_LOG_DIR"/
}

function move_to_work_dead_letter() {
    mkdir -p "$WORK_DIR_DEAD_LETTER"
    mv "$1" "$WORK_DIR_DEAD_LETTER"
}

function enumerate_present_ro() {
    # # echo "Processor status: 9/25 Enumerating ROs Started"
    # sleep 1
    progress "Enumerating ROs"
    (
        cd "$SINGLE_RO_JSON_DIR"
        find . -type f -exec basename {} .json \; | sort > "$WORK_DIR_PROCESSING_RESULTS_DIR"/single-ro-json-present-list.txt
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet <<SQL
        set client_min_messages = warning;
        DROP TABLE IF EXISTS present_repair_order_number;
        DROP TABLE IF EXISTS repair_order_number_summary;
        CREATE TABLE present_repair_order_number (
            invoice_number text primary key
        );
        \copy present_repair_order_number from single-ro-json-present-list.txt with (format csv, header false)
        select count(*) AS count_all, count(*) filter (where invoice_number !~ '^\d+$') AS count_non_numeric
          from present_repair_order_number;

        CREATE UNLOGGED TABLE repair_order_number_summary AS
        WITH
        split_ranges AS (
             SELECT min(invoice_number::int) AS first_invoicenumber, max(invoice_number::int) AS last_invoicenumber
               FROM present_repair_order_number
              WHERE invoice_number ~ '^\d+$'
        ),
        explode_invoice_numbers AS (
              SELECT generate_series(first_invoicenumber, last_invoicenumber, 1) AS invoice_number
                FROM split_ranges
        ),
        present AS (
            SELECT invoice_number::int, true AS is_present
              FROM present_repair_order_number
             WHERE invoice_number ~ '^\d+$'
        ),
        combined AS (
            SELECT *, invoice_number::integer / 100 AS inv_grp FROM explode_invoice_numbers NATURAL LEFT JOIN present
        )
        SELECT
          (sum("Present #") OVER () / sum("Total #") OVER ())::numeric(10,2) AS "Overall %"
        , sum("Present #") OVER () AS "Included #"
        , sum("Total #") OVER () AS "Overall #"
        , ("Present #" / "Total #"::numeric)::numeric(10,2) AS "Tranche %"
        , *
        FROM (
            SELECT
                  sum(CASE WHEN is_present IS TRUE THEN 1 ELSE 0 END) AS "Present #"
                , sum(CASE WHEN is_present IS TRUE THEN 0 ELSE 1 END) AS "Missing #"
                , max(invoice_number) - min(invoice_number) + 1 AS "Total #"
                , min(invoice_number) AS "Starting"
                , max(invoice_number) AS "Ending"
                , array_agg(DISTINCT CASE WHEN is_present IS NOT TRUE
                                          THEN invoice_number
                                          ELSE NULL END) AS "Missing Invoices"
                , inv_grp AS "Inv Grp ID"
            FROM combined
            GROUP BY inv_grp
            ORDER BY inv_grp
        ) overall_calc
        ;

        \o 'removed_ros.csv'
            COPY (
        WITH grouped_company AS (
        SELECT array_agg(DISTINCT "AreaNumber"||'S') AS store
        FROM du_dms_reynoldsrci_model.etl_store_detail
        )
        ,get_start_end_date AS (
            SELECT MIN(opendate::date) AS startdate, MAX(opendate::date) AS enddate
            FROM du_dms_reynoldsrci_model.etl_invoicemaster im
                JOIN grouped_company gc ON im.dms_store_id = ANY (gc.store)   
        )
        ,get_ros AS(
        select "roNumber", "RoCreateDate", "FinalPostDate"  FROM du_dms_reynoldsrci_model.etl_head_detail h  
            WHERE ("RoCreateDate"::date < (SELECT startdate FROM get_start_end_date)
            OR "RoCreateDate"::date > (SELECT enddate FROM get_start_end_date))
            AND (SELECT startdate FROM get_start_end_date) IS NOT null
        )
        select * from get_ros) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

        WITH grouped_company AS (
        SELECT array_agg(DISTINCT "AreaNumber"||'S') AS store
        FROM du_dms_reynoldsrci_model.etl_store_detail
        )
        ,get_start_end_date AS (
            SELECT MIN(opendate::date) AS startdate, MAX(opendate::date) AS enddate
            FROM du_dms_reynoldsrci_model.etl_invoicemaster im
                JOIN grouped_company gc ON im.dms_store_id = ANY (gc.store)   
        )
        ,get_ros AS(
        select "roNumber", "RoCreateDate", "FinalPostDate"  FROM du_dms_reynoldsrci_model.etl_head_detail h  
            WHERE ("RoCreateDate"::date < (SELECT startdate FROM get_start_end_date)
            OR "RoCreateDate"::date > (SELECT enddate FROM get_start_end_date))
            AND (SELECT startdate FROM get_start_end_date) IS NOT null
        )
        ,delete_parts AS(
        DELETE FROM du_dms_reynoldsrci_model.etl_parts_detail
        USING get_ros
        WHERE du_dms_reynoldsrci_model.etl_parts_detail."roNumber" = get_ros."roNumber"
        )
        ,delete_job AS(
        DELETE FROM du_dms_reynoldsrci_model.etl_job_detail
        USING get_ros
        WHERE du_dms_reynoldsrci_model.etl_job_detail."roNumber" = get_ros."roNumber"
       )
        DELETE FROM du_dms_reynoldsrci_model.etl_head_detail
        USING get_ros
        WHERE du_dms_reynoldsrci_model.etl_head_detail."roNumber" = get_ros."roNumber";


         \o 'misc_paytype_exception.csv'
        COPY (
            SELECT
                fd."roNumber"                                               AS "RoNumber",
                fd."JobNo"                                                  AS "JobNo",
                fd."PayType"                                                AS "PayType",
                fd."CodeAmt"                                                AS "CodeAmt",
                f."PayType"                                                 AS "PayTypeTotal",
                f."TotalAmt"                                                AS "TotalAmt",
                fd."MiscCode"                                               AS "OtherCode",
                fd."MiscCodeDesc"                                           AS "Other_Description",
                fd."AutoCalc"                                               AS "AutoCalc"
            FROM etl_misc_detail fd
            LEFT JOIN etl_misc_detail1 f
                ON fd."roNumber" = f."roNumber" AND fd."JobNo"= f."JobNo"
            WHERE  fd."CodeAmt" IS NOT NULL AND f."TotalAmt" IS NOT NULL AND fd."CodeAmt" <> f."TotalAmt"
            AND f."TotalAmt" :: numeric<>0 and fd."CodeAmt"::numeric <>0
            GROUP BY fd."roNumber",fd."JobNo",fd."CodeAmt",  fd."MiscCode",fd."MiscCodeDesc",f."PayType",fd."AutoCalc",f."TotalAmt",fd."PayType"
            ORDER BY fd."roNumber",fd."JobNo"
             )TO stdout WITH (FORMAT csv, HEADER true);
        \o 'open_ros.csv'
        COPY (
           WITH open_ros AS (
             	   SELECT "roNumber",
	                (SELECT "StoreNumber"::text||'S' FROM du_dms_reynoldsrci_model.etl_store_detail LIMIT 1) AS "Store" 
	                FROM du_dms_reynoldsrci_model.etl_head_detail WHERE "FinalPostDate" IS NULL
	                UNION
	                SELECT invoicenumber, dms_store_id  FROM du_dms_reynoldsrci_model.etl_invoicemaster WHERE closedate IS NULL AND 
	                dms_store_id =  (SELECT "StoreNumber"::text||'S' FROM du_dms_reynoldsrci_model.etl_store_detail LIMIT 1) 
	                AND invoicenumber NOT IN (SELECT "roNumber" FROM du_dms_reynoldsrci_model.etl_head_detail)
	                GROUP BY invoicenumber, dms_store_id
                )
                SELECT * FROM open_ros ORDER BY "roNumber"
            )TO stdout WITH (FORMAT csv, HEADER true);
        
        \o 'omitted_ros.csv'
        COPY (
             SELECT "roNumber" AS "RO Number", dms_store_id AS "Store" FROM du_dms_reynoldsrci_model.etl_head_detail JOIN (
                SELECT dms_store_id, invoicenumber FROM du_dms_reynoldsrci_model.etl_invoicemaster GROUP BY dms_store_id,invoicenumber) i ON
                invoicenumber = "roNumber"  WHERE 
                dms_store_id <>  (SELECT
                    "AreaNumber"::text||'S'
                FROM du_dms_reynoldsrci_model.etl_store_detail LIMIT 1)
                ORDER BY dms_store_id, "roNumber"
             )TO stdout WITH (FORMAT csv, HEADER true);

        \o 'repair-order-sequence-non-numeric.txt'
        \C 'Non-Numeric Repair Order Numbers'
        SELECT * FROM present_repair_order_number WHERE invoice_number !~ '^\d+$';

        \o repair-order-sequence-summary.txt
        \C 'Numeric-only Repair Order Number Summary'
        SELECT "Overall %", "Included #", "Overall #",
               "Tranche %", "Present #", "Missing #", "Total #",
               "Starting", "Ending"
          FROM repair_order_number_summary
         WHERE "Present #" > 0;

        \C
        \t
        \pset format unaligned
        \o 'repair-order-sequence-missing-numerics.txt'
        SELECT invoice_number
          FROM (
            SELECT unnest("Missing Invoices") AS invoice_number
              FROM repair_order_number_summary
             WHERE "Present #" > 0
             ORDER BY 1
               ) AS src
         WHERE invoice_number IS NOT NULL;

        \o 'estimate.csv'
        SELECT *
         FROM du_dms_reynoldsrci_model.etl_misc_detail;
        \o 'punch_time_missing.csv'
        COPY (SELECT et."roNumber",et."JobNo", "TechNo","TechName","TechHrs" FROM du_dms_reynoldsrci_model.etl_tech_detail et JOIN du_dms_reynoldsrci_model.etl_job_detail ej ON 
        et."roNumber" = ej."roNumber" AND et."JobNo" = ej."JobNo" WHERE  "TechStartTime" = '00:00:00' AND  "TechFinishTime" = '00:00:00' AND "TechHrs"::numeric <> 0
        AND "PayType" = 'Cust')TO stdout WITH (FORMAT csv, HEADER true);
        \o 'punch_time_missing_percentage.txt'
         WITH punch_percent_calc AS (
         SELECT COUNT(et."JobNo") as cust_job, COUNT(et."JobNo") FILTER  ( WHERE  "TechStartTime" = '00:00:00' AND  "TechFinishTime" = '00:00:00' AND "TechHrs"::numeric <> 0) as job_no_punch 
         FROM du_dms_reynoldsrci_model.etl_tech_detail et JOIN du_dms_reynoldsrci_model.etl_job_detail ej ON 
         et."roNumber" = ej."roNumber" AND et."JobNo" = ej."JobNo" WHERE   "PayType" = 'Cust'
         )
         SELECT ROUND(((job_no_punch::numeric/cust_job::numeric)*100),2) AS percent FROM punch_percent_calc WHERE cust_job <>0;

        \o 'suffixed-invoices.csv'
        COPY (SELECT "roNumber"::text, "RoCreateDate"::text, "FinalPostDate"::text  
        FROM du_dms_reynoldsrci_model.etl_head_detail WHERE "roNumber" ~ '[A-Za-z]'
        UNION
        SELECT invoicenumber::text, opendate::text, closedate::text 
        FROM du_dms_reynoldsrci_model.etl_invoicemaster WHERE invoicenumber ~ '[A-Za-z]' group by invoicenumber, opendate, closedate)TO stdout WITH (FORMAT csv, HEADER true);
        \o 'grouped_suffixinvoice.csv'
        COPY (
        WITH grouped_suffixinvoice AS (
        SELECT 'API' AS source,
        EXTRACT('Year' FROM  "RoCreateDate"::date) || '-' || LPAD(EXTRACT('Month' FROM  "RoCreateDate"::date)::text,2,'0') AS date,
        COUNT("roNumber") AS "ROCount"
        FROM du_dms_reynoldsrci_model.etl_head_detail WHERE "roNumber" ~ '[A-Za-z]' 
        GROUP BY EXTRACT('Year' FROM "RoCreateDate"::date) || '-' || LPAD(EXTRACT('Month' FROM "RoCreateDate"::date)::text,2,'0')
        UNION
        SELECT 'CSV' AS source,  EXTRACT('Year' FROM  opendate::date) || '-' || LPAD(EXTRACT('Month' FROM opendate::date)::text,2,'0') AS date,
        COUNT(opendate) AS "ROCount"
        FROM du_dms_reynoldsrci_model.etl_invoicemaster WHERE invoicenumber ~ '[A-Za-z]'
        GROUP BY EXTRACT('Year' FROM opendate::date) || '-' || LPAD(EXTRACT('Month' FROM opendate::date)::text,2,'0')
                                     )
        SELECT * FROM grouped_suffixinvoice ORDER BY source, date::text ASC)TO stdout WITH (FORMAT csv, HEADER true);

        \o 'exception-closed-invoices.csv'
        COPY (
              WITH grouped_company AS (
                    SELECT array_agg(DISTINCT "AreaNumber"||'S') as store
                    FROM du_dms_reynoldsrci_model.etl_store_detail
                )
                SELECT im.dms_store_id as "Store", invoicenumber as "RONumber", STRING_AGG(invoice_status,','), opendate, closedate 
                FROM (SELECT dms_store_id, invoicenumber, invoice_status, opendate, closedate FROM du_dms_reynoldsrci_model.etl_invoicemaster GROUP BY 1, 2, 3, 4, 5) im
                    JOIN grouped_company gc
                        ON im.dms_store_id = ANY (gc.store) 
                where im.closedate is not null and invoicenumber not in (
                    select "roNumber" from du_dms_reynoldsrci_model.etl_head_detail where "FinalPostDate" is not NULL
                ) group by 1, 2, 4, 5
        ) TO stdout WITH (FORMAT csv, HEADER true);

        \o 'extraros_in_xml.csv'
	    COPY (
    		SELECT
                hd."roNumber",
                hd."RoCreateDate",
                hd."FinalPostDate"
            FROM 
                du_dms_reynoldsrci_model.etl_head_detail hd
            WHERE 
                EXISTS (SELECT 1 FROM du_dms_reynoldsrci_model.etl_invoicemaster)
                AND hd."roNumber" NOT IN (SELECT im.invoicenumber FROM du_dms_reynoldsrci_model.etl_invoicemaster im)
            ORDER BY 
                hd."roNumber"
	    ) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

         \o 'im_opened_closed_rci_ros.csv'
	    COPY (
            select i.invoicenumber as ronumber, opendate::date as invoice_master_open,"RoCreateDate"::date AS rci_open, "FinalPostDate" :: date as rci_closed 
            from (SELECT invoicenumber, opendate, closedate FROM du_dms_reynoldsrci_model.etl_invoicemaster GROUP BY invoicenumber, opendate, closedate) i join 
        du_dms_reynoldsrci_model.etl_head_detail h on h."roNumber" = i.invoicenumber where closedate is null and "FinalPostDate" is not NULL
        ) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );
    
    \o 'core-mismatch.csv'
    COPY (
        SELECT "roNumber" as RoNumber, "JobNo" as JobNumber, "PartNo" as PartNumber, 'CRSale - CRCost Mismatch' as Type, ("CustQtyShip"::int * "DlrCost"::numeric * -1) as CoreChargeCost,("CustQtyShip"::int * "CustPrice"::numeric * -1) as CoreChargeSale, ("CustQtyShip"::int * "DlrCost"::numeric) as CoreReturnCost,("CustQtyShip"::int * "CustPrice"::numeric) as CoreReturnSale
        FROM etl_parts_detail
        WHERE ABS("CustQtyShip"::int * "CustPrice"::numeric) != ABS("CustQtyShip"::int * "DlrCost"::numeric) AND "CustQtyShip"::int < 0
    ) TO stdout WITH (FORMAT csv, HEADER true);  
SQL
        rm single-ro-json-present-list.txt
        cp 'misc_paytype_exception.csv' $INVALID_MISC_PAYTYPE_EXCEPTION
        cp 'estimate.csv' $ESTIMATE
        cp 'punch_time_missing.csv' $PUNCH_TIME_MISSING
        cp 'punch_time_missing_percentage.txt' $PUNCH_TIME_MISSING_PERCENTAGE
        cp 'suffixed-invoices.csv' $SUFFIXED_INVOICES_FILEPATH
        cp 'grouped_suffixinvoice.csv' $GROUPED_SUFFIX_INVOICE_FILEPATH
        cp 'exception-closed-invoices.csv' $CLOSED_IN_EXCEPTION
        cp 'extraros_in_xml.csv' $EXTRAROS_IN_XML_EXCEPTION
        cp 'im_opened_closed_rci_ros.csv' $IM_OPEN_CLOSED_RCI_EXCEPTION
        cp 'removed_ros.csv' $DELETED_ROS_CSV_FILEPATH
        cp 'core-mismatch.csv' $CORE_MISMATCH_CSV_FILE_PATH 
        
        ROCOUNT_DETAILS=$(psql_local -t -c "SELECT count(*) FROM etl_head_detail;")
        echo "Total Ros Count:-$ROCOUNT_DETAILS"

        
        "$DU_ETL_HOME"/invoice-manipulation-common/summarize-ro-list \
        repair-order-sequence-missing-numerics.txt \
        > repair-order-sequence-missing-numerics-summary.txt
        
        misc_ro_count=`psql_local -t -c "SELECT count(*)
        FROM etl_misc_detail1"`
        
        estimate=`psql_local -t -c "SELECT COUNT(*)
        FROM du_dms_reynoldsrci_model.etl_misc_detail"`

        im_count=`psql_local -t -c "SELECT COUNT(invoicenumber) FROM du_dms_reynoldsrci_model.etl_invoicemaster"`

        im_exception=$(psql_local -t -c "SELECT count(invoicenumber) FROM du_dms_reynoldsrci_model.etl_invoicemaster JOIN du_dms_reynoldsrci_model.etl_head_detail ON invoicenumber = \"roNumber\"")


        echo "im_count:$im_count"
        echo "im_exception:$im_exception"

        if [ $im_count -gt 0 ]; then
           echo "im_count:$im_count"
            if [ $im_exception -eq 0 ]; then
            echo "ERROR: Invoice Master file uploaded doesn't match the raw data ROs."
            die "ERROR: Invoice Master file uploaded doesn't match the raw data ROs."
            else
               echo "im_exception not equal to zero"
            fi
        else
        echo "im_count less than zero"
        fi

        
        
        # punch_time_missing=`psql_local -t -c "SELECT COUNT(*)
        # FROM du_dms_reynoldsrci_model.etl_misc_detail"`

    #     suffixed_invoices_count=`psql_local -t -c 'SELECT 
    #    "roNumber"::text, "RoCreateDate"::text, "FinalPostDate"::text  
    #     FROM du_dms_reynoldsrci_model.etl_head_detail WHERE "roNumber" ~ \'[A-Za-z]\'
    #     UNION
    #     SELECT invoicenumber::text, opendate::text, closedate::text 
    #     FROM du_dms_reynoldsrci_model.etl_invoicemaster WHERE invoicenumber ~ \'[A-Za-z]\''`
        

        suffixed_invoices_count=$(cat 'suffixed-invoices.csv'  | wc -l)
      
        echo "misc_ro_count: $misc_ro_count"
        echo "estimate: $misc_ro_count"
        # echo "punch_time_missing: $punch_time_missing"
        echo "suffixed_invoices_count:$suffixed_invoices_count"
        
        content_array=()
        # while IFS= read -r line; do
        # content_array+=("$line")
        # done < "$PUNCH_TIME_MISSING_PERCENTAGE"
        # punch_time_missing=${content_array[0]}
        punch_time_missing=0
               
        #  if [[ -f "$PUNCH_TIME_MISSING_PERCENTAGE" ]]; then
        #    while IFS= read -r line; do
        #    content_array+=("$line")
        #    done < "$PUNCH_TIME_MISSING_PERCENTAGE"
        #    punch_time_missing=${content_array[0]}
        
        #  else
        #  echo "File does not exist: $PUNCH_TIME_MISSING_PERCENTAGE"
        
        # fi


           if [[ -f "$PUNCH_TIME_MISSING_PERCENTAGE" ]]; then
             while IFS= read -r line; do
             content_array+=("$line")
             done < "$PUNCH_TIME_MISSING_PERCENTAGE"
               if [ "${#content_array[@]}" -gt 0 ]; then
                   punch_time_missing=${content_array[0]}
               else
                 echo "File is empty: $PUNCH_TIME_MISSING_PERCENTAGE"
               fi
           else
           echo "File does not exist: $PUNCH_TIME_MISSING_PERCENTAGE"
           fi




        echo "punch time missing count :$punch_time_missing "
        misc_ro_count_numeric=$(echo "$misc_ro_count" | bc)
        estimate_numeric=$(echo "$estimate" | bc)
        d1=1
        suffixed_invoices_count_numeric=$(echo "$suffixed_invoices_count - $d1" | bc)
        # punch_time_missing=$(echo "$punch_time_missing" | bc)
        
        if [ $misc_ro_count_numeric -gt 1 ]; then
            echo "misc_ro_count_numeric: $misc_ro_count_numeric"
        fi
        echo "misc_ro_count:$misc_ro_count"
        echo "misc_ro_count_numeric:$misc_ro_count_numeric"
        echo "suffixed_invoices_count_numeric:$suffixed_invoices_count_numeric"
        
        misc_ro_count_numeric=0
        # if [[ "$HALT_OVER_RIDE" = 'false' ]]; then
            
        #     if [[ $misc_ro_count_numeric -eq 0 ]];
        #     then
                
                # echo "Empty accounting MISC, misc ro count: $misc_ro_count_numeric"
                # echo "Process is HALT!"
                # die "Process is HALT!"
        #     else
                
        #         echo "MISC data is not empty"
        #     fi
        # fi
          
        # if [[ "$HALT_OVER_RIDE" = 'false' ]]; then
            

        #     if [ "$(echo "$punch_time_missing > 40" | bc)" -eq 1 ];
        #     then
                
        #         echo "punch_time_missing: $punch_time_missing"
        #         die  "Process is HALT!"
        #     else
                
        #         echo "punch_time_missing less than 1"
        #     fi


        # fi


        # if [[ "$HALT_OVER_RIDE" = 'false' ]]; then
            
        #     if [[ $suffixed_invoices_count_numeric -gt 10 ]];
        #     then
                
        #         echo "Suffixed_invoices_count_numeric: $suffixed_invoices_count_numeric"
        #         die  "Process is HALT!"
        #     else
                
        #         echo "Suffixed_invoices_count_numeric less than 1"
        #     fi
        # fi
if [[ "$HALT_OVER_RIDE" = 'true' ]]; then

    psql_local --quiet <<SQL
        DELETE FROM etl_invoicemaster WHERE invoicenumber ~ '[A-Z]';
        DELETE FROM etl_head_detail WHERE "roNumber" ~ '[A-Z]';
        DELETE FROM etl_customer_detail WHERE "roNumber" ~ '[A-Z]';
        DELETE FROM etl_parts_detail WHERE "roNumber" ~ '[A-Z]';
        DELETE FROM etl_other_detail WHERE "roNumber" ~ '[A-Z]';
        DELETE FROM etl_job_detail WHERE "roNumber" ~ '[A-Z]';
        DELETE FROM etl_warrantyclaim_detail WHERE "roNumber" ~ '[A-Z]';
        DELETE FROM etl_tech_detail WHERE "roNumber" ~ '[A-Z]';
        DELETE FROM etl_sublet_detail WHERE "roNumber" ~ '[A-Z]';
        DELETE FROM etl_spg_detail WHERE "roNumber" ~ '[A-Z]';
        DELETE FROM etl_misc_detail1 WHERE "roNumber" ~ '[A-Z]';
        DELETE FROM etl_misc_detail WHERE "roNumber" ~ '[A-Z]';
        DELETE FROM etl_estimate_details WHERE "roNumber" ~ '[A-Z]';
        DELETE FROM etl_gog_detail WHERE "roNumber" ~ '[A-Z]';
                
SQL

fi
      
        
    )
    # # echo "Processor status: 9/25 Enumerating ROs Completed"
    # sleep 1
}

function check_missing_against_extraction() {
    # echo "Processor status: 10/21 Checking for Missing ROs in Original Raw Data Started"
    # sleep 1
    progress "Checking for Missing ROs in Original Raw Data"
    (
        cd "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"
        grep -o \
        -f "$WORK_DIR_PROCESSING_RESULTS_DIR"/repair-order-sequence-missing-numerics.txt \
        ./* \
        > "$WORK_DIR_PROCESSING_RESULTS_DIR"/missing-lookup-in-extraction.txt
        if [[ $? != 0 ]]; then
            progress "No missing RO#s found in extraction data"
        else
            yell "At least one missing RO# found in extraction data (false positives possible)"
        fi
    )
    # echo "Processor status: 10/21 Checking for Missing ROs in Original Raw Data Completed"
    # sleep 1
}

function report_open_or_void_ros() {
    (
        # echo "Processor status: 9/21 Detecting Open/Void RO data and reporting Started"
    # sleep 1
        progress "Detecting Open/Void RO data and reporting"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet \
        --set=BRANCH="$CUSTOM_BRANCH_NAME" \
        <<SQL
        set client_min_messages = warning;
        \o 'repair-order-sequence-open-list.txt'
        SELECT "roNumber","RoCreateDate", "TransactionType"
          FROM du_dms_reynoldsrci_model.etl_head_detail
         WHERE "TransactionType" = 'OPEN'
         ORDER BY "roNumber";

        \o 'repair-order-sequence-closed-by-date-then-ro.txt'
        \t
        \pset format unaligned
        SELECT "roNumber", "RoCreateDate", "TransactionType"
          FROM du_dms_reynoldsrci_model.etl_head_detail
         WHERE "TransactionType" = 'COMPLETED'
         ORDER BY "RoCreateDate", "roNumber";

        \o 'repair-order-sequence-closed-by-ro-then-date.txt'
        SELECT "roNumber", "RoCreateDate", "TransactionType"
          FROM du_dms_reynoldsrci_model.etl_head_detail
         WHERE "TransactionType" = 'COMPLETED'
         ORDER BY "roNumber";

        \o 'fractional-parts-quantity.csv'
        COPY (
            SELECT "roNumber", "PartNo", "CustQtyShip",  "DlrCost",  "CustPrice"
            FROM du_dms_reynoldsrci_model.etl_parts_detail
            WHERE "CustQtyShip"::NUMERIC % 1 != 0
        ) TO stdout WITH (FORMAT csv, HEADER true);

        \o 'invoice-master.csv'
        COPY (
            WITH store_tab AS (
                SELECT * FROM du_dms_reynoldsrci_model.etl_store_detail LIMIT 1
            ), multiStore AS (
                SELECT dms_store_id FROM du_dms_reynoldsrci_model.etl_invoicemaster GROUP BY dms_store_id
            )
            , all_ros AS (
                SELECT COALESCE(dms_store_id, (SELECT "AreaNumber"||'S' FROM du_dms_reynoldsrci_model.etl_store_detail LIMIT 1)) AS store, "roNumber" As invoicenumber, TO_CHAR("RoCreateDate"::date, 'mm/dd/yyyy')
                        AS open_date, 
                        CASE WHEN (SELECT count(dms_store_id) FROM multiStore) > 0 AND closedate IS NULL
                        THEN
                        NULL
                        ELSE
                        "FinalPostDate" 
                        END AS closed_date, '' AS void_date,"MakeName" AS make,  "Carline" AS model,
                        "VehicleYr" AS year,"Vin" AS vin, "CustName" As customer,"AdvName" AS advisor, "DeptType" AS department,
                        "MileageOut" AS mileage  FROM du_dms_reynoldsrci_model.etl_head_detail
                    LEFT JOIN du_dms_reynoldsrci_model.etl_invoicemaster ON etl_invoicemaster.invoicenumber = "roNumber" GROUP BY dms_store_id, invoicenumber, "roNumber", closedate
                    UNION ALL 
                    SELECT dms_store_id AS store, invoicenumber, opendate AS open_date, NULL, voiddate AS void_date, vehicle_make_name AS make, '' AS  model,
                        vehicle_year AS year, vehicle_identifier AS vin, '' As customer,'' AS advisor, departmentcode AS department, '' AS mileage  
                    FROM du_dms_reynoldsrci_model.etl_invoicemaster WHERE invoicenumber NOT IN (SELECT "roNumber" FROM du_dms_reynoldsrci_model.etl_head_detail) 
                    GROUP BY dms_store_id, invoicenumber, opendate, voiddate, vehicle_make_name, vehicle_year, vehicle_identifier, departmentcode
                )
              ,numeric_invoicenumbers AS (
				    SELECT regexp_replace("invoicenumber", '[^0-9]', '', 'g') AS invoicenumber
				    FROM all_ros
				)
              ,start_end (min_ro, max_ro) AS (
                    SELECT min("invoicenumber"::integer),
                        max("invoicenumber"::integer)
                    FROM numeric_invoicenumbers
                    WHERE "invoicenumber" ~ '^\d+$'
              )
              , grouped_store as (
              	    SELECT store FROM all_ros group by store
              )
              , get_store_id_for_void_ro as (
                    SELECT 
                        CASE WHEN COUNT(store) > 1 THEN '' ELSE (SELECT store FROM all_ros where store is not NULL limit 1) end as store_id  
                    from grouped_store
              )
              SELECT * from 
                (SELECT * FROM  all_ros
                UNION ALL
                SELECT
                    (SELECT store_id from get_store_id_for_void_ro) as store,         
                    ro_num :: text AS "invoicenumber", NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
                    NULL
                FROM start_end,
                        generate_series(min_ro, max_ro) gs (ro_num)
                WHERE NOT EXISTS(SELECT regexp_replace("invoicenumber", '[^0-9]', '', 'g')
                            FROM all_ros
                            WHERE regexp_replace("invoicenumber", '[^0-9]', '', 'g')::integer = ro_num ::integer)) t
                ORDER BY regexp_replace("invoicenumber", '[^0-9]', '', 'g')::numeric asc
           ) TO stdout WITH (FORMAT csv, HEADER true);

        \o 'part-details.csv'
        COPY (
            SELECT 
                h."roNumber" AS invoicenumber,
                "RoCreateDate" AS open_date,
                "FinalPostDate" AS close_date,
                NULL AS partsource,
                "PartNo" AS partnumber,
                "PartNoDesc" AS partdescription,
                "PayType" AS paytype,
                "IntrQtyShip" AS quantity,
                "CustQtyShip"::numeric * "DlrCost"::numeric AS extendedcost,
                "CustQtyShip"::numeric * "DlrCost"::numeric AS extendedsale,
                "DlrCost" AS unitcost,
                "CustPrice" AS unitsale,
                "VehicleYr" AS vehicle_year,
                "VehicleMake" AS vehicle_make,
                "ModelDesc" AS vehicle_model,
                "Vin" AS vin,
                "AdvNo" AS advisorid,
                "CustNo" AS customernumber,
                "JobNo" AS jobid
            FROM etl_head_detail h join etl_parts_detail p ON h."roNumber" = p."roNumber"
        ) TO stdout WITH (FORMAT csv, HEADER true);


        \o 'labor-details.csv'
        COPY (
            SELECT 
                h."roNumber" AS invoicenumber,
                "RoCreateDate" AS open_date,
                "FinalPostDate" AS close_date,
                "OpCode" AS opcode,
                "OpCodeDesc" AS opcodedescription,
                "PayType" AS paytype,
                "BillTime" AS hours,
                "TotalAmt" AS unitsale,
                "VehicleYr" AS vehicle_year,
                "VehicleMake" AS vehicle_make,
                "ModelDesc" AS vehicle_model,
                "Vin" AS vin,
                "AdvNo" AS advisorid,
                "CustNo" AS customernumber,
                "JobNo" AS jobid
            FROM etl_head_detail h join etl_job_detail l ON h."roNumber" = l."roNumber"
        ) TO stdout WITH (FORMAT csv, HEADER true);

        \o 'missing-invoices.csv'
        COPY (
              SELECT im.* FROM (SELECT * FROM du_dms_reynoldsrci_model.etl_invoicemaster GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12) im 
              LEFT JOIN du_dms_reynoldsrci_model.etl_head_detail hd ON hd."roNumber"= im.invoicenumber  WHERE dms_store_id IN (
                SELECT dms_store_id FROM du_dms_reynoldsrci_model.etl_head_detail h
                JOIN du_dms_reynoldsrci_model.etl_invoicemaster i ON h."roNumber"= i.invoicenumber GROUP BY dms_store_id
            )
            AND hd."roNumber" IS NULL
        ) TO stdout WITH (FORMAT csv, HEADER true);

        \o 'status_updated_ros.csv'
	    COPY (
		SELECT
    			hd."roNumber",
    			hd."RoStatus" AS New_Status,
    			hd."OldStatus" AS Old_Status
		FROM etl_head_detail hd
		WHERE LEFT (hd."RoStatus", 1) != LEFT (hd."OldStatus", 1) AND hd."OldStatus" IS NOT NULL
		ORDER BY hd."roNumber"
	) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

SQL
    )
    # echo "Processor status: 9/21 Detecting Open/Void RO data and reporting Completed"
    # sleep 1
    # Remove extension and append -report.zip
    BASENAME=$(basename "$INPUT_BUNDLE_ZIP" .zip)
    OUTPUT_ZIP="${BASENAME}-REPORT.zip"
    echo "-----COPY RCI REPORT FILE TO AUDIT DIRECTORY----------${OUTPUT_FILE_PREFIX}${OUTPUT_ZIP}-----"
    zip -j "/etl/audit-dispatch-bundle/${OUTPUT_FILE_PREFIX}""${OUTPUT_ZIP}" "$WORK_DIR_PROCESSING_RESULTS_DIR/"invoice-master.csv
}

function move_input_store_files_to_archive() {
    progress "Moving all input files under the store to archive"
    extracts=$(echo $INPUT_STORE_NAME | tr "/" "\n")
    archiveDirectory=''
    lastElement=''
    archiveDirectory=''
    replacedStoreNamepattern=''
    for extract in $extracts
    do
        lastElement=$extract
    done
    replacedElement="*_${lastElement}_*.gz"
    echo "replacedElement:$replacedElement"
    echo $lastElement
    archiveFilePath=$(echo "${INPUT_STORE_NAME/$lastElement/archive}")
    inputFilePath=$(echo "${INPUT_STORE_NAME/$lastElement/$replacedElement}")
    echo "archiveFilePath:$archiveFilePath"
    echo "inputFilePath:$inputFilePath"

    if [[ $inputFilePath != *"archive"* ]]; then
    echo "The inputFilePath contains 'archive'>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>."
    mv $inputFilePath $archiveFilePath
    else
    echo "The inputFilePath does not contain 'archive@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@'."
    fi
   
}

function generate_exception_report() {
    (
        # echo "Processor status: 7/21 Generating exception Report Started"
    # sleep 1
        progress "Generating exception report"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/src/parse/exception-report.psql \
        || die "Parse Finalize Failed"
        
        # echo "Processor status: 7/21 Generating exception Report Completed"
    # sleep 1
    )
}

function extract_proxy_to_tsv(){
    # echo "Processor status: 18/21 Extracting Text ROs to TSV Started"
    # sleep 1
    progress "$(date +'%H:%M:%S') : Ready to Extract Text ROs to TSV"
    proxy_path="$1"
    tsv_path="$2"
    cd ${DU_ETL_HOME}/${DMS_HOME}/src/extract/python
    python3 ./extract-text-proxy-to-tsv.py "$1" "$2"
    progress "$(date +'%H:%M:%S') : Done Extracting Text ROs to TSV"
    # echo "Processor status: 18/21 Extracting Text ROs to TSV Completed"
    # sleep 1
}

function exception_report_analysis(){
    # echo "Processor status: 15/21 Generate Exception Analysis Started"
    # sleep 1
    progress "$(date +'%H:%M:%S') : Ready to Generate Exception Analysis Report"
    input_dir="$1"
    output_dir="$2"
    cd ${DU_ETL_HOME}/${DMS_HOME}/Processor-Application/src/python
    python3 ./exception_details.py "$1" "$2"
     src_file="${output_dir}/All_exception_details.csv"
    destination="/home/<USER>/tmp/du-etl-dms-automate-extractor-work/exception_tag/All_exception_details.csv"
    if [ -f "$src_file" ]; then
        cp "$src_file" "$destination"
        ls $destination
        echo "File copied to $destination"
    else
        echo "Source file not found: $src_file"
        return 1
    fi
    progress "$(date +'%H:%M:%S') : Done Generating Exception Analysis Report"
    # echo "Processor status: 15/21 Generate Exception Analysis Completed"
    # sleep 1
}

function load_exception_analysis_report() {
    (
        # echo "Processor status: 16/21 Loading Exception Analysis Started"
    # sleep 1
        progress "$(date +'%H:%M:%S') : Loading Exception Analysis Report"
        total_ro_count_for_report=`psql_local -t -c "SELECT COUNT(*) FROM du_dms_reynoldsrci_model.etl_head_detail"`
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_exception_analysis --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/Processor-Application/src/psql/exception-report-analysis.psql \
                    --set=uuid="${UUID}" \
                    --set=total_ro_count="${total_ro_count_for_report}" \
                    --set=performed_by="${PERFORMED_BY}" \
                    --set=dms="${DMS}" \
                   || die "Exception Analysis Report load Failed"
        progress "$(date +'%H:%M:%S') : Done Loading Exception Analysis Report"
        # echo "Processor status: 16/21 Loading Exception Analysis Completed"
    # sleep 1

    )
}


function generate_store_specific_rule() {
    (
        progress "Generating Store Specific Rules"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet <<SQL
        set client_min_messages = warning;
        TRUNCATE table etl_makerenames_detail;
        WITH maketable AS (
            SELECT "VehicleMake" AS makename FROM etl_head_detail GROUP BY makename
        )        
        , store_rules AS (  
        SELECT original_name, renamed_name FROM etl_makerenames_rule_detail
            WHERE original_name !~ '[\^\>\<\$\+]'
                AND original_name <> renamed_name
        UNION ALL     

        SELECT makename AS original_name, renamed_name
            FROM maketable AS mt
                CROSS JOIN (SELECT *
                    FROM etl_makerenames_rule_detail
                    WHERE original_name ~ '[\^\>\<\$\+]') AS rr
            WHERE mt.makename ~ rr.original_name
                AND mt.makename <> rr.renamed_name
                AND mt.makename NOT IN (
                    SELECT rr2.original_name
                        FROM etl_makerenames_rule_detail AS rr2
                        WHERE rr2.original_name !~ '[\^\>\<\$\+]'
                    )
        )
        INSERT INTO etl_makerenames_detail
        SELECT original_name, renamed_name, true FROM store_rules;
        
SQL
    )
}

function  pre_import_halt_detection() {
    # echo "Processor status: 20/21 Pre-import Halt Detection Started"
    # sleep 1
    cd "$ZIP_WORK_DIR"
    deptment_halt=false
    paytype_halt=false
    make_halt=false
    inv_seq_halt=false
    generate_store_specific_rule || die "Store Make Rules generation failed"
    echo "Paytype import halt checking"
    paytype_import_halt=`psql_local -t -c "WITH store_paytype AS ( 
        SELECT COALESCE(\"PayType\", 'NA' ) As paytype_value FROM du_dms_reynoldsrci_model.etl_job_detail WHERE \"PayType\" !~ '^I'
        UNION
        SELECT COALESCE(\"PayType\", 'NA' ) As paytype_value FROM du_dms_reynoldsrci_model.etl_parts_detail WHERE \"PayType\" !~ '^I'
    )
    SELECT count(*) FROM store_paytype WHERE paytype_value NOT IN (SELECT paytype FROM du_dms_reynoldsrci_model.etl_paytype_detail)"`

    if [[ $paytype_import_halt -gt 0 ]];
        then
        paytype_halt=true
    fi

    echo "Department import halt checking"
    department_import_halt=`psql_local -t -c 'WITH store_department AS ( 
        SELECT distinct "DeptType" As department_value FROM du_dms_reynoldsrci_model.etl_head_detail
    )
    SELECT count(*) FROM store_department WHERE department_value NOT IN (SELECT department_name FROM du_dms_reynoldsrci_model.etl_department_detail)'`

    if [[ $department_import_halt -gt 0 ]];
        then
        deptment_halt=true
    fi

    echo "Inv Seq halt checking"
    inv_import_halt=`psql_local -t -c "WITH seq AS ( 
        SELECT
            \"roNumber\" as ro_number,
            \"RoCreateDate\" as open_date, 
            \"FinalPostDate\" as close_date,
            '' as void_date,
            ((regexp_matches(\"roNumber\", '^(\\d+)\\D*$')) [1])::integer AS ro_num_int
        FROM du_dms_reynoldsrci_model.etl_head_detail rd 
        ORDER BY 3, 1
    )
    , seq_1 AS (
        SELECT *,
            COALESCE(lead(ro_num_int) OVER seq_range > ro_num_int + 100, true) AS shift_seq,
            COALESCE(lag(ro_num_int) OVER seq_range < ro_num_int - 100, true)  AS new_seq 
        FROM seq
        WINDOW seq_range AS (ORDER BY ro_num_int, ro_number) 
    )
    , start_end_seq AS (
        SELECT *
        FROM seq_1
        WHERE shift_seq OR new_seq
    )
    , wind_seq AS (
        SELECT
            ro_number                                              AS start_ro,
            lead(ro_number) OVER(ORDER BY ro_num_int, ro_number)   AS end_ro,
            ro_num_int                                             AS start_ro_int,
            lead(ro_num_int) OVER(ORDER BY ro_num_int, ro_number)  AS end_ro_int,
            open_date                                              AS start_ro_date,
            lead(open_date) OVER(ORDER BY ro_num_int, ro_number)   AS end_ro_date,
            lead(shift_seq) OVER(ORDER BY ro_num_int, ro_number)   AS end_shift_seq,
            shift_seq,
            new_seq
        FROM start_end_seq
    )
    SELECT count(*) FROM wind_seq WHERE new_seq AND end_ro_date::date > CURRENT_DATE - INTERVAL '6 months'"`

    if [[ $inv_import_halt -gt 1 ]];
        then
        inv_seq_halt=true
    fi

    echo "Unassigned Make import halt checking"
    make_import_halt=`psql_local -t -c "WITH unassigned_make AS (
    SELECT COALESCE(renamed_name, UPPER(\"VehicleMake\")) AS store_make
    FROM du_dms_reynoldsrci_model.etl_head_detail
    LEFT JOIN du_dms_reynoldsrci_model.etl_makerenames_detail
        ON UPPER(\"VehicleMake\") = original_name
    WHERE NULLIF(\"VehicleMake\", '') IS NOT NULL
    GROUP BY COALESCE(renamed_name, UPPER(\"VehicleMake\"))
    EXCEPT
    SELECT unnest(valid_makes_array::text[]) AS make_name
    FROM du_dms_reynoldsrci_model.etl_all_manufacturer_detail
)
SELECT count(*) 
FROM unassigned_make"`

    if [[ $make_import_halt -gt 0 ]];
        then
        make_halt=true
    fi

    echo "paytype_halt     $paytype_halt"
    echo "inv_seq_halt     $inv_seq_halt"
    echo "make_halt        $make_halt"
    echo "deptment_halt    $deptment_halt"
    IMPORT_HALT_FOLDER='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/pre-import-halt'
    mkdir -p $IMPORT_HALT_FOLDER
    IMPORT_HALT_FILE="$IMPORT_HALT_FOLDER/halt-import.txt"
    rm $IMPORT_HALT_FILE

    EXCEPTION_HALT_FOLDER='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception'
    mkdir -p $EXCEPTION_HALT_FOLDER
    EXCEPTION_HALT_FILE="$EXCEPTION_HALT_FOLDER/halt-import.txt"
    rm $EXCEPTION_HALT_FILE
    rm -rf "$ZIP_WORK_DIR/processing-result/halt-import.txt"
    touch "$ZIP_WORK_DIR/processing-result/halt-import.txt"
    if [ "$make_halt" = true ] || [ "$deptment_halt" = true ] || [ "$paytype_halt" = true ] || [ "$inv_seq_halt" = true ] || [ "$SAVE_FILE" = true ]; then
        PRE_IMPORT_HALT=true
#         rm -rf "$ZIP_WORK_DIR/processing-result/halt-import.txt"
#         touch "$ZIP_WORK_DIR/processing-result/halt-import.txt"
        echo "paytype Halt: $paytype_import_halt" >> "$ZIP_WORK_DIR/processing-result/halt-import.txt"
        echo "Inv Sequence Halt: $inv_import_halt" >> "$ZIP_WORK_DIR/processing-result/halt-import.txt"
        echo "Make Halt: $make_import_halt" >> "$ZIP_WORK_DIR/processing-result/halt-import.txt"
        echo "Department Halt: $department_import_halt" >> "$ZIP_WORK_DIR/processing-result/halt-import.txt"
        cp "$ZIP_WORK_DIR/processing-result/halt-import.txt" "$IMPORT_HALT_FILE"
        cp "$ZIP_WORK_DIR/processing-result/halt-import.txt" "$EXCEPTION_HALT_FILE"  
        store_name_full=$(basename "$INPUT_BUNDLE_ZIP")
        STORE_NAME=${store_name_full%-INITIAL*}
        SUBJECT="Pre Import Halt Detected for $STORE_NAME"
        BODY=$(cat "$ZIP_WORK_DIR/processing-result/halt-import.txt")
        # email_halt_summary "$SUBJECT" "$BODY"
        IFS=',' read -ra ALL_COMPANY_IDS <<< "$COMPANY_IDS"
        for COMPANY_ID in "${ALL_COMPANY_IDS[@]}"; do
            echo "COMPANY_ID: $COMPANY_ID"
            save_import_halt_status $UUID $COMPANY_ID $make_halt $deptment_halt $paytype_halt $inv_seq_halt || die "Loading halt status failed"
        done

    else
        PRE_IMPORT_HALT=false
        echo "No Pre-import halt detected."
    fi
    # echo "Processor status: 20/21 Pre-import Halt Detection Completed"
    # sleep 1
}

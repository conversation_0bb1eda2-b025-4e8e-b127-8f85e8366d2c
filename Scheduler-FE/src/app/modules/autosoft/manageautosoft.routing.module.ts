import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManageautosoftComponent } from './manageautosoft.component';

const routes: Routes = [
    { path:"", component: ManageautosoftComponent,
    data: {
        title: "ManageAutosoft",
        breadcrumb: [{ label: "ManageAutosoft", url: "" }],
      }, }
];

@NgModule({
    exports: [RouterModule],
    imports:[RouterModule.forChild(routes)]
})
export class ManageautosoftRoutingModule{}
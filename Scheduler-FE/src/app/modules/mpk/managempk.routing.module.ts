import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManagempkComponent } from './managempk.component';

const routes: Routes = [
    { path:"", component: ManagempkComponent ,
    data: {
        title: "ManageMPK",
        breadcrumb: [{ label: "ManageMPK", url: "" }],
      },}
];

@NgModule({
    exports: [RouterModule],
    imports:[RouterModule.forChild(routes)]
})
export class ManagempkRoutingModule{}
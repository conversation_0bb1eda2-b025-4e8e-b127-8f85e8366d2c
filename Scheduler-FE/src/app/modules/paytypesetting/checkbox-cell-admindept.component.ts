import { Component, EventEmitter, Input, OnDestroy, Output } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { ICellRendererParams } from "ag-grid-community";

@Component({
  selector: "checkbox-cell",
  template: ` <input
    type="checkbox"
    (click)="onChange($event)"
    [checked]="params.data.is_allowed"
  />`,
})
export class CheckboxCellAdminDeptComponent implements ICellRendererAngularComp {
  public params: any;
  public isChecked: boolean = false;
  checkedItems: any[] = []; // Array to store checked items
  @Output() checkboxChange = new EventEmitter<any>(); // Define output property

  constructor() {}
  refresh(params: any): boolean {
    this.params = params;
    return true;
  }
  agInit(params: any): void {
    this.params = params;
    console.log(params.data, "params===============");

    this.isChecked = this.params.data[this.params.colDef.field];
  }
  public onChange(event: any) {
    const updatedData = {
      ...this.params.data,
      [this.params.colDef.field]: event.currentTarget.checked,
    };
    const result = {
      value: this.params.value,
      event: event,
      data: updatedData,
      rowdata: updatedData,
    };
    console.log(result, this.params, "result====================");
    // this.checkboxChange.emit(result);
    // Call the new function in the parent component
    this.params.context.componentParent.handleCheckboxChange(result);
  }
}

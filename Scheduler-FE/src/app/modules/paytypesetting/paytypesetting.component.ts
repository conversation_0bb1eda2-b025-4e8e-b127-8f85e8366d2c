import { HttpClient,HttpHeaders } from "@angular/common/http";
import { ChangeDetectorRef, Component, ElementRef, Renderer2, View<PERSON>hild,OnDestroy,OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { Column<PERSON>pi, <PERSON>rid<PERSON><PERSON> } from "ag-grid-community";
import { ToastrService } from "ngx-toastr";
import { Apollo, gql } from "apollo-angular";
import { IDropdownSettings } from "ng-multiselect-dropdown";
import { Subject, debounceTime, takeUntil } from "rxjs";
import { ConstantService } from "src/app/structure/constants/constant.service";
import { CommonService } from "src/app/structure/services/common.service";
import { environment } from "src/environments/environment";
import { CheckboxCellAdminDeptComponent } from "./checkbox-cell-admindept.component";
import { SubscriptionConstantService } from "./../../structure/constants/subscription.constant.service";
import { SharedModule } from "../shared/shared.module";
import { FormBuilder,AbstractControl,ValidationErrors} from '@angular/forms';

import Swal from 'sweetalert2';
import * as moment from "moment-timezone";
declare var $: any;
declare var swal: any;
declare var NProgress: any;
const storePayType = gql`
  query getStorePaytype($companyId: BigInt) {
    getStorePaytype(inCompanyId: $companyId)
  }
`;
const deleteStorePaytypeDetails = gql`
  mutation deleteStorePaytypeDetails(
    $inCompanyId: BigInt!
    $inPaytype: String!
  ) {
    deleteStorePaytypeDetails(
      input: { inCompanyId: $inCompanyId, inPaytype: $inPaytype }
    ) {
      json
    }
  }
`;
const deleteDepartmentDetails = gql`
  mutation deleteDepartmentDetails($inDept: String!, $inDms: String!) {
    deleteDepartmentDetails(input: { inDept: $inDept, inDms: $inDms }) {
      json
    }
  }
`;

const deleteMake = gql`
  mutation deleteMake($inManufacturer: String!, $inMake: String!,$inUserName: String!) {
    deleteMake(input: { inManufacturer: $inManufacturer, inMake: $inMake, inUserName: $inUserName }) {
      json
    }
  }
`;


const storePaytypeDetailsUpdation = gql`
  mutation storePaytypeDetailsUpdation($payTypeDet: JSON) {
    storePaytypeDetailsUpdation(input: { payTypeDet: $payTypeDet }) {
      json
    }
  }
`;

const updateMake = gql`
  mutation updateMake($inData: JSON,$inUserName: String!) {
    updateMake(input: { inData: $inData,inUserName:$inUserName }) {
      json
    }
  }
`;

const approveMake = gql`
  mutation updateMakeList($input: UpdateMakeListInput!) {
    updateMakeList(input: $input) {
      json
    }
  }
`;




const getDepartmentDetails = gql`
  query getDepartmentDetails($inDms: String) {
    getDepartmentDetails(inDms: $inDms)
  }
`;

const departmentDetailsInfo = gql`
  mutation departmentDetailsInfo($depDet: JSON) {
    departmentDetailsInfo(input: { depDet: $depDet }) {
      json
    }
  }
`;


const getMakeList = gql`
  query {
  getMakeList {
    edges {
      node {
        manufacturer
        make
        isDefault

      }
    }
  }
}
`;

const getPendingMakeList = gql`
  query {
    getAllMakeApprovals
  }
`;


const deleteMakeAlias = gql`
  mutation deleteMakeRuleById($inId: Int!) {
    deleteMakeRuleById(input: { inId: $inId }) {
      json
    }
  }
`;


const getMakeAlias = gql`
   query {
  getNonDefaultMakeRules {
    edges {
      node {
        id
        makeName
        makeFormat
        isDefault
      }
    }
  }
}
`;

const updateMakeApproveList = gql`
  mutation updateMakeApprovalStatus(
    $inId: BigInt!, 
    $inIsApproved: Boolean!, 
    $inIsDeleted: Boolean!
  ) {
    updateMakeApprovalStatus(
      input: { 
        inId: $inId, 
        inIsApproved: $inIsApproved, 
        inIsDeleted: $inIsDeleted 
      }
    ) {
      json
    }
  }
`;


const approveMakeList = gql`
  mutation updateMakeApprovalStatus($inData: JSON!) {
    updateMakeApprovalStatus(input: { inData: $inData }) {
      json
    }
  }
`;







@Component({
  selector: "app-paytypesetting",
  templateUrl: "./paytypesetting.component.html",
  styleUrls: ["./paytypesetting.component.css"],
  standalone: true,
  imports: [
    SharedModule,
  ],
})
export class PaytypesettingComponent implements OnInit ,OnDestroy {
  @ViewChild(CheckboxCellAdminDeptComponent)
  checkboxCellDeptInstance!: CheckboxCellAdminDeptComponent;
  public rowDataUnassignPay: any = [];
  public overlayLoadingTemplate: any;
  public overlayNoRowsTemplate: any;
  public defaultColDefPayType: any;
  public columnDefsUnassignPay: any;

  public columnDefsMake: any = [];
  public columnDefsMake1: any = [];

  public columnDefsMakeAlias: any = [
    {
      headerName: "ID",
      field: "id",
      hide: true,
    },
    {
      headerName: "Alias",
      field: "make",
      sortable: true,
      width: 100,
      editable: false,
    },
    {
      headerName: "Make",
      field: "alias",
      sortable: true,
      width: 100,
      editable: false,
    },
    {
      headerName: "isDefault",
      field: "isDefault",
      sortable: true,
      width: 100,
      editable: false,
      hide:true,
      sort: "asc", 
      sortIndex: 0,  
  
    },
    {
      headerName: "Action",
      field: "",
      width: 135,
      editable: false,
      cellRenderer: function (params:any) {
        const dataString: any = JSON.stringify(params.data);
        let html = ` `;
        // if(params.data.isDefault==false){
          html += ` <em class="badge badge-primary badge-font make-alias" title="Delete" data-toggle="tooltip" data-animation="false" data-placement="top"  data-info='${dataString}' style="cursor: pointer;">View</em>`;
        // }
        return html;
      },
      cellClass: (params: any) => {
        return "left-aligned-cell-t";
      },
    },
  ];

  public columnDefsMakeAlias1: any = [
    {
      headerName: "ID",
      field: "id",
      hide: true,
    },
    {
      headerName: "Alias",
      field: "make",
      sortable: true,
      width: 100,
      editable: false,
    },
    {
      headerName: "Make",
      field: "alias",
      sortable: true,
      width: 100,
      editable: false,
    },
    {
      headerName: "isDefault",
      field: "isDefault",
      sortable: true,
      width: 100,
      editable: false,
      hide:true,
      sort: "asc", 
      sortIndex: 0,  
  
    },
    {
      headerName: "Action",
      field: "",
      width: 135,
      editable: false,
      cellRenderer: function (params:any) {
        const dataString: any = JSON.stringify(params.data);
        let html = ` `;
        // if(params.data.isDefault==false){
          html += ` <em class="badge badge-danger badge-font make-alias-view" title="Delete" data-toggle="tooltip" data-animation="false" data-placement="top"  data-info='${dataString}' style="cursor: pointer;">Delete</em>`;
        // }
        return html;
      },
      cellClass: (params: any) => {
        return "left-aligned-cell-t";
      },
    },
  ];

  public columnDefsPendingMakeApproval: any = [
    {
      headerName: '',
      checkboxSelection: true,
      headerCheckboxSelection: true,
      width: 40,
      pinned: 'left',
      suppressNavigable: true 
    },    
    {
      headerName: "ID",
      field: "id",
      hide: true,
    },
    {
      headerName: "Store Name",
      field: "companyName",
      sortable: true,
      width: 100,
      editable: false,
    },
    {
      headerName: "Manufacturer",
      field: "manufacturer",
      sortable: true,
      width: 100,
      editable: false,
    },
    {
      headerName: "Make",
      field: "make",
      sortable: true,
      width: 100,
      editable: false,
    },
    {
      headerName: "Created By",
      field: "createdBy",
      sortable: true,
      width: 100,
      editable: false,
    },
    {
      headerName: "Created At",
      field: "createdAt",
      sortable: true,
      width: 100,
      editable: false,
    },
    {
      headerName: "isApproved",
      field: "isApproved",
      sortable: true,
      width: 100,
      editable: false,
      hide:true,
      sort: "asc", 
      sortIndex: 0,  
  
    },
    {
      headerName: "Action",
      field: "",
      width: 135,
      editable: false,
      cellRenderer: function (params: any) {
        const dataString: any = JSON.stringify(params.data);
        const container = document.createElement("div");
      
     if (params.data.isApproved === false) {
  const safeData = JSON.stringify(params.data).replace(/"/g, '&quot;');

  container.innerHTML = `
    <em class="badge badge-primary badge-font pending-make-approval"
         title="Approve"
         data-toggle="tooltip"
         data-animation="false"
         data-placement="top"
         data-info="${safeData}"
         style="cursor: pointer;">Approve</em>
    <em class="badge badge-danger badge-font delete-pending-make-approval"
         title="Delete"
         data-toggle="tooltip"
         data-animation="false"
         data-placement="top"
         data-info="${safeData}"
         style="cursor: pointer;">Delete</em>
  `;
}


      
        // Prevent cell focus / editing on click
        container.addEventListener("mousedown", function (event) {
          event.stopPropagation();
        });
      
        return container;
      },
      cellClass: (params: any) => {
        return "left-aligned-cell-t";
      },
    },
  ];
  
  
  public unAssignCustomeType: any;
  public unAssignWarrantyType: any;
  public unassignedPayType: any;
  public defaultColDefArcPayType: any;
  public defaultColPendinMakeApproval:any;
  public defaultColDefMake: any;
  public defaultColDefMake1: any;
  public defaultColDef: any;
  public gridContext: any;
  public domLayout: any;
  public editType: any;
  public gridColumnApiModel!: ColumnApi;
  private subscription: any;
  private subscription$ = new Subject();
  public companyCommonId: any;
  public allPaytypeList: any[] = [];
  public rowDataAllPaytypeList: any[] = [];
  public makeList:any[] =[];
  public pendingMakeList:any[]=[];
  public makeAliasList:any[] =[];
  public makeAliasOrginalList:any[] =[];
  public allPaytypeWarrantyList: any;
  public allPaytypeCustomerList: any = [];
  public resPayListData: any;
  public allArcPaytypeList: any = [];
  public saveArcPaytypeList: any = [];
  public columnDefsArchivePay: any;
  public gridApiArcPayType!: GridApi;
  public gridColumnApiArcPayType!: ColumnApi;
  private subscriptionList: any = [];
  public showSpinnerOnSave: any = false;
  public storeGroup: any[] = [];
  loading: any = false;
  public importGroupFilterList: any[] = [];
  public importMakeGroupFilterList: any[] = [];
  public importModelGroupFilterList: any[] = [];
  public storeGroupFilterList: any[] = [];
  public storeGroupList: any[] = [];
  public store: any[] = [];
  public linkProjectObj: any = null;
  public companyId: any = null;
  public enableLinkingModal = ""; //environment.enableSync;
  public toggleAssignedToDropDown: boolean = true;
  public confirmTagOption!: FormGroup;
  public dmsListItem: any[] = [];
  public dmsListS360: any[] = [];
  public selectedUser: any[] = [];
  public selectedUserList: any[] = [];
  public s360userListOp: any[] = [];
  public selectedGroupName = "";
  public selectedGroup: any = null;
  public createStoreSolve360!: FormGroup;
  public setFormFlag = 0;
  public selectedStore: any = null;
  public createProjectSolve360!: FormGroup;
  public userListS360: any[] = [];
  public userListSalesPersonS360: any[] = [];
  public showSpinnerStoreButton = false;
  public storeNameExist = false;
  public testTagDisplayStoreCreation = false;
  public version: any;
  public fileList: any[] = [];
  public versionFilterList: any[] = [];
  public storeFilterList: any[] = [];
  public columnDefsUnassignMake: any;
  public rowDataUnassignMake: any = [];
  public storeFileList: any[] = [];
  public createSchedule!: FormGroup;
  private storeList: any[] = [];
  private storeFlag = false;
  private jobGroupList: any[] = [];
  public allStoreList: any[] = [];
  public makeOrginalList:any[]=[];
  public editMakeListData:any[]=[];
  public editMakeAliasData:any[]=[];
  public selectedMake = { manufacturer: 'test manufacturer', make: 'test make' };
  public singleDropdownSettingsDisable: IDropdownSettings = {
    singleSelection: true,
    idField: "id",
    textField: "itemName",
    allowSearchFilter: true,
  };
  public singleDropdownSettings: IDropdownSettings = {
    singleSelection: true,
    idField: "id",
    textField: "itemName",
    allowSearchFilter: true,
    closeDropDownOnSelection: true,
  };
  public reloadGroup = false;
  public companyIds: any;
  public listener: any;
  public editMode = false;
  public selectedRowsArc: any[] = [];
  public selectedRowsMake: any[] = [];
  public selectedRowsPendingMake: any[] = [];
  public allDepartmentList: any;
  public deptList: any;
  public rowUnassignedDataDept: any;
  public columnDefsUnassignDept: any;
  public deptModal: any;
  public isDeptChecked: boolean = false;
  public gridApiPDeptRate: any;
  public gridColumnApiPDeptRate: any;
  public rowDataDept: any = [];
  public rowDataMake: any = [];
   public selectedDeptList: any = [];
  public isFirstPRate = true;
  public rowDataDesc: any = [];
  public columnDefsDeptDesc: any;
  public columnDefsPayTypeRate: any;
  public dmsList: any;
  public gridApiPRate: any;
  public gridColumnApiPRate: any;
  private gridApiMake!: GridApi;
  private gridColumnApiMake!: ColumnApi;
  public selectedRowsDept: any;
  public isAuthenticated = false;
  public dmsItems: any[];
  public dmsSelectItems: any;
  public oldMake:any;
  public modifiedMake: any[] = [];
  public makeFilterList:any[]=[];
  public selectedMan:any;
  manufacturer: any = null
  myForm: FormGroup;
  submitted = false;

  public gridOptions = {
    context: {
      componentParent: this,
    },
    // other grid options
  };
  public updateDeptClick$ = new Subject<void>();
  public updateArcClick$ = new Subject<void>();
  public updateMakeList$ = new Subject<void>();
  
  constructor(
    private commonService: CommonService,
    private apollo: Apollo,
    public constantService: ConstantService,
    private changeDetectorRef: ChangeDetectorRef,
    private toastrService: ToastrService,
    private http: HttpClient,
    private renderer: Renderer2,
    private elRef: ElementRef,
    public SubscriptionConstantService: SubscriptionConstantService,
    private fb: FormBuilder
  ) {
    this.updateDeptClick$.pipe(debounceTime(300)).subscribe(() => {
      this.updateDeptType("update");
    });
    this.updateArcClick$.pipe(debounceTime(300)).subscribe(() => {
      this.updateArcPayType("update");
    });

    this.updateMakeList$.pipe(debounceTime(300)).subscribe(() => {
      this.updateMakeList("update");
    });

    this.myForm = this.fb.group({
      manufacturer: [null, Validators.required], // Manufacturer Dropdown
      make: ['', [Validators.required, this.makeValidator]]     // Make Input Field
    });
  

    this.gridContext = {
      componentParent: this,
    };
    this.defaultColDefPayType = {
      resizable: true,
      filter: true,
      sortable: true,
      editType: "fullRow",
      editable: true,
      cellDataType: false,
      stopEditingWhenCellsLoseFocus: true,
    };
    this.defaultColDef = {
      resizable: true,
      filter: true,
      sortable: true,
      cellDataType: false,
      stopEditingWhenCellsLoseFocus: true,
      flex: 1,
    };
    this.defaultColDefArcPayType = {
      resizable: true,
      filter: true,
      sortable: true,
      editable: true,
      cellDataType: false,
      flex: 1,
      stopEditingWhenCellsLoseFocus: true,
    };

    this.defaultColPendinMakeApproval = {
      resizable: true,
      filter: true,
      sortable: true,
      editable: false,
      cellDataType: false,
      flex: 1,
      // stopEditingWhenCellsLoseFocus: true,
    };





    this.defaultColDefMake = {
      resizable: true,
      filter: true,
      sortable: true,
      editable: true,
      cellDataType: false,
      flex: 1,
      stopEditingWhenCellsLoseFocus: true,
    }
    this.defaultColDefMake1 = {
      resizable: true,
      filter: true,
      sortable: true,
      editable: true,
      cellDataType: false,
      flex: 1,
      stopEditingWhenCellsLoseFocus: true,
    }



    
    this.dmsItems = [
      { id: "CDK3PA", itemName: "CDK3PA" },
      { id: "DealerTrack", itemName: "DealerTrack" },
      { id: "DealerBuilt", itemName: "DealerBuilt" },
      { id: "Reynolds", itemName: "Reynolds" },
      { id: "Auto/Mate", itemName: "Auto/Mate" },
      { id: "Tekion", itemName: "Tekion" },
      { id: "PBS", itemName: "PBS" },
      { id: "MPK", itemName: "MPK" },
      { id: "Dominion / VUE", itemName: "Dominion / VUE" },
      { id: "QUORUM", itemName: "QUORUM" },
      { id: "ADAMS", itemName: "ADAMS" },
      { id: "AutoSoft", itemName: "AutoSoft" },
      { id: "TekionAPI", itemName: "TekionAPI" },
    ];
    this.dmsItems = this.dmsItems.sort((a, b) => a.itemName.localeCompare(b.itemName));
    this.overlayLoadingTemplate =
      '<span class="ag-overlay-loading-center">Loading <em aria-hidden="true" class="fa fa-spinner fa-pulse"></em></span>';
    this.overlayNoRowsTemplate =
      '<span style="padding: 10px; border: 2px solid #444; background: lightgoldenrodyellow;">No data found</span>';
  }
  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    this.subscription$.next(void 0);
    this.subscription$.complete();
  }

  ngOnInit() {
    this.commonService.getGroups(() => {
      this.commonService.checkGroups((flag) => {
        if (!flag) {
          return;
        }
    this.isAuthenticated = true;
    this.init();
    this.SubscriptionConstantService.pageTitle = " - Settings";
      });
    });
    this.createSchedule = new FormGroup({
      storeGroup: new FormControl("", Validators.required),
      store: new FormControl("", Validators.required),
    });
  }
  init() {
    this.commonService.allS360Jobs("", "production", (result: any) => {
      console.log(result, "resultstoregroup----------");
      this.loading = false;
      this.storeGroupList = result.storeGroupList;
      this.storeList = result.storeList;
      this.jobGroupList = result.jobGroupList;
      for (let i = 0; i < this.jobGroupList.length; i++) {
        //if (this.jobGroupList[i].dmsCode == "CDK3PA") {
        this.allStoreList.push(this.jobGroupList[i]);
        //}
      }
      this.getGroupFilterList();
    });
    this.setArchivedPayType();
    this.getDepartment((status: boolean) => {
      this.setDepartmentGrid();
    });
    if (!this.listener) {
      this.listener = this.renderer.listen(this.elRef.nativeElement, "click", (evt) => {
        const editModeMap: any = {};
        let debounceDeptTimer: any = null;
        let debouncePayTypeTimer: any = null;
        if (evt.target.className === "badge badge-danger badge-font") {
          if (!debouncePayTypeTimer) {
            debouncePayTypeTimer = setTimeout(() => {
              debouncePayTypeTimer = null;
              this.deleteArcPayType(evt.target.dataset.info);
            }, 300); // 300ms debounce time
          }
        } else if (evt.target.className === "badge badge-info badge-font save-icon") {
          const dataInfo: any[] = evt.target.getAttribute("data-info");
          this.updateArcPayType("Add");
        } else if (evt.target.className === "badge badge-danger badge-font Dept") {
          if (!debounceDeptTimer) {
            debounceDeptTimer = setTimeout(() => {
              debounceDeptTimer = null;
              this.deleteDeptType(evt.target.dataset.info);
            }, 300); // 300ms debounce time
          }

          // this.deleteDeptType(evt.target.dataset.info);
        } else if (evt.target.className === "badge badge-info badge-font save-icon Dept") {
          this.updateDeptType("Add");
        }else if (evt.target.className === "badge badge-danger badge-font delete-make") {
           this.deleteMake(evt.target.dataset.info);
           
        }
       else if (evt.target.className === "badge badge-primary badge-font pending-make-approval") {
          const rawData = evt.target.dataset.info.replace(/&quot;/g, '"');
          let data = JSON.parse(rawData); 
           console.log("data:", data);
           data.isApproved = true;
           data.isDeleted = false;
           this.selectedRowsPendingMake = [data];
          this.approveMake(data);
}
 else if (evt.target.className === "badge badge-danger badge-font delete-pending-make-approval") {
          let data = JSON.parse(evt.target.dataset.info);
           console.log("Pending delete data$$$$$$$$$$$$$$$$$$$$$$$$$",data);
           data.isApproved = false;
           data.isDeleted = true;
           this.selectedRowsPendingMake = [data];
           this.approveMake(data);
        } 

        
        else if (evt.target.className === "badge badge-primary badge-font make-alias") {
         let data = JSON.parse(evt.target.dataset.info);
         this.openEditMakeAliasModal(data.make)         
       }else if (evt.target.className === "badge badge-primary badge-font edit-make") {
        let data = JSON.parse(evt.target.dataset.info);
        this.oldMake=data.make;
        this.selectedMake.manufacturer = data.manufacturer;
        this.selectedMake.make = data.make;
        this.openEditMakeModal(data.manufacturer)
      }else if (evt.target.className === "badge badge-primary badge-font edit-make-inner") {
         let data = JSON.parse(evt.target.dataset.info);
         this.oldMake=data.make;
         this.selectedMake.manufacturer = data.manufacturer;
         this.selectedMake.make = data.make;
         $("#editMakeModal").modal("hide");
         $("#editMakeModal1").modal("show");
       }else if (evt.target.className === "badge badge-danger badge-font delete-make-inner") {
         this.deleteMake(evt.target.dataset.info);
       }else if (evt.target.className === "badge badge-danger badge-font make-alias-view",evt.target.dataset.info) {
       this.deleteMakeAlias(evt.target.dataset.info);
      }      
      });
    }
    
    this.getMakeList1();
    this.setMakeGrid();
    this.getMakeAlias();
    this.getPendingApprovalMakeList();



  }
  closeEditMakeModal(){
    $("#editMakeModal").modal("hide");

  }
 
  cancelEditMakeModal(){
    $("#editMakeModal1").modal("hide");
    this.openEditMakeModal(this.selectedMan);
    
  }


  closeEditMakeModal1(){
    $("#editMakeModal1").modal("hide");
  }

  openEditMakeModal(man:any){
    this.subscription = this.apollo
    .use("manageScheduler")
    .query<{ data: any }>({
      query: getMakeList,
      fetchPolicy: "network-only",
    })
    .pipe(takeUntil(this.subscription$))
    .subscribe({
      next: (listdata) => {
        const getMakeListData = (listdata?.data as any)?.getMakeList;
       if (getMakeListData?.edges) {
          this.makeList = getMakeListData.edges.map((edge: any, index: number) => ({
            id: index,
            manufacturer: edge.node.manufacturer,
            make: edge.node.make,
            isDefault:edge.node.isDefault
          }));
         this.makeFilterList = Array.from(
            new Set(this.makeList.map((item) => item.manufacturer))
          ).map((manufacturer, index) => ({
            id: index + 1, 
            manufacturer: manufacturer,
          }));
          this.editMakeListData = this.makeOrginalList.filter(item => item.manufacturer == man);
          this.selectedMan = man;
          this.setEditMakeGrid();
         $("#editMakeModal").modal("show");
          this.makeOrginalList = this.makeList;
           this.makeList = Object.values(
            this.makeOrginalList.reduce((acc, item) => {
              if (!acc[item.manufacturer]) {
                acc[item.manufacturer] = { 
                  id: item.id, 
                  manufacturer: item.manufacturer, 
                  make: [] as string[],
                  isDefault: true
                };
              }
              acc[item.manufacturer].make.push(item.make);         
              if (!item.isDefault) {
                acc[item.manufacturer].isDefault = false;
              }         
              return acc;
            }, {} as Record<string, { id: number; manufacturer: string; make: string[]; isDefault: boolean }>)  
          ).map((entry:any) => ({
            ...entry,
            make: entry.make.join(', ') 
          }));         
        } else {
          console.warn("getMakeList.edges is undefined or empty");
        }
      },
      error: (err) => {
        console.error("Error fetching make list:", err);
      },
      complete: () => {
        console.log("Completed");
      },
    }); 
  }

  openEditMakeAliasModal(man:any){
    this.subscription = this.apollo
      .use("manageScheduler")
      .query<{ data: any }>({ 
        query: getMakeAlias,
        fetchPolicy: "network-only",
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata) => {
          const getMakeListData = (listdata?.data as any)?.getNonDefaultMakeRules;
  
          if (getMakeListData?.edges) {
            this.makeAliasList = getMakeListData.edges.map((edge: any) => ({
              id: edge.node.id,
              make: edge.node.makeName,
              alias: edge.node.makeFormat,
              isDefault:edge.node.isDefault
            }));

           console.log("Orghinal makeAliasList:", this.makeAliasList);

            this.makeAliasOrginalList = this.makeAliasList;
        
            this.makeAliasList = Object.values(
              this.makeAliasOrginalList.reduce((acc, item) => {
                  if (!acc[item.make]) {
                      acc[item.make] = { ...item, alias: item.alias };
                  } else {
                      acc[item.make].alias += `,${item.alias}`;
                      acc[item.make].isDefault = acc[item.make].isDefault && item.isDefault;
                  }
                  return acc;
              }, {})
          );
              
          } else {
            console.warn("getMakeList.edges is undefined or empty");
          }
        },
        error: (err) => {
          console.error("Error fetching make list:", err);
        },
        complete: () => {
          console.log("Completed");
          this.editMakeAliasData = this.makeAliasOrginalList.filter(item => item.make == man);
          this.selectedMan = man;
          $("#editMakeAliasModal").modal("show");
        },
      });


  }
  
  closeEditMakeAliasModal(){
    $("#editMakeAliasModal").modal("hide");
  }
 openAddMakeModal(){
    $("#addMakeModal").modal("show");
  }

  closeAddMakeModal(){
    $("#addMakeModal").modal("hide");
  }


  singleDropdownSettingsMake = {
    singleSelection: true,
    idField: 'id',
    textField: 'manufacturer',
    allowSearchFilter: true,
    closeDropDownOnSelection: true 
  };

 onSubmitMake() {
    if (this.myForm.valid) {
      const formData = this.myForm.value;
      const formattedData = {
        manufacturer: formData.manufacturer.length > 0 ? formData.manufacturer[0].manufacturer : "", 
        make: formData.make
      };
   const currentUserObj = JSON.parse(localStorage.getItem("currentUser") || "{}");
      const userName = currentUserObj?.userPrincipalName || "";
      const formattedMakeList = {   
       manufacturer: formattedData.manufacturer,
       make_old: null,
       make_new: formattedData.make,
       }
       const dbObj = { inData: JSON.stringify([formattedMakeList]),inUserName:userName }; 
       const subscription = this.apollo
         .use("manageScheduler")
         .mutate({
           mutation: updateMake,
           variables: dbObj,
         })
         .pipe(takeUntil(this.subscription$))
         .subscribe({
           next: (listdata:any) => {
             NProgress.done();
             const result: any = listdata;
             console.log("Add Make Response",result);
             this.getMakeList1();
             const parsedJson = JSON.parse(result.data.updateMake.json);
            if (parsedJson.status === "success") {
              
                this.closeAddMakeModal();
                 swal({
                   title: parsedJson.message,
                   type: "success",
                   confirmButtonClass: "btn-success pointer",
                   confirmButtonText: this.constantService.CLOSE,
                   allowOutsideClick: false
 
                 });
               } else {
                this.closeAddMakeModal();
                 swal({
                       title: parsedJson.message ,
                       type: "warning",
                       confirmButtonClass: "btn-warning pointer",
                       confirmButtonText: this.constantService.CLOSE,
                       allowOutsideClick: false
 
                    });
            }
         },
           error: (err) => {
             NProgress.done();
             this.commonService.errorCallback(err, this);
           },
           complete: () => {
             console.log("Completed");
           },
         });
   
    }
  }

  updateMake(){
     const currentUserObj = JSON.parse(localStorage.getItem("currentUser") || "{}");
     const userName = currentUserObj?.userPrincipalName || "";
     const formattedMakeList = {   
      manufacturer: this.selectedMake.manufacturer,
      make_old: this.oldMake,
      make_new: this.selectedMake.make,
      }
      const dbObj = { inData: JSON.stringify([formattedMakeList]),inUserName:userName }; 
      const subscription = this.apollo
        .use("manageScheduler")
        .mutate({
          mutation: updateMake,
          variables: dbObj,
        })
        .pipe(takeUntil(this.subscription$))
        .subscribe({
          next: (listdata:any) => {
            NProgress.done();
            const result: any = listdata;
            this.getMakeList1();
            const parsedJson = JSON.parse(result.data.updateMake.json);
          if (parsedJson.status === "success") {
            this.closeEditMakeModal1();
            // this.openEditMakeModal(this.selectedMan);
              // swal({
              //     title: parsedJson.message,
              //     type: "success",
              //     confirmButtonClass: "btn-success pointer",
              //     confirmButtonText: this.constantService.CLOSE,
              //     allowOutsideClick: false

              //   });
              Swal.fire({
                title: parsedJson.message,
                icon: "success",
                confirmButtonColor: "#28a745",
                confirmButtonText: this.constantService.CLOSE,
                allowOutsideClick: false
              });




              } else {
               this.closeEditMakeModal1();
                const jsonData = JSON.parse(result.data.updateMake.json);
                const makeListKey = Object.keys(jsonData).find(key => key.trim() === "make list");
                const makeList: string[] = makeListKey ? jsonData[makeListKey] : [];
                // swal({
                //       title: parsedJson.message ,
                //       type: "warning",
                //       confirmButtonClass: "btn-warning pointer",
                //       confirmButtonText: this.constantService.CLOSE,
                //       allowOutsideClick: false

                //    });

                Swal.fire({
                  title: parsedJson.message,
                  icon: "info",
                  confirmButtonColor:"#d33",
                  confirmButtonText: this.constantService.CLOSE,
                  allowOutsideClick: false
                });


                  //  Swal.fire({
                  //   title: parsedJson.message ,
                  //   text: "",
                  //   icon: "info",
                  //   confirmButtonText: this.constantService.CLOSE,
                  //   confirmButtonColor: "#d33",
                  //   allowOutsideClick: false,
                  //   confirmButtonClass: "btn-success pointer",
                  // });

                  //  this.openEditMakeModal(this.selectedMan);
           }
         },
          error: (err) => {
            NProgress.done();
            // this.openEditMakeModal(this.selectedMan);
            this.commonService.errorCallback(err, this);

          },
          complete: () => {
            console.log("Completed");
            this.openEditMakeModal(this.selectedMan);
            
          },
        });


   }

  getStoreTypes(callback: any) {
    this.subscription = this.apollo
      .use("manageScheduler")
      .query({
        query: storePayType,
        fetchPolicy: "network-only",
        variables: {
          companyId: this.companyId, // Pass your variable here
        },
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata) => {
          const result: any = listdata;
          const jsonData = result;
          const jsonArrayPaytype = jsonData.data.getStorePaytype;
          const jsonTypeArray = JSON.parse(jsonArrayPaytype);
          this.allPaytypeList = jsonTypeArray;
          if (this.gridApiArcPayType && !this.allPaytypeList) {
            this.rowDataAllPaytypeList = [];
            this.gridApiArcPayType.showNoRowsOverlay();
          }
          this.allArcPaytypeList = [];
          let items: any[] = this.allPaytypeList?.filter(
            (item: any) =>
              item.category == "WARRANTY" || item.category == "CUSTOMER"
          );
          let filteredDataList: any[] = [];
          if (items) {
            for (let i = 0; i < items.length; i++) {
              let obj = {
                category: items[i].category,
                company_id: items[i].company_id,
                description: items[i].description,
                is_determined: items[i].is_determined == false ? "No" : "Yes",
                is_labor_allowed: items[i].is_labor_allowed == false ? "Yes" : "No",
                is_parts_allowed: items[i].is_parts_allowed == false ? "Yes" : "No",
                paytype: items[i].paytype,
              };
              filteredDataList.push(obj);
            }
          } else {
            filteredDataList = [];
          }
          this.allArcPaytypeList = filteredDataList;
          if (callback) {
            callback(true);
          }
        },
        error: (err) => {
          callback(false); // Call the callback function with status false
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }
  setUnassignedPayType() {
    const filterByCategory = (items: any, category: any) =>
      items?.filter((item: any) => item.category === category);
    // Filter result items by base paytype
    const filterByBasePaytype = (items: any, basePaytype: any) =>
      items?.filter((item: any) => item.base_paytype === basePaytype);
    // Update descriptions
    const updateDescriptions = (sourceItems: any, resultItems: any) => {
      sourceItems?.forEach((obj1: any) => {
        const matchingObj = resultItems?.find(
          (obj2: any) => obj2.paytype === obj1.paytype
        );
        if (
          matchingObj &&
          obj1.description === null &&
          matchingObj.description !== null
        ) {
          obj1.description = matchingObj.description;
        }
      });
    };
    // Add IDs to items
    const addIds = (items: any) => {
      return items.map((item: any, index: any) => {
        const id = `${index}:${index + 1}`;
        return { ...item, id };
      });
    };
    // Filter items by  scheduler category
    const warrantyItems = filterByCategory(this.allPaytypeList, "WARRANTY");
    const payTypeItems = filterByCategory(this.allPaytypeList, "CUSTOMER");
    // Filter result items by  local category
    const warrantyResultItems: any[] = filterByBasePaytype(
      this.resPayListData,
      "Warranty"
    );
    const payTypeResultItems: any[] = filterByBasePaytype(
      this.resPayListData,
      "Customer"
    );
    // Find unassigned paytypes
    const arrCustPaytypes: any[] = payTypeItems?.map(
      (item: any) => item.paytype
    );
    this.unAssignCustomeType = payTypeResultItems?.filter(
      (item: any) => !arrCustPaytypes.includes(item.paytype)
    );
    const arrWarPaytypes: any[] = warrantyItems?.map(
      (item: any) => item.paytype
    );
    this.unAssignWarrantyType = warrantyResultItems?.filter(
      (item: any) => !arrWarPaytypes.includes(item.paytype)
    );
    // Update descriptions
    updateDescriptions(payTypeItems, payTypeResultItems);
    updateDescriptions(warrantyItems, warrantyResultItems);
    // // Assign lists
    // if (warrantyItems.length > 0) {
    //   this.warrantyAllList = addIds(warrantyItems);
    // // this.allPaytypeWarrantyList = warrantyItems;
    // }
    // if (payTypeItems.length > 0) {
    //     this.customAllList = addIds(payTypeItems);
    //   // this.allPaytypeCustomerList = payTypeItems;
    // }
    const unassignedCustPayType: any[] = this.unAssignCustomeType;
    const unassignedWarPayType: any[] = this.unAssignWarrantyType;
    this.unassignedPayType = [
      {
        paytype: "CZUR",
        description: null,
        base_paytype: "Customer",
      },
      {
        paytype: "CHAIL",
        description: null,
        base_paytype: "Customer",
      },
      {
        paytype: "CRENT",
        description: null,
        base_paytype: "Customer",
      },
      {
        paytype: "CPEL",
        description: null,
        base_paytype: "Customer",
      },
      {
        paytype: "CP",
        description: "CUSTOMER PAY",
        base_paytype: "Customer",
      },
      {
        paytype: "WHC",
        description: null,
        base_paytype: "Warranty",
      },
      {
        paytype: "WRENT",
        description: null,
        base_paytype: "Warranty",
      },
      {
        paytype: "WH",
        description: null,
        base_paytype: "Warranty",
      },
    ];
    this.rowDataUnassignPay = this.unassignedPayType;
    this.columnDefsUnassignPay = [
      {
        headerName: "Pay Types",
        field: "paytype",
        sortable: true,
        width: 100,
        editable: false,
        cellRenderer: function (params: any) {
          if (params.data) {
            return params.data.paytype;
          } else {
            return "";
          }
        },
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
      },
      {
        headerName: "Description",
        field: "description",
        sortable: true,
        width: 100,
        cellRenderer: function (params: any) {
          if (params.data) {
            return params.data.description;
          } else {
            return "";
          }
        },
        editable: true,
      },
      {
        headerName: "Category",
        field: "category",
        sortable: true,
        width: 100,
        cellRenderer: function (params: any) {
          if (params.data.category != "INTERNAL") {
            return params.data.category;
          } else {
            return "";
          }
        },
        editable: true,
        cellEditor: "agSelectCellEditor",
        cellEditorParams: {
          values: ["WARRANTY", "CUSTOMER"],
        },
      },
      {
        headerName: "Labor Exclude",
        field: "is_labor_allowed",
        sortable: true,
        width: 100,
        cellRenderer: function (params: any) {
          return "No";
        },
        editable: true,
        cellEditor: "agSelectCellEditor",
        cellEditorParams: {
          values: ["Yes", "No"],
        },
      },
      {
        headerName: "Parts Exclude",
        field: "is_parts_allowed",
        sortable: true,
        width: 100,
        cellRenderer: function (params: any) {
          return "No";
        },
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
        editable: true,
        cellEditor: "agSelectCellEditor",
        cellEditorParams: {
          values: ["Yes", "No"],
        },
      },
      {
        headerName: "Action",
        field: "",
        width: 135,
        editable: false,
        cellRenderer: function (params: any) {
          let html = "<span></span>";

          html += `<a href="javascript:void(0);" 
                        data-toggle="tooltip" 
                        data-animation="false" 
                        data-placement="left" 
                        data-note="" 
                        title="Add to Archive">
                       
                        <span class="badge halt-paytype"><i class="fa fa-plus paytypeicon"><span style="display: none">${params.data.paytype}</span></i></span>
                        </a>`;
          html += `<a href="javascript:void(0);" 
                        data-toggle="tooltip" 
                        data-animation="false" 
                        data-placement="left" 
                        data-note="" 
                        title="Add to submission">
                       
                        <span class="badge halt-paytype"><i class="fa fa-plus paytypeicon"><span style="display: none">${params.data.paytype}</span></i></span>
                        </a>`;
          return html;
        },
        // cellRendererFramework: CheckboxCellComponent_,
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
      },
    ];
  }

 
  getMakeList1() {
    this.subscription = this.apollo
      .use("manageScheduler")
      .query<{ data: any }>({
        query: getMakeList,
        fetchPolicy: "network-only", 
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata) => {
          console.log("Get makeList from UI", listdata);

          const getMakeListData = (listdata?.data as any)?.getMakeList;

          if (getMakeListData?.edges) {
            this.makeList = getMakeListData.edges.map((edge: any, index: number) => ({
              id: index,
              manufacturer: edge.node.manufacturer,
              make: edge.node.make,
              isDefault:edge.node.isDefault
            }));
           this.makeFilterList = Array.from(
              new Set(this.makeList.map((item) => item.manufacturer))
            ).map((manufacturer, index) => ({
              id: index + 1, 
              manufacturer: manufacturer,
            }));
            this.makeOrginalList = this.makeList;
             this.makeList = Object.values(
              this.makeOrginalList.reduce((acc, item) => {
                if (!acc[item.manufacturer]) {
                  acc[item.manufacturer] = { 
                    id: item.id, 
                    manufacturer: item.manufacturer, 
                    make: [] as string[],
                    isDefault: false
                  };
                }
                acc[item.manufacturer].make.push(item.make);
            
                if (!item.isDefault) {
                  acc[item.manufacturer].isDefault = false;
                }
                // make isDefault  false
                acc[item.manufacturer].isDefault = false; 
            
                return acc;
              }, {} as Record<string, { id: number; manufacturer: string; make: string[]; isDefault: boolean }>)
            ).map((entry:any) => ({
              ...entry,
              make: entry.make.join(', ') 
            }));         
            this.gridApiMake.setRowData(this.makeList);
          } else {
            console.warn("getMakeList.edges is undefined or empty");
          }
        },
        error: (err) => {
          console.error("Error fetching make list:", err);
        },
        complete: () => {
          console.log("Completed");
        },
      });
}

getPendingApprovalMakeList() {
  this.subscription = this.apollo
    .use("manageScheduler")
    .query<{ getAllMakeApprovals: string }>({
      query: getPendingMakeList,
      fetchPolicy: "network-only",
    })
    .pipe(takeUntil(this.subscription$))
    .subscribe({
      next: (listdata) => {
        console.log("Get Pending Make List from UI", listdata);

        const rawData = (listdata?.data as any)?.getAllMakeApprovals;

        try {
          const parsedData = JSON.parse(rawData); // Parse the JSON string
          console.log("Parsed Make List >>>>", parsedData);

          // ✅ Filter items where is_approved === false
          const filteredList = parsedData.filter((item: any) => item.is_approved === false);

          this.pendingMakeList = filteredList.map((item: any, index: number) => ({
            id: item.id,
            manufacturer: item.manufacturer,
            make: item.make,
            isApproved: item.is_approved,
            createdAt: moment(item.created_at).format('MM-DD-YYYY HH:mm:ss'),
            createdBy: item.created_by,
            companyName:item.company_name
          }));

          console.log("Pending Make List ✅", this.pendingMakeList);

          // If using AG-Grid
          // this.gridApiMake.setRowData(this.pendingMakeList);

        } catch (err) {
          console.error("Error parsing getAllMakeApprovals JSON string", err);
        }
      },
      error: (err) => {
        console.error("Error fetching make list:", err);
      },
      complete: () => {
        console.log("Completed");
      },
    });
}


 
  getMakeAlias() {
   this.subscription = this.apollo
      .use("manageScheduler")
      .query<{ data: any }>({ 
        query: getMakeAlias,
        fetchPolicy: "network-only",
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata) => {
          const getMakeListData = (listdata?.data as any)?.getNonDefaultMakeRules;
         if (getMakeListData?.edges) {
            this.makeAliasList = getMakeListData.edges.map((edge: any) => ({
              id: edge.node.id,
              make: edge.node.makeName,
              alias: edge.node.makeFormat,
              isDefault:edge.node.isDefault
            }));

            console.log("Orghinal makeAliasList:", this.makeAliasList);

            this.makeAliasOrginalList = this.makeAliasList;
        
            this.makeAliasList = Object.values(
              this.makeAliasOrginalList.reduce((acc, item) => {
                  if (!acc[item.make]) {
                      acc[item.make] = { ...item, alias: item.alias };
                  } else {
                      acc[item.make].alias += `,${item.alias}`;
                      acc[item.make].isDefault = acc[item.make].isDefault && item.isDefault;
                  }
                  return acc;
              }, {})
          );
                  
          } else {
            console.warn("getMakeList.edges is undefined or empty");
          }
        },
        error: (err) => {
          console.error("Error fetching make list:", err);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }
  
  
  setArchivedPayType() {

    let self = this;
    this.rowDataAllPaytypeList = [];
    this.rowDataAllPaytypeList = this.allArcPaytypeList;
    if (this.rowDataAllPaytypeList) {
      if (this.gridApiArcPayType) {
        this.gridApiArcPayType.hideOverlay();
      }
    } else {
      if (this.gridApiArcPayType) {
        this.gridApiArcPayType.showNoRowsOverlay();
      }
    }
    this.columnDefsArchivePay = [
      {
        headerName: "Pay Types",
        field: "paytype",
        sortable: true,
        width: 100,
        editable: false,
        cellRenderer: function (params: any) {
          if (params.data) {
            return params.data.paytype;
          } else {
            return "";
          }
        },
      },
      {
        headerName: "Description",
        field: "description",
        sortable: true,
        width: 100,
        cellRenderer: function (params: any) {
          if (params.data) {
            return params.data.description;
          } else {
            return "";
          }
        },
        editable: true,
        onCellValueChanged: (params: any) => {
          self.gridApiArcPayType = params.api;
          self.gridColumnApiModel = params.columnApi;
          const oldValue = params.oldValue;
          const newValue = params.newValue.toString().trim();
          let data = params.data;
          const obj = {
            inCompanyId: data.company_id,
            inPaytype: data.paytype,
            inCategory: data.category,
            inIsPartsAllowed:
              data.is_parts_allowed == "Yes"
                ? false
                : data.is_parts_allowed == "No"
                ? true
                : true,
            inIsLaborAllowed:
              data.is_labor_allowed == "Yes"
                ? false
                : data.is_labor_allowed == "No"
                ? true
                : true,
            inDescription: newValue,
            inIsDetermined: data.is_determined,
          };
          // self.selectedArcList.push(obj);
        },
      },
      {
        headerName: "Category",
        field: "category",
        sortable: true,
        resizable: true,
        width: 100,
        editable: (params: any) => {
          return params.data.category;
        },
        cellEditor: "agRichSelectCellEditor",
        cellEditorPopup: true,
        cellEditorParams: (params: any) => {
          self.gridApiArcPayType = params.api;
          self.gridColumnApiArcPayType = params.columnApi;
          return {
            values: ["WARRANTY", "CUSTOMER"],
          };
        },
        onCellValueChanged: (params: any) => {
          self.gridApiArcPayType = params.api;
          self.gridColumnApiModel = params.columnApi;
          const oldValue = params.oldValue;
          const newValue = params.newValue.toString().trim();
          let data = params.data;
          const obj = {
            inCompanyId: data.company_id,
            inPaytype: data.paytype,
            inCategory: newValue,
            inIsPartsAllowed:
              data.is_parts_allowed == "Yes"
                ? false
                : data.is_parts_allowed == "No"
                ? true
                : true,
            inIsLaborAllowed:
              data.is_labor_allowed == "Yes"
                ? false
                : data.is_labor_allowed == "No"
                ? true
                : true,
            inDescription: data.description,
            inIsDetermined: data.is_determined,
          };
          // self.selectedArcList.push(obj);
        },
      },
      
      {
        headerName: "Parts Exclude",
        field: "is_parts_allowed",
        sortable: true,
        width: 100,
        editable: (params: any) => {
          return params.data.is_parts_allowed;
        },
        cellEditorPopup: true,
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
        cellEditor: "agRichSelectCellEditor",
        cellEditorParams: (params: any) => {
          self.gridApiArcPayType = params.api;
          self.gridColumnApiArcPayType = params.columnApi;
          return {
            values: ["Yes", "No"],
          };
        },

        onCellValueChanged: (params: any) => {
          self.gridApiArcPayType = params.api;
          self.gridColumnApiModel = params.columnApi;
          const oldValue = params.oldValue;
          const newValue = params.newValue.toString().trim();
          let data = params.data;

          // const obj = {
          //   // discount_list: params.data.,
          //   // estimation_value: newValue,
          //   // new_index: indexer,
          //   // is_job_level_only: params.data.,
          //   inCompanyId: data.company_id,
          //   inPaytype: data.paytype,
          //   inCategory: data.category,
          //   inIsPartsAllowed: newValue == "Yes" ? true : newValue == "No" ? false : false,
          //   inIsLaborAllowed:
          //     data.is_labor_allowed == "Yes" ? true : data.is_labor_allowed == "No" ? false : false,
          //   inDescription: data.description,
          //   inIsDetermined: data.is_determined,
          // };
          // self.selectedArcList.push(obj);
        },
      },
      {
        headerName: "Labor Exclude",
        field: "is_labor_allowed",
        sortable: true,
        width: 100,
        editable: (params: any) => {
          return params.data.is_labor_allowed;
        },
        cellEditorPopup: true,
        cellEditor: "agRichSelectCellEditor",
        cellEditorParams: (params: any) => {
          self.gridApiArcPayType = params.api;
          self.gridColumnApiArcPayType = params.columnApi;
          return {
            values: ["Yes", "No"],
          };
        },
        onCellValueChanged: (params: any) => {
          self.gridApiArcPayType = params.api;
          self.gridColumnApiModel = params.columnApi;
          const oldValue = params.oldValue;
          const newValue = params.newValue.toString().trim();
          let data = params.data;
          // const obj = {
          // discount_list: params.data.,
          // estimation_value: newValue,
          // new_index: indexer,
          // is_job_level_only: params.data.,
          // inCompanyId: data.company_id,
          // inPaytype: data.paytype,
          // inCategory: data.category,
          // inIsPartsAllowed:
          //   data.is_parts_allowed == "Yes" ? true : data.is_parts_allowed == "No" ? false : false,
          // inIsLaborAllowed: newValue == "Yes" ? true : newValue == "No" ? false : false,
          // inDescription: data.description,
          // inIsDetermined: data.is_determined,
          //};
          // self.selectedArcList.push(obj);
        },
      },
      {
        headerName: "Archived",
        field: "is_determined",
        sortable: true,
        width: 100,
        // cellRenderer: function (params: any) {
        //   if (params.data) {
        //     return params.data.is_determined == false ? "No" : "Yes";
        //   } else {
        //     return "";
        //   }
        // },
        editable: false,
      },
      {
        headerName: "Action",
        field: "",
        width: 100,
        editable: false,
        cellRenderer: function (params: any) {
          const dataString: any = JSON.stringify(params.data);
          let html = ` `;
          if (params.data.is_determined == "No") {
            // html += `<em class="badge badge-info badge-font save-icon" title="Add to Archive" data-toggle="tooltip" data-animation="false" data-placement="top"  data-info='${dataString}'>Add </em>`;
          }
          html += ` <em class="badge badge-danger badge-font" title="Delete" data-toggle="tooltip" data-animation="false" data-placement="top"  data-info='${dataString}'>Delete</em>`;
          return html;
        },
      },
    ];
  }
  /* section for model */
  onGridReadyArcPayType(params: any) {
    this.gridApiArcPayType = params.api;
    this.gridColumnApiArcPayType = params.columnApi;
  }

  onGridReadyMake(params: any) {
    this.gridApiMake = params.api;
    this.gridColumnApiMake = params.columnApi;
  }
  onFirstDataRenderedArcPayType(params: any) {
    this.gridApiArcPayType = params.api;
    this.gridApiArcPayType.sizeColumnsToFit();
  }

  onFirstDataRenderedMake(params: any) {
    this.gridApiMake = params.api;
    this.gridApiMake.sizeColumnsToFit();
  }

  deleteArcPayType(info: any) {
    let delInfo = JSON.parse(info);
    swal(
      {
        title: this.constantService.AREYOUSURE,
        text: "This Paytype will be deleted",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default pointer",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: "Delete",
        showLoaderOnConfirm: true,
        closeOnConfirm: true,
        closeOnCancel: true,
        allowOutsideClick: false

      },
      () => {
        NProgress.start();
        const dbObj = {
          inCompanyId: delInfo.company_id,
          inPaytype: delInfo.paytype,
        };
        let message = "";
        const subscription = this.apollo
          .use("manageScheduler")
          .mutate({
            mutation: deleteStorePaytypeDetails,
            variables: dbObj,
          })
          .pipe(takeUntil(this.subscription$))
          .subscribe({
            next: (listdata) => {
              const result: any = listdata.data;
              NProgress.done();
              const obj = JSON.parse(
                result["deleteStorePaytypeDetails"]["json"]
              );
              if (obj.status === this.constantService.SUCCESS_MESSAGE) {
                //  message = "Delete successfully";
                // this.showStatusMessage(message, "success");
                this.getStoreTypes(() => {
                  this.setArchivedPayType();
                });
                this.gridApiArcPayType.refreshCells({ force: true });
              } else {
                message = obj.reason;
                swal({
                  title: message,
                  type: "warning",
                  confirmButtonClass: "btn-warning pointer",
                  confirmButtonText: this.constantService.CLOSE,
                  allowOutsideClick: false

                });
              }
            },
            error: (err) => {
              NProgress.done();
              message = this.constantService.CANNOT_FETCH_DATA;
              swal({
                title: message,
                type: "warning",
                confirmButtonClass: "btn-warning pointer",
                confirmButtonText: this.constantService.CLOSE,
                allowOutsideClick: false

                
              });
              this.commonService.errorCallback(err, this);
            },
            complete: () => {
              console.log("Completed");
            },
          });

        this.subscriptionList.push(subscription);
        return subscription;
      }
    );
  }

  onCellValueChanged(event: any) {
    console.log(
      "onCellValueChanged: " + event.colDef.field + " = " + event.newValue
    );
  }

  onRowValueChanged(event: any) {
    var data = event.data;
    console.log(
      "onRowValueChanged: (" +
        data.make +
        ", " +
        data.model +
        ", " +
        data.price +
        ", " +
        data.field5 +
        ")"
    );
  }
  onCancelEditing() {
    this.gridApiArcPayType.stopEditing();
  }

  cancelOtherRowEditors(currentRowIndex: any) {
    const renderers = this.gridApiArcPayType.getCellRendererInstances();
    renderers.forEach(function (renderer: any) {
      if (
        !renderer.rowEditComponent?.isNew &&
        currentRowIndex !== renderer.rowEditComponent?.params.node.rowIndex
      ) {
        renderer.rowEditComponent.onCancelClick();
      }
    });
  }

  onCellClicked(params: any) {
    if (params.node.field !== "action") {
      this.cancelOtherRowEditors(params.node.rowIndex);
    }
  }

  createRowData() {
    var rowData = [];
    for (var i = 0; i < 15; i++) {
      rowData.push({
        row: "Row " + i,
        value1: i,
        value2: i + 10,
        value3: i + 30,
        currency: i + Number(Math.random().toFixed(2)),
      });
    }
    return rowData;
  }

  updateArcPayType(flag: any) {
    swal(
      {
        title: this.constantService.AREYOUSURE,
        text: flag == "Add" ? "Paytype will be archived" : "This paytype will be updated ",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default pointer",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: "Ok",
        showLoaderOnConfirm: true,
        closeOnConfirm: true,
        closeOnCancel: true,
        allowOutsideClick: false
      },
      () => {
        this.showSpinnerStoreButton = true;
        NProgress.start();
        this.selectedRowsArc = this.gridApiArcPayType.getSelectedRows();
        const UpdatedPaytype = flag == "Add" ? this.selectedRowsArc : this.rowDataAllPaytypeList;
        const UpdatedPaytypeList = UpdatedPaytype.map((item: any) => {
          if (item.is_parts_allowed === "No") {
            item.is_parts_allowed = true;
          } else if (item.is_parts_allowed === "Yes") {
            item.is_parts_allowed = false;
          }
          if (item.is_labor_allowed === "No") {
            item.is_labor_allowed = true;
          } else if (item.is_labor_allowed === "Yes") {
            item.is_labor_allowed = false;
          }
          if (item.is_determined === "Yes") {
            item.is_determined = true;
          } else if (item.is_determined === "No") {
            item.is_determined = flag == "Add" ? true : false;
          }
          return item;
        });
        const dbObj = { payTypeDet: JSON.stringify(UpdatedPaytypeList) };
        const subscription = this.apollo
          .use("manageScheduler")
          .mutate({
            mutation: storePaytypeDetailsUpdation,
            variables: dbObj,
          })
          .pipe(takeUntil(this.subscription$))
          .subscribe({
            next: (listdata) => {
              NProgress.done();
              const result: any = listdata;

              if (listdata) {
                const data = listdata.data as {
                  storePaytypeDetailsUpdation: { json: string };
                };
                const status = JSON.parse(
                  data.storePaytypeDetailsUpdation.json
                ).status;
                // Now you can check the value of 'status' to see if it's "success"
                if (status == "success") {
                  setTimeout(() => {
                    this.getStoreTypes(() => {
                      this.setArchivedPayType();
                    });
                    //  this.gridApiArcPayType.refreshCells();
                  }, 100);

                  this.showSpinnerStoreButton = false;
                  swal({
                    title: "Paytype Saved Successfully",
                    type: "success",
                    confirmButtonClass: "btn-success pointer",
                    confirmButtonText: this.constantService.CLOSE,
                    allowOutsideClick: false

                  });
                } else {
                  swal({
                    title: "Paytype Saved Failed",
                    type: "success",
                    confirmButtonClass: "btn-success pointer",
                    confirmButtonText: this.constantService.CLOSE,
                    allowOutsideClick: false

                  });
                }
              }
            },
            error: (err) => {
              NProgress.done();
              this.commonService.errorCallback(err, this);
            },
            complete: () => {
              console.log("Completed");
            },
          });

        this.subscriptionList.push(subscription);
        return subscription;
      }
    );
  }


  updateMakeList(flag: any) {
      const formattedMakeList = this.modifiedMake.map(item => ({
        manufacturer: item.manufacturer,
        make_old: item.oldMake,
        make_new: item.editedMake 
      }));
      const dbObj = { inData: JSON.stringify(formattedMakeList) };  
      const subscription = this.apollo
        .use("manageScheduler")
        .mutate({
          mutation: updateMake,
          variables: dbObj,
        })
        .pipe(takeUntil(this.subscription$))
        .subscribe({
          next: (listdata:any) => {
            NProgress.done();
            const result: any = listdata;
            this.getMakeList1();
            const parsedJson = JSON.parse(result.data.updateMake.json);
           if (parsedJson.status == "success") {
                swal({
                  title: parsedJson.message,
                  type: "success",
                  confirmButtonClass: "btn-success pointer",
                  confirmButtonText: this.constantService.CLOSE,
                  allowOutsideClick: false

                });
              } else {
              
                const jsonData = JSON.parse(result.data.updateMake.json);

                const makeListKey = Object.keys(jsonData).find(key => key.trim() === "make list");
            
                const makeList: string[] = makeListKey ? jsonData[makeListKey] : [];
                
                console.log(makeList); 
                swal({
                      title:  `Make ${makeList.join(', ')} already in list`,
                      type: "warning",
                      confirmButtonClass: "btn-warning pointer",
                      confirmButtonText: this.constantService.CLOSE,
                      allowOutsideClick: false

                   });
           }

  
          },
          error: (err) => {
            NProgress.done();
            this.commonService.errorCallback(err, this);
          },
          complete: () => {
            console.log("Completed");
          },
        });

     this.modifiedMake =[];


  }


  showStatusMessage(message: any, statusType: any) {
    if (statusType === "success") {
      this.toastrService.success (message);
    } else {
      this.toastrService.error(message);
    }
  }

  isFieldValidCreateSchedule(field: string) {
    let retValue: any = null;
    retValue =
      !this.createSchedule.get(field)?.valid &&
      this.createSchedule.get(field)?.touched;
    return retValue;
  }

  displayFieldCssCreateSchedule(field: string) {
    return {
      "has-danger": this.isFieldValidCreateSchedule(field),
    };
  }
  /**
   * callback function for dms group selection
   *
   */
  onSelectDMSGroup(item: any) {
    if (item.itemName) {
      this.dmsList = item.itemName;
    } else {
      this.dmsList = item;
    }
    if (this.gridApiPRate) {
      this.gridApiPRate.showLoadingOverlay();
    }
    this.getDepartment((status: boolean) => {
      this.setDepartmentGrid();
    });
    this.dmsItems = item;
  }
  /**
   * callback function for dms group deselection
   *
   */
  OnDeSelectDMSGroup(item: any) {
    this.rowDataDept = [];
    this.dmsSelectItems = [];
  }
  /**
   * callback function for store group selection
   *
   */
  onSelectStoreGroup(item: any) {
    this.selectStoreGroup(item, () => {});
  }

  /**
   * callback function for store selection
   *
   */
  onSelectStore(items: any) {
    if (this.gridApiArcPayType) {
      this.gridApiArcPayType.showLoadingOverlay();
    }
    this.loading = true;
    // this.store = [];
    this.getStoreFilterList();
    const matchedItem = this.storeFilterList?.find(
      (item) => item.itemName === items.itemName
    );
    if (matchedItem) {
      this.companyId = matchedItem.companyID;
    }
    if (!this.containsObject(items, this.store)) {
      this.store.push(items);
    }
    const selectedStoreFilter: any[] = this.storeList.filter(function (res) {
      return res.stId == items.id;
    });
    this.selectedStore = selectedStoreFilter[0];
    const matchedItems: any[] = this.storeList.filter(
      (storeitem) => storeitem.companyName == items.id
    );
    if (matchedItems.length > 0) {
      const obj = {
        id: matchedItems[0].dmsCode,
        itemName: matchedItems[0].dmsCode,
      };
      if (!this.containsObject(obj, this.versionFilterList)) {
        this.versionFilterList.push(obj);
      }
      this.loading = false;
      this.onSelectversion(obj);
      //this.version = "fff";//matchedItems[0].dmsCode;
    }
    this.versionFilterList = this.sortListAsc(this.versionFilterList);
    const filteredData: any[] = this.storeList.filter((item) =>
      items.id.includes(item.companyName)
    );
    const companyIds = filteredData.map((item) => item.companyId);
    this.companyIds = companyIds;
    this.getStoreTypes(() => {
      this.setUnassignedPayType();
      this.setArchivedPayType();
    });
    this.storeFlag = true;
  }
  /**
   * callback function for store deselection
   *
   */
  OnDeSelectStore(item: any) {
    this.store = [];
    // if (!this.containsObject(item, this.store)) {
    //   this.store.push(item);
    // }
  }
  selectStoreGroup(item: any, callback: any) {
    this.storeList = [];
    this.storeFilterList = [];
    this.store = [];
    this.storeFlag = false;
    if (!this.containsObject(item, this.storeGroup)) {
      this.storeGroup.push(item);
    }
    let selectedGroupFilter: any[] = this.storeGroupList.filter(function (res) {
      return res.sgId == item.id;
    });
    this.selectedGroup = selectedGroupFilter[0];
    //this.getGroupFilterList();
    let itemFilter: any = this.storeGroupFilterList.filter(function (res) {
      return res.id == item.id; //updated as per new array list
    });
    this.getStoreList("", itemFilter[0], () => {
      this.getStoreFilterList();
      if (callback) {
        callback();
      }
    });
  }
  containsObject(obj: any, list: any) {
    let i;
    if (list && list.length && obj) {
      for (i = 0; i < list.length; i++) {
        if (list[i].id === obj.id) {
          return true;
        }
      }
    }
    return false;
  }
  /**
   * getStoreList function fetch Stores list for the selected StoreGroup
   *
   */
  getStoreList(type: any, item: any, callback: any) {
    // const groupCode = item.mageGroupCode;
    const groupCode = item.mageGroupName;
    this.loading = true;
    this.storeList = [];
    this.storeList = this.jobGroupList
      .filter((item: any, index: number) => index < this.jobGroupList.length)
      .filter(
        (item: any, index: number) =>
          item.mageGroupName != null &&
          item.mageGroupName === groupCode &&
          item.thirdPartyUsername !== null
      );
    //.filter((item: any, index: number) => item.dmsCode.toLowerCase() == type.toLowerCase());

    this.loading = false;
    if (callback) {
      callback();
    }
  }
  /**
   * getGroupFilterList function will collect the group list for filtering purpose
   *
   */
  getGroupFilterList() {
    this.storeGroupFilterList = [];
    for (let i = 0; i < this.storeGroupList.length; i++) {
      const companyName = this.storeGroupList[i].mageGroupName;
      const mageGroupCode = this.storeGroupList[i].mageGroupCode;
      const mageGroupName = this.storeGroupList[i].mageGroupName
        ? this.storeGroupList[i].mageGroupName
        : "";
      const mageStoreCode = this.storeGroupList[i].mageStoreCode;
      const mageStoreName = this.storeGroupList[i].mageStoreName;
      const projectId = this.storeGroupList[i].projectId;
      const secondProjectId = this.storeGroupList[i].secondaryProjectId;
      const companyId = this.storeGroupList[i].companyId;
      const parentName = this.storeGroupList[i].companyName;
      if (companyName) {
        const obj = {
          id: companyName,
          itemName: companyName,
          mageGroupCode: mageGroupCode,
          mageGroupName: mageGroupName,
          mageStoreCode: mageStoreCode,
          mageStoreName: mageStoreName,
          projectId: projectId,
          companyId: companyId,
          parentName: parentName,
          secondProjectId: secondProjectId,
        };
        if (!this.containsObject(obj, this.storeGroupFilterList)) {
          this.storeGroupFilterList.push(obj);
        }
      }
    }
    this.storeGroupFilterList = this.sortListAsc(this.storeGroupFilterList);
  }
  /**
   * sortListAsc function will sort the list in ascending order.
   *
   */
  sortListAsc(temp: any) {
    temp = temp
      .filter((item: any, index: number) => index < temp.length)
      .sort((a: any, b: any): any => {
        const x: any = a["itemName"].toLowerCase().replace(/^[^a-z0-9]*/g, "");
        const y: any = b["itemName"].toLowerCase().replace(/^[^a-z0-9]*/g, "");
        return x < y ? -1 : x > y ? 1 : 0;
      });
    return temp;
  }
  /**
   * getStoreFilterList function will collect the store list for filtering purpose
   *
   */
  getStoreFilterList() {
    this.storeFilterList = [];
    for (let i = 0; i < this.storeList.length; i++) {
      const companyID = this.storeList[i].companyId;
      const storeName = this.storeList[i].companyName;
      const stId = this.storeList[i].companyName;
      const dmsCode = this.storeList[i].dmsCode;
      const mageGroupCode = this.storeList[i].mageGroupName;
      const mageStoreName = this.storeList[i].mageStoreName;
      const mageStoreCode = this.storeList[i].mageStoreCode;
      const thirdPartyUsername = this.storeList[i].thirdPartyUsername;
      const glAccountCompanyID = this.storeList[i].dealerbuiltSourceId;
      const stateCode = this.storeList[i].state;
      const projectId = this.storeList[i].projectId;
      const secondProjectId = this.storeList[i].secondaryProjectId;
      const solve360Update = this.storeList[i].solve360Update;
      const buildProxies = this.storeList[i].buildProxies;
      const includeMetaData = this.storeList[i].includeMetaData;
      const extractAccountingData = this.storeList[i].extractAccountingData;
      const dualProxy = this.storeList[i].dualProxy;
      const mageManufacturer = this.storeList[i].mageManufacturer;
      if (stId) {
        const obj = {
          id: stId,
          itemName: `${storeName} [${thirdPartyUsername}]`,
          mageGroupCode: mageGroupCode,
          mageStoreName: mageStoreName,
          mageStoreCode: mageStoreCode,
          thirdPartyUsername: thirdPartyUsername,
          stateCode: stateCode,
          projectId: projectId,
          solve360Update: solve360Update,
          buildProxies: buildProxies,
          includeMetaData: includeMetaData,
          companyID: companyID,
          glAccountCompanyID: glAccountCompanyID,
          extractAccountingData: extractAccountingData,
          mageManufacturer: mageManufacturer,
          dualProxy: dualProxy,
          secondProjectId: secondProjectId,
          dmsCode: dmsCode,
        };
        if (!this.containsObject(obj, this.storeFilterList)) {
          this.storeFilterList.push(obj);
        }
      }
    }
    this.storeFilterList = this.sortListAsc(this.storeFilterList);

    let activityDataSchedule = {
      activityName: "CDK3PA : Store filter list",
      activityType: "Store filter list",
      activityDescription: `CDK3PA Store filter list: ${JSON.stringify(this.storeFilterList)}`,
    };
    this.commonService.saveActivity("Manage CDK3PA", activityDataSchedule);
  }
  reloadGroupList() {
    this.closeToolTip();
    this.reloadGroup = true;
    this.commonService.allS360Jobs("", "production", (result: any) => {
      this.getGroupFilterList();
      this.reloadGroup = false;
    });
  }
  closeToolTip() {
    $("[rel=tooltip]").tooltip("enable");
    $(".tooltip").tooltip("hide");
  }
  /**
   * callback function for DM version selection
   *
   */
  onSelectversion(item: any) {
    let itemFiltered: any = this.versionFilterList.filter(
      (res) => res.id == item.id
    );
    this.version = itemFiltered;
    if (!this.containsObject(item, this.version)) {
      this.version.push(itemFiltered[0]);
    }
  }

  /**
   * callback function for store group deselection
   *
   */
  OnDeSelectStoreGroup(item: any) {
    // if (!this.containsObject(item, this.storeGroup)) {
    //   this.storeGroup.push(item);
    // }
    // this.getGroupFilterList();
    this.store = [];
    this.storeFilterList = [];
    this.rowDataAllPaytypeList = [];
    this.createSchedule.reset();
    this.getGroupFilterList();
  }
  onSelectionArc(params: any) {
    // $(".tooltip").tooltip("hide");
    // $(".tooltip").trigger("click");
    this.selectedRowsArc = params.api.getSelectedRows();
  }
  onSelectionDept(params: any) {
    // $(".tooltip").tooltip("hide");
    // $(".tooltip").trigger("click");
    this.selectedRowsDept = params.api.getSelectedRows();
  }

  onSelectionMake(params: any) {
    this.selectedRowsMake = params.api.getSelectedRows();
    console.log("Selected Rows:", this.selectedRowsMake);
  
    this.selectedRowsMake.forEach((selectedRow: any) => {
      // Find if the row is already in modifiedMake
      const existingIndex = this.modifiedMake.findIndex(
        (item) => item.manufacturer === selectedRow.manufacturer && item.oldMake === selectedRow.make
      );
  
      if (existingIndex !== -1) {
        // Update existing record
        this.modifiedMake[existingIndex].editedMake = selectedRow.make;
      } else {
        // Store the original value before editing
        const originalRow = this.makeList.find(
          (row: any) => row.manufacturer === selectedRow.manufacturer && row.make === selectedRow.make
        );
  
        if (originalRow) {
          this.modifiedMake.push({
            manufacturer: selectedRow.manufacturer,
            oldMake: originalRow.make, // Store the original make
            editedMake: selectedRow.make, // Store the new edited make
          });
        }
      }
    });
  
    console.log("Modified Make List:", this.modifiedMake);
  }
  

  /* start dept*/
  setDepartmentGrid() {
    const self = this;
    let data = [];
    this.rowDataDept = this.allDepartmentList;
    if (this.rowDataDept) {
      if (this.gridApiPRate) {
        this.gridApiPRate.hideOverlay();
      }
    } else {
      if (this.gridApiPRate) {
        this.gridApiPRate.showNoRowsOverlay();
      }
    }
    this.columnDefsPayTypeRate = [
      {
        headerName: "Department",
        field: "dept",
        sortable: true,
        width: 180,
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
        cellRenderer: function (params: any) {
          if (params.data.dept) {
            return params.data.dept;
          } else {
            return "default";
          }
        },
      },
      {
        headerName: "DMS",
        field: "dms",
        sortable: true,
        width: 100,
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
        cellRenderer: function (params: any) {
          if (params.data.dms) {
            return params.data.dms;
          } else {
            return "";
          }
        },
      },
      {
        headerName: "Description",
        field: "description",
        sortable: true,
        width: 130,
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
        cellRenderer: function (params: any) {
          if (params.data.description) {
            return params.data.description;
          } else {
            return "";
          }
        },
        editable: true,
        onCellValueChanged: (params: any) => {
          self.selectedDeptList = [];
          self.gridApiPRate = params.api;
          self.gridColumnApiPRate = params.columnApi;
          const oldValue = params.oldValue;
          const newValue = params.newValue.toString().trim();
          let data: any = params.data;
          const obj = {
            inDept: data.dept,
            inDms: data.dms,
            inDescription: data.description,
            inIsAllowed: data.is_allowed == "Yes" ? false : data.is_allowed == "No" ? true : true,
            inIsDetermined: data.is_determined,
          };
          this.selectedDeptList.push(obj);
        },
      },
      {
        headerName: "IsAllowed?",
        field: "is_allowed",
        width: 80,
        cellRendererFramework: CheckboxCellAdminDeptComponent      
      },
      {
        headerName: "Archived",
        field: "is_determined",
        sortable: true,
        width: 130,
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
        cellRenderer: function (params: any) {
          if (params.data && params.data.is_determined != null) {
            return params.data.is_determined == false ? "No" : "Yes";
          } else {
            return "";
          }
        },
        editable: false,
      },
      {
        headerName: "Action",
        field: "",
        width: 100,
        editable: false,
        cellRenderer: function (params: any) {
          const dataString: any = JSON.stringify(params.data);
          let html = ` `;
          if (params.data.is_determined == false) {
            html += `<em class="badge badge-info badge-font save-icon Dept" title="Save" data-toggle="tooltip" data-animation="false" data-placement="top"  data-info='${dataString}'>Add</em>`;
          }
          html += ` <em class="badge badge-danger badge-font Dept" title="Delete" data-toggle="tooltip" data-animation="false" data-placement="top"  data-info='${dataString}'>Delete</em>`;
          return html;
        },
      },
    ];
  }


  setMakeGrid() {
    const self = this;
    let data = [];
    this.rowDataMake = this.makeList;
    if (this.rowDataMake) {
      if (this.gridApiMake) {
        this.gridApiMake.hideOverlay();
      }
    } else {
      if (this.gridApiMake) {
        this.gridApiMake.showNoRowsOverlay();
      }
    }
    this.columnDefsMake = [
      {
        headerName: "ID",
        field: "id",
        hide: true,
      },
      {
        headerName: "Manufacturer",
        field: "manufacturer",
        sortable: true,
        width: 100,
        editable: false,
        sort: 'asc',
      },
      {
        headerName: "Make",
        field: "make",
        sortable: true,
        width: 100,
        editable: false,
        onCellValueChanged: (params: any) => {
          if (params.oldValue !== params.newValue) {
            const existingIndex = this.modifiedMake.findIndex(
              (item) => item.manufacturer === params.data.manufacturer && item.oldMake === params.oldValue
            );
    
            if (existingIndex !== -1) {
              this.modifiedMake[existingIndex].editedMake = params.newValue;
            } else {
              this.modifiedMake.push({
                manufacturer: params.data.manufacturer,
                oldMake: params.oldValue,
                editedMake: params.newValue,
              });
            }
  
          }
        },
      },
      {
        headerName: "isDefault",
        field: "isDefault",
        hide: true,
        sort: "asc", 
        sortIndex: 0,  
      },
      {
        headerName: "Action",
        field: "",
        width: 135,
        editable: false,
        cellRenderer: function (params:any) {
          const dataString: any = JSON.stringify(params.data);
          let html ='';
          // if (params.data.isDefault === false) {
            html = ` 
            <em class="badge badge-primary badge-font edit-make" 
                title="Edit" 
                data-toggle="tooltip" 
                data-animation="false" 
                data-placement="top"  
                data-info='${dataString}' 
                style="cursor: pointer; margin-right: 5px;">
                View
            </em>
            `;
          // }else{
          //   html='';
          // }

       
    
        return html;
        
  
        },
        cellClass: () => "left-aligned-cell-t",
      },
    ];
 }
 setEditMakeGrid() {
    const self = this;
    let data = [];
    this.rowDataMake = this.editMakeListData;
    if (this.rowDataMake) {
      if (this.gridApiMake) {
        this.gridApiMake.hideOverlay();
      }
    } else {
      if (this.gridApiMake) {
        this.gridApiMake.showNoRowsOverlay();
      }
    }
    this.columnDefsMake1 = [
      {
        headerName: "ID",
        field: "id",
        hide: true,
      },
      {
        headerName: "Manufacturer",
        field: "manufacturer",
        sortable: true,
        width: 100,
        editable: false,
        sort: 'asc',
      },
      {
        headerName: "Make",
        field: "make",
        sortable: true,
        width: 100,
        editable: false,
        onCellValueChanged: (params: any) => {
          if (params.oldValue !== params.newValue) {
            const existingIndex = this.modifiedMake.findIndex(
              (item) => item.manufacturer === params.data.manufacturer && item.oldMake === params.oldValue
            );
    
            if (existingIndex !== -1) {
              this.modifiedMake[existingIndex].editedMake = params.newValue;
            } else {
              this.modifiedMake.push({
                manufacturer: params.data.manufacturer,
                oldMake: params.oldValue,
                editedMake: params.newValue,
              });
            }
  
          }
        },
      },
      {
        headerName: "isDefault",
        field: "isDefault",
        hide: true,
        sort: "asc", 
        sortIndex: 0,  
      },
      {
        headerName: "Action",
        field: "",
        width: 135,
        editable: false,
        cellRenderer: function (params:any) {
          const dataString: any = JSON.stringify(params.data);
          let html ='';
          // if (params.data.isDefault === false) {
            html = ` 
            <em class="badge badge-primary badge-font edit-make-inner" 
                title="Edit" 
                data-toggle="tooltip" 
                data-animation="false" 
                data-placement="top"  
                data-info='${dataString}' 
                style="cursor: pointer; margin-right: 5px;">
                Edit
            </em>
            <em class="badge badge-danger badge-font delete-make-inner" 
                title="Delete" 
                data-toggle="tooltip" 
                data-animation="false" 
                data-placement="top"  
                data-info='${dataString}' 
                style="cursor: pointer;">
                Delete
            </em>`;
          // }else{
          //   html='';
          // }

       
    
        return html;
        
  
        },
        cellClass: () => "left-aligned-cell-t",
      },
    ];

    // this.gridApiMake.refreshCells({ force: true });

  }
  getDepartment(callback: any) {
    let activityDataSchedule = {
      activityName: "Fetch Department from back end point",
      activityType: "Department List",
      activityDescription: `Fetch Department List from api.
    )})`,
    };
    this.commonService.saveActivity("Scheduler Import", activityDataSchedule);
    this.subscription = this.apollo
      .use("manageScheduler")
      .query({
        query: getDepartmentDetails,
        fetchPolicy: "network-only",
        variables: { inDms: this.dmsList },
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          const jsonData = result;
          //Extract the array from the object
          const jsonArrayPaytype = JSON.parse(result.data.getDepartmentDetails);
          this.allDepartmentList = jsonArrayPaytype;
          callback(true); // Call the callback function with status true
        },
        error: (err: any) => {
          callback(false); // Call the callback function with status false
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }
  onFirstDataRenderedPRate(params: any) {
    this.gridApiPRate = params.api;
    this.gridApiPRate.sizeColumnsToFit();
    this.changeDetectorRefCheck();
  }

  onGridReadyPRate(params: any) {
    this.gridApiPRate = params.api;
    this.gridColumnApiPRate = params.columnApi;
    this.gridApiPRate.showLoadingOverlay();
    this.changeDetectorRefCheck();
  }
  changeDetectorRefCheck() {
    this.changeDetectorRef.markForCheck();
  }
  updateDeptType(flag: any) {
    this.selectedRowsDept = this.gridApiPRate.getSelectedRows();

    const UpdatedPaytypeList = flag == "Add" ? this.selectedRowsDept : this.rowDataDept;
    let dbObj: any;
    if (flag == "Add") {
      // Update the is_determined property to true for all objects in the UpdatedPaytypeList
      const updatedPaytypeList = UpdatedPaytypeList.map((item: any) => {
        return { ...item, is_determined: true };
      });

      dbObj = { depDet: JSON.stringify(updatedPaytypeList) };
    } else {
      dbObj = { depDet: JSON.stringify(UpdatedPaytypeList) };
    }
    swal(
      {
        title: this.constantService.AREYOUSURE,
        text:
          flag == "Add"
            ? "Department is archived"
            : "This Department will be updated",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default pointer",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: "Ok",
        showLoaderOnConfirm: true,
        closeOnConfirm: true,
        closeOnCancel: true,
        allowOutsideClick: false

      },
      () => {
        this.showSpinnerStoreButton = true;
        const subscription = this.apollo
          .use("manageScheduler")
          .mutate({
            mutation: departmentDetailsInfo,
            variables: dbObj,
          })
          .pipe(takeUntil(this.subscription$))
          .subscribe({
            next: (listdata) => {
              NProgress.done();
              const result: any = listdata;
              if (listdata) {
                const data = listdata.data as {
                  departmentDetailsInfo: { json: string };
                };
                const status = JSON.parse(data.departmentDetailsInfo.json).status;
                // Now you can check the value of 'status' to see if it's "success"
                if (status == "success") {
                  this.getDepartment((status: boolean) => {
                    this.setDepartmentGrid();
                  });
                  this.gridApiPRate.refreshCells();
                  this.showSpinnerStoreButton = false;
                  swal({
                    title: "Department Saved Successfully",
                    type: "success",
                    confirmButtonClass: "btn-success pointer",
                    confirmButtonText: this.constantService.CLOSE,
                    allowOutsideClick: false

                  });
                } else {
                  swal({
                    title: "Department Saved Failed",
                    type: "success",
                    confirmButtonClass: "btn-success pointer",
                    confirmButtonText: this.constantService.CLOSE,
                    allowOutsideClick: false

                  });
                }
              }
            },
            error: (err) => {
              NProgress.done();
              this.commonService.errorCallback(err, this);
            },
            complete: () => {
              console.log("Completed");
            },
          });

        this.subscriptionList.push(subscription);
        return subscription;
      }
    );
  }

  deleteDeptType(info: any) {
    swal(
      {
        title: this.constantService.AREYOUSURE,
        text: "This Department will be deleted",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default pointer",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: "Delete",
        showLoaderOnConfirm: true,
        closeOnConfirm: true,
        closeOnCancel: true,
        allowOutsideClick: false

      },
      () => {
        NProgress.start();
        let delInfo = JSON.parse(info);
        const dbObj = {
          inDept: delInfo.dept,
          inDms: delInfo.dms,
        };
        let message = "";
        const subscription = this.apollo
          .use("manageScheduler")
          .mutate({
            mutation: deleteDepartmentDetails,
            variables: dbObj,
          })
          .pipe(takeUntil(this.subscription$))
          .subscribe({
            next: (listdata) => {
              const result: any = listdata.data;
              NProgress.done();
              const obj = JSON.parse(result["deleteDepartmentDetails"]["json"]);
              if (obj.status === this.constantService.SUCCESS_MESSAGE) {
                swal({
                  title: "Department deleted Successfully",
                  type: "success",
                  confirmButtonClass: "btn-success pointer",
                  confirmButtonText: this.constantService.CLOSE,
                  allowOutsideClick: false

                });
                this.getDepartment((status: boolean) => {
                  this.setDepartmentGrid();
                });
                this.gridApiPRate.refreshCells({ force: true });
              } else {
                message = obj.reason;
                swal({
                  title: message,
                  type: "warning",
                  confirmButtonClass: "btn-warning pointer",
                  confirmButtonText: this.constantService.CLOSE,
                  allowOutsideClick: false

                });
              }
            },
            error: (err) => {
              NProgress.done();
              message = this.constantService.CANNOT_FETCH_DATA;
              swal({
                title: message,
                type: "warning",
                confirmButtonClass: "btn-warning pointer",
                confirmButtonText: this.constantService.CLOSE,
                allowOutsideClick: false

              });
              this.commonService.errorCallback(err, this);
            },
            complete: () => {
              console.log("Completed");
            },
          });

        this.subscriptionList.push(subscription);
        return subscription;
      }
    );
  }


  deleteMake(info: any) {
    this.closeEditMakeModal();
    // console.log("swal version ***********************************", Swal.VERSION);
  
    Swal.fire({
      title: this.constantService.AREYOUSURE,
      text: "This make will be deleted",
      icon: "warning",
      showCancelButton: true,
      cancelButtonText: "Cancel",
      confirmButtonText: "Delete",
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      allowOutsideClick: false,
      showLoaderOnConfirm: true,
      preConfirm: () => {
        return new Promise((resolve) => {
          // When "Delete" (Confirm) is clicked
          NProgress.start();
          let delInfo = JSON.parse(info);
          const currentUserObj = JSON.parse(localStorage.getItem("currentUser") || "{}");
          const userName = currentUserObj?.userPrincipalName || "";
          const dbObj = {
            inManufacturer: delInfo.manufacturer,
            inMake: delInfo.make,
            inUserName: userName
          };
  
          let message = "";
          const subscription = this.apollo
            .use("manageScheduler")
            .mutate({
              mutation: deleteMake,
              variables: dbObj,
            })
            .pipe(takeUntil(this.subscription$))
            .subscribe({
              next: (listdata) => {
                NProgress.done();
                const result: any = listdata.data;
                const obj = JSON.parse(result["deleteMake"]["json"]);
  
                if (obj.status === this.constantService.SUCCESS_MESSAGE) {
                  Swal.fire({
                    title: obj.message,
                    icon: "success",
                    confirmButtonColor: "#28a745",
                    confirmButtonText: this.constantService.CLOSE,
                    allowOutsideClick: false
                  });
                  this.getMakeList1();
                } else {
                  message = obj.message;
                  Swal.fire({
                    title: obj.message,
                    icon: "warning",
                    confirmButtonColor: "#d33",
                    confirmButtonText: this.constantService.CLOSE,
                    allowOutsideClick: false
                  });
                }
                resolve(true);
              },
              error: (err) => {
                NProgress.done();
                message = this.constantService.CANNOT_FETCH_DATA;
                Swal.fire({
                  title: message,
                  icon: "warning",
                  confirmButtonColor: "#d33",
                  confirmButtonText: this.constantService.CLOSE,
                  allowOutsideClick: false
                });
                this.commonService.errorCallback(err, this);
                resolve(false);
              },
              complete: () => {
                this.openEditMakeModal(this.selectedMan);
                console.log("Completed");
              },
            });
  
          this.subscriptionList.push(subscription);
        });
      }
    }).then((result) => {
      if (!result.isConfirmed) {
        // When "Cancel" is clicked
        console.log("Deletion was canceled.@@@@@@@@@@@@@");
        this.openEditMakeModal(this.selectedMan);
      }
    });
  }


  deleteMakeAlias(info: any) {
    this.closeEditMakeAliasModal();
  
    Swal.fire({
      title: this.constantService.AREYOUSURE,
      text: "This alias will be deleted",
      icon: "warning",
      showCancelButton: true,
      cancelButtonText: "Cancel",
      confirmButtonText: "Delete",
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      allowOutsideClick: false,
      showLoaderOnConfirm: true,
      preConfirm: () => {
        return new Promise((resolve) => {
          NProgress.start();
          let delInfo = JSON.parse(info);
          const dbObj = { inId: delInfo.id };
          let message = "";
  
          const subscription = this.apollo
            .use("manageScheduler")
            .mutate({
              mutation: deleteMakeAlias,
              variables: dbObj,
            })
            .pipe(takeUntil(this.subscription$))
            .subscribe({
              next: (listdata) => {
                const result: any = listdata.data;
                NProgress.done();
  
                const obj = JSON.parse(result["deleteMakeRuleById"]["json"]);
  
                if (obj.status === this.constantService.SUCCESS_MESSAGE) {
                  Swal.fire({
                    title: obj.message,
                    icon: "success",
                    confirmButtonText: this.constantService.CLOSE,
                    confirmButtonColor: "#5cb85c",
                    allowOutsideClick: false,
                  });
  
                  this.getMakeAlias();
                } else {
                  message = obj.reason;
                  Swal.fire({
                    title: message,
                    icon: "warning",
                    confirmButtonText: this.constantService.CLOSE,
                    confirmButtonColor: "#d33",
                    allowOutsideClick: false,
                  });
                }
                resolve(true);
              },
              error: (err) => {
                NProgress.done();
                message = this.constantService.CANNOT_FETCH_DATA;
                Swal.fire({
                  title: message,
                  icon: "warning",
                  confirmButtonText: this.constantService.CLOSE,
                  confirmButtonColor: "#d33",
                  allowOutsideClick: false,
                });
                this.commonService.errorCallback(err, this);
                resolve(false);
              },
              complete: () => {
                console.log("Completed");
                this.openEditMakeAliasModal(this.selectedMan);
              },
            });
  
          this.subscriptionList.push(subscription);
        });
      },
    }).then((result) => {
      if (result.dismiss === Swal.DismissReason.cancel) {
        console.log("Delete make alias action cancelled")
        this.openEditMakeAliasModal(this.selectedMan);
      }
    });
  }
  
  approveMake(data: any) {
    console.log("approve make selected rows",this.selectedRowsPendingMake);
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser") || "{}");
    const userName = currentUserObj?.userPrincipalName || "";
    const textMsg = data.isApproved
      ? "This make will be approved"
      : "This make will be deleted";
    const btnText = data.isApproved ? "Approve" : "Delete";
    const makeUpdatePayload = this.selectedRowsPendingMake.map(row => ({
      id: row.id,
      is_approved: data.isApproved? true:false,
      is_deleted:data.isDeleted ? true:false
    }));
  
    const jsonPayload = JSON.stringify(makeUpdatePayload); 
    console.log("makeUpdatePayload as JSON string:", jsonPayload);
    Swal.fire({
      title: this.constantService.AREYOUSURE,
      text: textMsg,
      icon: "warning",
      showCancelButton: true,
      cancelButtonText: "Cancel",
      confirmButtonText: btnText,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      allowOutsideClick: false,
      showLoaderOnConfirm: true,
      preConfirm: () => {
        return new Promise((resolve, reject) => {
          NProgress.start();
  
          const proceedToMainMutation = () => {
            const subscription = this.apollo
              .use("manageScheduler")
              .mutate({
                mutation:approveMakeList,
                variables: {
                  inData: jsonPayload  
                },
              })
              .pipe(takeUntil(this.subscription$))
              .subscribe({
                next: (listdata: any) => {
                  NProgress.done();
                  const result: any = listdata;
                  console.log("result:approveMakeList",result);
                  const parsedJson = JSON.parse(
                    result.data.updateMakeApprovalStatus.json
                  );  
                  if (parsedJson.status === "success") {
                    this.toastrService.success (parsedJson.message);
                  } else {
                    const jsonData = JSON.parse(result.data.updateMakeApprovalStatus.json);
                    const makeListKey = Object.keys(jsonData).find(
                      (key) => key.trim() === "make list"
                    );
                    const makeList: string[] = makeListKey
                      ? jsonData[makeListKey]
                      : [];

                      this.toastrService.error(parsedJson.message);
                  }
                  resolve(true);
                },
                error: (err) => {
                  NProgress.done();
                  this.commonService.errorCallback(err, this);
                  reject();
                },
                complete: () => {
                  this.getPendingApprovalMakeList();
                },
              });
  
            this.subscriptionList.push(subscription);
          };
  
          if (data.isApproved) {

            const makeUpdatePayload = this.selectedRowsPendingMake.map(row => ({
              in_manufacturer: row.manufacturer,
              in_make_list: row.make,
              in_user_name: userName
            }));
          
            const jsonPayload = makeUpdatePayload; 
            console.log("makeUpdatePayload as JSON string for final update:", jsonPayload);
            this.apollo
              .use("manageScheduler")
              .mutate({
                mutation: approveMake,
                variables: {
                  input: {
                    inputJson: JSON.stringify(makeUpdatePayload)  
                  }
                }
              })
              .pipe(takeUntil(this.subscription$))
              .subscribe({
                next: (checkRes: any) => {
                  console.log("Update make result",checkRes);
                  const parsedCheck = JSON.parse(
                    checkRes?.data?.updateMakeList?.json
                  );

                  console.log("parsed Check$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",parsedCheck);

                  if (parsedCheck?.status === "success") {
                    this.getPendingApprovalMakeList();
                    proceedToMainMutation(); 
                  } else {
                    NProgress.done();
                    this.toastrService.error(parsedCheck?.message);
                    this.getPendingApprovalMakeList();
                    resolve(true);
                  }
                },
                error: (err) => {
                  NProgress.done();
                  this.commonService.errorCallback(err, this);
                  reject();
                },
              });
          } else {
            proceedToMainMutation();
          }
        });
      },
    }).then((result) => {
      if (!result.isConfirmed) {
        console.log("User cancelled the operation.");
      }
    });
  }
  
  

  handleCheckboxChange(updatedData: any) {
    const deptIndex = this.rowDataDept.findIndex(
      (dept: any) => dept.dept == updatedData.rowdata.dept
    );
    if (deptIndex !== -1) {
      this.rowDataDept[deptIndex].is_allowed = updatedData.rowdata.is_allowed;
    }
  }

  makeValidator(control: AbstractControl): ValidationErrors | null {
    const pattern = /^[a-zA-Z0-9 _-]+$/; 
    if (control.value && !pattern.test(control.value)) {
      return { invalidMake: true };
    }
    return null; 
  }

  approveSelected() {
    let data ={
      isApproved:true,
      isDeleted:false
    };

     this.approveMake(data);
  }
   
  deleteSelected(){
    let data ={
      isApproved:false,
      isDeleted:true
    };
     this.approveMake(data);
  }

  onSelectionChanged(evt:any){
    this.selectedRowsPendingMake = evt.api.getSelectedRows();

  }


}

import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { PaytypesettingComponent } from "./paytypesetting.component";

const routes: Routes = [
  {
    path: "",
    component: PaytypesettingComponent,
    data: {
      title: "ManageSchedule-Setting",
      breadcrumb: [{ label: "ManageSchedule-Setting", url: "" }],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PayTypeSettingRoutingModule {}

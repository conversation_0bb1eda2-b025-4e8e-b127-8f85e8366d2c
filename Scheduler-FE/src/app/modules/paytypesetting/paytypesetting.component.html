<div class="col-lg-12" id="tabAdminContainer">
    <div *ngIf="isAuthenticated">
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="grid1-tab" data-toggle="tab" href="#grid1" role="tab" aria-controls="grid1" aria-selected="true"><strong>Pay type</strong></a
        >
      </li>
      <li class="nav-item">
        <a
          class="nav-link"
          id="grid2-tab"
          data-toggle="tab"
          href="#grid2"
          role="tab"
          aria-controls="grid2"
          aria-selected="false"
          ><strong>Department</strong></a
        >
      </li>
      <li class="nav-item">
        <a
          class="nav-link"
          id="grid3-tab"
          data-toggle="tab"
          href="#grid3"
          role="tab"
          aria-controls="grid3"
          aria-selected="false"
          ><strong>Make List</strong></a
        >
      </li>
      <li class="nav-item">
        <a
          class="nav-link"
          id="grid3-tab"
          data-toggle="tab"
          href="#grid4"
          role="tab"
          aria-controls="grid3"
          aria-selected="false"
          ><strong>Make Alias</strong></a
        >
      </li>
      <li class="nav-item">
        <a
          class="nav-link"
          id="grid3-tab"
          data-toggle="tab"
          href="#grid5"
          role="tab"
          aria-controls="grid3"
          aria-selected="false"
          ><strong>Pending Approval Make</strong></a
        >
      </li>
    </ul>
    <div class="tab-content" id="myTabContent">
      <div
        class="tab-pane fade show active"
        id="grid1"
        role="tabpanel"
        aria-labelledby="grid1-tab"
      >
        <div class="row">
          <div class="col-lg-12">
            <div>
              <section class="card" order-id="card-1">
                <div class="card-header cursor_default">
                  <span class="cat__core__title"> Archived Types </span>
                </div>
                <div class="card-block clear-padding">
                 
                  <form class="new-form" [formGroup]="createSchedule">
                    <div class="row">
                      <div class="col-lg-12 row">
                        <div class="col-lg-3">
                          <label class="col-form-label" style="font-weight: 700"
                            >Store Group</label
                          >
                          <span
                            style="padding-top: 10px"
                            class="pull-right text-warning"
                            *ngIf="storeFilterList.length !== 0"
                            ><a
                              href="https://secure.solve360.com/company/{{
                                storeFilterList[0].companyID
                              }}"
                              target="_blank"
                              class="text-warning"
                              >s360 Group
                              <em
                                class="fa fa-external-link"
                                aria-hidden="true"
                              ></em></a
                          ></span>

                          <div
                            id="storeGroup-select"
                            *ngIf="!loading"
                            [ngClass]="
                              displayFieldCssCreateSchedule('storeGroup')
                            "
                          >
                            <ng-multiselect-dropdown appFocusOnClick
                              formControlName="storeGroup"
                              class="select-sp-report searchicon-dropdown filter-type-status multi-search"
                              [placeholder]="'Select Item'"
                              [settings]="singleDropdownSettings"
                              [data]="storeGroupFilterList"
                              [(ngModel)]="storeGroup"
                              (onSelect)="onSelectStoreGroup($event)"
                              (onDeSelect)="OnDeSelectStoreGroup($event)"
                            >
                            </ng-multiselect-dropdown>
                          </div>
                          <div
                            id="storeGroup-select"
                            *ngIf="loading"
                            [ngClass]="
                              displayFieldCssCreateSchedule('storeGroup')
                            "
                          >
                            <ng-multiselect-dropdown appFocusOnClick
                              formControlName="storeGroup"
                              class="searchicon-dropdown"
                              [settings]="singleDropdownSettingsDisable"
                              [data]="storeGroupFilterList"
                              [(ngModel)]="storeGroup"
                              (onSelect)="onSelectStoreGroup($event)"
                              (onDeSelect)="OnDeSelectStoreGroup($event)"
                            ></ng-multiselect-dropdown>
                          </div>
                          <app-field-error-display
                            [displayError]="
                              isFieldValidCreateSchedule('storeGroup')
                            "
                            errorMsg="{{
                              constantService.ERROR_MESSAGE_FOR_REQUIRED
                            }}"
                          >
                          </app-field-error-display>                    
                        </div>
                        <div class="col-lg-3">
                          <!-- <span
                            style="padding-top: 10px;"
                            class="pull-right text-warning"
                            *ngIf="store.length"
                            ><a
                              *ngIf="store[0].companyId"
                              href="https://secure.solve360.com/company/{{ store[0].companyId }}"
                              target="_blank"
                              class="text-warning"
                              >s360 Store <em class="fa fa-external-link" aria-hidden="true"></em
                            ></a>
                </span> -->
                <label class="col-form-label" style="font-weight: 700; padding-left: 3px;">Store</label
                          >
                          <div
                            id="store-select"
                            *ngIf="!loading"
                            [ngClass]="displayFieldCssCreateSchedule('store')"
                          >
                            <ng-multiselect-dropdown appFocusOnClick
                              formControlName="store"
                              class="select-sp-report searchicon-dropdown filter-type-status multi-search"
                              [placeholder]="'Select Item'"
                              [settings]="singleDropdownSettings"
                              [data]="storeFilterList"
                              [(ngModel)]="store"
                              (onSelect)="onSelectStore($event)"
                              (onDeSelect)="OnDeSelectStore($event)"
                            ></ng-multiselect-dropdown>
                          </div>
                          <div
                            id="store-select"
                            formControlName="store"
                            style="padding-left: 50px"
                            *ngIf="loading"
                            [ngClass]="displayFieldCssCreateSchedule('store')"
                          >
                            <ng-multiselect-dropdown appFocusOnClick
                              class="searchicon-dropdown"
                              [data]="storeFilterList"
                              [(ngModel)]="store"
                              [settings]="singleDropdownSettingsDisable"
                              (onSelect)="onSelectStore($event)"
                              (onDeSelect)="OnDeSelectStore($event)"
                            >
                            </ng-multiselect-dropdown>
                          </div>
                          <app-field-error-display
                            [displayError]="isFieldValidCreateSchedule('store')"
                            errorMsg="{{
                              constantService.ERROR_MESSAGE_FOR_REQUIRED
                            }}"
                          >
                          </app-field-error-display>
                        </div>
                      </div>
                    </div>
                  </form>
                  <div
                    *ngIf=""
                    style="
                      position: absolute;
                      z-index: 5000;
                      top: 13%;
                      left: 43%;
                    "
                  >
                    <div class="text-center">
                      <em class="fa fa-spinner fa-pulse fa-2x fa-fw"> </em
                      ><span class="sr-only">Loading...</span>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <br />
                  <div class="card-block col-lg-12">
                    <div
                      class="col-lg-12"
                      style="position: relative;"

                    >
                      <div style="position: absolute; right: 12px">
                        <button
                          *ngIf="
                            this.selectedRowsArc.length &&
                            rowDataAllPaytypeList.length &&
                            store &&
                            storeGroup
                          "
                          [disabled]="false"
                          class="pointer btn btn-primary btn-sm"
                          (click)="updateArcClick$.next()"
                          style="margin-right: 4px; margin-top: -76px;"
                        >
                          Update
                        </button>
                      </div>
                    </div>

                    <div class="col-lg-12">
                      <ag-grid-angular
                        style="width: 100%; height: ''"
                        class="ag-theme-balham"
                        id="archive-grid"
                        [suppressScrollOnNewData]="true"
                        [pagination]="false"
                        [animateRows]="true"
                        [quickFilterText]=""
                        [enableRangeSelection]="true"
                        [suppressCellFocus]="true"
                        [rowData]="rowDataAllPaytypeList"
                        [overlayLoadingTemplate]="overlayLoadingTemplate"
                        [overlayNoRowsTemplate]="overlayNoRowsTemplate"
                        [defaultColDef]="defaultColDefArcPayType"
                        [columnDefs]="columnDefsArchivePay"
                        (firstDataRendered)="
                          onFirstDataRenderedArcPayType($event)
                        "
                        [enableCellTextSelection]="true"
                        (gridReady)="onGridReadyArcPayType($event)"
                        [enableRangeSelection]="true"
                        [suppressCellFocus]="true"
                        (selectionChanged)="onSelectionArc($event)"
                        [suppressCellFocus]="true"
                        [singleClickEdit]="true"
                        [rowSelection]="'multiple'"
                        [domLayout]="'autoHeight'"
                        [stopEditingWhenCellsLoseFocus]="true"
                      >
                      </ag-grid-angular>
                    </div>
                    <br />
                  </div>
                </div>
              </section>
            </div>
          </div>
        </div>
      </div>
      <div
        class="tab-pane fade"
        id="grid2"
        role="tabpanel"
        aria-labelledby="grid2-tab"
      >
        <div class="row">
          <div class="col-lg-12">
            <div>
              <section class="card" order-id="card-1">
                <div class="row">
                  <div class="card-block col-lg-12">
                    <div class="col-lg-12 row mb-3">
                      <div class="col-lg-3">
                        <label class="col-form-label" style="font-weight: 700; padding-left: 5px;"
                          >DMS</label
                        >
                        <div>
                          <ng-multiselect-dropdown appFocusOnClick
                            class="select-sp-report searchicon-dropdown filter-type-status multi-search"
                            style="position: relative;"
                            [placeholder]="'Select Item'"
                            [settings]="singleDropdownSettings"
                            [data]="this.dmsItems"
                            [(ngModel)]="dmsSelectItems"
                            (onSelect)="onSelectDMSGroup($event)"
                            (onDeSelect)="OnDeSelectDMSGroup($event)"
                          >
                          </ng-multiselect-dropdown>
                        </div>
                      </div>
                    </div>
                    <div
                      class="col-lg-12"
                      style="position: relative;"
                    >
                      <div style="position: absolute; right: 12px">
                        <button
                          *ngIf="dmsSelectItems && rowDataDept && this.selectedRowsDept"
                          [disabled]="false"
                          class="pointer btn btn-primary btn-sm"
                          (click)="updateDeptClick$.next()"
                          style="margin-right: 4px; margin-top: -76px;"
                        >
                          Update
                        </button>
                      </div>
                    </div>
                    <div class="col-lg-12">
                      <ag-grid-angular
                        style="width: 100%; height: ''"
                        class="ag-theme-balham"
                        [suppressScrollOnNewData]="true"
                        [pagination]="false"
                        [animateRows]="true"
                        [quickFilterText]=""
                        [enableRangeSelection]="true"
                        [suppressCellFocus]="true"
                        [rowData]="rowDataDept"
                        [overlayLoadingTemplate]="overlayLoadingTemplate"
                        [overlayNoRowsTemplate]="overlayNoRowsTemplate"
                        [defaultColDef]="defaultColDef"
                        [columnDefs]="columnDefsPayTypeRate"
                        (firstDataRendered)="onFirstDataRenderedPRate($event)"
                        [enableCellTextSelection]="true"
                        (gridReady)="onGridReadyPRate($event)"
                        [enableRangeSelection]="true"
                        (selectionChanged)="onSelectionDept($event)"
                        [suppressCellFocus]="true"
                        [singleClickEdit]="true"
                        [rowSelection]="'multiple'"
                        [domLayout]="'autoHeight'"
                        [stopEditingWhenCellsLoseFocus]="true"
                        [gridOptions]="gridOptions"
                      >
                      </ag-grid-angular>
                    </div>
                    <br />
                  </div>
                </div>
              </section>
            </div>
          </div>
        </div>
      </div>
   <!-- Make List -->

   <div
   class="tab-pane fade"
   id="grid3"
   role="tabpanel"
   aria-labelledby="grid3-tab"
 >
   <div class="row">
     <div class="col-lg-12">
       <div>
           
          <section class="card" order-id="card-1">
                <div class="card-header cursor_default">
                  <span class="cat__core__title"> Make List </span>
                </div>
                <div class="col-lg-12">
                <button class="btn btn-primary btn-sm pull-right mt-2" style="cursor: pointer;" (click)="openAddMakeModal()">Add Make</button>
                </div>
               <div class="row">
                  <div class="card-block col-lg-12">
                    <div
                      class="col-lg-12"
                      style="position: relative;"

                    >
                      <div style="position: absolute; right: 12px">
                        </div>
                    </div>

                    <div class="col-lg-12">
                      <ag-grid-angular
                        style="width: 100%; height: ''"
                        class="ag-theme-balham"
                        id="archive-grid"
                        [suppressScrollOnNewData]="true"
                        [pagination]="false"
                        [animateRows]="true"
                        [quickFilterText]=""
                        [enableRangeSelection]="true"
                        [suppressCellFocus]="true"
                        [rowData]="makeList"
                        [overlayLoadingTemplate]="overlayLoadingTemplate"
                        [overlayNoRowsTemplate]="overlayNoRowsTemplate"
                        [defaultColDef]="defaultColDefMake"
                        [columnDefs]="columnDefsMake"
                        (firstDataRendered)="
                          onFirstDataRenderedMake($event)
                        "
                        [enableCellTextSelection]="true"
                        (gridReady)="onGridReadyMake($event)"
                        [enableRangeSelection]="true"
                        [suppressCellFocus]="true"
                        (selectionChanged)="onSelectionMake($event)"
                        [suppressCellFocus]="true"
                        [singleClickEdit]="true"
                        [rowSelection]="'multiple'"
                        [domLayout]="'autoHeight'"
                        [stopEditingWhenCellsLoseFocus]="true"
                      >
                      </ag-grid-angular>
                    </div>
                    <br />
                  </div>
                </div>
              </section>


       </div>
     </div>
   </div>
 </div>

    <!-- Make List -->

 <!-- Make Alias -->
 <div
 class="tab-pane fade"
 id="grid4"
 role="tabpanel"
 aria-labelledby="grid4-tab"
>
 <div class="row">
   <div class="col-lg-12">
     <div>
         
        <section class="card" order-id="card-1">
              <div class="card-header cursor_default">
                <span class="cat__core__title"> Make Alias </span>
              </div>
              <div class="row">
                <br />
                <div class="card-block col-lg-12">
                  <div
                    class="col-lg-12"
                    style="position: relative;"

                  >
                    <div style="position: absolute; right: 12px">
                      <button
                        *ngIf="
                          this.selectedRowsArc.length &&
                          rowDataAllPaytypeList.length &&
                          store &&
                          storeGroup
                        "
                        [disabled]="false"
                        class="pointer btn btn-primary btn-sm"
                        (click)="updateArcClick$.next()"
                        style="margin-right: 4px; margin-top: -76px;"
                      >
                        Update
                      </button>
                    </div>
                  </div>

                  <div class="col-lg-12">
                    <ag-grid-angular
                      style="width: 100%; height: ''"
                      class="ag-theme-balham"
                      id="archive-grid"
                      [suppressScrollOnNewData]="true"
                      [pagination]="false"
                      [animateRows]="true"
                      [quickFilterText]=""
                      [enableRangeSelection]="true"
                      [suppressCellFocus]="true"
                      [rowData]="makeAliasList"
                      [overlayLoadingTemplate]="overlayLoadingTemplate"
                      [overlayNoRowsTemplate]="overlayNoRowsTemplate"
                      [defaultColDef]="defaultColDefArcPayType"
                      [columnDefs]="columnDefsMakeAlias"
                      (firstDataRendered)="
                        onFirstDataRenderedArcPayType($event)
                      "
                      [enableCellTextSelection]="true"
                      (gridReady)="onGridReadyArcPayType($event)"
                      [enableRangeSelection]="true"
                      [suppressCellFocus]="true"
                      [suppressCellFocus]="true"
                      [singleClickEdit]="true"
                      [rowSelection]="'multiple'"
                      [domLayout]="'autoHeight'"
                      [stopEditingWhenCellsLoseFocus]="true"
                    >
                    </ag-grid-angular>
                  </div>
                  <br />
                </div>
              </div>
            </section>


     </div>
   </div>
 </div>
</div>

<!-- Make Alias -->

 <!-- Pending Approval Make -->
 <div
 class="tab-pane fade"
 id="grid5"
 role="tabpanel"
 aria-labelledby="grid5-tab"
>
 <div class="row">
   <div class="col-lg-12">
     <div>
         
        <section class="card" order-id="card-1">
              <div class="card-header cursor_default">
                <span class="cat__core__title"> Pending Approval Make</span>
                  <div class="pull-right">
                    <button
                    *ngIf="selectedRowsPendingMake.length"
                    class="btn btn-danger btn-sm pull-right ml-2 mr-3"
                    style="cursor: pointer;"
                    (click)="deleteSelected()"
                  >
                    Delete Selected
                  </button>
                  <button
                    *ngIf="selectedRowsPendingMake.length"
                    class="btn btn-success btn-sm pull-right ml-2"
                    style="cursor: pointer;"
                    (click)="approveSelected()"
                  >
                    Approve Selected
                  </button>
                  </div>
              </div>
              <div class="card-block">
                <div class="col-lg-12">
                  <ag-grid-angular
                  class="ag-theme-balham"
                  id="archive-grid"
                  [suppressScrollOnNewData]="true"
                  [pagination]="false"
                  [animateRows]="true"
                  [enableRangeSelection]="true"
                  [suppressCellFocus]="true"
                  [rowData]="pendingMakeList"
                  [overlayLoadingTemplate]="overlayLoadingTemplate"
                  [overlayNoRowsTemplate]="overlayNoRowsTemplate"
                  [defaultColDef]="defaultColPendinMakeApproval"
                  [columnDefs]="columnDefsPendingMakeApproval"
                  (firstDataRendered)="onFirstDataRenderedArcPayType($event)"
                  [enableCellTextSelection]="true"
                  (gridReady)="onGridReadyArcPayType($event)"
                  [singleClickEdit]="true"
                  [rowSelection]="'multiple'"
                  [rowMultiSelectWithClick]="true"
                  [domLayout]="'autoHeight'"
                  [stopEditingWhenCellsLoseFocus]="true"
                  (selectionChanged)="onSelectionChanged($event)"
                >
                </ag-grid-angular>
                
                </div>
              </div>
            </section>


     </div>
   </div>
 </div>
</div>

<!-- Pending Approval Make -->

    </div>
  </div>
</div>


<div
  *ngIf="isAuthenticated"
  class="modal fade"
  id="editMakeModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <!-- Modal Header -->
      <div class="modal-header">
        <h5 class="modal-title" id="requestModalLabel">Edit Make</h5>
        <button type="button" class="close" (click)="closeEditMakeModal()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="modal-body">
        <div class="col-lg-12">
          <ag-grid-angular
            style="width: 100%;"
            class="ag-theme-balham"
            id="edit-make-grid"
            [suppressScrollOnNewData]="true"
            [pagination]="false"
            [animateRows]="true"
            [enableRangeSelection]="true"
            [suppressCellFocus]="true"
            [rowData]="editMakeListData"
            [overlayLoadingTemplate]="overlayLoadingTemplate"
            [overlayNoRowsTemplate]="overlayNoRowsTemplate"
            [defaultColDef]="defaultColDefMake1"
            [columnDefs]="columnDefsMake1"
            (firstDataRendered)="onFirstDataRenderedMake($event)"
            [enableCellTextSelection]="true"
            (gridReady)="onGridReadyMake($event)"
            [singleClickEdit]="true"
            [rowSelection]="'multiple'"
            [domLayout]="'autoHeight'"
            [stopEditingWhenCellsLoseFocus]="true"
          >
          </ag-grid-angular>
        </div>
      </div>

      <!-- Modal Footer
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeEditMakeModal()">Cancel</button>
        <button class="btn btn-primary" (click)="updateMake()">Update Make</button>
      </div> -->
    </div>
  </div>
</div>



<!-- Add Make Modal -->
<div *ngIf="isAuthenticated" class="modal fade" id="addMakeModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width: 700px">
      <div class="modal-header">
        <h5 class="modal-title" id="requestModalLabel">Add Make</h5>
        <button type="button" class="close" (click)="closeAddMakeModal()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
    <div class="modal-body">
        <ng-container>
          <div class="col-md-12">
            <form [formGroup]="myForm" (ngSubmit)="onSubmitMake()">
             <div class="form-group">
                <label for="manufacturer">Manufacturer:</label>
                <ng-multiselect-dropdown 
                  id="manufacturer"
                  appFocusOnClick
                  formControlName="manufacturer"
                  class="select-sp-report searchicon-dropdown filter-type-status multi-search"
                  [placeholder]="'Select Manufacturer'"
                  [settings]="singleDropdownSettingsMake"
                  [data]="makeFilterList"
                  [(ngModel)]="manufacturer"
                  >
                </ng-multiselect-dropdown>
                <div *ngIf="myForm.get('manufacturer')?.invalid && myForm.get('manufacturer')?.touched" class="text-danger">
                  Manufacturer is required.
                </div>
              </div>
              <div class="form-group">
                <label for="make">Make:</label>
                <input id="make" type="text" formControlName="make" placeholder="Enter Make" class="form-control" />

                <div *ngIf="myForm.get('make')?.invalid && myForm.get('make')?.touched" class="text-danger">
                  Only letters, numbers, spaces, "-" and "_" are allowed.
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="closeAddMakeModal()">Cancel</button>
                <button type="submit" class="btn btn-primary" [disabled]="myForm.invalid">Add Make</button>
              </div>

            </form>
          </div>
        </ng-container>
      </div>
    
    </div>
  </div>
</div>
 <!-- Add Make modal end -->


 <!-- Edit modal>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> -->
 <div *ngIf="isAuthenticated" class="modal fade" id="editMakeModal1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width: 700px">
      <div class="modal-header">
        <h5 class="modal-title" id="requestModalLabel">Edit Make</h5>
        <button type="button" class="close" (click)="closeEditMakeModal1()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      
      <div class="modal-body">
        <ng-container>
          <div class="col-md-12">
            <!-- Manufacturer Field (Read-Only) -->
            <div class="form-group">
              <label for="manufacturer">Manufacturer</label>
              <input 
                type="text" 
                id="manufacturer" 
                class="form-control" 
                [(ngModel)]="selectedMake.manufacturer" 
                name="manufacturer" 
                disabled 
              />
            </div>

            <!-- Make Field with Validation -->
            <div class="form-group">
              <label for="make">Make</label>
              <input 
                type="text" 
                id="make" 
                class="form-control" 
                [(ngModel)]="selectedMake.make" 
                name="make" 
                #editedMake="ngModel" 
                required 
                pattern="^[a-zA-Z0-9 _-]+$" 
              />
              <!-- Error message if special characters are used -->
              <div *ngIf="editedMake.invalid && (editedMake.dirty || editedMake.touched)" class="text-danger">
                <small *ngIf="editedMake.errors?.['pattern']">

                  Special characters are not allowed.
                </small>
              </div>
            </div>
          </div>
        </ng-container>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="cancelEditMakeModal()">Cancel</button>
        <!-- Update button is disabled if the "Make" input is invalid -->
        <button class="btn btn-primary" (click)="updateMake()" [disabled]="editedMake.invalid">Update Make</button>
      </div>
    </div>
  </div>
</div>


  <!-- Edit Modal>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> -->

  <!-- view make alias modal -->

  <div
  *ngIf="isAuthenticated"
  class="modal fade"
  id="editMakeAliasModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <!-- Modal Header -->
      <div class="modal-header">
        <h5 class="modal-title" id="requestModalLabel">Edit Make</h5>
        <button type="button" class="close" (click)="closeEditMakeAliasModal()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="modal-body">
        <div class="col-lg-12">
          <ag-grid-angular
            style="width: 100%;"
            class="ag-theme-balham"
            id="edit-make-grid"
            [suppressScrollOnNewData]="true"
            [pagination]="false"
            [animateRows]="true"
            [enableRangeSelection]="true"
            [suppressCellFocus]="true"
            [rowData]="editMakeAliasData"
            [overlayLoadingTemplate]="overlayLoadingTemplate"
            [overlayNoRowsTemplate]="overlayNoRowsTemplate"
            [defaultColDef]="defaultColDefMake1"
            [columnDefs]="columnDefsMakeAlias1"
            (firstDataRendered)="onFirstDataRenderedMake($event)"
            [enableCellTextSelection]="true"
            (gridReady)="onGridReadyMake($event)"
            [singleClickEdit]="true"
            [rowSelection]="'multiple'"
            [domLayout]="'autoHeight'"
            [stopEditingWhenCellsLoseFocus]="true"
          >
          </ag-grid-angular>
        </div>
      </div>

      <!-- Modal Footer
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeEditMakeModal()">Cancel</button>
        <button class="btn btn-primary" (click)="updateMake()">Update Make</button>
      </div> -->
    </div>
  </div>
</div>

  <!-- Action modal make alias -->
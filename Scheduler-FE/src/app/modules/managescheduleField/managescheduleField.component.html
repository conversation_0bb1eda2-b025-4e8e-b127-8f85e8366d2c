<div class="row">
  <div class="col-lg-12">
    <div  *ngIf="isAuthenticated">
      <section class="card" order-id="card-1">
        <div class="card-header cursor_default">
          <span
            class="cat__core__title"
            style="font-size: 17px; font-weight: bold"
          >
            Add DMS fields
          </span>
        </div>
        <form class="new-form manage-schedule" [formGroup]="createSchedule">          
          <div class="row">
            <div class="col-lg-12 row">
              <div class="col-lg-3">
                <label class="col-form-label" style="font-weight: 700"
                  >Store Group
                </label>               
                <div
                  id="storeGroup-select"
                  *ngIf="!loading"
                  [ngClass]="displayFieldCssCreateSchedule('storeGroup')"
                >
                  <ng-multiselect-dropdown
                    class="searchicon-dropdown rounded-selection rouded-sel-label multi-search filter-type-status"
                    formControlName="storeGroup"
                    [settings]="singleDropdownSettings"
                    [data]="storeGroupFilterList"
                    [(ngModel)]="storeGroup"
                    (onSelect)="onSelectStoreGroup($event)"
					(onDeSelect)="OnDeSelectStoreGroup($event)"
                  ></ng-multiselect-dropdown>
                </div>
                <div
                  id="storeGroup-select"
                  *ngIf="loading"
                  [ngClass]="displayFieldCssCreateSchedule('storeGroup')"
                >
                  <ng-multiselect-dropdown
                    class="searchicon-dropdown"
                    formControlName="storeGroup"
                    [settings]="singleDropdownSettingsDisable"
                    [data]="storeGroupFilterList"
                    [(ngModel)]="storeGroup"
                    (onSelect)="onSelectStoreGroup($event)" 
					(onDeSelect)="OnDeSelectStoreGroup($event)"
                  ></ng-multiselect-dropdown>
                </div>
                <app-field-error-display
                  [displayError]="isFieldValidCreateSchedule('storeGroup')"
                  errorMsg="{{ constantService.ERROR_MESSAGE_FOR_REQUIRED }}"
                >
                </app-field-error-display>
                <span
                  class="pull-right"
                  data-placement="top"
                  data-toggle="tooltip"
                  style="
                    z-index: 1;
                    position: relative;
                    top: -28px;
                    right: -30px;
                  "
                  data-original-title="Click to refresh"
                  data-animation="false"
                >
                  <em
                    (click)="reloadGroupList()"
                    class="plus fa fa-refresh fa-2x pointer"
                    style="font-size: 20px"
                    *ngIf="!reloadGroup && !storeLoading"
                  ></em>
                  <em
                    class="plus fa fa-refresh fa-spin fa-2x"
                    style="font-size: 20px"
                    *ngIf="reloadGroup || storeLoading"
                  ></em>
                </span>
              </div>            
              <div class="col-lg-3"  >
                <label
                  class="col-form-label"
                  style="font-weight: 700; padding-left: 40px"
                  >Store
                </label> <span style="float: right;font-weight: 700" class="col-form-label">
                  <input
                  type="checkbox"
                  [(ngModel)]="metaGroup"
                  formControlName="metaGroup"
                  name="metaGroup"
                  (ngModelChange)="onMetaGroupChange($event)"
                  />
                  &nbsp;Meta Group
                </span>
                <div
                  style="padding-left: 40px"
                  id="store-select"
                  *ngIf="!loading"
                  [ngClass]="displayFieldCssCreateSchedule('store')"
                >
                  <ng-multiselect-dropdown
                    class="searchicon-dropdown rounded-selection rouded-sel-label multi-search filter-type-status"
                    [settings]="singleDropdownSettings"
                    [data]="storeFilterList"
                    [(ngModel)]="store"
                    formControlName="store"
                    (onSelect)="onSelectStore($event)"
                    (onDeSelect)="OnDeSelectStore($event)"
                  ></ng-multiselect-dropdown>
                </div>
                <div
                  id="store-select"
                  style="padding-left: 50px"
                  *ngIf="loading"
                  [ngClass]="displayFieldCssCreateSchedule('store')"
                >
                  <ng-multiselect-dropdown
                    class="searchicon-dropdown"
                    [settings]="singleDropdownSettingsDisable"
                    [data]="storeFilterList"
                    formControlName="store"
                    [(ngModel)]="store"
                    (onSelect)="onSelectStore($event)"
                    (onDeSelect)="OnDeSelectStore($event)"
                  >
                  </ng-multiselect-dropdown>
                </div>
                <app-field-error-display
                  [displayError]="isFieldValidCreateSchedule('store')" class ="error-display-padding" 
                  errorMsg="{{ constantService.ERROR_MESSAGE_FOR_REQUIRED }}"
                >
                </app-field-error-display>
              </div> 
              
              <div [hidden]="storeGroupLoading " style="top: 51%;position: absolute;left: 50%;">
                <div class="text-center">
                  <em class="plus fa fa-refresh fa-spin fa-2x" style="font-size: 20px;">
                  </em><span class="sr-only">Loading...</span></div>
              </div>
            </div>
          </div>
          <div class="row">
          <div class="col-lg-12 row">
           
              <div class="col-lg-3">
                <label class="col-form-label" style="font-weight: 700;">Third Party UserName </label>
                <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" formControlName="thirdPartyUserName">
              </div>

              <div class="col-lg-3"  style="padding-left: 50px;">
                <label class="col-form-label" style="font-weight: 700;">Enterprise/Source ID</label>
                <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" formControlName="sourceId">
              </div>
           
          </div>
        </div>
          <div class="row">
          <div class="col-lg-12 row">
           
              <div class="col-lg-3">
                <label class="col-form-label" style="font-weight: 700;">Server Name/Store</label>
                <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" formControlName="serverName">
              </div>

              <div class="col-lg-3"  style="padding-left: 50px;">
                <label class="col-form-label" style="font-weight: 700;">Branch#</label>
                <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" formControlName="branch">
              </div>

          </div>
        </div>
          <div class="row">
          <div class="col-lg-12 row">
           
              <div class="col-lg-3">
                <label class="col-form-label" style="font-weight: 700;">Mage Group Name</label>
                <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" formControlName="mageGroupName">
              </div>

              <div class="col-lg-3"  style="padding-left: 50px;">
                <label class="col-form-label" style="font-weight: 700;">Mage Group Code</label>
                <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" formControlName="mageGroupCode">
              </div>

          </div>
        </div>
        <div class="row">
          <div class="col-lg-12 row">
           
              <div class="col-lg-3">
                <label class="col-form-label" style="font-weight: 700;">Mage Store Name</label>
                <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" formControlName="mageStoreName">
              </div>

              <div class="col-lg-3" style="padding-left: 50px;">
                <label class="col-form-label" style="font-weight: 700;">Mage Store Code</label>
                <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" formControlName="mageStoreCode">
              </div>

          </div>
        </div>

        <div class="row">
          <div class="col-lg-12 row">
           
              <div class="col-lg-3">
                <label class="col-form-label" style="font-weight: 700;">Billing Code</label>
                <input type="text" class="form-control" id="billingCode" aria-describedby="emailHelp" formControlName="billingCode">
              </div>

              <div class="col-lg-3" style="padding-left: 50px;">
                <label class="col-form-label" style="font-weight: 700"
                  >Third-Party Cancel Sent:</label
                >
                <div class="input-group">
                  <input
                    type="text"
                    formControlName="cancelledDate"
                    [(ngModel)]="inFromDate"
                    class="form-control datepicker-only-init"
                    id="inStartDate"
                    name="inStartDate"
                    #dtinStartDate
                    (blur)="setDtinStartDate(dtinStartDate)"
                    placeholder="Select Date"
                  />
                  <div class="input-group-append">
                    <span class="input-group-btn">
                      <button
                        type="button"
                        class="btn btn-default"
                        (click)="openDatepicker()"
                      >
                        <em class="fa fa-calendar"></em>
                      </button>
                    </span>
                  </div>
                </div>
              </div>

          </div>
        </div>


          <div class="row">
            <div class="col-lg-12 row">
              <div class="col-lg-3" >
                <div id="input-group" style="float: left">
                  <label class="col-form-label" style="font-weight: 700;" >Is FOPC Store</label>
                  <div style="position:absolute">  
                    <input class="form-control"
                      type="checkbox"
                      [(ngModel)]="saveFopc"
                      formControlName="saveFopc"
                      name="saveFopc"
                    />   
                    </div>              
                </div>               
              </div>
            
            </div>
          </div>
          <div class="col-lg-6 row" style="justify-content: flex-end; margin-bottom: 35px;">
            <div
              class="pull-right"
              style="margin-top: 20px; padding-right: 5px"
            >
              <button
                type="button"                
                (click)="cancelSchedule()"
                class="btn btn-primary pointer"
                style="width: 100px;">
                Cancel
              </button>
            </div>
            <div
            class="pull-right"
            style="margin-top: 20px; padding-right: 5px"
          >
            <button
              type="button"
              [disabled]="!createSchedule.valid || !storeSelected"
              (click)="goToSchedule()"
              class="btn btn-primary pointer"
              style="width: 100px;">
              Save  <i class="fa fa-spinner fa-spin " *ngIf="isLoading"> </i>
            </button>
          </div>
          </div>
        </form>
      </section>
    </div>
  </div>
</div>

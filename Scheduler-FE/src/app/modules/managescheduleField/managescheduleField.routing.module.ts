import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManageScheduleFieldComponent } from "./managescheduleField.component";

const routes: Routes = [
  {
    path: "",
    component: ManageScheduleFieldComponent,
    data: {
      title: "ManageScheduleField",
      breadcrumb: [{ label: "ManageScheduleField", url: "" }],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ManageScheduleFieldRoutingModule {}

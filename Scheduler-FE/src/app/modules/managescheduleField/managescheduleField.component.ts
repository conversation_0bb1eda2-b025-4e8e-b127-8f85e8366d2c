import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { Apollo } from "apollo-angular";
import gql from "graphql-tag";
import * as moment from "moment-timezone";
import { SubscriptionConstantService } from "src/app/structure/constants/subscription.constant.service";
import { environment } from "src/environments/environment";
import { CommonService } from "src/app/structure/services/common.service";
import { ConstantService } from "src/app/structure/constants/constant.service";
import { SchedulerConstantService } from "src/app/structure/constants/scheduler.constant.service";
import { DmFormGroupService } from "src/app/structure/services/dm.formgroup.services";
import { Subject, takeUntil } from "rxjs";
import { HttpHeaders, HttpClient } from '@angular/common/http';
import { SharedModule } from "../shared/shared.module";

declare var $: any;
declare var swal: any;
const getScheduleFieldDataByStoreIdQuery = gql`
  query getScheduleFieldDataByStoreId($companyId: BigInt!) {
    getScheduleFieldDataByStoreId(companyId: $companyId) {
     storeCredentials{       
        companyId
        thirdPartyUserName
        enterpriseSourceId
        mageGroupCode
        mageGroupName
        serverStore
        mageStoreCode
        billingCode
        mageStoreName
        isFopcStore
        branch
        cancelledDate        
    },
    status
    message
    }
  }
`;
const allSolve360StoreGroupsQuery = gql`
  query allSolve360StoreGroups {
  allSolve360StoreGroups(
    condition: { isInPortal: true, tagGroupRegion: false }
  ) {
    edges {
      node {
        companyId
        companyName
        brandList
        state
        sgId
        isInPortal
        tagStoreGroup
        tagGroupRegion
        tagSoloStore
        tagCustomer
        sp
        salesPerson
        spEmail
        spPhone
        createdBy
        createdAt
        updatedBy
        updatedAt
      }
    }
  }
}`;
  const getStoreDetailsByStoreGroupCompanyIdQuery = gql`
  query getStoreDetailsByStoreGroupCompanyId($inSgCompanyId: BigInt!) {
    getStoreDetailsByStoreGroupCompanyId(inSgCompanyId: $inSgCompanyId) 
  }
`;

@Component({
  selector: "app-managescheduleField",
  templateUrl: "./managescheduleField.component.html",
  styleUrls: ["./managescheduleField.component.css"],
  standalone: true,
  imports: [
    SharedModule,
  ],
})

export class ManageScheduleFieldComponent implements OnInit {
  private subscription$ = new Subject();
  public isAuthenticated = false;
  loading: any = false;
  private storeGroupList: any[] = [];
  private storeList: any[] = [];
  public storeGroupFilterList: any[] = [];
  public storeFilterList: any[] = [];
  public storeGroup: any[] = [];
  public store: any[] = [];
  public loadAllStore = false;
  public storeGroupLoading = true;
  public storeLoading: any = true;
  public reloadGroup = false;
  public storesWithFopcMetaGroup: any[] = [];
  public storesWithOnlyStore: any[] = [];
  public isLoading =false;
  public allStoreList: any[] = [];
  public dmsObj: any;
  public dms = [{ id: "All", itemName: "All" }];
  public loadingSchedule = false;
  public inFromDate: any = null;
  public saveFopc: boolean = false;
  public metaGroup: boolean = false;
  public formAction: string = "creation";
  public agendaDashboardUrl = "";
  public shows360Link = false;
  public s360CompanyId: any;
  public schedulerFields: any;
  public createSchedule!: FormGroup;
  public storeSelected =false;
  constructor(
    public constantService: ConstantService,
    private commonService: CommonService,
    public SubscriptionConstantService: SubscriptionConstantService,
    private router: Router,
    private apollo: Apollo,
    public SchedulerConstantService: SchedulerConstantService,
    public DmFormGroupService: DmFormGroupService,
    private http: HttpClient
  ) {}

  public singleDropdownSettings: any;
  public dropdownSettings: any;
  public singleDropdownSettingsDisable: any;
  public singleDropdownSettingsState: any;
  public multiDropdownSettingsDisable: any;
  public multiDropdownSettings = {
    enableSearchFilter: true,
    idField: "id",
    textField: "itemName",
    allowSearchFilter: true,
  };
  ngOnDestroy() {
    this.subscription$.next(void 0);
    this.subscription$.complete();
  }
  getCalenderPropertyObject(maxDate: any) {
    let settings: any = {
      useCurrent: true,
      widgetPositioning: {
        horizontal: "left",
      },
      icons: {
        time: "fa fa-clock-o",
        date: "fa fa-calendar",
        up: "fa fa-arrow-up",
        down: "fa fa-arrow-down",
        previous: "fa fa-arrow-left",
        next: "fa fa-arrow-right",
      },
      format: "MM-DD-YYYY",
    };
    if (maxDate) {
      settings["maxDate"] = new Date();
    }
    return settings;
  }
  ngOnInit() {
    this.commonService.getGroups(() => {
      this.commonService.checkGroups((flag) => {
        if (!flag) {
          return;
        }
        this.isAuthenticated = true;
        this.init();
      });
    });
  }
  init() {
    console.log("test 2");
    console.log(this.DmFormGroupService);
    this.singleDropdownSettings =
      this.DmFormGroupService.singleDropdownSettings();
    this.dropdownSettings = this.DmFormGroupService.dropdownSettings();
    const date = new Date();
    this.inFromDate = "";
    const elm = this;
    $(function () {
      let objPropertyCalender: { [k: string]: any } = {};
      objPropertyCalender = elm.getCalenderPropertyObject(false);
      $("#inStartDate").datetimepicker(objPropertyCalender);
      $("#inEndDate").datetimepicker(objPropertyCalender);
      $('[data-toggle="tooltip"]').tooltip({
        trigger: "hover",
      });
    });
    this.singleDropdownSettingsDisable =
      this.DmFormGroupService.singleDropdownSettingsDisable();
    this.singleDropdownSettingsState =
      this.DmFormGroupService.singleDropdownSettingsState();
    let activityData = {
      activityName: "Manage Schedule",
      activityType: "Manage Schedule",
      activityDescription: "Current Page: " + this.router.url,
    };
    this.commonService.saveActivity("Manage Schedule", activityData);

    let scheduleUrl = environment.jobListUrl;
    scheduleUrl = scheduleUrl.replace(
      this.SchedulerConstantService.END_POINTS.GRAPHQL_END_POINT,
      this.SchedulerConstantService.END_POINTS.AGENDA_END_POINT
    );
    this.agendaDashboardUrl = scheduleUrl;
    this.SubscriptionConstantService.pageTitle = "- DMS Fields";
    this.createSchedule = new FormGroup({
      storeGroup: new FormControl("", Validators.required),
      store: new FormControl("", Validators.required),
      thirdPartyUserName: new FormControl(""),
      sourceId: new FormControl(""),
      serverName: new FormControl(""),
      branch: new FormControl(""),
      mageGroupName: new FormControl(""),
      mageGroupCode: new FormControl(""),
      mageStoreName: new FormControl(""),
      mageStoreCode: new FormControl(""),
      billingCode: new FormControl(""),
      cancelledDate: new FormControl(""),
      saveFopc: new FormControl(false),
      metaGroup:new FormControl(false)
    });
    this.getAllSolve360StoreGroups()
    .then((result) => {
      console.log("Callback result:", result);       
        const edges = result.edges;
        this.storeGroupList = edges.map((item: any) => item.node);
        console.log("storeGroupList", this.storeGroupList);        
        this.getGroupFilterList();      
    });
  }
  getSchedulerFields(inCompanyId: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.schedulerFields = [];
      this.apollo
        .use("manageScheduleAddFields")
        .query({
          query: getScheduleFieldDataByStoreIdQuery,
          fetchPolicy: "network-only",
          variables: {
            companyId: inCompanyId,
          }
        })
        .pipe(takeUntil(this.subscription$))
        .subscribe({
          next: (listdata: any) => {
            const result: any = listdata;
            const resp = result["data"]["getScheduleFieldDataByStoreId"]; 
            this.schedulerFields = resp;
            let storeData;
            if(resp.storeCredentials){
              storeData = resp.storeCredentials;
            }
            resolve(storeData);  // Accessing the first element of storeCredentials array
          },
          error: (err: any) => {
            this.commonService.errorCallback(err, this);
            reject(err);
          },
          complete: () => {
            console.log("Completed: getSchedulerFields");
          },
        });
    });
  }
  getAllSolve360StoreGroups(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.schedulerFields = [];
      this.apollo
        .use("manageGroups")
        .query({
          query: allSolve360StoreGroupsQuery,
          fetchPolicy: "network-only"
        })
        .pipe(takeUntil(this.subscription$))
        .subscribe({
          next: (listdata: any) => {
            console.log("listdata:", listdata); // Log to inspect structure            
            const result = listdata;
            if (result && result.data && result.data.allSolve360StoreGroups) {
                const resp = result.data.allSolve360StoreGroups;
                this.schedulerFields = resp;
                resolve(resp); // Resolves with allSolve360StoreGroups data
            } else {
                reject(new Error("Unexpected response structure"));
            }
          },
          error: (err: any) => {
            this.commonService.errorCallback(err, this);
            reject(err);
          },
          complete: () => {
            console.log("Completed: getSchedulerFields");
          },
        });
    });
  }

GetStoresForStoreGroup(inSgCompanyId: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.schedulerFields = [];
      this.apollo
        .use("duportal")
        .query({
          query: getStoreDetailsByStoreGroupCompanyIdQuery,
          fetchPolicy: "network-only",
          variables: {
            inSgCompanyId: inSgCompanyId,
          }
        })
        .pipe(takeUntil(this.subscription$))
        .subscribe({
          next: (listdata: any) => {
            const result: any = listdata;
            const resp = result["data"]["getStoreDetailsByStoreGroupCompanyId"];             
            let storeData;
            if(resp){
              storeData = resp;
              const parsedData = JSON.parse(storeData);
              console.log(parsedData)
           
            resolve(parsedData);  // Accessing the first element of storeCredentials array
             }else {
              this.storeGroupLoading = true;
            }
          },
          error: (err: any) => {
            this.commonService.errorCallback(err, this);
            reject(err);
          },
          complete: () => {
            console.log("Completed: getSchedulerFields");
          },
        });
    });
  }
  setDtinStartDate(e: any) {
    if (e.value && e.value !== "") {
      this.inFromDate = e.value;
    } else {
      this.inFromDate = null;
    }
  }
  openDatepicker() {
    const dateInput = document.getElementById(
      "inStartDate"
    ) as HTMLInputElement;
    if (dateInput) {
      dateInput.focus(); // Focus the input to trigger the datepicker
      // Optionally trigger the datepicker if not already attached
      if (!$(dateInput).hasClass("hasDatepicker")) {
        $(dateInput).datepicker("show"); // Show the datepicker
      }
    }
  }
  isFieldValidCreateSchedule(field: string) {
    let retValue: any = null;
    retValue =
      !this.createSchedule.get(field)?.valid &&
      this.createSchedule.get(field)?.touched;
    return retValue;
  }
  displayFieldCssCreateSchedule(field: string) {
    return {
      "has-danger": this.isFieldValidCreateSchedule(field),
    };
  }
  clearFormFields() {
    console.log("clerar fielde ");
    this.createSchedule.patchValue({
      thirdPartyUserName: "",
      sourceId: "",
      serverName: "",
      branch: "",
      cancelledDate: "",
      saveFopc: false,
      mageGroupName: "",
      mageGroupCode: "",
      mageStoreName: "",
      mageStoreCode: "", 
      billingCode:""     
    });
  }
  onMetaGroupChange(value: boolean): void {
    console.log('Meta Group changed:', value);
    const storeControl = this.createSchedule.get('store');
    console.log(this.storeList);
    console.log(this.storesWithOnlyStore); console.log(this.storesWithFopcMetaGroup);
    this.createSchedule.get('store')?.reset();
    this.clearFormFields();
    // Update the store field validation based on metaGroup value
    if (!value) {
      this.storeList = this.storesWithOnlyStore;
    } else {
      this.storeList = this.storesWithFopcMetaGroup;
    }
    console.log(this.storeList);
    this.getStoreFilterList();
  }
  /**
   * callback function for store group selection
   *
   */
  onSelectStoreGroup(item: any) {    
    this.metaGroup = false;
      const storeControlValue = this.createSchedule.get('store');
      storeControlValue?.updateValueAndValidity();
      this.createSchedule.get('store')?.reset();
      this.storeGroupLoading = false;
      this.selectStoreGroup(item, () => {
        this.shows360Link = true;
        let itemFilter: any = this.storeGroupFilterList.filter(
          (res) => res.id == item.id
        );
        this.s360CompanyId = itemFilter[0].companyId;
      });
      this.storeSelected = false;
  
  }
    /**
   * callback function for store selection
   *
   */
    getScheduleFields(selecteGroupCompanyId: any) {
      console.log("selected Store:",selecteGroupCompanyId);
      this.clearFormFields();
      this.getSchedulerFields(selecteGroupCompanyId)
        .then((data) => {
          if (data && data.length > 0) {
            console.log(data[0]);
            this.formAction = "update";
            const scheduledData = data[0];
            console.log(scheduledData);
            let cancelledDate: string = moment(
              new Date(scheduledData.cancelledDate)
            ).format(this.SchedulerConstantService.DATE_FORMAT);
            this.createSchedule.patchValue({
              thirdPartyUserName: scheduledData.thirdPartyUserName || "",
              sourceId: scheduledData.enterpriseSourceId || "",
              serverName: scheduledData.serverStore || "",
              branch: scheduledData.branch || "",
              cancelledDate: cancelledDate || "",
              saveFopc: scheduledData.isFopcStore || false,
              mageGroupName: scheduledData.mageGroupName || "",
              mageGroupCode: scheduledData.mageGroupCode || "",
              mageStoreName: scheduledData.mageStoreName || "",
              mageStoreCode: scheduledData.mageStoreCode || "",
              billingCode: scheduledData.billingCode || ""
            });
          } else {
            this.formAction = "create";
          }
          console.log(this.formAction);
        })
        .catch((error) => {
          console.error("Error retrieving scheduler fields:", error);
        });
      console.log(this.schedulerFields);
    }
  selectStoreGroup(item: any, callback: any) {
    this.loadAllStore = true;
    this.storeList = [];
    this.storeFilterList = [];
    this.store = [];
    this.clearFormFields();
    if (!this.containsObject(item, this.storeGroup)) {
      this.storeGroup.push(item);
    }
    console.log("this.storeGroupList", this.storeGroupList);
    this.GetStoresForStoreGroup(item.id)
      .then((data) => {
        console.log(data);
        if (data && data.length > 0) {
          let itemFilter: any;
          this.storesWithOnlyStore = data.filter((store: { type: string | any[]; }) => store.type.length === 1 && store.type[0] !== "Fopc Meta Group");
          console.log("storeGroupFilterList (Store only)", this.storesWithOnlyStore);
          this.storesWithFopcMetaGroup = data.filter((store: { type: string | string[]; }) => store.type.includes("Fopc Meta Group"));
          console.log("storeGroupFilterList (Fopc Meta Group)", this.storesWithFopcMetaGroup);
          if (!this.metaGroup) {
            this.storeList = this.storesWithOnlyStore;
          } else {
            this.storeList = this.storesWithFopcMetaGroup;
          }
          this.loadAllStore = false;
          this.getStoreFilterList();
          this.storeGroupLoading = true;
          if (callback) {
            callback();
          }
        } else if (data && data.length === 0) {
          this.storeGroupLoading = true;
        }
      })
      .catch((error) => {
        console.error("Error fetching stores:", error);
        this.loadAllStore = false;
        this.storeGroupLoading = true;
      });
  }    
  /**
   * callback function for store selection
   *
   */
  onSelectStore(item: any) {
    this.storeSelected = true;
    this.store = [];
    this.getStoreFilterList();
    if (!this.containsObject(item, this.store)) {
      this.store.push(item);
    }
    const selectedStoreFilter: any = this.storeList.filter((res) => {
      return res.companyName === item.itemName;
    });
    const companyId = item.id;
    console.log("selected Store:",companyId);
    //const projectId = this.selectedStore.projectId;
    this.clearFormFields();
    this.getSchedulerFields(companyId)
      .then((data) => {
        if (data && data.length > 0) {
          console.log(data[0]);
          this.formAction = "update";
          const scheduledData = data[0];
          let cancelledDate: string = scheduledData.cancelledDate ? moment(
            new Date(scheduledData.cancelledDate)
          ).format(this.SchedulerConstantService.DATE_FORMAT) : "";
          this.createSchedule.patchValue({
            thirdPartyUserName: scheduledData.thirdPartyUserName || "",
            sourceId: scheduledData.enterpriseSourceId || "",
            serverName: scheduledData.serverStore || "",
            branch: scheduledData.branch || "",
            cancelledDate: cancelledDate || "",
            saveFopc: scheduledData.isFopcStore || false,
            mageGroupName: scheduledData.mageGroupName || "",
            mageGroupCode: scheduledData.mageGroupCode || "",
            mageStoreName: scheduledData.mageStoreName || "",
            mageStoreCode: scheduledData.mageStoreCode || "",
            billingCode: scheduledData.billingCode || ""
          });
        } else {
          this.formAction = "create";
        }
        console.log(this.formAction);
      })
      .catch((error) => {
        console.error("Error retrieving scheduler fields:", error);
      });
    console.log(this.schedulerFields);
  }
  /**
   * callback function for store group deselection
   *
   */
  OnDeSelectStoreGroup(item: any) {
    this.createSchedule.get('store')?.reset();
    this.metaGroup = false;
    this.storeList = [];
    this.storeFilterList = [];
    this.clearFormFields();
  }

  OnDeSelectStore(item: any) {
    this.clearFormFields();
  }

  /**
   * getStoreFilterList function will collect the store list for filtering purpose
   *
   */
  getStoreFilterList() {
    this.storeFilterList = [];
    console.log("this.this.storeList", this.storeList);
    for (let i = 0; i < this.storeList.length; i++) {
      const storeName = this.storeList[i].store_name;
      const stId = this.storeList[i].store_id;
      const stateCode = this.storeList[i].state;
      const etlDMSType = this.storeList[i].dms;
      const partsProjectId = this.storeList[i].parts_project_id ?? null;
      const laborProjectId = this.storeList[i].labor_project_id ?? null;
      if (stId) {
        const obj = {
          id: stId,
          itemName: storeName,
          stateCode: stateCode,
 	      partsProjectId: partsProjectId,
          laborProjectId: laborProjectId,
          etlDMSType: etlDMSType,
        };
       
        if (!this.containsObject(obj, this.storeFilterList)) {
          this.storeFilterList.push(obj);
        }
      }
    }
    this.storeFilterList = this.sortListAsc(this.storeFilterList);
    console.log("this.storeFilterList", this.storeFilterList);
  }

  /**
   * getGroupFilterList function will collect the group list for filtering purpose
   *
   */
  getGroupFilterList() {
    this.storeGroupFilterList = [];
    this.storeLoading = true;
    for (let i = 0; i < this.storeGroupList.length; i++) {
      console.log("storeGroupList..........", this.storeGroupList[i]); 
      const companyName = this.storeGroupList[i].companyName;
      const mageGroupCode = this.storeGroupList[i].mageGroupCode;
      const mageGroupName = this.storeGroupList[i].mageGroupName
        ? this.storeGroupList[i].mageGroupName
        : "";
      const mageStoreCode = this.storeGroupList[i].mageStoreCode;
      const billingCode = this.storeGroupList[i].billingCode;
      const mageStoreName = this.storeGroupList[i].mageStoreName;
      const projectId = this.storeGroupList[i].projectId;
      const companyId = this.storeGroupList[i].companyId;
      const sgId = this.storeGroupList[i].sgId;
      if (companyName) {
        const obj = {
          id: companyId,
          itemName: companyName,
          mageGroupCode: mageGroupCode,
          mageGroupName: mageGroupName,
          mageStoreCode: mageStoreCode,
          billingCode  : billingCode,
          mageStoreName: mageStoreName,
          projectId: projectId,
          companyId: companyId,
          sgId:this.storeGroupList[i].sgId
        };

        if (!this.containsObject(obj, this.storeGroupFilterList)) {
          this.storeGroupFilterList.push(obj);
        }
      }
    }
    this.storeGroupFilterList = this.sortListAsc(this.storeGroupFilterList);
    console.log("this.storeGroupFilterList", this.storeGroupFilterList);
    this.storeLoading = false;
  }
  containsObject(obj: any, list: any) {
    let i;
    for (i = 0; i < list.length; i++) {
      if (list[i].id === obj.id) {
        return true;
      }
    }
    return false;
  }
  /**
   * sortListAsc function will sort the list in ascending order.
   *
   */
  sortListAsc(temp: any) {
    temp = temp
      .filter((item: any, index: any) => index < temp.length)
      .sort((a: any, b: any): any => {
        const x = a["itemName"]
          ? a["itemName"].toLowerCase().replace(/^[^a-z0-9]*/g, "")
          : "";
        const y = b["itemName"]
          ? b["itemName"].toLowerCase().replace(/^[^a-z0-9]*/g, "")
          : "";
        return x < y ? -1 : x > y ? 1 : 0;
      });
    return temp;
  }

  reloadGroupList() {
    this.closeToolTip();
    this.reloadGroup = true;
    this.getAllSolve360StoreGroups()
    .then((result: any) => {
      //this.getGroupFilterList();
      this.reloadGroup = false;
    });
  }

  closeToolTip() {
    $("[rel=tooltip]").tooltip("enable");
    $(".tooltip").tooltip("hide");
  }

  cancelSchedule() {
    this.createSchedule.reset();
  }
  calculateSalesTag(formData: { thirdPartyUserName: any; accessGranted: any; extractionFailed: any; cancelledDate: any; }) {
    let addSalesTag = true;
    let salesTagComment = '';
    let thirdPartyUserName =false;
    let cancelledDate = false;
    let todaysTime = new Date().getTime();
    let canceledTime = new Date(formData.cancelledDate).getTime(); // Convert to Date object first
    if (formData.thirdPartyUserName && formData.thirdPartyUserName.trim() !== "") {
      thirdPartyUserName = true;
    }
    if (formData.cancelledDate) {
      cancelledDate = true;
    }
    // Example logic to determine addSalesTag and salesTagComment
    if (thirdPartyUserName &&  !cancelledDate) {
        addSalesTag = false;
        salesTagComment = "";
    } else if (!thirdPartyUserName) {        
        salesTagComment = "Need DMS Access Granted & Credentials";
    } else if (thirdPartyUserName && cancelledDate) {       
        salesTagComment = "Need DMS Access";
    }
    return { addSalesTag, salesTagComment };
}
  goToSchedule() {
    this.isLoading = true;
    if (this.createSchedule.valid) {
      const formData = this.createSchedule.value;
      const scheduleObj: any = {};
      let userName = "";
      const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
      if (currentUserObj) {
        userName = currentUserObj.userPrincipalName
          ? currentUserObj.userPrincipalName
          : "";
      }
      const storeValue = this.createSchedule.get('store')?.value;
      console.log('Store Value:', storeValue);
      const storeId = formData.store?.[0]?.id || storeValue;
      console.log(storeId)     
      const storeData = this.storeFilterList.find((x) => x.id === storeId);
    if (storeData) {
      const partsProjectId = storeData.partsProjectId ?? null;  // If undefined or null, assign null
      const laborProjectId = storeData.laborProjectId ?? null;  // If undefined or null, assign null
      
      if (partsProjectId !== null) {  // Check explicitly for null
        scheduleObj.inPartsProjectId = partsProjectId;
        scheduleObj.inPartsProjectType = "Parts UL";
      }
    
      if (laborProjectId !== null) {  // Check explicitly for null
        scheduleObj.inLaborProjectId = laborProjectId;
        scheduleObj.inLaborProjectType = "Labor UL";
      }
    }
scheduleObj.inCompanyId = parseInt(storeId) || null;
      formData.sourceId &&
        (scheduleObj.inEnterpriseSourceId = formData.sourceId);
      formData.thirdPartyUserName &&
        (scheduleObj.inThirdPartyUserName = formData.thirdPartyUserName);
      formData.serverName && (scheduleObj.inServerStore = formData.serverName);
      formData.branch && (scheduleObj.inBranch = formData.branch);
      formData.mageStoreCode &&
        (scheduleObj.inMageStoreCode = formData.mageStoreCode);
      formData.billingCode &&
        (scheduleObj.inBillingCode= formData.billingCode);
      formData.mageStoreName &&
        (scheduleObj.inMageStoreName = formData.mageStoreName);
      formData.mageGroupCode &&
        (scheduleObj.inMageGroupCode = formData.mageGroupCode);
      formData.mageGroupName &&
        (scheduleObj.inMageGroupName = formData.mageGroupName);
      formData.cancelledDate &&
        (scheduleObj.inCancelledDate = formData.cancelledDate);
      formData.saveFopc && (scheduleObj.inIsFopcStore = formData.saveFopc);
      scheduleObj.inUpdatedBy = userName;
      scheduleObj.formAction = this.formAction;    
    const { addSalesTag, salesTagComment } = this.calculateSalesTag(formData);
    scheduleObj.inIsInSales = addSalesTag;
    scheduleObj.inSalesComment = salesTagComment;
      console.log(scheduleObj);
      const requestUriData = scheduleObj;
      let activityDataSchedule = {
        activityName: "adding shedule fields",
        activityType: "Addschedulefields",
        activityDescription: `add schedule fields params ${requestUriData}.
    )})`,
      };
      this.commonService.saveActivity(
        "Add Schedule fields",
        activityDataSchedule
      );
      const token = localStorage.getItem("token");
      const headers = new HttpHeaders({
        authorization: token ? `Bearer ${token}` : "",
        requestData: JSON.stringify(requestUriData),
        "Content-Type": "application/json",
      });
      let url = environment.schedulerAddfieldsUrl;
      this.http
        .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
        .subscribe((res: any) => {
          let activityDataSchedule = {
            activityName: "schedulerAddFields",
            activityType: "schedulerAddFields",
            activityDescription: `insert schedulerAddFields with request params ${JSON.stringify(
              requestUriData
            )} and response ${JSON.stringify(res)}.`,
          };
          this.commonService.saveActivity("schedulerAddFields", activityDataSchedule);
          this.createSchedule.reset();
          this.isLoading = false;
          if (res.status == "success") {
            const message = res.message ? res.message : "Success" ;           
            swal({
              title:message,
              type: "success",
              confirmButtonClass: "btn-success pointer",
              confirmButtonText: this.constantService.OK,
            });
          } if (res.status == "warning") {       
            let swalText = `FOPC: ${res.API.FOPC}\nSales: ${res.API.Sales}\nPortal: ${res.API.Portal}`;
            if (res.API.SalesTagLabor) {
              swalText += `\nSalesTagLabor: ${res.API.SalesTagLabor}`;
            }

            if (res.API.SalesTagParts) {
              swalText += `\nSalesTagParts: ${res.API.SalesTagParts}`;
            }       
            swal({
              title: "Warning",
              text: swalText,
              customClass: {
                popup: 'text-left'
              },
              type: "warning",
              confirmButtonClass: "btn-warning pointer",
              confirmButtonText: this.constantService.CLOSE,
            });
          } else {
            console.log("elsecase", res);
            if (res.status == "error") {
              const message = "Error: schedulefields not Saved";
              swal({
                title: message,
                type: "warning",
                confirmButtonClass: "btn-warning pointer",
                confirmButtonText: this.constantService.CLOSE,
              });
            }
          }
        },
          (error: any) => {
            // Handle error responses
            this.isLoading = false;
  
            if (error.status === 500) {
              const errorMessage = error.error?.message || "An unexpected error occurred.";
              swal({
                title: "Server Error",
                text: `Failed to save schedule fields`,
                type: "error",
                confirmButtonClass: "btn-danger pointer",
                confirmButtonText: this.constantService.CLOSE,
              });
            } else {
              swal({
                title: "Error",
                text: "An unexpected error occurred. Please try again later.",
                type: "error",
                confirmButtonClass: "btn-danger pointer",
                confirmButtonText: this.constantService.CLOSE,
              });
            }
  
            console.error("HTTP Error:", error);
          }
        );
    } else {
      console.error("Form is invalid");
    }
  }
}

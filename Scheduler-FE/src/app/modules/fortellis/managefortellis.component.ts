import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ement<PERSON><PERSON>, Renderer2 } from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { ConstantService } from "../../structure/constants/constant.service";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Apollo } from "apollo-angular";
import gql from "graphql-tag";
import { Router } from "@angular/router";
import { CommonService } from "../../structure/services/common.service";
import * as moment from "moment-timezone";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { EventEmitterService } from "../../structure/services/event.emitter.services";
import { SchedulerConstantService } from "../../structure/constants/scheduler.constant.service";
import { DmFormGroupService } from "../../structure/services/dm.formgroup.services";
import { environment } from "./../../../environments/environment";
import { SubscriptionConstantService } from "./../../structure/constants/subscription.constant.service";
import { Subject, takeUntil } from "rxjs";
import { SharedModule } from "../shared/shared.module";
import * as dayjs from "dayjs"; 

let table1;
declare var $: any;
declare var jQuery: any;
declare var swal: any;
declare var NProgress: any;

/**
 * Mutation to create new schedule
 *
 */
const createNewSchedule = gql`
  mutation scheduleFortellisExtractJob(
    $jobSchedule: DateTime!
    $jobData: JobData!
  ) {
    scheduleFortellisExtractJob(
      input: { jobSchedule: $jobSchedule, jobData: $jobData }
    ) {
      status
      message
      job {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
      }
    }
  }
`;

/**
 * Mutation to cancel schedule
 *
 */
const cancelFortellisExtractJobByStore = gql`
  mutation cancelFortellisExtractJobByStore(
    $jobSchedule: DateTime!
    $jobData: SingleStoreJobData!
  ) {
    cancelFortellisExtractJobByStore(
      input: { jobSchedule: $jobSchedule, jobData: $jobData }
    ) {
      status
      message
      job {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
      }
    }
  }
`;

const runNowFortellisExtractJobByStore = gql`
  mutation runNowFortellisExtractJobByStore(
    $jobSchedule: DateTime!
    $jobData: SingleStoreJobData!
  ) {
    runNowFortellisExtractJobByStore(
      input: { jobSchedule: $jobSchedule, jobData: $jobData }
    ) {
      status
      message
      job {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
      }
    }
  }
`;

const getAllFortellisExtractJobs = gql`
  query getAllFortellisExtractJobs {
    getAllFortellisExtractJobs {
      timeFrameZone
      timeFrameStartTime
      timeFrameEndTime
      poolTime
      jobArray {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
        running
        scheduled
        queued
        completed
        failed
        repeating
        failReason
        data {
          groupName
          storeDataArray {
            dealerId
            projectId
            secondProjectId
            glAccountCompanyID
            mageManufacturer
            solve360Update
            buildProxies
            userName
            startDate
            endDate
            message
            startTime
            endTime
            closedROOption
            status
            jobType
            mageGroupCode
            mageStoreCode
            dealerAddress
            projectIds
            secondProjectIdList
            testData
	          companyIds
            companyObj
            processFileName
          }
        }
      }
    }
  }
`;

const getAllFortellisProcessJSONJobs = gql`
  query getAllFortellisProcessJSONJobs {
    getAllFortellisProcessJSONJobs {
      processJSONJobs {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
        running
        scheduled
        queued
        completed
        uploadStatus
        failed
        repeating
        failReason
        data {
          inputFile
          storeID
          outputFile
          status
          message
          operation
          createdAt
          miscExceptionCount
          address
          processorUniqueId
        }
      }
      processJSONJobsQueue {
        storeID
        fileToProcess
	      priority
      }
    }
  }
`;

const createProxyWithSqlDump = gql`
  mutation createProxyWithSqlDump($proxyJobData: ProxyGenerationUsingPgDump!) {
    createProxyWithSqlDump(input: { proxyJobData: $proxyJobData }) {
      status
      message
    }
  }
`;

@Component({
  selector: "app-managefortellis",
  templateUrl: "./managefortellis.component.html",
  styleUrls: ["./managefortellis.component.css"],
  standalone: true,
  imports: [
    SharedModule,
  ],
})
export class ManagefortellisComponent implements OnInit {
  public isAuthenticated = false;
  private subscription$ = new Subject();
  loading: any = false;
  private storeGroupList: any[] = [];
  private storeList: any[] = [];
  private storeFlag = false;
  private selectedGroup = null;
  private selectedStore = null;
  private storeGroupFlag = false;
  private storeLoading: any = false;
  private scheduleDate = null;
  private processQueueList: any[] = [];
  private processQueueListCompleted: any[] = [];
  private listener: any;
  private jobGroupList: any[] = [];
  private timeFrameZone: any;
  private processJsonListArray: any[] = [];
  private roOptionList: any;
  private onChangeRoOption: any[] = [];
  private compareObjArray: any[] = [];
  private compareObjArrayLatest: any[] = [];
  private compareObjArrayProcessJSONList: any[] = [];
  private processJSONJobsQueueLength = 0;
  private jobTypeStatus = true;
  private autoReloadFortellisStatus = true;
  public storeGroupFilterList: any[] = [];
  public storeFilterList: any[] = [];
  public store: any[] = [];
  public storeGroup: any[] = [];
  public scheduleProcessQueueLoading = false;
  public scheduleProcessQueueCompletedLoading = false;
  public processJSONLoading = false;
  public createSchedule!: FormGroup;
  public loadingSchedule = false;
  public loadingScheduleCompleted = false;
  public loadingProcessJson = false;
  public loadAllStore = false;
  public completedListCollapsed = true;
  public processQueueListCollapsed = false;
  public completedProcessjsonListCollapsed = false;
  public timeFrameStartTime: any;
  public timeFrameEndTime: any;
  public tootlTipInfo = null;
  public tootlTipInfoTitle = null;
  public selectMultiDateRangeOption = true;
  public reloadGroup = false;
  public isPaused = true;
  public displayOnDemand = false;
  public modalDisplayFlag = true;
  public showSpinnerButton = true;
  public updateDealeAddressInput: any;
  public uniqueCode: any;
  public priorityList: any[] = [];
  public numberInput: any;
  public fileNameForPriorityChange='';
  public changePriorityForProcessorJob: any;
  public currentPriority ='';
  public uniqueIdentifier: any;
  public dealerAddress: any;
  public dealerAddressProcessor: any;
  public doProxyFilePath: any;
  public solve360ServerDecider!: boolean;
  public isTestSchedule = false;
  public isMockServer = environment.envName == "prod" ? false : true;
  public dateInput: any ;

  public changeSchedule(event:any){
    this.isTestSchedule = this.createSchedule.get('testSchedule')?.value;

 }
  
  public isTestServer(value:any){
    this.isTestSchedule = value
  }

  
  public singleDropdownSettings: any;
  public dropdownSettings: any;
  public singleDropdownSettingsDisable: any;
  public multiDropdownSettings: any;
  public multiDropdownSettingsDisable: any;

  public agendaDashboardUrl = "";
  public shows360Link = false;
  public s360CompanyId: any;
  public poolTime: any;

  public resumeProcessorInput: any;
  public haltState!: string;
  public miscExceptionCountMsg: any;
  selectedRange: any; // Initialize selectedRange

  constructor(
    private http: HttpClient,
    private apollo: Apollo,
    public constantService: ConstantService,
    private elRef: ElementRef,
    private renderer: Renderer2,
    private toastrService: ToastrService,
    private commonService: CommonService,
    private EventEmitterService: EventEmitterService,
    private router: Router,
    private DmFormGroupService: DmFormGroupService,
    private SchedulerConstantService: SchedulerConstantService,
    public SubscriptionConstantService: SubscriptionConstantService,
    // private modal: Modal
  ) {}

  ngOnDestroy() {
    this.autoReloadFortellisStatus = false;
    localStorage.removeItem("selectedStoreObj");
    localStorage.removeItem("selectedGroupObj");
  }
  ngOnInit() {
    let activityData = {
      activityName: "Manage Fortellis",
      activityType: "Manage Fortellis",
      activityDescription: "Current Page: " + this.router.url,
    };
    this.commonService.saveActivity("Manage Fortellis", activityData);
    this.SubscriptionConstantService.pageTitle = " - Fortellis";
    let scheduleUrl = environment.jobListUrl;
    this.multiDropdownSettings = {
      enableSearchFilter: true,
      idField: "id",
      textField: "itemName",
      allowSearchFilter: true,
    };
    scheduleUrl = scheduleUrl.replace(
      this.SchedulerConstantService.END_POINTS.GRAPHQL_END_POINT,
      this.SchedulerConstantService.END_POINTS.AGENDA_END_POINT
    );
    this.agendaDashboardUrl = scheduleUrl;
    this.dropdownSettings = this.DmFormGroupService.dropdownSettings();
    this.singleDropdownSettings =
      this.DmFormGroupService.singleDropdownSettings();
    this.singleDropdownSettingsDisable =
      this.DmFormGroupService.singleDropdownSettingsDisable();
    this.commonService.getGroups(() => {
      this.commonService.checkGroups((flag) => {
        if (!flag) {
          return;
        }
        this.isAuthenticated = true;
        this.init();
      });
    });
    this.selectedRange = [
      this.startDateSelection().toDate(),
      this.endDateSelection().toDate(),
    ];
    console.log("initial start------------", this.selectedRange);
    this.dateInput = {
      start: this.selectedRange[0],
      end: this.selectedRange[1],
    };
    console.log("initial start------------dateInput", this.dateInput);
    // this.dateInput = {
    //   start: this.startDateSelection(),
    //   end: this.endDateSelection(),
    // };
  }

  init() {
    this.priorityList = [
      {id:1,itemName:'1'},
      {id:2,itemName:'2'},
      {id:3,itemName:'3'},
      {id:4,itemName:'4'},
      {id:5,itemName:'5'},
      {id:6,itemName:'6'},
      {id:7,itemName:'7'}
    ]
    this.getRoOptionList();
    this.createSchedule = new FormGroup({
      storeGroup: new FormControl("", Validators.required),
      store: new FormControl("", Validators.required),
      roOption: new FormControl(
        this.SchedulerConstantService.DEFAULT_RO_OPTION
      ),
      updateRetreiveROinSolve360: new FormControl(false),
      buildProxies: new FormControl(true),
      dealerAddress: new FormControl(""),
      solve360ServerDecider: new FormControl(false),
      jobType: new FormControl(this.SchedulerConstantService.DEFAULT_JOB_TYPE),
      dealerAddressProcessor: new FormControl(),

      testSchedule: new FormControl(false),
      testDealerId: new FormControl(''),
      testGlId: new FormControl(''),
      testStoreCode: new FormControl(''),
      testGroupCode: new FormControl('')
    });
    this.getAllJobs(() => {
      this.showScheduledProcessList(true);
      this.showScheduledProcessListCompleted(true);
    });
    this.getAllProcessJsonJobs(() => {
      this.showProcessJsonList();
    });
    this.loading = true;
    this.commonService.allS360Jobs("CDKFORTELLIS", "production", (result: any) => {
      this.loading = false;
      this.storeGroupList = result.storeGroupList;
      this.storeList = result.storeList;
      this.jobGroupList = result.jobGroupList;
      console.log(
        "############################################################"
      );
      console.log(result.jobGroupList);
      console.log(
        "############################################################"
      );
      this.getGroupFilterList();
      this.preSelectGroupAndStore();
    });
    let objPropertyCalender: { [k: string]: any } = {};
    objPropertyCalender = this.commonService.getCalenderPropertyObject();
    objPropertyCalender["minDate"] = new Date();
    $(() => {
      $('[data-toggle="tooltip"]').tooltip({
        trigger: "hover",
        html: true,
      });
    });
    if (!this.listener) {
      this.listener = this.renderer.listen(
        this.elRef.nativeElement,
        "click",
        (evt: any) => {
          console.log(
            "evt.target___________________________________________",
            evt.target
          );
          if (evt.target.className === "fa fa-play-circle overrideSchedule") {
            this.runNowSchedule(evt.target.dataset.info);
          } else if (evt.target.className === "fa fa-ban cancelSchedule") {
            this.cancelSchedule(evt.target.dataset.info);
          } else if (
            evt.target.className.indexOf("statusMessageDisplay") != -1
          ) {
            let res = evt.target.dataset.info.split(",");
            let toolTipInfo = this.getProcessJsonToolTipData(res);
            this.showAlert(toolTipInfo, "Process JSON Status");
          } else if (
            evt.target.className.indexOf("popUpInfoCompletedJobs") != -1
          ) {
            let res = evt.target.dataset.info.split(",");
            var toolTipInfo = this.getToolTipInfoCompletedJobs(res);
            this.showAlert(toolTipInfo, "Completed Extractions");
          } else if (
            evt.target.className ===
            "fa fa-caret-square-o-up text-success mt-2 updateDealerAddress"
          ) {
            this.updateDealerAddress(evt.target.dataset.info);
          } else if (
            evt.target.className ===
            "fa fa-caret-square-o-up text-success mt-2 haltAndResumeDetails"
          ) {
            // alert("Hiiiiiiiiiiiiiiiiii");
            this.haltAndResumeDetails(evt.target.dataset.info);
          } else if (evt.target.className === 'fa fa-edit text-primary mt-2 changePriority') {
          console.log('Change priority>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>',evt.target.dataset.info);
          this.changePriority(evt.target.dataset.info);
        }
else if (evt.target.className === 'fa fa-upload text-primary mt-2 requeue') {
            console.log("requeue event data%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%", evt.target.dataset.info);
            this.requeue(evt.target.dataset.info);
          }
        }
      );
    }
    this.getNotificationForUi();
  }

  getNotificationForUi() {
    let self = this;
    if (this.autoReloadFortellisStatus) {
      this.getAllJobsForUiUpdate((data: any) => {
        this.compareObjectValue();
        this.processJSONReloadList((result: any) => {
          this.compareObjectValueProcessJSON();
          setTimeout(() => {
            self.getNotificationForUi();
          }, 3000);
        });
      });
    }
  }

  getProcessJsonToolTipData(rowData: any) {
    let toolTip = "";
    let className = "";
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.COMPLETED
      ? (className = "label-success")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.SCHEDULED
      ? (className = "label-scheduled")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.RUNNING
      ? (className = "label-running")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.REPEATING
      ? (className = "label-repeating")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.FAILED
      ? (className = "label-failed")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.QUEUED
      ? (className = "label-queued")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.LOCKED
      ? (className = "label-locked")
      : null;
    let status = '<span class="label ' + className + '">' + rowData[2] + "</span>";
    toolTip += rowData[8] + " \n";
    if (rowData[7] && rowData[2] === "Failed") {
      toolTip += "Failed Reason: " + rowData[7] + " \n";
    }    
    if (rowData[15] === "false") {      
      toolTip += '<span class="label label-success">Completed</span><span style="padding-left: 1rem" class="label label-upload-halt">' + this.SchedulerConstantService.STATUS_FLAG.UPLOADHALT + '</span>\n';
    } else{
      toolTip += "Status: " + status + " \n";
      }
           return toolTip;
  }

  showAlert(toolTipText: any, title: any) {
    this.tootlTipInfo = toolTipText.split("\n").join("<br>");
    this.tootlTipInfoTitle = title;
    $("#resolveProjectModal").modal({ show: true, backdrop: "static" });
  }

  closeToolTipModal() {
    $("#resolveProjectModal").modal("hide");
  }

  /**
   * callback function for store group deselection
   *
   */
  OnDeSelectStoreGroup(item: any) {
    // this.dealerAddressProcessor = "";
    // if (!this.containsObject(item, this.storeGroup)) {
    //   this.storeGroup.push(item);
    // }
    // this.getGroupFilterList();
    this.store = [];
    this.storeFilterList = [];
    this.dealerAddressProcessor = "";
    this.createSchedule.reset();
    this.createSchedule.patchValue({
      updateRetreiveROinSolve360: false,
      buildProxies: true,
    });
    // this.storeFilterList = [];
    this.selectMultiDateRangeOption = true;
    this.setDateRange(true, "");
    // this.dateInput = {
    //   start: this.startDateSelection(),
    //   end: this.endDateSelection(),
    // };
    this.selectedRange = [
      this.startDateSelection().toDate(),
      this.endDateSelection().toDate(),
    ];
    this.dateInput = {
      start: this.selectedRange[0],
      end: this.selectedRange[1],
    };
    this.createSchedule.patchValue({
      roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
      jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE,
    });
    this.getGroupFilterList();
  }

  OnDeSelectStore(item: any) {
    this.dealerAddressProcessor = "";
    if (!this.containsObject(item, this.store)) {
      this.store.push(item);
    }
    this.getStoreFilterList();
  }

  /**
   * getGroupFilterList function will collect the group list for filtering purpose
   *
   */
  getGroupFilterList() {
    this.storeGroupFilterList = [];
    for (let i = 0; i < this.storeGroupList.length; i++) {
      const companyName = this.storeGroupList[i].mageGroupName;
      const mageGroupCode = this.storeGroupList[i].mageGroupCode;
      const mageGroupName = this.storeGroupList[i].mageGroupName
        ? this.storeGroupList[i].mageGroupName
        : "";
      const mageStoreCode = this.storeGroupList[i].mageStoreCode;
      const mageStoreName = this.storeGroupList[i].mageStoreName;
      const companyId = this.storeGroupList[i].companyId;
      const projectId = this.storeGroupList[i].projectId;
      const secondProjectId = this.storeGroupList[i].secondaryProjectId;
      const projectType = this.storeGroupList[i].projectType;
      const secondProjectType = this.storeGroupList[i].secondProjectType;
      if (companyName) {
        const obj = {
          id: companyName,
          itemName: companyName,
          mageGroupCode: mageGroupCode,
          mageGroupName: mageGroupName,
          mageStoreCode: mageStoreCode,
          mageStoreName: mageStoreName,
          companyId: companyId,
          projectId: projectId,
          secondProjectId: secondProjectId,
          projectType:projectType,
          secondProjectType:secondProjectType
        };
        if (!this.containsObject(obj, this.storeGroupFilterList)) {
          this.storeGroupFilterList.push(obj);
        }
      }
    }
    this.storeGroupFilterList = this.sortListAsc(this.storeGroupFilterList);
  }
  containsObject(obj: any, list: any) {
    let i;
    for (i = 0; i < list.length; i++) {
      if (list[i].id === obj.id) {
        return true;
      }
    }
    return false;
  }

  /**
   * sortListAsc function will sort the list in ascending order.
   *
   */
  sortListAsc(temp: any) {
    temp = temp
      .filter((item: any, index: number) => index < temp.length)
      .sort((a: any, b: any): any => {
        const x = a["itemName"]
          ? a["itemName"].toLowerCase().replace(/^[^a-z0-9]*/g, "")
          : "";
        const y = b["itemName"]
          ? b["itemName"].toLowerCase().replace(/^[^a-z0-9]*/g, "")
          : "";
        return x < y ? -1 : x > y ? 1 : 0;
      });
    return temp;
  }
  /**
   * callback function for store group selection
   *
   */
  onSelectStoreGroup(item: any) {
    this.dealerAddressProcessor = "";
    this.selectStoreGroup(item, () => {
      this.shows360Link = true;
      console.log('s360CompanyId',item, "storeGroupFilterList" ,this.storeGroupFilterList)
      let itemFilter = this.storeGroupFilterList.find(res => res.id == item.id);
      this.s360CompanyId = itemFilter.companyId;
    });
  }

  selectStoreGroup(item: any, callback: any) {
    this.loadAllStore = true;
    this.storeList = [];
    this.storeFilterList = [];
    this.store = [];
    this.storeFlag = false;
    if (!this.containsObject(item, this.storeGroup)) {
      this.storeGroup.push(item);
    }
    let selectedGroupFilter: any = this.storeGroupList.filter(function (res) {
      return res.sgId == item.id;
    });
    this.selectedGroup = selectedGroupFilter[0];
    //this.getGroupFilterList();
    let itemFilter: any = this.storeGroupFilterList.filter(function (res) {
      return res.id == item.id; //updated as per new array list
    });
    console.log("this.storeGroupFilterList", itemFilter);
    //this.getGroupFilterList();
    this.getStoreList("CDKFORTELLIS", itemFilter[0], () => {
      this.loadAllStore = false;
      this.getStoreFilterList();
      this.storeGroupFlag = true;
      if (callback) {
        callback();
      }
    });
  }

  /**
   * getStoreList function fetch Stores list for the selected StoreGroup
   *
   */
  getStoreList(type: any, item: any, callback: any) {
    const groupCode = item.mageGroupCode;
    this.loading = true;
    this.storeLoading = true;
    this.storeList = [];
    this.storeList = this.jobGroupList
      .filter((item: any, index: number) => index < this.jobGroupList.length)
      .filter(
        (item: any, index: number) =>
          item.mageGroupCode != null &&
          item.mageGroupCode === groupCode &&
          item.thirdPartyUsername !== null
      );
    this.loading = false;
    this.storeLoading = false;
    if (callback) {
      callback();
    }
  }

  /**
   * getStoreFilterList function will collect the store list for filtering purpose
   *
   */
  getStoreFilterList() {
    this.storeFilterList = [];
    for (let i = 0; i < this.storeList.length; i++) {
      const companyID = this.storeList[i].companyId;
      const companyName = this.storeList[i].companyName;
      const storeName = this.storeList[i].companyName;
      const stId = this.storeList[i].companyName;
      const mageGroupCode = this.storeList[i].mageGroupName;
      const mageStoreName = this.storeList[i].mageStoreName;
      const mageStoreCode = this.storeList[i].mageStoreCode;
      const thirdPartyUsername = this.storeList[i].thirdPartyUsername;
      const glAccountCompanyID = this.storeList[i].dealerbuiltSourceId;
      const stateCode = this.storeList[i].state;
      const projectId = this.storeList[i].projectId;
      const solve360Update = this.storeList[i].solve360Update;
      const buildProxies = this.storeList[i].buildProxies;
      const address = "QA address"; //this.storeList[i].address;
      const mageManufacturer = this.storeList[i].mageManufacturer;
      const streetaddress = this.storeList[i].streetaddress;
      const city = this.storeList[i].city;
      const zipcode = this.storeList[i].zipcode;
      const secondProjectId = this.storeList[i].secondaryProjectId;
      const secondaryProjectType = this.storeList[i].secondaryProjectType;
      const projectType = this.storeList[i].projectType;
      const projectName = this.storeList[i].projectName;
      const secondaryProjectName = this.storeList[i].secondaryProjectName;
      const groupCode = this.storeList[i].mageGroupCode;
      if (stId) {
        const obj = {
          id: stId,
          itemName: `${storeName} [${thirdPartyUsername}]`,
          mageGroupCode: mageGroupCode,
          mageStoreName: mageStoreName,
          mageStoreCode: mageStoreCode,
          thirdPartyUsername: thirdPartyUsername,
          stateCode: stateCode,
          projectId: projectId,
          solve360Update: solve360Update,
          buildProxies: buildProxies,
          companyID: companyID,
          glAccountCompanyID: glAccountCompanyID,
          address: address,
          mageManufacturer: mageManufacturer,
          streetaddress: streetaddress,
          city: city,
          zipcode: zipcode,
          secondProjectId: secondProjectId,
          projectType:projectType,
          secondaryProjectType:secondaryProjectType,
          projectName:projectName,
          secondaryProjectName:secondaryProjectName,
          groupCode:groupCode,          
          errors:this.storeList[i].errors,
          assignedtoCn:this.storeList[i].assignedtoCn,
          companyName:companyName

        };
        if (!this.containsObject(obj, this.storeFilterList)) {
          this.storeFilterList.push(obj);
        }
      }
      console.log("this.storeFilterList>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",this.storeFilterList);
    }
    this.storeFilterList = this.sortListAsc(this.storeFilterList);
    console.log("this.storeFilterList>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>1",this.storeFilterList);
  }

  /**
   * callback function for store selection
   *
   */
  onSelectStore(item: any) {

    console.log("ITEM:::::::::::::::::::::::::::::::;;", item);
    let itemFilter = this.storeFilterList.find(res => res.id == item.id);

    this.dealerAddressProcessor = `${
      itemFilter.itemName ? itemFilter.itemName.toUpperCase().split("[")[0] + "\n" : ""
    }${itemFilter.streetaddress ? itemFilter.streetaddress + "\n" : ""}${
      itemFilter.city ? itemFilter.city + ", " : ""
    }${itemFilter.stateCode ? itemFilter.stateCode + " " : ""}${itemFilter.zipcode || ""}`;
    // this.store = [];
    this.getStoreFilterList();
    if (!this.containsObject(item, this.store)) {
      this.store.push(item);
    }
    const selectedStoreFilter: any = this.storeList.filter(function (res) {
      return res.companyName == item.id;
    });
    this.selectedStore = selectedStoreFilter[0];
    this.storeFlag = true;
  }

  /**
   *  Select the date from datepicker in the Filter
   *
   */
  public selectedDate(value: any, dateInput: any) {
    dateInput.start = value.start;
    dateInput.end = value.end;
  }

  validateDealerAddress() {
    if (this.createSchedule.get("dealerAddressProcessor")?.value?.trim()) {
      this.saveSchedule();
    } else {
      swal(
        {
          title: this.constantService.AREYOUSURE,
          text: this.SchedulerConstantService.DEALER_ADDRESS_EMPTY,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default pointer",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: "Continue",
          closeOnConfirm: true,
          showLoaderOnConfirm: true,
        },
        () => {
          this.saveSchedule();
        }
      );
    }
  }

  saveSchedule() {
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      var userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
    }
    let storeFilterListTemp = this.storeFilterList;
    let storeTemp1: any = storeFilterListTemp.filter(
      (x) => x.id == this.createSchedule.get("store")?.value[0].id
    );

    //   const dealerCode = this.createSchedule.get("store")?.value[0].thirdPartyUsername
    //   ? storeTemp1.thirdPartyUsername
    //   : "3PAARMLIVE1";
    // const mageGrpCode = this.createSchedule.get("store")?.value[0].mageGroupCode;
    // const mageStrCode = this.createSchedule.get("store")?.value[0].mageStoreCode;
    // const steCode = this.createSchedule.get("store")?.value[0].stateCode;

    const dealerCode = storeTemp1[0].thirdPartyUsername
      ? storeTemp1[0].thirdPartyUsername
      : "3PAARMLIVE1";
    const mageGrpCode = storeTemp1[0].mageGroupCode;
    const mageStrCode = storeTemp1[0].mageStoreCode;
    const steCode = storeTemp1[0].stateCode;
    const groupCode = storeTemp1[0].groupCode;
    const mageStoreName = storeTemp1[0].mageStoreName;
    let solve360Update;
    let buildProxies;
    let dealerAddress;

    //Get projectId from local storage
    // let projectId = this.createSchedule.get("store")?.value[0].projectId ? this.createSchedule.get("store")?.value[0].projectId : "";

    let projectId = storeTemp1[0].projectId ? storeTemp1[0].projectId : "";
    const errors= storeTemp1[0].errors || "";
    const assignedtoCn= storeTemp1[0].assignedtoCn || "";
    const thirdPartyUsername = storeTemp1[0].thirdPartyUsername || "";
    //Get second projectId from local storage
    let secondProjectId = "";
    // const storeTemp = this.createSchedule.get("store")?.value;
    let storeTemp: any = storeFilterListTemp.filter((c: any) =>
      this.createSchedule.get("store")?.value.some((s: any) => s.id === c.id)
    );
    let tempProject = storeTemp.map((store: any) => store.projectId);
    let tempSecond = storeTemp.map((store: any) => store.secondProjectId);
    let brandList = storeTemp.map((store: any) => store.mageManufacturer);
    console.log("tempSecond!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!",tempSecond);
    if(tempSecond.length>0 && tempSecond[0]!=undefined && tempSecond[0]!=''){
      secondProjectId =  tempSecond[0];    
    }
    console.log("secondProjectId!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!",secondProjectId);
    let companyIdList = storeTemp.map((store: any) => store.companyID);
    let companyObj = storeTemp.map((store: any) => ({
      companyId: store.companyID,
      projectId: store.projectId,
      companyName: store.companyName,
      secondProjectId: store.secondProjectId,
      projectType: store.projectType,
      secondaryProjectType: store.secondaryProjectType,
      projectName: store.projectName,
      secondaryProjectName: store.secondaryProjectName  
    }));
    console.log("companyObj",companyObj);   
    let projectIds = "";
    let secondProjectIdList = "";
    let companyIds = '';
    let brands = "";
    for (let i = 0; i < tempProject.length; i++) {
      projectIds += tempProject[i] + "*";
    }

    for (let i = 0; i < tempSecond.length; i++) {
      if (tempSecond[i] != undefined) {
        secondProjectIdList += tempSecond[i] + "*";
      }
    }

    for(let i=0;i<companyIdList.length;i++){
      if(companyIdList[i]!=undefined){
        companyIds+=companyIdList[i]+'*';
      }
      
    }
    for (let i = 0; i < brandList.length; i++) {
      if (brandList[i] != undefined) {
        brands += brandList[i] + '*';
      }
    }
    console.log("Company Ids$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",companyIds);

    let dealerIdList = storeTemp.map((store: any) => store.thirdPartyUsername);

    let allDealerIdSame = this.areAllDealerIdSame(dealerIdList);
    const glAccountCompanyID = storeTemp1[0].glAccountCompanyID;
    // if (
    //   this.createSchedule
    //     .get("store")
    //     ?.value[0].hasOwnProperty("secondProjectId")
    // ) {
    //   secondProjectId = storeTemp1[0].secondProjectId;
    // }
    // const parentID = this.createSchedule.get("store")?.value[0].companyID;
    const parentID = storeTemp1[0].companyID;
    const companyId: number = parseInt(parentID);
    console.log("company ID:", companyId);

    // for(let i=0;i<companyIdList.length;i++){
    //   this.getSecondProjectId(companyIdList[i],(result)=>{
    //    if(result){
    //      if (result[1]) {
    //        if (result[1].hasOwnProperty('project_id')) {
    //          secondProjectIdList+=result[1].project_id.toString()+'*';
    //        }
    //      }
    //      if (result[0]) {
    //       if (result[0].hasOwnProperty('project_id')) {
    //         projectIds+=result[0].project_id.toString()+'*';
    //       }
    //     }
    //    }
    //   })
    // }
    // console.log('Second project id list after callback',secondProjectIdList)
    // const mageManufacturer =
    //   this.createSchedule.get("store")?.value[0].mageManufacturer;
    const mageManufacturer = storeTemp1[0].mageManufacturer;
    console.log(mageManufacturer);
    const projectType = storeTemp1[0].projectType;
    const secondaryProjectType = storeTemp1[0].secondaryProjectType;
    // const dealerAddress = this.createSchedule.get('store')?.value[0].address;

    this.getSecondProjectId(companyId, (result: any) => {
      console.log("+++++++++++++++++++++++++++++++++++++++++++");
      console.log(result);
      console.log("+++++++++++++++++++++++++++++++++++++++++++");

      let s: string = moment(new Date(this.dateInput.start)).format(
        this.SchedulerConstantService.DATE_FORMAT
      );
      let e: string = moment(new Date(this.dateInput.end)).format(
        this.SchedulerConstantService.DATE_FORMAT
      );
      let dateRangeCompare = s + " - " + e;
      let isExist = this.checkJobExistInExtractionQueue(
        this.createSchedule.get("storeGroup")?.value[0].itemName.trim(),
        dealerCode,
        dateRangeCompare
      );
      if (isExist) {
        swal({
          title: this.SchedulerConstantService.JOB_STARTED,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      } else {
        if (!allDealerIdSame) {
          this.toastrService.error (
            "Please select stores with same DealerID");
        }
        if (this.createSchedule.valid && allDealerIdSame) {
          let activityData = {
            activityName: "Manage Fortellis",
            activityType: "Create Schedule",
            activityDescription: `Create New Schedule for Group ${this.createSchedule
              .get("storeGroup")
              ?.value[0].itemName.trim()} (Group: ${mageGrpCode}, Store: ${mageStrCode})`,
          };
          this.commonService.saveActivity("Manage Fortellis", activityData);
          let roOptionValue = this.createSchedule.get("roOption")?.value;
          let jobType = this.createSchedule.get("jobType")?.value;
          solve360Update = this.createSchedule.get(
            "updateRetreiveROinSolve360"
          )?.value;
          buildProxies = this.createSchedule.get("buildProxies")?.value;
          dealerAddress = this.dealerAddressProcessor
            ? this.dealerAddressProcessor.replace(/\n/g, "~")
            : "";

          if (!buildProxies) {
            solve360Update = false;
          }
          roOptionValue = roOptionValue
            ? roOptionValue
            : this.SchedulerConstantService.DEFAULT_RO_OPTION;
          jobType = jobType
            ? jobType
            : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
          this.EventEmitterService.displayProgress.emit(
            this.SchedulerConstantService.EVENT_EMITTER.STEP_1
          );
          let scheduleDate = "";
          scheduleDate = moment().format(
            this.SchedulerConstantService.DATE_FORMAT
          );
          let startDate: string = moment(
            new Date(this.dateInput.start)
          ).format(this.SchedulerConstantService.DATE_FORMAT);
          let endDate: string = moment(new Date(this.dateInput.end)).format(
            this.SchedulerConstantService.DATE_FORMAT
          );
          let date = new Date();
          let dateArray: any = scheduleDate.split("-");
          date.setMonth(dateArray[0] * 1 - 1);
          date.setDate(dateArray[1] * 1);
          date.setFullYear(dateArray[2] * 1);
          let now_utc = Date.UTC(
            date.getUTCFullYear(),
            date.getUTCMonth(),
            date.getUTCDate(),
            date.getUTCHours(),
            date.getUTCMinutes(),
            date.getUTCSeconds()
          );
          const now_utcOP = new Date(now_utc);
          let now_utcOutPut = now_utcOP.toUTCString();
          // alert(solve360Update);
          if (
            glAccountCompanyID === undefined ||
            !glAccountCompanyID ||
            glAccountCompanyID == null
          ) {
            this.EventEmitterService.displayProgress.emit("");
            NProgress.done();
            const messageValidate =
              this.SchedulerConstantService.VALIDATION_MESSAGE_GL_COMPANY_ID;
            this.showStatusMessage(messageValidate, "failure");
            return false;
          }
          const scheduleObj: any = {
            jobSchedule: moment(now_utcOutPut).toISOString(),
            jobData: {
              groupName: this.createSchedule
                .get("storeGroup")
                ?.value[0].itemName.trim(),
              storeDataArray: {
                dealerId: dealerCode.trim(),
                //dealerId: '1813',// Static dealerId for Fortellis API testing purpose.
                projectId: projectId === undefined ? "" : projectId,
                secondProjectId:
                  secondProjectId === undefined ? "" : secondProjectId,
 		            projectType: projectType === undefined ? "" : projectType,
                secondaryProjectType:
                secondaryProjectType === undefined ? "" : secondaryProjectType,
                glAccountCompanyID: glAccountCompanyID === undefined ? "" : glAccountCompanyID,
                mageManufacturer: mageManufacturer,
                solve360Update: solve360Update,
                buildProxies: buildProxies,
                userName: userName,
                startDate: startDate,
                endDate: endDate,
                closedROOption: roOptionValue,
                jobType: jobType,
                mageGroupCode: mageGrpCode,
                mageStoreCode: mageStrCode,
                stateCode: steCode,
                dealerAddress: dealerAddress,
                projectIds: projectIds,
                testData:false,
                secondProjectIdList: secondProjectIdList,
                companyIds:companyIds,
                companyObj:JSON.stringify(companyObj),
                groupCode: groupCode,
                mageStoreName:mageStoreName,
                errors:errors,
                thirdPartyUsername: thirdPartyUsername,
                assignedtoCn:assignedtoCn,
                brands:brands   
              },
            },
          };

          let activityDataSchedule = {
            activityName: "BE call to save agenda objects",
            activityType: "Save Fortellis Jobs",
            activityDescription: `Fortellis Schedule object is: ${JSON.stringify(
              scheduleObj
            )})`,
          };
          this.commonService.saveActivity(
            "Manage Fortellis",
            activityDataSchedule
          );
          let self = this;
          this.apollo
            .use("manageFortellisSchedule")
            .mutate({
              mutation: createNewSchedule,
              variables: scheduleObj,
            }).pipe(takeUntil(this.subscription$)).subscribe({
              next: (listdata: any) => {
                const result: any = listdata.data;
                NProgress.done();
                const status = result.scheduleFortellisExtractJob.status;
                let message = "";
                message = result.scheduleFortellisExtractJob.message;
                if (status) {
                  this.solve360ServerDecider = false;
                  this.switchServer();
                  this.EventEmitterService.displayProgress.emit(
                    this.SchedulerConstantService.EVENT_EMITTER
                      .STEP_SAVE_SCHEDULE
                  );
                  this.refreshScheduleList();
                  setTimeout(() => {
                    self.EventEmitterService.displayProgress.emit("");
                    self.showStatusMessage(message, "success");
                  }, 3000);
                  this.createSchedule.reset();
                  this.createSchedule.patchValue({
                    updateRetreiveROinSolve360: false,
                    buildProxies: true,
                  });
                  this.storeFilterList = [];
                  // this.selectMultiDateRangeOption = !this.selectMultiDateRangeOption;
                  this.selectMultiDateRangeOption = true;
                  this.setDateRange(true, "");
                  // this.dateInput = {
                  //   start: this.startDateSelection(),
                  //   end: this.endDateSelection(),
                  // };
                  this.dateInput = {
                    start: this.selectedRange[0],
                    end: this.selectedRange[1],
                  };
                  this.createSchedule.patchValue({
                    roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
                    jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE,
                  });
                } else {
                  self.EventEmitterService.displayProgress.emit("");
                }
              },
              error: (err:Error) => {
                this.EventEmitterService.displayProgress.emit("");
                NProgress.done();
                const message =
                  this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, "failure");
                this.commonService.errorCallback(err, this);
              },
              complete: () => {
                console.log("Completed");
              },
            });
        } else {
          this.validateAllFormFields(this.createSchedule);
        }
      }
      return;
    });
  }
  saveTestSchedule() {
    const currentUserObj = JSON.parse(localStorage.getItem('currentUser')!);
    if (currentUserObj) {
       var userName = currentUserObj.userName ? currentUserObj.userName : '';
    }
    const testDealerId = this.createSchedule.get('testDealerId')?.value;
    const testGlId  = this.createSchedule.get('testGlId')?.value;
    const testStoreCode = this.createSchedule.get('testStoreCode')?.value;
    const testGroupCode = this.createSchedule.get('testGroupCode')?.value
    const dealerCode = testDealerId
    const mageGrpCode = testGroupCode
    const mageStrCode = testStoreCode;
    const steCode = 'NA';
    const glAccountCompanyID = testGlId;
    let solve360Update;
    let buildProxies;
    let dealerAddress;

    //Get projectId from local storage
    let projectId = "0"
    //Get second projectId from local storage
    let secondProjectId = "";
    // const storeTemp =this.createSchedule.get('store')?.value;
    // let tempProject = storeTemp.map(store => store.projectId);
    // let tempSecond = storeTemp.map(store => store.secondProjectId);
    // let companyIdList = storeTemp.map(store => store.companyID);
    let projectIds ='';
    let secondProjectIdList ='';
    
    // for(let i=0;i<tempProject.length;i++){
    //   projectIds+=tempProject[i]+'*';
    // }
    
    // for(let i=0;i<tempSecond.length;i++){
    //   if(tempSecond[i]!=undefined){
    //   secondProjectIdList+=tempSecond[i]+'*';
    //   }
    // }
   
    // let dealerIdList = storeTemp.map(store => store.thirdPartyUsername);
        
    // let allDealerIdSame =  this.areAllDealerIdSame(dealerIdList);
    
    // if(this.createSchedule.get('store')?.value[0].hasOwnProperty('secondProjectId')){
    //    secondProjectId =  this.createSchedule.get('store')?.value[0].secondProjectId;
    // }
    const parentID = '12345';
    const companyId: number = parseInt(parentID);
    console.log('company ID:',companyId);
    let companyObj: any = []; // Initialize with an empty array
    // for(let i=0;i<companyIdList.length;i++){
    //   this.getSecondProjectId(companyIdList[i],(result)=>{
    //    if(result){
    //      if (result[1]) {
    //        if (result[1].hasOwnProperty('project_id')) {
    //          secondProjectIdList+=result[1].project_id.toString()+'*';
    //        }
    //      }
    //      if (result[0]) {
    //       if (result[0].hasOwnProperty('project_id')) {
    //         projectIds+=result[0].project_id.toString()+'*';
    //       }
    //     }
    //    }
    //   })
    // }
    // console.log('Second project id list after callback',secondProjectIdList)



    const mageManufacturer = 'NA'; 
    console.log(mageManufacturer);

    // const dealerAddress = this.createSchedule.get('store')?.value[0].address;

    this.getSecondProjectId(companyId, (result: any) => {
      console.log('+++++++++++++++++++++++++++++++++++++++++++');
      console.log(result);
      console.log('+++++++++++++++++++++++++++++++++++++++++++');
      //Override projectId and secondProjectId from api
      // if(result){
      //   if (result[0]) {
      //     if (result[0].hasOwnProperty('project_id')) {
      //       projectId = result[0].project_id.toString();
      //     }
      //   }
      //   if (result[1]) {
      //     if (result[1].hasOwnProperty('project_id')) {
      //       secondProjectId = result[1].project_id.toString();
      //     }
      //   }
      // }
     
      // console.log('projectId:', projectId);
      // console.log('secondProjectId:', secondProjectId); 
    
  

      let s: string = moment(new Date(this.dateInput.start)).format(this.SchedulerConstantService.DATE_FORMAT);
      let e: string = moment(new Date(this.dateInput.end)).format(this.SchedulerConstantService.DATE_FORMAT);
      let dateRangeCompare = s + ' - ' + e
      let isExist = this.checkJobExistInExtractionQueue(testGroupCode, dealerCode, dateRangeCompare);
      if (isExist) {
        swal({
          title: this.SchedulerConstantService.JOB_STARTED,
          type: 'warning',
          confirmButtonClass: 'btn-warning pointer',
          confirmButtonText: this.constantService.CLOSE
        });
      } else {
        if (true) {
          // let activityData = {
          //   activityName: "Manage Fortellis",
          //   activityType: "Create Schedule",
          //   activityDescription: `Create New Schedule for Group ${this.createSchedule.get('storeGroup')?.value[0].itemName.trim()} (Group: ${mageGrpCode}, Store: ${mageStrCode})`
          // };
          // this.commonService.saveActivity('Manage Fortellis', activityData);
          let roOptionValue = this.createSchedule.get('roOption')?.value;
          let jobType = this.createSchedule.get('jobType')?.value;
          solve360Update = this.createSchedule.get('updateRetreiveROinSolve360')?.value;
          buildProxies = this.createSchedule.get('buildProxies')?.value;
          dealerAddress = this.dealerAddressProcessor ? this.dealerAddressProcessor.replace(/\n/g, "~") : '';;
          
          // if (!buildProxies) {
          //   solve360Update = false;
          // }
          roOptionValue = roOptionValue ? roOptionValue : this.SchedulerConstantService.DEFAULT_RO_OPTION;
          jobType = jobType ? jobType : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
          this.EventEmitterService.displayProgress.emit(this.SchedulerConstantService.EVENT_EMITTER.STEP_1);
          let scheduleDate = '';
          scheduleDate = moment().format(this.SchedulerConstantService.DATE_FORMAT);
          let startDate: string = moment(new Date(this.dateInput.start)).format(this.SchedulerConstantService.DATE_FORMAT);
          let endDate: string = moment(new Date(this.dateInput.end)).format(this.SchedulerConstantService.DATE_FORMAT);
          let date = new Date();
          let dateArray: any = scheduleDate.split("-");
          date.setMonth((dateArray[0] * 1) - 1);
          date.setDate(dateArray[1] * 1);
          date.setFullYear(dateArray[2] * 1)
          let now_utc = Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),
            date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());
          const now_utcOP = new Date(now_utc);
          let now_utcOutPut = now_utcOP.toUTCString();

          if(glAccountCompanyID === undefined || !glAccountCompanyID || glAccountCompanyID == null){
              this.EventEmitterService.displayProgress.emit('');
              NProgress.done();
              const messageValidate =  this.SchedulerConstantService.VALIDATION_MESSAGE_GL_COMPANY_ID;
              this.showStatusMessage(messageValidate, 'failure');
              return false;
          }

          if(testDealerId === undefined || !testDealerId || testDealerId == null){
            this.EventEmitterService.displayProgress.emit('');
            NProgress.done();
            const messageValidate = 'Please provide third party username for data pull ';
            this.showStatusMessage(messageValidate, 'failure');
            return false;
          }else{
              if (!this.hasNoSpecialCharacters(testDealerId)) {
                    console.log('The string contains special characters.');
                    this.EventEmitterService.displayProgress.emit('');
                       NProgress.done();
                     const messageValidate = 'Please provide third party username  With out Special Characters ';
                     this.showStatusMessage(messageValidate, 'failure');
                      return false;
               } 
        }

          if(testGroupCode === undefined || !testGroupCode || testGroupCode == null){
             this.EventEmitterService.displayProgress.emit('');
             NProgress.done();
             const messageValidate = 'Please provide Mage Group Code  for data pull ';
             this.showStatusMessage(messageValidate, 'failure');
            return false;
          }else{
             
            if (!this.hasNoSpecialCharacters(testGroupCode)) {
               console.log('Mage Group code  string contains special characters.');

              this.EventEmitterService.displayProgress.emit('');
              NProgress.done();
              const messageValidate = 'Please provide Mage Group Code  With out Special Characters ';
              this.showStatusMessage(messageValidate, 'failure');
              return false;
            } 
         }

      
      if(testStoreCode === undefined || !testStoreCode || testStoreCode == null){
        this.EventEmitterService.displayProgress.emit('');
        NProgress.done();
        const messageValidate = 'Please provide Mage Store Code  for data pull ';
        this.showStatusMessage(messageValidate, 'failure');
        return false;
      }else{
      if (!this.hasNoSpecialCharacters(testStoreCode)) {
      
      this.EventEmitterService.displayProgress.emit('');
      NProgress.done();
      const messageValidate = 'Please provide Mage Store Code  without special characters ';
      this.showStatusMessage(messageValidate, 'failure');
      return false;
    }
  }


          // alert(solve360Update);
          const scheduleObj: any = {
            jobSchedule: moment(now_utcOutPut).toISOString(),
            jobData: {
              groupName: testGroupCode,
              storeDataArray:
              {
                dealerId: dealerCode.trim(),
                //dealerId: '1813',// Static dealerId for Fortellis API testing purpose.
                projectId:(projectId === undefined) ? "" : projectId,
                secondProjectId: (secondProjectId === undefined) ? "" : secondProjectId,
                glAccountCompanyID: (glAccountCompanyID === undefined) ? "" : glAccountCompanyID,
                mageManufacturer: mageManufacturer,
                solve360Update:false,
                buildProxies:buildProxies,
                userName:userName,
                startDate: startDate,
                endDate: endDate,
                closedROOption: roOptionValue,
                jobType: jobType,
                mageGroupCode: mageGrpCode,
                mageStoreCode: mageStrCode,
                stateCode: steCode,
                dealerAddress: dealerAddress,
                projectIds:"0",
                secondProjectIdList:"0",
                testData:true,
                companyIds:'0',
                companyObj: JSON.stringify(companyObj),
                brands:"test*test"
              }
            }
          };

          let activityDataSchedule = {
            activityName: "BE call to save agenda objects",
            activityType: "Save Fortellis Jobs",
            activityDescription: `Fortellis Schedule object is: ${JSON.stringify(scheduleObj)})`,

          };
          this.commonService.saveActivity('Manage Fortellis', activityDataSchedule);
          let self = this;
          this.apollo.use('manageFortellisSchedule').mutate({
            mutation: createNewSchedule,
            variables: scheduleObj,
          }).subscribe(( listdata: any ) => {
            NProgress.done();
            const result: any = listdata.data;
            const status = result.scheduleFortellisExtractJob.status;
            let message = '';
            message = result.scheduleFortellisExtractJob.message;
            if (status) {
              this.solve360ServerDecider = false;
              this.switchServer();
              this.EventEmitterService.displayProgress.emit(this.SchedulerConstantService.EVENT_EMITTER.STEP_SAVE_SCHEDULE);
              this.refreshScheduleList();
              setTimeout(function () {
                self.EventEmitterService.displayProgress.emit('');
                self.showStatusMessage(message, 'success');
              }, 3000)
              this.createSchedule.reset();
              this.createSchedule.patchValue({
                updateRetreiveROinSolve360: false,
                buildProxies:true
              });
              this.storeFilterList = [];
              // this.selectMultiDateRangeOption = !this.selectMultiDateRangeOption;
              this.selectMultiDateRangeOption = true;
              this.isTestSchedule = false;
              this.setDateRange(true, '');
              // this.dateInput = {
              //   start: this.startDateSelection(),
              //   end: this.endDateSelection(),
              // };
              this.dateInput = {
                start: this.selectedRange[0],
                end: this.selectedRange[1],
              };
              this.createSchedule.patchValue({
                roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
                jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE
              });
            } else {
              self.EventEmitterService.displayProgress.emit('');
            }
          },
            (err: Error)=> {
              this.EventEmitterService.displayProgress.emit('');
              NProgress.done();
              const message = this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
              this.showStatusMessage(message, 'failure');
              this.commonService.errorCallback(err, this);
            }
          );
        } else {
          this.validateAllFormFields(this.createSchedule);
        }
      }
      return;
    });  
  }

  hasNoSpecialCharacters(inputString: string): boolean {
    const regex = /^[a-zA-Z0-9 ]*$/; 
    return regex.test(inputString);
  }


  areAllDealerIdSame(dealerIdList: any) {
    const referenceValue = dealerIdList[0];
    const result = dealerIdList.every(
      (element: any) => element === referenceValue
    );
    return result;
  }

  getSecondProjectId(companyId: any, cb: any) {
    this.commonService.getSecondProjectId(companyId, (result: any) => {
      if (cb) {
        cb(result);
      }
    });
  }

  validateAllFormFields(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach((field) => {
      const control = formGroup.get(field);
      if (control instanceof FormControl) {
        control.markAsTouched({ onlySelf: true });
      } else if (control instanceof FormGroup) {
        this.validateAllFormFields(control);
      }
    });
  }

  isFieldValidCreateSchedule(field: string) {
    let retValue: any = null;
    retValue =
      !this.createSchedule.get(field)?.valid &&
      this.createSchedule.get(field)?.touched;
    return retValue;
  }

  displayFieldCssCreateSchedule(field: string) {
    return {
      "has-danger": this.isFieldValidCreateSchedule(field),
    };
  }

  /**
   * flash message style set for success and error
   *
   */
  showStatusMessage(message: any, statusType: any) {
    if (statusType === "success") {
      this.toastrService.success (message);
    } else {
      this.toastrService.error(message);
    }
  }

  timeFormat(timeString: any) {
    var hourEnd = timeString.indexOf(":");
    var H = +timeString.substr(0, hourEnd);
    var h = H % 12 || 12;
    var ampm = H < 12 ? " AM" : " PM";
    timeString = h + timeString.substr(hourEnd, 3) + ampm;
    return timeString;
  }

  getAllJobs(callback: any) {
    this.compareObjArray = [];
    this.processQueueList = [];
    this.processQueueListCompleted = [];
    const allStoreGroupsList = this.apollo
      .use("manageFortellisSchedule")
      .query({
        query: getAllFortellisExtractJobs,
        fetchPolicy: "network-only",
      }).pipe(takeUntil(this.subscription$)).subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          let obj: any = {};
          this.processQueueList = [];
          this.processQueueListCompleted = [];
          this.compareObjArray = [];
          this.poolTime =
            result["data"]["getAllFortellisExtractJobs"]["poolTime"];
          $.each(
            result["data"]["getAllFortellisExtractJobs"]["jobArray"],
            (key: any, val: any) => {
              let groupName = val.data.groupName;
              let scheduledDate = val.nextRunAt
                ? val.nextRunAt
                : val.lastRunAt
                ? val.lastRunAt
                : null;
              let date = "";
              scheduledDate
                ? (date = moment
                    .unix(scheduledDate / 1000)
                    .format("MM-DD-YYYY HH:mm:ss"))
                : null;
              let nextRunAt = "";
              val.nextRunAt
                ? (nextRunAt = moment.unix(val.nextRunAt / 1000).fromNow())
                : null;
              let groupStatus: any = null;
              groupStatus = val;
              let failedReason = val.failReason;
              $.each(val.data["storeDataArray"], (key: any, val: any) => {
                obj = {};
                let jobStartDate = null;
                let jobEndDate = null;
                let status = null;
                let jobType = null;
                let solve360Update;
                let glAccountCompanyID;
                let buildProxies;
                let dealerAddress;
                let mageManufacturer;
                let seperator = "/";
                let projectIds;
                let secondProjectIdList;
		            let testData;
                let companyIds;
                let processFileName;
                if (val.startDate.includes("-")) {
                  seperator = "-";
                }
                status = val.status;
                const dateArray = val.startDate.split(seperator);
                const startDate =
                  dateArray[0] + "-" + dateArray[1] + "-" + dateArray[2];
                if (val.endDate.includes("-")) {
                  seperator = "-";
                }
                const dateArrayEnd = val.endDate.split(seperator);
                const ClosedROOption = val.closedROOption;
                const endtDate =
                  dateArrayEnd[0] +
                  "-" +
                  dateArrayEnd[1] +
                  "-" +
                  dateArrayEnd[2];
                let range = startDate + " - " + endtDate;
                let projectId;
                let secondProjectId;
                jobStartDate = val.startTime;
                jobEndDate = val.endTime;
                jobType = val.jobType;
                processFileName = val.processFileName;
                solve360Update = val.solve360Update;
                glAccountCompanyID = val.glAccountCompanyID;
                buildProxies = val.buildProxies;
                projectId = val.projectId;
                secondProjectId = val.secondProjectId;
                dealerAddress = val.dealerAddress;
                mageManufacturer = val.mageManufacturer;
                projectIds = val.projectIds;
                secondProjectIdList = val.secondProjectIdList;
		            testData = val.testData;
                companyIds = val.companyIds;

                let jobStatus = false;
                let statusFlag = null;
                if (jobStartDate && jobEndDate && status) {
                  jobStatus = true;
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.COMPLETED;
                } else if (jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.RUNNING;
                } else if (!jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.SCHEDULED;
                } else if (jobStartDate && jobEndDate && !status) {
                  jobStatus = true;
                  statusFlag = this.SchedulerConstantService.STATUS_FLAG.FAILED;
                  failedReason = val.message;
                } else {
                  statusFlag = this.getJobStatus(groupStatus);
                }
                obj = {
                  groupName: groupName,
                  store: val.dealerId,
                  range: range,
                  date: date,
                  status: statusFlag,
                  override: this.SchedulerConstantService.RUN_NOW,
                  failedReason: failedReason,
                  nextRunAt: nextRunAt,
                  jobStartDate: jobStartDate,
                  jobEndDate: jobEndDate,
                  ClosedROOption: ClosedROOption,
                  jobType: jobType,
                  storeName: val.mageStoreCode,
                  solve360Update: solve360Update,
                  buildProxies: buildProxies,
                  projectId: projectId,
                  secondProjectId: secondProjectId,
                  glAccountCompanyID: glAccountCompanyID,
                  dealerAddress: dealerAddress,
                  mageManufacturer: mageManufacturer,
                  projectIds: projectIds,
                  secondProjectIdList: secondProjectIdList,
                  testData: testData,
                  companyIds: companyIds,
                  processFileName: processFileName
                };
                if (
                  jobStatus &&
                  (statusFlag ===
                    this.SchedulerConstantService.STATUS_FLAG.COMPLETED ||
                    statusFlag ==
                      this.SchedulerConstantService.STATUS_FLAG.FAILED)
                ) {
                  this.processQueueListCompleted.push(obj);
                  this.compareObjArray.push(obj);
                } else {
                  this.processQueueList.push(obj);
                }
              });
            }
          );
          callback();
        },
        error: (err: Error) => {
          this.commonService.errorCallback(err, this);

        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  getDealerIdFromStoreName(sName: any) {
    let storeFilterListCopy: any[] = [];
    storeFilterListCopy = Object.assign([], this.storeGroupList);
    storeFilterListCopy = storeFilterListCopy
      .filter((item: any, index: number) => index < storeFilterListCopy.length)
      .filter((item: any, index: number) => item.companyName === sName);
    let dealerId = "";
    if (storeFilterListCopy.length) {
      dealerId = storeFilterListCopy[0].thirdPartyUsername;
    }
    return dealerId;
  }

  getStoreNameFromDealerId(dealerID: any) {
    let storeFilterListCopy: any[] = [];
    storeFilterListCopy = Object.assign([], this.jobGroupList);
    storeFilterListCopy = storeFilterListCopy
      .filter((item: any, index: number) => index < storeFilterListCopy.length)
      .filter(
        (item: any, index: number) => item.thirdPartyUsername === dealerID
      );
    let dealerId = "";
    if (storeFilterListCopy.length) {
      dealerId = storeFilterListCopy[0].companyName;
    }
    return dealerId;
  }
  /**
   * showSouceBundleDetails function will show the Scheduled Process
   *
   */
  showScheduledProcessList(loaderStatus: any) {
    loaderStatus ? (this.scheduleProcessQueueLoading = true) : "";
    if ($("#scheduleProcessQueueFortellis").data('datatable')) {
	$("#scheduleProcessQueueFortellis").dataTable().fnDestroy();
    }
    table1 = $("#scheduleProcessQueueFortellis").dataTable().fnClearTable();
    const elm = this;
    let i = 0;
    setTimeout(() => {
      $(document).ready(() => {
        table1 = $("#scheduleProcessQueueFortellis").dataTable({
          language: {
            decimal: ".",
            thousands: ",",
          },
          columnDefs: [
            { type: "numeric-comma", targets: "_all" },
            { orderable: false, targets: [5] },
            { orderable: true, targets: [0, 1, 2, 3] },
          ],
          fixedHeader: {
            header: true,
            footer: true,
            headerOffset: $(".cat__top-bar").outerHeight() - 11,
          },
          bSort: false,
          order: [0, "asc"],
          responsive: true,
          scrollX: false,
          scrollY: "200px",
          destroy: true,
          paging: true,
          deferRender: true,
          ordering: true,
          info: true,
          filter: true,
          length: true,
          processing: true,
          lengthMenu: [
            [50, 25, 10, 5],
            [50, 25, 10, 5],
          ],
          autoWidth: false,
          fnRowCallback: function (settings: any, aData: any) {
            const pagination = $(this)
              .closest(".dataTables_wrapper")
              .find(".dataTables_paginate");
            pagination.toggle(this.api().page.info().pages > 1);
          },
          drawCallback: function (settings: any) {
            table1 = $("#scheduleProcessQueueFortellis").DataTable();
            $("td:eq(1)", settings).css("width", "24%");
            $("td:eq(1)", settings).css("width", "10%");
            $("td:eq(2)", settings).css("width", "13%");
            $("td:eq(3)", settings).css("width", "23%");
            $("td:eq(4)", settings).css("width", "15%");
            $("td:eq(5)", settings).css("width", "15%");
            var api = this.api();
            var rows = api.rows({ page: "current" }).nodes();
            var last: any = null;
            api
              .column(0, { page: "current" })
              .data()
              .each(function (group: any, i: any) {
                if (last !== group) {
                  $(rows)
                    .eq(i)
                    .before(
                      '<tr class="group"><td colspan="8" style="BACKGROUND-COLOR:rgb(86, 85, 78);font-weight:700;color:#f5f5f5;">' +
                        "Store Group: " +
                        group +
                        "</td></tr>"
                    );
                  last = group;
                }
              });
          },
          columns: [
            {
              title: "Store",
              width: "24%",
              className: "dt-head-left",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let dealerId = elm.getDealerIdFromStoreName(rowData[1]);
                let storeName = elm.getStoreNameFromDealerId(rowData[1]);
                storeName = storeName ? storeName : rowData[1];
                let toolTipForDealerId = "";
                let toolTipForJobType = "";
                if (dealerId) {
                  toolTipForDealerId = "DealerId: " + dealerId;
                }
                if (rowData[11]) {
                  toolTipForJobType = "Job Type: " + rowData[11];
                }
                let cursorStyle = toolTipForDealerId ? 'cursor:pointer;' : '';
                let tooltipAttributes = toolTipForDealerId 
                  ? `data-toggle="tooltip" data-placement="top" title="${toolTipForDealerId}" data-animation="false"`
                  : '';
                data = 
                  `<span style="${cursorStyle}" ${tooltipAttributes}>
                    ${storeName}
                  </span>`;
                return data !== null ? data : null;
              },
            },
            {
              title: "Extraction Type",
              width: "13%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let toolTipForJobType = "";
                console.log("rowdata!!!!!!!!!!!!!!!!!",rowData[11]);
                if(rowData[11]){
                  rowData[11] = rowData[11] ? rowData[11].charAt(0).toUpperCase() + rowData[11].slice(1) : "";
                }
               
                if (rowData[11]) {
                  toolTipForJobType = rowData[11];
                }
                data =
                  '<span  data-placement="top" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  toolTipForJobType +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Data Extraction Range",
              width: "13%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                data =
                  '<span data-placement="top" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  rowData[2] +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Schedule Date",
              width: "23%",
              type: "formatted-num",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let toolTipTitle = "";
                if (rowData[4] == "Locked") {
                  toolTipTitle = elm.SchedulerConstantService.LOCKED_MESSAGE;
                }
                if (rowData[4] == "Running") {
                  toolTipTitle = rowData[4];
                }
                let b = rowData[3].split(" ");
                let r = b[0].split("-");
                let op = r[2] + "-" + r[0] + "-" + r[1] + " " + b[1];
                let scheduleDateDisplay = moment(op).format(
                  elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                );
                let scheduleDateOp = scheduleDateDisplay
                  ? scheduleDateDisplay
                  : rowData[3];
                data =
                  '<span  data-placement="top" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  scheduleDateOp +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Status",
              width: "15%",
              type: "formatted-alphabet",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                var className = "";
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.COMPLETED
                  ? (className = "label-success")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.SCHEDULED
                  ? (className = "label-scheduled")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.RUNNING
                  ? (className = "label-running")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.REPEATING
                  ? (className = "label-repeating")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.FAILED
                  ? (className = "label-failed")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.QUEUED
                  ? (className = "label-queued")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.LOCKED
                  ? (className = "label-locked")
                  : null;
                if (rowData[4] === "Rescheduled") {
                  data =
                    '<span style="float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="label label-scheduled">Scheduled</span>';
                  data +=
                    '<span aria-hidden="true" data-toggle="tooltip" data-placement="top" title="' +
                    rowData[6] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '" style="margin-left:5px;" class="label label-failed">Failed</span>';
                } else {
                  data =
                    '<span style="float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="label ' +
                    className +
                    '">' +
                    rowData[4] +
                    "</span>";
                }
                return data;
              },
            },
            {
              title: "Actions",
              width: "15%",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                const d = data;
                let rowData = [];
                rowData = Object.assign([], rows);
                const cancelSchedule = "Cancel Schedule";
                const overrideSchedule = "Run Now";
                if (
                  rowData[4] === "Completed" ||
                  rowData[4] === "Locked" ||
                  //rowData[4] === "Running" ||
                  rowData[4] === "Queued"
                ) {
                  data =
                    '<a style="font-size: 18px;" class="overrideScheduleCancel" style="color: #b9b5b5;cursor: not-allowed;" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-play-circle overrideScheduleCancel"  data-toggle="tooltip" data-placement="top" title="' +
                    rowData[4] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  style="color: #b9b5b5;cursor: not-allowed;"></i></a>';
                  data +=
                    '<a style="margin: 18px;font-size: 18px;" class="scheduleCancel" style="color: #b9b5b5;cursor: not-allowed;" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-ban scheduleCancel"  data-toggle="tooltip" data-placement="top" title="' +
                    rowData[4] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '" style="color: #b9b5b5;cursor: not-allowed;" ></i></a>';
                }else if(rowData[4] === "Running"){
                  data = `
                   <a style="margin: 18px; font-size: 18px;" class="cancelSchedule" href="javascript:void(0);" data-note="${d}">
                     </a>
                    `;                 
                } else {
                  data =
                    '<a style="margin: 18px;font-size: 18px;" class="cancelSchedule" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-ban cancelSchedule"  data-toggle="tooltip" data-placement="top" title="' +
                    cancelSchedule +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  ></i></a>';
                }
                return data;
              },
            },
          ],
          rowGroup: {
            dataSrc: "Store Group",
          },
        });
        // tslint:disable-next-line:no-unused-expression
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
          "alphanumeric-sort-asc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "alphanumeric-sort-desc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? 1 : x > y ? -1 : 0;
          },
          "formatted-num-pre": function (a: any) {
            a = a === "-" || a === "" ? 0 : a.replace(/[^\d\-\.]/g, "");
            return parseFloat(a);
          },
          "formatted-num-asc": function (a: any, b: any) {
            return a - b;
          },
          "formatted-num-desc": function (a: any, b: any) {
            return b - a;
          },
          "formatted-alphabet-asc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "formatted-alphabet-desc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? 1 : x > y ? -1 : 0;
          },
        });
        elm.reDrawScheduleTable(elm.processQueueList, loaderStatus);
      });
    }, 200);
  }

  /**
   * reDrawPortalStoreTable function will redraw the datatable using new table values
   *
   */
  reDrawScheduleTable(temp: any, loaderStatus: any) {
    table1 = $("#scheduleProcessQueueFortellis").DataTable();
    table1.search("").draw();
    table1 = $("#scheduleProcessQueueFortellis").dataTable();
    table1.fnClearTable();
    table1 = $("#scheduleProcessQueueFortellis").dataTable();
    const tempArr: any[] = [];
    for (let i = 0; i < temp.length; i++) {
      let rpt: any[] = [];
      const t = temp[i];
      rpt = [
        t.groupName,
        t.store,
        t.range,
        t.date,
        t.status,
        t.override,
        t.failedReason,
        t.nextRunAt,
        t.ClosedROOption,
        t.jobStartDate,
        t.jobEndDate,
        t.jobType,
        t.projectId,
        t.solve360Update,
        t.buildProxies,
        t.includeMetaData,
        t.secondProjectId,
        t.dealerAddress,
        t.mageManufacturer,
        t.projectIds,
        t.secondProjectIdList,
        t.testData,
        t.companyIds,
        t.storeName,
 t.processFileName
      ];
      tempArr.push(rpt);
    }
    if (tempArr.length > 0) {
      table1.fnAddData(tempArr, false); // Add new data
    }
    table1.fnDraw(); // Redraw the DataTable
    loaderStatus ? (this.scheduleProcessQueueLoading = false) : "";
    if (temp.length > 0) {
      setTimeout(() => {
        $("#scheduleProcessQueueFortellis")
          .DataTable()
          .columns.adjust()
          .draw(false);
      }, 100);
    }
  }

  getToolTipInfoCompletedJobs(rowData: any) {
    let toolTip = "";
    let className = "";
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.COMPLETED
      ? (className = "label-success")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.SCHEDULED
      ? (className = "label-scheduled")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.RUNNING
      ? (className = "label-running")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.REPEATING
      ? (className = "label-repeating")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.FAILED
      ? (className = "label-failed")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.QUEUED
      ? (className = "label-queued")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.LOCKED
      ? (className = "label-locked")
      : null;
    status = '<span class="label ' + className + '">' + rowData[4] + "</span>";
    if (rowData[3]) {
      let b = rowData[3].split(" ");
      let r = b[0].split("-");
      let op = r[2] + "-" + r[0] + "-" + r[1] + " " + b[1];
      const time = moment.duration(
        `00:${this.poolTime ? this.poolTime : "00"}:00`
      );
      let scheduleDateDisplay = moment(op)
        .subtract(time)
        .format(this.SchedulerConstantService.SCHEDULE_DATE_FORMAT);
      let scheduleDateOp = scheduleDateDisplay
        ? scheduleDateDisplay
        : rowData[3];
      toolTip += "Scheduled Date: " + scheduleDateOp + " \n";
    }
    toolTip += "Last Run At: " + rowData[3] + " \n";
    if (rowData[6]) {
      toolTip +=
        "Extraction Start Time: " +
        moment(rowData[6] * 1).format(
          this.SchedulerConstantService.SCHEDULE_DATE_FORMAT
        ) +
        " \n";
    }
    if (rowData[7]) {
      toolTip +=
        "Extraction End Time: " +
        moment(rowData[7] * 1).format(
          this.SchedulerConstantService.SCHEDULE_DATE_FORMAT
        ) +
        " \n";
    }
    toolTip += "Status: " + status + " \n";
    if (rowData[5] && rowData[4] === "Failed") {
      toolTip += "Failed Reason: " + rowData[5] + " \n";
    }
    return toolTip;
  }

  /**
   * showScheduledProcessListCompleted function will show the Scheduled Process
   *
   */
  showScheduledProcessListCompleted(loaderStatus: any) {
    loaderStatus ? (this.scheduleProcessQueueCompletedLoading = true) : "";
    if ($("#scheduleProcessCompleted").data('datatable')) {
      $("#scheduleProcessCompleted").dataTable().fnDestroy();
    }
    table1 = $("#scheduleProcessCompleted").dataTable().fnClearTable();
    const elm = this;
    let i = 0;
    setTimeout(() => {
      $(document).ready(() => {
        table1 = $("#scheduleProcessCompleted").dataTable({
          language: {
            decimal: ".",
            thousands: ",",
          },
          columnDefs: [{ type: "numeric-comma", targets: "_all" }],
          fixedHeader: {
            header: true,
            footer: true,
            headerOffset: $(".cat__top-bar").outerHeight() - 11,
          },
          bSort: false,
          order: [4, "desc"],
          responsive: true,
          scrollX: false,
          destroy: true,
          paging: true,
          deferRender: true,
          ordering: true,
          info: true,
          filter: true,
          length: true,
          processing: true,
          lengthMenu: [
            [50, 25, 10, 5],
            [50, 25, 10, 5],
          ],
          autoWidth: false,
          scrollY: "200px",
          // initialization params as usual
          fnRowCallback: function (settings: any, aData: any) {
            const pagination = $(this)
              .closest(".dataTables_wrapper")
              .find(".dataTables_paginate");
            pagination.toggle(this.api().page.info().pages > 1);
          },
          drawCallback: function (settings: any) {
            table1 = $("#scheduleProcessQueueFortellis").DataTable();
            $("td:eq(1)", settings).css("width", "28%");
            $("td:eq(2)", settings).css("width", "17%");
            $("td:eq(3)", settings).css("width", "25%");
            $("td:eq(4)", settings).css("width", "20%");
            $("td:eq(5)", settings).css("width", "10%");
            var api1 = this.api();
            var rows1 = api1.rows({ page: "current" }).nodes();
            var last: any = null;

            api1
              .column(0, { page: "current" })
              .data()
              .each(function (group: any, i: any) {
                if (last !== group) {
                  $(rows1)
                    .eq(i)
                    .before(
                      '<tr class="group"><td colspan="8" style="BACKGROUND-COLOR:rgb(86, 85, 78);font-weight:700;color:#f5f5f5;">' +
                        "Store Group: " +
                        group +
                        "</td></tr>"
                    );
                  last = group;
                }
              });
          },
          columns: [
            {
              title: "Store",
              width: "28%",
              className: "dt-head-left",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let storeName = elm.getStoreNameFromDealerId(rowData[1]);
                storeName = rowData[9] ? rowData[9] : rowData[1];
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[1] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  storeName +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Extraction Type",
              width: "17%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let toolTipForJobType = "";
                rowData[8] = rowData[8] ? rowData[8].charAt(0).toUpperCase() + rowData[8].slice(1) : "";
                if (rowData[8]) {
                  toolTipForJobType = rowData[8];
                }
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[8] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  toolTipForJobType +
                  "</span>";
                return toolTipForJobType !== "" ? data : null;
              },
            },
            {
              title: "Data Extraction Range",
              width: "25%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[2] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  rowData[2] +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Completed Date",
              width: "20%",
              type: "formatted-date",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);

                let completedDate = "";
                let tempDate = new Date(rowData[3]);
                rowData[6] = tempDate.getTime()
                let tempDate1 = new Date(rowData[7]);
                rowData[7] = tempDate1.getTime();

                if (rowData[3]) {
                  let time = rowData[3].split(" ");
                  let HourFormat = elm.convertTimeFormat(time[1]);
                  completedDate = time[0] + " " + HourFormat;
                }
                if (rowData[9]) {
                  completedDate = moment(rowData[7] * 1).format(
                    elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                  );
                }
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  completedDate +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  completedDate +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Status",
              width: "10%",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                var className = "";
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.COMPLETED
                  ? (className = "label-success")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.SCHEDULED
                  ? (className = "label-scheduled")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.RUNNING
                  ? (className = "label-running")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.REPEATING
                  ? (className = "label-repeating")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.FAILED
                  ? (className = "label-failed")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.QUEUED
                  ? (className = "label-queued")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.LOCKED
                  ? (className = "label-locked")
                  : null;
                let toolTip = "";
                if (rowData[3]) {
                  let b = rowData[3].split(" ");
                  let r = b[0].split("-");
                  let op = r[2] + "-" + r[0] + "-" + r[1] + " " + b[1];
                  const time = moment.duration(
                    `00:${elm.poolTime ? elm.poolTime : "00"}:00`
                  );
                  let scheduleDateDisplay = moment(op)
                    .subtract(time)
                    .format(elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT);
                  let scheduleDateOp = scheduleDateDisplay
                    ? scheduleDateDisplay
                    : rowData[3];
                  toolTip += "Scheduled Date: " + scheduleDateOp + " \n";
                }
                toolTip += "Last Run At: " + rowData[3] + " \n";
                if (rowData[6]) {
                  toolTip +=
                    "Extraction Start Time: " +
                    moment(rowData[6] * 1).format(
                      elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                    ) +
                    " \n";
                }
                if (rowData[7]) {
                  toolTip +=
                    "Extraction End Time: " +
                    moment(rowData[7] * 1).format(
                      elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                    ) +
                    " \n";
                }
                toolTip += "Status: " + rowData[4] + " \n";
                if (rowData[5] && rowData[4] === "Failed") {
                  toolTip +=
                    "Failed Reason: " + rowData[5]
                      ? rowData[5].replace(/[^a-zA-Z ]/g, " ")
                      : "" + " \n";
                }
                rowData[5] = rowData[5]
                  ? rowData[5].replace(/[^a-zA-Z ]/g, " ")
                  : "";
                if (
                  rowData[4] === "Failed" &&
                  rowData[5] === elm.SchedulerConstantService.EXCEED_TIME
                ) {
                  rowData[4] = "Completed";
                  className = "label-success";
                }
                data =
                  '<span style="cursor:pointer;float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="popUpInfoCompletedJobs label ' +
                  className +
                  '"  data-toggle="tooltip" data-placement="top" title="' +
                  toolTip +
                  '" data-info="' +
                  rowData +
                  '">' +
                  rowData[4] +
                  "</span>";
                if (rowData[5]) {
                  data =
                    '<span style="cursor:pointer;float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="popUpInfoCompletedJobs label ' +
                    className +
                    '" data-toggle="tooltip" data-placement="top" title="' +
                    toolTip +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '">' +
                    rowData[4] +
                    "</span>";
                }

                return data;
              },
            },
            {
              title: "",
              width: "5%",
              className: "dt-head-left pl-0",
              render: function (data: any, type: any, rows: any, meta: any) {
                const d = data;
                let rowData = [];
                rowData = Object.assign([], rows);                
                console.log("Rowdata in Completed Extractions",rowData);
                if (
                  rowData[4] ===
                    elm.SchedulerConstantService.STATUS_FLAG.COMPLETED) {                 
                    let updateParentGroup = 'Re-process Store';                
                    data = '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                        + '<i aria-hidden="true" class="fa fa-upload text-primary mt-2 requeue" style="font-size: 18px; margin-right: 10px;" data-toggle="tooltip" data-placement="top" title="'
                        + updateParentGroup + '" data-animation="false" data-info="' + rowData + '" ></i></a>';                 
                } else {
                  data = "";
                }
                return data;
              },
            },
          ],
        });
        // tslint:disable-next-line:no-unused-expression
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
          "alphanumeric-sort-asc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "alphanumeric-sort-desc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? 1 : x > y ? -1 : 0;
          },
          "formatted-date-asc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a ? new Date(a).getTime() : 0;
            const y = b ? new Date(b).getTime() : 0;
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "formatted-date-desc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a ? new Date(a.toString()).getTime() : 0;
            const y = b ? new Date(b.toString()).getTime() : 0;
            return x < y ? 1 : x > y ? -1 : 0;
          },
        });
        elm.reDrawScheduleTableCompleted(
          elm.processQueueListCompleted,
          loaderStatus
        );
      });
    }, 200);
  }

  /**
   * reDrawScheduleTableCompleted function will redraw the datatable using new table values
   *
   */
  reDrawScheduleTableCompleted(temp: any, loaderStatus: any) {
    table1 = $("#scheduleProcessCompleted").DataTable();
    table1.search("").draw();
    table1 = $("#scheduleProcessCompleted").dataTable();
    table1.fnClearTable();
    table1 = $("#scheduleProcessCompleted").dataTable();

    const tempArr: any[] = [];
    for (let i = 0; i < temp.length; i++) {
      const t = temp[i];
      const rpt = [
        t.groupName,
        t.store,
        t.range,
        t.date,
        t.status,
        t.failedReason,
        t.jobStartDate,
        t.jobEndDate,
        t.jobType,
        t.storeName,
t.processFileName
      ];
      tempArr.push(rpt);
    }
    if (tempArr.length > 0) {
      table1.fnAddData(tempArr, false); // Add new data
    }
    table1.fnDraw(); // Redraw the DataTable
    loaderStatus ? (this.scheduleProcessQueueCompletedLoading = false) : "";
    if (temp.length > 0) {
      setTimeout(() => {
        $("#scheduleProcessCompleted").DataTable().columns.adjust().draw(false);
      }, 100);
    }
  }

  refreshScheduleList() {
    this.closeToolTip();
    this.loadingSchedule = true;
    this.getAllJobs(() => {
      this.showScheduledProcessList(true);
      this.loadingSchedule = false;
    });
  }

  refreshScheduleListCompleted() {
    this.closeToolTip();
    this.loadingScheduleCompleted = true;
    this.getAllJobs(() => {
      this.showScheduledProcessListCompleted(true);
      this.loadingScheduleCompleted = false;
    });
  }

  refreshProcessJsonList() {
    this.closeToolTip();
    this.loadingProcessJson = true;
    this.getAllProcessJsonJobs(() => {
      this.showProcessJsonList();
      this.loadingProcessJson = false;
    });
  }

  runNowSchedule(data: any) {
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      var userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
    }
    let rowData = data.split(",");
    console.log("rowdata>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", rowData);

    if(!rowData[21] || rowData[21] == "false"){
      const thridPartyUserName = rowData[1].toString();
      let filteredData: any = Object.assign([], this.jobGroupList);
      let resObj = filteredData.filter(
        (item: any) => item.thirdPartyUsername === thridPartyUserName
      );
      let mageGrpCode = "G1";
      let mageStrCode = "S1";
      let steCode = "St";
      let projectId = "";
      let secondProjectId = "";
      let dealerAddress: any;
      let mageManufacturer: any;
      let projectIds: any;
      let secondProjectIdList: any;
      let companyIds:any;
      let testData : any;
      if (resObj.length) {
        mageGrpCode = resObj[0].mageGroupCode;
        mageStrCode = resObj[0].mageStoreCode;
        steCode = resObj[0].state;
        projectId = resObj[0].projectId;
      }

      mageGrpCode = rowData[0];
      mageStrCode = rowData[22];
      steCode = rowData[23];
      projectId = rowData[13];

      
      var date = moment(rowData[3]);
      var now = moment();
      if (now > date) {
        swal({
          title: this.SchedulerConstantService.JOB_STARTED,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
        this.refreshScheduleList();
      } else {
        swal(
          {
            title: this.constantService.AREYOUSURE,
            text: this.SchedulerConstantService.RUN_SCHEDULE_NOW,
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default pointer",
            confirmButtonClass: "btn-warning pointer",
            confirmButtonText: "Run",
            closeOnConfirm: true,
            showLoaderOnConfirm: true,
          },
          () => {
            this.EventEmitterService.displayProgress.emit(
              this.SchedulerConstantService.EVENT_EMITTER.STEP1
            );
            let rowData = data.split(",");
            const groupName = rowData[0];
            let activityData = {
              activityName: "Manage Fortellis",
              activityType: "Run Schedule",
              activityDescription: `Run Fortellis Extract Job for Group ${groupName} (Group: ${mageGrpCode}, Store: ${mageStrCode})`,
            };
            this.commonService.saveActivity("Manage Fortellis", activityData);
            const dealerId = rowData[1].toString();
            // let jobSchedule: any = moment(
            //   rowData[3],
            //   this.SchedulerConstantService.DATE_FORMAT
            // ).format();
            let jobSchedule: any = moment(
              rowData[3],
            ).
            format('MM-DD-YYYY HH:mm:ss');
            let scheduleOpSplit = jobSchedule.split(" ");
            let scheduleOp = scheduleOpSplit[0].split("-");
            jobSchedule =
              scheduleOp[2] +
              "-" +
              scheduleOp[0] +
              "-" +
              scheduleOp[1] +
              " " +
              scheduleOpSplit[1];
            const dateRange = rowData[2].split(" - ");
            const startDate = dateRange[0].trim();
            const endDate = dateRange[1].trim();
            let date = new Date(jobSchedule);
            const closedRoOption = rowData[8];
            let jobType = rowData[11]
              ? rowData[11]
              : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
            let solve360Update = rowData[13].toLowerCase() == "true";
            let buildProxies = rowData[14].toLowerCase() == "true";

            if (rowData[12]) {
              projectId = rowData[12].toString();
            }
            if (rowData[16]) {
              secondProjectId = rowData[16].toString();
            }

            if (rowData[17]) {
              dealerAddress = rowData[17].toString();
            }

            if (rowData[18]) {
              mageManufacturer = rowData[18].toString();
            }

            if (rowData[19]) {
              projectIds = rowData[19].toString();
            }

            if (rowData[20]) {
              secondProjectIdList = rowData[20].toString();
            }

            if (rowData[22]) {
              companyIds = rowData[22].toString();
            }

            if(rowData[21]){
              testData = Boolean(rowData[21]);
            }
        

            console.log("projectId:", projectId);
            console.log("secondProjectId:", secondProjectId);
            console.log("dealerAddress:", dealerAddress);
            console.log("mageManufacturer:", mageManufacturer);

            let now_utc = Date.UTC(
              date.getUTCFullYear(),
              date.getUTCMonth(),
              date.getUTCDate(),
              date.getUTCHours(),
              date.getUTCMinutes(),
              date.getUTCSeconds()
            );
            const now_utcOP = new Date(now_utc);
            const scheduleObj: any = {
              jobSchedule: moment(now_utcOP).toISOString(),
              jobData: {
                groupName: groupName.trim(),
                storeData: {
                  dealerId: dealerId.trim(),
                  projectId: projectId,
                  secondProjectId: secondProjectId,
                  mageManufacturer: mageManufacturer,
                  solve360Update: solve360Update,
                  buildProxies: buildProxies,
                  userName: userName,
                  startDate: startDate,
                  endDate: endDate,
                  closedROOption: closedRoOption,
                  jobType: jobType,
                  mageGroupCode: mageGrpCode,
                  mageStoreCode: mageStrCode,
                  stateCode: steCode,
                  dealerAddress: dealerAddress,
                  projectIds: projectIds,
                  secondProjectIdList: secondProjectIdList,
                  testData:false,
                  companyIds:companyIds
                },
              },
            };
            let self = this;
            this.apollo
              .use("manageFortellisSchedule")
              .mutate({
                mutation: runNowFortellisExtractJobByStore,
                variables: scheduleObj,
              }).pipe(takeUntil(this.subscription$)).subscribe({
                next: (listdata: any) => {
                  const result: any = listdata.data;
                  NProgress.done();
                  const status = result.runNowFortellisExtractJobByStore.status;
                  const message = result.runNowFortellisExtractJobByStore.message;
                  if (status) {
                    this.EventEmitterService.displayProgress.emit(
                      this.SchedulerConstantService.EVENT_EMITTER.STEP_RELOAD_LIST
                    );
                    this.refreshScheduleList();
                    setTimeout(() => {
                      self.refreshScheduleList();
                      self.EventEmitterService.displayProgress.emit("");
                      self.showStatusMessage(message, "success");
                    }, 3000);
                  } else {
                    self.EventEmitterService.displayProgress.emit("");
                  }
                },
                error: (err: Error) => {
                  this.EventEmitterService.displayProgress.emit("");
                  NProgress.done();
                  const message =
                    this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                  this.showStatusMessage(message, "failure");
                  this.commonService.errorCallback(err, this);
                },
                complete: () => {
                  console.log("Completed");
                },
              });
          }
        );
      }
    }else{
          
      const thridPartyUserName = rowData[1].toString();
      // let filteredData: any = Object.assign([], this.jobGroupList);
      // let resObj = filteredData.filter((item) => item.thirdPartyUsername === thridPartyUserName)
      let mageGrpCode = "G1";
      let mageStrCode = "S1";
      let steCode = "St";
      let projectId = "";
      let secondProjectId = "";
      let dealerAddress: any;
      let mageManufacturer: any;
      let projectIds: any;
      let secondProjectIdList: any;
      let testData: any;
      let companyIds: any;
      let futureDate: any;


      // if (resObj.length) {
      //   mageGrpCode = resObj[0].mageGroupCode;
      //   mageStrCode = resObj[0].mageStoreCode;
      //   steCode = resObj[0].state;
      //   projectId = resObj[0].projectId;
      // }
      mageGrpCode = rowData[0];
      mageStrCode = rowData[22];
      steCode = rowData[23];
      projectId = rowData[13];

      var date = moment(rowData[3])
      var now = moment();
      if (now > date) {
        swal({
          title: this.SchedulerConstantService.JOB_STARTED,
          type: 'warning',
          confirmButtonClass: 'btn-warning pointer',
          confirmButtonText: this.constantService.CLOSE
        });
        this.refreshScheduleList();
      } else {
        swal({
          title: this.constantService.AREYOUSURE,
          text: this.SchedulerConstantService.RUN_SCHEDULE_NOW,
          type: 'warning',
          showCancelButton: true,
          cancelButtonClass: 'btn-default pointer',
          confirmButtonClass: 'btn-warning pointer',
          confirmButtonText: 'Run',
          closeOnConfirm: true,
          showLoaderOnConfirm: true
        },
        () => {
            this.EventEmitterService.displayProgress.emit(this.SchedulerConstantService.EVENT_EMITTER.STEP1);
            let rowData = data.split(",");
            const groupName = rowData[0];
            let activityData = {
              activityName: "Manage Fortellis",
              activityType: "Run Schedule",
              activityDescription: `Run Fortellis Extract Job for Group ${groupName} (Group: ${mageGrpCode}, Store: ${mageStrCode})`

            };
            this.commonService.saveActivity('Manage Fortellis', activityData);
            const dealerId = rowData[1].toString();
            let jobSchedule: any = moment(rowData[3]).format('MM-DD-YYYY HH:mm:ss');
            let scheduleOpSplit = jobSchedule.split(" ");
            let scheduleOp = scheduleOpSplit[0].split("-");
            jobSchedule = scheduleOp[2] + '-' + scheduleOp[0] + '-' + scheduleOp[1] + ' ' + scheduleOpSplit[1];
            const dateRange = rowData[2].split(" - ");
            const startDate = dateRange[0].trim();
            const endDate = dateRange[1].trim();
            let date = new Date(jobSchedule);
            const closedRoOption = rowData[8];
            let jobType = rowData[11] ? rowData[11] : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
            let solve360Update = rowData[13].toLowerCase() == 'true';
            let buildProxies = rowData[14].toLowerCase() == 'true';
          
            if(rowData[12]){
              projectId = rowData[12].toString();
            }
            if(rowData[16]){
              secondProjectId = rowData[16].toString();
            }

            if(rowData[17]){
              dealerAddress = rowData[17].toString();
            }

            if(rowData[18]){
              mageManufacturer = rowData[18].toString();
            }

            if(rowData[19]){
              projectIds = rowData[19].toString();
            }
        
            if(rowData[20]){
              secondProjectIdList = rowData[20].toString();
            }

              
            if(rowData[21]){
              testData = rowData[21].toString();
            }


            if(rowData[22]){
              companyIds = rowData[22];
              } 
            
            
            if(rowData[24]){
              futureDate = rowData[24];
            } 
            

            console.log('projectId:', projectId);
            console.log('secondProjectId:', secondProjectId);
            console.log('dealerAddress:', dealerAddress);
            console.log('mageManufacturer:', mageManufacturer);

            let now_utc = Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),
            date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());
            const now_utcOP = new Date(now_utc);
            const scheduleObj: any = {
              jobSchedule: moment(now_utcOP).toISOString(),
              jobData: {
                groupName: groupName.trim(),
                storeData:
                {
                  dealerId: dealerId.trim(),
                  projectId: '0',
                  secondProjectId: '0',
                  mageManufacturer: 'mageManufacturer',
                  solve360Update: false,
                  buildProxies:buildProxies,
                  userName: userName,
                  startDate: startDate,
                  endDate: endDate,
                  closedROOption: closedRoOption,
                  jobType: jobType,
                  mageGroupCode: mageGrpCode,
                  mageStoreCode: mageStrCode,
                  stateCode: steCode,
                  dealerAddress: dealerAddress,
                  projectIds:'0',
                  secondProjectIdList:'0',
                  testData:true,
                  companyIds:'0',
                  //futureDate:futureDate
                }
              }
            };
            let self = this;
            this.apollo.use('manageFortellisSchedule').mutate({
              mutation: runNowFortellisExtractJobByStore,
              variables: scheduleObj,
            }).subscribe(( data:any ) => {
              NProgress.done();
              const result: any = data;
              const status = result.runNowFortellisExtractJobByStore.status;
              const message = result.runNowFortellisExtractJobByStore.message;
              if (status) {
                this.EventEmitterService.displayProgress.emit(this.SchedulerConstantService.EVENT_EMITTER.STEP_RELOAD_LIST);
                this.refreshScheduleList();
                setTimeout(function () {
                  self.refreshScheduleList();
                  self.EventEmitterService.displayProgress.emit('');
                  self.showStatusMessage(message, 'success');
                }, 3000);
              } else {
                self.EventEmitterService.displayProgress.emit('');
              }
            },
              (err: Error) => {
                this.EventEmitterService.displayProgress.emit('');
                NProgress.done();
                const message = this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, 'failure');
                this.commonService.errorCallback(err, this);
              },
              // complete: () => {
              //   console.log("Completed");
             // },
            );
          });
          
        }
    }
  }
    

  cancelSchedule(data: any) {
    let userName: any;
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
    }

    let rowData = data.split(",");
    const thridPartyUserName = rowData[1].toString();
    let filteredData: any = Object.assign([], this.jobGroupList);
    let resObj = filteredData.filter(
      (item: any) => item.thirdPartyUsername === thridPartyUserName
    );
    let mageGrpCode = "";
    let mageStrCode = "";
    let steCode = "";
    let projectId = "";
    let secondProjectId = "";
    let dealerAddress: any;
    let mageManufacturer: any;

    if (resObj.length) {
      mageGrpCode = resObj[0].mageGroupCode;
      mageStrCode = resObj[0].mageStoreCode;
      steCode = resObj[0].state;
      projectId = resObj[0].projectId;
    }
    var date = moment(rowData[3]);
    var now = moment();
    if (now > date) {
      swal({
        title: this.SchedulerConstantService.JOB_STARTED,
        type: "warning",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: this.constantService.CLOSE,
      });
      this.refreshScheduleList();
    } else {
      swal(
        {
          title: this.constantService.AREYOUSURE,
          text: this.SchedulerConstantService.CANCEL_SCHEDULE_NOW,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default pointer",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: "Continue",
          closeOnConfirm: true,
          showLoaderOnConfirm: true,
        },
        () => {
          this.EventEmitterService.displayProgress.emit(
            this.SchedulerConstantService.EVENT_EMITTER.CANCEL_SCHEDULE
          );
          let rowData = data.split(",");
          const groupName = rowData[0];
          let activityData = {
            activityName: "Manage Fortellis",
            activityType: "Cancel Schedule",
            activityDescription: `Cancel Job Schedule for Group ${groupName} (Group: ${mageGrpCode}, Store: ${mageStrCode})`,
          };
          this.commonService.saveActivity("Manage Fortellis", activityData);
          const dealerId = rowData[1].toString();
          let jobSchedule: any = moment(
            rowData[3],
          ).
          format('MM-DD-YYYY HH:mm:ss');
          // let jobSchedule: string = moment(
          //   rowData[3],
          //   this.SchedulerConstantService.DATE_FORMAT
          // ).format();
          let scheduleOpSplit = jobSchedule.split(" ");
          let scheduleOp = scheduleOpSplit[0].split("-");
          jobSchedule =
            scheduleOp[2] +
            "-" +
            scheduleOp[0] +
            "-" +
            scheduleOp[1] +
            " " +
            scheduleOpSplit[1];

          let solve360Update = rowData[13].toLowerCase() == "true";
          let buildProxies = rowData[14].toLowerCase() == "true";

          const dateRange = rowData[2].split(" - ");
          const startDate = dateRange[0].trim();
          const endDate = dateRange[1].trim();
          const closedRoOption = rowData[8];

          if (rowData[12]) {
            projectId = rowData[12].toString();
          }
          if (rowData[16]) {
            secondProjectId = rowData[16].toString();
          }
          if (rowData[17]) {
            dealerAddress = rowData[17].toString();
          }

          if (rowData[18]) {
            mageManufacturer = rowData[18].toString();
          }

          console.log("projectId:", projectId);
          console.log("secondProjectId:", secondProjectId);
          console.log("dealerAddress:", dealerAddress);
          console.log("mageManufacturer:", mageManufacturer);

          let date = new Date(jobSchedule);
          let now_utc = Date.UTC(
            date.getUTCFullYear(),
            date.getUTCMonth(),
            date.getUTCDate(),
            date.getUTCHours(),
            date.getUTCMinutes(),
            date.getUTCSeconds()
          );
          const now_utcOP = new Date(now_utc);
          const scheduleObj: any = {
            jobSchedule: moment(now_utcOP).toISOString(),
            jobData: {
              groupName: groupName,
              storeData: {
                dealerId: dealerId,
                projectId: projectId,
                secondProjectId: secondProjectId,
                mageManufacturer: mageManufacturer,
                solve360Update: solve360Update,
                buildProxies: buildProxies,
                userName: userName,
                startDate: startDate,
                endDate: endDate,
                closedROOption: closedRoOption,
                mageGroupCode: mageGrpCode,
                mageStoreCode: mageStrCode,
                stateCode: steCode,
                dealerAddress: dealerAddress,
              },
            },
          };
          let self = this;
          this.apollo
            .use("manageFortellisSchedule")
            .mutate({
              mutation: cancelFortellisExtractJobByStore,
              variables: scheduleObj,
            }).pipe(takeUntil(this.subscription$)).subscribe({
              next: (listdata: any) => {
                const result: any = listdata.data;
                NProgress.done();
                const status = result.cancelFortellisExtractJobByStore.status;
                const message = result.cancelFortellisExtractJobByStore.message;
                if (status) {
                  this.EventEmitterService.displayProgress.emit(
                    this.SchedulerConstantService.EVENT_EMITTER.CANCEL_SCHEDULE
                  );
                  this.refreshScheduleList();
                  setTimeout(() => {
                    self.EventEmitterService.displayProgress.emit("");
                    self.showStatusMessage(message, "success");
                  }, 3000);
                } else {
                  self.EventEmitterService.displayProgress.emit("");
                }
              },
              error: (err: Error) => {
                this.EventEmitterService.displayProgress.emit("");
                NProgress.done();
                const message =
                  this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, "failure");
                this.commonService.errorCallback(err, this);
              },
              complete: () => {
                console.log("Completed");
              },
            });
        }
      );
    }
  }

  processQueueToggle() {
    this.processQueueListCollapsed = !this.processQueueListCollapsed;
    if (!this.processQueueListCollapsed) {
      this.setActiveFixedHeader();
    }
  }

  completedJobsToggle() {
    this.completedListCollapsed = !this.completedListCollapsed;
    if (!this.completedListCollapsed) {
      this.refreshScheduleListCompleted();
      this.completedProcessjsonListCollapsed = true;
      this.setActiveFixedHeader();
    }
  }

  processJsonToggle() {
    this.completedProcessjsonListCollapsed =
      !this.completedProcessjsonListCollapsed;
    if (!this.completedProcessjsonListCollapsed) {
      this.completedListCollapsed = true;
      this.refreshProcessJsonList();
      this.setActiveFixedHeader();
    }
  }

  getAllProcessJsonJobs(callback: any) {
    this.processJsonListArray = [];
    const allStoreGroupsList = this.apollo
      .use("manageFortellisSchedule")
      .query({
        query: getAllFortellisProcessJSONJobs,
        fetchPolicy: "network-only",
      }).pipe(takeUntil(this.subscription$)).subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          let obj: any = {};
          this.processJsonListArray = [];
          if (
            result["data"]["getAllFortellisProcessJSONJobs"] &&
            result["data"]["getAllFortellisProcessJSONJobs"][
              "processJSONJobsQueue"
            ]
          ) {
            $.each(
              result["data"]["getAllFortellisProcessJSONJobs"][
                "processJSONJobsQueue"
              ],
              (key: any, val: any) => {
                let groupName = "test";
                let date = "";
                let nextRunAt = "";
                let statusFlag =
                  this.SchedulerConstantService.STATUS_FLAG.QUEUED;
                obj = {
                  groupName: groupName,
                  storeID: val.storeID,
                  statusFlag: statusFlag,
                  lastRunAt: null,
                  fileProcessed: null,
                  outputFile: null,
                  message: null,
                  failedReason: null,
                  nextRunAt: nextRunAt,
                  statusInfo: "",
                  uniqueId: "",
                  reRun: false,
                  priority: val.priority
                };
                obj.statusInfo =
                  "File To Process: " + val.fileToProcess + " \n";
                this.processJsonListArray.push(obj);
                console.log('Process JSON-------------------------->',this.processJsonListArray)

              }
            );
          }
          if (
            result["data"]["getAllFortellisProcessJSONJobs"] &&
            result["data"]["getAllFortellisProcessJSONJobs"]["processJSONJobs"]
          ) {
            $.each(
              result["data"]["getAllFortellisProcessJSONJobs"][
                "processJSONJobs"
              ],
              (key: any, val: any) => {
                let groupName = "test";
                let scheduledDate = val.nextRunAt
                  ? val.nextRunAt
                  : val.lastRunAt
                  ? val.lastRunAt
                  : null;
                let date = "";
                scheduledDate
                  ? (date = moment
                      .unix(scheduledDate / 1000)
                      .format("MM-DD-YYYY HH:mm:ss"))
                  : null;
                let nextRunAt = "";
                val.nextRunAt
                  ? (nextRunAt = moment.unix(val.nextRunAt / 1000).fromNow())
                  : null;
                let statusFlag = this.getJobStatus(val);

                let statusMessage = val.failReason;
                // let haltIdentifier = false;
                // if(statusMessage == 'Error: Halt' || statusMessage == 'Error: Dead'){
                //   haltIdentifier = true;
                // }

                // if(haltIdentifier){
                  let failReason: string =  '';
                if (statusMessage == "Error: Halt") {
                  statusFlag = "HALT";
                  this.haltState = "Halted";
                  failReason = "Exceptions in core";
                } else if (statusMessage == "Error: Dead") {
                  statusFlag = "DEAD";
                  this.haltState = "Held";
                  failReason = "Exceptions in core";
                }
                // }

                // let failedReason = val.failReason;
                let failedReason = failReason ? failReason : val.failReason;
                if (failedReason) {
                  failedReason = failedReason.replace(/,/g, "; ");
                }
                const uniqueId = val._id;
                val.scheduled && val.failed
                  ? (statusFlag =
                      this.SchedulerConstantService.STATUS_FLAG.RESCHEDULED)
                  : null;
                let storeName = "";
                if (val.data.storeID && val.data.inputFile) {
                  let sp1 = val.data.inputFile.split(val.data.storeID);
                  storeName = sp1 && sp1.length ? sp1[0].split("-")[1] : "";
                }

                let miscExceptionCount;
                let address;
                let processorUniqueId;
                if (val.data) {
                  console.log(
                    "******************************SHARK TRACK*********************************************"
                  );
                  if (val.data.hasOwnProperty("miscExceptionCount")) {
                    miscExceptionCount = val.data.miscExceptionCount;
                  }

                  if (val.data.hasOwnProperty("processorUniqueId")) {
                    processorUniqueId = val.data.processorUniqueId;
                  }
                }
                if (val.data) {
                  if (val.data.hasOwnProperty("address")) {
                    address = val.data.address;
                  }
                }
                let reRunFlag = true;
                if (
                  val.data.inputFile &&
                  val.data.inputFile.includes("-RERUN")
                ) {
                  if (
                    val.data.outputFile != "" &&
                    val.data.outputFile != null
                  ) {
                    let tmp = val.data.outputFile.split("&");
                    val.data.outputFile = tmp[0];
                  }
                  reRunFlag = false;
                }
                let priority;
                if (val.data) {
                  if (val.data.hasOwnProperty('priority')) {
                    priority = val.data.priority;
                  }
                }
                obj = {
                  groupName: groupName,
                  storeID: val.data.storeID,
                  statusFlag: statusFlag,
                  lastRunAt: date,
                  fileProcessed: val.inputFile,
                  outputFile: val.data.outputFile,
                  message: val.data.message,
                  failedReason: failedReason,
                  nextRunAt: nextRunAt,
                  statusInfo: "",
                  uniqueId: uniqueId,
                  storeName: storeName,
                  projectId: val.data.projectId,
                  solve360Update: val.data.solve360Update,
                  buildProxies: val.data.buildProxies,
                  miscExceptionCount: miscExceptionCount,
                  reRun: reRunFlag,
                  address: address,
processorUniqueId: processorUniqueId,
                  priority: val.priority,
                  uploadStatus: val.uploadStatus
                };
                let updatedData = { ...val.data, uploadStatus: val.uploadStatus };
                obj.statusInfo = this.getStatusInfo(updatedData);
                if (
                  val.data.operation === "json-processing" &&
                  val.data.storeID
                ) {
                  this.processJsonListArray.push(obj);
                }
                this.sortJSONArray(this.processJsonListArray);
              }
            );
          }
          callback();
        },
        error: (err: Error) => {
          this.commonService.errorCallback(err, this);

        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  getStatusInfo(inputData: any) {
    let data = "";
    data += "Input File: " + inputData.inputFile + " \n";
    if (inputData.outputFile) {
      data += "Output File: " + inputData.outputFile + " \n";
    }
    let createdAt = null;

    if (inputData.createdAt && !isNaN(inputData.createdAt)) {
      let dt = inputData.createdAt;
      var y = dt.substr(0, 4);
      var t = dt.substr(4, dt.length);
      let remArray = t.match(/.{1,2}/g);
      remArray[0] = (remArray[0] * 1 - 1).toString();
      remArray.unshift(y);
      createdAt = moment(remArray).format("MM-DD-YYYY HH:mm:ss");
      data += "Created At: " + createdAt + " \n";
    }
    return data;
  }
  /**
   * showProcessJsonList function will show the Process JSON List
   *
   */
  showProcessJsonList() {
    this.processJSONLoading = true;
    $("#processJsonList").dataTable().fnDestroy();
    table1 = $("#processJsonList").dataTable().fnClearTable();
    const elm = this;
    let i = 0;
    setTimeout(() => {
      $(document).ready(() => {
        table1 = $("#processJsonList").dataTable({
          language: {
            decimal: ".",
            thousands: ",",
          },
          columnDefs: [
            { type: "numeric-comma", targets: "_all" },
            { orderable: false, targets: [3] },
            { orderable: true, targets: [0, 1, 2] },
          ],
          fixedHeader: {
            header: true,
            footer: true,
            headerOffset: $(".cat__top-bar").outerHeight() - 11,
          },
          bSort: false,
          order: [1, "desc"],
          responsive: true,
          scrollX: false,
          destroy: true,
          paging: true,
          deferRender: true,
          ordering: true,
          info: true,
          filter: true,
          length: true,
          processing: true,
          lengthMenu: [
            [50, 25, 10, 5],
            [50, 25, 10, 5],
          ],
          autoWidth: false,
          scrollY: "200px",
          fnRowCallback: function (settings: any, aData: any) {
            const pagination = $(this)
              .closest(".dataTables_wrapper")
              .find(".dataTables_paginate");
            pagination.toggle(this.api().page.info().pages > 1);
          },
          drawCallback: function (settings: any) {
            table1 = $("#processJsonList").DataTable();
            $("td:eq(1)", settings).css("width", "24%");
            $("td:eq(2)", settings).css("width", "13%");
            $("td:eq(3)", settings).css("width", "15%");
            $("td:eq(4)", settings).css("width", "5%");
          },
          columns: [
            {
              title: "Store",
              width: "24%",
              className: "dt-head-left",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let storeName = elm.getStoreNameFromDealerId(rowData[1]);
                storeName = storeName ? storeName : rowData[1];
                storeName =
                  storeName === rowData[1]
                    ? rowData[10]
                      ? rowData[10]
                      : rowData[1]
                    : storeName;
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[1] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  storeName +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Last Run At",
              width: "15%",
              type: "formatted-date",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let lastRunAt = "";
                if (rowData[3]) {
                  let time = rowData[3].split(" ");
                  let HourFormat = elm.convertTimeFormat(time[1]);
                  lastRunAt = time[0] + " " + HourFormat;
                }
                data = "<span>" + lastRunAt + "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Status",
              width: "15%",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                var className = "";
                var toolTipMessage = rowData[8];
                rowData[7]
                  ? (toolTipMessage += "Failed Reason: " + rowData[7] + " \n")
                  : "";
                rowData[2] ===
                elm.SchedulerConstantService.STATUS_FLAG.COMPLETED
                  ? (className = "label-success")
                  : null;
                rowData[2] ===
                elm.SchedulerConstantService.STATUS_FLAG.SCHEDULED
                  ? (className = "label-scheduled")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.RUNNING
                  ? (className = "label-running")
                  : null;
                rowData[2] ===
                elm.SchedulerConstantService.STATUS_FLAG.REPEATING
                  ? (className = "label-repeating")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.FAILED
                  ? (className = "label-failed")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.QUEUED
                  ? (className = "label-queued")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.LOCKED
                  ? (className = "label-locked")
                  : null;
                rowData[2] == "HALT" ? (className = "label-halt") : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.UPLOADHALT
                ? (className = "label-upload-halt")
                : null;
                data =
                  '<span style="cursor:pointer;float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="statusMessageDisplay label ' +
                  className +
                  '" data-toggle="tooltip" data-info="' +
                  rowData +
                  '" data-placement="left" title="' +
                  toolTipMessage +
                  '" data-animation="false">' +
                  rowData[2] +
                  "</span>";
                return data;
              },
            },
            {
              title: "",
              width: "5%",
              className: "dt-head-left pl-0",
              render: function (data: any, type: any, rows: any, meta: any) {
                const d = data;
                let rowData: any[] = [];
                rowData = Object.assign([], rows);                             
                  const updateParentGroup = "Resume";
                  if (rowData[2] == "HALT") {
                    data =
                      '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                      d +
                      '">' +
                      '<i aria-hidden="true" class="fa fa-caret-square-o-up text-success mt-2 haltAndResumeDetails" style="font-size: 18px;color: #80047d !important;"  data-toggle="tooltip" data-placement="top" title="' +
                      updateParentGroup +
                      '" data-animation="false" data-info="' +
                      rowData +
                      '"  ></i></a>';
                  } else if(rowData[2] == "Queued"){
                    let updateParentGroup = 'Change Priority';
                      data = '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                      + '<i aria-hidden="true" class="fa fa-edit text-primary mt-2 changePriority" style="font-size: 18px;"  data-toggle="tooltip" data-placement="top" title="'
                      + updateParentGroup + '" data-animation="false" data-info="' + rowData + '"  ></i></a>';
                    } else {
                    data = "";
                  }
               
                // re run

                return data;
              },
            },
          ],
          rowGroup: {
            dataSrc: "Store Group",
          },
        });
        // tslint:disable-next-line:no-unused-expression
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
          "formatted-num-pre": function (a: any) {
            a = a === "-" || a === "" ? 0 : a.replace(/[^\d\-\.]/g, "");
            return parseFloat(a);
          },
          "formatted-num-asc": function (a: any, b: any) {
            return a - b;
          },
          "formatted-num-desc": function (a: any, b: any) {
            return b - a;
          },
          "formatted-date-asc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a ? new Date(a).getTime() : 0;
            const y = b ? new Date(b).getTime() : 0;
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "formatted-date-desc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a
              ? new Date(a.toString()).getTime()
              : moment().unix() * 1000;
            const y = b
              ? new Date(b.toString()).getTime()
              : moment().unix() * 1000;
            return x < y ? 1 : x > y ? -1 : 0;
          },
        });
        console.log('Process JSON-------------------------->',elm.processJsonListArray)
        elm.reDrawProcessJsonTable(elm.processJsonListArray);
      });
    }, 200);
  }

  /**
   * reDrawProcessJsonTable function will redraw the datatable using new table values
   *
   */
  reDrawProcessJsonTable(temp: any) {
    console.log('Process JSON-------------------------->',temp)
    table1 = $("#processJsonList").DataTable();
    table1.search("").draw();
    table1 = $("#processJsonList").dataTable();
    table1.fnClearTable();
    table1 = $("#processJsonList").dataTable();
    const tempArr: any[] = [];
    for (let i = 0; i < temp.length; i++) {
      const t = temp[i];
      const rpt = [
        t.groupName,
        t.storeID,
        t.statusFlag,
        t.lastRunAt,
        t.fileProcessed,
        t.outputFile,
        t.message,
        t.failedReason,
        t.statusInfo,
        t.uniqueId,
        t.storeName,
        t.miscExceptionCount,
        t.reRun,
        t.address,
        t.priority,
        t.uploadStatus,
t.processorUniqueId

      ];
      tempArr.push(rpt);
    }
    if (tempArr.length > 0) {
      table1.fnAddData(tempArr, false); // Add new data
    }
    table1.fnDraw(); // Redraw the DataTable
    this.processJSONLoading = false;
    if (temp.length > 0) {
      setTimeout(() => {
        $("#processJsonList").DataTable().columns.adjust().draw(false);
      }, 100);
    }
  }

  setActiveFixedHeader() {
    if (
      navigator.userAgent.indexOf("MSIE") !== -1 ||
      navigator.appVersion.indexOf("Trident/") > 0
    ) {
      const evt = document.createEvent("UIEvents");
      evt.initUIEvent("resize", true, false, window, 0);
      window.dispatchEvent(evt);
    } else {
      window.dispatchEvent(new Event("resize"));
    }
  }

  startDateSelection() {
    let date: any = moment().subtract(6, "months");
    if (date.format("D") <= this.SchedulerConstantService?.DAY_NUMBER_CHECK) {
      return moment(date).startOf("month");
    } else {
      return moment(date).add(1, "months").startOf("month");
    }
  }

  endDateSelection() {
    return moment();
  }

  utcToLocalTime(time: any) {
    let localTime = null;
    localTime = moment(time * 1)
      .local()
      .format(this.SchedulerConstantService.SCHEDULE_DATE_FORMAT);
    return localTime;
  }

  getRoOptionList() {
    this.roOptionList = [];
    let data = this.SchedulerConstantService.RO_OPTION;
    for (let i = 0; i < data.length; i++) {
      this.roOptionList.push({ id: data[i], itemName: data[i] });
    }
  }

  onChangeRoOptionList(item: any) {
    if (!this.containsObject(item, this.onChangeRoOption)) {
      this.onChangeRoOption.push(item);
    }
  }

  getAllJobsForUiUpdate(callback: any) {
    this.compareObjArrayLatest = [];
    const allStoreGroupsList = this.apollo
      .use("manageFortellisSchedule")
      .query({
        query: getAllFortellisExtractJobs,
        fetchPolicy: "network-only",
      }).pipe(takeUntil(this.subscription$)).subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          let obj: any = {};
          $.each(
            result["data"]["getAllFortellisExtractJobs"]["jobArray"],
            (key: any, val: any) => {
              let groupName = val.data.groupName;
              let scheduledDate = val.nextRunAt
                ? val.nextRunAt
                : val.lastRunAt
                ? val.lastRunAt
                : null;
              let date = "";
              scheduledDate
                ? (date = moment
                    .unix(scheduledDate / 1000)
                    .format("MM-DD-YYYY HH:mm:ss"))
                : null;
              let nextRunAt = "";
              val.nextRunAt
                ? (nextRunAt = moment.unix(val.nextRunAt / 1000).fromNow())
                : null;
              let groupStatus = val;
              const failedReason = val.failReason;
              $.each(val.data["storeDataArray"], (key: any, val: any) => {
                let jobStartDate = null;
                let jobEndDate = null;
                let rpt: any[] = [];
                let keyVal = null;
                let status = null;
                let seperator = "/";
                if (val.startDate.includes("-")) {
                  seperator = "-";
                }
                const dateArray = val.startDate.split(seperator);
                const startDate =
                  dateArray[0] + "-" + dateArray[1] + "-" + dateArray[2];
                if (val.endDate.includes("-")) {
                  seperator = "-";
                }
                const dateArrayEnd = val.endDate.split(seperator);
                const endDate =
                  dateArrayEnd[0] +
                  "-" +
                  dateArrayEnd[1] +
                  "-" +
                  dateArrayEnd[2];
                let range = startDate + " - " + endDate;
                jobStartDate = val.startTime;
                jobEndDate = val.endTime;
                rpt = [groupName, val.dealerId, range, date];
                keyVal = rpt.join();
                let jobStatus = false;
                status = val.status;
                let statusFlag = "";
                if (jobStartDate && jobEndDate && status) {
                  jobStatus = true;
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.COMPLETED;
                } else if (jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.RUNNING;
                } else if (!jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.SCHEDULED;
                } else if (jobStartDate && jobEndDate && !status) {
                  jobStatus = true;
                  statusFlag = this.SchedulerConstantService.STATUS_FLAG.FAILED;
                } else {
                  statusFlag = this.getJobStatus(groupStatus);
                }
                status = statusFlag;
                let objNew: any = {};
                objNew[keyVal] = status;
                this.compareObjArrayLatest.push(objNew);
              });
            }
          );
          callback();
        },
        error: (err: Error) => {
          this.commonService.errorCallback(err, this);

        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  compareObjectValue() {
    let dataTableObjects: any[] = [];
    let data = null;
    let self = this;
    table1 = $("#scheduleProcessQueueFortellis").DataTable();
    data = table1.rows().data();
    let i = 0;
    data.each(function (value: any, index: any) {
      let status = null;
      let keyVal = "";
      let res = null;
      keyVal = value[0] + "," + value[1] + "," + value[2] + "," + value[3];
      status = value[4];
      res = self.compareKey(keyVal);
      if (res !== status) {
        self.reloadExtractionQueue(res);
      }
      i++;
    });
  }

  reloadExtractionQueue(res: any) {
    this.refreshScheduleList();
    if (
      res === this.SchedulerConstantService.STATUS_FLAG.COMPLETED ||
      res === this.SchedulerConstantService.STATUS_FLAG.FAILED
    ) {
      this.refreshScheduleList();
      this.refreshScheduleListCompleted();
    }
  }

  compareKey(key: any) {
    let status = null;
    var itemsProcessed = 0;
    let length = this.compareObjArrayLatest.length;
    this.compareObjArrayLatest.forEach(function (obj, index, self) {
      itemsProcessed++;
      if (obj.hasOwnProperty(key)) {
        status = obj[key];
      }
    });
    if (itemsProcessed === length) {
      return status;
    }
    return;
  }

  processJSONReloadList(callback: any) {
    this.compareObjArrayProcessJSONList = [];
    this.processJSONJobsQueueLength = 0;
    const allStoreGroupsList = this.apollo
      .use("manageFortellisSchedule")
      .query({
        query: getAllFortellisProcessJSONJobs,
        fetchPolicy: "network-only",
      }).pipe(takeUntil(this.subscription$)).subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          if (
            result["data"]["getAllFortellisProcessJSONJobs"] &&
            result["data"]["getAllFortellisProcessJSONJobs"]["processJSONJobs"]
          ) {
            $.each(
              result["data"]["getAllFortellisProcessJSONJobs"][
                "processJSONJobs"
              ],
              (key: any, val: any) => {
                let statusFlag = this.getJobStatus(val);
                let obj = { statusFlag: statusFlag };
                if (
                  val.data.operation === "json-processing" &&
                  val.data.storeID
                ) {
                  let keyVal = null;
                  keyVal = val._id;
                  let objNew: any = {};
                  objNew[keyVal] = statusFlag;
                  this.compareObjArrayProcessJSONList.push(objNew);
                }
              }
            );
            if (
              result["data"]["getAllFortellisProcessJSONJobs"] &&
              result["data"]["getAllFortellisProcessJSONJobs"][
                "processJSONJobsQueue"
              ]
            ) {
              this.processJSONJobsQueueLength = result["data"][
                "getAllFortellisProcessJSONJobs"
              ]["processJSONJobsQueue"].length
                ? result["data"]["getAllFortellisProcessJSONJobs"][
                    "processJSONJobsQueue"
                  ].length
                : 0;
            }
            callback();
          }
        },
        error: (err: Error) => {
          this.commonService.errorCallback(err, this);

        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  compareObjectValueProcessJSON() {
    let reloadStatus = false;
    let data = null;
    let self = this;
    table1 = $("#processJsonList").DataTable();
    data = table1.rows().data();
    let incQueuedList = 0;
    let incProcessList = 0;
    data.each(function (value: any, index: number) {
      let status = null;
      let keyVal = "";
      let res = null;
      if (value[2] != self.SchedulerConstantService.STATUS_FLAG.QUEUED) {
        keyVal = value[9];
        status = value[2];
        incProcessList++;
      } else {
        incQueuedList++;
      }
      res = self.compareProcessJSONKey(keyVal);

      if (status == "HALT" || status == "DEAD") {
        status = "Failed";
      }

      if (res !== status) {
        reloadStatus = true;
      }
    });
    if (
      this.processJSONJobsQueueLength !== incQueuedList &&
      this.processJSONJobsQueueLength > incQueuedList
    ) {
      reloadStatus = true;
    }
    if (this.compareObjArrayProcessJSONList.length !== incProcessList) {
      reloadStatus = true;
    }
    if (reloadStatus) {
      this.getAllProcessJsonJobs(() => {
        this.showProcessJsonList();
      });
    }
  }

  compareProcessJSONKey(key: any) {
    let status = null;
    var itemsProcessed = 0;
    let length = this.compareObjArrayProcessJSONList.length;
    this.compareObjArrayProcessJSONList.forEach(function (obj, index, self) {
      itemsProcessed++;
      if (obj.hasOwnProperty(key)) {
        status = obj[key];
      }
    });
    if (itemsProcessed === length) {
      return status;
    }
    return;
  }

  getJobStatus(val: any) {
    let statusFlag: any = "";
    val.lockedAt
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.LOCKED)
      : null;
    val.scheduled
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.SCHEDULED)
      : null;
    val.queued
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.QUEUED)
      : null;
    val.completed
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.COMPLETED)
      : null;
    val.failed
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.FAILED)
      : null;
    val.repeating
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.REPEATING)
      : null;
    val.running
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.RUNNING)
      : null;
    val.scheduled && val.failed
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.RESCHEDULED)
      : null;
    return statusFlag;
  }

  convertTimeFormat(timeString: any) {
    var H = +timeString.substr(0, 2);
    var h = H % 12 || 12;
    var ampm = H < 12 || H === 24 ? " AM" : " PM";
    timeString = h + timeString.substr(2, 3) + ampm;
    return timeString;
  }

  setDateRange(type: any, jobType: any) {
    if (type) {
      this.createSchedule.patchValue({
        roOption: "all",
      });
    }
    this.setExtractionModeSelection(jobType);
    if (this.jobTypeStatus !== type) {
      this.jobTypeStatus = type;
      if (!type) {
        this.selectMultiDateRangeOption = false;
        this.createSchedule.patchValue({
          roOption: "all",
        });
        this.setExtractionModeSelection(jobType);
        let date = moment().subtract(31, "days");
       
        // this.dateInput = {
        //   start: date,
        //   end: this.selectedRange[1],
        // };
        this.selectedRange = [date.toDate(), this.endDateSelection().toDate()];
        console.log("initial start------------", this.selectedRange);

        this.dateInput = {
          start: this.selectedRange[0],
          end: this.selectedRange[1],
        };
      } else {
        this.selectMultiDateRangeOption = true;
       
        this.dateInput = {
          start: this.selectedRange[0],
          end: this.selectedRange[1],
        };
      }
    }
  }

  setExtractionModeSelection(jobType: any) {
    this.displayOnDemand = false;
    if (jobType === "current") {
      this.displayOnDemand = true;
      this.createSchedule.patchValue({
        roOption: "current",
      });
    }
    if (jobType === "refresh") {
      this.createSchedule.patchValue({
        roOption: "all",
      });
    }
  }

  checkJobExistInExtractionQueue(
    groupName: any,
    storeCode: any,
    dateRange: any
  ) {
    let processQueueListCopy: any[] = [];
    processQueueListCopy = Object.assign([], this.processQueueList);
    processQueueListCopy = processQueueListCopy
      .filter((item: any, index: number) => index < processQueueListCopy.length)
      .filter(
        (item: any, index: number) =>
          item.groupName === groupName &&
          item.store === storeCode &&
          item.range === dateRange &&
          item.status !== "Scheduled"
      );
    return processQueueListCopy.length;
  }
  sortJSONArray(temp: any) {
    this.processJsonListArray = temp
      .filter((item: any, index: number) => index < temp.length)
      .sort((a: any, b: any): any => {
        const x = a["statusFlag"].toLowerCase().replace(/^[^a-z0-9]*/g, "");
        const y = b["statusFlag"].toLowerCase().replace(/^[^a-z0-9]*/g, "");
        return x > y ? -1 : x < y ? 1 : 0;
      });
    return temp;
  }

  reloadGroupList() {
    this.closeToolTip();
    this.reloadGroup = true;
    this.commonService.allS360Jobs("CDKFORTELLIS", "production", (result: any) => {
      console.log(result);
      this.getGroupFilterList();
      this.reloadGroup = false;
    });
  }

  stopTimer() {
    this.autoReloadFortellisStatus = false;
    this.isPaused = false;
  }

  startTimer() {
    this.autoReloadFortellisStatus = true;
    this.isPaused = true;
  }

  closeToolTip() {
    $("[rel=tooltip]").tooltip("enable");
    $(".tooltip").tooltip("hide");
  }

  preSelectGroupAndStore() {
    const storeObj = localStorage.getItem("selectedStoreObj")
      ? JSON.parse(localStorage.getItem("selectedStoreObj")!)
      : null;
    const groupObj = localStorage.getItem("selectedGroupObj")
      ? JSON.parse(localStorage.getItem("selectedGroupObj")!)
      : null;
    if (storeObj && groupObj) {
      const grpObj = {
        id: groupObj.mageGroupName,
        itemName: groupObj.mageGroupName,
        mageGroupCode: groupObj.mageGroupCode,
        mageGroupName: groupObj.mageGroupName,
        mageStoreCode: groupObj.mageStoreCode,
        mageStoreName: groupObj.mageStoreName,
        projectId: groupObj.projectId,
        companyId: groupObj.companyId,
      };
      const strObj = {
        id: storeObj.companyName,
        itemName: storeObj.companyName,
        mageGroupCode: storeObj.mageGroupName,
        mageStoreCode: storeObj.mageStoreCode,
        mageStoreName: storeObj.mageStoreName,
        stateCode: storeObj.state,
        projectId: storeObj.projectId,
        thirdPartyUsername: storeObj.thirdPartyUsername,
        secondProjectId: storeObj.secondProjectId,
        companyID: storeObj.companyId,
      };
      console.log("group obj*************************************",grpObj)
      this.onSelectStoreGroup(grpObj);
      this.onSelectStore(strObj);
    }
  }

  haltAndResumeDetails(data: any) {
    let res;
    res = data.split(",").reverse();
    this.miscExceptionCountMsg = res[0];
    this.resumeProcessorInput = data;
    $("#haltAndResumeModal").modal("show");
  }

  closeHaltAndResumeModal() {
    $("#haltAndResumeModal").modal("hide");
  }

  resumeProcessor() {
    let res, tmp, tmp1, tmp2, inputFile;
    console.log("********************************************");
    console.log(this.resumeProcessorInput);
    console.log(typeof this.resumeProcessorInput);
    res = this.resumeProcessorInput.split(",");
    console.log(res);
    tmp = res[8];
    console.log("tmp:", tmp);
    tmp1 = tmp.split("\n")[0];
    console.log("tmp1", tmp1);
    tmp2 = tmp1.split("Input File:")[1];
    console.log("tmp2:", tmp2);
    console.log("********************************************");
    if (tmp2) {
      inputFile = tmp2.trim();
    }
    let processorUniqueId = res[16];
    const payload = { extractFile: inputFile, dms: "FORTELLIS",processorUniqueId:processorUniqueId };
    let url = environment.haltAndResume;
    const token = localStorage.getItem("token");
    // let headers = new HttpHeaders({ "Content-Type": "application/json" });
    // headers.append("authorization", `Bearer ${token}`);
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      // const resData = JSON.parse(res["_body"]);
      const resData = res;
      this.closeHaltAndResumeModal();
      this.refreshProcessJsonList();
      if (resData.status) {
        console.log(resData);
        swal({
          title: "Processor job resumed successfully",
          type: "success",
          confirmButtonClass: "btn-success pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      } else {
        console.log(resData);
        swal({
          title: "Something went wrong",
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      }
    });
  }

  updateDealerAddress(data: any) {
    $("#payTypeModal").modal("show");
    this.updateDealeAddressInput = data;
    let res = data.split(",");
    let dealerAddress = res[13];
    let dealerAdd = dealerAddress ? dealerAddress.split("~").join("\n") : "";
    this.dealerAddress = dealerAdd;
    let splitFile = res[5].split("&");
    const distFile = splitFile[0];
    const etlFile = splitFile[1];
    let distFile_temp1 = distFile.split("/").reverse()[0];
    distFile_temp1 = distFile_temp1.replace("PROC-", "");
    let distFile_temp2 = distFile_temp1.split("-");
    this.uniqueCode = distFile_temp2[1];
    this.uniqueIdentifier = distFile_temp2[4];
    this.doProxyFilePath = distFile.trim();
  }
  fetchPayTypeDetails(data: any) {}

  closePayTypeModal() {
    $("#payTypeModal").modal("hide");
  }

  updatePayTypeInfo() {
    swal(
      {
        title: this.constantService.AREYOUSURE,
        text: this.SchedulerConstantService.DO_PROXY_NOW,
        type: "success",
        showCancelButton: true,
        cancelButtonClass: "btn-default pointer",
        confirmButtonClass: "btn-success pointer",
        confirmButtonText: "Run",
        closeOnConfirm: true,
        showLoaderOnConfirm: true,
      },
      () => {
        this.updatePayTypeInfoSubmit();
      }
    );
  }

  updatePayTypeInfoSubmit() {
    this.writeFileTypes((res: any) => {
      // this.showSpinnerButton = false;
      if (this.doProxyFilePath) {
        this.scheduleJobUsingPgDump(this.doProxyFilePath, (result: any) => {
          if (result["createProxyWithSqlDump"].status) {
            swal({
              title:
                this.SchedulerConstantService.PAY_TYPE_FILTER.JOB_RERUN_SUCCESS,
              type: "success",
              confirmButtonClass: "btn-success pointer",
              confirmButtonText: this.constantService.CLOSE,
            });
          } else {
            swal({
              title: result["createProxyWithSqlDump"].message,
              type: "warning",
              confirmButtonClass: "btn-warning pointer",
              confirmButtonText: this.constantService.CLOSE,
            });
          }
          this.showSpinnerButton = false;
          this.closePayTypeModal();
          this.refreshProcessJsonList();
        });
      } else {
        this.closePayTypeModal();
        swal({
          title:
            this.SchedulerConstantService.PAY_TYPE_FILTER
              .WARNING_MESSAGE_DO_PROXY,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      }
    });
  }
  writeFileTypes(callback: any) {
    let payload = [{}];

    let obj = {
      uniqueId: this.uniqueCode,
    };

    let obj1 = {
      dealerAddress: this.dealerAddress,
    };

    let obj2 = {
      dealerId: this.uniqueIdentifier,
    };

    payload.push(obj);
    payload.push(obj1);
    payload.push(obj2);
    // let obj ={
    //   name:"test"
    // }

    // payload.push(obj);
    let url = environment.updateDealerAddress;
    const token = localStorage.getItem("token");
    // let headers = new HttpHeaders({ "Content-Type": "application/json" });
    // headers.append("Authorization", `Bearer ${token}`);
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      // const resData = JSON.parse(res['_body']);
      if (res.status) {
        callback(res);
      } else {
        callback("");
      }
    });
  }
  scheduleJobUsingPgDump(doProxyFilePath: any, callback: any) {
    const scheduleObj: any = {
      proxyJobData: {
        zipPath: doProxyFilePath,
        // payType: payTypeFileName
      },
    };
    let self = this;
    this.apollo
      .use("manageFortellisSchedule")
      .mutate({
        mutation: createProxyWithSqlDump,
        variables: scheduleObj,
      }).pipe(takeUntil(this.subscription$)).subscribe({
        next: (listdata: any) => {
          const result: any = listdata.data;
          NProgress.done();
          callback(result);
        },
        error: (err: Error) => {
          NProgress.done();
          this.closePayTypeModal();
          swal({
            title:
              this.SchedulerConstantService.PAY_TYPE_FILTER.WARNING_MESSAGE,
            type: "warning",
            confirmButtonClass: "btn-warning pointer",
            confirmButtonText: this.constantService.CLOSE,
          });
          this.showSpinnerButton = false;
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  switchServer(toggleMockServer =false) {
    if (this.solve360ServerDecider) {
      let serverType = "test";
      this.commonService.allS360Jobs("CDKFORTELLIS", serverType, (result: any) => {
        this.loading = false;
        this.storeGroupList = result.storeGroupList;
        this.storeList = result.storeList;
        this.jobGroupList = result.jobGroupList;
        this.getGroupFilterList();
        this.preSelectGroupAndStore();
        if(toggleMockServer){
          this.toastrService.success ("Switched to Mock Server");
        }
      });
    } else {
      let serverType = "production";
      this.commonService.allS360Jobs("CDKFORTELLIS", serverType, (result: any) => {
        this.loading = false;
        this.storeGroupList = result.storeGroupList;
        this.storeList = result.storeList;
        this.jobGroupList = result.jobGroupList;
        this.getGroupFilterList();
        this.preSelectGroupAndStore();
        if(toggleMockServer){
          this.toastrService.success ("Switched to Production Server");
        }
      });
    }
  }
  changePriority(data: string){
    let res = data.split(",");
    console.log('res?????????????????????????????',res);
    this.currentPriority = res[14]
    this.changePriorityForProcessorJob = res[14];
    this.fileNameForPriorityChange = res[8].split("/")[7]  
    console.log('>>>>>>>>>>>>>fileNameForPriorityChange>>>>>>>>>>>>>>>>>>>',this.fileNameForPriorityChange);
    $('#changePriorityModal').modal('show');
    
  
  }
  
  changePriorityOfProcessorJob(){
    const payload = { fileNameForPriorityChange: this.fileNameForPriorityChange ,priority:this.changePriorityForProcessorJob, dms: 'FORTELLIS'};
    let url = environment.changePriority;
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' , 'Authorization': `Bearer ${token}`});    
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      const resData = res;       
      this.closeChangePriorityModal();
      this.refreshProcessJsonList();
        if (resData && resData.status) {
          console.log(resData)
          this.showStatusMessage('Priority Updated Successfully','success')
      } else {
          console.log(resData);
          this.showStatusMessage('Somethinbg Went Wrong!','failure')
          
      }
    }
    );
    
  
  }
  
  closeChangePriorityModal(){
    $('#changePriorityModal').modal('hide');
  }
  onDateRangeChange(range: any) {
    console.log("Selected Date Range:", range);
    if (range) {
      this.dateInput.start = dayjs(range[0]).format("MM-DD-YYYY");
      this.dateInput.end = dayjs(range[1]).format("MM-DD-YYYY");
      console.log("Selected Date Range:", this.dateInput);
    }
  }
  requeue(data: any) {
    swal(
      {
        title: this.constantService.AREYOUSURE,
        text: "",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default pointer",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: "Continue",
        closeOnConfirm: true,
        showLoaderOnConfirm: true,
      },
      () => {
    console.log("REQUEUE DATA$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$", data.split(','));
    const url = environment.reQueueItem;
    const token = localStorage.getItem('token');
    let filePath = data.split(',')[10];
    alert(filePath)
    // Use HttpHeaders instead of Headers
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });
    const payload = {
      dms: 'fortellis',
      filePath: filePath
    };
    this.http.post(url, payload, { headers }).subscribe((res: any) => {
      // Assuming res is already parsed JSON
      const resData = res;
      this.refreshProcessJsonList();
      if (resData && resData.status) {
        console.log(resData);
        this.showStatusMessage('Job Added to Processor Queue', 'success');
      } else {
        console.log(resData);
        this.showStatusMessage('Something Went Wrong!', 'failure');
      }
    });
  })
  }
}

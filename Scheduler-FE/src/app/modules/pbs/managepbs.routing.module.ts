import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManagepbsComponent } from './managepbs.component';

const routes: Routes = [
    { path:"", component: ManagepbsComponent ,
    data: {
        title: "ManagePbs",
        breadcrumb: [{ label: "ManagePbs", url: "" }],
      },}
];

@NgModule({
    exports: [RouterModule],
    imports:[RouterModule.forChild(routes)]
})
export class ManagepbsRoutingModule{}
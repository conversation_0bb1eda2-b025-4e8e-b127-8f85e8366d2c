import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ementRef,
  Renderer2
} from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { ConstantService } from "../../structure/constants/constant.service";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Apollo } from "apollo-angular";
import gql from "graphql-tag";
// import {
//   BootstrapModalModule,
//   Modal,
//   bootstrap4Mode,
// } from "ngx-modialog/plugins/bootstrap";
import { Router } from "@angular/router";
import { CommonService } from "../../structure/services/common.service";
import * as moment from "moment-timezone";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { EventEmitterService } from "../../structure/services/event.emitter.services";
import { SchedulerConstantService } from "../../structure/constants/scheduler.constant.service";
import { DmFormGroupService } from "../../structure/services/dm.formgroup.services";
import { environment } from "../../../environments/environment";
import { SubscriptionConstantService } from "../../structure/constants/subscription.constant.service";
import { IDropdownSettings } from "ng-multiselect-dropdown";
import { Subject, takeUntil } from "rxjs";
import { SharedModule } from "../shared/shared.module";
import * as dayjs from "dayjs"; 
import { ChangeDetectorRef } from '@angular/core';

let table1;
declare var $: any;
declare var jQuery: any;
declare var swal: any;
declare var NProgress: any;

const getAllDealerTrackExtractJobs = gql`
  query getAllDealerTrackExtractJobs {
    getAllDealerTrackExtractJobs {
      timeFrameZone
      timeFrameStartTime
      timeFrameEndTime
      poolTime
      jobArray {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
        running
        scheduled
        queued
        completed
        failed
        repeating
        failReason
        data {
          groupName
          storeDataArray {
            enterpriseCode
            projectId
            secondProjectId
            mageManufacturer
            solve360Update
            buildProxies
            userName
            startDate
            endDate
            message
            startTime
            endTime
            closedROOption
            status
            jobType
            companyNumber
            serverName
            mageGroupCode
            mageStoreCode
            dealerAddress
            skipErrorCount
            coupon_and_discountCSVFilePath
            chart_of_accounts_file_path
            projectIds
            secondProjectIdList
            testData
            companyObj
            companyIds
            parentName
            processFileName
          }
        }
      }
    }
  }
`;

const getAllDealerTrackProcessJSONJobs = gql`
  query getAllDealerTrackProcessJSONJobs {
    getAllDealerTrackProcessJSONJobs {
      processJSONJobs {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
        running
        scheduled
        queued
        completed
        uploadStatus
        failed
        repeating
        failReason
        data {
          inputFile
          storeID
          company_no_not_matching_count
          grouped_team_work_count
          address
          isResumed
          isAlreadyResumed
          suffixedInvoicesCsvData
          new_line_type_count
          negative_coupon_count
          labor_with_zero_sale_nonzero_cost_count
          gl_missing_ros_count
          coupon_discount_basis_amount_mismatch_exception_count
          labor_with_no_paytype_exception_count
          parts_excluded_from_history_exception_count
          lost_sale_parts_exception_count
          processorUniqueId
          chart_of_accounts_file_path     
          warningMessage {
            skipErrorCount
            errorwarningMessage
            closedRODetailErrorwarningMessage
            vehicleErrorwarningMessage
            customerErrorwarningMessage
            glDeatilErrorwarningMessage
            couponAndDiscountWarningMessage
            roAccountDescriptionWarningMessage
            couponAndDiscountFileNotUploadedWarningMessage
            coaExceptionWarningMessage
            customerExceptionWarningMessage
          }
          roAccountDescExceptionCount
          outputFile
          status
          message
          operation
          createdAt
          storeName
        }
      }
      processJSONJobsQueue {
        storeID
        fileToProcess
        priority
      }
    }
  }
`;

const createNewDealerTrack = gql`
  mutation scheduleDealerTrackExtractJob(
    $jobSchedule: DateTime!
    $jobData: JobData!
  ) {
    scheduleDealerTrackExtractJob(
      input: { jobSchedule: $jobSchedule, jobData: $jobData }
    ) {
      status
      message
      job {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
      }
    }
  }
`;

/**
 * Mutation to cancel schedule
 *
 */
const cancelDealerTrackExtractJobByStore = gql`
  mutation cancelDealerTrackExtractJobByStore(
    $jobSchedule: DateTime!
    $jobData: SingleStoreJobData!
  ) {
    cancelDealerTrackExtractJobByStore(
      input: { jobSchedule: $jobSchedule, jobData: $jobData }
    ) {
      status
      message
      job {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
      }
    }
  }
`;

const runNowDealerTrackExtractJobByStore = gql`
  mutation runNowDealerTrackExtractJobByStore(
    $jobSchedule: DateTime!
    $jobData: SingleStoreJobData!
  ) {
    runNowDealerTrackExtractJobByStore(
      input: { jobSchedule: $jobSchedule, jobData: $jobData }
    ) {
      status
      message
      job {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
      }
    }
  }
`;

const createProxyWithSqlDump = gql`
  mutation createProxyWithSqlDump($proxyJobData: ProxyGenerationUsingPgDump!) {
    createProxyWithSqlDump(input: { proxyJobData: $proxyJobData }) {
      status
      message
    }
  }
`;
@Component({
  selector: "app-managedealertrack",
  templateUrl: "./managedealertrack.component.html",
  styleUrls: ["./managedealertrack.component.css"],
  standalone: true,
  imports: [
    SharedModule,
  ],
})
export class ManagedealertrackComponent implements OnInit {
  public isAuthenticated = false;
  private subscription$ = new Subject();
  loading: any = false;
  private storeGroupList: any[] = [];
  private storeList: any[] = [];
  private storeFlag = false;
  private selectedGroup = null;
  private selectedStore = null;
  private storeGroupFlag = false;
  private storeLoading: any = false;
  private scheduleDate = null;
  private processQueueList: any[] = [];
  private processQueueListCompleted: any[] = [];
  private listener: any;
  private jobGroupList = [];
  private timeFrameZone: any;
  private processJsonListArray: any[] = [];
  private roOptionList: any;
  private onChangeRoOption: any[] = [];
  private compareObjArray: any[] = [];
  private compareObjArrayLatest: any[] = [];
  private compareObjArrayProcessJSONList: any[] = [];
  private processJSONJobsQueueLength = 0;
  private jobTypeStatus = true;
  private autoReloadDealerTrackStatus = true;
  public storeGroupFilterList: any[] = [];
  public storeFilterList: any[] = [];
  public store: any[] = [];
  public storeGroup: any[] = [];
  public coupon_and_discountCSVFileListAl: any;
  public coupon_and_discountCSVFileList = [];
  public coupon_and_discountCSVFile = [];
  public archiveDecider!: boolean;
  public company_no_not_matching_Msg: any;
  public grouped_team_work_Msg: any;

  public scheduleProcessQueueLoading = false;
  public scheduleProcessQueueCompletedLoading = false;
  public processJSONLoading = false;
  public createSchedule!: FormGroup;
  public loadingSchedule = false;
  public loadingScheduleCompleted = false;
  public loadingProcessJson = false;
  public loadAllStore = false;
  public completedListCollapsed = true;
  public processQueueListCollapsed = false;
  public completedProcessjsonListCollapsed = false;
  public timeFrameStartTime: any;
  public timeFrameEndTime: any;
  public tootlTipInfo = null;
  public tootlTipInfoTitle = null;
  public selectMultiDateRangeOption = true;
  public reloadGroup = false;
  public isPaused = true;
  public modalDisplayFlag = true;
  public showSpinnerButton = true;
  public updateDealeAddressInput: any;
  public uniqueCode: any;
  public uniqueIdentifier: any;
  public dealerAddress: any;
  public doProxyFilePath: any;
  public displayOnDemand = false;
  public dealerAddressProcessor: any;
  public couponAndDiscountWarningMsg: any;
  public suffixedInvoicesCsvData: any;
  public suffixedInvoices: any[] = [];
  public newLineTypeDetectedMsg: any;
  public negativeCouponMsg: any;
  public laborWithZeroSaleNonZeroCostMsg: any;
  public couponDiscountBasisAmountMismatchExceptionCountMsg: any;
  public laborWithNoPaytypeExceptionMsg: any;
  public partsExcludedFromHistoryExceptionCountMsg: any;
  public lostSalePartsExceptionCountMsg: any;
  public glMissingRosMsg: any;
  public solve360ServerDecider!: boolean;
  public selectedStores = [];
  public allStoreList = [];
  public allStoreFilterList = [];
  public multiStore = [];
  public isTestSchedule = false;
  public isMockServer = environment.envName == "prod" ? false : true;
  public priorityList: any[] = [];
  public numberInput: any;
  public fileNameForPriorityChange='';
  public changePriorityForProcessorJob: any;
  public currentPriority ='';
  public dateInput: any ;
  public processRunningStatus ='';
  public processorStatusUpdatedAt ='';

  public dealer_address = null;
  public skipError: any;

  public resumeProcessorInput: any;

  public changeSchedule(event:any){
    this.isTestSchedule = this.createSchedule.get('testSchedule')?.value;

 }
  
  public isTestServer(value:any){
    this.isTestSchedule = value
  }

  // public dateRangePickerOptions: any = {
  //   startDate: this.dateInput.start,
  //   endDate: this.dateInput.end,
  //   showDropdowns: true,
  //   autoApply: true,
  //   opens: "left",
  //   maxDate: new Date(),
  //   disabled: false,
  // };
  public singleDropdownSettings: any;
  public dropdownSettings: any;
  public singleDropdownSettingsDisable: any;
  public singleDropdownSettingsState: any;
  public multiDropdownSettingsDisable: any;
  public multiDropdownSettings: IDropdownSettings = {
    idField: "id",
    textField: "itemName",
    allowSearchFilter: true,
  };

  singleDropdownFileSettings = {
    singleSelection: true,
    idField: "filename",
    textField: "filename",
    selectAllText: "Select All",
    unSelectAllText: "UnSelect All",
    enableSearchFilter: true,
    classes: "single-selection",
    // labelKey: "filename",
    // primaryKey: "filename",
  };

  public agendaDashboardUrl = "";
  public shows360Link = false;
  public s360CompanyId: any;
  public poolTime: any;
  public accessValidationLoader: boolean = false;
  public couponAndDiscountFile: any;
  public chartOfAccountsFile: any;
  public coupon_and_discount_file_path: any;
  public chart_of_accounts_file_path: any;
  public roAccountDescriptionCountMsg!: number;

  public coaExceptionWarningMsg!: string;
  public customerExceptionWarningMsg!: string;

  public chartOfAccountsFileInput: any;
  selectedRange: any; // Initialize selectedRange
  allowedTypes = [
    'text/csv','application/vnd.ms-excel','application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];

  allowedExtensions = ['csv', 'xls', 'xlsx'];
  errorMessage: string = ''; // Store validation message
  constructor(
    private http: HttpClient,
    private apollo: Apollo,
    public constantService: ConstantService,
    private elRef: ElementRef,
    private renderer: Renderer2,
    private toastrService: ToastrService,
    private commonService: CommonService,
    private EventEmitterService: EventEmitterService,
    private router: Router,
    private DmFormGroupService: DmFormGroupService,
    private SchedulerConstantService: SchedulerConstantService,
    public SubscriptionConstantService: SubscriptionConstantService,
    private cdr: ChangeDetectorRef
    // private modal: Modal
  ) {}

  ngOnDestroy() {
    this.autoReloadDealerTrackStatus = false;
    localStorage.removeItem("selectedStoreObj");
    localStorage.removeItem("selectedGroupObj");
  }
  ngOnInit() {
    this.commonService.getGroups(() => {
      this.commonService.checkGroups((flag) => {
        if (!flag) {
          return;
        }
        this.isAuthenticated = true;
        this.init();
      });
    });
    let activityData = {
      activityName: "Manage DealerTrack",
      activityType: "Manage DealerTrack",
      activityDescription: "Current Url: " + this.router.url,
    };
    this.commonService.saveActivity("Manage DealerTrack", activityData);
    this.createSchedule = new FormGroup({
      storeGroup: new FormControl("", Validators.required),
      store: new FormControl("", Validators.required),
      // coupon_and_discountCSVFile : new FormControl('', Validators.required),
      roOption: new FormControl(
        this.SchedulerConstantService.DEFAULT_RO_OPTION
      ),
      updateRetreiveROinSolve360: new FormControl(true),
      buildProxies: new FormControl(true),
      discountFile: new FormControl(),
      chartOfAccountsFile: new FormControl(),
      skipError: new FormControl(this.constantService.DEFAULT_SKIP_COUNT),
      archiveDecider: new FormControl(false),
      solve360ServerDecider: new FormControl(false),
      jobType: new FormControl(this.SchedulerConstantService.DEFAULT_JOB_TYPE),
      dealerAddressProcessor: new FormControl(),

      testSchedule: new FormControl(false),
      testDealerId: new FormControl(''),
      testCompanyNumber: new FormControl(''),
      testServerName: new FormControl(''),
      testGlId: new FormControl(''),
      testStoreCode: new FormControl(''),
      testGroupCode: new FormControl('')
    });
    this.SubscriptionConstantService.pageTitle = " - DealerTrack";
    let scheduleUrl = environment.jobListUrl;
    scheduleUrl = scheduleUrl.replace(
      this.SchedulerConstantService.END_POINTS.GRAPHQL_END_POINT,
      this.SchedulerConstantService.END_POINTS.AGENDA_END_POINT
    );
    this.agendaDashboardUrl = scheduleUrl;
    this.dropdownSettings = this.DmFormGroupService.dropdownSettings();
    this.singleDropdownSettings =
      this.DmFormGroupService.singleDropdownSettings();
    this.singleDropdownSettingsDisable =
      this.DmFormGroupService.singleDropdownSettingsDisable();
    this.singleDropdownSettingsState =
      this.DmFormGroupService.singleDropdownSettingsState();
    this.multiDropdownSettingsDisable =
      this.DmFormGroupService.multiDropdownSettingsDisable();
      this.selectedRange = [
        this.startDateSelection().toDate(),
        this.endDateSelection().toDate(),
      ];
      console.log("initial start------------", this.selectedRange);
      this.dateInput = {
        start: this.selectedRange[0],
        end: this.selectedRange[1],
      };
      console.log("initial start------------dateInput", this.dateInput);
      // this.dateInput = {
      //   start: this.startDateSelection(),
      //   end: this.endDateSelection(),
      // };
  }

  init() {
    this.priorityList = [
      {id:1,itemName:'1'},
      {id:2,itemName:'2'},
      {id:3,itemName:'3'},
      {id:4,itemName:'4'},
      {id:5,itemName:'5'},
      {id:6,itemName:'6'},
      {id:7,itemName:'7'}
    ]
    this.getRoOptionList();

    this.getAllJobs(() => {
      this.showScheduledProcessList(true);
      this.showScheduledProcessListCompleted(true);
    });
    this.getAllProcessJsonJobs(() => {
      this.showProcessJsonList();
    });
    this.loading = true;
    this.commonService.allS360Jobs(
      "DEALERTRACK",
      "production",
      (result: any) => {
        console.log("hiigrp dataaaaa", result);
        let activityDataScheduleSolve360 = {
          activityName: "All sol360 groups of DealerTrack",
          activityType: "All sol360 groups of DealerTrack",
          activityDescription: `All sol360 groups of DealerTrack w.r.t.x DealerTrack : ${JSON.stringify(
            result
          )})`,
        };
        this.commonService.saveActivity(
          "Manage DealerTrack",
          activityDataScheduleSolve360
        );

        this.loading = false;
        this.storeGroupList = result.storeGroupList;
        this.storeList = result.storeList;
        this.jobGroupList = result.jobGroupList;
        this.dealer_address = result.address;
        this.getGroupFilterList();
        this.preSelectGroupAndStore();
      }
    );

    let objPropertyCalender: any = {};
    objPropertyCalender = this.commonService.getCalenderPropertyObject();
    objPropertyCalender.minDate = new Date();
    $(function () {
      $('[data-toggle="tooltip"]').tooltip({
        trigger: "hover",
        html: true,
      });
    });
    if (!this.listener) {
      this.listener = this.renderer.listen(
        this.elRef.nativeElement,
        "click",
        (evt: any) => {
          if (evt.target.className === "fa fa-play-circle overrideSchedule") {
            console.log("evt.target.dataset.info::::::",evt.target.dataset.info);
            this.runNowSchedule(evt.target.dataset.info);
          } else if (evt.target.className === "fa fa-ban cancelSchedule") {
            this.cancelSchedule(evt.target.dataset.info);
          } else if (
            evt.target.className.indexOf("statusMessageDisplay") != -1
          ) {
            let res = evt.target.dataset.info.split(",");
            let toolTipInfo = this.getProcessJsonToolTipData(res);
            this.showAlert(toolTipInfo, "Process JSON Status");
          } else if (
            evt.target.className.indexOf("popUpInfoCompletedJobs") != -1
          ) {
            let res = evt.target.dataset.info.split(",");
            var toolTipInfo = this.getToolTipInfoCompletedJobs(res);
            this.showAlert(toolTipInfo, "Completed Extractions");
          } else if (
            evt.target.className ===
            "fa fa-caret-square-o-up text-success mt-2 fetchPayTypeDetails"
          ) {
            // alert("Hiiiiiiiiiiiiiiiiii");
            this.fetchPayTypeDetails(evt.target.dataset.info);
          } else if (
            evt.target.className ===
            "fa fa-caret-square-o-up mt-2 fetchPayTypeDetails"
          ) {
            // alert("Hiiiiiiiiiiiiiiiiii");
            this.fetchPayTypeDetails(evt.target.dataset.info);
          } else if (
            evt.target.className ===
            "fa fa-caret-square-o-up text-success mt-2 reRunProcessor"
          ) {
            // alert("Hiiiiiiiiiiiiiiiiii");
            this.reRunProcessor(evt.target.dataset.info);
          } else if (
            evt.target.className ===
            "fa fa-caret-square-o-up text-success mt-2 updateDealerAddress"
          ) {
            this.updateDealerAddress(evt.target.dataset.info);
          } else if (
            evt.target.className ===
            "fa fa-caret-square-o-up mt-2 updateDealerAddress"
          ) {
            this.updateDealerAddress(evt.target.dataset.info);
          } else if (evt.target.className === 'fa fa-edit text-primary mt-2 changePriority') {
          this.changePriority(evt.target.dataset.info);
        } else if (evt.target.className === 'fa fa-trash text-danger mt-2 removeQueueItem') {
          this.removeQueueItem(evt.target.dataset.info);
          
          // this.changePriority(evt.target.dataset.info);
        }else if (evt.target.className === 'fa fa-upload text-primary mt-2 requeue') {
          console.log("requeue event data%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%",evt.target.dataset.info);
          this.requeue(evt.target.dataset.info);
        
        }else if (evt.target.className === 'fa fa-info-circle text-dark mt-2 showProcessorStatus') {
          console.log("showProcessorStatus event data%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%",evt.target.dataset.info);
          this.showProcessorStatus(evt.target.dataset.info);
        
        }
        
        }
      );
    }
    this.getNotificationForUi();
  }

  getNotificationForUi() {
    let self = this;
    if (this.autoReloadDealerTrackStatus) {
      this.getAllJobsForUiUpdate((data: any) => {
        this.compareObjectValue();
        this.processJSONReloadList(() => {
          this.compareObjectValueProcessJSON();
          setTimeout(() => {
            self.getNotificationForUi();
          }, 3000);
        });
      });
    }
  }

  getProcessJsonToolTipData(rowData: any) {
    let toolTip = "";
    let className = "";
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.COMPLETED
      ? (className = "label-success")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.SCHEDULED
      ? (className = "label-scheduled")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.RUNNING
      ? (className = "label-running")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.REPEATING
      ? (className = "label-repeating")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.FAILED
      ? (className = "label-failed")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.QUEUED
      ? (className = "label-queued")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.LOCKED
      ? (className = "label-locked")
      : null;

    rowData[2] == "HALT" ? (className = "label-halt") : null;

    status = '<span class="label ' + className + '">' + rowData[2] + "</span>";
    toolTip += rowData[8] + " \n";
    if (rowData[7] && rowData[2] === "Failed") {
      toolTip += "Failed Reason: " + rowData[7] + " \n";
    }
    
 if (rowData[31] === "false") {
      toolTip +='<span class="label label-success">Completed</span><span style="padding-left: 1rem" class="label label-upload-halt">'+this.SchedulerConstantService.STATUS_FLAG.UPLOADHALT+'</span>  \n';
    } else {
	toolTip += "Status: " + status;
        toolTip += " \n";
    }
    return toolTip;
  }

  showAlert(toolTipText: any, title: any) {
    this.tootlTipInfo = toolTipText.split("\n").join("<br>");
    this.tootlTipInfoTitle = title;
    $("#resolveProjectModal").modal({ show: true, backdrop: "static" });
  }

  closeToolTipModal() {
    $("#resolveProjectModal").modal("hide");
  }

  /**
   * callback function for store group deselection
   *
   */
  OnDeSelectStoreGroup(item: any) {
    // if (!this.containsObject(item, this.storeGroup)) {
    //   this.storeGroup.push(item);
    // }
    // this.getGroupFilterList();
    this.store = [];
    this.storeFilterList = [];
    this.dealerAddressProcessor = "";
    this.createSchedule.reset();
    this.createSchedule.patchValue({
      updateRetreiveROinSolve360: false,
      buildProxies: true,
    });
    // this.storeFilterList = [];
    this.selectMultiDateRangeOption = true;
    this.setDateRange(true, "");
    // this.dateInput = {
    //   start: this.startDateSelection(),
    //   end: this.endDateSelection(),
    // };
    this.dateInput = {
      start: this.selectedRange[0],
      end: this.selectedRange[1],
    };
    this.createSchedule.patchValue({
      roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
      jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE,
    });
    this.getGroupFilterList();
  }

  OnDeSelectStore(item: any) {
    this.dealerAddressProcessor = "";
    if (!this.containsObject(item, this.store)) {
      this.store.push(item);
    }
    this.getStoreFilterList();
  }

  /**
   * getGroupFilterList function will collect the group list for filtering purpose
   *
   */
  getGroupFilterList() {
    this.storeGroupFilterList = [];
    for (let i = 0; i < this.storeGroupList.length; i++) {
      const companyName = this.storeGroupList[i].mageGroupName;
      const mageGroupCode = this.storeGroupList[i].mageGroupCode;
      const mageGroupName = this.storeGroupList[i].mageGroupName
        ? this.storeGroupList[i].mageGroupName
        : "";
      const mageStoreCode = this.storeGroupList[i].mageStoreCode;
      const mageStoreName = this.storeGroupList[i].mageStoreName;
      const projectId = this.storeGroupList[i].projectId;
      const secondProjectId = this.storeGroupList[i].secondaryProjectId;
      const projectType = this.storeGroupList[i].projectType;
      const secondProjectType = this.storeGroupList[i].secondProjectType;
      const companyId = this.storeGroupList[i].companyId;
      const parentName = this.storeGroupList[i].companyName;
      if (companyName) {
        const obj = {
          id: companyName,
          itemName: companyName,
          mageGroupCode: mageGroupCode,
          mageGroupName: mageGroupName,
          mageStoreCode: mageStoreCode,
          mageStoreName: mageStoreName,
          companyId: companyId,
          projectId: projectId,
          secondProjectId: secondProjectId,
          parentName: parentName,
          projectType:projectType,
          secondProjectType:secondProjectType
        };
        if (!this.containsObject(obj, this.storeGroupFilterList)) {
          this.storeGroupFilterList.push(obj);
        }
      }
    }
    this.storeGroupFilterList = this.sortListAsc(this.storeGroupFilterList);

    let activityDataSchedule = {
      activityName: "Group filter list",
      activityType: "Group filter list",
      activityDescription: `Store list w.r.t.x  group : ${JSON.stringify(
        this.storeGroupFilterList
      )})`,
    };
    this.commonService.saveActivity("Manage DealerTrack", activityDataSchedule);
  }
  containsObject(obj: any, list: any) {
    let i;
    for (i = 0; i < list.length; i++) {
      if (list[i].id === obj.id) {
        return true;
      }
    }
    return false;
  }

  /**
   * sortListAsc function will sort the list in ascending order.
   *
   */
  sortListAsc(temp: any) {
    temp = temp
      .filter((item: any, index: any) => index < temp.length)
      .sort((a: any, b: any): any => {
        const x = a["itemName"]
          ? a["itemName"].toLowerCase().replace(/^[^a-z0-9]*/g, "")
          : "";
        const y = b["itemName"]
          ? b["itemName"].toLowerCase().replace(/^[^a-z0-9]*/g, "")
          : "";
        return x < y ? -1 : x > y ? 1 : 0;
      });
    return temp;
  }
  /**
   * callback function for store group selection
   *
   */
  onSelectStoreGroup(item: any) {
    this.dealerAddressProcessor = "";
    this.selectStoreGroup(item, () => {
      this.shows360Link = true;
    // this.s360CompanyId = item.companyId;
      let itemFilter = this.storeGroupFilterList.find(res => res.id == item.id);
      console.log('s360CompanyId',itemFilter, "storeGroupList" ,this.storeGroupList)
      this.s360CompanyId = itemFilter.companyId;  
      });
  }

  selectStoreGroup(item: any, callback: any) {
    this.loadAllStore = true;
    this.storeList = [];
    this.storeFilterList = [];
    this.store = [];
    this.storeFlag = false;
    if (!this.containsObject(item, this.storeGroup)) {
      this.storeGroup.push(item);
    }
    let selectedGroupFilter: any = this.storeGroupList.filter(function (res) {
      return res.sgId == item.id;
    });
    this.selectedGroup = selectedGroupFilter[0];
    //this.getGroupFilterList();

    let itemFilter: any = this.storeGroupFilterList.filter(function (res) {
      return res.id == item.id; //updated as per new array list
    });
    console.log("this.storeGroupFilterList", itemFilter);
    //this.getGroupFilterList();
    this.getStoreList(itemFilter[0], () => {
      this.loadAllStore = false;
      this.getStoreFilterList();
      this.storeGroupFlag = true;
      if (callback) {
        callback();
      }
    });
  }

  /**
   * getStoreList function fetch Stores list for the selected StoreGroup
   *
   */
  getStoreList(item: any, callback: any) {
    const groupCode = item.mageGroupCode;
    this.loading = true;
    this.storeLoading = true;
    this.storeList = [];
    this.storeList = this.jobGroupList
      .filter((item, index) => index < this.jobGroupList.length)
      .filter(
        (item: any, index) =>
          item.mageGroupCode != null &&
          item.mageGroupCode === groupCode &&
          item.thirdPartyUsername !== null
      );
    this.loading = false;
    this.storeLoading = false;
    if (callback) {
      callback();
    }
  }

  /**
   * getStoreFilterList function will collect the store list for filtering purpose
   *
   */
  getStoreFilterList() {
    this.storeFilterList = [];
    for (let i = 0; i < this.storeList.length; i++) {
      const companyID = this.storeList[i].companyId;
      const companyName = this.storeList[i].companyName;
      const storeName = this.storeList[i].companyName;
      const stId = this.storeList[i].companyName;
      const mageGroupCode = this.storeList[i].mageGroupName;
      const mageStoreName = this.storeList[i].mageStoreName;
      const mageStoreCode = this.storeList[i].mageStoreCode;
      const thirdPartyUsername = this.storeList[i].thirdPartyUsername;
      const enterpriseCode = this.storeList[i].dealerbuiltSourceId;
      const serverName = this.storeList[i].dealerbuiltStoreId;
      const stateCode = this.storeList[i].state;
      const address = this.storeList[i].address;
      const projectId = this.storeList[i].projectId;
      const solve360Update = this.storeList[i].solve360Update;
      const buildProxies = this.storeList[i].buildProxies;
      const mageManufacturer = this.storeList[i].mageManufacturer;
      const streetaddress = this.storeList[i].streetaddress;
      const city = this.storeList[i].city;
      const zipcode = this.storeList[i].zipcode;
      const secondProjectId = this.storeList[i].secondaryProjectId;
      const secondaryProjectType = this.storeList[i].secondaryProjectType;
      const projectType = this.storeList[i].projectType;
      const projectName = this.storeList[i].projectName;
      const secondaryProjectName = this.storeList[i].secondaryProjectName;
      const groupCode = this.storeList[i].mageGroupCode;
      if (stId) {
        const obj = {
          id: stId,
          itemName: `${storeName} [${enterpriseCode},${thirdPartyUsername},${serverName}]`,
          mageGroupCode: mageGroupCode,
          mageStoreName: mageStoreName,
          mageStoreCode: mageStoreCode,
          thirdPartyUsername: thirdPartyUsername,
          stateCode: stateCode,
          address: address,
          projectId: projectId,
          solve360Update: solve360Update,
          buildProxies: buildProxies,
          enterpriseCode: enterpriseCode,
          serverName: serverName,
          companyID: companyID,
          mageManufacturer: mageManufacturer,
          companyName: companyName,
          streetaddress: streetaddress,
          city: city,
          zipcode: zipcode,
          secondProjectId: secondProjectId,
          projectType:projectType,
          secondaryProjectType:secondaryProjectType,
          projectName:projectName,
          secondaryProjectName:secondaryProjectName,
          groupCode:groupCode,
          errors:this.storeList[i].errors,
          assignedtoCn:this.storeList[i].assignedtoCn
        };
        if (!this.containsObject(obj, this.storeFilterList)) {
          this.storeFilterList.push(obj);
        }
      }
    }
    this.storeFilterList = this.sortListAsc(this.storeFilterList);
  }

  /**
   * callback function for store selection
   *
   */
  validateDealerAddress() {
    if (this.createSchedule.get("dealerAddressProcessor")?.value?.trim()) {
      this.saveSchedule();
    } else {
      swal(
        {
          title: this.constantService.AREYOUSURE,
          text: this.SchedulerConstantService.DEALER_ADDRESS_EMPTY,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default pointer",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: "Continue",
          closeOnConfirm: true,
          showLoaderOnConfirm: true,
        },
        () => {
          this.saveSchedule();
        }
      );
    }
  }
  onSelectStore(item: any) {
    console.log("ITEM:::::::::::::::::::::::::::::::;;", item);
    let itemFilter = this.storeFilterList.find(res => res.id == item.id);
    this.dealerAddressProcessor = `${
      itemFilter.companyName ? itemFilter.companyName.toUpperCase() + "\n" : ""
    }${itemFilter.streetaddress ? itemFilter.streetaddress + "\n" : ""}${
      itemFilter.city ? itemFilter.city + ", " : ""
    }${itemFilter.stateCode ? itemFilter.stateCode + " " : ""}${itemFilter.zipcode || ""}`;

    // this.store = [];
    this.getStoreFilterList();
    if (!this.containsObject(item, this.store)) {
      this.store.push(item);
    }
    const selectedStoreFilter: any = this.storeList.filter(function (res) {
      return res.companyName == item.id;
    });
    this.selectedStore = selectedStoreFilter[0];
    this.storeFlag = true;
  }

  /**
   *  Select the date from datepicker in the Filter
   *
   */
  public selectedDate(value: any, dateInput: any) {
    dateInput.start = value.start;
    dateInput.end = value.end;
  }

  async saveSchedule() {
    await this.uploadChartOfAccountsFile();
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      var userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
    }
    // const mageGrpCode = this.createSchedule.get("store").value[0].mageGroupCode;
    // const mageStrCode = this.createSchedule.get("store").value[0].mageStoreCode;
    // const steCode = this.createSchedule.get("store").value[0].stateCode;
    // const enterpriseCode =
    //   this.createSchedule.get("store").value[0].enterpriseCode;
    // const companyNumber =
    //   this.createSchedule.get("store").value[0].thirdPartyUsername;
    // // const dealerAddress = this.createSchedule.get('store').value[0].address+','+this.createSchedule.get('store').value[0].companyName;
    // let dealerAddress = this.dealerAddressProcessor
    //   ? this.dealerAddressProcessor.replace(/\n/g, "~")
    //   : "";
    // dealerAddress = dealerAddress ? dealerAddress.replace(/,/g, "|") : "";
    // const serverName = this.createSchedule.get("store").value[0].serverName;
    // let projectId = this.createSchedule.get("store").value[0].projectId
    //   ? this.createSchedule.get("store").value[0].projectId
    //   : "";

    // const mageManufacturer =
    //   this.createSchedule.get("store").value[0].mageManufacturer;
    // // console.log(mageManufacturer);

    // //Get second projectId from local storage
    // const storeTemp = this.createSchedule.get("store").value;
    // console.log("Store temp**********************2", storeTemp);
    // let tempProject = storeTemp.map((store) => store.projectId);
    // let tempSecond = storeTemp.map((store) => store.secondProjectId);
    // let companyIdList = storeTemp.map((store) => store.companyID);

    let storeFilterListTemp = this.storeFilterList;

    let storeTemp1: any = storeFilterListTemp.filter(
      (x) => x.id == this.createSchedule.get("store")?.value[0].id
    );

    const dealerCode = storeTemp1[0].thirdPartyUsername
      ? storeTemp1[0].thirdPartyUsername
      : "3PAARMLIVE1";
    const parentName = storeTemp1[0].itemName;
    const mageGrpCode = storeTemp1[0].mageGroupCode;
    const mageStoreID = storeTemp1[0].mageStoreCode;
    const mageStrCode = storeTemp1[0].mageStoreCode;
    const steCode = storeTemp1[0].stateCode;
    const enterpriseCode = storeTemp1[0].enterpriseCode;
    const companyNumber = storeTemp1[0].thirdPartyUsername;
    const groupCode = storeTemp1[0].groupCode;
    const mageStoreName = storeTemp1[0].mageStoreName;
    // const dealerAddress = this.createSchedule.get('store').value[0].address+','+this.createSchedule.get('store').value[0].companyName;
    let dealerAddress = this.dealerAddressProcessor
      ? this.dealerAddressProcessor.replace(/\n/g, "~")
      : "";
    dealerAddress = dealerAddress ? dealerAddress.replace(/,/g, "|") : "";
    const serverName = storeTemp1[0].serverName;
    let projectId = storeTemp1[0].projectId;

    const mageManufacturer = storeTemp1[0].mageManufacturer;
    const errors= storeTemp1[0].errors || "";
    const assignedtoCn= storeTemp1[0].assignedtoCn || "";
    const thirdPartyUsername = storeTemp1[0].thirdPartyUsername || "";
    let storeTemp: any = storeFilterListTemp.filter((c: any) =>
      this.createSchedule.get("store")?.value.some((s: any) => s.id === c.id)
    );

    console.log(
      "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>storeTemp",
      storeTemp
    );

    console.log(
      "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>storeFilterList",
      storeTemp,
      this.storeFilterList
    );
    let tempProject = storeTemp.map((store: any) => store.projectId);
    let tempSecond = storeTemp.map((store: any) => store.secondProjectId);
    let companyIdList = storeTemp.map((store: any) => store.companyID);
    let brandList = storeTemp.map((store: any) => store.mageManufacturer);
    let companyObj = storeTemp.map((store: any) => ({
      companyId: store.companyID,
      projectId: store.projectId,
      companyName: store.companyName,
      secondProjectId: store.secondProjectId,
      projectType: store.projectType,
      secondaryProjectType: store.secondaryProjectType,
      projectName: store.projectName,
      secondaryProjectName: store.secondaryProjectName  
    }));
console.log("companyObj?????????????????????????????????????????????????????????",companyObj);    

    console.log(
      "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>companyIdList",
      tempProject,
      tempSecond,
      companyIdList
    );

    let projectIds = "";
    let secondProjectIdList = "";

    for (let i = 0; i < tempProject.length; i++) {
      projectIds += tempProject[i] + "*";
    }
    for (let i = 0; i < tempSecond.length; i++) {
      if (tempSecond[i] != undefined) {
        secondProjectIdList += tempSecond[i] + "*";
      }
    }

    // let dealerIdList = storeTemp.map((store) => store.thirdPartyUsername);
    let dealerIdList = storeTemp.map((store: any) => store.thirdPartyUsername);

    let enterpriseCodeList = storeTemp.map(
      (store: any) => store.enterpriseCode
    );
    let serverNameList = storeTemp.map((store: any) => store.serverName);
    console.log(enterpriseCodeList, "hii11111");
    console.log(enterpriseCode, "hii222");
    let allDealerIdSame = this.areAllDealerIdSame(
      dealerIdList,
      enterpriseCodeList,
      serverNameList
    );
    console.log("should schedule ", allDealerIdSame);
    let secondProjectId = "";
    if (
      this.createSchedule
        .get("store")
        ?.value[0].hasOwnProperty("secondProjectId")
    ) {
      secondProjectId =
        //this.createSchedule.get("store")?.value[0].secondProjectId;
        storeTemp.companyID;
    }
   
    let parentID = storeTemp1[0].companyID ? storeTemp1[0].companyID : "";
    const companyId: number = parseInt(parentID);
    console.log(" companyId list after callback ", companyId);

    // for(let i=0;i<companyIdList.length;i++){
    //   this.getSecondProjectId(companyIdList[i],(result)=>{
    //    if(result){
    //      if (result[1]) {
    //        if (result[1].hasOwnProperty('project_id')) {
    //          secondProjectIdList+=result[1].project_id.toString()+'*';
    //        }
    //      }
    //      if (result[0]) {
    //       if (result[0].hasOwnProperty('project_id')) {
    //         projectIds+=result[0].project_id.toString()+'*';
    //       }
    //     }
    //    }
    //   })
    // }
    const projectType = storeTemp1[0].projectType;
    const secondaryProjectType = storeTemp1[0].secondaryProjectType;
    if (this.couponAndDiscountFile) {
      await this.uploadDiscountFile();
    }
      // console.log('+++++++++++++++++++++++++++++++++++++++++++');
    this.getSecondProjectId(companyId, (result: any) => {
      // console.log('Ready for fetch project ID');
      // console.log('+++++++++++++++++++++++++++++++++++++++++++');
      // console.log(result);
      // console.log('+++++++++++++++++++++++++++++++++++++++++++');
      //Override projectId and secondProjectId from api
      // if (result) {
      //   if (result[0]) {
      //     if (result[0].hasOwnProperty('project_id')) {
      //       projectId = result[0].project_id.toString();
      //     }
      //   }
      //   if (result[1]) {
      //     if (result[1].hasOwnProperty('project_id')) {
      //       secondProjectId = result[1].project_id.toString();
      //     }
      //   }
      // }

      // console.log('projectId:', projectId);
      // console.log('secondProjectId:', secondProjectId);
      const currentDate = new Date();

      let futureDateList =''
      let futureDateList1 = storeTemp.reduce((acc : any, item : any) => {
        let futureDate = item.primary_project_start;
        let secondary_project_start = new Date(item.secondary_project_start);
        let projectId = item.projectId;
        let secondProjectId = item.secondProjectId;
        const targetDatePrimary = new Date(futureDate);
      
        if (targetDatePrimary > currentDate) {
          console.log('greter');
          futureDateList+=projectId+'*';
          acc.push(projectId);
        } 
        if(secondary_project_start > currentDate){
          futureDateList+=secondProjectId+'*'
        }
      
        return acc;
      }, []);
      
      console.log("futuredate lsiusrt>>>>>>>>>>>>>>>>>>>>>>>.", futureDateList);
      
      let projectIds ='';
      let secondProjectIdList ='';
      let companyIds = '';
      let brands = "";
      for(let i=0;i<tempProject.length;i++){
        projectIds+=tempProject[i]+'*';
      }
      console.log('Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>',projectIds);
      for(let i=0;i<tempSecond.length;i++){
        if(tempSecond[i]!=undefined){
          secondProjectIdList+=tempSecond[i]+'*';
        }
        
      }
       for(let i=0;i<brandList.length;i++){
        if(brandList[i]!=undefined){
          brands+=brandList[i]+'*';
        }
        
      }



      if(tempSecond.length>0 && tempSecond[0]!=undefined && tempSecond[0]!=''){
        secondProjectId =  tempSecond[0];    
      }
  
      for(let i=0;i<companyIdList.length;i++){
        if(companyIdList[i]!=undefined){
          companyIds+=companyIdList[i]+'*';
        }
        
      }
      console.log("Company Ids$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",companyIds);

      let s: string = moment(new Date(this.dateInput.start)).format(
        this.SchedulerConstantService.DATE_FORMAT
      );
      let e: string = moment(new Date(this.dateInput.end)).format(
        this.SchedulerConstantService.DATE_FORMAT
      );
      let dateRangeCompare = s + " - " + e;
      let isExist = this.checkJobExistInExtractionQueue(
        mageStrCode.trim(),
        enterpriseCodeList,
        dateRangeCompare
      );

      if (isExist) {
        swal({
          title: this.SchedulerConstantService.JOB_STARTED,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      } else {
        if (!allDealerIdSame) {
          this.toastrService.error (
            "Please select stores with same enterpriseCode,companyNumber and server name");
        }
        if (this.createSchedule.valid && allDealerIdSame) {
          let activityData = {
            activityName: "Manage DealerTrack",
            activityType: "Create New Schedule",
            activityDescription: `Create new Schedule for Group ${this.createSchedule
              .get("storeGroup")
              ?.value[0].itemName.trim()} (Group: ${mageGrpCode}, Store: ${mageStrCode})`,
          };
          this.commonService.saveActivity("Manage DealerTrack", activityData);
          let roOptionValue = this.createSchedule.get("roOption")?.value;
          let jobType = this.createSchedule.get("jobType")?.value;
          roOptionValue = roOptionValue
            ? roOptionValue
            : this.SchedulerConstantService.DEFAULT_RO_OPTION;
          jobType = jobType
            ? jobType
            : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
          let solve360Update;
          let buildProxies;
          let skipErrorCount: number;
          solve360Update = this.createSchedule.get(
            "updateRetreiveROinSolve360"
          )?.value;
          buildProxies = this.createSchedule.get("buildProxies")?.value;
          if (!buildProxies) {
            solve360Update = false;
          }

          skipErrorCount = +this.createSchedule.get("skipError")?.value;
          if (
            isNaN(skipErrorCount) ||
            skipErrorCount == undefined ||
            skipErrorCount == null ||
            !skipErrorCount
          ) {
            skipErrorCount = 0;
          }

          // console.log('+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++');
          // console.log('this.coupon_and_discount_file_path:',this.coupon_and_discount_file_path);
          // console.log('+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++');
          // return false;

          roOptionValue = roOptionValue
            ? roOptionValue
            : this.SchedulerConstantService.DEFAULT_RO_OPTION;
          jobType = jobType
            ? jobType
            : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
          this.EventEmitterService.displayProgress.emit(
            this.SchedulerConstantService.EVENT_EMITTER.STEP_1
          );
          let scheduleDate = "";
          scheduleDate = moment().format(
            this.SchedulerConstantService.DATE_FORMAT
          );
          let startDate: string = moment(
            new Date(this.dateInput.start)
          ).format(this.SchedulerConstantService.DATE_FORMAT);
          let endDate: string = moment(new Date(this.dateInput.end)).format(
            this.SchedulerConstantService.DATE_FORMAT
          );
          let date = new Date();
          let dateArray: any = scheduleDate.split("-");
          date.setMonth(dateArray[0] * 1 - 1);
          date.setDate(dateArray[1] * 1);
          date.setFullYear(dateArray[2] * 1);
          let now_utc = Date.UTC(
            date.getUTCFullYear(),
            date.getUTCMonth(),
            date.getUTCDate(),
            date.getUTCHours(),
            date.getUTCMinutes(),
            date.getUTCSeconds()
          );
          const now_utcOP = new Date(now_utc);
          let now_utcOutPut = now_utcOP.toUTCString();
          const scheduleObj: any = {
            jobSchedule: moment(now_utcOutPut).toISOString(),
            jobData: {
              groupName: this.createSchedule.get("storeGroup")?.value.length
                ? this.createSchedule
                    .get("storeGroup")
                    ?.value[0].itemName.trim()
                : "xyz",
              storeDataArray: {
                enterpriseCode: enterpriseCode, // Static enterpriseCode for DealerTrack API testing purpose.
                projectId: projectId === undefined ? "" : projectId,
                secondProjectId:
                secondProjectId === undefined ? "" : secondProjectId,
 		            projectType: projectType === undefined ? "" : projectType,
                secondaryProjectType:
                secondaryProjectType === undefined ? "" : secondaryProjectType,
                mageManufacturer: mageManufacturer,
                solve360Update: solve360Update,
                buildProxies: buildProxies,
                userName: userName,
                startDate: startDate,
                endDate: endDate,
                closedROOption: roOptionValue,
                jobType: jobType,
                mageGroupCode: mageGrpCode,
                mageStoreCode: mageStrCode,
                stateCode: steCode,
                companyNumber: companyNumber,
                serverName: serverName,
                dealerAddress: dealerAddress,
                skipErrorCount: skipErrorCount,
                coupon_and_discountCSVFilePath:
                  this.coupon_and_discount_file_path,
		        chart_of_accounts_file_path: this.chart_of_accounts_file_path,
                projectIds: projectIds,
                secondProjectIdList: secondProjectIdList,
                testData:false,
                companyIds:companyIds,
                parentName:parentName,
                companyObj:JSON.stringify(companyObj),
                groupCode: groupCode,
                mageStoreName:mageStoreName,
                errors:errors,
                thirdPartyUsername: thirdPartyUsername,
                assignedtoCn:assignedtoCn,
                brands:brands 
              },
            },
          };

          let activityDataSchedule = {
            activityName: "BE call to save agenda objects",
            activityType: "Save DealerTrack Jobs",
            activityDescription: `DealerTrack Schedule object is: ${JSON.stringify(
              scheduleObj
            )})`,
          };
          this.commonService.saveActivity(
            "Manage DealerTrack",
            activityDataSchedule
          );

          let self = this;
          this.apollo
            .use("manageDealerTrackSchedule")
            .mutate({
              mutation: createNewDealerTrack,
              variables: scheduleObj,
            }).pipe(takeUntil(this.subscription$)).subscribe({
              next: (listdata: any) => {
                NProgress.done();
                console.log("listdata********************************************",listdata);
                const result: any = listdata.data;
                console.log("result********************************************",result);
                const status = result.scheduleDealerTrackExtractJob.status;
                let message = "";
                message = result.scheduleDealerTrackExtractJob.message;
                if (status) {
                  this.solve360ServerDecider = false;
                  this.switchServer();
                  this.EventEmitterService.displayProgress.emit(
                    this.SchedulerConstantService.EVENT_EMITTER
                      .STEP_SAVE_SCHEDULE
                  );
                  this.refreshScheduleList();
                  setTimeout(function () {
                    self.EventEmitterService.displayProgress.emit("");
                    self.showStatusMessage(message, "success");
                  }, 3000);
                  this.createSchedule.reset();
                  this.createSchedule.patchValue({
                    updateRetreiveROinSolve360: true,
                    buildProxies: true,
                    skipError: this.constantService.DEFAULT_SKIP_COUNT,
                    archiveDecider: false,
                  });
                  this.createSchedule.patchValue({
                    roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
                    jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE,
                  });
                  this.storeFilterList = [];
                  // this.selectMultiDateRangeOption = !this.selectMultiDateRangeOption;
                  this.selectMultiDateRangeOption = true;
                  this.setDateRange(true, "");
                  // this.dateInput = {
                  //   start: this.startDateSelection(),
                  //   end: this.endDateSelection(),
                  // };
                  this.selectedRange = [
                    this.startDateSelection().toDate(),
                    this.endDateSelection().toDate(),
                  ];
                  this.dateInput = {
                    start: this.selectedRange[0],
                    end: this.selectedRange[1],
                  };
                  this.createSchedule.patchValue({
                    roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
                    jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE,
                  });
                } else {
                  self.EventEmitterService.displayProgress.emit("");
                }
              },
              error: (err: Error) => {
                this.EventEmitterService.displayProgress.emit("");
                NProgress.done();
                const message =
                  this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, "failure");
                this.commonService.errorCallback(err, this);
              },
              complete: () => {
                console.log("Completed");
              },
            });
        } else {
          this.validateAllFormFields(this.createSchedule);
        }
      }
      return;
    });
  }

  async saveTestSchedule() {
    const currentUserObj = JSON.parse(localStorage.getItem('currentUser')!);
    if (currentUserObj) {
      var userName = currentUserObj.userName ? currentUserObj.userName : '';
    }
    // const mageGrpCode = this.createSchedule.get('store')?.value[0].mageGroupCode;
    // const mageStrCode = this.createSchedule.get('store')?.value[0].mageStoreCode;
    // const steCode = this.createSchedule.get('store')?.value[0].stateCode;
    // const enterpriseCode = this.createSchedule.get('store')?.value[0].enterpriseCode;
    // const companyNumber = this.createSchedule.get('store')?.value[0].thirdPartyUsername;
    // const dealerAddress = this.createSchedule.get('store')?.value[0].address+','+this.createSchedule.get('store')?.value[0].companyName;

    const enterpriseCode = this.createSchedule.get('testDealerId')?.value;
    const companyNumber  = this.createSchedule.get('testCompanyNumber')?.value;
    const testStoreCode = this.createSchedule.get('testStoreCode')?.value;
    const testGroupCode = this.createSchedule.get('testGroupCode')?.value;
    const testserverName = this.createSchedule.get('testServerName')?.value;
    const parentName = `${testGroupCode}_${testStoreCode}[${enterpriseCode},${companyNumber},${testserverName}]`;

    let dealerAddress = this.dealerAddressProcessor ? this.dealerAddressProcessor.replace(/\n/g, "~") : '';
    dealerAddress = dealerAddress ? dealerAddress.replace(/,/g, "|") : '';
    const serverName = testserverName;
    let projectId = '12345678'

    const mageManufacturer = 'testManufacturer';
    // console.log(mageManufacturer);

    //Get second projectId from local storage
    // const storeTemp =this.createSchedule.get('store')?.value;
    // console.log('Store temp**********************2',storeTemp);
    // let tempProject = storeTemp.map(store => store.projectId);
    // let tempSecond = storeTemp.map(store => store.secondProjectId);
    // let companyIdList = storeTemp.map(store => store.companyID);
    // let projectIds ='';
    // let secondProjectIdList ='';

    // for(let i=0;i<tempProject.length;i++){
    //   projectIds+=tempProject[i]+'*';
    // }
    // for(let i=0;i<tempSecond.length;i++){
    //   if(tempSecond[i]!=undefined){
    //   secondProjectIdList+=tempSecond[i]+'*';
    //   }
    // }
   
    // let dealerIdList = storeTemp.map(store => store.thirdPartyUsername);
    // let enterpriseCodeList = storeTemp.map(store => store.enterpriseCode);
    // let serverNameList = storeTemp.map(store => store.serverName);
        
    // let allDealerIdSame =  this.areAllDealerIdSame(dealerIdList,enterpriseCodeList,serverNameList);
    // console.log('should schedule ',allDealerIdSame);
    let secondProjectId = "";
    // if (this.createSchedule.get('store')?.value[0].hasOwnProperty('secondProjectId')) {
    //   secondProjectId = this.createSchedule.get('store')?.value[0].secondProjectId;
    // }
    const parentID = '123456'
    const companyId: number = parseInt(parentID);

    // for(let i=0;i<companyIdList.length;i++){
    //   this.getSecondProjectId(companyIdList[i],(result)=>{
    //    if(result){
    //      if (result[1]) {
    //        if (result[1].hasOwnProperty('project_id')) {
    //          secondProjectIdList+=result[1].project_id.toString()+'*';
    //        }
    //      }
    //      if (result[0]) {
    //       if (result[0].hasOwnProperty('project_id')) {
    //         projectIds+=result[0].project_id.toString()+'*';
    //       }
    //     }
    //    }
    //   })
    // }



    if(this.couponAndDiscountFile){
      await this.uploadDiscountFile();
    }
    
    this.getSecondProjectId(companyId, (result:any) => {
      // console.log('Ready for fetch project ID');
      // console.log('+++++++++++++++++++++++++++++++++++++++++++');
      // console.log(result);
      // console.log('+++++++++++++++++++++++++++++++++++++++++++');
      //Override projectId and secondProjectId from api
      // if (result) {
      //   if (result[0]) {
      //     if (result[0].hasOwnProperty('project_id')) {
      //       projectId = result[0].project_id.toString();
      //     }
      //   }
      //   if (result[1]) {
      //     if (result[1].hasOwnProperty('project_id')) {
      //       secondProjectId = result[1].project_id.toString();
      //     }
      //   }
      // }

      // console.log('projectId:', projectId);
      // console.log('secondProjectId:', secondProjectId);

      let s: string = moment(new Date(this.dateInput.start)).format(this.SchedulerConstantService.DATE_FORMAT);
      let e: string = moment(new Date(this.dateInput.end)).format(this.SchedulerConstantService.DATE_FORMAT);
      let dateRangeCompare = s + ' - ' + e
      let isExist = this.checkJobExistInExtractionQueue(testStoreCode, enterpriseCode, dateRangeCompare);
      if (isExist) {
        swal({
          title: this.SchedulerConstantService.JOB_STARTED,
          type: 'warning',
          confirmButtonClass: 'btn-warning pointer',
          confirmButtonText: this.constantService.CLOSE
        });
      } else {
        // if(!allDealerIdSame){
        //   toastrService.success ('Please select stores with same enterpriseCode,companyNumber and server name', {
        //     cssClass: 'flashfade alert-danger flash-message',
        //     timeout: 3000
        //   });
        // }
        if (true) {
          let activityData = {
            activityName: "Manage DealerTrack",
            activityType: "Create New Schedule",
            activityDescription: `Create New Schedule for Group ${testGroupCode} (Group: ${testGroupCode}, Store: ${testStoreCode})`
          };
          let companyObj: any = [];
          this.commonService.saveActivity('Manage DealerTrack', activityData);
          let roOptionValue = this.createSchedule.get('roOption')?.value;
          let jobType = this.createSchedule.get('jobType')?.value;
          let solve360Update;
          let buildProxies;
          let skipErrorCount: number;
          solve360Update = this.createSchedule.get('updateRetreiveROinSolve360')?.value;
          buildProxies = this.createSchedule.get('buildProxies')?.value;
          skipErrorCount = +this.createSchedule.get('skipError')?.value;
          if(isNaN(skipErrorCount) || skipErrorCount == undefined || skipErrorCount == null || !skipErrorCount){
            skipErrorCount = 0;
          }

          // console.log('+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++');
          // console.log('this.coupon_and_discount_file_path:',this.coupon_and_discount_file_path);
          // console.log('+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++');
          // return false;
        
          roOptionValue = roOptionValue ? roOptionValue : this.SchedulerConstantService.DEFAULT_RO_OPTION;
          jobType = jobType ? jobType : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
          this.EventEmitterService.displayProgress.emit(this.SchedulerConstantService.EVENT_EMITTER.STEP_1);
          let scheduleDate = '';
          scheduleDate = moment().format(this.SchedulerConstantService.DATE_FORMAT);
          let startDate: string = moment(new Date(this.dateInput.start)).format(this.SchedulerConstantService.DATE_FORMAT);
          let endDate: string = moment(new Date(this.dateInput.end)).format(this.SchedulerConstantService.DATE_FORMAT);
          let date = new Date();
          let dateArray: any = scheduleDate.split("-");
          date.setMonth((dateArray[0] * 1) - 1);
          date.setDate(dateArray[1] * 1);
          date.setFullYear(dateArray[2] * 1)
          let now_utc = Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),
            date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());
          const now_utcOP = new Date(now_utc);
          let now_utcOutPut = now_utcOP.toUTCString();

          if(enterpriseCode === undefined || !enterpriseCode || enterpriseCode == null){
            this.EventEmitterService.displayProgress.emit('');
            NProgress.done();
            const messageValidate = 'Please provide third party username for data pull ';
            this.showStatusMessage(messageValidate, 'failure');
            return false;
          }else{
              if (!this.hasNoSpecialCharacters(enterpriseCode)) {
                    console.log('The string contains special characters.');
                    this.EventEmitterService.displayProgress.emit('');
                       NProgress.done();
                     const messageValidate = 'Please provide third party username without Special Characters ';
                     this.showStatusMessage(messageValidate, 'failure');
                      return false;
               } 
        }

          if(testGroupCode === undefined || !testGroupCode || testGroupCode == null){
             this.EventEmitterService.displayProgress.emit('');
             NProgress.done();
             const messageValidate = 'Please provide Mage Group Code  for data pull ';
             this.showStatusMessage(messageValidate, 'failure');
            return false;
          }else{
             
            if (!this.hasNoSpecialCharacters(testGroupCode)) {
               console.log('Mage Group code  string contains special characters.');

              this.EventEmitterService.displayProgress.emit('');
              NProgress.done();
              const messageValidate = 'Please provide Mage Group Code without Special Characters ';
              this.showStatusMessage(messageValidate, 'failure');
              return false;
            } 
         }

      
      if(testStoreCode === undefined || !testStoreCode || testStoreCode == null){
        this.EventEmitterService.displayProgress.emit('');
        NProgress.done();
        const messageValidate = 'Please provide Mage Store Code  for data pull ';
        this.showStatusMessage(messageValidate, 'failure');
        return false;
    }else{
      if (!this.hasNoSpecialCharacters(testStoreCode)) {
      
      this.EventEmitterService.displayProgress.emit('');
      NProgress.done();
      const messageValidate = 'Please provide Mage Store Code  without special characters ';
      this.showStatusMessage(messageValidate, 'failure');
      return false;
    }
  }

          const scheduleObj: any = {
            jobSchedule: moment(now_utcOutPut).toISOString(),
            jobData: {
              groupName:testGroupCode,
              storeDataArray:
              {
                enterpriseCode: enterpriseCode,// Static enterpriseCode for DealerTrack API testing purpose.
                projectId: (projectId === undefined) ? "" : projectId,
                secondProjectId: (secondProjectId === undefined) ? "" : secondProjectId,
                mageManufacturer: mageManufacturer,
                solve360Update: false,
                buildProxies: false,
                userName: userName,
                startDate: startDate,
                endDate: endDate,
                closedROOption: roOptionValue,
                jobType: jobType,
                mageGroupCode: testGroupCode,
                mageStoreCode: testStoreCode,
                stateCode: 'AL',
                companyNumber: companyNumber,
                serverName: serverName,
                dealerAddress: dealerAddress,
                skipErrorCount: skipErrorCount,
                coupon_and_discountCSVFilePath: this.coupon_and_discount_file_path,
                projectIds:'12345678',
                secondProjectIdList:'12345678',
                testData:true,
                companyIds:'0',
                companyObj: JSON.stringify(companyObj),
                parentName:parentName,
                brands:"test*test"
              }
            }
          };

          let activityDataSchedule = {
            activityName: "BE call to save agenda objects",
            activityType: "Save DealerTrack Jobs",
            activityDescription: `DealerTrack Schedule object is: ${JSON.stringify(scheduleObj)})`,

          };
          this.commonService.saveActivity('Manage DealerTrack', activityDataSchedule);

          let self = this;
          this.apollo.use('manageDealerTrackSchedule').mutate({
            mutation: createNewDealerTrack,
            variables: scheduleObj,
          }).subscribe(( listdata: any ) => {
            NProgress.done();
            const result: any = listdata.data;
            const status = result.scheduleDealerTrackExtractJob.status;
            let message = '';
            message = result.scheduleDealerTrackExtractJob.message;
            if (status) {
              this.solve360ServerDecider = false;
              this.switchServer();
              this.isTestServer(false);
              this.EventEmitterService.displayProgress.emit(this.SchedulerConstantService.EVENT_EMITTER.STEP_SAVE_SCHEDULE);
              this.refreshScheduleList();
              setTimeout(function () {
                self.EventEmitterService.displayProgress.emit('');
                self.showStatusMessage(message, 'success');
              }, 3000)
              this.createSchedule.reset();
              this.createSchedule.patchValue({
                updateRetreiveROinSolve360: true,
                buildProxies: true,
                skipError: this.constantService.DEFAULT_SKIP_COUNT,
                archiveDecider: false
              });
              this.createSchedule.patchValue({
                roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
                jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE
              });
              this.storeFilterList = [];
              // this.selectMultiDateRangeOption = !this.selectMultiDateRangeOption;
              this.selectMultiDateRangeOption = true;
              this.setDateRange(true, '');
              // this.dateInput = {
              //   start: this.startDateSelection(),
              //   end: this.endDateSelection(),
              // };
              this.dateInput = {
                start: this.selectedRange[0],
                end: this.selectedRange[1],
              };
              this.isTestSchedule = false;
              this.createSchedule.patchValue({
                roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
                jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE
              });
            } else {
              self.EventEmitterService.displayProgress.emit('');
            }
          },
            (err: Error) => {
              this.EventEmitterService.displayProgress.emit('');
              NProgress.done();
              const message = this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
              this.showStatusMessage(message, 'failure');
              this.commonService.errorCallback(err, this);
            }
          );
        } else {
          this.validateAllFormFields(this.createSchedule);
        }
      }
      return;
    });

  }
  hasNoSpecialCharacters(inputString: string): boolean {
    const regex = /^[a-zA-Z0-9 _]*$/;  
    return regex.test(inputString);
  }

  areAllDealerIdSame(
    dealerIdList: any,
    enterpriseCodeList: any,
    serverNameList: any
  ) {
    const referenceValue = dealerIdList[0];
    const enterPriceReference = enterpriseCodeList[0];
    const serverNameReference = serverNameList[0];
    const allDealerIdSame = dealerIdList.every(
      (element: any) => element === referenceValue
    );
    const allEnterPriceCodeSame = enterpriseCodeList.every(
      (element: any) => element === enterPriceReference
    );
    const allServerNameSame = serverNameList.every(
      (element: any) => element === serverNameReference
    );

    console.log("allDealerIdSame", allDealerIdSame);
    console.log("allEnterPriceCodeSame", allEnterPriceCodeSame);
    console.log("allServerNameSame", allServerNameSame);

    return allDealerIdSame && allEnterPriceCodeSame && allServerNameSame;
  }

  getSecondProjectId(companyId: any, cb: any) {
    this.commonService.getSecondProjectId(companyId, (result: any) => {
      if (cb) {
        cb(result);
      }
    });
  }

  validateAllFormFields(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach((field) => {
      const control = formGroup.get(field);
      if (control instanceof FormControl) {
        control.markAsTouched({ onlySelf: true });
      } else if (control instanceof FormGroup) {
        this.validateAllFormFields(control);
      }
    });
  }

  isFieldValidCreateSchedule(field: string) {
    let retValue: any = null;
    retValue =
      !this.createSchedule.get(field)?.valid &&
      this.createSchedule.get(field)?.touched;
    return retValue;
  }

  displayFieldCssCreateSchedule(field: string) {
    return {
      "has-danger": this.isFieldValidCreateSchedule(field),
    };
  }

  /**
   * flash message style set for success and error
   *
   */
  showStatusMessage(message: any, statusType: any) {
    if (statusType === "success") {
      this.toastrService.success (message);
    } else {
      this.toastrService.error(message);
    }
  }

  timeFormat(timeString: any) {
    var hourEnd = timeString.indexOf(":");
    var H = +timeString.substr(0, hourEnd);
    var h = H % 12 || 12;
    var ampm = H < 12 ? " AM" : " PM";
    timeString = h + timeString.substr(hourEnd, 3) + ampm;
    return timeString;
  }

  getAllJobs(callback: any) {
    this.compareObjArray = [];
    this.processQueueList = [];
    this.processQueueListCompleted = [];
    const allStoreGroupsList = this.apollo
      .use("manageDealerTrackSchedule")
      .query({
        query: getAllDealerTrackExtractJobs,
        fetchPolicy: "network-only",
      }).pipe(takeUntil(this.subscription$)).subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          console.log("Extraction que------------------------------------listdata",listdata);
          let obj: any = {};
          this.processQueueList = [];
          this.processQueueListCompleted = [];
          this.compareObjArray = [];
          this.poolTime =
            result["data"]["getAllDealerTrackExtractJobs"]["poolTime"];
          $.each(
            result["data"]["getAllDealerTrackExtractJobs"]["jobArray"],
            (key: any, val: any) => {
              let groupName = val.data.groupName;
              let scheduledDate : any = val.nextRunAt
                ? val.nextRunAt
                : val.lastRunAt
                ? val.lastRunAt
                : null;
                console.log("Extraction que---------------------->scheduledDate",scheduledDate);

              let date : any = "";
              scheduledDate
                ? (date = moment
                    .unix(scheduledDate / 1000)
                    .format("MM-DD-YYYY HH:mm:ss"))
                : null;
                console.log("Extraction que---------------------->date",date);

              let nextRunAt = "";
              val.nextRunAt
                ? (nextRunAt = moment.unix(val.nextRunAt / 1000).fromNow())
                : null;
              let groupStatus: any = null;
              groupStatus = val;
              let failedReason = val.failReason;
              $.each(val.data["storeDataArray"], (key: any, val: any) => {
                obj = {};
                let jobStartDate = null;
                let jobEndDate = null;
                let status = null;
                let jobType = null;
                let seperator = "/";
                let solve360Update;
                let buildProxies;
                let skipErrorCount;
                let projectId;
                let secondProjectId;
                let coupon_and_discountCSVFilePath;
                let mageManufacturer;
                let projectIds;
                let secondProjectIdList;
                let companyObj;
                let testData;
                let companyIds;
                let parentName;
                let processFileName;
                if (val.startDate && val.startDate.includes("-")) {
                  seperator = "-";
                }
                status = val.status;
                const dateArray = val.startDate.split(seperator);
                const startDate =
                  dateArray[0] + "-" + dateArray[1] + "-" + dateArray[2];
                if (val.endDate.includes("-")) {
                  seperator = "-";
                }
                const dateArrayEnd = val.endDate.split(seperator);
                const ClosedROOption = val.closedROOption;
                const endtDate =
                  dateArrayEnd[0] +
                  "-" +
                  dateArrayEnd[1] +
                  "-" +
                  dateArrayEnd[2];
                let range = startDate + " - " + endtDate;
                jobStartDate = val.startTime;
                jobEndDate = val.endTime;
                jobType = val.jobType;
                solve360Update = val.solve360Update;
                buildProxies = val.buildProxies;
                skipErrorCount = val.skipErrorCount;
                projectId = val.projectId;
                secondProjectId = val.secondProjectId;
                coupon_and_discountCSVFilePath =
                  val.coupon_and_discountCSVFilePath;
                mageManufacturer = val.mageManufacturer;
                projectIds = val.projectIds;
                testData = val.testData;
                secondProjectIdList = val.secondProjectIdList;
                companyObj = val.companyObj;
                companyIds = val.companyIds;
                parentName = val.parentName;
                processFileName = val.processFileName;
                let jobStatus = false;
                let statusFlag = null;
                if (jobStartDate && jobEndDate && status) {
                  jobStatus = true;
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.COMPLETED;
                } else if (jobStartDate && !jobEndDate) {
                  // console.log('++++++++++++++++++++++++++++++++++++++++++++++++++++++++++');
                  // console.log(val);
                  // console.log('groupName:', groupName);
                  // console.log('enterpriseCode:', val.enterpriseCode);
                  // console.log('++++++++++++++++++++++++++++++++++++++++++++++++++++++++++');
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.RUNNING;
                } else if (!jobStartDate && !jobEndDate) {
                  // console.log('------------------------------------------------------------');
                  // console.log(val);
                  // console.log('groupName:', groupName);
                  // console.log('enterpriseCode:', val.enterpriseCode);
                  // console.log('------------------------------------------------------------');
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.SCHEDULED;
                } else if (jobStartDate && jobEndDate && !status) {
                  jobStatus = true;
                  statusFlag = this.SchedulerConstantService.STATUS_FLAG.FAILED;
                  failedReason = val.message;
                } else {
                  statusFlag = this.getJobStatus(groupStatus);
                }
                obj = {
                  groupName: groupName,
                  store: val.enterpriseCode,
                  range: range,
                  date: date,
                  status: statusFlag,
                  override: this.SchedulerConstantService.RUN_NOW,
                  failedReason: failedReason,
                  nextRunAt: nextRunAt,
                  jobStartDate: jobStartDate,
                  jobEndDate: jobEndDate,
                  ClosedROOption: ClosedROOption,
                  jobType: jobType,
                  companyNumber: val.companyNumber,
                  storeName: val.mageStoreCode,
                  dealerAddress: val.dealerAddress,
                  solve360Update: solve360Update,
                  buildProxies: buildProxies,
                  serverName: val.serverName,
                  projectId: projectId,
                  secondProjectId: secondProjectId,
                  skipErrorCount: skipErrorCount,
                  coupon_and_discountCSVFilePath:
                  coupon_and_discountCSVFilePath,
                  mageManufacturer: mageManufacturer,
                  projectIds: projectIds,
                  secondProjectIdList: secondProjectIdList,
                  testData:testData,
                  companyObj:companyObj,
		              companyIds:companyIds,
                  parentName:parentName,
                  processFileName:processFileName
                
                };
                console.log("Extraction que------------------------------------obj",obj,jobStatus);
                if (
                  jobStatus &&
                  (statusFlag ===
                    this.SchedulerConstantService.STATUS_FLAG.COMPLETED ||
                    statusFlag ==
                      this.SchedulerConstantService.STATUS_FLAG.FAILED)
                ) {
                  this.processQueueListCompleted.push(obj);
                  console.log("Extraction que------------------------------------processQueueListCompleted",this.processQueueListCompleted);

                  this.compareObjArray.push(obj);
                } else {
                  this.processQueueList.push(obj);
                  console.log("Extraction que------------------------------------processQueueList",this.processQueueList);

                }
              });
            }
          );
          callback();
        },
        error: (err: Error) => {
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }
  getDealerIdFromStoreName(sName: any) {
    let storeFilterListCopy: any[] = [];
    storeFilterListCopy = Object.assign([], this.storeGroupList);
    storeFilterListCopy = storeFilterListCopy
      .filter((item: any, index: number) => index < storeFilterListCopy.length)
      .filter((item: any, index: number) => item.companyName === sName);
    let dealerId = "";
    if (storeFilterListCopy.length) {
      dealerId = storeFilterListCopy[0].thirdPartyUsername;
    }
    return dealerId;
  }

  getStoreNameFromDealerId(dealerID: any) {
    let storeFilterListCopy: any[] = [];
    storeFilterListCopy = Object.assign([], this.jobGroupList);
    storeFilterListCopy = storeFilterListCopy
      .filter((item: any, index: number) => index < storeFilterListCopy.length)
      .filter(
        (item: any, index: number) => item.thirdPartyUsername === dealerID
      );
    let dealerId = "";
    if (storeFilterListCopy.length) {
      dealerId = storeFilterListCopy[0].companyName;
    }
    return dealerId;
  }
  /**
   * showSouceBundleDetails function will show the Scheduled Process
   *
   */
  showScheduledProcessList(loaderStatus: any) {
    console.log("Extraction que------------------------------------")
    console.log("Extraction que------------------------------------processQueueList",this.processQueueList);
    loaderStatus ? (this.scheduleProcessQueueLoading = true) : "";
    console.log("Extraction que-----------------------------------scheduleProcessQueueLoading-")
    if ($("#scheduleProcessQueueDealerTrack").data('datatable')) {
      $("#scheduleProcessQueueDealerTrack").dataTable().fnDestroy();
    }
    table1 = $("#scheduleProcessQueueDealerTrack").dataTable().fnClearTable();
    const elm = this;
    console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$elm$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",elm);
    let i = 0;
    setTimeout(() => {
      $(document).ready(function () {
        table1 = $("#scheduleProcessQueueDealerTrack").dataTable({
          language: {
            decimal: ".",
            thousands: ",",
          },
          columnDefs: [
            { type: "numeric-comma", targets: "_all" },
            { orderable: false, targets: [5] },
            { orderable: true, targets: [0, 1, 2, 3] },
          ],
          fixedHeader: {
            header: true,
            footer: true,
            headerOffset: $(".cat__top-bar").outerHeight() - 11,
          },
          bSort: false,
          order: [0, "asc"],
          responsive: true,
          scrollX: false,
          scrollY: "200px",
          destroy: true,
          paging: true,
          deferRender: true,
          ordering: true,
          info: true,
          filter: true,
          length: true,
          processing: true,
          lengthMenu: [
            [50, 25, 10, 5],
            [50, 25, 10, 5],
          ],
          autoWidth: false,
          fnRowCallback: function (settings: any, aData: any) {
            const pagination = $(this)
              .closest(".dataTables_wrapper")
              .find(".dataTables_paginate");
            pagination.toggle(this.api().page.info().pages > 1);
          },
          drawCallback: function (settings: any) {
            table1 = $("#scheduleProcessQueueDealerTrack").DataTable();
            $("td:eq(1)", settings).css("width", "24%");
            $("td:eq(1)", settings).css("width", "10%");
            $("td:eq(2)", settings).css("width", "13%");
            $("td:eq(3)", settings).css("width", "23%");
            $("td:eq(4)", settings).css("width", "15%");
            $("td:eq(5)", settings).css("width", "15%");
            var api = this.api();
            var rows = api.rows({ page: "current" }).nodes();
            var last: any = null;
            api
              .column(0, { page: "current" })
              .data()
              .each(function (group: any, i: any) {
                if (last !== group) {
                  $(rows)
                    .eq(i)
                    .before(
                      '<tr class="group"><td colspan="8" style="BACKGROUND-COLOR:rgb(86, 85, 78);font-weight:700;color:#f5f5f5;">' +
                        "Store Group: " +
                        group +
                        "</td></tr>"
                    );
                  last = group;
                }
              });
          },
          columns: [
            {
              title: "Store",
              width: "24%",
              className: "dt-head-left",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                console.log("row data%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%",rowData);
                let dealerId = elm.getDealerIdFromStoreName(rowData[1]);
                let storeName = elm.getStoreNameFromDealerId(rowData[1]);
                storeName = rowData[28] ? rowData[28] : rowData[1];
                let groupName = rowData[13];
                let mageStoreCode = rowData[17];
                let toolTipForDealerId = "";
                let toolTipForJobType = "";
                if (dealerId) {
                  toolTipForDealerId = "DealerId: " + dealerId;
                }
                if (rowData[11]) {
                  toolTipForJobType = "Job Type: " + rowData[11];
                }
                let cursorStyle = toolTipForDealerId ? 'cursor:pointer;' : '';
                let tooltipAttributes = toolTipForDealerId ? `data-toggle="tooltip" data-placement="top" title="${toolTipForDealerId}" data-animation="false"` : '';
                data =
                  `<span style="${cursorStyle}" ${tooltipAttributes} data-info="${rowData}">
                    ${storeName} ${mageStoreCode}
                  </span>`;
                return data !== null ? data : null;
              },
            },
            {
              title: "Extraction Type",
              width: "13%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                let toolTipForJobType = "";
                rowData[11] = rowData[11] ? rowData[11].charAt(0).toUpperCase() + rowData[11].slice(1): "";
                if (rowData[11]) {
                  toolTipForJobType = rowData[11];
                }
                data =
                  '<span  data-placement="top" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  toolTipForJobType +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Data Extraction Range",
              width: "13%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                data =
                  '<span data-placement="top" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  rowData[2] +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Schedule Date",
              width: "23%",
              type: "formatted-num",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                console.log("RowData>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>@@@@@@@@@@@@@@@@@@@@",rowData);
                let toolTipTitle = "";
                if (rowData[4] == "Locked") {
                  toolTipTitle = elm.SchedulerConstantService.LOCKED_MESSAGE;
                }
                if (rowData[4] == "Running") {
                  toolTipTitle = rowData[4];
                }
                let b = rowData[3].split(" ");
                let r = b[0].split("-");
                let op = r[2] + "-" + r[0] + "-" + r[1] + " " + b[1];
                let scheduleDateDisplay = moment(op).format(
                  elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                );
                let scheduleDateOp = scheduleDateDisplay
                  ? scheduleDateDisplay
                  : rowData[3];
                  console.log("scheduleDateOp************************************************************************",scheduleDateOp);
                data =
                  '<span data-placement="top" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  scheduleDateOp +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Status",
              width: "15%",
              type: "formatted-alphabet",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                var className = "";
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.COMPLETED
                  ? (className = "label-success")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.SCHEDULED
                  ? (className = "label-scheduled")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.RUNNING
                  ? (className = "label-running")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.REPEATING
                  ? (className = "label-repeating")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.FAILED
                  ? (className = "label-failed")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.QUEUED
                  ? (className = "label-queued")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.LOCKED
                  ? (className = "label-locked")
                  : null;
                if (rowData[4] === "Rescheduled") {
                  data =
                    '<span style="float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="label label-scheduled">Scheduled</span>';
                  data +=
                    '<span aria-hidden="true" data-toggle="tooltip" data-placement="top" title="' +
                    rowData[6] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '" style="margin-left:5px;" class="label label-failed">Failed</span>';
                } else {
                  data =
                    '<span style="float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="label ' +
                    className +
                    '">' +
                    rowData[4] +
                    "</span>";
                }
                return data;
              },
            },
            {
              title: "Actions",
              width: "15%",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                const d = data;
                let rowData = [];
                rowData = Object.assign([], rows);
                const cancelSchedule = "Cancel Schedule";
                const overrideSchedule = "Run Now";
                if (
                  rowData[4] === "Completed" ||
                  rowData[4] === "Locked" ||
                  // rowData[4] === "Running" ||
                  rowData[4] === "Queued"
                ) {
                  data =
                    '<a style="font-size: 18px;" class="overrideScheduleCancel" style="color: #b9b5b5;cursor: not-allowed;" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-play-circle overrideScheduleCancel"  data-toggle="tooltip" data-placement="top" title="' +
                    rowData[4] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  style="color: #b9b5b5;cursor: not-allowed;"></i></a>';
                  data +=
                    '<a style="margin: 18px;font-size: 18px;" class="scheduleCancel" style="color: #b9b5b5;cursor: not-allowed;" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-ban scheduleCancel"  data-toggle="tooltip" data-placement="top" title="' +
                    rowData[4] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '" style="color: #b9b5b5;cursor: not-allowed;" ></i></a>';
                }else if(rowData[4] === "Running"){
                  // data =
                  // '<a style="margin: 18px;font-size: 18px;" class="cancelSchedule" href="javascript:void(0);" data-note="' +
                  // d +
                  // '">' </a>';
                  // '<i aria-hidden="true" class="fa fa-ban cancelSchedule"  data-toggle="tooltip" data-placement="top" title="' +
                  // cancelSchedule +
                  // '" data-animation="false" data-info="' +
                  // '' +
                  // '"  ></i>

                   data = `
                    <a style="margin: 18px; font-size: 18px;" class="cancelSchedule" href="javascript:void(0);" data-note="${d}">
                      </a>
                     `;

                 
                 }else {
                  // data =
                  //   '<a style="font-size: 18px;" class="overrideSchedule" href="javascript:void(0);" data-note="' +
                  //   d +
                  //   '">' +
                  //   '<i aria-hidden="true" class="fa fa-play-circle overrideSchedule"  data-toggle="tooltip" data-placement="top" title="' +
                  //   overrideSchedule +
                  //   '" data-animation="false" data-info="' +
                  //   rowData +
                  //   '"  ></i></a>';
                  data =
                    '<a style="margin: 18px;font-size: 18px;" class="cancelSchedule" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-ban cancelSchedule"  data-toggle="tooltip" data-placement="top" title="' +
                    cancelSchedule +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  ></i></a>';
                }
                return data;
              },
            },
          ],
          rowGroup: {
            dataSrc: "Store Group",
          },
        });
        // tslint:disable-next-line:no-unused-expression
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
          "alphanumeric-sort-asc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "alphanumeric-sort-desc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? 1 : x > y ? -1 : 0;
          },
          "formatted-num-pre": function (a: any) {
            a = a === "-" || a === "" ? 0 : a.replace(/[^\d\-\.]/g, "");
            return parseFloat(a);
          },
          "formatted-num-asc": function (a: any, b: any) {
            return a - b;
          },
          "formatted-num-desc": function (a: any, b: any) {
            return b - a;
          },
          "formatted-alphabet-asc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "formatted-alphabet-desc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? 1 : x > y ? -1 : 0;
          },
        });
        elm.reDrawScheduleTable(elm.processQueueList, loaderStatus);
      });
    }, 200);
  }

  /**
   * reDrawPortalStoreTable function will redraw the datatable using new table values
   *
   */
  reDrawScheduleTable(temp: any, loaderStatus: any) {
    console.log("Extraction que------------------------------------reDrawScheduleTable temp",temp);
    table1 = $("#scheduleProcessQueueDealerTrack").DataTable();
    table1.search("").draw();
    table1 = $("#scheduleProcessQueueDealerTrack").dataTable();
    table1.fnClearTable();
    table1 = $("#scheduleProcessQueueDealerTrack").dataTable();
    const tempArr = [];
    for (let i = 0; i < temp.length; i++) {
      let rpt = [];
      const t = temp[i];
      rpt = [
        t.groupName,
        t.store,
        t.range,
        t.date,
        t.status,
        t.override,
        t.failedReason,
        t.nextRunAt,
        t.ClosedROOption,
        t.jobStartDate,
        t.jobEndDate,
        t.jobType,
        t.companyNumber,
        t.projectId,
        t.solve360Update,
        t.buildProxies,
        t.serverName,
        t.storeName,
        t.secondProjectId,
        t.skipErrorCount,
        t.coupon_and_discountCSVFilePath,
        t.mageManufacturer,
        t.projectIds,
        t.secondProjectIdList,
        t.testData,        
        t.companyObj,
        t.companyIds,
        t.priority,
        t.parentName,
        t.processFileName
      ];
      tempArr.push(rpt);
    }
    if (tempArr.length > 0) {
      table1.fnAddData(tempArr, false); // Add new data
    }
    table1.fnDraw(); // Redraw the DataTable
    loaderStatus ? (this.scheduleProcessQueueLoading = false) : "";
    if (temp.length > 0) {
      setTimeout(() => {
        $("#scheduleProcessQueueDealerTrack")
          .DataTable()
          .columns.adjust()
          .draw(false);
      }, 100);
    }
  }

  getToolTipInfoCompletedJobs(rowData: any) {
    let toolTip = "";
    let className = "";
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.COMPLETED
      ? (className = "label-success")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.SCHEDULED
      ? (className = "label-scheduled")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.RUNNING
      ? (className = "label-running")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.REPEATING
      ? (className = "label-repeating")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.FAILED
      ? (className = "label-failed")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.QUEUED
      ? (className = "label-queued")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.LOCKED
      ? (className = "label-locked")
      : null;
    status = '<span class="label ' + className + '">' + rowData[4] + "</span>";
    if (rowData[3]) {
      let b = rowData[3].split(" ");
      let r = b[0].split("-");
      let op = r[2] + "-" + r[0] + "-" + r[1] + " " + b[1];
      const time = moment.duration(
        `00:${this.poolTime ? this.poolTime : "00"}:00`
      );
      let scheduleDateDisplay = moment(op)
        .subtract(time)
        .format(this.SchedulerConstantService.SCHEDULE_DATE_FORMAT);
      let scheduleDateOp = scheduleDateDisplay
        ? scheduleDateDisplay
        : rowData[3];
      toolTip += "Scheduled Date: " + scheduleDateOp + " \n";
    }
    toolTip += "Last Run At: " + rowData[3] + " \n";
    if (rowData[6]) {
      toolTip +=
        "Extraction Start Time: " +
        moment(rowData[6] * 1).format(
          this.SchedulerConstantService.SCHEDULE_DATE_FORMAT
        ) +
        " \n";
    }
    if (rowData[7]) {
      toolTip +=
        "Extraction End Time: " +
        moment(rowData[7] * 1).format(
          this.SchedulerConstantService.SCHEDULE_DATE_FORMAT
        ) +
        " \n";
    }
    toolTip += "Status: " + status + " \n";
    if (rowData[5] && rowData[4] === "Failed") {
      toolTip += "Failed Reason: " + rowData[5] + " \n";
    }
    return toolTip;
  }

  /**
   * showScheduledProcessListCompleted function will show the Scheduled Process
   *
   */
  showScheduledProcessListCompleted(loaderStatus: any) {
    loaderStatus ? (this.scheduleProcessQueueCompletedLoading = true) : "";
    if ($("#scheduleProcessCompleted").data('datatable')) {
      $("#scheduleProcessCompleted").dataTable().fnDestroy();
    }
    table1 = $("#scheduleProcessCompleted").dataTable().fnClearTable();
    const elm = this;
    let i = 0;
    setTimeout(() => {
      $(document).ready(function () {
        table1 = $("#scheduleProcessCompleted").dataTable({
          language: {
            decimal: ".",
            thousands: ",",
          },
          columnDefs: [{ type: "numeric-comma", targets: "_all" }],
          fixedHeader: {
            header: true,
            footer: true,
            headerOffset: $(".cat__top-bar").outerHeight() - 11,
          },
          bSort: false,
          order: [4, "desc"],
          responsive: true,
          scrollX: false,
          destroy: true,
          paging: true,
          deferRender: true,
          ordering: true,
          info: true,
          filter: true,
          length: true,
          processing: true,
          lengthMenu: [
            [50, 25, 10, 5],
            [50, 25, 10, 5],
          ],
          autoWidth: false,
          scrollY: "200px",
          // initialization params as usual
          fnRowCallback: function (settings: any, aData: any) {
            const pagination = $(this)
              .closest(".dataTables_wrapper")
              .find(".dataTables_paginate");
            pagination.toggle(this.api().page.info().pages > 1);
          },
          drawCallback: function (settings: any) {
            table1 = $("#scheduleProcessQueueDealerTrack").DataTable();
            $("td:eq(1)", settings).css("width", "28%");
            $("td:eq(2)", settings).css("width", "17%");
            $("td:eq(3)", settings).css("width", "25%");
            $("td:eq(4)", settings).css("width", "20%");
            $("td:eq(5)", settings).css("width", "10%");
            var api1 = this.api();
            var rows1 = api1.rows({ page: "current" }).nodes();
            var last: any = null;

            api1
              .column(0, { page: "current" })
              .data()
              .each(function (group: any, i: any) {
                if (last !== group) {
                  $(rows1)
                    .eq(i)
                    .before(
                      '<tr class="group"><td colspan="8" style="BACKGROUND-COLOR:rgb(86, 85, 78);font-weight:700;color:#f5f5f5;">' +
                        "Store Group: " +
                        group +
                        "</td></tr>"
                    );
                  last = group;
                }
              });
          },
          columns: [
            {
              title: "Store",
              width: "28%",
              className: "dt-head-left",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                let storeName = elm.getStoreNameFromDealerId(rowData[1]);
                let enterpriseCode = rowData[1];
                let mageStoreCode = rowData[9];
                storeName = rowData[9] ? rowData[9] : rowData[1];
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[1] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  enterpriseCode +
                  " " +
                  mageStoreCode +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Extraction Type",
              width: "17%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                let toolTipForJobType = "";
                rowData[8] = rowData[8] ? rowData[8].charAt(0).toUpperCase() + rowData[8].slice(1) : "";
                if (rowData[8]) {
                  toolTipForJobType = rowData[8];
                }
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[8] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  toolTipForJobType +
                  "</span>";
                return toolTipForJobType !== "" ? data : null;
              },
            },
            {
              title: "Data Extraction Range",
              width: "25%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[2] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  rowData[2] +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Completed Date",
              width: "20%",
              type: "formatted-date",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                let completedDate = "";
                let tempDate = new Date(rowData[3]);
                rowData[6] = tempDate.getTime()
                let tempDate1 = new Date(rowData[7]);
                rowData[7] = tempDate1.getTime();
                console.log("Completed date$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",rowData);
                if (rowData[3]) {
                  let time = rowData[3].split(" ");
                  let HourFormat = elm.convertTimeFormat(time[1]);
                  completedDate = time[0] + " " + HourFormat;
                }
                if (rowData[9]) {
                  completedDate = moment(rowData[7] * 1).format(
                    elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                  );
                }
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  completedDate +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  completedDate +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Status",
              width: "10%",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                var className = "";
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.COMPLETED
                  ? (className = "label-success")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.SCHEDULED
                  ? (className = "label-scheduled")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.RUNNING
                  ? (className = "label-running")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.REPEATING
                  ? (className = "label-repeating")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.FAILED
                  ? (className = "label-failed")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.QUEUED
                  ? (className = "label-queued")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.LOCKED
                  ? (className = "label-locked")
                  : null;
                let toolTip = "";
                if (rowData[3]) {
                  let b = rowData[3].split(" ");
                  let r = b[0].split("-");
                  let op = r[2] + "-" + r[0] + "-" + r[1] + " " + b[1];
                  const time = moment.duration(
                    `00:${elm.poolTime ? elm.poolTime : "00"}:00`
                  );
                  let scheduleDateDisplay = moment(op)
                    .subtract(time)
                    .format(elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT);
                  let scheduleDateOp = scheduleDateDisplay
                    ? scheduleDateDisplay
                    : rowData[3];
                  toolTip += "Scheduled Date: " + scheduleDateOp + " \n";
                }
                toolTip += "Last Run At: " + rowData[3] + " \n";
                if (rowData[6]) {
                  toolTip +=
                    "Extraction Start Time: " +
                    moment(rowData[6] * 1).format(
                      elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                    ) +
                    " \n";
                }
                if (rowData[7]) {
                  toolTip +=
                    "Extraction End Time: " +
                    moment(rowData[7] * 1).format(
                      elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                    ) +
                    " \n";
                }
                toolTip += "Status: " + rowData[4] + " \n";
                if (rowData[5] && rowData[4] === "Failed") {
                  toolTip +=
                    "Failed Reason: " + rowData[5]
                      ? rowData[5].replace(/[^a-zA-Z ]/g, " ")
                      : "" + " \n";
                }
                rowData[5] = rowData[5]
                  ? rowData[5].replace(/[^a-zA-Z ]/g, " ")
                  : "";
                if (
                  rowData[4] === "Failed" &&
                  rowData[5] === elm.SchedulerConstantService.EXCEED_TIME
                ) {
                  rowData[4] = "Completed";
                  className = "label-success";
                }
                data =
                  '<span style="cursor:pointer;float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="popUpInfoCompletedJobs label ' +
                  className +
                  '"  data-toggle="tooltip" data-placement="top" title="' +
                  toolTip +
                  '" data-info="' +
                  rowData +
                  '">' +
                  rowData[4] +
                  "</span>";
                if (rowData[5]) {
                  data =
                    '<span style="cursor:pointer;float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="popUpInfoCompletedJobs label ' +
                    className +
                    '" data-toggle="tooltip" data-placement="top" title="' +
                    toolTip +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '">' +
                    rowData[4] +
                    "</span>";
                }

                return data;
              },
            },
            {
              title: "",
              width: "5%",
              className: "dt-head-left pl-0",
              render: function (data: any, type: any, rows: any, meta: any) {
                const d = data;
                let rowData = [];
                rowData = Object.assign([], rows);
                const enableReRunFlag = rowData[11];
                console.log(
                  "?????????????????????????????????????????????????rowdata",
                  rowData
                );
                if (
                  rowData[2] ===
                    elm.SchedulerConstantService.STATUS_FLAG.COMPLETED &&
                  enableReRunFlag && rowData[20] !== false
                ) {
                  const updateParentGroup = "Re-Run";
                  data =
                    '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-caret-square-o-up text-success mt-2 fetchPayTypeDetails" style="font-size: 18px;color: #516e84;"  data-toggle="tooltip" data-placement="top" title="' +
                    updateParentGroup +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  ></i></a>';
                } else {
                  const updateParentGroup = "Resume";
                  if (rowData[2] == "HALT") {
                    data =
                      '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                      d +
                      '">' +
                      '<i aria-hidden="true" class="fa fa-caret-square-o-up text-success mt-2 haltAndResumeDetails" style="font-size: 18px;color: #80047d !important;"  data-toggle="tooltip" data-placement="top" title="' +
                      updateParentGroup +
                      '" data-animation="false" data-info="' +
                      rowData +
                      '"  ></i></a>';
                  }  else if(rowData[4]=='Completed'){
                    let updateParentGroup = 'Re-process Store';
                
                    data = '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                        + '<i aria-hidden="true" class="fa fa-upload text-primary mt-2 requeue" style="font-size: 18px; margin-right: 10px;" data-toggle="tooltip" data-placement="top" title="'
                        + updateParentGroup + '" data-animation="false" data-info="' + rowData + '" ></i></a>';
                    // data += '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                    //     + '<i aria-hidden="true" class="fa fa-trash text-danger mt-2 removeQueueItem" style="font-size: 18px;" data-toggle="tooltip" data-placement="top" title="'
                    //     + updateParentGroup1 + '" data-animation="false" data-info="' + rowData + '" ></i></a>';
                  } else {
                    data = "";
                  }
                }
                return data;
              },
            },
          ],
        });
        // tslint:disable-next-line:no-unused-expression
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
          "alphanumeric-sort-asc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "alphanumeric-sort-desc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? 1 : x > y ? -1 : 0;
          },
          "formatted-date-asc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a ? new Date(a).getTime() : 0;
            const y = b ? new Date(b).getTime() : 0;
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "formatted-date-desc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a ? new Date(a.toString()).getTime() : 0;
            const y = b ? new Date(b.toString()).getTime() : 0;
            return x < y ? 1 : x > y ? -1 : 0;
          },
        });
        elm.reDrawScheduleTableCompleted(
          elm.processQueueListCompleted,
          loaderStatus
        );
      });
    }, 200);
  }

  /**
   * reDrawScheduleTableCompleted function will redraw the datatable using new table values
   *
   */
  reDrawScheduleTableCompleted(temp: any, loaderStatus: any) {
    table1 = $("#scheduleProcessCompleted").DataTable();
    table1.search("").draw();
    table1 = $("#scheduleProcessCompleted").dataTable();
    table1.fnClearTable();
    table1 = $("#scheduleProcessCompleted").dataTable();

    const tempArr = [];
    for (let i = 0; i < temp.length; i++) {
      const t = temp[i];
      const rpt = [
        t.groupName,
        t.store,
        t.range,
        t.date,
        t.status,
        t.failedReason,
        t.jobStartDate,
        t.jobEndDate,
        t.jobType,
        t.storeName,
        t.processFileName
      ];
      tempArr.push(rpt);
    }
    if (tempArr.length > 0) {
      table1.fnAddData(tempArr, false); // Add new data
    }
    table1.fnDraw(); // Redraw the DataTable
    loaderStatus ? (this.scheduleProcessQueueCompletedLoading = false) : "";
    if (temp.length > 0) {
      setTimeout(() => {
        $("#scheduleProcessCompleted").DataTable().columns.adjust().draw(false);
      }, 100);
    }
  }

  refreshScheduleList() {
    this.closeToolTip();
    this.loadingSchedule = true;
    this.getAllJobs(() => {
      this.showScheduledProcessList(true);
      this.loadingSchedule = false;
    });
  }

  refreshScheduleListCompleted() {
    this.closeToolTip();
    this.loadingScheduleCompleted = true;
    this.getAllJobs(() => {
      this.showScheduledProcessListCompleted(true);
      this.loadingScheduleCompleted = false;
    });
  }

  refreshProcessJsonList() {
    this.closeToolTip();
    this.loadingProcessJson = true;
    this.getAllProcessJsonJobs(() => {
      this.showProcessJsonList();
      this.loadingProcessJson = false;
    });
  }

  runNowSchedule(data: any) {
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      var userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
    }
    console.log("row data before split>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",data);
    let rowData = data.split(",");
    console.log("row data>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>DT",rowData);
    if(rowData[24] == "false"){
      const thridPartyUserName = rowData[12].toString();
      let filteredData: any = Object.assign([], this.jobGroupList);
      let resObj = filteredData.filter(
        (item: any) => item.thirdPartyUsername === thridPartyUserName
      );
      let mageGrpCode = "";
      let mageStrCode = "";
      let steCode = "";
      let dealerAddress = "";
      let companyNumber = rowData[12];
      let projectId = "";
      let secondProjectId = "";
      let coupon_and_discountCSVFile: any;
      let mageManufacturer: any;
      let projectIds: any;
      let secondProjectIdList: any;    
      let testData: any;
      let companyIds: any;
      let futureDate;
      let companyObj:any;
      if (resObj.length) {
        mageGrpCode = resObj[0].mageGroupCode;
        mageStrCode = resObj[0].mageStoreCode;
        steCode = resObj[0].state;
        dealerAddress =
          (resObj[0].companyName ? resObj[0].companyName : "") +
          "," +
          (resObj[0].address ? resObj[0].address.replace(/'|'/g, ",") : "");
        projectId = resObj[0].projectId;
      }
      mageGrpCode = rowData[0];
      mageStrCode = rowData[17];
      steCode = rowData[25];
      dealerAddress = dealerAddress ?dealerAddress :rowData[0];
      projectId = rowData[13];
  
      var date = moment(rowData[3]);
      var now = moment();
      if (now > date) {
        swal({
          title: this.SchedulerConstantService.JOB_STARTED,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
        this.refreshScheduleList();
      } else {
        swal(
          {
            title: this.constantService.AREYOUSURE,
            text: this.SchedulerConstantService.RUN_SCHEDULE_NOW,
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default pointer",
            confirmButtonClass: "btn-warning pointer",
            confirmButtonText: "Run",
            closeOnConfirm: true,
            showLoaderOnConfirm: true,
          },
          () => {
            this.EventEmitterService.displayProgress.emit(
              this.SchedulerConstantService.EVENT_EMITTER.STEP1
            );
            let rowData = data.split(",");
            const groupName = rowData[0];
            let activityData = {
              activityName: "Manage DealerTrack",
              activityType: "RunNow DealerTrack ",
              activityDescription: `Run Schedule for Group ${groupName} (Group: ${mageGrpCode}, Store: ${mageStrCode})`,
            };
            this.commonService.saveActivity("Manage DealerTrack", activityData);
            const enterpriseCode = rowData[1].toString();
            let serverName = rowData[16].trim();
            // let jobSchedule: any = moment(
            //   rowData[3],
            //   this.SchedulerConstantService.DATE_FORMAT
            // );
            let jobSchedule: any = moment(
              rowData[3]
          ).format('MM-DD-YYYY HH:mm:ss');
            let scheduleOpSplit = jobSchedule.split(" ");
            let scheduleOp = scheduleOpSplit[0].split("-");
            jobSchedule =
              scheduleOp[2] +
              "-" +
              scheduleOp[0] +
              "-" +
              scheduleOp[1] +
              " " +
              scheduleOpSplit[1];
            const dateRange = rowData[2].split(" - ");
            const startDate = dateRange[0].trim();
            const endDate = dateRange[1].trim();
            let date = new Date(jobSchedule);
            const closedRoOption = rowData[8];
            let jobType = rowData[11]
              ? rowData[11]
              : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
            let solve360Update = rowData[14].toLowerCase() == "true";
            let buildProxies = rowData[15].toLowerCase() == "true";
            if (rowData[13]) {
              projectId = rowData[13].toString();
            }
            if (rowData[18]) {
              secondProjectId = rowData[18].toString();
            }

            let skipErrorCount: number = +rowData[19];

            if (rowData[20]) {
              coupon_and_discountCSVFile = rowData[20].toString();
            }

            if (rowData[21]) {
              mageManufacturer = rowData[21].toString();
            }

            if (rowData[22]) {
              projectIds = rowData[22].toString();
            }

            if (rowData[23]) {
              secondProjectIdList = rowData[23].toString();
            }

            if(rowData[24]){
              testData =Boolean(rowData[24]);
            }

            // if(rowData[25]){
            //   stateCode =Boolean(rowData[24]);
            // }


            if(rowData[25]){
              companyObj = rowData[25];
            }

            if(rowData[26]){
              companyIds = rowData[26];
            }
            
            if(rowData[27]){
              futureDate = rowData[27];
            }
            // console.log('+++++++++++++++++++++++++++++++++++++++++++++');
            // console.log(JSON.stringify(rowData));
            // console.log('+++++++++++++++++++++++++++++++++++++++++++++');

            console.log("projectId:", projectId);
            console.log("secondProjectId:", secondProjectId);
            console.log(
              "coupon_and_discountCSVFile:",
              coupon_and_discountCSVFile
            );
            console.log("mageManufacturer:", mageManufacturer);

            let now_utc = Date.UTC(
              date.getUTCFullYear(),
              date.getUTCMonth(),
              date.getUTCDate(),
              date.getUTCHours(),
              date.getUTCMinutes(),
              date.getUTCSeconds()
            );
            const now_utcOP = new Date(now_utc);
            const scheduleObj: any = {
              jobSchedule: moment(now_utcOP).toISOString(),
              jobData: {
                groupName: groupName.trim(),
                storeData: {
                  enterpriseCode: enterpriseCode,
                  projectId: projectId,
                  secondProjectId: secondProjectId,
                  mageManufacturer: mageManufacturer,
                  solve360Update: solve360Update,
                  buildProxies: buildProxies,
                  userName: userName,
                  startDate: startDate,
                  endDate: endDate,
                  closedROOption: closedRoOption,
                  jobType: jobType,
                  mageGroupCode: mageGrpCode,
                  mageStoreCode: mageStrCode,
                  stateCode: steCode ? steCode :'St',
                  companyNumber: companyNumber,
                  serverName: serverName,
                  dealerAddress: dealerAddress,
                  skipErrorCount: skipErrorCount,
                  coupon_and_discountCSVFilePath: coupon_and_discountCSVFile,
                  projectIds: projectIds,
                  secondProjectIdList: secondProjectIdList,
                  companyIds:companyIds,
                  testData:testData,
                  parentName:mageStrCode,
                  companyObj:companyObj
                },
              },
            };
            let self = this;
            this.apollo
              .use("manageDealerTrackSchedule")
              .mutate({
                mutation: runNowDealerTrackExtractJobByStore,
                variables: scheduleObj,
              }).pipe(takeUntil(this.subscription$)).subscribe({
                next: (listdata: any) => {
                  NProgress.done();
                  const result: any = listdata.data;
                  const status = result.runNowDealerTrackExtractJobByStore.status;
                  const message =
                    result.runNowDealerTrackExtractJobByStore.message;
                  if (status) {
                    this.EventEmitterService.displayProgress.emit(
                      this.SchedulerConstantService.EVENT_EMITTER.STEP_RELOAD_LIST
                    );
                    this.refreshScheduleList();
                    setTimeout(function () {
                      self.refreshScheduleList();
                      self.EventEmitterService.displayProgress.emit("");
                      self.showStatusMessage(message, "success");
                    }, 3000);
                  } else {
                    self.EventEmitterService.displayProgress.emit("");
                  }
                },
                error: (err: Error) => {
                  this.EventEmitterService.displayProgress.emit("");
                  NProgress.done();
                  const message =
                    this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                  this.showStatusMessage(message, "failure");
                  this.commonService.errorCallback(err, this);
                },
                complete: () => {
                  console.log("Completed");
                },
              });
          }
        );
      }
    }else{
      const thridPartyUserName = rowData[12].toString();
      // let filteredData: any = Object.assign([], this.jobGroupList);
      // let resObj = filteredData.filter((item) => item.thirdPartyUsername === thridPartyUserName)
      let mageGrpCode = "";
      let mageStrCode = "";
      let steCode = "";
      let dealerAddress = "";
      let companyNumber = rowData[12];
      let projectId = "";
      let secondProjectId = "";
      let coupon_and_discountCSVFile: any;
      let mageManufacturer: any ;
      let projectIds: any;
      let secondProjectIdList: any;
      let testData: any;
      let stateCode;     
      let futureDate: any ;
      // if (resObj.length) {
      //   mageGrpCode = resObj[0].mageGroupCode;
      //   mageStrCode = resObj[0].mageStoreCode;
      //   steCode = resObj[0].state;
      //   dealerAddress =(resObj[0].companyName?resObj[0].companyName:'')+','+(resObj[0].address?(resObj[0].address).replace(/'|'/g, ","):'');
      //   projectId = resObj[0].projectId;
      // }

      mageGrpCode = rowData[0];
      mageStrCode = rowData[17];
      steCode = rowData[25];
      dealerAddress = dealerAddress ?dealerAddress :rowData[0];
      projectId = rowData[13];
      
      var date = moment(rowData[3])
      var now = moment();
      if (now > date) {
        swal({
          title: this.SchedulerConstantService.JOB_STARTED,
          type: 'warning',
          confirmButtonClass: 'btn-warning pointer',
          confirmButtonText: this.constantService.CLOSE
        });
        this.refreshScheduleList();
      } else {
        swal({
          title: this.constantService.AREYOUSURE,
          text: this.SchedulerConstantService.RUN_SCHEDULE_NOW,
          type: 'warning',
          showCancelButton: true,
          cancelButtonClass: 'btn-default pointer',
          confirmButtonClass: 'btn-warning pointer',
          confirmButtonText: 'Run',
          closeOnConfirm: true,
          showLoaderOnConfirm: true
        },
        () => {
            this.EventEmitterService.displayProgress.emit(this.SchedulerConstantService.EVENT_EMITTER.STEP1);
            let rowData = data.split(",");
            console.log('row data>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>',rowData);
            const groupName = rowData[0];
            let activityData = {
              activityName: "Manage DealerTrack",
              activityType: "RunNow DealerTrack ",
              activityDescription: `Run Schedule for Group ${groupName} (Group: ${mageGrpCode}, Store: ${mageStrCode})`
            };
            this.commonService.saveActivity('Manage DealerTrack', activityData);
            const enterpriseCode = rowData[1].toString();
            let serverName = rowData[16].trim();
            // let jobSchedule: any = moment(rowData[3], this.SchedulerConstantService.DATE_FORMAT);
            let jobSchedule: any = moment(
              rowData[3]
           ).format('MM-DD-YYYY HH:mm:ss');
            let scheduleOpSplit = jobSchedule.split(" ");
            let scheduleOp = scheduleOpSplit[0].split("-");
            jobSchedule = scheduleOp[2] + '-' + scheduleOp[0] + '-' + scheduleOp[1] + ' ' + scheduleOpSplit[1];
            const dateRange = rowData[2].split(" - ");
            const startDate = dateRange[0].trim();
            const endDate = dateRange[1].trim();
            let date = new Date(jobSchedule);
            const closedRoOption = rowData[8];
            let jobType = rowData[11] ? rowData[11] : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
            let solve360Update = rowData[14].toLowerCase() == 'true';
            let buildProxies = rowData[15].toLowerCase() == 'true';
            if (rowData[13]) {
              projectId = rowData[13].toString();
            }
            if (rowData[18]) {
              secondProjectId = rowData[18].toString();
            }

            let skipErrorCount: number = +rowData[19];

            if(rowData[20]){
              coupon_and_discountCSVFile = rowData[20].toString();
            }

            if(rowData[21]){
              mageManufacturer = rowData[21].toString();
            }

            if(rowData[22]){
              projectIds = rowData[22].toString();
            }
        
            if(rowData[23]){
              secondProjectIdList = rowData[23].toString();
            }


            if(rowData[24]){
              testData =Boolean(rowData[24]);
            }  

              if(rowData[27]){
              futureDate = rowData[27];
              }

            // console.log('+++++++++++++++++++++++++++++++++++++++++++++');
            // console.log(JSON.stringify(rowData));
            // console.log('+++++++++++++++++++++++++++++++++++++++++++++');

            console.log('projectId:', projectId);
            console.log('secondProjectId:', secondProjectId);
            console.log('coupon_and_discountCSVFile:', coupon_and_discountCSVFile);
            console.log('mageManufacturer:', mageManufacturer);

            let now_utc = Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),
              date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());
            const now_utcOP = new Date(now_utc);
            const scheduleObj: any = {
              jobSchedule: moment(now_utcOP).toISOString(),
              jobData: {
                groupName: groupName.trim(),
                storeData:
                {
                  enterpriseCode: enterpriseCode,
                  projectId: projectId,
                  secondProjectId: secondProjectId,
                  mageManufacturer: mageManufacturer,
                  solve360Update: solve360Update,
                  buildProxies: buildProxies,
                  userName: userName,
                  startDate: startDate,
                  endDate: endDate,
                  closedROOption: closedRoOption,
                  jobType: jobType,
                  mageGroupCode: mageGrpCode,
                  mageStoreCode: mageStrCode,
                  stateCode: steCode,
                  companyNumber: companyNumber,
                  serverName: serverName,
                  dealerAddress: dealerAddress,
                  skipErrorCount: skipErrorCount,
                  coupon_and_discountCSVFilePath: coupon_and_discountCSVFile,
                  projectIds:projectIds,
                  secondProjectIdList:secondProjectIdList,
                  testData:testData,
                  companyIds:'0',
                  futureDate:futureDate
                }
              }
            };
            let self = this;
            this.apollo.use('manageDealerTrackSchedule').mutate({
              mutation: runNowDealerTrackExtractJobByStore,
              variables: scheduleObj,
            }).subscribe(( data: any ) => {
              NProgress.done();
              const result: any = data.data;
              const status = result.runNowDealerTrackExtractJobByStore.status;
              const message = result.runNowDealerTrackExtractJobByStore.message;
              if (status) {
                this.EventEmitterService.displayProgress.emit(this.SchedulerConstantService.EVENT_EMITTER.STEP_RELOAD_LIST);
                this.refreshScheduleList();
                setTimeout(function () {
                  self.refreshScheduleList();
                  self.EventEmitterService.displayProgress.emit('');
                  self.showStatusMessage(message, 'success');
                }, 3000);
              } else {
                self.EventEmitterService.displayProgress.emit('');
              }
            },
              (err: Error) => {
                this.EventEmitterService.displayProgress.emit('');
                NProgress.done();
                const message = this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, 'failure');
                this.commonService.errorCallback(err, this);
              },
             // complete: () => {
             //   console.log("Completed");
            //  },
            );
          });
          
        }
    }
  }   

  cancelSchedule(data: any) {
    let userName: any;
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
    }

    let rowData = data.split(",");
    const thridPartyUserName = rowData[1].toString();
    let filteredData: any = Object.assign([], this.jobGroupList);
    let resObj = filteredData.filter(
      (item: any) => item.thirdPartyUsername === thridPartyUserName
    );
    let mageGrpCode = "";
    let mageStrCode = "";
    let steCode = "";
    let companyNumber = rowData[12];
    let projectId = "";
    let secondProjectId = "";
    let coupon_and_discountCSVFile: any;
    let mageManufacturer: any;

    if (resObj.length) {
      mageGrpCode = resObj[0].mageGroupCode;
      mageStrCode = resObj[0].mageStoreCode;
      steCode = resObj[0].state;
      projectId = resObj[0].projectId;
    }
    var date = moment(rowData[3]);
    var now = moment();

    if (now > date) {
      swal({
        title: this.SchedulerConstantService.JOB_STARTED,
        type: "warning",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: this.constantService.CLOSE,
      });
      this.refreshScheduleList();
    } else {
      swal(
        {
          title: this.constantService.AREYOUSURE,
          text: this.SchedulerConstantService.CANCEL_SCHEDULE_NOW,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default pointer",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: "Continue",
          closeOnConfirm: true,
          showLoaderOnConfirm: true,
        },
        () => {
          this.EventEmitterService.displayProgress.emit(
            this.SchedulerConstantService.EVENT_EMITTER.CANCEL_SCHEDULE
          );
          let rowData = data.split(",");
          let jobType = this.createSchedule.get("jobType")?.value;
          const groupName = rowData[0];
          let activityData = {
            activityName: "Manage DealerTrack",
            activityType: "Cancel DealerTrack ",
            activityDescription: `Cancel Schedule for Group - ${groupName} (Group: ${mageGrpCode}, Store: ${mageStrCode})`,
          };
          this.commonService.saveActivity("Manage DealerTrack", activityData);
          const enterpriseCode = rowData[1].toString();
          let serverName = rowData[16].trim();
          // let jobSchedule: any = moment(
          //   rowData[3],
          //   this.SchedulerConstantService.DATE_FORMAT
          // );
          let jobSchedule: any = moment(
            rowData[3]
         ).format('MM-DD-YYYY HH:mm:ss');
          let scheduleOpSplit = jobSchedule.split(" ");
          let scheduleOp = scheduleOpSplit[0].split("-");
          jobSchedule =
            scheduleOp[2] +
            "-" +
            scheduleOp[0] +
            "-" +
            scheduleOp[1] +
            " " +
            scheduleOpSplit[1];

          const dateRange = rowData[2].split(" - ");
          const startDate = dateRange[0].trim();
          const endDate = dateRange[1].trim();
          const closedRoOption = rowData[8];

          let solve360Update = rowData[14].toLowerCase() == "true";
          let buildProxies = rowData[15].toLowerCase() == "true";

          if (rowData[13]) {
            projectId = rowData[13].toString();
          }
          if (rowData[18]) {
            secondProjectId = rowData[18].toString();
          }

          let skipErrorCount: number = +rowData[19];

          if (rowData[20]) {
            coupon_and_discountCSVFile = rowData[20].toString();
          }

          if (rowData[21]) {
            mageManufacturer = rowData[21].toString();
          }

          console.log("projectId:", projectId);
          console.log("secondProjectId:", secondProjectId);
          console.log(
            "coupon_and_discountCSVFile:",
            coupon_and_discountCSVFile
          );
          console.log("mageManufacturer:", mageManufacturer);

          let date = new Date(jobSchedule);
          let now_utc = Date.UTC(
            date.getUTCFullYear(),
            date.getUTCMonth(),
            date.getUTCDate(),
            date.getUTCHours(),
            date.getUTCMinutes(),
            date.getUTCSeconds()
          );
          const now_utcOP = new Date(now_utc);
          const scheduleObj: any = {
            jobSchedule: moment(now_utcOP).toISOString(),
            jobData: {
              groupName: groupName,
              storeData: {
                enterpriseCode: enterpriseCode,
                projectId: projectId,
                secondProjectId: secondProjectId,
                mageManufacturer: mageManufacturer,
                solve360Update: solve360Update,
                buildProxies: buildProxies,
                userName: userName,
                startDate: startDate,
                endDate: endDate,
                closedROOption: closedRoOption,
                jobType: jobType,
                mageGroupCode: mageGrpCode,
                mageStoreCode: mageStrCode,
                stateCode: steCode,
                companyNumber: companyNumber,
                serverName: serverName,
                skipErrorCount: skipErrorCount,
                coupon_and_discountCSVFilePath: coupon_and_discountCSVFile,
              },
            },
          };
          let self = this;
          this.apollo
            .use("manageDealerTrackSchedule")
            .mutate({
              mutation: cancelDealerTrackExtractJobByStore,
              variables: scheduleObj,
            }).pipe(takeUntil(this.subscription$)).subscribe({
              next: (listdata: any) => {
                NProgress.done();
                const result: any = listdata.data;
                const status = result.cancelDealerTrackExtractJobByStore.status;
                const message =
                  result.cancelDealerTrackExtractJobByStore.message;
                if (status) {
                  this.EventEmitterService.displayProgress.emit(
                    this.SchedulerConstantService.EVENT_EMITTER.CANCEL_SCHEDULE
                  );
                  this.refreshScheduleList();
                  setTimeout(function () {
                    self.EventEmitterService.displayProgress.emit("");
                    self.showStatusMessage(message, "success");
                  }, 3000);
                } else {
                  self.EventEmitterService.displayProgress.emit("");
                }
              },
              error: (err: Error) => {
                this.EventEmitterService.displayProgress.emit("");
                NProgress.done();
                const message =
                  this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, "failure");
                this.commonService.errorCallback(err, this);
              },
              complete: () => {
                console.log("Completed");
              },
            });
        }
      );
    }
  }

  processQueueToggle() {
    this.processQueueListCollapsed = !this.processQueueListCollapsed;
    if (!this.processQueueListCollapsed) {
      this.setActiveFixedHeader();
    }
  }

  completedJobsToggle() {
    this.completedListCollapsed = !this.completedListCollapsed;
    if (!this.completedListCollapsed) {
      this.refreshScheduleListCompleted();
      this.completedProcessjsonListCollapsed = true;
      this.setActiveFixedHeader();
    }
  }

  processJsonToggle() {
    this.completedProcessjsonListCollapsed =
      !this.completedProcessjsonListCollapsed;
    if (!this.completedProcessjsonListCollapsed) {
      this.completedListCollapsed = true;
      this.refreshProcessJsonList();
      this.setActiveFixedHeader();
    }
  }

  getAllProcessJsonJobs(callback: any) {
    this.processJsonListArray = [];
    const allStoreGroupsList = this.apollo
      .use("manageDealerTrackSchedule")
      .query({
        query: getAllDealerTrackProcessJSONJobs,
        fetchPolicy: "network-only",
      }).pipe(takeUntil(this.subscription$)).subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          let obj: any = {};
          this.processJsonListArray = [];
          if (
            result["data"]["getAllDealerTrackProcessJSONJobs"] &&
            result["data"]["getAllDealerTrackProcessJSONJobs"][
              "processJSONJobsQueue"
            ]
          ) {
            $.each(
              result["data"]["getAllDealerTrackProcessJSONJobs"][
                "processJSONJobsQueue"
              ],
              (key: any, val: any) => {
                let storeNameFromInputFilePath = val.fileToProcess
                  .split("/")
                  .reverse()[0];
                let storeNameFromInputFileName = "";
                storeNameFromInputFileName =
                  storeNameFromInputFilePath.split("-")[1];
                let groupName = "test";
                let date = "";
                let nextRunAt = "";
                let statusFlag =
                  this.SchedulerConstantService.STATUS_FLAG.QUEUED;
                obj = {
                  groupName: groupName,
                  storeID: val.storeID,
                  statusFlag: statusFlag,
                  lastRunAt: null,
                  fileProcessed: null,
                  outputFile: null,
                  message: null,
                  failedReason: null,
                  nextRunAt: nextRunAt,
                  statusInfo: "",
                  uniqueId: "",
                  storeName: storeNameFromInputFileName,
                  errorWarnningMessage: "",
                  reRun: false,
                  priority:val.priority
                };
                obj.statusInfo =
                  "File To Process: " + val.fileToProcess + " \n";
                this.processJsonListArray.push(obj);
                console.log("processJsonListArray",this.processJsonListArray)
              }
            );
          }
          if (
            result["data"]["getAllDealerTrackProcessJSONJobs"] &&
            result["data"]["getAllDealerTrackProcessJSONJobs"][
              "processJSONJobs"
            ]
          ) {
            $.each(
              result["data"]["getAllDealerTrackProcessJSONJobs"][
                "processJSONJobs"
              ],
              (key: any, val: any) => {
                let groupName = "test";
                let scheduledDate = val.nextRunAt
                  ? val.nextRunAt
                  : val.lastRunAt
                  ? val.lastRunAt
                  : null;
                let date = "";
                scheduledDate
                  ? (date = moment
                      .unix(scheduledDate / 1000)
                      .format("MM-DD-YYYY HH:mm:ss"))
                  : null;
                let nextRunAt = "";
                val.nextRunAt
                  ? (nextRunAt = moment.unix(val.nextRunAt / 1000).fromNow())
                  : null;
                let statusFlag = this.getJobStatus(val);

                // console.log('++++++++++++++++++++++++++++++++++++');
                // console.log('val.data.message:',val.data.message);
                // console.log('++++++++++++++++++++++++++++++++++++');

                // let statusMessage = val.failReason;
                // let haltIdentifier = false;
                // if (statusMessage == "Error: Halt") {
                //   haltIdentifier = true;
                // }

                // if (haltIdentifier) {
                //   statusFlag = "HALT";
                //   val.failReason =
                //     "Process is hold, due to some exception occured";
                // }

                // let failedReason = val.failReason;
                // if (failedReason) {
                //   failedReason = failedReason.replace(/,/g, "; ");
                // }
                let failReason: string =  '';
                let statusMessage = val.failReason;
                let haltIdentifier = false;
                if (statusMessage == "Error: Halt") {
                  haltIdentifier = true;
                }

                if (haltIdentifier) {
                  statusFlag = "HALT";
                  failReason =
                    "Process is hold, due to some exception occured";
                }

                let failedReason = failReason ? failReason : val.failReason;
                if (failedReason) {
                  failedReason = failedReason.replace(/,/g, "; ");
                }
                console.log("processJsonListArray============failedReason",failedReason)

                const uniqueId = val._id;
                val.scheduled && val.failed
                  ? (statusFlag =
                      this.SchedulerConstantService.STATUS_FLAG.RESCHEDULED)
                  : null;
                let storeName = "";
                if (val.data.storeID && val.data.inputFile) {
                  let sp1 = val.data.inputFile.split(val.data.storeID);
                  storeName = sp1 && sp1.length ? sp1[0].split("-")[1] : "";
                }

                let roAccountDescriptionCount;
                let company_no_not_matching_count;
                let grouped_team_work_count;
                let address;
                let isResumed;
                let isAlreadyResumed;
                let suffixedInvoicesCsvData;
                let new_line_type_count;
                let negative_coupon_count;
                let labor_with_zero_sale_nonzero_cost_count;
                let gl_missing_ros_count;
                let coupon_discount_basis_amount_mismatch_exception_count;
                let labor_with_no_paytype_exception_count;
                let parts_excluded_from_history_exception_count;
                let lost_sale_parts_exception_count;
                let processorUniqueId;
                let priority;
                if (val.data) {
                  // console.log('******************************SHARK TRACK*********************************************')
                  if (val.data.hasOwnProperty("roAccountDescExceptionCount")) {
                    roAccountDescriptionCount =
                      val.data.roAccountDescExceptionCount;
                  }
                  if (val.data.hasOwnProperty("suffixedInvoicesCsvData")) {
                    suffixedInvoicesCsvData = val.data.suffixedInvoicesCsvData;
                  }

                  if (val.data.hasOwnProperty("new_line_type_count")) {
                    new_line_type_count = val.data.new_line_type_count;
                  }

                  if (val.data.hasOwnProperty("negative_coupon_count")) {
                    negative_coupon_count = val.data.negative_coupon_count;
                  }

                  if (
                    val.data.hasOwnProperty(
                      "labor_with_zero_sale_nonzero_cost_count"
                    )
                  ) {
                    labor_with_zero_sale_nonzero_cost_count =
                      val.data.labor_with_zero_sale_nonzero_cost_count;
                  }

                  if (val.data.hasOwnProperty("gl_missing_ros_count")) {
                    gl_missing_ros_count = val.data.gl_missing_ros_count;
                  }

                  if (
                    val.data.hasOwnProperty(
                      "coupon_discount_basis_amount_mismatch_exception_count"
                    )
                  ) {
                    coupon_discount_basis_amount_mismatch_exception_count =
                      val.data
                        .coupon_discount_basis_amount_mismatch_exception_count;
                  }

                  if (
                    val.data.hasOwnProperty(
                      "labor_with_no_paytype_exception_count"
                    )
                  ) {
                    labor_with_no_paytype_exception_count =
                      val.data.labor_with_no_paytype_exception_count;
                  }

                  if (
                    val.data.hasOwnProperty(
                      "parts_excluded_from_history_exception_count"
                    )
                  ) {
                    parts_excluded_from_history_exception_count =
                      val.data.parts_excluded_from_history_exception_count;
                  }

                  if (
                    val.data.hasOwnProperty("lost_sale_parts_exception_count")
                  ) {
                    lost_sale_parts_exception_count =
                      val.data.lost_sale_parts_exception_count;
                  }

                  if (
                    val.data.hasOwnProperty("processorUniqueId")
                  ) {
                    processorUniqueId =
                      val.data.processorUniqueId;
                  }
                }


                
                let coaExceptionWarningMessage;
                if (val.data) {
                  if (val.data.hasOwnProperty("warningMessage")) {
                    if (val.data.warningMessage) {
                      if (
                        val.data.warningMessage.hasOwnProperty(
                          "coaExceptionWarningMessage"
                        )
                      ) {
                        coaExceptionWarningMessage =
                          val.data.warningMessage.coaExceptionWarningMessage;
                      }
                    }
                  }
                }

                let customerExceptionWarningMessage;
                if (val.data) {
                  if (val.data.hasOwnProperty("warningMessage")) {
                    if (val.data.warningMessage) {
                      if (
                        val.data.warningMessage.hasOwnProperty(
                          "customerExceptionWarningMessage"
                        )
                      ) {
                        customerExceptionWarningMessage =
                          val.data.warningMessage
                            .customerExceptionWarningMessage;
                      }
                    }
                  }
                }

                // console.log('++++++++++++++++++++++++++++++++++++');
                // console.log('val.data.message:',val.data.message);
                // console.log('++++++++++++++++++++++++++++++++++++');

                // let statusMessage = val.data.message;
                // let haltIdentifier = false;
                // if(statusMessage == 'Halt'){
                //   haltIdentifier = true;
                // }

                // if(haltIdentifier){
                //   statusFlag = 'HALT';
                // }

                let parsedWarningMessageObj,
                  warningMessageString,
                  tmpCouponExceptionMessage;
                let tmpDescritionArray = [];

                if (val.data.hasOwnProperty("warningMessage")) {
                  if (
                    val.data.warningMessage &&
                    val.data.warningMessage != null &&
                    val.data.warningMessage != undefined
                  ) {
                    parsedWarningMessageObj = val.data.warningMessage;

                    if (
                      parsedWarningMessageObj.hasOwnProperty(
                        "errorwarningMessage"
                      )
                    ) {
                      if (parsedWarningMessageObj.errorwarningMessage) {
                        tmpDescritionArray =
                          parsedWarningMessageObj.errorwarningMessage;
                      }
                    }

                    if (tmpDescritionArray && tmpDescritionArray.length > 0) {
                      warningMessageString =
                        "Error in " + tmpDescritionArray.join("\n");
                    }

                    if (
                      parsedWarningMessageObj.hasOwnProperty(
                        "couponAndDiscountFileNotUploadedWarningMessage"
                      )
                    ) {
                      if (
                        parsedWarningMessageObj.couponAndDiscountFileNotUploadedWarningMessage
                      ) {
                        if (
                          parsedWarningMessageObj
                            .couponAndDiscountFileNotUploadedWarningMessage
                            .length > 0
                        ) {
                          if (warningMessageString) {
                            warningMessageString =
                              warningMessageString +
                              "\n" +
                              parsedWarningMessageObj.couponAndDiscountFileNotUploadedWarningMessage;
                          } else {
                            warningMessageString =
                              "\n" +
                              parsedWarningMessageObj.couponAndDiscountFileNotUploadedWarningMessage;
                          }
                        }
                      }
                    }

                    if (
                      parsedWarningMessageObj.hasOwnProperty(
                        "roAccountDescriptionWarningMessage"
                      )
                    ) {
                      if (
                        parsedWarningMessageObj.roAccountDescriptionWarningMessage
                      ) {
                        if (
                          parsedWarningMessageObj.roAccountDescriptionWarningMessage >
                          0
                        ) {
                          if (warningMessageString) {
                            warningMessageString =
                              warningMessageString +
                              "\n" +
                              "Customer name showing as acc description: " +
                              parsedWarningMessageObj.roAccountDescriptionWarningMessage;
                          } else {
                            warningMessageString =
                              "\n" +
                              "Customer name showing as acc description: " +
                              parsedWarningMessageObj.roAccountDescriptionWarningMessage;
                          }
                        }
                      }
                    }

                    if (
                      parsedWarningMessageObj.hasOwnProperty(
                        "couponAndDiscountWarningMessage"
                      )
                    ) {
                      if (
                        parsedWarningMessageObj.couponAndDiscountWarningMessage
                      ) {
                        tmpCouponExceptionMessage =
                          parsedWarningMessageObj.couponAndDiscountWarningMessage;
                        tmpCouponExceptionMessage =
                          tmpCouponExceptionMessage.replace("0", "");
                        tmpCouponExceptionMessage =
                          tmpCouponExceptionMessage.replace("99999", "");
                        tmpCouponExceptionMessage =
                          tmpCouponExceptionMessage.trim();
                        tmpCouponExceptionMessage =
                          tmpCouponExceptionMessage.replace(/^,|,$/g, "");
                        // tmpCouponExceptionMessage = tmpCouponExceptionMessage.replace(/,\s*$/, "");
                        if (tmpCouponExceptionMessage.trim().length > 0) {
                          if (warningMessageString) {
                            warningMessageString =
                              warningMessageString +
                              "\n" +
                              "Exceptions in coupons/discounts: " +
                              tmpCouponExceptionMessage;
                          } else {
                            warningMessageString =
                              "\n" +
                              "Exceptions in coupons/discounts: " +
                              tmpCouponExceptionMessage;
                          }
                        }
                      }
                    }

                    if (val.data) {
                      if (
                        val.data.hasOwnProperty("company_no_not_matching_count")
                      ) {
                        company_no_not_matching_count =
                          val.data.company_no_not_matching_count;
                      }

                      if (val.data.hasOwnProperty("grouped_team_work_count")) {
                        grouped_team_work_count =
                          val.data.grouped_team_work_count;
                      }

                      if (val.data.hasOwnProperty("new_line_type_count")) {
                        new_line_type_count = val.data.new_line_type_count;
                      }

                      if (val.data.hasOwnProperty("negative_coupon_count")) {
                        negative_coupon_count = val.data.negative_coupon_count;
                      }

                      if (
                        val.data.hasOwnProperty(
                          "labor_with_zero_sale_nonzero_cost_count"
                        )
                      ) {
                        labor_with_zero_sale_nonzero_cost_count =
                          val.data.labor_with_zero_sale_nonzero_cost_count;
                      }

                      if (val.data.hasOwnProperty("gl_missing_ros_count")) {
                        gl_missing_ros_count = val.data.gl_missing_ros_count;
                      }

                      if (
                        val.data.hasOwnProperty(
                          "coupon_discount_basis_amount_mismatch_exception_count"
                        )
                      ) {
                        coupon_discount_basis_amount_mismatch_exception_count =
                          val.data
                            .coupon_discount_basis_amount_mismatch_exception_count;
                      }

                      if (
                        val.data.hasOwnProperty(
                          "labor_with_no_paytype_exception_count"
                        )
                      ) {
                        labor_with_no_paytype_exception_count =
                          val.data.labor_with_no_paytype_exception_count;
                      }

                      if (
                        val.data.hasOwnProperty(
                          "parts_excluded_from_history_exception_count"
                        )
                      ) {
                        parts_excluded_from_history_exception_count =
                          val.data.parts_excluded_from_history_exception_count;
                      }

                      if (
                        val.data.hasOwnProperty(
                          "lost_sale_parts_exception_count"
                        )
                      ) {
                        lost_sale_parts_exception_count =
                          val.data.lost_sale_parts_exception_count;
                      }

                      if (
                        val.data.hasOwnProperty(
                          "processorUniqueId"
                        )
                      ) {
                        processorUniqueId =
                          val.data.processorUniqueId;
                      }

                      

                      if (val.data.hasOwnProperty("address")) {
                        address = val.data.address;
                      }

                      if (val.data.hasOwnProperty("isResumed")) {
                        isResumed = val.data.isResumed;
                      }

                      if (val.data.hasOwnProperty("isAlreadyResumed")) {
                        isAlreadyResumed = val.data.isAlreadyResumed;
                      }
                    }
                  }
                }

                let reRunFlag = true;
                let tempValue ='';
                console.log(Object.getOwnPropertyDescriptor(val.data, 'outputFile&&&&&&&&&&&&&&&&&&&&&&&&'));
                console.log("val.data#################################",val.data);
                if (val.data.inputFile &&val.data.inputFile.includes("-RERUN")) {
                  if ( val.data.outputFile != "" && val.data.outputFile != null)
                   {
                   
                    console.log("val.data.outputFile@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",val.data.outputFile);
                    let tmp = val.data.outputFile.split("&");
                    tempValue = tmp[0];
                  }
                  reRunFlag = false;
                }
                obj = {
                  groupName: groupName,
                  storeID: val.data.storeID,
                  statusFlag: statusFlag,
                  lastRunAt: date,
                  fileProcessed: val.inputFile,
                  outputFile: tempValue,
                  message: val.data.message,
                  failedReason: failedReason,
                  nextRunAt: nextRunAt,
                  statusInfo: "",
                  uniqueId: uniqueId,
                  storeName: val.data.storeName
                    ? val.data.storeName
                    : storeName,
                  errorWarnningMessage: warningMessageString,
                  roAccountDescriptionCount: roAccountDescriptionCount,
                  coaExceptionWarningMessage: coaExceptionWarningMessage,
                  customerExceptionWarningMessage:
                    customerExceptionWarningMessage,
                  company_no_not_matching_count: company_no_not_matching_count,
                  grouped_team_work_count: grouped_team_work_count,
                  reRun: reRunFlag,
                  address: address,
                  isResumed: isResumed,
                  isAlreadyResumed: isAlreadyResumed,
                  suffixedInvoicesCsvData: suffixedInvoicesCsvData,
                  new_line_type_count: new_line_type_count,
                  negative_coupon_count: negative_coupon_count,
                  labor_with_zero_sale_nonzero_cost_count:
                    labor_with_zero_sale_nonzero_cost_count,
                    gl_missing_ros_count: gl_missing_ros_count,
                    coupon_discount_basis_amount_mismatch_exception_count:
                    coupon_discount_basis_amount_mismatch_exception_count,
                    labor_with_no_paytype_exception_count:
                    labor_with_no_paytype_exception_count,
                    parts_excluded_from_history_exception_count:
                    parts_excluded_from_history_exception_count,
                    lost_sale_parts_exception_count:
                    lost_sale_parts_exception_count,
                    priority:val.data.priority,
		                uploadStatus: val.uploadStatus,
                    processorUniqueId:processorUniqueId
                };
                let updatedData = { ...val.data, uploadStatus: val.uploadStatus };
                obj.statusInfo = this.getStatusInfo(updatedData);
                if (
                  val.data.operation === "json-processing" &&
                  val.data.storeID
                ) {
                  this.processJsonListArray.push(obj);
                  console.log("processJsonListArray",this.processJsonListArray)
                }
                this.sortJSONArray(this.processJsonListArray);
              }
            );
          }
          callback();
        },
        error: (err: Error) => {
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  getStatusInfo(inputData: any) {
    let data = "";
    data += "Input File: " + inputData.inputFile + " \n";
    if (inputData.outputFile) {
      data += "Output File: " + inputData.outputFile + " \n";
    }
    let createdAt = null;
    if (inputData.createdAt && !isNaN(inputData.createdAt)) {
      let dt = inputData.createdAt;
      var y = dt.substr(0, 4);
      var t = dt.substr(4, dt.length);
      let remArray = t.match(/.{1,2}/g);
      remArray[0] = (remArray[0] * 1 - 1).toString();
      remArray.unshift(y);
      createdAt = moment(remArray).format("MM-DD-YYYY HH:mm:ss");
      data += "Created At: " + createdAt + " \n";
    }
    return data;
  }
  /**
   * showProcessJsonList function will show the Process JSON List
   *
   */
  showProcessJsonList() {
    this.processJSONLoading = true;
    $("#processJsonList").dataTable().fnDestroy();
    table1 = $("#processJsonList").dataTable().fnClearTable();
    const elm = this;
    let i = 0;
    setTimeout(() => {
      $(document).ready(function () {
        table1 = $("#processJsonList").dataTable({
          language: {
            decimal: ".",
            thousands: ",",
          },
          columnDefs: [
            { type: "numeric-comma", targets: "_all" },
            { orderable: false, targets: [3] },
            { orderable: true, targets: [0, 1, 2] },
          ],
          fixedHeader: {
            header: true,
            footer: true,
            headerOffset: $(".cat__top-bar").outerHeight() - 11,
          },
          bSort: false,
          order: [1, "desc"],
          responsive: true,
          scrollX: false,
          destroy: true,
          paging: true,
          deferRender: true,
          ordering: true,
          info: true,
          filter: true,
          length: true,
          processing: true,
          lengthMenu: [
            [50, 25, 10, 5],
            [50, 25, 10, 5],
          ],
          autoWidth: false,
          scrollY: "200px",
          fnRowCallback: function (settings: any, aData: any) {
            const pagination = $(this)
              .closest(".dataTables_wrapper")
              .find(".dataTables_paginate");
            pagination.toggle(this.api().page.info().pages > 1);
          },
          drawCallback: function (settings: any) {
            table1 = $("#processJsonList").DataTable();
            $("td:eq(1)", settings).css("width", "25%");
            $("td:eq(2)", settings).css("width", "45%");
            $("td:eq(3)", settings).css("width", "25%");
            $("td:eq(4)", settings).css("width", "5%");
          },
          columns: [
            {
              title: "Store",
              width: "24%",
              className: "dt-head-left",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);

                let storeName = elm.getStoreNameFromDealerId(rowData[1]);
                storeName = storeName ? storeName : rowData[1];
                storeName =
                  storeName === rowData[1]
                    ? rowData[10]
                      ? rowData[10]
                      : rowData[1]
                    : storeName;
                let enterpriseCode = rowData[1] ? rowData[1] : storeName;
                let mageStoreCode =
                  rowData[10] &&
                  rowData[10] != null &&
                  rowData[10] != undefined &&
                  rowData[10] != ""
                    ? rowData[10]
                    : "";
                let errorWarnningMessage =
                  rowData[11] &&
                  rowData[11] != null &&
                  rowData[11] != undefined &&
                  rowData[11] != ""
                    ? rowData[11]
                    : "";
                if (errorWarnningMessage) {
                  data =
                    '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                    rowData[1] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '">' +
                    enterpriseCode +
                    " " +
                    mageStoreCode +
                    ' <span class="text-info"> <em class="fa fa-info-circle" style="margin-top:0.4%;" data-toggle="tooltip" data-placement="top" title="' +
                    errorWarnningMessage +
                    '"></em></span></span>';
                  return data !== null ? data : null;
                } else {
                  data =
                    '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                    rowData[1] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '">' +
                    enterpriseCode +
                    " " +
                    mageStoreCode +
                    "</span>";
                  return data !== null ? data : null;
                }
              },
            },
            {
              title: "Last Run At",
              width: "15%",
              type: "formatted-date",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                let lastRunAt = "";
                if (rowData[3]) {
                  let time = rowData[3].split(" ");
                  let HourFormat = elm.convertTimeFormat(time[1]);
                  lastRunAt = time[0] + " " + HourFormat;
                }
                data = "<span>" + lastRunAt + "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Status",
              width: "15%",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);

                console.log('Ohhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh')
                 console.log('rowData:', rowData);
                 console.log('rowData[2]:', rowData[2]);
                 console.log('Ohhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh')

                var className = "";
                // var iconColor = '#516e84';
                var toolTipMessage = rowData[8];
                rowData[7]
                  ? (toolTipMessage += "Failed Reason: " + rowData[7] + " \n")
                  : "";
                if (rowData[31] === false) {
                    toolTipMessage += "Sharepoint file upload failed \n";
                    rowData[2]="Upload Halt";
                }                
                rowData[2] ===
                elm.SchedulerConstantService.STATUS_FLAG.COMPLETED
                  ? (className = "label-success")
                  : null;
                rowData[2] ===
                elm.SchedulerConstantService.STATUS_FLAG.SCHEDULED
                  ? (className = "label-scheduled")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.RUNNING
                  ? (className = "label-running")
                  : null;
                rowData[2] ===
                elm.SchedulerConstantService.STATUS_FLAG.REPEATING
                  ? (className = "label-repeating")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.FAILED
                  ? (className = "label-failed")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.QUEUED
                  ? (className = "label-queued")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.LOCKED
                  ? (className = "label-locked")
                  : null;
                rowData[2] == "HALT" ? (className = "label-halt") : null;

                const d = data;
                if (rowData[19] == "true" && className !== "label-failed") {
                  className = "label-resumed";
                }
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.UPLOADHALT
                ? (className = "label-upload-halt")
                : null;
                data =
                  '<span style="cursor:pointer;float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="statusMessageDisplay label ' +
                  className +
                  '" data-toggle="tooltip" data-info="' +
                  rowData +
                  '" data-placement="left" title="' +
                  toolTipMessage +
                  '" data-animation="false">' +
                  rowData[2] +
                  "</span>";
                return data;
              },
            },
            {
              title: "",
              width: "5%",
              className: "dt-head-left pl-0",
              render: function (data: any, type: any, rows: any, meta: any) {
                const d = data;
                let rowData = [];
                rowData = Object.assign([], rows);
                const enableReRunFlag = rowData[17];
                if (
                  rowData[2] ===
                    elm.SchedulerConstantService.STATUS_FLAG.COMPLETED &&
                  enableReRunFlag
                ) {
                  const updateParentGroup = "Re-Run";
                  if (rowData[19] == "true") {
                    data =
                      '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                      d +
                      '">' +
                      '<i aria-hidden="true" class="fa fa-caret-square-o-up mt-2 updateDealerAddress" style="font-size: 18px;color: #48663c;"  data-toggle="tooltip" data-placement="top" title="' +
                      updateParentGroup +
                      '" data-animation="false" data-info="' +
                      rowData +
                      '"  ></i></a>';
                  } else {
                    data =
                      '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                      d +
                      '">' +
                      '<i aria-hidden="true" class="fa fa-caret-square-o-up text-success mt-2 updateDealerAddress" style="font-size: 18px;color: #48663c;"  data-toggle="tooltip" data-placement="top" title="' +
                      updateParentGroup +
                      '" data-animation="false" data-info="' +
                      rowData +
                      '"  ></i></a>';
                  }
                } else {
                  const updateParentGroup = "Resume";
                  if (rowData[2] == "HALT") {
                    if (rowData[13] && rowData[20]) {
                      data =
                        '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                        d +
                        '">' +
                        '<i aria-hidden="true" class="fa fa-caret-square-o-up mt-2 fetchPayTypeDetails" style="font-size: 18px;color: #c6c6c6 !important;"  data-toggle="tooltip" data-placement="top" title="' +
                        "Resume Again" +
                        '" data-animation="false" data-info="' +
                        rowData +
                        '"  ></i></a>';
                    } else if (rowData[20]) {
                      data =
                        '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                        d +
                        '">' +
                        "</a>";
                    } else {
                      data =
                        '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                        d +
                        '">' +
                        '<i aria-hidden="true" class="fa fa-caret-square-o-up text-success mt-2 fetchPayTypeDetails" style="font-size: 18px;color: #80047d !important;"  data-toggle="tooltip" data-placement="top" title="' +
                        updateParentGroup +
                        '" data-animation="false" data-info="' +
                        rowData +
                        '"  ></i></a>';
                    }
                  } else if(rowData[2] == "Queued"){
                    
                    let updateParentGroup = 'Change Priority';
                    let updateParentGroup1 = 'Remove Item';
                    data = '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                        + '<i aria-hidden="true" class="fa fa-edit text-primary mt-2 changePriority" style="font-size: 18px; margin-right: 10px;" data-toggle="tooltip" data-placement="top" title="'
                        + updateParentGroup + '" data-animation="false" data-info="' + rowData + '" ></i></a>';
                    data += '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                        + '<i aria-hidden="true" class="fa fa-trash text-danger mt-2 removeQueueItem" style="font-size: 18px;" data-toggle="tooltip" data-placement="top" title="'
                        + updateParentGroup1 + '" data-animation="false" data-info="' + rowData + '" ></i></a>';

                   } else if(rowData[2] == "Running"){
                    let updateParentGroup = 'Show Processor Status';
              
                      data = '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                        + '<i aria-hidden="true" class="fa fa-info-circle text-dark mt-2 showProcessorStatus" style="font-size: 18px; margin-right: 10px;" data-toggle="tooltip" data-placement="top" title="'
                        + updateParentGroup + '" data-animation="false" data-info="' + rowData + '" ></i></a>';
                   
                   }
                    else {
                    data = "";
                  }
                }
                return data;
              },
            },
          ],
          rowGroup: {
            dataSrc: "Store Group",
          },
        });
        // tslint:disable-next-line:no-unused-expression
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
          "formatted-num-pre": function (a: any) {
            a = a === "-" || a === "" ? 0 : a.replace(/[^\d\-\.]/g, "");
            return parseFloat(a);
          },
          "formatted-num-asc": function (a: any, b: any) {
            return a - b;
          },
          "formatted-num-desc": function (a: any, b: any) {
            return b - a;
          },
          "formatted-date-asc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a ? new Date(a).getTime() : 0;
            const y = b ? new Date(b).getTime() : 0;
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "formatted-date-desc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a
              ? new Date(a.toString()).getTime()
              : moment().unix() * 1000;
            const y = b
              ? new Date(b.toString()).getTime()
              : moment().unix() * 1000;
            return x < y ? 1 : x > y ? -1 : 0;
          },
        });
        console.log("processJsonListArray",elm.processJsonListArray)
        elm.reDrawProcessJsonTable(elm.processJsonListArray);
      });
    }, 200);
  }

  /**
   * reDrawProcessJsonTable function will redraw the datatable using new table values
   *
   */
  reDrawProcessJsonTable(temp: any) {
    table1 = $("#processJsonList").DataTable();
    table1.search("").draw();
    table1 = $("#processJsonList").dataTable();
    table1.fnClearTable();
    table1 = $("#processJsonList").dataTable();
    const tempArr = [];
    for (let i = 0; i < temp.length; i++) {
      const t = temp[i];
      const rpt = [
        t.groupName,
        t.storeID,
        t.statusFlag,
        t.lastRunAt,
        t.fileProcessed,
        t.outputFile,
        t.message,
        t.failedReason,
        t.statusInfo,
        t.uniqueId,
        t.storeName,
        t.errorWarnningMessage,
        t.roAccountDescriptionCount,
        t.coaExceptionWarningMessage,
        t.customerExceptionWarningMessage,
        t.company_no_not_matching_count,
        t.grouped_team_work_count,
        t.reRun,
        t.address,
        t.isResumed,
        t.isAlreadyResumed,
        t.suffixedInvoicesCsvData,
        t.new_line_type_count,
        t.negative_coupon_count,
        t.labor_with_zero_sale_nonzero_cost_count,
        t.gl_missing_ros_count,
        t.coupon_discount_basis_amount_mismatch_exception_count,
        t.labor_with_no_paytype_exception_count,
        t.parts_excluded_from_history_exception_count,
        t.lost_sale_parts_exception_count,
        t.priority,
	t.uploadStatus,
  t.processorUniqueId
      ];
      tempArr.push(rpt);
    }
    if (tempArr.length > 0) {
      table1.fnAddData(tempArr, false); // Add new data
    }
    table1.fnDraw(); // Redraw the DataTable
    this.processJSONLoading = false;
    if (temp.length > 0) {
      setTimeout(() => {
        $("#processJsonList").DataTable().columns.adjust().draw(false);
      }, 100);
    }
  }

  setActiveFixedHeader() {
    if (
      navigator.userAgent.indexOf("MSIE") !== -1 ||
      navigator.appVersion.indexOf("Trident/") > 0
    ) {
      const evt = document.createEvent("UIEvents");
      evt.initUIEvent("resize", true, false, window, 0);
      window.dispatchEvent(evt);
    } else {
      window.dispatchEvent(new Event("resize"));
    }
  }

  startDateSelection() {
    let date: any = moment().subtract(6, "months");
    if (date.format("D") <= this.SchedulerConstantService?.DAY_NUMBER_CHECK) {
      return moment(date).startOf("month");
    } else {
      return moment(date).add(1, "months").startOf("month");
    }
  }
  endDateSelection() {
    return moment();
  }

  utcToLocalTime(time: any) {
    let localTime = null;
    localTime = moment(time * 1)
      .local()
      .format(this.SchedulerConstantService.SCHEDULE_DATE_FORMAT);
    return localTime;
  }

  getRoOptionList() {
    this.roOptionList = [];
    let data = this.SchedulerConstantService.RO_OPTION;
    for (let i = 0; i < data.length; i++) {
      this.roOptionList.push({ id: data[i], itemName: data[i] });
    }
  }

  onChangeRoOptionList(item: any) {
    if (!this.containsObject(item, this.onChangeRoOption)) {
      this.onChangeRoOption.push(item);
    }
  }

  getAllJobsForUiUpdate(callback: any) {
    console.log("::::getAllJobsForUiUpdate::::::::::::::::::::::::::::::::::::::::::::::::");
    this.compareObjArrayLatest = [];
    const allStoreGroupsList = this.apollo
      .use("manageDealerTrackSchedule")
      .query({
        query: getAllDealerTrackExtractJobs,
        fetchPolicy: "network-only",
      }).pipe(takeUntil(this.subscription$)).subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          console.log("result>????????????????????????????????????????????????????",result);
          let obj: any = {};
          $.each(
            result["data"]["getAllDealerTrackExtractJobs"]["jobArray"],
            (key: any, val: any) => {
              console.log("val*********************************************************",val);
              let groupName = val.data.groupName;
              let scheduledDate = val.nextRunAt
                ? val.nextRunAt
                : val.lastRunAt
                ? val.lastRunAt
                : null;
              let date = "";
              scheduledDate
                ? (date = moment
                    .unix(scheduledDate / 1000)
                    .format("MM-DD-YYYY HH:mm:ss"))
                : null;
              let nextRunAt = "";
              val.nextRunAt
                ? (nextRunAt = moment.unix(val.nextRunAt / 1000).fromNow())
                : null;
              let groupStatus = val;
              const failedReason = val.failReason;
              $.each(val.data["storeDataArray"], (key: any, val: any) => {
                let jobStartDate = null;
                let jobEndDate = null;
                let rpt = [];
                let keyVal = null;
                let status = null;
                let seperator = "/";
                if (val.startDate.includes("-")) {
                  seperator = "-";
                }
                const dateArray = val.startDate.split(seperator);
                const startDate =
                  dateArray[0] + "-" + dateArray[1] + "-" + dateArray[2];
                if (val.endDate.includes("-")) {
                  seperator = "-";
                }
                const dateArrayEnd = val.endDate.split(seperator);
                const endDate =
                  dateArrayEnd[0] +
                  "-" +
                  dateArrayEnd[1] +
                  "-" +
                  dateArrayEnd[2];
                let range = startDate + " - " + endDate;
                jobStartDate = val.startTime;
                jobEndDate = val.endTime;
                rpt = [groupName, val.enterpriseCode, range, date];
                keyVal = rpt.join();
                let jobStatus = false;
                status = val.status;
                let statusFlag = "";
                if (jobStartDate && jobEndDate && status) {
                  jobStatus = true;
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.COMPLETED;
                } else if (jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.RUNNING;
                } else if (!jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.SCHEDULED;
                } else if (jobStartDate && jobEndDate && !status) {
                  jobStatus = true;
                  statusFlag = this.SchedulerConstantService.STATUS_FLAG.FAILED;
                } else {
                  statusFlag = this.getJobStatus(groupStatus);
                }
                status = statusFlag;

                let objNew: any = {};
                objNew[keyVal] = status;
                this.compareObjArrayLatest.push(objNew);
              });
            }
          );
          callback();
        },
        error: (err: Error) => {
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }
  compareObjectValue() {
    let dataTableObjects = [];
    let data = null;
    let self = this;
    table1 = $("#scheduleProcessQueueDealerTrack").DataTable();
    data = table1.rows().data();
    let i = 0;
    data.each(function (value: any, index: any) {
      let status = null;
      let keyVal = "";
      let res = null;
      keyVal = value[0] + "," + value[1] + "," + value[2] + "," + value[3];
      status = value[4];
      console.log("status@@@@@@@@@@@@@@@@@@@@@@@@@@",status);
      console.log("@@@@@@@@@@@@@@@@@@@@@@@@@@@@keyValDTY@@@@",keyVal);
      res = self.compareKey(keyVal);
      console.log("res!!!!!!!!!!!!!!!!!!",res);
      if (res !== status) {
        self.reloadExtractionQueue(res);
      }
      i++;
    });
  }

  reloadExtractionQueue(res: any) {
    this.refreshScheduleList();
    if (
      res === this.SchedulerConstantService.STATUS_FLAG.COMPLETED ||
      res === this.SchedulerConstantService.STATUS_FLAG.FAILED
    ) {
      this.refreshScheduleList();
      this.refreshScheduleListCompleted();
    }
  }
  compareKey(key: any) {
    let status = null;
    var itemsProcessed = 0;
    let length = this.compareObjArrayLatest.length;
    this.compareObjArrayLatest.forEach(function (
      obj: any,
      index: number,
      self
    ) {
      itemsProcessed++;
      if (obj.hasOwnProperty(key)) {
        status = obj[key];
      }
    });
    if (itemsProcessed === length) {
      return status;
    }
    return;
  }
  processJSONReloadList(callback: any) {
    this.compareObjArrayProcessJSONList = [];
    this.processJSONJobsQueueLength = 0;
    const allStoreGroupsList = this.apollo
      .use("manageDealerTrackSchedule")
      .query({
        query: getAllDealerTrackProcessJSONJobs,
        fetchPolicy: "network-only",
      }).pipe(takeUntil(this.subscription$)).subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          if (
            result["data"]["getAllDealerTrackProcessJSONJobs"] &&
            result["data"]["getAllDealerTrackProcessJSONJobs"][
              "processJSONJobs"
            ]
          ) {
            $.each(
              result["data"]["getAllDealerTrackProcessJSONJobs"][
                "processJSONJobs"
              ],
              (key: any, val: any) => {
                let statusFlag = this.getJobStatus(val);
                let statusMessage = val.failReason;
                if (statusMessage == "Error: Halt") {
                  statusFlag = "Failed";
                }
                let obj = { statusFlag: statusFlag };
                if (
                  val.data.operation === "json-processing" &&
                  val.data.storeID
                ) {
                  let keyVal = null;
                  keyVal = val._id;
                  let objNew: any = {};
                  objNew[keyVal] = statusFlag;
                  this.compareObjArrayProcessJSONList.push(objNew);
                }
              }
            );
            if (
              result["data"]["getAllDealerTrackProcessJSONJobs"] &&
              result["data"]["getAllDealerTrackProcessJSONJobs"][
                "processJSONJobsQueue"
              ]
            ) {
              this.processJSONJobsQueueLength = result["data"][
                "getAllDealerTrackProcessJSONJobs"
              ]["processJSONJobsQueue"].length
                ? result["data"]["getAllDealerTrackProcessJSONJobs"][
                    "processJSONJobsQueue"
                  ].length
                : 0;
            }
            callback();
          }
        },
        error: (err: Error) => {
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }
  compareObjectValueProcessJSON() {
    let reloadStatus = false;
    let data = null;
    let self = this;
    table1 = $("#processJsonList").DataTable();
    data = table1.rows().data();
    let incQueuedList = 0;
    let incProcessList = 0;
    data.each(function (value: any, index: any) {
      let status = null;
      let keyVal = "";
      let res = null;
      if (value[2] != self.SchedulerConstantService.STATUS_FLAG.QUEUED) {
        keyVal = value[9];
        status = value[2];
        incProcessList++;
      } else {
        incQueuedList++;
      }
      res = self.compareProcessJSONKey(keyVal);

      if (status == "HALT" || status == "DEAD") {
        status = "Failed";
      }

      if (res !== status) {
        reloadStatus = true;
      }
    });

    if (
      this.processJSONJobsQueueLength !== incQueuedList &&
      this.processJSONJobsQueueLength > incQueuedList
    ) {
      reloadStatus = true;
    }
    if (this.compareObjArrayProcessJSONList.length !== incProcessList) {
      reloadStatus = true;
    }

    if (reloadStatus) {
      this.getAllProcessJsonJobs(() => {
        this.showProcessJsonList();
      });
    }
  }

  compareProcessJSONKey(key: any) {
    let status = null;
    var itemsProcessed = 0;
    let length = this.compareObjArrayProcessJSONList.length;
    this.compareObjArrayProcessJSONList.forEach(function (
      obj: any,
      index,
      self
    ) {
      itemsProcessed++;
      if (obj.hasOwnProperty(key)) {
        status = obj[key];
      }
    });
    if (itemsProcessed === length) {
      return status;
    }
    return;
  }

  getJobStatus(val: any) {
    let statusFlag: any = "";
    val.lockedAt
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.LOCKED)
      : null;
    val.scheduled
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.SCHEDULED)
      : null;
    val.queued
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.QUEUED)
      : null;
    val.completed
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.COMPLETED)
      : null;
    val.failed
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.FAILED)
      : null;
    val.repeating
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.REPEATING)
      : null;
    val.running
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.RUNNING)
      : null;
    val.scheduled && val.failed
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.RESCHEDULED)
      : null;
    return statusFlag;
  }

  convertTimeFormat(timeString: any) {
    var H = +timeString.substr(0, 2);
    var h = H % 12 || 12;
    var ampm = H < 12 || H === 24 ? " AM" : " PM";
    timeString = h + timeString.substr(2, 3) + ampm;
    return timeString;
  }

  setDateRange(type: any, jobType: any) {
    if (type) {
      this.createSchedule.patchValue({
        roOption: "all",
      });
    }
    this.setExtractionModeSelection(jobType);
    if (this.jobTypeStatus !== type) {
      this.jobTypeStatus = type;
      if (!type) {
        this.selectMultiDateRangeOption = false;
        this.createSchedule.patchValue({
          roOption: "all",
        });
        this.setExtractionModeSelection(jobType);
        let date = moment().subtract(31, "days");
        // this.dateInput = {
        //   start: date,
        //   end: this.selectedRange[1],
        // };
        this.selectedRange = [date.toDate(), this.endDateSelection().toDate()];
        console.log("initial start------------", this.selectedRange);

        this.dateInput = {
          start: this.selectedRange[0],
          end: this.selectedRange[1],
        };
      } else {
        this.selectMultiDateRangeOption = true;        
        this.dateInput = {
          start: this.selectedRange[0],
          end: this.selectedRange[1],
        };
      }
    }
  }

  setExtractionModeSelection(jobType: any) {
    this.displayOnDemand = false;
    if (jobType === "current") {
      this.displayOnDemand = true;
      this.createSchedule.patchValue({
        roOption: "current",
      });
    }
    if (jobType === "refresh") {
      this.createSchedule.patchValue({
        roOption: "all",
      });
    }
  }

  checkJobExistInExtractionQueue(
    storeCode: any,
    enterpriseCode: any,
    dateRange: any
  ) {
    let processQueueListCopy = [];
    processQueueListCopy = Object.assign([], this.processQueueList);
    processQueueListCopy = processQueueListCopy
      .filter((item, index) => index < processQueueListCopy.length)
      .filter(
        (item: any, index: any) =>
          item.storeName === storeCode &&
          item.store === enterpriseCode &&
          item.range === dateRange &&
          item.status !== "Scheduled"
      );
    return processQueueListCopy.length;
  }
  sortJSONArray(temp: any) {
    this.processJsonListArray = temp
      .filter((item: any, index: any) => index < temp.length)
      .sort((a: any, b: any): any => {
        const x = a["statusFlag"].toLowerCase().replace(/^[^a-z0-9]*/g, "");
        const y = b["statusFlag"].toLowerCase().replace(/^[^a-z0-9]*/g, "");
        return x > y ? -1 : x < y ? 1 : 0;
      });
    return temp;
  }

  reloadGroupList() {
    this.closeToolTip();
    this.reloadGroup = true;
    this.commonService.allS360Jobs(
      "DealerTrack",
      "production",
      (result: any) => {
        this.storeGroupList = result.storeGroupList;
        this.jobGroupList = result.jobGroupList;
        this.getGroupFilterList();
        this.reloadGroup = false;
        this.cdr.detectChanges();
      }
    );
  }

  stopTimer() {
    this.autoReloadDealerTrackStatus = false;
    this.isPaused = false;
  }

  startTimer() {
    this.autoReloadDealerTrackStatus = true;
    this.isPaused = true;
    this.getNotificationForUi();
  }

  closeToolTip() {
    $("[rel=tooltip]").tooltip("enable");
    $(".tooltip").tooltip("hide");
  }

  preSelectGroupAndStore() {
    const storeObj = localStorage.getItem("selectedStoreObj")
      ? JSON.parse(localStorage.getItem("selectedStoreObj")!)
      : null;
    const groupObj = localStorage.getItem("selectedGroupObj")
      ? JSON.parse(localStorage.getItem("selectedGroupObj")!)
      : null;
    if (storeObj && groupObj) {
      const grpObj = {
        id: groupObj.mageGroupName,
        itemName: groupObj.mageGroupName,
        mageGroupCode: groupObj.mageGroupCode,
        mageGroupName: groupObj.mageGroupName,
        mageStoreCode: groupObj.mageStoreCode,
        mageStoreName: groupObj.mageStoreName,
        projectId: groupObj.projectId,
        companyId: groupObj.companyId,
      };
      const strObj = {
        id: storeObj.companyName,
        itemName: storeObj.companyName,
        mageGroupCode: storeObj.mageGroupName,
        mageStoreCode: storeObj.mageStoreCode,
        mageStoreName: storeObj.mageStoreName,
        stateCode: storeObj.state,
        projectId: storeObj.projectId,
        thirdPartyUsername: storeObj.thirdPartyUsername,
        secondProjectId: storeObj.secondProjectId,
        companyID: storeObj.companyId,
      };
      this.onSelectStoreGroup(grpObj);
      this.onSelectStore(strObj);
    }
  }

  checkAccessValidationErrors() {
    this.accessValidationLoader = true;
    let payload = {};
    let tmpDescritionArray: any[] = [];
   

    let storeFilterListTemp = this.storeFilterList;

    let storeTemp1: any = storeFilterListTemp.filter(
      (x) => x.id == this.createSchedule.get("store")?.value[0].id
    );

    const enterpriseCode = storeTemp1[0].enterpriseCode;
    const companyNumber = storeTemp1[0].thirdPartyUsername;

    const serverName = storeTemp1[0].serverName;
  
       
    // const enterpriseCode =
    //   this.createSchedule.get("store")?.value[0].enterpriseCode;
    // const companyNumber =
    //   this.createSchedule.get("store")?.value[0].thirdPartyUsername;
 
    const startDate: string = moment(new Date(this.dateInput.start)).format(
      this.SchedulerConstantService.DATE_FORMAT
    );
    const endDate: string = moment(new Date(this.dateInput.end)).format(
      this.SchedulerConstantService.DATE_FORMAT
    );
    let warningText;
    let url = environment.checkDealerTrackAccessValidation;
    console.log("enterpriseCode!!!!!!!!!!!!!!!!!!!!!!!!!",enterpriseCode);
    console.log("companyNumber!!!!!!!!!!!!!!!!!!!!!!!!!",companyNumber);
    console.log("serverName!!!!!!!!!!!!!!!!!!!!!!!!!",serverName);
    payload = {
      enterpriseCode: enterpriseCode,
      companyNumber: companyNumber,
      serverName: serverName,
      startDate: startDate,
      endDate: endDate,
    };
    const token = localStorage.getItem("token");
    // let headers = new HttpHeaders({ "Content-Type": "application/json" });
    // headers.append("Authorization", `Bearer ${token}`);
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      this.accessValidationLoader = false;
      tmpDescritionArray = [];
      warningText = "";
      let successIncrementer = 0;
      // const resData = JSON.parse(res["_body"]);
      const resData = res;
      if (resData.length > 0) {
        resData.forEach((e: any) => {
          if (
            e.errorMessage ==
              this.constantService.ENTERPRISE_NOT_FOUND_ERROR_MSG ||
            e.errorMessage == this.constantService.COMPANY_NOT_FOUND_ERROR_MSG
          ) {
            tmpDescritionArray.push(e.errorMessage);
          } else if (e.errorMessage == "Success") {
            successIncrementer++;
            tmpDescritionArray.push(e.apiType + " - " + e.errorMessage);
          } else {
            tmpDescritionArray.push(
              e.apiType +
                " - " +
                this.constantService.ACCESS_VALIDATION_FAIL_MSG
            );
          }
        });
        warningText = tmpDescritionArray.join("\n");
        if (successIncrementer == 5) {
          swal({
            title: this.constantService.ACCESS_VALIDATION,
            text: warningText,
            type: "success",
            confirmButtonClass: "btn-success pointer",
            confirmButtonText: this.constantService.CLOSE,
          });
        } else {
          swal({
            title: this.constantService.ACCESS_VALIDATION,
            text: warningText,
            type: "warning",
            confirmButtonClass: "btn-warning pointer",
            confirmButtonText: this.constantService.CLOSE,
          });
        }
      } else {
        swal({
          title: this.constantService.ACCESS_VALIDATION,
          text: this.constantService.SOMETHING_WENT_WRONG,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      }
    });
  }

  upload(event: any) {
    this.couponAndDiscountFile = event.target.files[0];
    console.log(this.couponAndDiscountFile);
  }

  uploadDiscountFile() {
    return new Promise((resolve, reject) => {
      console.log("Ready for upload");
      const formData = new FormData();
      formData.append("file", this.couponAndDiscountFile);
      let url = environment.dealerTrackUploadDiscountFileUploadUrl;
      const token = localStorage.getItem("token");
      // let headers = new HttpHeaders();
      // headers.append("Authorization", `Bearer ${token}`);
      const headers = new HttpHeaders ({
        authorization: token ? `Bearer ${token}` : "",
      });
      this.http.post(url, formData, { headers: headers }).subscribe(
        (res: any) => {
          console.log("File uploaded");
          console.log(res);
          // const resData = JSON.parse(res["_body"]);
          const resData = res;
          if (resData.hasOwnProperty("path")) {
            this.coupon_and_discount_file_path = resData.path;
            resolve("");
          }
        },
        (error: any) => {
          console.log(error);
          resolve("");
        }
      );
    });
  }

  fetchPayTypeDetails(data: any) {
    let res;
    this.suffixedInvoices = [];
    res = data.split(",");
    console.log("fetchPayTypeDetails");
    console.log("++++++++++++++++++++++++++++++++++++++");
    console.log(res);
    console.log("++++++++++++++++++++++++++++++++++++++");
    this.resumeProcessorInput = data;
    this.roAccountDescriptionCountMsg = res[12];
    this.coaExceptionWarningMsg = res[13];
    this.customerExceptionWarningMsg = res[14];
    this.company_no_not_matching_Msg = res[15];
    this.grouped_team_work_Msg = res[16];
    this.suffixedInvoicesCsvData = res[21];
    this.newLineTypeDetectedMsg = res[22];
    this.negativeCouponMsg = res[23];
    this.laborWithZeroSaleNonZeroCostMsg = res[24];
    this.glMissingRosMsg = res[25];
    this.couponDiscountBasisAmountMismatchExceptionCountMsg = res[26];
    this.laborWithNoPaytypeExceptionMsg = res[27];
    this.partsExcludedFromHistoryExceptionCountMsg = res[28];
    this.lostSalePartsExceptionCountMsg = res[29];
    let newArr = this.suffixedInvoicesCsvData.split("*");

    for (let i = 0; i < newArr.length; i++) {
      let tempObj: { LineType: string; Count: string } = {
        LineType: "",
        Count: "",
      };
      tempObj.LineType = newArr[i].split(":")[0];
      tempObj.Count = newArr[i].split(":")[1];
      this.suffixedInvoices.push(tempObj);
    }

    if (res[11]) {
      this.couponAndDiscountWarningMsg = res[11].split("\n")[3];
    }
    $("#payTypeModal").modal("show");
  }

  closePayTypeModal() {
    $("#payTypeModal").modal("hide");
  }

  async resumeProcessor(){
    let res, tmp, tmp1, tmp2, inputFile, processJobId;
    res = this.resumeProcessorInput.split(",");
    console.log("res $$$$$$$$$$$$$$$$$$$$$",res);
    let processorUniqueId = res[32];
    processJobId = res[9];
    tmp = res[8];
    console.log("tmp:", tmp);
    tmp1 = tmp.split("\n")[0];
    console.log("tmp1", tmp1);
    tmp2 = tmp1.split("Input File:")[1];

    // if(this.chartOfAccountsFile){
    await this.uploadChartOfAccountsFile();
    // }

    if (tmp2) {
      inputFile = tmp2.trim();
    }
    const payload = {
      extractFile: inputFile,
      dms: "DealerTrack",
      chartOfAccountsFilePath: this.chart_of_accounts_file_path,
      processJobId: processJobId,
      processorUniqueId:processorUniqueId
    };
    console.log("payload$$$$$$$$$$$$$$$$$$$$$$",payload);
    let url = environment.haltAndResume;
    const token = localStorage.getItem("token");
    // let headers = new HttpHeaders({ "Content-Type": "application/json" });
    // headers.append("Authorization", `Bearer ${token}`);
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      // const resData = JSON.parse(res["_body"]);
      const resData = res;
      this.closePayTypeModal();
      this.refreshProcessJsonList();
      if (resData.status) {
        console.log(resData);
        swal({
          title: "Processor job resumed successfully",
          type: "success",
          confirmButtonClass: "btn-success pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      } else {
        console.log(resData);
        swal({
          title: "Something went wrong",
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      }
    });
  }
  reRunProcessor(data: any) {
    let res;

    res = data.split(",").reverse();
    console.log("++++++++++++++++++RERUN++++++++++++++++++++");
    console.log(res);
    console.log("++++++++++++++++++++++++++++++++++++++");
    this.resumeProcessorInput = data;
    this.roAccountDescriptionCountMsg = res[2];
    // this.coaExceptionWarningMsg = res[1];
    // this.customerExceptionWarningMsg = res[0];
    $("#reRunProcessorModal").modal("show");
  }

  closeReRunProcessorModal() {
    $("#reRunProcessorModal").modal("hide");
  }

  async performReRunProcessor() {
    let res, tmp, tmp1, tmp2, inputFile;
    console.log("********************************************");
    console.log(this.resumeProcessorInput);
    console.log(typeof this.resumeProcessorInput);
    res = this.resumeProcessorInput.split(",");
    console.log(res);
    tmp = res[8];
    console.log("tmp:", tmp);
    tmp1 = tmp.split("\n")[0];
    console.log("tmp1", tmp1);
    tmp2 = tmp1.split("Input File:")[1];
    console.log("tmp2:", tmp2);
    console.log("********************************************");

    if (this.chartOfAccountsFile) {
      await this.uploadChartOfAccountsFile();
    }

    if (tmp2) {
      inputFile = tmp2.trim();
    }
    const payload = {
      extractFile: inputFile,
      dms: "DealerTrack",
      chartOfAccountsFilePath: this.chart_of_accounts_file_path,
    };
    let url = environment.reRunDealerTrackProcessJson;
    const token = localStorage.getItem("token");
    // let headers = new HttpHeaders({ "Content-Type": "application/json" });
    // headers.append("Authorization", `Bearer ${token}`);
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      // const resData = JSON.parse(res["_body"]);
      const resData = res;
      this.closeReRunProcessorModal();
      this.refreshProcessJsonList();
      if (resData.status) {
        console.log(resData);
        swal({
          title: "Processor job rescheduled successfully",
          type: "success",
          confirmButtonClass: "btn-success pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      } else {
        console.log(resData);
        swal({
          title: "Something went wrong",
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      }
    });
  }

  uploadChartOfAccounts(event: any) {
    const file: File = event.target.files[0];
    this.errorMessage = ''; // Clear any previous error    
    if (file) {
      const fileType = file.type;
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (this.allowedExtensions.includes(fileExtension!) && this.allowedTypes.includes(fileType)) {
        console.log('File is valid:', file);       
        this.chartOfAccountsFile = event.target.files[0];
      }else {
        this.errorMessage = 'Invalid file type. Only CSV, XLS, and XLSX files are allowed.';
        event.target.value = ''; // Clear input field
      }
    }
  }

  uploadChartOfAccountsFile() {
    return new Promise((resolve, reject) => {
      if (!this.chartOfAccountsFile) {
        resolve(""); // Simply resolve if no file is selected
        return;
      }
      console.log("Ready for upload");
      const formData = new FormData();
      formData.append("file", this.chartOfAccountsFile);
      let url = environment.dealerTrackUploadchartOfAccountsFileUploadUrl;
      const token = localStorage.getItem("token");
      // let headers = new HttpHeaders();
      // headers.append("Authorization", `Bearer ${token}`);
      const headers = new HttpHeaders({
        authorization: token ? `Bearer ${token}` : "",
      });
      this.http.post(url, formData, { headers: headers }).subscribe(
        (res: any) => {
          console.log("File uploaded");
          console.log(res);
          const resData = res;
          // const resData = JSON.parse(res["_body"]);
          if (resData.hasOwnProperty("path")) {
            this.chart_of_accounts_file_path = resData.path;
            resolve("");
          }
        },
        (error: any) => {
          console.log(error);
          resolve("");
        }
      );
    });
  }
  updateDealerAddress(data: any) {
    $("#dealerAddressModel").modal("show");
    this.updateDealeAddressInput = data;
    let res = data.split(",");
    let dealerAddress = res[18];
    let dealerAdd = dealerAddress ? dealerAddress.split("~").join("\n") : "";
    dealerAdd = dealerAdd ? dealerAdd.split("|").join(",") : "";
    this.dealerAddress = dealerAdd;
    let splitFile = res[5].split("&");
    const distFile = splitFile[0];
    const etlFile = splitFile[1];
    let distFile_temp1 = distFile.split("/").reverse()[0];
    distFile_temp1 = distFile_temp1.replace("PROC-", "");
    let distFile_temp2 = distFile_temp1.split("-");
    this.uniqueCode = distFile_temp2[1];
    this.uniqueIdentifier = distFile_temp2[4];
    this.doProxyFilePath = distFile.trim();
  }
  closeDealerAddressModel() {
    $("#dealerAddressModel").modal("hide");
  }

  updateDAInfo() {
    swal(
      {
        title: this.constantService.AREYOUSURE,
        text: this.SchedulerConstantService.DO_PROXY_NOW,
        type: "success",
        showCancelButton: true,
        cancelButtonClass: "btn-default pointer",
        confirmButtonClass: "btn-success pointer",
        confirmButtonText: "Run",
        closeOnConfirm: true,
        showLoaderOnConfirm: true,
      },
      () => {
        this.updateDAInfoSubmit();
      }
    );
  }
  updateDAInfoSubmit() {
    this.writeFileTypes((res: any) => {
      if (this.doProxyFilePath) {
        this.scheduleJobUsingPgDump(this.doProxyFilePath, (result: any) => {
          if (result["createProxyWithSqlDump"].status) {
            swal({
              title:
                this.SchedulerConstantService.PAY_TYPE_FILTER.JOB_RERUN_SUCCESS,
              type: "success",
              confirmButtonClass: "btn-success pointer",
              confirmButtonText: this.constantService.CLOSE,
            });
          } else {
            swal({
              title: result["createProxyWithSqlDump"].message,
              type: "success",
              confirmButtonClass: "btn-warning pointer",
              confirmButtonText: this.constantService.CLOSE,
            });
          }
          this.showSpinnerButton = false;
          this.closeDealerAddressModel();
          this.refreshProcessJsonList();
        });
      } else {
        this.closeDealerAddressModel();
        swal({
          title: this.SchedulerConstantService.WARNING_MESSAGE_DA_PROXY,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      }
    });
  }
  writeFileTypes(callback: any) {
    let payload = [{}];

    let obj = {
      uniqueId: this.uniqueCode,
    };

    let obj1 = {
      dealerAddress: this.dealerAddress,
    };

    let obj2 = {
      dealerId: this.uniqueIdentifier,
    };

    payload.push(obj);
    payload.push(obj1);
    payload.push(obj2);
    // let obj ={
    //   name:"test"
    // }

    // payload.push(obj);
    let url = environment.updateDealerTrackDealerAddress;
    const token = localStorage.getItem("token");
    // let headers = new HttpHeaders({ "Content-Type": "application/json" });
    // headers.append("Authorization", `Bearer ${token}`);
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      // const resData = JSON.parse(res['_body']);
      if (res.status) {
        callback(res);
      } else {
        callback("");
      }
    });
  }
  scheduleJobUsingPgDump(doProxyFilePath: any, callback: any) {
    const scheduleObj: any = {
      proxyJobData: {
        zipPath: doProxyFilePath,
        // payType: payTypeFileName
      },
    };
    let self = this;
    this.apollo
      .use("manageDealerTrackSchedule")
      .mutate({
        mutation: createProxyWithSqlDump,
        variables: scheduleObj,
      }).pipe(takeUntil(this.subscription$)).subscribe({
        next: (listdata: any) => {
          NProgress.done();
          const result: any = listdata.data;
          callback(result);
        },
        error: (err: Error) => {
          NProgress.done();
          this.closeDealerAddressModel();
          swal({
            title: this.SchedulerConstantService.WARNING_MESSAGE_DA,
            type: "warning",
            confirmButtonClass: "btn-warning pointer",
            confirmButtonText: this.constantService.CLOSE,
          });
          this.showSpinnerButton = false;
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  switchServer(toggleMockServer =false) {
    if (this.solve360ServerDecider) {
      let serverType = "test";
      this.commonService.allS360Jobs(
        "DealerTrack",
        serverType,
        (result: any) => {
          this.loading = false;
          this.storeGroupList = result.storeGroupList;
          this.storeList = result.storeList;
          this.jobGroupList = result.jobGroupList;
          this.dealer_address = result.address;
          this.getGroupFilterList();
          this.preSelectGroupAndStore();
          if(toggleMockServer){
            this.toastrService.success ("Switched to Mock Server");
          }
        }
      );
    } else {
      let serverType = "production";
      this.commonService.allS360Jobs(
        "DealerTrack",
        serverType,
        (result: any) => {
          this.loading = false;
          this.storeGroupList = result.storeGroupList;
          this.storeList = result.storeList;
          this.jobGroupList = result.jobGroupList;
          this.dealer_address = result.address;
          this.getGroupFilterList();
          this.preSelectGroupAndStore();
          if(toggleMockServer){
            this.toastrService.success ("Switched to Production Server");
          }
        }
      );
    }
  }

changePriority(data: string){
  let res = data.split(",");
  this.currentPriority = res[30]
  this.changePriorityForProcessorJob = res[30];
  this.fileNameForPriorityChange = res[8].split("/")[7]  
  $('#changePriorityModal').modal('show');
  

}

removeQueueItem(data:any){

  swal(
    {
      title: this.constantService.AREYOUSURE,
      text: "",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default pointer",
      confirmButtonClass: "btn-warning pointer",
      confirmButtonText: "Continue",
      closeOnConfirm: true,
      showLoaderOnConfirm: true,
    },
    () => {
      let res = data.split(",");
  console.log("reomve item!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!",res);
  let filePath = res[8].split(":")[1].trim();
  

  const url = environment.reomoveQueueItem;
  const token = localStorage.getItem('token');
  
  // Use HttpHeaders instead of Headers
  const headers = new HttpHeaders({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  });

  const payload = { 
     filePath:filePath
  };

  this.http.post(url, payload, { headers }).subscribe((res: any) => {
    // Assuming res is already parsed JSON
    const resData = res;
    this.refreshProcessJsonList();

    if (resData && resData.status) {
      console.log(resData);
      this.showStatusMessage('Item  removed Successfully', 'success');
    } else {
      console.log(resData);
      this.showStatusMessage('Something Went Wrong!', 'failure');
    }
  });

    }
  );

  
}

changePriorityOfProcessorJob() {
  const payload = { 
    fileNameForPriorityChange: this.fileNameForPriorityChange, 
    priority: this.changePriorityForProcessorJob, 
    dms: 'DEALERTRACK' 
  };

  const url = environment.changePriority;
  const token = localStorage.getItem('token');
  
  // Use HttpHeaders instead of Headers
  const headers = new HttpHeaders({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  });

  this.http.post(url, payload, { headers }).subscribe((res: any) => {
    // Assuming res is already parsed JSON
    const resData = res;
    
    this.closeChangePriorityModal();
    this.refreshProcessJsonList();

    if (resData && resData.status) {
      console.log(resData);
      this.showStatusMessage('Priority Updated Successfully', 'success');
    } else {
      console.log(resData);
      this.showStatusMessage('Something Went Wrong!', 'failure');
    }
  });
}


closeChangePriorityModal(){
  $('#changePriorityModal').modal('hide');
}

onDateRangeChange(range: any) {
  console.log("Selected Date Range:", range);
  if (range) {
    this.dateInput.start = dayjs(range[0]).format("MM-DD-YYYY");
    this.dateInput.end = dayjs(range[1]).format("MM-DD-YYYY");
    console.log("Selected Date Range:", this.dateInput);
  }
}

requeue(data:any){

swal(
    {
      title: this.constantService.AREYOUSURE,
      text: "",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default pointer",
      confirmButtonClass: "btn-warning pointer",
      confirmButtonText: "Continue",
      closeOnConfirm: true,
      showLoaderOnConfirm: true,
    },
    () => {
          

  console.log("REQUEUE DATA$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",data.split(','));
  const url = environment.reQueueItem;
  const token = localStorage.getItem('token');
  let filePath = data.split(',')[10];
  // Use HttpHeaders instead of Headers
  const headers = new HttpHeaders({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  });

  const payload = { 
     dms:'dealertrack',
     filePath:filePath
  };

  this.http.post(url, payload, { headers }).subscribe((res: any) => {
    // Assuming res is already parsed JSON
    const resData = res;
    this.refreshProcessJsonList();

    if (resData && resData.status) {
      console.log(resData);
      this.showStatusMessage('Job Added to Processor Queue', 'success');
    } else {
      console.log(resData);
      this.showStatusMessage('Something Went Wrong!', 'failure');
    }
  });
}
);
  
  

}

processorSteps = [
 "Unzipping Input to Work",
  "Creating Schema from Model",
  "Iterating Over Zip File Contents",
  "Loading Individual Open ROs",
  "Loading Individual ROs",
  "Updating Part Kit Description",
  "Updating Chart of Account Description",
  "Detecting Problematic ROs",
  "Detecting Open/Void RO Data and Reporting",
  "Checking for Missing ROs in Original Raw Data",
  "Generating Scheduler ID",
  "Generating Config File",
  "Loading From Scheduler DB",
  "Compressing Directory",
  "Generating Exception Analysis",
  "Loading Exception Analysis",
  "Generating Proxy Repair Orders per Request",
  "Extracting Text ROs to TSV",
  "Pre-import Halt Detection",
  "Moving Work to Bundle Directory"
];
currentStepIndex: number = -1;

showProcessorStatus(data: string) {
  let res = data.split(",");
  console.log('showProcessorStatus:', res);
  let jobId = res[9];
  
  const payload = { mongoId: jobId };
  let url = environment.fetchProcessStatus;
  const token = localStorage.getItem('token');
  const headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` });

  this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
    const resData = res?.data?.response[0];
    console.log("resData:", resData);

    if (resData) {
      this.processRunningStatus = resData.processorRunningStatus;
      let formattedDate = moment.utc(resData.processorStatusUpdatedAt).format('MM/DD/YYYY HH:mm:ss');
      this.processorStatusUpdatedAt = formattedDate;
      console.log("Process Running status$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.processRunningStatus);
      // Determine the current step index
      console.log("Process Running status$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.processRunningStatus.split('/'));
      this.currentStepIndex = Number(this.processRunningStatus.split('/')[0])
      console.log("currentStepIndex$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.currentStepIndex);
    } else {
      console.log("Error:", resData);
      this.showStatusMessage('Something Went Wrong!', 'failure');
    }
  });

  $('#showProcessorStatusModal').modal('show');
}

closeProcessorStatusModal(){
  $('#showProcessorStatusModal').modal('hide');
}

}

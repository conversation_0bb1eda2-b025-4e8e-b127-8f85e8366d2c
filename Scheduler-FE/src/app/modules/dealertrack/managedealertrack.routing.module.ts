import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManagedealertrackComponent } from "./managedealertrack.component";

const routes: Routes = [
  {
    path: "",
    component: ManagedealertrackComponent,
    data: {
      title: "ManageDealerTrack",
      breadcrumb: [{ label: "ManageDealerTrack", url: "" }],
    },
  },
];

@NgModule({
  exports: [RouterModule],
  imports: [RouterModule.forChild(routes)],
})
export class ManagedealertrackRoutingModule {}

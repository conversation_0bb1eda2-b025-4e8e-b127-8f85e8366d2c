<style>
    .horizontal-scroll-container {
        overflow-x: hidden;
        display: flex;
        position: relative;
        width: 100%;
        scrollbar-width: none;
    }

    .horizontal-scroll-container .row {
        display: flex;
        transition: transform 0.3s ease-in-out;
    }
    .btn {
        margin: 10px;
    }

    .priority-ordering {
        position: relative;
    }
    .left-scroll-btn{
        position: absolute;
        top: 98px;
        left: -23px;
        z-index: 1;
        opacity: 0.8;
        margin: 10px;
        cursor: pointer;
        
    }
    .horizontal-scroll-container::-webkit-scrollbar {
       display: none; 
    }

    .right-scroll-btn{
    position: absolute;
    top: 98px;
    right: -26px;
    z-index: 1;
    opacity: 0.8;
    margin: 10px;
    cursor: pointer;
       
    }
    .priority-ordering .ag-body-horizontal-scroll{
        display: none;
    }
    .priority-ordering .ag-root-wrapper{
       min-height:175px !important; 
    }

    .ag-grid-container{
        min-height: 200px;
    }
    .ag-layout-normal {
    scrollbar-width: thin;          /* For Firefox */
    scrollbar-color: #bbb transparent;

    /* For Chrome/Safari/Edge */
    }

    .ag-layout-normal::-webkit-scrollbar {
    width: 6px;
    }

    .ag-layout-normal::-webkit-scrollbar-thumb {
    background-color: #bbb;
    border-radius: 4px;
    }

    .ag-layout-auto-height .ag-center-cols-clipper, .ag-layout-auto-height .ag-center-cols-container, .ag-layout-print .ag-center-cols-clipper, .ag-layout-print .ag-center-cols-container { 
        min-height: 150px !important;
    }
  
</style>
<div class="row">
    <div class="col-lg-12 priority-ordering">
        <section class="card" style="padding: 10px;">
            <!-- Navigation Buttons -->
            <div class="d-flex justify-content-between align-items-center mb-2 ">
                <button class="btn btn-primary left-scroll-btn" (click)="scrollLeft()">←</button>
                <button class="btn btn-primary right-scroll-btn" (click)="scrollRight()">→</button>
            </div>

            <!-- Horizontal Container -->
            <div class="horizontal-scroll-container" #scrollContainer>
                <div class="row flex-nowrap">
                    <div class="col-3 border" *ngFor="let queue of queues">
                        <strong>{{ queue.title }}</strong>
                        <div class="ag-grid-container">
                        <ag-grid-angular [ngStyle]="{ height: (queue.rowData || []).length > 7 ? '230px' : 'auto' }"
                            style="width: 100%;"

                            class="ag-theme-balham "
                            [pagination]="false"                            
                            [animateRows]="true"
                            [quickFilterText]="queue.searchText"
                            [enableCellTextSelection]="true"
                            [defaultColDef]="queue.defaultColDef"
                            [rowData]="queue.rowData"
                            [columnDefs]="queue.columnDefs"
                            (gridReady)="onQueueGridReady($event, queue.type)"
                            [domLayout]="(queue.rowData || []).length > 7 ? 'normal' : 'autoHeight'"
                            (cellValueChanged)="onCellValueChanged($event, queue.type)">
                        </ag-grid-angular>
                    </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>



<div class="row">
    <div class="col-lg-12 import-grid">
        <div *ngIf="isAuthenticated">
            <section class="card" order-id="card-1" style="padding: 10px;">
                <div class="card-header cursor_default" style="display: none">
                    <span class="cat__core__title">
            <div style="float: right; font-size: 16px; color: #000">
              <span> </span>
                    <span style="
                  position: absolute;
                  left: 85px;
                  top: 10px;
                  cursor: pointer;
                  color: gray;
                ">
              </span>
                    <span style="position: absolute; left: 20px; top: 10px"> </span>
                </div>
                </span>
        </div>
        <!-- <div class="card-block">
            <div class="mt-3 mb-2"> -->
        <div class="row">
            <div class="col-lg-12 row">
                <div class="col-lg-6">
                    <span class="pull-left" style="position: relative; top: 2px">
                    <div
                      style="width: 120px; float: left"
                      class="pull-left"
                    >
                      <span><strong>From Date:</strong></span>
                    <input type="text" [(ngModel)]="inFromDate" class="form-control datepicker-only-init" id="inStartDate" name="inStartDate" #dtinStartDate [disabled]="downloading" (blur)="setDtinStartDate(dtinStartDate)" placeholder="Select Date" />
                </div>
                &nbsp;&nbsp;
                <div style="width: 120px; float: left" class="ml-3 pull-left">
                    <span><strong>To Date:</strong></span>
                    <input type="text" [(ngModel)]="inToDate" class="form-control datepicker-only-init" id="inEndDate" name="inEndDate" #dtinEndDate [disabled]="downloading" (blur)="setDtinEndDate(dtinEndDate)" placeholder="Select Date" />
                </div>

                <div class="ml-3 pull-left" style="width: 100px; padding-top: 18px">
                    <button class="btn btn-sm btn-primary pointer" (click)="getchedulerStatusSearchList()">
                        <em class="" aria-hidden="true"></em> Search
                        <em
                          *ngIf="searcheportSpinner"
                          class="fa fa-spinner fa-pulse fa-1x fa-fw"
                        ></em>
                      </button>
                </div>
                </span>
            </div>
        </div>
    </div><br>
    <div class="row">
        <div class="col-lg-6 import-grid">
            <input type="search" #search class="input-sm form-control ag-grid-custom-search" placeholder="Search.." (keyup)="searchFilterData(search.value)" />
            <ag-grid-angular style="width: 100%; height:'';" class="ag-theme-balham" [pagination]="true" [paginationPageSize]="paginationPageSize" [animateRows]="true" [quickFilterText]="" [enableCellTextSelection]="true" [defaultColDef]="defaultColDef" [rowData]="rowData"
                [columnDefs]="columnDefs" (gridReady)="onGridReady($event)" (firstDataRendered)="onFirstDataRendered($event)" [headerHeight]="70" [domLayout]="'autoHeight'" [overlayLoadingTemplate]="overlayLoadingTemplate" [overlayNoRowsTemplate]="overlayNoRowsTemplate">
            </ag-grid-angular>
        </div>
        <div class="col-lg-6 import-grid">
            <input type="search" #search1 class="input-sm form-control ag-grid-custom-search" placeholder="Search.." (keyup)="searchFilterImportData(search1.value)" />
            <ag-grid-angular style="width: 100%; height: '';" class="ag-theme-balham" [pagination]="true" [paginationPageSize]="paginationPageSize" [animateRows]="true" [quickFilterText]="" [enableCellTextSelection]="true" [defaultColDef]="defaultColDef1" [rowData]="rowData1"
                [columnDefs]="columnDefs1" (gridReady)="onGridImportReady($event)" [headerHeight]="70" [domLayout]="'autoHeight'" (firstDataRendered)="onFirstDataImportRendered($event)" [overlayLoadingTemplate]="overlayLoadingTemplate" [overlayNoRowsTemplate]="overlayNoRowsTemplate">
            </ag-grid-angular>
        </div>
    </div>
    </section>
</div>
</div>
</div>
<div *ngIf="isAuthenticated" class="modal fade" id="haltReasonModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width: 700px">
            <div class="modal-header">
                <h5 class="modal-title" id="requestModalLabel">Halt Reason</h5>
                <button type="button" class="close" (click)="closeHaltReasonModal()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
            </div>
            <div class="modal-body">
                <ng-container>
                    <div class="col-md-12">
                        <ul style="margin-top: 20px">
                            <li *ngIf="invSeqHalt">Invoice wrap around</li>
                            <li *ngIf="isPaytypeHalt">New paytype detected</li>
                            <li *ngIf="isDeptHalt">New Department detected</li>
                            <li *ngIf="isMakeHalt">Unassigned make detected</li>
                        </ul>
                    </div>
                </ng-container>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" (click)="closeHaltReasonModal()">
            Cancel
          </button>
                    <button class="btn btn-primary" (click)="gotoScheduleImport()" [routerLink]="SCHEDULERIMPORT_URL">
            Resume Process
          </button>
                </div>
            </div>
        </div>
    </div>
</div>

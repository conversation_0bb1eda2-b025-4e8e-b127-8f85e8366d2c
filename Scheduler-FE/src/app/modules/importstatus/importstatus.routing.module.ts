import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ImportStatusComponent } from "./importstatus.component";

const routes: Routes = [
  // {
  //   path: "",
  //   component: ImportStatusComponent,
  //   // data: {
  //   //   title: "ImportStatus",
  //   //   breadcrumb: [{ label: "ImportStatus", url: "" }],
  //   // },
  // },
  {
    path: "",
    component: ImportStatusComponent,
    data: { title: "Home", breadcrumb: [{ label: "Home", url: "" }],
           },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ImportStatusRoutingModule {}

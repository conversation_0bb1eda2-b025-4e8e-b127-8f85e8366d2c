import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ementRef, Renderer2,ChangeDetector<PERSON>ef,ViewChild } from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { ConstantService } from "../../structure/constants/constant.service";
import { Apollo } from "apollo-angular";
import gql from "graphql-tag";
import { Router,RouterLink } from "@angular/router";
import { CommonService } from "../../structure/services/common.service";
import * as moment from "moment-timezone";
import { SchedulerConstantService } from "../../structure/constants/scheduler.constant.service";
import { SubscriptionConstantService } from "./../../structure/constants/subscription.constant.service";
import { HttpClient, HttpHeaders } from "@angular/common/http";

import { DmFormGroupService } from "src/app/structure/services/dm.formgroup.services";
import { environment } from "src/environments/environment";

import { Subject, takeUntil,Observable, timer, switchMap } from "rxjs";
import { SharedModule } from "../shared/shared.module";
let table1;
declare var $: any;
declare var jQuery: any;
declare var swal: any;
declare var NProgress: any;

interface QueueItem {
  _id: string;
  extractionId: number;
  fileName: string;
  priority: number;
}

interface QueueData {
  cdkQueueData: QueueItem[];
  dealerTrackQueueData: QueueItem[];
  reynoldsQueueData: QueueItem[];
  automateQueueData: QueueItem[];
  autoSoftQueueData: QueueItem[];
  dealerBuiltQueue: QueueItem[];
  dominionQueueData: QueueItem[];
  tekionapiQueueData: QueueItem[];  
}


/**
 * Mutation to create new schedule
 *
 */

const getSchedulerImportDetails = gql`
  query getSchedulerImportDetails($inStartDate: Date!, $inEndDate: Date!) {
    getSchedulerImportDetails(inStartDate: $inStartDate, inEndDate: $inEndDate)
  }
`;

const getSchedulerPreImportStatusDetails = gql`
  query getSchedulerPreImportStatusDetails(
    $inStartDate: Date
    $inEndDate: Date
  ) {
    getSchedulerPreImportStatusDetails(
      inStartDate: $inStartDate
      inEndDate: $inEndDate
    )
  }
`;
const getStoreCompanyDetails = gql`
  query getCompanyDetails($inCompanyId: BigInt) {
    getCompanyDetails(inCompanyId: $inCompanyId)
  }
`;
const preImportStatusDeleteUpdation = gql`
mutation preImportStatusIsDeleted(
  $inSchedulerId: String
  $inIsDeleted: Boolean
) {
  preImportStatusIsDeleted(
    input: { inSchedulerId: $inSchedulerId, inIsDeleted: $inIsDeleted}
  ) {
    json
  }
}
`;




interface Queue {
  title: string;
  class?: string;
  paginationPageSize: number;
  searchText: string;
  defaultColDef: { sortable: boolean; filter: boolean,resizable: boolean, };
  rowData: any[];
  columnDefs: Array<
    | { headerName: string; field: string; editable: boolean }
    | {
        headerName: string;
        field: string;
        editable: boolean;
        cellEditor: string;
        cellEditorParams: { values: string[] };
      }
  >;
  type: string;
  isExpanded: boolean;
}
@Component({
  selector: "app-importstatus",
  templateUrl: "./importstatus.component.html",
  styleUrls: ["./importstatus.component.css"],
  standalone: true,
  imports: [
    RouterLink,
    SharedModule,
  ],
})
export class ImportStatusComponent implements OnInit {
  @ViewChild('scrollContainer') scrollContainer!: ElementRef;
  private subscription$ = new Subject();
  public gridApi: any;
  public gridColumnApi: any;
  public columnDefs: any;
  public columnDefs1: any;
  public cdkColDef1:any;
  public rowData: any[] = [];
  public rowData1: any[] = [];
  public importDetailsList: any[] = [];
  public preImportStausList: any[] = [];

  public shows360Link = false;
  public s360CompanyId: any;
  public listener: any;
  public SCHEDULERIMPORT_URL: any;
  public defaultColDef: any;
  public defaultColDef1: any;
  public cdkqueueColDef:any;
  public inFromDate: any = null;
  public inToDate: any = null;
  public downloading = false;
  public searcheportSpinner = false;

  public invSeqHalt = false;
  public isDeptHalt = false;
  public isMakeHalt = false;
  public isPaytypeHalt = false;
  public overlayLoadingTemplate: any;
  public isAuthenticated = false;
  public manufacturerData:any;
  public companyName:any;
  public dms:any;
  public brandList:any;
  public state:any;
  public gridImportApi: any;
  public gridImportColumnApi: any;
  public overlayNoRowsTemplate: any;
  public paginationPageSize = 30;
  private subscriptionList: any = [];
  public cdkQueuList:any[]=[];
  public dealertrackQueuList:any[]=[];
  public reynoldsQueueList:any[]=[];
  public automateQueueList:any[]=[];
  public autoSoftQueueList:any[]=[];
  public dealerbuiltQueueList:any[]=[];
  public dominionQueuelist:any[]=[];
  public tekionapiQueuelist:any[]=[];
  public scrollAmount = 300;
  public isExpandedGrid3:any= false; 
  cdkpaginationPageSize = 10;
  cdksearchText: string = '';
  queues: Queue[] = [];
  public dmsAliasesMap: Record<string, string[]> = {
    dealertrack: ["dealertrack"],
    reynoldsrci: ["reynoldsrci", "reynolds","ucs"],
    mpk: ["mpk"],
    automate: ["automate", "auto/mate"],
    tekion: ["tekion", "tekionapi"],
    pbs: ["pbs"],
    dealerbuilt: ["dealerbuilt"],
    autosoft: ["autosoft"],
    dominionvue: ["dominionvue", "dominion", "dominion / vue", "dominion / acs"],
    cdk: ["cdk", "cdk3pa", "cdkdash", "cdk drive"],
    cdkflex: ["cdkflex"],
    fortellis: ["fortellis"],
    adam: ["adam"],
    quorum: ["quorum"],
  };
 

  constructor(
    private http: HttpClient,
    private apollo: Apollo,
    public constantService: ConstantService,
    private elRef: ElementRef,
    private renderer: Renderer2,
    private toastrService: ToastrService,
    private commonService: CommonService,
    private router: Router,
    private DmFormGroupService: DmFormGroupService,
    public SchedulerConstantService: SchedulerConstantService,
    public SubscriptionConstantService: SubscriptionConstantService,
    private changeDetectorRef: ChangeDetectorRef,

    // private modal: Modal
  ) {}


 


  scrollLeft() {
    const container = this.scrollContainer.nativeElement;
    container.scrollLeft -= this.scrollAmount;
  }

  scrollRight() {
    const container = this.scrollContainer.nativeElement;
    container.scrollLeft += this.scrollAmount;
  }


  ngOnDestroy() {
    this.subscription$.next(void 0);
    this.subscription$.complete();
  }
  getCalenderPropertyObject(maxDate: any) {
    let settings: any = {
      useCurrent: true,
      widgetPositioning: {
        horizontal: "left",
      },
      icons: {
        time: "fa fa-clock-o",
        date: "fa fa-calendar",
        up: "fa fa-arrow-up",
        down: "fa fa-arrow-down",
        previous: "fa fa-arrow-left",
        next: "fa fa-arrow-right",
      },
      format: "MM-DD-YYYY",
    };
    if (maxDate) {
      settings["maxDate"] = new Date();
    }
    return settings;
  }
  private previousQueueData: QueueData | null = null;

  hasDataChanged(newData: QueueData): boolean {
    if (!this.previousQueueData) {
      this.previousQueueData = newData;
      return true; // If no previous data, always return true
    }
  
    // Compare each queue data, you can also perform deep comparison if needed
    const isEqual = JSON.stringify(newData) === JSON.stringify(this.previousQueueData);
    if (!isEqual) {
      this.previousQueueData = newData; // Update the previous data
    }
  
    return !isEqual;
  } 

  ngOnInit() {
    const token = localStorage.getItem('token');
  
  timer(0, 10000)
  .pipe(
    switchMap(() => {
      const timestamp = new Date().getTime();
      const url = `${environment.getQueueData}?_=${timestamp}`;
      return this.http.get<QueueData>(url, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
    })
  )
  .subscribe(
    (data: QueueData) => {
      console.log("Get queue data response:", data);
      console.log("cdk3pa queue:", data.cdkQueueData);
      console.log("dealertrack queue:", data.dealerTrackQueueData);

      // Compare the data with the previous data
      if (this.hasDataChanged(data)) {
        // If data has changed, process the data
        const dealerTrackQueue = data.dealerTrackQueueData.map((item: QueueItem) => ({
          ...item,
          storeName: item.fileName.split("-")[1]
        }));

        const cdk3paQueue = data.cdkQueueData.map((item: QueueItem) => ({
          ...item,
          storeName: item.fileName.split("-")[1]
        }));

        const reynoldsQueue = data.reynoldsQueueData.map((item: QueueItem) => ({
          ...item,
          storeName: item.fileName.split("-")[1]
        }));

        const automateQueue = data.automateQueueData.map((item: QueueItem) => ({
          ...item,
          storeName: item.fileName.split("-")[1]
        }));

        const autosoftQueue = data.autoSoftQueueData.map((item: QueueItem) => ({
          ...item,
          storeName: item.fileName.split("-")[1]
        }));

        const dealerbuiltQueue = data.dealerBuiltQueue.map((item: QueueItem) => ({
          ...item,
          storeName: item.fileName.split("-")[1]
        }));

        const dominionQueue = data.dominionQueueData.map((item: QueueItem) => ({
          ...item,
          storeName: item.fileName.split("-")[1]
        }));

        const tekionapiQueue = data.tekionapiQueueData.map((item: QueueItem) => ({
          ...item,
          storeName: item.fileName.split("-")[1]
        }));

        // Log modified queues
        console.log("Modified Queues:", { cdk3paQueue, dealerTrackQueue, reynoldsQueue, automateQueue, autosoftQueue, dealerbuiltQueue, dominionQueue, tekionapiQueue});

        // Assign the modified data to component variables
        this.cdkQueuList = cdk3paQueue;
        this.dealertrackQueuList = dealerTrackQueue;
        this.reynoldsQueueList = reynoldsQueue;
        this.automateQueueList = automateQueue;
        this.autoSoftQueueList = autosoftQueue;
        this.dealerbuiltQueueList = dealerbuiltQueue;
        this.dominionQueuelist = dominionQueue;
        this.tekionapiQueuelist = tekionapiQueue;

        // Update Ag-Grid if there is a change
        this.queues = [
          {
            title: 'Manage Queue - CDK3PA',
            class: "ag-theme-balham",
            paginationPageSize: 10,
            searchText: '',
            defaultColDef: { sortable: true, filter: true, resizable: true },
            rowData: this.cdkQueuList,
            columnDefs: [
              { headerName: 'Store Name', field: 'parentName', editable: false },
              {
                headerName: 'Priority',
                field: 'priority',
                editable: true,
                cellEditor: 'agSelectCellEditor',
                cellEditorParams: { values: ['1', '2', '3', '4', '5', '6', '7'] }
              },
              
            ],
            type: 'cdk3pa',
            isExpanded: false,
          },
        
          {
  
            title: 'Manage Queue - DealerTrack',
             paginationPageSize: 10,
             searchText: '',
             defaultColDef: { sortable: true, filter: true,resizable: true, },
             rowData: this.dealertrackQueuList,
             columnDefs: [
               { headerName: 'Store Name', field: 'parentName', editable: false },
               {
                 headerName: 'Priority',
                 field: 'priority',
                 editable: true,
                 cellEditor: 'agSelectCellEditor',
                 cellEditorParams: {
                   values: ['1', '2', '3', '4', '5', '6', '7'],
                 },
               },
             ],
             type: 'dealertrack',
             isExpanded: false,
           },
           {
             title: 'Manage Queue - Reynolds',
             paginationPageSize: 10,
             searchText: '',
             defaultColDef: { sortable: true, filter: true,resizable: true, },
             rowData: this.reynoldsQueueList,
             columnDefs: [
               { headerName: 'Store Name', field: 'parentName', editable: false },
               {
                 headerName: 'Priority',
                 field: 'priority',
                 editable: true,
                 cellEditor: 'agSelectCellEditor',
                 cellEditorParams: {
                   values: ['1', '2', '3', '4', '5', '6', '7'],
                 },
               },
             ],
             type: 'reynolds',
             isExpanded: false,
           },
           {
             title: 'Manage Queue - AutoMate',
             paginationPageSize: 10,
             searchText: '',
             defaultColDef: { sortable: true, filter: true,resizable: true, },
             rowData: this.automateQueueList,
             columnDefs: [
               { headerName: 'Store Name', field: 'parentName', editable: false },
               {
                 headerName: 'Priority',
                 field: 'priority',
                 editable: true,
                 cellEditor: 'agSelectCellEditor',
                 cellEditorParams: {
                   values: ['1', '2', '3', '4', '5', '6', '7'],
                 },
               },
             ],
             type: 'automate',
             isExpanded: false,
           },
           {
             title: 'Manage Queue - DealerBuilt',
             paginationPageSize: 10,
             searchText: '',
             defaultColDef: { sortable: true, filter: true,resizable: true, },
             rowData: this.dealerbuiltQueueList,
             columnDefs: [
               { headerName: 'Store Name', field: 'parentName', editable: false },
               {
                 headerName: 'Priority',
                 field: 'priority',
                 editable: true,
                 cellEditor: 'agSelectCellEditor',
                 cellEditorParams: {
                   values: ['1', '2', '3', '4', '5', '6', '7'],
                 },
               },
             ],
             type: 'dealerbuilt',
             isExpanded: false,
           },
           {
             title: 'Manage Queue - AutoSoft',
             paginationPageSize: 10,
             searchText: '',
             defaultColDef: { sortable: true, filter: true,resizable: true, },
             rowData: this.autoSoftQueueList,
             columnDefs: [
               { headerName: 'Store Name', field: 'parentName', editable: false },
               {
                 headerName: 'Priority',
                 field: 'priority',
                 editable: true,
                 cellEditor: 'agSelectCellEditor',
                 cellEditorParams: {
                   values: ['1', '2', '3', '4', '5', '6', '7'],
                 },
               },
             ],
             type: 'autosoft',
             isExpanded: false,
           },
           {
             title: 'Manage Queue - Dominion',
             paginationPageSize: 10,
             searchText: '',
             defaultColDef: { sortable: true, filter: true,resizable: true, },
             rowData: this.dominionQueuelist,
             columnDefs: [
               { headerName: 'Store Name', field: 'parentName', editable: false },
               {
                 headerName: 'Priority',
                 field: 'priority',
                 editable: true,
                 cellEditor: 'agSelectCellEditor',
                 cellEditorParams: {
                   values: ['1', '2', '3', '4', '5', '6', '7'],
                 },
               },
             ],
             type: 'dominion',
             isExpanded: false,
           },
           {
            title: 'Manage Queue - TekionAPI',
            paginationPageSize: 10,
            searchText: '',
            defaultColDef: { sortable: true, filter: true,resizable: true, },
            rowData: this.tekionapiQueuelist,
            columnDefs: [
              { headerName: 'Store Name', field: 'parentName', editable: false },
              {
                headerName: 'Priority',
                field: 'priority',
                editable: true,
                cellEditor: 'agSelectCellEditor',
                cellEditorParams: {
                  values: ['1', '2', '3', '4', '5', '6', '7'],
                },
              },
            ],
            type: 'tekionapi',
            isExpanded: false,
          },
        ];
      } else {
        console.log('No change in queue data. Skipping Ag-Grid update.');
      }
    },
    (error) => {
      console.error('Error fetching data:', error);
    }
  );


  

    this.commonService.getGroups(() => {
      this.commonService.checkGroups((flag) => {
        if (!flag) {
          return;
        }
        this.isAuthenticated = true;
        this.init();
      });
    });
    this.defaultColDef = {
      resizable: true,
      filter: true,
      sortable: true,
      suppressExcelExport: true,
      filterParams: {
        newRowsAction: "keep",
      },
      flex: 1 
    };
    this.defaultColDef1 = {
      resizable: true,
      filter: true,
      sortable: true,
      suppressExcelExport: true,
      filterParams: {
        newRowsAction: "keep",
      },
      flex: 1 
    };
    this.cdkqueueColDef = {
      sortable: true,   // Enables sorting on all columns by default
      filter: true,     // Enables filtering on all columns by default
      resizable: true,  // Allows columns to be resized
    };
    this.overlayNoRowsTemplate = `
        <span class="no-rows-import-overlay">
            No data found
        </span>`;
    this.overlayLoadingTemplate =
      '<span class="ag-overlay-loading-center">Loading <em aria-hidden="true" class="fa fa-spinner fa-pulse"></em></span>';
    let activityData = {
      activityName: "Manage Import Status",
      activityType: "Manage Import Status",
      activityDescription: "Current Url: " + this.router.url,
    };
    this.commonService.saveActivity("Manage Import Status", activityData);
    this.SubscriptionConstantService.pageTitle = " - Import Status";
     
    // this.cdkQueuList = [
    //   { storeName: 'TEST STORE 1', priority: ' 2' },
    //   { storeName: 'TEST STORE 2', priority: ' 4' },
    //   { storeName: 'TEST STORE 3', priority: ' 3' },
    //   { storeName: 'TEST STORE 4', priority: ' 4' },

    // ]

  }



  // style="width: 100%; height: 250px;"
  // class="ag-theme-balham"
  // [pagination]="true"
  // [paginationPageSize]="cdkpaginationPageSize"
  // [animateRows]="true"
  // [quickFilterText]="cdksearchText"
  // [enableCellTextSelection]="true"
  // [defaultColDef]="cdkqueueColDef"
  // [rowData]="cdkQueuList"
  // [columnDefs]="cdkColDef1"
  // (gridReady)="onGridCDKQueueReady($event)"
  // (cellValueChanged)="onCellValueChanged($event)">



  setDtinStartDate(e: any) {
    if (e.value && e.value !== "") {
      this.inFromDate = e.value;
    } else {
      this.inFromDate = null;
    }
  }

  setDtinEndDate(e: any) {
    if (e.value && e.value !== "") {
      this.inToDate = e.value;
    } else {
      this.inToDate = null;
    }
  }
  init() {
    const elm = this;
    $(function () {
      let objPropertyCalender: { [k: string]: any } = {};
      objPropertyCalender = elm.getCalenderPropertyObject(false);
      $("#inStartDate").datetimepicker(objPropertyCalender);
      $("#inEndDate").datetimepicker(objPropertyCalender);
      $('[data-toggle="tooltip"]').tooltip({
        trigger: "hover",
      });
    });
    this.inToDate = moment(new Date()).format("MM-DD-YYYY");
    const date = new Date();
    this.inFromDate = moment(
      new Date(date.getFullYear(), date.getMonth(), 1)
    ).format("MM-DD-YYYY");
    this.getchedulerStatusSearchList();
    
    if (!this.listener) {
      this.listener = this.renderer.listen(
        this.elRef.nativeElement,
        "click",
        (evt) => {
          if (evt.target.className === "badge-custom halt-class") {
            let data = JSON.parse(evt.target.dataset.info);
            console.log("data>>>>>>>>>>>>>>>>>>>>>>",data);
            console.log("data@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",data.dms);
            console.log("data.sch_dms>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",data.sch_dms);
            const dmsNormalized = this.normalizeDmsName(data.dms);
            const schDmsNormalized = this.normalizeDmsName(data.sch_dms);
            console.log("dmsNormalized@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",dmsNormalized);
            console.log("schDmsNormalized@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",schDmsNormalized);
            if (dmsNormalized && schDmsNormalized && dmsNormalized === schDmsNormalized) {
              console.log("opening halt reson modal>>>>>>>>>>>>>>>>>>>>>>>>>");
              this.openHaltReasonModal(evt.target.dataset.info);
            } else {
              swal({
                title: "DMS Mismatch in Solve",
                type: "warning",
                confirmButtonClass: "btn-warning pointer",
                confirmButtonText: this.constantService.CLOSE,
              });
            }
            // this.saveUser(evt.target.dataset.info);
          }else if(evt.target.className == "fa fa-trash-o text-danger fa-2"){
          this.deletePreImportStatus(evt.target.dataset.info,"");
        }
        }
      );
    }
  }

  normalizeDmsName(dms: string): string | undefined {
    dms = dms?.toLowerCase();
    for (const [key, aliases] of Object.entries(this.dmsAliasesMap)) {
      if (aliases.map(a => a?.toLowerCase()).includes(dms)) {
        return key; 
      }
    }
    return undefined; 
  }

  onFirstDataRendered(params: any) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
  }
  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.showLoadingOverlay();
  }
  onQueueGridReady(params: any, type: string){
       console.log(`on Qoeoe grid ready:Params=${params} type${type}`);
       params.api.sizeColumnsToFit();
  }

  onQueueCellValueChanged(event: any, type: string) {
    console.log(`Cell value changed in ${type}`, event);
  }

  onFirstDataImportRendered(params: any) {
    this.gridImportApi = params.api;
    this.gridImportApi.sizeColumnsToFit();
  }
  onGridImportReady(params: any) {
    this.gridImportApi = params.api;
    this.gridImportColumnApi = params.columnApi;
    this.gridImportApi.showLoadingOverlay();
  }
  onGridCDKQueueReady(params: any){
    params.api.sizeColumnsToFit();
  }
  
  searchFilterData(params: any) {
    this.gridApi.setFilterModel(null);
    this.gridApi.onFilterChanged();
    this.gridApi.setQuickFilter(params);
  }
  searchFilterImportData(params: any){
    this.gridImportApi.setFilterModel(null);
    this.gridImportApi.onFilterChanged();
    this.gridImportApi.setQuickFilter(params);
  }

  generateReports() {
    this.searcheportSpinner = false;
    this.downloading = false;
    this.rowData = this.preImportStausList;
    this.rowData1 = this.importDetailsList;
    if (this.importDetailsList == null) {
      this.gridImportApi?.showNoRowsOverlay();
    } else {
      this.gridImportApi?.hideOverlay();
    }
    if (this.gridApi && typeof this.gridApi.hideLoadingOverlay === "function") {
      this.gridApi.hideLoadingOverlay();
    }
    this.columnDefs = [
      {
        headerName: "Pre Import Report ",
        size: "14",
        children: [
          {
            headerName: "Store Name",
            field: "company_name",
            width: 120,
            resizable: true,
          },
          {
            headerName: "DMS",
            field: "dms",
            width: 80,
            resizable: true,
          },
          {
            headerName: "Halted ON",
            field: "created_at",
            width: 100,
            resizable: true,
            sortable: true,
            sort: "desc",
            cellRenderer: function (params: any) {
              // const data = params.value ? moment(params.value).format("MM-DD-YYYY HH:mm:ss") : "";
              // return data;
              const data = params.value 
              ? moment(params.value).utc().format("MM-DD-YYYY HH:mm:ss") 
              : "";
            return data;
          
            },
            // valueFormatter: (params: any) => {
            //   const date = new Date(params.value); // Parse the datetime string
            //   const month = String(date.getMonth() + 1).padStart(2, "0"); // Extract month with leading zero
            //   const day = String(date.getDate()).padStart(2, "0"); // Extract day with leading zero
            //   const year = date.getFullYear(); // Extract year
            //   const hours = String(date.getHours()).padStart(2, "0"); // Extract hours with leading zero
            //   const minutes = String(date.getMinutes()).padStart(2, "0"); // Extract minutes with leading zero
            //   const seconds = String(date.getSeconds()).padStart(2, "0"); // Extract seconds with leading zero

            //   return `${month}-${day}-${year} ${hours}:${minutes}:${seconds}`; // Format as MM-DD-YYYY HH:MM:SS
            // },
          },
          {
            headerName: "Resumed ON",
            field: "resumed_on",
            width: 100,
            resizable: true,
            sortable: true,
            cellRenderer: function (params: any) {
              // const data = params.value ? moment(params.value).format("MM-DD-YYYY HH:mm:ss") : "";
              // return data;
              const data = params.value 
              ? moment(params.value).utc().format("MM-DD-YYYY HH:mm:ss") 
              : "";
            return data;
            },
            // valueFormatter: (params: any) => {
            //   if (params.value != null) {
            //     const date = new Date(params.value); // Parse the datetime string
            //     const month = String(date.getMonth() + 1).padStart(2, "0"); // Extract month with leading zero
            //     const day = String(date.getDate()).padStart(2, "0"); // Extract day with leading zero
            //     const year = date.getFullYear(); // Extract year
            //     const hours = String(date.getHours()).padStart(2, "0"); // Extract hours with leading zero
            //     const minutes = String(date.getMinutes()).padStart(2, "0"); // Extract minutes with leading zero
            //     const seconds = String(date.getSeconds()).padStart(2, "0"); // Extract seconds with leading zero
            //     return `${month}-${day}-${year} ${hours}:${minutes}:${seconds}`; // Format as MM-DD-YYYY HH:MM:SS
            //   }
            //   return "";
            // },
          },
          {
            headerName: "Status",
            field: "",
            width: 70,
            sortable: true,
            resizable: true,
            filter: "agSetColumnFilter",
            filterParams: {
              values: ["Complete", "Halt", "Failed"], // Filter values
              comparator: (a: any, b: any) => a.localeCompare(b), // Optional if you want to control sorting behavior
            },
            valueGetter: (params: any) => {
              // Compute the status value based on the row data
              const {
                is_mapper_load,
                is_audit_load,
                inv_seq_halt,
                is_dept_halt,
                is_make_halt,
                is_paytype_halt,
                is_process_queue_completed,
              } = params.data;

              // Define Complete status
              if (
                (is_mapper_load === true && is_audit_load === true) ||
                is_process_queue_completed === true
              ) {
                return "Complete";
              }
              // Define Failed status
              if (
                is_mapper_load === false ||
                is_audit_load === false ||
                is_process_queue_completed === false
              ) {
                return "Failed";
              }
              // Define Halt status
              if (
                inv_seq_halt === true ||
                is_dept_halt === true ||
                is_make_halt === true ||
                is_paytype_halt === true
              ) {
                return "Halt";
              }

              // If none of the conditions match, return null
              return null;
            },
            tooltipValueGetter: (params: any) => {
              // Show tooltip only when the file generation failed
              if (
                params.data.is_process_queue_completed == false &&
                params.data.is_process_queue_completed != null
              ) {
                return params.data.process_queue_comment || "No comment available";
              } else {
                if (
                  params.data.is_process_queue_completed == true &&
                  params.data.is_process_queue_completed != null
                ) {
                  return params.data.process_queue_comment || "No comment available";
                }
              }
              return null; // No tooltip for other cases
            },
            cellRenderer: function (params: any) {
            //  const dataString: any = JSON.stringify(params.data);
            let dataString = JSON.stringify(params.data);

              let escapedString = escapeSpecialCharacters(dataString);
              function escapeSpecialCharacters(dataString:any) {
                return dataString.replace(/[^a-zA-Z0-9]/g, function(char:any) {
                    return `&#${char.charCodeAt(0)};`;
                });
            }
              // Escape special characters if necessary (like quotes in HTML)
              dataString = escapedString;            
              const schedulerParamsid = btoa(params.data.scheduler_id);
              const companyParamsid = btoa(params.data.company_id);
              if (params.data) {
                let html = ``;
				let showDeleteIcon = false;  // Variable to track if Delete icon should be shown
                let deleteIconMarginLeft;
                if (
                  params.data.is_mapper_load == true &&
                  params.data.is_audit_load == true
                ) {
                  html += `<em class="badge badge-success badge-font " title="" data-toggle="tooltip" data-placement="left"  data-animation="false" >Complete</em>`;
                } else if (
                  (params.data.is_mapper_load == false &&
                    params.data.is_audit_load == true) ||
                  (params.data.is_mapper_load == true &&
                    params.data.is_audit_load == false) ||
                  (params.data.is_mapper_load == false &&
                    params.data.is_audit_load == false)
                ) {
                  html +=
                    '<em class="badge badge-danger badge-font" title="" data-toggle="tooltip" data-placement="left"  data-animation="false" >Failed</em>';
                } else if (
                  (params.data.inv_seq_halt == true ||
                    params.data.is_dept_halt == true ||
                    params.data.is_make_halt == true ||
                    params.data.is_paytype_halt == true) &&
                    ((params.data.is_process_queue_completed == null &&
                      params.data.resumed_on == null) ||(params.data.is_process_queue_completed == false &&
                        params.data.resumed_on != null) )
                ) {
                  html += `<em class="badge-custom halt-class" title="" data-toggle="tooltip" data-animation="false" data-placement="top"  data-info="${dataString}">Halt</em>`;
                   showDeleteIcon = true;
                   deleteIconMarginLeft = "5px"; 
}
                if (
                  params.data.is_process_queue_completed == false &&
                  params.data.is_process_queue_completed != null
                ) {
                  html += "";
                  html +=
                    '<em class="badge badge-danger badge-font" title="" data-toggle="tooltip" data-placement="left"  data-animation="false" >Failed</em>';
                		showDeleteIcon = true;
                     	deleteIconMarginLeft = "9px"; 
		 } else {
                  if (
                    params.data.is_process_queue_completed == true &&
                    params.data.is_process_queue_completed != null
                  ) {
                    html += `<span halt-encode='${schedulerParamsid}----------${companyParamsid}' class="badge badge-success">Complete</span>`;
                  }
                }
                // If either Halt or Failed condition was satisfied, show the delete icon
                if (showDeleteIcon) {
                  html += `<i class="fa fa-trash-o text-danger fa-2" aria-hidden="true" data-info="${dataString}" title="Delete" data-toggle="tooltip" data-placement="top" style="cursor: pointer; font-weight: bold; margin-left:${deleteIconMarginLeft}"></i>`;
                }
                return html;
              } else {
                return "";
              }
            },
          },
        ],
      },
    ];
    this.columnDefs1 = [
      {
        headerName: "Import Report ",
        size: "14",
        children: [
          {
            headerName: "Store Name",
            field: "company_name",
            width: 120,
            resizable: true,
          },

          {
            headerName: "Import Started",
            field: "import_start_on",
            width: 100,
            resizable: true,
            sortable: true,
            sort: "desc",
            cellRenderer: function (params: any) {
             // const data = params.value ? moment(params.value).format("MM-DD-YYYY HH:mm:ss") : "";
              
              const data = params.value 
              ? moment(params.value).utc().format("MM-DD-YYYY HH:mm:ss") 
              : "";
              return data;
            },
            //sort: "desc",
          },
          {
            headerName: "Staging File Created",
            field: "is_input_file_generated",
            width: 100,
            resizable: true,
            sortable: true,
            tooltipValueGetter: (params: any) => {
              // Show tooltip only when the file generation failed
              if (params.value === false) {
                return params.data.input_file_generated_comment || "No comment available";
              }
              return null; // No tooltip for other cases
            },
            cellRenderer: function (params: any) {
              let html = "<span></span>";
              if (params.value === false) {
                html += `<span class="badge badge-danger" style="cursor: pointer;" >Failed</span>`;
              } else if (params.value === true) {
                html += '<span class="badge badge-success">Complete</span>';
              }
              return html;
            },
          },
          {
            headerName: "Audit Imported ON",
            field: "audit_load_on",
            width: 80,
            resizable: true,
            cellRenderer: function (params: any) {
              // const data = params.value ? moment(params.value).format("MM-DD-YYYY HH:mm:ss") : "";
              // return data;
              const data = params.value 
              ? moment(params.value).utc().format("MM-DD-YYYY HH:mm:ss") 
              : "";
              return data;
            },
          },
          {
            headerName: "Audit Imported Status",
            field: "is_audit_load",
            width: 80,
            resizable: true,
            tooltipValueGetter: (params: any) => {
              // Show tooltip only when the file generation failed
              if (params.data.is_audit_load == false) {
                return params.data.audit_load_comment || "No comment available";
              }
              return null; // No tooltip for other cases
            },
            cellRenderer: function (params: any) {
              if (params.data) {
                let html = "<span></span>";
                if (params.data.is_audit_load == true) {
                  html += '<span class="badge badge-success">Complete</span>';
                } else if (params.data.is_audit_load == false) {
                  html += `<span class="badge badge-danger" style="cursor: pointer;" >Failed</span>`;
                }
                return html;
              } else {
                return "";
              }
            },
          },
          {
            headerName: "Mapper Imported ON",
            field: "mapper_load_on",
            width: 70,
            resizable: true,
            cellRenderer: function (params: any) {
              // const data = params.value ? moment(params.value).format("MM-DD-YYYY HH:mm:ss") : "";
              // return data;
              const data = params.value 
              ? moment(params.value).utc().format("MM-DD-YYYY HH:mm:ss") 
              : "";
              return data;
            },
          },
          {
            headerName: "Mapper Imported Status",
            field: "is_mapper_load",
            width: 70,
            resizable: true,
            tooltipValueGetter: (params: any) => {
              // Show tooltip only when the file generation failed
              if (params.data.is_mapper_load == false) {
                return params.data.mapper_load_comment || "No comment available";
              }
              return null; // No tooltip for other cases
            },
            cellRenderer: function (params: any) {
              if (params.data) {
                let html = "<span></span>";
                if (params.data.is_mapper_load == true) {
                  html += '<span class="badge badge-success">Complete</span>';
                } else if (params.data.is_mapper_load == false) {
                  html += `<span class="badge badge-danger"  style="cursor: pointer;">Failed</span>`;
                }
                return html;
              } else {
                return "";
              }
            },
          },
          {
            headerName: "Import Status",
            field: "",
            width: 70,
            resizable: true,
            sortable: true,
            filter: "agSetColumnFilter",
            filterParams: {
              values: ["Complete", "Failed"], // Filter values
              comparator: (a: any, b: any) => a.localeCompare(b), // Optional if you want to control sorting behavior
            },
            valueGetter: (params: any) => {
              // Compute the status value based on the row data
              const {
                is_mapper_load,
                is_audit_load,
                inv_seq_halt,
                is_dept_halt,
                is_make_halt,
                is_paytype_halt,
                is_input_file_generated,
              } = params.data;

              // Define Complete status
              if (
                (is_mapper_load === true && is_audit_load === true) ||
                is_input_file_generated === true
              ) {
                return "Complete";
              }
              // Define Failed status
              if (
                is_mapper_load === false ||
                is_audit_load === false ||
                is_input_file_generated === false
              ) {
                return "Failed";
              }
              // Define Halt status
              if (
                inv_seq_halt === true ||
                is_dept_halt === true ||
                is_make_halt === true ||
                is_paytype_halt === true
              ) {
                return "Halt";
              }

              // If none of the conditions match, return null
              return null;
            },
            cellRenderer: function (params: any) {
              const dataString: any = JSON.stringify(params.data);
              if (params.data) {
                let html = ``;
                if (
                  params.data.is_mapper_load == true &&
                  params.data.is_audit_load == true &&
                  params.data.is_input_file_generated == true
                ) {
                  html += `<em class="badge badge-success badge-font " title="" data-toggle="tooltip" data-placement="left"  data-animation="false" >Complete</em>`;
                } else if (
                  params.data.is_mapper_load === false ||
                  params.data.is_audit_load === false ||
                  params.data.is_input_file_generated === false
                ) {
                  html +=
                    '<em class="badge badge-danger badge-font" title="" data-toggle="tooltip" data-placement="left"  data-animation="false" style="cursor: pointer;">Failed</em>';
                } else if (
                  params.data.inv_seq_halt == true ||
                  params.data.is_dept_halt == true ||
                  params.data.is_make_halt == true ||
                  params.data.is_paytype_halt == true
                ) {
                  html += `<em class="badge-custom halt-class" title="" data-toggle="tooltip" data-animation="false" data-placement="top"  data-info='${dataString}'>Halt</em>`;
                }
                return html;
              } else {
                return "";
              }
            },
          },
          {
            headerName: "Submission UUID",
            field: "submission_uuid",
            width: 70,
            resizable: true,
            hide: true,
          },
       ],
      },
    ];

    this.cdkColDef1 = [
      { headerName: 'Store Name', field: 'storeName', editable: false },
      {
        headerName: 'Priority',
        field: 'priority',
        editable: true,
        cellEditor: 'agSelectCellEditor',      // Use AG Grid's select editor
        cellEditorParams: {
          values: ['1', '2', '3', '4', '5', '6', '7'],  // Dropdown options
        },
      },
    ];
    
    
  }
  getSchedulerImportDetails(callback: any) {
    // this.rowData = [];
    this.importDetailsList = [];
    const exceptionDetails = this.apollo
      .use("manageScheduler")
      .query({
        query: getSchedulerImportDetails,
        fetchPolicy: "network-only",
        variables: {
          inStartDate: moment(this.inFromDate).format("YYYY-MM-DD"),
          inEndDate: moment(this.inToDate).format("YYYY-MM-DD"),
        },
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata) => {
          const result: any = listdata;
          const resp = JSON.parse(result["data"]["getSchedulerImportDetails"]);
          this.importDetailsList = resp;
          callback();
        },
        error: (err) => {
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed : getSchedulerImportDetails");
        },
      });
  }
  onCellValueChanged(event: any,type: string) {
    const updatedData = event.data;
    console.log('Priority updated$$$$$$$$$$$$$$$$$$$$$$$$$$$$:', updatedData);
    console.log('fileName:$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$', updatedData.fileName);
    let payload = {
      fileName:updatedData.fileName,
      dms:`${type}_queue`,
      priority:updatedData.priority

    };
   console.log("Update priority payLoad^^^^^^^^^^^^^^^^^^^",payload);
   
    let url = environment.updatePriority;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    console.log("headers@@@@@@@@@@@@@@@@@@@",headers);
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
    console.log("get Extract Job data???????????????????????????????????????????????????????",res);
    })



  
  }

  onDealerTrackCellValueChanged(event:any){
    const updatedData = event.data;
    console.log('Priority updated:', updatedData);
    console.log('fileName:', updatedData.fileName);
    let payload = {
      fileName:updatedData.fileName,
      dms:'dealertrack_queue',
      priority:updatedData.priority

    };
   console.log("Update priority payLoad^^^^^^^^^^^^^^^^^^^",payload);
   
    let url = environment.updatePriority;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    console.log("headers@@@@@@@@@@@@@@@@@@@",headers);
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
    console.log("get Extract Job data???????????????????????????????????????????????????????",res);
    })

  }

  onReynoldsCellValueChanged(event:any){
    const updatedData = event.data;
    console.log('Priority updated:', updatedData);
    console.log('fileName:', updatedData.fileName);
    let payload = {
      fileName:updatedData.fileName,
      dms:'reynolds_queue',
      priority:updatedData.priority

    };
   console.log("Update priority payLoad^^^^^^^^^^^^^^^^^^^",payload);
   
    let url = environment.updatePriority;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    console.log("headers@@@@@@@@@@@@@@@@@@@",headers);
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
    console.log("get Extract Job data???????????????????????????????????????????????????????",res);
    })
  }

  onAutoMateCellValueChanged(event:any){
    
    const updatedData = event.data;
    console.log('Priority updated:', updatedData);
    console.log('fileName:', updatedData.fileName);
    let payload = {
      fileName:updatedData.fileName,
      dms:'automate_queue',
      priority:updatedData.priority

    };
   console.log("Update priority payLoad^^^^^^^^^^^^^^^^^^^",payload);
   
    let url = environment.updatePriority;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    console.log("headers@@@@@@@@@@@@@@@@@@@",headers);
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
    console.log("get Extract Job data???????????????????????????????????????????????????????",res);
    })
  }

  onAutoSoftCellValueChanged(event:any){
    const updatedData = event.data;
    console.log('Priority updated:', updatedData);
    console.log('fileName:', updatedData.fileName);
    let payload = {
      fileName:updatedData.fileName,
      dms:'autosoft_queue',
      priority:updatedData.priority

    };
   console.log("Update priority payLoad^^^^^^^^^^^^^^^^^^^",payload);
   
    let url = environment.updatePriority;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    console.log("headers@@@@@@@@@@@@@@@@@@@",headers);
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
    console.log("get Extract Job data???????????????????????????????????????????????????????",res);
    })
  }

  onDealerBuiltCellValueChanged(event:any){
    const updatedData = event.data;
    console.log('Priority updated:', updatedData);
    console.log('fileName:', updatedData.fileName);
    let payload = {
      fileName:updatedData.fileName,
      dms:'autosoft_queue',
      priority:updatedData.priority

    };
   console.log("Update priority payLoad^^^^^^^^^^^^^^^^^^^",payload);
   
    let url = environment.updatePriority;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    console.log("headers@@@@@@@@@@@@@@@@@@@",headers);
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
    console.log("get Extract Job data???????????????????????????????????????????????????????",res);
    })
  }
  toggleExpandCollapse(index: number): void {
    this.queues[index].isExpanded = !this.queues[index].isExpanded;
  }
  

  onDominionCellValueChanged(event:any){
    const updatedData = event.data;
    console.log('Priority updated:', updatedData);
    console.log('fileName:', updatedData.fileName);
    let payload = {
      fileName:updatedData.fileName,
      dms:'dominion_queue',
      priority:updatedData.priority

    };
   console.log("Update priority payLoad^^^^^^^^^^^^^^^^^^^",payload);
   
    let url = environment.updatePriority;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    console.log("headers@@@@@@@@@@@@@@@@@@@",headers);
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
    console.log("get Extract Job data???????????????????????????????????????????????????????",res);
    })
  }
  
  onTekionAPICellValueChanged(event:any){
    const updatedData = event.data;
    console.log('Priority updated:', updatedData);
    console.log('fileName:', updatedData.fileName);
    let payload = {
      fileName:updatedData.fileName,
      dms:'tekionapi_queue',
      priority:updatedData.priority

    };
   console.log("Update priority payLoad^^^^^^^^^^^^^^^^^^^",payload);
   
    let url = environment.updatePriority;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    console.log("headers@@@@@@@@@@@@@@@@@@@",headers);
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
    console.log("get Extract Job data???????????????????????????????????????????????????????",res);
    })
  }
  
  getSchedulerPreImportStatusDetails(callback: any) {
    // this.rowData = [];
    this.preImportStausList = [];
    if (this.gridApi) {
      this.gridApi.showLoadingOverlay();
    }
    const exceptionDetails = this.apollo
      .use("manageScheduler")
      .query({
        query: getSchedulerPreImportStatusDetails,
        fetchPolicy: "network-only",
        variables: {
          inStartDate: moment(this.inFromDate).format("YYYY-MM-DD"),
          inEndDate: moment(this.inToDate).format("YYYY-MM-DD"),
        },
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata) => {
          const result: any = listdata;
          if (
            result["data"]["getSchedulerPreImportStatusDetails"] &&
            result["data"]["getSchedulerPreImportStatusDetails"]
          ) {
            const resp = JSON.parse(
              result["data"]["getSchedulerPreImportStatusDetails"]
            );
            this.preImportStausList = resp;
            if (this.gridApi && typeof this.gridApi.hideLoadingOverlay === "function") {
              this.gridApi.hideLoadingOverlay();
            }
          }
          callback();
        },
        error: (err) => {
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed : getSchedulerPreImportStatusDetails");
        },
      });
  }

  closeHaltReasonModal() {
    $("#haltReasonModal").modal("hide");
  }
  openHaltReasonModal(info: any) {
    const item = JSON.parse(info);
    this.manufacturerData=item;
    this.invSeqHalt = item.inv_seq_halt == true ? item.inv_seq_halt : false;
    this.isDeptHalt = item.is_dept_halt == true ? item.is_dept_halt : false;
    this.isMakeHalt = item.is_make_halt == true ? item.is_make_halt : false;
    this.isPaytypeHalt =
      item.is_paytype_halt == true ? item.is_paytype_halt : false;
    $("#haltReasonModal").modal("show");
  }
  gotoScheduleImport() {
    console.log("haltReason--------", event, this.manufacturerData);
    $("#haltReasonModal").modal("hide");
    const companyId = this.manufacturerData.company_id;
    const schedulerId = this.manufacturerData.scheduler_id;
    // const manufacturer =this.manufacturerData.brand_list;
    const projectId = this.manufacturerData.project_id_list;
    console.log("gotoScheduleImport@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",projectId);
    this.saveUser(schedulerId,companyId,projectId)
    
  }

  getchedulerStatusSearchList() {
    this.downloading = true;
    this.searcheportSpinner = true;
    this.getSchedulerImportDetails(() => {
      if (this.gridImportApi) {
        this.gridImportApi.showLoadingOverlay();
      }
      setTimeout(() => {
        if (this.gridImportApi) this.gridImportApi.sizeColumnsToFit();
      }, 50);
      this.getSchedulerPreImportStatusDetails(() => {
        if (this.gridApi) {
          this.gridColumnApi.resetColumnState();
          this.gridApi.showLoadingOverlay();
        }
        setTimeout(() => {
          if (this.gridApi) this.gridApi.sizeColumnsToFit();
        }, 50);
        this.generateReports();
      });
    });
  }
  getStoreCompanyDetails(compId: any, schedulerId: any, projectId: any, callback: any) {
    const exceptionDetails = this.apollo
      .use("manageScheduler")
      .query({
        query: getStoreCompanyDetails,
        fetchPolicy: "network-only",
        variables: {
          inCompanyId: compId
        },
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata:any) => {
          const result: any = listdata;
          const resp = JSON.parse(result["data"]["getCompanyDetails"]);
          this.companyName=resp[0].company_name;
          this.dms=resp[0]['dms'];
          this.state=resp[0]['state'];
          this.brandList=resp[0].brand_list;
          const queryParams = {
            companyId: compId,
            companyName:  this.companyName,
            schedulerId: schedulerId,
            manufacturer: this.brandList,
            dmsList: this.dms,
            state: this.state,
            projectId: projectId,
            invSeqHalt: this.invSeqHalt,
            isDeptHalt: this.isDeptHalt,
            isPaytypeHalt: this.isPaytypeHalt,
            isMakeHalt: this.isMakeHalt,
          };
          // Convert object to JSON string
          const jsonString = JSON.stringify(queryParams);
          // Encode JSON string to Base64
          const base64QueryString = btoa(jsonString);
          console.log(queryParams,"querySchedulerImportParams")
         this.SCHEDULERIMPORT_URL = this.constantService.SCHEDULERIMPORT_URL;
          this.router.navigate([`${this.SCHEDULERIMPORT_URL}/${base64QueryString}`]);
          callback();
        },
        error: (err:any) => {
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed : getStoreCompanyDetails");
        },
      });
  }
  deletePreImportStatus(eventInfo:any, callback: any){
    const self = this;

    swal(
      {
        title:this.constantService.AREYOUSURE,
        text: "This pre import will be deleted",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default pointer",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: "Delete",
        showLoaderOnConfirm: true,
        closeOnConfirm: true,
        closeOnCancel: true,
        allowOutsideClick: false,
      },
      function (isConfirm: any) {
        // Using arrow function here
        console.log(isConfirm, "isConfirmisConfirm");
        if (isConfirm) {
           // Proceed with delete if user confirms
          const data = JSON.parse(eventInfo);
          const schedulerId = data.scheduler_id;

          const dbObj: any = {
            inSchedulerId: schedulerId,
            inIsDeleted: true,
          };
          const activityData1 = {
            activityName: "update preImportStatusIsDeleted",
            activityDescription: "update preImportStatusIsDeleted: started",
            activityData: dbObj,
          };
          self.commonService.saveActivity("Delete Pre Import Status", activityData1);

          const subscription = self.apollo.use("manageScheduler").mutate({
            mutation: preImportStatusDeleteUpdation,
            variables: dbObj,
          });

          subscription.pipe(takeUntil(self.subscription$)).subscribe(
            ({ data }) => {
              NProgress.done();
              const result: any = data;
              const activityData2 = {
                activityName: "update preImportStatusIsDeleted",
                activityDescription: "update preImportStatusIsDeleted: completed",
                activityData: data,
              };
              self.commonService.saveActivity("Delete Pre Import Status", activityData2);

              const obj = JSON.parse(result["preImportStatusIsDeleted"]["json"]);
              if (obj.status === self.constantService.SUCCESS_MESSAGE) {
                self.getSchedulerPreImportStatusDetails(() => {
                  if (self.gridApi) {
                    self.gridColumnApi.resetColumnState();
                    self.gridApi.showLoadingOverlay();
                  }
                  setTimeout(() => {
                    if (self.gridApi) self.gridApi.sizeColumnsToFit();
                  }, 50);
          
                  self.generateReports();
                });
              self.changeDetectorRefCheck();
                if (callback) {
                  callback(true);
                }
              } else {
                swal({
                  title: self.constantService.CANNOT_FETCH_DATA,
                  type: "warning",
                  confirmButtonClass: "btn-warning pointer",
                  confirmButtonText: self.constantService.CLOSE,
                  allowOutsideClick: false,
                });
                if (callback) {
                  callback(false);
                }
              }
            },
            (err: any) => {
              NProgress.done();
              swal({
                title: self.constantService.CANNOT_FETCH_DATA,
                type: "warning",
                confirmButtonClass: "btn-warning pointer",
                confirmButtonText: self.constantService.CLOSE,
                allowOutsideClick: false,
              });
              self.commonService.errorCallback(err, self);
              if (callback) {
                callback(false);
              }
            }
          );

          self.subscriptionList.push(subscription);
          return subscription;
        } else {
          console.log(isConfirm, "isConfirmisConfirmmmmm");

          if (callback) {
            callback(false);
          }
          return "";

        }
      }
      
    )};
    changeDetectorRefCheck() {
      this.changeDetectorRef.markForCheck();
    }

    saveUser(scheduler_id:any,company_id:any,projectId:any) {
      try {
        // const parsedData = JSON.parse(data);
        // const { scheduler_id, company_id } = parsedData;
        const currentUserObj = JSON.parse(localStorage.getItem("currentUser") || "{}");
        const userName = currentUserObj?.userPrincipalName || "";
       
        const payload = { schedulerId: scheduler_id, companyId: company_id, userName };
    
       console.log("payload@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",payload);
        const token = localStorage.getItem("token");
        const headers = new HttpHeaders({
          authorization: token ? `Bearer ${token}` : "",
        });
    
        
        console.log("Sending request to save user", { payload, headers });
    
        this.http.post(environment.getUserName, payload, { headers }).subscribe(
          (res: any) => {
            console.log("res$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",res);
            const useBy = res?.data?.use_by;
            
            if (useBy) {
              swal(
                {
                  title: this.constantService.AREYOUSURE,
                  text: `The resume is in progress by the user ${useBy}. Do you want to continue?`,
                  type: "warning",
                  showCancelButton: true,
                  cancelButtonClass: "btn-default pointer",
                  confirmButtonClass: "btn-warning pointer",
                  confirmButtonText: "Continue",
                  closeOnConfirm: true,
                  showLoaderOnConfirm: true,
                },
                () => {
                  this.updateResumeduserName(scheduler_id,company_id,projectId);
                  this.getStoreCompanyDetails(company_id, scheduler_id, projectId, () => {
                  });
                }
              );
            } else {
              this.updateResumeduserName(scheduler_id,company_id,projectId);
              this.getStoreCompanyDetails(company_id, scheduler_id, projectId, () => {
              });
        
            }
          },
          (error) => {
            console.error("Error saving user:", error);
          }
        );
      } catch (error) {
        console.error("Error parsing user data:", error);
      }
    }

    updateResumeduserName(schedulerId:any, companyId:any, projectId:any){
             

      try {
        // const parsedData = JSON.parse(data);
        // const { scheduler_id, company_id } = parsedData;
        const currentUserObj = JSON.parse(localStorage.getItem("currentUser") || "{}");
        const userName = currentUserObj?.userPrincipalName || "";
       
        const payload = { schedulerId: schedulerId, companyId: companyId, userName };
    
       console.log("payload@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",payload);
        const token = localStorage.getItem("token");
        const headers = new HttpHeaders({
          authorization: token ? `Bearer ${token}` : "",
        });
    
        
        console.log("Sending request to save user!!!!!!!!!!!", { payload, headers });
    
        this.http.post(environment.updateUserName, payload, { headers }).subscribe(
          (res: any) => {
           console.log("Update resume user res$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",res);
             
           
          },
          (error) => {
            console.error("Error saving user:", error);
          }
        );
      } catch (error) {
        console.error("Error parsing user data:", error);
      }
    }
    

 
}

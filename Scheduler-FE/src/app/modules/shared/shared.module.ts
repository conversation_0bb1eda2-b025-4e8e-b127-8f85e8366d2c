import {
  NgModule,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
} from "@angular/core";
import { CommonModule, Ng<PERSON><PERSON>, NgFor } from "@angular/common";
import { NgMultiSelectDropDownModule } from "ng-multiselect-dropdown";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { FieldErrorDisplayComponent } from "src/app/structure/components/field-error-display/field-error-display.component";
import { AgGridModule } from "ag-grid-angular";
import { NgDynamicBreadcrumbModule } from "ng-dynamic-breadcrumb";
import { ProgressbarModule } from "ngx-bootstrap/progressbar";
import { DmFormGroupService } from "src/app/structure/services/dm.formgroup.services";
import { AngularMultiSelectModule } from "angular2-multiselect-dropdown";
import { ApolloModule } from "apollo-angular";
import { HttpClientModule } from "@angular/common/http";
import { GraphQLModule } from "src/app/graphql.module";
import { ConstantService } from "src/app/structure/constants/constant.service";
import { CommonService } from "src/app/structure/services/common.service";
import { EventEmitterService } from "src/app/structure/services/event.emitter.services";
import { SchedulerConstantService } from "src/app/structure/constants/scheduler.constant.service";
import { FocusOnClickDirective } from "src/app/structure/components/formatter/focus-on-directive/focus-on-click.directive";
import { BsDatepickerModule } from "ngx-bootstrap/datepicker";

@NgModule({
  declarations:[FocusOnClickDirective],
  exports: [
    FieldErrorDisplayComponent,
    FormsModule,
    ReactiveFormsModule,
    AgGridModule,
    NgDynamicBreadcrumbModule,
    ProgressbarModule,
    AngularMultiSelectModule,
    FormsModule,
    NgClass,
    NgMultiSelectDropDownModule,
    FieldErrorDisplayComponent,
    NgFor,
    CommonModule,
    BsDatepickerModule,
    FocusOnClickDirective
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FieldErrorDisplayComponent,
    GraphQLModule,
    HttpClientModule,
    ApolloModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
  providers: [
    ConstantService,
    DmFormGroupService,
    EventEmitterService,
    SchedulerConstantService,
    CommonService,
    FocusOnClickDirective,
  ],
})
export class SharedModule {}

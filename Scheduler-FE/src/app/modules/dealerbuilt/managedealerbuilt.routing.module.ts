import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManagedealerbuiltComponent } from './managedealerbuilt.component';
const routes: Routes = [
    {
      path: "",
      component: ManagedealerbuiltComponent,
      data: {
        title: "ManageDealerBuilt",
        breadcrumb: [{ label: "ManageDealerBuilt", url: "" }],
      },
    },
  ];
  
@NgModule({
    exports: [RouterModule],
    imports:[RouterModule.forChild(routes)]
})
export class ManagedealerbuiltRoutingModule{}
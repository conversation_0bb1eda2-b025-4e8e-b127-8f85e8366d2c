import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManagecdkflexjobsComponent } from "./managecdkflexjobs.component";

const routes: Routes = [
  {
    path: "",
    component: ManagecdkflexjobsComponent,
    data: {
      title: "ManageCDKFlex",
      breadcrumb: [{ label: "ManageCDKFlex", url: "" }],
    },
  },
];

@NgModule({
  exports: [RouterModule],
  imports: [RouterModule.forChild(routes)],
})
export class ManagecdkflexjobsRoutingModule {}

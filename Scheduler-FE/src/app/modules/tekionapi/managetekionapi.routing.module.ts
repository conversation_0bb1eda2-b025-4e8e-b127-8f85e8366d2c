import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManagetekionapiComponent } from './managetekionapi.component';

const routes: Routes = [
    { path:"", component: ManagetekionapiComponent ,
    data: {
        title: "ManageTekionApi",
        breadcrumb: [{ label: "ManageTekionApi", url: "" }],
      },}
];

@NgModule({
    exports: [RouterModule],
    imports:[RouterModule.forChild(routes)]
})
export class ManageTekionapiRoutingModule{}
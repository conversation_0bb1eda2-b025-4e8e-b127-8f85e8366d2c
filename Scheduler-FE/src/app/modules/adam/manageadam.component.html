<div class="row">
  <div class="col-lg-12">
    <div *ngIf="isAuthenticated">
      <section class="card" order-id="card-1">
        <div class="card-header cursor_default">
          <span class="cat__core__title">
            <div style="font-size: 16px; color: #000; padding-left: 3%">
              <span>
                <a
                  href="{{ agendaDashboardUrl }}"
                  target="_blank"
                  class="text-warning"
                  >Agenda Dashboard<i
                    style="margin-left: 5px; color: #f19b1a"
                    class="fa fa-external-link"
                  ></i
                ></a>
              </span>
              <span style="position: absolute; left: 20px; top: 10px">
                <i
                  *ngIf="isPaused"
                  (click)="stopTimer()"
                  class="fa fa-pause"
                  style="cursor: pointer"
                ></i>
                <i
                  *ngIf="!isPaused"
                  (click)="startTimer()"
                  class="fa fa-play"
                  style="cursor: pointer"
                ></i>
                <i
                  *ngIf="isPaused"
                  class="fa fa-spinner fa-pulse fa-1x fa-fw"
                ></i>
                <i *ngIf="!isPaused" class="fa fa-spinner"></i>
              </span>
            </div>
          </span>
        </div>
        <div class="card-block clear-padding">
         
          <form class="new-form" [formGroup]="createSchedule">
            <div class="row">
              <div class="col-lg-12 row">
                <div class="col-lg-3" *ngIf="!isTestSchedule" >
                  <div id="input-group" style="float: left">
                    <label style="padding-top: 10px">
                      <input
                        type="checkbox"
                        formControlName="updateRetreiveROinSolve360"
                        name="updateRetreiveROinSolve360"
                      />
                      &nbsp;
                    </label>
                  </div>
                  <label
                    class="col-form-label"
                    style="font-weight: 700; float: left"
                  >
                    Update retreive RO in Solve360</label
                  >
                </div>
                <div class="col-lg-2 first-area">
                  <div id="input-group" style="float: left">
                    <label style="padding-top: 10px">
                      <input
                        type="checkbox"
                        formControlName="buildProxies"
                        name="buildProxies"
                      />
                      &nbsp;
                    </label>
                  </div>
                  <label
                    class="col-form-label"
                    style="font-weight: 700; float: left"
                  >
                    Build proxies</label
                  >
                </div>
                <div class="col-lg-2 first-area">
                  <div id="input-group" style="float: left">
                    <label style="padding-top: 10px">
                      <!-- <input type="checkbox" formControlName="solve360ServerDecider" name="solve360ServerDecider"  [(ngModel)]="solve360ServerDecider" (change)="switchServer()"/> -->
                      <input
                        type="checkbox"
                        [(ngModel)]="solve360ServerDecider"
                        formControlName="solve360ServerDecider"
                        name="solve360ServerDecider"
                        (change)="switchServer(true)"
                        *ngIf="isMockServer"
                      />
                      &nbsp;
                    </label>
                  </div>
                  <label
                    class="col-form-label"
                    style="font-weight: 700; float: left"
                    *ngIf="isMockServer"
                  >
                    Mock Server</label
                  >
                </div>
                <div class="col-lg-1">
                  <div id="input-group" style="float:left;">
                    <label style="padding-top: 10px;">
                      <input type="checkbox" formControlName="testSchedule" name="testSchedule" (change) ="changeSchedule($event)"  />
                      &nbsp;
                    </label>
                  </div>
                  <label class="col-form-label" style="font-weight: 700;float:left;"> Save file only </label>
                </div>
              </div>
              <div class="col-lg-12 row">
                <ng-container *ngIf="!isTestSchedule; else testScheduleBlock">
                <div class="col-lg-3">
                  <label class="col-form-label" style="font-weight: 700"
                    >Store Group <span style="color: red;">*</span>
                  </label>
                  <span
                    *ngIf="shows360Link && s360CompanyId"
                    class="pull-right text-warning"
                    style="padding-top: 10px"
                    ><a
                      class="text-warning"
                      target="_blank"
                      href="https://secure.solve360.com/company/{{
                        s360CompanyId
                      }}"
                      >s360 Group
                      <em
                        aria-hidden="true"
                        class="fa fa-external-link"
                      ></em></a
                  ></span>
                  <div
                    id="storeGroup-select"
                    *ngIf="!loading"
                    [ngClass]="displayFieldCssCreateSchedule('storeGroup')"
                  >
                    <ng-multiselect-dropdown
                      appFocusOnClick
                      class="searchicon-dropdown rounded-selection rouded-sel-label multi-search filter-type-status"
                      [placeholder]="'Select Item'"
                      formControlName="storeGroup"
                      [(ngModel)]="storeGroup"
                      [data]="storeGroupFilterList"
                      [settings]="singleDropdownSettings"
                      (onSelect)="onSelectStoreGroup($event)"
                      (onDeSelect)="OnDeSelectStoreGroup($event)"

                    >
                    </ng-multiselect-dropdown>
                  </div>
                  <div
                    id="storeGroup-select"
                    *ngIf="loading"
                    [ngClass]="displayFieldCssCreateSchedule('storeGroup')"
                  >
                    <ng-multiselect-dropdown
                      appFocusOnClick
                      class="searchicon-dropdown"
                      formControlName="storeGroup"
                      [(ngModel)]="storeGroup"
                      [data]="storeGroupFilterList"
                      [settings]="singleDropdownSettingsDisable"
                      (onSelect)="onSelectStoreGroup($event)"
                      (onDeSelect)="OnDeSelectStoreGroup($event)"
                    ></ng-multiselect-dropdown>
                  </div>
                  <app-field-error-display
                    [displayError]="isFieldValidCreateSchedule('storeGroup')"
                    errorMsg="{{ constantService.ERROR_MESSAGE_FOR_REQUIRED }}"
                  >
                  </app-field-error-display>               
                </div>
                <span
                class="pull-right"
                data-placement="top"
                data-toggle="tooltip"
                style="
                  z-index: 1;
                  position: relative;
                  top: 52px;
                "
                data-original-title="Click to refresh"
                data-animation="false"
              >
                <em
                  (click)="reloadGroupList()"
                  class="plus fa fa-refresh fa-2x pointer"
                  style="font-size: 20px"
                  *ngIf="!reloadGroup"
                ></em>
                <em
                  class="plus fa fa-refresh fa-spin fa-2x"
                  style="font-size: 20px"
                  *ngIf="reloadGroup"
                ></em>
              </span>
            </ng-container>
            <ng-template #testScheduleBlock>
          
              <div class="col-lg-3">
                <label class="col-form-label" style="font-weight: 700;">Mage Group Code <span style="color: red;">*</span></label>
                <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" formControlName="testGroupCode">
              </div>
            </ng-template>
            <ng-container *ngIf="!isTestSchedule; else testScheduleBlock2"> 
                <div class="col-lg-2">
                  <label
                    class="col-form-label"
                    style="font-weight: 700;"
                    >Store <span style="color: red;">*</span>
                  </label>
                  <div
                    id="store-select"
                    *ngIf="!loading"
                    [ngClass]="displayFieldCssCreateSchedule('store')"
                  >
                    <ng-multiselect-dropdown
                      appFocusOnClick
                      class="select-sp-report searchicon-dropdown filter-type-status multi-search"
                      [data]="storeFilterList"
                      formControlName="store"
                      [(ngModel)]="store"
                      [settings]="multiDropdownSettings"
                      (onSelect)="onSelectStore($event)"
                    ></ng-multiselect-dropdown>
                  </div>
                  <div
                    id="store-select"
                    *ngIf="loading"
                    [ngClass]="displayFieldCssCreateSchedule('store')"
                  >
                    <ng-multiselect-dropdown
                      appFocusOnClick
                      class="searchicon-dropdown"
                      [data]="storeFilterList"
                      formControlName="store"
                      [(ngModel)]="store"
                      [settings]="multiDropdownSettingsDisable"
                      (onSelect)="onSelectStore($event)"
                    ></ng-multiselect-dropdown>
                  </div>
                  <app-field-error-display
                    [displayError]="isFieldValidCreateSchedule('store')"
                    errorMsg="{{ constantService.ERROR_MESSAGE_FOR_REQUIRED }}"
                  >
                  </app-field-error-display>
                </div>
              </ng-container> 
              
              <ng-template #testScheduleBlock2>
            
                <div class="col-lg-2">
                  <label class="col-form-label" style="font-weight: 700;">Mage Store Code <span style="color: red;">*</span></label>
                  <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" formControlName="testStoreCode">
                </div>
              </ng-template>
                <div class="col-lg-2">
                  <label
                    class="col-form-label"
                    style="font-weight: 700;"
                    >Extraction Type</label
                  >
                  <div id="input-group">
                    <label style="padding-top: 10px">
                      <input
                        type="radio"
                        formControlName="jobType"
                        [checked]="true"
                        name="jobType"
                        value="initial"
                        (click)="setDateRange(true, '')"
                      />
                      Initial &nbsp;
                    </label>
                    <label style="padding-top: 10px">
                      <input
                        type="radio"
                        formControlName="jobType"
                        name="jobType"
                        value="refresh"
                        (click)="setDateRange(false, 'refresh')"
                      />
                      Refresh &nbsp;
                    </label>
                    <!-- <label style="padding-top: 10px;">
                      <input type="radio" formControlName="jobType" name="jobType" value=on_demand
                        (click)="setDateRange(false, 'current')" />
                      On Demand
                      &nbsp;
                    </label> -->
                  </div>
                </div>
                <div class="col-lg-2">
                  <label class="col-form-label" style="font-weight: 700"
                    >Data Extraction Date Range</label
                  >
                  <div>
                    <input
                    type="text"
                    class="form-control de-form-control"
                    placeholder="Select date range"
                    bsDaterangepicker
                    #d="bsDaterangepicker"
                    [(bsValue)]="selectedRange"                   
                    (bsValueChange)="onDateRangeChange($event)"
                  />
                    <span class="de-icon"
                    ><i
                      class="fa fa-calendar"
                      aria-hidden="true"
                      (click)="d.toggle()"
                    ></i
                  ></span>
                  </div>                
                </div>            
                <div class="col-lg-2">
                  <label
                    class="col-form-label"
                    style="font-weight: 700;"
                    >Extraction Mode</label
                  >
                  <div id="input-group">
                    <label style="padding-top: 10px">
                      <input
                        type="radio"
                        formControlName="roOption"
                        name="roOption"
                        value="all"
                        [attr.disabled]="displayOnDemand ? 'true' : null"
                      />
                      All &nbsp;
                    </label>
                    <label style="padding-top: 10px">
                      <input
                        type="radio"
                        formControlName="roOption"
                        [attr.disabled]="
                          !selectMultiDateRangeOption ? 'true' : null
                        "
                        name="roOption"
                        [checked]="true"
                        value="monthly"
                      />
                      Monthly &nbsp;
                    </label>
                    <label style="padding-top: 10px">
                      <input
                        type="radio"
                        formControlName="roOption"
                        [attr.disabled]="
                          !selectMultiDateRangeOption ? 'true' : null
                        "
                        name="roOption"
                        value="weekly"
                      />
                      Weekly &nbsp;
                    </label>
                    <label style="padding-top: 10px" *ngIf="displayOnDemand">
                      <input
                        type="radio"
                        formControlName="roOption"
                        [attr.checked]="true"
                        name="roOption"
                        value="current"
                      />
                      Current &nbsp;
                    </label>
                  </div>
                </div>
                <ng-container *ngIf="!isTestSchedule; else testScheduleBlock3">
                <div class="col-lg-1 row">
                  <div
                    class="pull-right"
                    style="margin-top: 41px; padding-right: 5px"
                  >
                    <button
                      type="button"
                      [disabled]="!createSchedule.valid"
                      (click)="saveSchedule()"
                      class="btn btn-primary btn pointer btn-sm"
                      style="height: 28px; width: 61px"
                    >
                      ADD
                    </button>
                  </div>
                </div>
              </ng-container>
              <ng-template #testScheduleBlock3>
            
                <div class="col-lg-1 row" style="padding-left: 40px;">
                  <div class="pull-right" style="margin-top: 41px;padding-right: 5px;">
                    <button type="button" (click)="saveTestSchedule()"
                      class="btn btn-primary btn pointer btn-sm" style="height: 28px;width: 61px;">ADD</button>
                  </div>
                </div>
              </ng-template>
              </div>
              <div class="col-lg-12 row">
                <ng-container *ngIf="isTestSchedule">
                  <div class="col-lg-3">
                    <label class="col-form-label" style="font-weight: 700;">Third Party UserName <span style="color: red;">*</span> </label>
                    <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" formControlName="testDealerId">
                  </div>
                </ng-container>
              </div>
            </div>
          </form>
          <div
            *ngIf="loadAllStore"
            style="position: absolute; z-index: 5000; top: 13%; left: 43%"
          >
            <div class="text-center">
              <em class="fa fa-spinner fa-pulse fa-2x fa-fw"> </em
              ><span class="sr-only">Loading...</span>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="card-block col-lg-6">
            <div class="accordion" id="accordion">
              <div class="card">
                <div
                  class="card-header"
                  [ngClass]="{ collapsed: processQueueListCollapsed }"
                  role="tab"
                  id="headingOne5"
                  data-toggle="collapse"
                  data-parent="#accordion"
                >
                  <div
                    class="card-title cat__core__step cat__core__step--squared cat__core__step--success"
                  >
                    <span
                      class="accordion-indicator pull-right"
                      (click)="processQueueToggle()"
                    >
                      <em class="plus fa fa-plus"></em>
                      <em class="minus fa fa-minus"></em>
                    </span>
                    <a class="text-success">
                      <strong>Extraction Queue</strong>
                    </a>
                    <span
                      class="pull-right"
                      style="padding-left: 15px; padding-right: 14px"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Click to refresh"
                      data-animation="false"
                    >
                      <em
                        class="plus fa fa-refresh fa-2x pointer"
                        style="font-size: 20px"
                        *ngIf="!loadingSchedule"
                        (click)="refreshScheduleList()"
                      ></em>
                      <em
                        class="plus fa fa-refresh fa-spin fa-2x"
                        style="font-size: 20px"
                        *ngIf="loadingSchedule"
                      ></em>
                    </span>
                  </div>
                </div>
                <div
                  id="collapseOne5"
                  class="card-collapse"
                  [ngClass]="{ collapse: processQueueListCollapsed }"
                  role="tabcard"
                  aria-labelledby="headingOne5"
                >
                  <div class="card-block">
                    <div
                      class="table-responsive private-network-table"
                      style="overflow-x: hidden"
                    >
                      <div
                        [hidden]="!scheduleProcessQueueLoading"
                        style="top: 17%; position: absolute; left: 50%"
                      >
                        <div class="text-center">
                          <em class="fa fa-spinner fa-pulse fa-2x fa-fw"> </em
                          ><span class="sr-only">Loading...</span>
                        </div>
                      </div>
                      <div
                        style="padding: 25px"
                        id="DataTables_Table_0_wrapper"
                        class="dataTables_wrapper no-footer"
                      >
                        <div
                          id="DataTables_Table_0_filter"
                          class="dataTables_filter"
                        ></div>
                        <table
                          id="scheduleProcessQueueAdam"
                          class="table dataTable no-footer dmTable"
                          cellspacing="0"
                          role="grid"
                          aria-describedby="DataTables_Table_0_info"
                          width="100%"
                        >
                          <thead>
                            <tr>
                              <th>Store</th>
                              <th>Extraction Type</th>
                              <th>Data Extraction Range</th>
                              <th>Schedule Date</th>
                              <th>Status</th>
                              <th>Actions</th>
                            </tr>
                          </thead>
                          <tbody></tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card-block col-lg-6">
            <div class="accordion" id="accordion">
              <div class="card">
                <div
                  class="card-header"
                  [ngClass]="{ collapsed: completedProcessjsonListCollapsed }"
                  role="tab"
                  id="headingOne5"
                  data-toggle="collapse"
                  data-parent="#accordion"
                >
                  <div
                    class="card-title cat__core__step cat__core__step--squared cat__core__step--success"
                  >
                    <span
                      class="accordion-indicator pull-right"
                      (click)="processJsonToggle()"
                    >
                      <em class="plus fa fa-plus"></em>
                      <em class="minus fa fa-minus"></em>
                    </span>
                    <a class="text-success">
                      <strong>Process JSON</strong>
                    </a>
                    <span
                      class="pull-right"
                      style="padding-left: 15px; padding-right: 14px"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Click to refresh"
                      data-animation="false"
                    >
                      <em
                        class="plus fa fa-refresh fa-2x pointer"
                        style="font-size: 20px"
                        *ngIf="!loadingProcessJson"
                        (click)="refreshProcessJsonList()"
                      ></em>
                      <em
                        class="plus fa fa-refresh fa-spin fa-2x"
                        style="font-size: 20px"
                        *ngIf="loadingProcessJson"
                      ></em>
                    </span>
                  </div>
                </div>
                <div
                  id="collapseOne5"
                  class="card-collapse"
                  [ngClass]="{ collapse: completedProcessjsonListCollapsed }"
                  role="tabcard"
                  aria-labelledby="headingOne5"
                >
                  <div class="card-block">
                    <div
                      class="table-responsive private-network-table"
                      style="overflow-x: hidden"
                    >
                      <div
                        [hidden]="!processJSONLoading"
                        style="top: 17%; position: absolute; left: 50%"
                      >
                        <div class="text-center">
                          <em class="fa fa-spinner fa-pulse fa-2x fa-fw"> </em
                          ><span class="sr-only">Loading...</span>
                        </div>
                      </div>
                      <div
                        style="padding: 25px"
                        id="DataTables_Table_0_wrapper"
                        class="dataTables_wrapper no-footer"
                      >
                        <div
                          id="DataTables_Table_0_filter"
                          class="dataTables_filter"
                        ></div>
                        <table
                          id="processJsonList"
                          class="table dataTable no-footer dmTable"
                          cellspacing="0"
                          role="grid"
                          aria-describedby="DataTables_Table_0_info"
                          width="100%"
                        >
                          <thead>
                            <tr>
                              <th>Store</th>
                              <th>Last Run AT</th>
                              <th>Status</th>
                              <th></th>
                            </tr>
                          </thead>
                          <tbody></tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card">
                <div
                  class="card-header"
                  [ngClass]="{ collapsed: completedListCollapsed }"
                  role="tab"
                  id="headingOne6"
                  data-toggle="collapse"
                  data-parent="#accordion"
                >
                  <div
                    class="card-title cat__core__step cat__core__step--squared cat__core__step--success"
                  >
                    <span
                      class="accordion-indicator pull-right"
                      (click)="completedJobsToggle()"
                    >
                      <em class="plus fa fa-plus"></em>
                      <em class="minus fa fa-minus"></em>
                    </span>
                    <a class="text-success">
                      <strong>Completed Extractions</strong>
                    </a>
                    <span
                      class="pull-right"
                      style="padding-left: 15px; padding-right: 14px"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Click to refresh"
                      data-animation="false"
                    >
                      <em
                        class="plus fa fa-refresh fa-2x pointer"
                        style="font-size: 20px"
                        *ngIf="!loadingScheduleCompleted"
                        (click)="refreshScheduleListCompleted()"
                      ></em>
                      <em
                        class="plus fa fa-refresh fa-spin fa-2x"
                        style="font-size: 20px"
                        *ngIf="loadingScheduleCompleted"
                      ></em>
                    </span>
                  </div>
                </div>
                <div
                  id="collapseOne6"
                  class="card-collapse"
                  [ngClass]="{ collapse: completedListCollapsed }"
                  role="tabcard"
                  aria-labelledby="headingOne6"
                >
                  <div class="card-block">
                    <div
                      class="table-responsive private-network-table"
                      style="overflow-x: hidden"
                    >
                      <div
                        [hidden]="!scheduleProcessQueueCompletedLoading"
                        style="top: 24%; position: absolute; left: 50%"
                      >
                        <div class="text-center">
                          <em class="fa fa-spinner fa-pulse fa-2x fa-fw"> </em
                          ><span class="sr-only">Loading...</span>
                        </div>
                      </div>
                      <div
                        style="padding: 25px"
                        id="DataTables_Table_0_wrapper"
                        class="dataTables_wrapper no-footer"
                      >
                        <div
                          id="DataTables_Table_0_filter"
                          class="dataTables_filter"
                        ></div>
                        <table
                          id="scheduleProcessCompleted"
                          class="table dataTable no-footer dmTable"
                          cellspacing="0"
                          role="grid"
                          aria-describedby="DataTables_Table_0_info"
                          width="100%"
                        >
                          <thead>
                            <tr>
                              <th>Store</th>
                              <th>Extraction Type</th>
                              <th>Data Extraction Range</th>
                              <th>Completed Date</th>
                              <th>Status</th>
                              <th></th>
                            </tr>
                          </thead>
                          <tbody></tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
<div
  class="modal fade"
  id="resolveProjectModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog" role="document" style="width: 700px !important">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="requestModalLabel">
          {{ tootlTipInfoTitle }}
        </h5>
        <button
          type="button"
          class="close"
          (click)="closeToolTipModal()"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <span [innerHtml]="tootlTipInfo"></span>
      </div>
    </div>
  </div>
</div>
<div
  class="modal fade"
  id="changePriorityModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width: 400px">
      <div class="modal-header">
        <h5 class="modal-title" id="requestModalLabel">Change Priority Order</h5>
        <button
          type="button"
          class="close"
          (click)="closeChangePriorityModal()"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
       
        <div class="col-md-12">
          <div class="row" style="background-color: #f2f4f8; border-radius: 5px; padding: 5px; margin-bottom: 10px;" >
            The highest value indicates the lowest priority. For example, Priority 1 is the highest priority.
          </div>
          <div class="row col-md-12">
            <label style="width: 100%;">Select Priority</label>
            <select name="priorityListSelect" style="width:150px" class="form-control" [(ngModel)]="changePriorityForProcessorJob">  
              <option *ngFor="let priority of priorityList" value="{{priority.id}}">{{priority.itemName}}</option>  
            </select>
          </div>
          <div class="row col-md-12 mt-3">
            <button class="btn btn-primary mr-2"  (click)="changePriorityOfProcessorJob()" style="cursor: pointer;"> Change Priority </button>
            <button type="button"  class="btn btn-secondary" (click)="closeChangePriorityModal()" >Cancel  </button>
          </div>
        </div>
       
      </div>
    </div>
  </div>
</div>
<div
  class="modal fade"
  id="showProcessorStatusModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog" role="document" style="max-width: 680px">
    <div class="modal-content" style="width: 800px">
      <div class="modal-header">
        <h3 class="modal-title" id="requestModalLabel">Processor Running Status</h3>
        <button
          type="button"
          class="close"
          (click)="closeProcessorStatusModal()"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="col-md-12">
          <div>
             <h5 style="color: rebeccapurple;">{{processRunningStatus}} on {{processorStatusUpdatedAt}}</h5>
          </div>
          <h5>Processing Steps</h5>
          <div
            class="row"
            *ngFor="let step of processorSteps; let i = index"
            [ngClass]="{'current-step': i+1 === currentStepIndex}"
            style="background-color: #f2f4f8; border-radius: 5px; padding: 5px; margin-bottom: 5px;"
          >
            {{ i + 1 }}. {{ step }}
          </div>
        </div>
      </div>
  </div>
</div>
</div>

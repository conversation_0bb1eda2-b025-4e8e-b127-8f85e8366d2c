import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ManageadamRoutingModule } from "./manageadam.routing.module";
import { ManageadamComponent } from "./manageadam.component";
import { SharedModule } from "../shared/shared.module";

@NgModule({
  imports: [CommonModule, ManageadamRoutingModule,ManageadamComponent, SharedModule],
})
export class AdamModule {}

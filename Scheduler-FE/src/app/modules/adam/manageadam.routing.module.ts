import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManageadamComponent } from "./manageadam.component";

const routes: Routes = [{ path: "", component: ManageadamComponent,
data: {
  title: "ManageAdam",
  breadcrumb: [{ label: "ManageAdam", url: "" }],
},
 }];

@NgModule({
  exports: [RouterModule],
  imports: [RouterModule.forChild(routes)],
})
export class ManageadamRoutingModule {}

import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManagecdkjobsComponent } from "./managecdkjobs.component";

const routes: Routes = [
  {
    path: "",
    component: ManagecdkjobsComponent,
    data: {
      title: "ManageCDKJobs",
      breadcrumb: [{ label: "ManageCDKJobs", url: "" }],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ManagecdkjobsRoutingModule {}

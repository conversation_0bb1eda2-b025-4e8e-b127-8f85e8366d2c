import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ementRef, Renderer2 } from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { ConstantService } from "../../structure/constants/constant.service";
import { Apollo } from "apollo-angular";
import gql from "graphql-tag";
import { Router } from "@angular/router";
import { CommonService } from "../../structure/services/common.service";
import * as moment from "moment-timezone";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { EventEmitterService } from "../../structure/services/event.emitter.services";
import { SchedulerConstantService } from "../../structure/constants/scheduler.constant.service";
import { environment } from "./../../../environments/environment";
import { SubscriptionConstantService } from "./../../structure/constants/subscription.constant.service";
import { HttpClient, HttpHeaders } from "@angular/common/http";

import { DmFormGroupService } from "src/app/structure/services/dm.formgroup.services";
import { IDropdownSettings } from "ng-multiselect-dropdown";

import { Subject, takeUntil } from "rxjs";
import { SharedModule } from "../shared/shared.module";
import * as dayjs from "dayjs"; 

let table1;
declare var $: any;
declare var jQuery: any;
declare var swal: any;
declare var NProgress: any;

/**
 * Mutation to create new schedule
 *
 */
const createNewSchedule = gql`
  mutation scheduleCDKExtractJob($jobSchedule: DateTime!, $jobData: JobData!) {
    scheduleCDKExtractJob(
      input: { jobSchedule: $jobSchedule, jobData: $jobData }
    ) {
      status
      message
      job {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
      }
    }
  }
`;

/**
 * Mutation to cancel schedule
 *
 */
const cancelCDKExtractJobByStore = gql`
  mutation cancelCDKExtractJobByStore(
    $jobSchedule: DateTime!
    $jobData: SingleStoreJobData!
  ) {
    cancelCDKExtractJobByStore(
      input: { jobSchedule: $jobSchedule, jobData: $jobData }
    ) {
      status
      message
      job {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
      }
    }
  }
`;

const runNowCDKExtractJobByStore = gql`
  mutation runNowCDKExtractJobByStore(
    $jobSchedule: DateTime!
    $jobData: SingleStoreJobData!
  ) {
    runNowCDKExtractJobByStore(
      input: { jobSchedule: $jobSchedule, jobData: $jobData }
    ) {
      status
      message
      job {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
      }
    }
  }
`;

const getAllCDKExtractJobs = gql`
  query getAllCDKExtractJobs {
    getAllCDKExtractJobs {
      timeFrameZone
      timeFrameStartTime
      timeFrameEndTime
      poolTime
      jobArray {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
        running
        scheduled
        queued
        completed
        failed
        repeating
        failReason
        data {
          groupName
          storeDataArray {
            dealerId
            projectId
            secondProjectId
            glAccountCompanyID
            mageManufacturer
            solve360Update
            buildProxies
            dualProxy
            includeMetaData
            extractAccountingData
            userName
            startDate
            endDate
            message
            startTime
            endTime
            closedROOption
            status
            jobType
            mageGroupCode
            mageStoreCode
            metaData
            projectIds
            secondProjectIdList
            testData
            stateCode
            companyIds
            companyObj
            parentName
            processFileName
            projectType
            secondaryProjectType
            brands
          }
        }
      }
    }
  }
`;

const getAllProcessXMLJobs = gql`
  query getAllProcessXMLJobs {
    getAllProcessXMLJobs {
      processXMLJobs {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
        running
        scheduled
        queued
        completed
        uploadStatus
        failed
        repeating
        failReason
        data {
          inputFile
          storeID
          outputFile
          status
          message
          operation
          createdAt
          coreReturnExceptionCount
          coreChargeExceptionCount
          coreReturnNotEqualCoreChargeExceptionCount
          coreChargeWithNoSaleCount
          invalidCoreCostSaleMismatchCount
          invalidCoreAmountMismatchCount
          partDetailsNullExceptionCount
          processorUniqueId
          processorRunningStatus
        }
      }
      processXMLJobsQueue {
        storeID
        fileToProcess
	priority
      }
    }
  }
`;

/**
 * Mutation to create new proxy with existing pgDump file
 *
 */
const createProxyWithSqlDump = gql`
  mutation createProxyWithSqlDump($proxyJobData: ProxyGenerationUsingPgDump!) {
    createProxyWithSqlDump(input: { proxyJobData: $proxyJobData }) {
      status
      message
    }
  }
`;

// @Component({
//   selector: "app-managecdkjobs",
//   templateUrl: "./managecdkjobs.component.html",
//   styleUrls: ["./managecdkjobs.component.css"],
// })
@Component({
  selector: "app-managecdkjobs",
  templateUrl: "./managecdkjobs.component.html",
  styleUrls: ["./managecdkjobs.component.css"],
  standalone: true,
  // providers: [
  //   ConstantService,
  //   CommonService,
  //   EventEmitterService,
  //   DmFormGroupService,
  // ], // <-- Provide the service here
  imports: [
    //NgIf,
    // FormsModule,
    // ReactiveFormsModule,
    // NgClass,
    // NgMultiSelectDropDownModule,
    // FieldErrorDisplayComponent,
    // NgFor,
    // CommonModule,
    SharedModule,

  ],
})
export class ManagecdkjobsComponent implements OnInit {
  public isAuthenticated = false;
  private subscription$ = new Subject();
  loading: any = false;
  private storeGroupList: any[] = [];
  private storeList: any[] = [];
  private storeFlag = false;
  private selectedGroup = null;
  private selectedStore = null;
  private storeGroupFlag = false;
  private storeLoading: any = false;
  private processQueueList: any[] = [];
  private processQueueListCompleted: any[] = [];
  private listener: any;
  private jobGroupList: any[] = [];
  private timeFrameZone: any;
  private processXmlListArray: any[] = [];
  private roOptionList: any;
  private onChangeRoOption: any[] = [];
  private compareObjArray: any[] = [];
  private compareObjArrayLatest: any[] = [];
  private compareObjArrayProcessXMLList: any[] = [];
  private processXMLJobsQueueLength = 0;
  private jobTypeStatus = true;
  private autoReloadCDKStatus = true;
  public storeGroupFilterList: any[] = [];
  public storeFilterList: any[] = [];
  public store: any[] = [];
  public storeGroup: any[] = [];
  public scheduleProcessQueueLoading = false;
  public scheduleProcessQueueCompletedLoading = false;
  public processXMLLoading = false;
  public createSchedule!: FormGroup;
  public loadingSchedule = false;
  public loadingScheduleCompleted = false;
  public loadingProcessXml = false;
  public loadAllStore = false;
  public completedListCollapsed = true;
  public processQueueListCollapsed = false;
  public completedProcessxmlListCollapsed = false;
  public timeFrameStartTime: any;
  public timeFrameEndTime: any;
  public timeFrameTooltipInfo: any;
  public tootlTipInfo = null;
  public tootlTipInfoTitle = null;
  public selectMultiDateRangeOption: any = true;
  public reloadGroup = false;
  public isPaused = true;
  public displayOnDemand = false;
  public showDualProxy = true;
  public extractAccountingData: any;
  public accountingProxyDecider = false;
  public dualProxyDecider = false;
  public checkbox!: boolean;
  public accountingProxy = false;
  public showDualProxy1 = false;
  public solve360ServerDecider!: boolean;
  public isMockServer = environment.envName == "prod" ? false : true;
  public selectedStores: any[] = [];
  public allStoreList: any[] = [];
  public allStoreFilterList: any[] = [];
  public multiStore: any[] = [];
  public priorityList: any[] = [];
  public numberInput: any;
  public fileNameForPriorityChange='';
  public changePriorityForProcessorJob: any;
  public currentPriority =''
  public isTestSchedule = false;
  public processRunningStatus ='';
  public processorStatusUpdatedAt ='';
  public accountingProxyOnChange(event: any) {
    this.extractAccountingData = this.createSchedule.get(
      "extractAccountingData"
    )?.value;
    if (this.extractAccountingData) {
      this.showDualProxy = true;
      this.accountingProxyDecider = true;
    } else {
      this.showDualProxy = false;
      this.accountingProxyDecider = false;
      this.createSchedule.patchValue({
        dualProxy: false,
      });
      this.checkbox = false;
    }
  }

  public changeAccountingProxyDecider() {
    // alert('Insde accounting proxy decider');
    if (this.accountingProxyDecider) {
      this.showDualProxy1 = true;
      this.accountingProxy = true;
    } else {
      this.showDualProxy1 = false;
      this.accountingProxy = false;
      this.dualProxyDecider = false;
    }
  }


  public changeSchedule(event:any){
    this.isTestSchedule = this.createSchedule.get('testSchedule')?.value;

 }
  
  public isTestServer(value:any){
    this.isTestSchedule = value
  }

  public changedualProxyDecider() {
    if (this.accountingProxyDecider && this.dualProxyDecider) {
      this.dualProxyDecider = true;
    } else {
      this.dualProxyDecider = false;
    }
  }

  public dateRangePickerOptions: any;
  public dateInput: any;

  public singleDropdownSettings: any;
  public dropdownSettings: any;
  public singleDropdownSettingsDisable: any;
  public singleDropdownSettingsState: any;
  public multiDropdownSettingsDisable: any;

  public agendaDashboardUrl = "";
  public shows360Link = false;
  public s360CompanyId: any;
  public poolTime: any;
  public modalDisplayFlag = false;
  public errorMessage = "";
  public items: any[] = [];
  public showSpinnerButton = false;
  public doProxyFilePath: any;
  public updatePaytypeFlag = false;
  public metaDataArray: any[] = [];
  public multiDropdownSettings: IDropdownSettings = {
    idField: "id",
    textField: "itemName",
    allowSearchFilter: true,
  };
  public resumeProcessorInput: any;
  public haltState!: string;

  public coreReturnExceptionCountMsg!: number;
  public coreChargeExceptionCountMsg!: number;
  public coreReturnNotEqualCoreChargeExceptionCountMsg!: number;
  public coreChargeWithNoSaleCountMsg!: number;

  public invalidCoreCostSaleMismatchCountMsg!: number;
  public invalidCoreAmountMismatchCountMsg!: number;
  public partDetailsNullExceptionCountMsg!: number;
  selectedRange: any; // Initialize selectedRange
  // public bsConfig: Partial<BsDaterangepickerConfig> = {};


  constructor(
    private http: HttpClient,
    private apollo: Apollo,
    public constantService: ConstantService,
    private elRef: ElementRef,
    private renderer: Renderer2,
    private toastrService: ToastrService,
    private commonService: CommonService,
    private EventEmitterService: EventEmitterService,
    private router: Router,
    private DmFormGroupService: DmFormGroupService,
    public SchedulerConstantService: SchedulerConstantService,
    public SubscriptionConstantService: SubscriptionConstantService,
  ) {}

  ngOnDestroy() {
    this.autoReloadCDKStatus = false;
    localStorage.removeItem("selectedStoreObj");
    localStorage.removeItem("selectedGroupObj");
    this.subscription$.next(void 0);
    this.subscription$.complete();
  }

  ngOnInit() {
    // this.bsConfig = {
    //   dateInputFormat: "MM-DD-YYYY",
    // };
    // console.log("bsConfig----------------", this.bsConfig);
    // let start: any = this.startDateSelection().toDate();
    // let end: any = this.startDateSelection().toDate();
    // this.selectedRange = [start, end];

    this.selectedRange = [
      this.startDateSelection().toDate(),
      this.endDateSelection().toDate(),
    ];
    console.log("initial start------------", this.selectedRange);
    this.dateInput = {
      start: this.selectedRange[0],
      end: this.selectedRange[1],
    };
    console.log("initial start------------dateInput", this.dateInput);
    // this.dateInput = {
    //   start: this.startDateSelection(),
    //   end: this.endDateSelection(),
    // };
   
    console.log("Componet", this.router.url);
    let activityData = {
      activityName: "Manage CDK3PA",
      activityType: "Manage CDK3PA",
      activityDescription: "Current Url: " + this.router.url,
    };
    this.commonService.saveActivity("Manage CDK3PA", activityData);
    this.SubscriptionConstantService.pageTitle = " - CDK3PA";
    console.log('SubscriptionConstantService',this.SubscriptionConstantService.pageTitle = " - CDK3PA")
    let scheduleUrl = environment.jobListUrl;
    scheduleUrl = scheduleUrl.replace(
      this.SchedulerConstantService.END_POINTS.GRAPHQL_END_POINT,
      this.SchedulerConstantService.END_POINTS.AGENDA_END_POINT
    );
    this.agendaDashboardUrl = scheduleUrl;
    this.dropdownSettings = this.DmFormGroupService.dropdownSettings();
    this.singleDropdownSettings =
      this.DmFormGroupService.singleDropdownSettings();
    this.singleDropdownSettingsDisable =
      this.DmFormGroupService.singleDropdownSettingsDisable();
    this.singleDropdownSettingsState =
      this.DmFormGroupService.singleDropdownSettingsState();
    this.multiDropdownSettingsDisable =
      this.DmFormGroupService.multiDropdownSettingsDisable();

      this.commonService.getGroups(() => {
        this.commonService.checkGroups((flag) => {
          if (!flag) {
            return;
          }
          this.isAuthenticated = true;
          this.init();
        });
     });
  }

  init() {
    this.priorityList = [
      {id:1,itemName:'1'},
      {id:2,itemName:'2'},
      {id:3,itemName:'3'},
      {id:4,itemName:'4'},
      {id:5,itemName:'5'},
      {id:6,itemName:'6'},
      {id:7,itemName:'7'}
    
    ]
    this.haltState = "";
    this.getRoOptionList();
    this.createSchedule = new FormGroup({
      storeGroup: new FormControl("", Validators.required),
      store: new FormControl("", Validators.required),
      roOption: new FormControl(
        this.SchedulerConstantService.DEFAULT_RO_OPTION
      ),
      updateRetreiveROinSolve360: new FormControl(true),
      buildProxies: new FormControl(true),
      includeMetaData: new FormControl(false),
      extractAccountingData: new FormControl(true),
      dualProxy: new FormControl(false),
      solve360ServerDecider: new FormControl(false),
      jobType: new FormControl(this.SchedulerConstantService.DEFAULT_JOB_TYPE),

      testSchedule: new FormControl(false),
      testDealerId: new FormControl(''),
      testGlId: new FormControl(''),
      testStoreCode: new FormControl(''),
      testGroupCode: new FormControl('')
      // store1:new FormControl('', Validators.required),
    });
    this.getAllJobs(() => {
      this.showScheduledProcessList(true);
      this.showScheduledProcessListCompleted(true);
    });
    this.getAllProcessXmlJobs(() => {
      this.showProcessXmlList();
    });
    this.loading = true;
    this.commonService.allS360Jobs("CDK3PA", "production", (result: any) => {
      this.loading = false;
      this.storeGroupList = result.storeGroupList;
      this.storeList = result.storeList;

      this.jobGroupList = result.jobGroupList;
      for (let i = 0; i < this.jobGroupList.length; i++) {
        if (this.jobGroupList[i].dmsCode == "CDK3PA") {
          this.allStoreList.push(this.jobGroupList[i]);
        }
      }
      this.getGroupFilterList();
      this.preSelectGroupAndStore();
      this.getAllStoreFilterList();
    });
    let objPropertyCalender: any = {};
    objPropertyCalender = this.commonService.getCalenderPropertyObject();
    objPropertyCalender.minDate = new Date();
    $(function () {
      $('[data-toggle="tooltip"]').tooltip({
        trigger: "hover",
        html: true,
      });
    });
    if (!this.listener) {
      this.listener = this.renderer.listen(
        this.elRef.nativeElement,
        "click",
        (evt:any) => {
          console.log(
            "evt.target___________________________________________",
            evt.target
          );
          if (evt.target.className === "fa fa-play-circle overrideSchedule") {
            console.log("evt.target.dataset$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",evt.target.dataset);
            console.log("evt.target.dataset.info$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",evt.target.dataset.info);
            this.runNowSchedule(evt.target.dataset.info);
          } else if (evt.target.className === "fa fa-ban cancelSchedule") {
            this.cancelSchedule(evt.target.dataset.info);
          } else if (
            evt.target.className.indexOf("statusMessageDisplay") != -1
          ) {
            let res = evt.target.dataset.info.split(",");
            let toolTipInfo = this.getProcessXmlToolTipData(res);
            this.showAlert(toolTipInfo, "Process XML Status");
          } else if (
            evt.target.className.indexOf("popUpInfoCompletedJobs") != -1
          ) {
            let res = evt.target.dataset.info.split(",");
            var toolTipInfo = this.getToolTipInfoCompletedJobs(res);
            this.showAlert(toolTipInfo, "Completed Extractions");
          } else if (
            evt.target.className ===
            "fa fa-caret-square-o-up text-success mt-2 fetchPayTypeDetails"
          ) {
            this.fetchPayTypeDetails(evt.target.dataset.info);
          } else if (
            evt.target.className ===
            "fa fa-caret-square-o-up text-success mt-2 haltAndResumeDetails"
          ) {
            // alert("Hiiiiiiiiiiiiiiiiii");
            this.haltAndResumeDetails(evt.target.dataset.info);
          } else if (evt.target.className === 'fa fa-edit text-primary mt-2 changePriority') {
          console.log('Change priority>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>',evt.target.dataset.info);
          this.changePriority(evt.target.dataset.info);
        }else if (evt.target.className === 'fa fa-trash text-danger mt-2 removeQueueItem') {
          this.removeQueueItem(evt.target.dataset.info);
        
        }else if (evt.target.className === 'fa fa-upload text-primary mt-2 requeue') {
          console.log("requeue event data%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%",evt.target.dataset.info);
          this.requeue(evt.target.dataset.info);
        
        }else if (evt.target.className === 'fa fa-info-circle text-dark mt-2 showProcessorStatus') {
           this.showProcessorStatus(evt.target.dataset.info);
         }
        

        
      }
      );
    }
    this.getNotificationForUi();
  }

  getNotificationForUi() {
    let self = this;
    if (this.autoReloadCDKStatus) {
      this.getAllJobsForUiUpdate((data: any) => {
        this.compareObjectValue();
        this.processXMLReloadList((result: any) => {
          this.compareObjectValueProcessXML();
          setTimeout(() => {
            self.getNotificationForUi();
          }, 3000);
        });
      });
    }
  }

  getProcessXmlToolTipData(rowData: any) {
    let toolTip = "";
    let className = "";
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.COMPLETED
      ? (className = "label-success")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.SCHEDULED
      ? (className = "label-scheduled")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.RUNNING
      ? (className = "label-running")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.REPEATING
      ? (className = "label-repeating")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.FAILED
      ? (className = "label-failed")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.QUEUED
      ? (className = "label-queued")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.LOCKED
      ? (className = "label-locked")
      : null;

    rowData[2] == "HALT" ? (className = "label-halt") : null;
    rowData[2] == "DEAD" || rowData[2] == "HELD"
      ? (className = "label-dead")
      : null;

    toolTip += rowData[8] + " \n";
    if (rowData[7] && rowData[2] === "Failed") {
      toolTip += "Failed Reason: " + rowData[7] + " \n";
    }
    /* Hide the Failed Reason from HALT tooltip
    if (rowData[2] == "HALT") {
      let msg1 = "",
        msg2 = "";
      toolTip += "Failed Reason:";
      if (rowData[12]) {
        if (rowData[12] > 0) {
          msg1 = " Core Return without Core Charge:" + rowData[12];
        }
      }

      toolTip += msg1 + " \n";
    } */
    if (rowData[2] == "DEAD" || rowData[2] == "HELD") {
      rowData[2] = "HELD";
      let msg1 = "",
        msg2 = "",
        msg3 = "",
        msg4 = "";
      toolTip += "Failed Reason:";
      if (rowData[13]) {
        if (rowData[13] > 0) {
          msg1 = " Core Charge without Core Return:" + rowData[13];
        }
      }

      if (rowData[15]) {
        if (rowData[15] > 0) {
          msg1 = " Core Charge with no Sale:" + rowData[15];
        }
      }

      if (rowData[16]) {
        if (rowData[16] > 0) {
          msg1 = " Invalid Core Cost-Sale mismatch:" + rowData[16];
        }
      }

      if (rowData[17]) {
        if (rowData[17] > 0) {
          msg1 = " Invalid Core Amount mismatch:" + rowData[17];
        }
      }

      toolTip += msg1 + msg2 + msg3 + msg4 + " \n";
    }

    let status;
    if (rowData[20] === "false") {
      status = '<span class="label label-success">Completed</span><span style="padding-left: 1rem" class="label label-upload-halt">'+this.SchedulerConstantService.STATUS_FLAG.UPLOADHALT+'</span>';
    } else {
      status = `<span class="label ${className}">${rowData[2]}</span>`;
    }
      toolTip += `Status: ${status}\n`;
    
    return toolTip;
  } 

  showAlert(toolTipText: any, title: any) {
    this.tootlTipInfo = toolTipText.split("\n").join("<br>");
    this.tootlTipInfoTitle = title;
    $("#resolveProjectModal").modal({ show: true, backdrop: "static" });
  }

  closeToolTipModal() {
    $("#resolveProjectModal").modal("hide");
  }

  /**
   * callback function for store group deselection
   *
   */
  // OnDeSelectStoreGroup(item: any) {
  //   if (!this.containsObject(item, this.storeGroup)) {
  //     this.storeGroup.push(item);
  //   }
  //   this.getGroupFilterList();
  // }

  OnDeSelectStoreGroup(item: any) {
    // if (!this.containsObject(item, this.storeGroup)) {
    //   this.storeGroup.push(item);
    // }
    // this.getGroupFilterList();
    this.store = [];
    this.storeFilterList = [];
    this.createSchedule.reset();
    this.createSchedule.patchValue({
      updateRetreiveROinSolve360: false,
      buildProxies: true,
    });
    // this.storeFilterList = [];
    this.selectMultiDateRangeOption = true;
    this.setDateRange(true, "");
    // this.dateInput = {
    //   start: this.startDateSelection(),
    //   end: this.endDateSelection(),
    // };
    this.dateInput = {
      start: this.selectedRange[0],
      end: this.selectedRange[1],
    };
    this.createSchedule.patchValue({
      roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
      jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE,
    });
    this.getGroupFilterList();
  }

  OnDeSelectStore(item: any) {
    if (!this.containsObject(item, this.store)) {
      this.store.push(item);
    }
    this.getStoreFilterList();
  }

  /**
   * getGroupFilterList function will collect the group list for filtering purpose
   *
   */
  getGroupFilterList() {
    this.storeGroupFilterList = [];
    for (let i = 0; i < this.storeGroupList.length; i++) {
      const companyName = this.storeGroupList[i].mageGroupName;
      const mageGroupCode = this.storeGroupList[i].mageGroupCode;
      const mageGroupName = this.storeGroupList[i].mageGroupName
        ? this.storeGroupList[i].mageGroupName
        : "";
      const mageStoreCode = this.storeGroupList[i].mageStoreCode;
      const mageStoreName = this.storeGroupList[i].mageStoreName;
      const projectId = this.storeGroupList[i].projectId;
      const secondProjectId = this.storeGroupList[i].secondaryProjectId;
      const projectType = this.storeGroupList[i].projectType;
      const secondaryProjectType = this.storeGroupList[i].secondaryProjectType;
      const companyId = this.storeGroupList[i].companyId;
      const parentName = this.storeGroupList[i].companyName;
      if (companyName) {
        const obj = {
          id: companyName,
          itemName: companyName,
          mageGroupCode: mageGroupCode,
          mageGroupName: mageGroupName,
          mageStoreCode: mageStoreCode,
          mageStoreName: mageStoreName,
          projectId: projectId,
          companyId: companyId,
          parentName: parentName,
          secondProjectId: secondProjectId,
          projectType:projectType,
          secondaryProjectType:secondaryProjectType
        };
        if (!this.containsObject(obj, this.storeGroupFilterList)) {
          this.storeGroupFilterList.push(obj);
        }
      }
    }
    this.storeGroupFilterList = this.sortListAsc(this.storeGroupFilterList);
  }
  containsObject(obj: any, list: any) {
    let i;
    for (i = 0; i < list.length; i++) {
      if (list[i].id === obj.id) {
        return true;
      }
    }
    return false;
  }

  /**
   * sortListAsc function will sort the list in ascending order.
   *
   */
  sortListAsc(temp: any) {
    temp = temp
      .filter((item: any, index: number) => index < temp.length)
      .sort((a: any, b: any): any => {
        const x = a["itemName"]
          ? a["itemName"].toLowerCase().replace(/^[^a-z0-9]*/g, "")
          : "";
        const y = b["itemName"]
          ? b["itemName"].toLowerCase().replace(/^[^a-z0-9]*/g, "")
          : "";
        return x < y ? -1 : x > y ? 1 : 0;
      });
    return temp;
  }
  /**
   * callback function for store group selection
   *
   */
  onSelectStoreGroup(item: any) {
    this.selectStoreGroup(item, () => {
      this.shows360Link = true;
      // this.s360CompanyId = item.companyId;
      let itemFilter = this.storeGroupFilterList.find(res => res.id == item.id);
      console.log('s360CompanyId',itemFilter, "storeGroupList" ,this.storeGroupList)
      this.s360CompanyId = itemFilter.companyId;
    });
  }

  selectStoreGroup(item: any, callback: any) {
    this.loadAllStore = true;
    this.storeList = [];
    this.storeFilterList = [];
    this.store = [];
    this.storeFlag = false;
    if (!this.containsObject(item, this.storeGroup)) {
      this.storeGroup.push(item);
    }
    let selectedGroupFilter: any = this.storeGroupList.filter(function (res) {
      return res.sgId == item.id;
    });
    this.selectedGroup = selectedGroupFilter[0];
    //this.getGroupFilterList();
    let itemFilter: any = this.storeGroupFilterList.filter(function (res) {
      return res.id == item.id; //updated as per new array list
    });
    console.log("this.storeGroupFilterList", itemFilter);
    this.getStoreList("CDK3PA", itemFilter[0], () => {
      this.loadAllStore = false;
      this.getStoreFilterList();
      this.storeGroupFlag = true;
      if (callback) {
        callback();
      }
    });
  }

  /**
   * getStoreList function fetch Stores list for the selected StoreGroup
   *
   */
  getStoreList(type: any, item: any, callback: any) {
    // const groupCode = item.mageGroupCode;
    const groupCode = item.mageGroupName;
    this.loading = true;
    this.storeLoading = true;
    this.storeList = [];
    this.storeList = this.jobGroupList
      .filter((item: any, index: number) => index < this.jobGroupList.length)
      .filter(
        (item: any, index: number) =>
          item.mageGroupName != null &&
          item.mageGroupName === groupCode &&
          item.thirdPartyUsername !== null
      )
      .filter(
        (item: any, index: number) =>
          item.dmsCode.toLowerCase() == type.toLowerCase()
      );

    this.loading = false;
    this.storeLoading = false;
    if (callback) {
      callback();
    }
  }

  /**
   * getStoreFilterList function will collect the store list for filtering purpose
   *
   */
  getStoreFilterList() {
    this.storeFilterList = [];
    for (let i = 0; i < this.storeList.length; i++) {
      const companyID = this.storeList[i].companyId;
      const storeName = this.storeList[i].companyName;
      const stId = this.storeList[i].companyName;
      const mageGroupCode = this.storeList[i].mageGroupName;
      const groupCode = this.storeList[i].mageGroupCode;
      const mageStoreName = this.storeList[i].mageStoreName;
      const mageStoreCode = this.storeList[i].mageStoreCode;
      const thirdPartyUsername = this.storeList[i].thirdPartyUsername;
      const glAccountCompanyID = this.storeList[i].dealerbuiltSourceId;
      const stateCode = this.storeList[i].state;
      const projectId = this.storeList[i].projectId;
      const secondProjectId = this.storeList[i].secondaryProjectId;
      const solve360Update = this.storeList[i].solve360Update;
      const buildProxies = this.storeList[i].buildProxies;
      const includeMetaData = this.storeList[i].includeMetaData;
      const extractAccountingData = this.storeList[i].extractAccountingData;
      const dualProxy = this.storeList[i].dualProxy;
      const mageManufacturer = this.storeList[i].mageManufacturer;
      const secondaryProjectType = this.storeList[i].secondaryProjectType;
      const projectType = this.storeList[i].projectType;
      const projectName = this.storeList[i].projectName;
      const secondaryProjectName = this.storeList[i].secondaryProjectName;
      const brands = this.storeList[i].mageManufacturer;

      if (stId) {
        const obj = {
          id: stId,
          itemName: `${storeName} [${thirdPartyUsername}]`,
          mageGroupCode: mageGroupCode,
          mageStoreName: mageStoreName,
          mageStoreCode: mageStoreCode,
          thirdPartyUsername: thirdPartyUsername,
          stateCode: stateCode,
          projectId: projectId,
          solve360Update: solve360Update,
          buildProxies: buildProxies,
          includeMetaData: includeMetaData,
          companyID: companyID,
          glAccountCompanyID: glAccountCompanyID,
          extractAccountingData: extractAccountingData,
          mageManufacturer: mageManufacturer,
          dualProxy: dualProxy,
          secondProjectId: secondProjectId,
          projectType:projectType,
          secondaryProjectType:secondaryProjectType,
          projectName:projectName,
          secondaryProjectName:secondaryProjectName,
          groupCode:groupCode,          
          errors:this.storeList[i].errors,
          assignedtoCn:this.storeList[i].assignedtoCn,
          companyName:this.storeList[i].companyName,
          brands: brands,

        };
        if (!this.containsObject(obj, this.storeFilterList)) {
          this.storeFilterList.push(obj);
        }
      }
    }
    this.storeFilterList = this.sortListAsc(this.storeFilterList);
    let activityDataSchedule = {
      activityName: "CDK3PA : Store filter list",
      activityType: "Store filter list",
      activityDescription: `CDK3PA Store filter list: ${JSON.stringify(
        this.storeFilterList
      )}`,
    };
    this.commonService.saveActivity("Manage CDK3PA", activityDataSchedule);
  }

  getAllStoreFilterList() {
    this.allStoreFilterList = [];
    console.log("allStoreList=", this.allStoreList);
    for (let i = 0; i < this.allStoreList.length; i++) {
      const companyID = this.allStoreList[i].companyId;
      const storeName = this.allStoreList[i].companyName;
      const stId = this.allStoreList[i].companyName;
      const mageGroupCode = this.allStoreList[i].mageGroupName;
      const mageStoreName = this.allStoreList[i].mageStoreName;
      const mageStoreCode = this.allStoreList[i].mageStoreCode;
      const thirdPartyUsername = this.allStoreList[i].thirdPartyUsername;
      const glAccountCompanyID = this.allStoreList[i].dealerbuiltSourceId;
      const stateCode = this.allStoreList[i].state;
      const projectId = this.allStoreList[i].projectId;
      const projectType = this.allStoreList[i].projectType;
      const secondaryProjectType = this.allStoreList[i].secondaryProjectType;
      const projectName = this.allStoreList[i].projectName;
      const secondaryProjectName = this.allStoreList[i].secondaryProjectName;
      const secondProjectId = this.allStoreList[i].secondaryProjectId;
      const solve360Update = this.allStoreList[i].solve360Update;
      const buildProxies = this.allStoreList[i].buildProxies;
      const includeMetaData = this.allStoreList[i].includeMetaData;
      const extractAccountingData = this.allStoreList[i].extractAccountingData;
      const dualProxy = this.allStoreList[i].dualProxy;
      const mageManufacturer = this.allStoreList[i].mageManufacturer;
      if (stId) {
        const obj = {
          id: stId,
          itemName: storeName,
          mageGroupCode: mageGroupCode,
          mageStoreName: mageStoreName,
          mageStoreCode: mageStoreCode,
          thirdPartyUsername: thirdPartyUsername,
          stateCode: stateCode,
          projectId: projectId,
          solve360Update: solve360Update,
          buildProxies: buildProxies,
          includeMetaData: includeMetaData,
          companyID: companyID,
          glAccountCompanyID: glAccountCompanyID,
          extractAccountingData: extractAccountingData,
          mageManufacturer: mageManufacturer,
          dualProxy: dualProxy,
          secondProjectId: secondProjectId,
          projectType,
          secondaryProjectType,
          projectName,
          secondaryProjectName,
          groupCode: this.allStoreList[i].mageGroupCode,
        };
        if (!this.containsObject(obj, this.allStoreFilterList)) {
          this.allStoreFilterList.push(obj);
        }
      }
    }
    this.allStoreFilterList = this.sortListAsc(this.allStoreFilterList);
    let activityDataSchedule = {
      activityName: "CDK3PA :All Store filter list",
      activityType: "All Store filter list",
      activityDescription: `CDK3PA All Store filter list: ${JSON.stringify(
        this.allStoreFilterList
      )}`,
    };
    this.commonService.saveActivity("Manage CDK3PA", activityDataSchedule);
  }

  /**
   * callback function for store selection
   *
   */
  onSelectStore(item: any) {
    // this.store = [];
    this.getStoreFilterList();
    if (!this.containsObject(item, this.store)) {
      this.store.push(item);
    }
    const selectedStoreFilter: any = this.storeList.filter(function (res) {
      return res.stId == item.id;
    });
    this.selectedStore = selectedStoreFilter[0];
    this.storeFlag = true;
  }

  onItemSelect(item: any) {
    if (!this.containsObject(item, this.multiStore)) {
      this.multiStore.push(item);
    }
    console.log("Selected Store List", this.multiStore);
  }

  onDeselectStore(item: any) {
    console.log("deselect");
    console.log("multistore", this.multiStore);
  }

  /**
   *  Select the date from datepicker in the Filter
   *
   */
  public selectedDate(value: any, dateInput: any) {
    dateInput.start = value.start;
    dateInput.end = value.end;
  }

  saveSchedule() {
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    console.log("current User Obj?????????????????????????????????????????????????????????????/",currentUserObj);
    if (currentUserObj) {
      var userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
    }
    let storeFilterListTemp = this.storeFilterList;
    //   const dealerCode = this.createSchedule.get("store")?.value[0]
    //   .thirdPartyUsername
    //   ? this.createSchedule.get("store")?.value[0].thirdPartyUsername
    //   : "3PAARMLIVE1";
    // const parentName = this.createSchedule.get("store")?.value[0].itemName;
    // const mageGrpCode =
    //   this.createSchedule.get("store")?.value[0].mageGroupCode;
    // const mageStoreID =
    //   this.createSchedule.get("store")?.value[0].mageStoreCode;
    // const mageStrCode =
    //   this.createSchedule.get("store")?.value[0].mageStoreCode;
    // const steCode = this.createSchedule.get("store")?.value[0].stateCode;

    let storeTemp1: any = storeFilterListTemp.filter(
      (x) => x.id == this.createSchedule.get("store")?.value[0].id
    );

    const dealerCode = storeTemp1[0].thirdPartyUsername
      ? storeTemp1[0].thirdPartyUsername
      : "3PAARMLIVE1";
    const parentName = storeTemp1[0].itemName;
    const mageGrpCode = storeTemp1[0].mageGroupCode;
    const mageStoreID = storeTemp1[0].mageStoreCode;
    const mageStrCode = storeTemp1[0].mageStoreCode;
    const mageStoreName = storeTemp1[0].mageStoreName;
    const groupCode = storeTemp1[0].groupCode;
    const steCode = storeTemp1[0].stateCode;
    const errors= storeTemp1[0].errors || "";
    const assignedtoCn= storeTemp1[0].assignedtoCn || "";
    const thirdPartyUsername = storeTemp1[0].thirdPartyUsername || "thirdPartyUsername";
    // const storeTemp = this.createSchedule.get('store')?.value[0].store;
    // let storeTempOld = this.createSchedule.get("store")?.value; //updated as per list contain id and item name
    // console.log(
    //   "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>storeTempOld",
    //   storeTempOld
    // );
    let storeTemp: any = storeFilterListTemp.filter((c: any) =>
      this.createSchedule.get("store")?.value.some((s: any) => s.id === c.id)
    );

    console.log(
      "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>storeTemp",
      storeTemp
    );

    console.log(
      "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>storeFilterList",
      storeTemp,
      this.storeFilterList
    );
    let tempProject = storeTemp.map((store: any) => store.projectId);
    let tempSecond = storeTemp.map((store: any) => store.secondProjectId);
    let companyIdList = storeTemp.map((store: any) => store.companyID);
    let brandList = storeTemp.map((store: any) => store.mageManufacturer); 

    let companyObj = storeTemp.map((store: any) => ({
           companyId: store.companyID,
           companyName: store.companyName,
           projectId: store.projectId,
           secondProjectId: store.secondProjectId,
           projectType: store.projectType,
           secondaryProjectType: store.secondaryProjectType,
           projectName: store.projectName,
           secondaryProjectName: store.secondaryProjectName           
         }));
     console.log("companyObj?????????????????????????????????????????????????????????",companyObj);
     console.log("brandList$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",brandList);

    console.log(
      "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>companyIdList",
      tempProject,
      tempSecond,
      companyIdList
    );
    let projectIds = "";
    let secondProjectIdList = "";
    for (let i = 0; i < tempProject.length; i++) {
      projectIds += tempProject[i] + "*";
    }
    console.log("Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>3", projectIds);
    for (let i = 0; i < tempSecond.length; i++) {
      if (tempSecond[i] != undefined) {
        secondProjectIdList += tempSecond[i] + "*";
      }
    }
    let dealerIdList = storeTemp.map((store: any) => store.thirdPartyUsername);

    let allDealerIdSame = this.areAllDealerIdSame(dealerIdList);
    console.log(
      "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>allDealerIdSame",
      allDealerIdSame
    );
    // const glAccountCompanyID =
    //   this.createSchedule.get("store")?.value[0].glAccountCompanyID;

    // console.log("glAccountCompanyID:", glAccountCompanyID);

    // const mageManufacturer =
    //   this.createSchedule.get("store")?.value[0].mageManufacturer;

    //console.log(mageManufacturer);

    console.log(
      "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>allDealerIdSame",
      storeTemp1
    );
    const glAccountCompanyID = storeTemp1[0].glAccountCompanyID;

    console.log(
      "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>glAccountCompanyID:",
      glAccountCompanyID
    );

    const mageManufacturer = storeTemp1[0].mageManufacturer;
    const projectType = storeTemp1[0].projectType;
    const secondaryProjectType = storeTemp1[0].secondaryProjectType;

    console.log(
      "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>mageManufacturer",
      mageManufacturer
    );

    //Get projectId from local storage
    // let projectId = this.createSchedule.get("store")?.value[0].projectId
    //   ? this.createSchedule.get("store")?.value[0].projectId
    //   : "";

    // Get projectId from local storage

    console.log("Store temp^^^^^^^^^^^^^^^^^^^^^^^^^^^^^",storeTemp);
    
    let projectId = storeTemp[0].projectId ? storeTemp[0].projectId : "";
    console.log(
      "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>projectId",
      projectId
    );

    //Get second projectId from local storage
    let secondProjectId = "";
    //  projectIds = ""
    // if (
    //   this.createSchedule
    //     .get("store")
    //     ?.value[0].hasOwnProperty("secondProjectId")
    // ) {
    //   secondProjectId =
    //     //this.createSchedule.get("store")?.value[0].secondProjectId;
    //     storeTemp[0].secondProjectId;
    // }
    // console.log(
    //   "Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>storeTemp[0].companyID",
    //   storeTemp,storeTemp1
    // );
    // const parentID = this.createSchedule.get("store")?.value[0].companyID;
    let parentID = storeTemp1[0].companyID ? storeTemp1[0].companyID : "";
    const companyId: number = parseInt(parentID);
    console.log(" companyId list after callback ", companyId);

    //  for(let i=0;i<companyIdList.length;i++){
    //    this.getSecondProjectId(parseInt(companyIdList[i], 10),(result)=>{
    //     console.log("result>>>>>>>>>>>>>>>>>>>>>>>>",result);
    //     if(result){
    //       if (result[1]) {

    //         if (result[1].hasOwnProperty('project_id')) {
    //           secondProjectIdList+=result[1].project_id.toString()+'*';
    //         }
    //       }
    //       if (result[0]) {
    //         if (result[0].hasOwnProperty('project_id')) {
    //           projectIds+=result[0].project_id.toString()+'*';
    //           console.log("Project ids!!!!!!!!!!!!!!!!!!!",projectIds);
    //         }
    //       }
    //     }
    //    })
    //  }
    console.log("Second project id list after callback", secondProjectIdList);
    this.getSecondProjectId(companyId, (result: any) => {
      // console.log('+++++++++++++++++++++++++++++++++++++++++++');
      // console.log(result);
      // console.log('+++++++++++++++++++++++++++++++++++++++++++');
      //Override projectId and secondProjectId from api
      // if(result){
      //   if (result[0]) {
      //     if (result[0].hasOwnProperty('project_id')) {
      //       projectId = result[0].project_id.toString();
      //     }
      //   }
      //   if (result[1]) {
      //     if (result[1].hasOwnProperty('project_id')) {
      //       secondProjectId = result[1].project_id.toString();
      //     }
      //   }
      // }
      const currentDate = new Date();
      let futureDateList =''
      let futureDateList1 = storeTemp.reduce((acc : any, item : any) => {
        let futureDate = item.primary_project_start;
        let secondary_project_start = new Date(item.secondary_project_start);
        let projectId = item.projectId;
        let secondProjectId = item.secondProjectId;
        const targetDatePrimary = new Date(futureDate);
      
        if (targetDatePrimary > currentDate) {
          console.log('greter');
          futureDateList+=projectId+'*';
          acc.push(projectId);
        } 
        if(secondary_project_start > currentDate){
          futureDateList+=secondProjectId+'*'
        }
      
        return acc;
      }, []);
      
      console.log("futuredate lsiusrt>>>>>>>>>>>>>>>>>>>>>>>.", futureDateList);
      
      let projectIds ='';
      let secondProjectIdList ='';
      let companyIds = '';
      let brands = "";
      for(let i=0;i<tempProject.length;i++){
        projectIds+=tempProject[i]+'*';
      }
      console.log('Project Ids>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>',projectIds);
      for(let i=0;i<tempSecond.length;i++){
        if(tempSecond[i]!=undefined){
          secondProjectIdList+=tempSecond[i]+'*';
        }
        
      }
      if(tempSecond.length>0 && tempSecond[0]!=undefined && tempSecond[0]!=''){
        secondProjectId =  tempSecond[0];    
      }
  
      for(let i=0;i<companyIdList.length;i++){
        if(companyIdList[i]!=undefined){
          companyIds+=companyIdList[i]+'*';
        }
        
      }

        
      for(let i=0;i<brandList.length;i++){
        if(brandList[i]!=undefined){
          brands+=brandList[i]+'*';
        }
        
      }

       
      console.log("Company Ids$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",companyIds);
      console.log("brands Ids$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",brands);

      let metaData = {
        parent_id: parentID,
        parent_name: parentName,
        store_id: mageStoreID,
        store_name: mageStrCode,
        mageStoreName: mageStoreName,
        groupCode:groupCode,
        errors:errors,
        assignedtoCn:assignedtoCn,
        thirdPartyUsername: thirdPartyUsername
      };
      const solve360Update =
        this.createSchedule.get("store")?.value[0].solve360Update;
      let s: string = moment(new Date(this.dateInput.start)).format(
        this.SchedulerConstantService.DATE_FORMAT
      );
      let e: string = moment(new Date(this.dateInput.end)).format(
        this.SchedulerConstantService.DATE_FORMAT
      );
      let dateRangeCompare = s + " - " + e;
      let isExist = this.checkJobExistInExtractionQueue(
        this.createSchedule.get("storeGroup")?.value[0].itemName.trim(),
        dealerCode,
        dateRangeCompare,
        mageStrCode

      );
      if (false) {
        swal({
          title: this.SchedulerConstantService.JOB_STARTED,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      } else {
        if (!allDealerIdSame) {
          this.toastrService.error (
            "Please select stores with same DealerID",       
          );
        }
        if (this.createSchedule.valid && allDealerIdSame) {
          let activityData = {
            activityName: "Manage CDK3PA",
            activityType: "Create CDK Jobs",
            activityDescription: `Create new Schedule for Group ${this.createSchedule
              .get("storeGroup")
              ?.value[0].itemName.trim()} (Group: ${mageGrpCode}, Store: ${mageStrCode})`,
          };
          this.commonService.saveActivity("Manage CDK3PA", activityData);
          let roOptionValue = this.createSchedule.get("roOption")?.value;
          let jobType = this.createSchedule.get("jobType")?.value;
          roOptionValue = roOptionValue
            ? roOptionValue
            : this.SchedulerConstantService.DEFAULT_RO_OPTION;
          jobType = jobType
            ? jobType
            : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
          let solve360Update;
          let buildProxies;
          let includeMetaData;
          let extractAccountingData;
          let dualProxy;
          solve360Update = this.createSchedule.get(
            "updateRetreiveROinSolve360"
          )?.value;
          buildProxies = this.createSchedule.get("buildProxies")?.value;
          if (!buildProxies) {
            solve360Update = false;
          }
          includeMetaData = this.createSchedule.get("includeMetaData")?.value;
          extractAccountingData = this.createSchedule.get(
            "extractAccountingData"
          )?.value;
          dualProxy = this.createSchedule.get("dualProxy")?.value;
          this.EventEmitterService.displayProgress.emit(
            this.SchedulerConstantService.EVENT_EMITTER.STEP_1
          );
          let scheduleDate = "";
          scheduleDate = moment().format(
            this.SchedulerConstantService.DATE_FORMAT
          );
          let startDate: string = moment(
            new Date(this.dateInput.start)
          ).format(this.SchedulerConstantService.DATE_FORMAT);
          let endDate: string = moment(new Date(this.dateInput.end)).format(
            this.SchedulerConstantService.DATE_FORMAT
          );
          let date = new Date();
          let dateArray: any = scheduleDate.split("-");
          date.setMonth(dateArray[0] * 1 - 1);
          date.setDate(dateArray[1] * 1);
          date.setFullYear(dateArray[2] * 1);
          let now_utc = Date.UTC(
            date.getUTCFullYear(),
            date.getUTCMonth(),
            date.getUTCDate(),
            date.getUTCHours(),
            date.getUTCMinutes(),
            date.getUTCSeconds()
          );
          const now_utcOP = new Date(now_utc);
          let now_utcOutPut = now_utcOP.toUTCString();

          if (
            glAccountCompanyID === undefined ||
            !glAccountCompanyID ||
            glAccountCompanyID == null
          ) {
            this.EventEmitterService.displayProgress.emit("");
            NProgress.done();
            const messageValidate =
              this.SchedulerConstantService.VALIDATION_MESSAGE_GL_COMPANY_ID;
            this.showStatusMessage(messageValidate, "failure");
            return false;
          }
          const scheduleObj: any = {
            jobSchedule: moment(now_utcOutPut).toISOString(),
            jobData: {
              groupName: this.createSchedule
                .get("storeGroup")
                ?.value[0].itemName.trim(),
              storeDataArray: {
                dealerId: dealerCode.trim(),
                projectId: projectId === undefined ? "" : projectId,
                secondProjectId:
                  secondProjectId === undefined ? "" : secondProjectId,
                  projectType: projectType === undefined ? "" : projectType,
                  secondaryProjectType:
                  secondaryProjectType === undefined ? "" : secondaryProjectType,
                glAccountCompanyID:
                  glAccountCompanyID === undefined ? "" : glAccountCompanyID,
                mageManufacturer: mageManufacturer,
                solve360Update: solve360Update,
                buildProxies: buildProxies,
                includeMetaData: includeMetaData,
                extractAccountingData: extractAccountingData,
                dualProxy: dualProxy,
                userName: userName,
                startDate: startDate,
                endDate: endDate,
                closedROOption: roOptionValue,
                jobType: jobType,
                mageGroupCode: mageGrpCode,
                mageStoreCode: mageStrCode,
                stateCode: steCode,
                metaData: JSON.stringify(metaData),
                projectIds: projectIds,
                secondProjectIdList: secondProjectIdList,
                testData:false,
                companyIds:companyIds,
                parentName:parentName,
                companyObj:JSON.stringify(companyObj),                
                errors:errors,
                thirdPartyUsername: thirdPartyUsername,
                assignedtoCn:assignedtoCn,
                brands:brands              
                //futureDate:futureDateList,
              //  parentName:parentName
              },
            },
          };
          console.log("bilbiiiiiiiiiiiiiiii",JSON.stringify(scheduleObj))
          let activityDataSchedule = {
            activityName: "BE call to save agenda objects",
            activityType: "Save CDK Jobs",
            activityDescription: `CDK3PA Schedule object is: ${JSON.stringify(
              scheduleObj
            )})`,
          };
          this.commonService.saveActivity(
            "Manage CDK3PA",
            activityDataSchedule
          );

          let self = this;
          this.apollo
            .use("manageSchedule")
            .mutate({
              mutation: createNewSchedule,
              variables: scheduleObj,
            })
            .pipe(takeUntil(this.subscription$))
            .subscribe({
              next: (listdata: any) => {
                NProgress.done();
                const result: any = listdata.data;
                const status = result.scheduleCDKExtractJob.status;
                let message = "";
                message = result.scheduleCDKExtractJob.message;
                if (status) {
                  this.solve360ServerDecider = false;
                  this.switchServer();
                  this.EventEmitterService.displayProgress.emit(
                    this.SchedulerConstantService.EVENT_EMITTER
                      .STEP_SAVE_SCHEDULE
                  );
                  this.refreshScheduleList();
                  setTimeout(function () {
                    self.EventEmitterService.displayProgress.emit("");
                    self.showStatusMessage(message, "success");
                  }, 3000);
                  this.createSchedule.reset();
                  this.createSchedule.patchValue({
                    updateRetreiveROinSolve360: true,
                    buildProxies: true,
                    includeMetaData: false,
                    extractAccountingData: true,
                    dualProxy: false,
                  });
                  this.showDualProxy = false;
                  this.storeFilterList = [];
                  // this.selectMultiDateRangeOption = !this.selectMultiDateRangeOption;
                  this.selectMultiDateRangeOption = true;
                  this.setDateRange(true, "");
                  // this.dateInput = {
                  //   start: this.startDateSelection(),
                  //   end: this.endDateSelection(),
                  // };
                  this.selectedRange = [
                    this.startDateSelection().toDate(),
                    this.endDateSelection().toDate(),
                  ];
                  this.dateInput = {
                    start: this.selectedRange[0],
                    end: this.selectedRange[1],
                  };
                  this.createSchedule.patchValue({
                    roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
                    jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE,
                  });
                } else {
                  self.EventEmitterService.displayProgress.emit("");
                }
              },
              error: (err:Error) => {
                this.EventEmitterService.displayProgress.emit("");
                NProgress.done();
                const message =
                  this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, "failure");
                this.commonService.errorCallback(err, this);
              },
              complete: () => {
                console.log("Completed");
              },
            });
        } else {
          this.validateAllFormFields(this.createSchedule);
        }
      }
      return;
    });
  }


  saveTestSchedule(){
    const testDealerId = this.createSchedule.get('testDealerId')?.value;
    const testGlId  = this.createSchedule.get('testGlId')?.value;
    const testStoreCode = this.createSchedule.get('testStoreCode')?.value;
    const testGroupCode = this.createSchedule.get('testGroupCode')?.value
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    const parentName = `${testGroupCode}_${testStoreCode}[${testDealerId}]`;
    if (currentUserObj) {
      var userName = currentUserObj.userName ? currentUserObj.userName : '';
    }
   const dealerCode = testDealerId;
    const glAccountCompanyID = testGlId
    let projectId = ""
    let secondProjectId = "";
    const parentID ='12345';
    const companyId: number = parseInt(parentID);
 
     this.getSecondProjectId(companyId, (result:any) => {
      let metaData = {};

      let s: string = moment(new Date(this.dateInput.start)).format(this.SchedulerConstantService.DATE_FORMAT);
      let e: string = moment(new Date(this.dateInput.end)).format(this.SchedulerConstantService.DATE_FORMAT);
      let dateRangeCompare = s + ' - ' + e
      let isExist = this.checkJobExistInExtractionQueue('Test', dealerCode, dateRangeCompare,testStoreCode);
      if (isExist) {
        swal({
          title: this.SchedulerConstantService.JOB_STARTED,
          type: 'warning',
          confirmButtonClass: 'btn-warning pointer',
          confirmButtonText: this.constantService.CLOSE
        });
      } else {
        if (true) {
          let roOptionValue = this.createSchedule.get('roOption')?.value;
          let jobType = this.createSchedule.get('jobType')?.value;
          roOptionValue = roOptionValue ? roOptionValue : this.SchedulerConstantService.DEFAULT_RO_OPTION;
          jobType = jobType ? jobType : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
          let solve360Update;
          let buildProxies;
          let includeMetaData;
          let extractAccountingData;          
          buildProxies = this.createSchedule.get("buildProxies")?.value;
          includeMetaData = 'Test';
          extractAccountingData = 'Test';
          // dualProxy = this.createSchedule.get('dualProxy').value;
          this.EventEmitterService.displayProgress.emit(this.SchedulerConstantService.EVENT_EMITTER.STEP_1);
          let scheduleDate = '';
          scheduleDate = moment().format(this.SchedulerConstantService.DATE_FORMAT);
          let startDate: string = moment(new Date(this.dateInput.start)).format(this.SchedulerConstantService.DATE_FORMAT);
          let endDate: string = moment(new Date(this.dateInput.end)).format(this.SchedulerConstantService.DATE_FORMAT);
          let date = new Date();
          let dateArray: any = scheduleDate.split("-");
          date.setMonth((dateArray[0] * 1) - 1);
          date.setDate(dateArray[1] * 1);
          date.setFullYear(dateArray[2] * 1)
          let now_utc = Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),
            date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());
          const now_utcOP = new Date(now_utc);
          let now_utcOutPut = now_utcOP.toUTCString();

          if(glAccountCompanyID === undefined || !glAccountCompanyID || glAccountCompanyID == null){
              this.EventEmitterService.displayProgress.emit('');
              NProgress.done();
              const messageValidate =  this.SchedulerConstantService.VALIDATION_MESSAGE_GL_COMPANY_ID;
              this.showStatusMessage(messageValidate, 'failure');
              return false;
          }

          if(testDealerId === undefined || !testDealerId || testDealerId == null){
            this.EventEmitterService.displayProgress.emit('');
            NProgress.done();
            const messageValidate = 'Please provide third party username for data pull ';
            this.showStatusMessage(messageValidate, 'failure');
            return false;
        }else{
              if (!this.hasNoSpecialCharacters(testDealerId)) {
                    console.log('The string contains special characters.');
                    this.EventEmitterService.displayProgress.emit('');
                       NProgress.done();
                     const messageValidate = 'Please provide third party username without Special Characters ';
                     this.showStatusMessage(messageValidate, 'failure');
                      return false;
               } 
        }

          if(testGroupCode === undefined || !testGroupCode || testGroupCode == null){
             this.EventEmitterService.displayProgress.emit('');
             NProgress.done();
             const messageValidate = 'Please provide Mage Group Code  for data pull ';
             this.showStatusMessage(messageValidate, 'failure');
            return false;
          }else{
             
            if (!this.hasNoSpecialCharacters(testGroupCode)) {
               console.log('Mage Group code  string contains special characters.');

              this.EventEmitterService.displayProgress.emit('');
              NProgress.done();
              const messageValidate = 'Please provide Mage Group Code without Special Characters ';
              this.showStatusMessage(messageValidate, 'failure');
              return false;
            } 
         }

      
      if(testStoreCode === undefined || !testStoreCode || testStoreCode == null){
        this.EventEmitterService.displayProgress.emit('');
        NProgress.done();
        const messageValidate = 'Please provide Mage Store Code  for data pull ';
        this.showStatusMessage(messageValidate, 'failure');
        return false;
    }else{
      if (!this.hasNoSpecialCharacters(testStoreCode)) {
      
      this.EventEmitterService.displayProgress.emit('');
      NProgress.done();
      const messageValidate = 'Please provide Mage Store Code  without special characters ';
      this.showStatusMessage(messageValidate, 'failure');
      return false;
    }
  }

          const scheduleObj: any = {
            jobSchedule: moment(now_utcOutPut).toISOString(),
            jobData: {
              groupName: testGroupCode ,
              storeDataArray:
              {
                dealerId: dealerCode.trim(),
                projectId:'0',
                secondProjectId:'',
                glAccountCompanyID: (glAccountCompanyID === undefined) ? "" : glAccountCompanyID,
                mageManufacturer: 'GM',
                buildProxies: buildProxies,
                includeMetaData: false,
                extractAccountingData: false,
                dualProxy:false,
                userName: '',
                startDate: startDate,
                endDate: endDate,
                closedROOption: roOptionValue,
                jobType: jobType,
                mageGroupCode: testGroupCode,
                mageStoreCode: testStoreCode,
                stateCode: 'NA',
                metaData: JSON.stringify(metaData),
                projectIds:'0',
                secondProjectIdList:'',
                testData:true,
                parentName:parentName
              }
            }
          };
          let activityDataSchedule = {
            activityName: "BE call to save agenda objects",
            activityType: "Save CDK Jobs",
            activityDescription: `CDK3PA Schedule object is: ${JSON.stringify(
              scheduleObj
            )})`,
          };
          this.commonService.saveActivity(
            "Manage CDK3PA",
            activityDataSchedule
          );

          let self = this;
          this.apollo
            .use("manageSchedule")
            .mutate({
              mutation: createNewSchedule,
              variables: scheduleObj,
            })
            .pipe(takeUntil(this.subscription$))
            .subscribe({
              next: (listdata:any) => {
                NProgress.done();
                const result: any = listdata.data;
                const status = result.scheduleCDKExtractJob.status;
                let message = "";
                message = result.scheduleCDKExtractJob.message;
                if (status) {
                  this.solve360ServerDecider = false;
                  this.switchServer();
                  this.EventEmitterService.displayProgress.emit(
                    this.SchedulerConstantService.EVENT_EMITTER
                      .STEP_SAVE_SCHEDULE
                  );
                  this.refreshScheduleList();
                  setTimeout(function () {
                    self.EventEmitterService.displayProgress.emit("");
                    self.showStatusMessage(message, "success");
                  }, 3000);
                  this.createSchedule.reset();
                  this.createSchedule.patchValue({
                    updateRetreiveROinSolve360: true,
                    buildProxies: true,
                    includeMetaData: false,
                    extractAccountingData: true,
                    dualProxy: false,
                  });
                  this.showDualProxy = false;
                  this.storeFilterList = [];
                  // this.selectMultiDateRangeOption = !this.selectMultiDateRangeOption;
                  this.selectMultiDateRangeOption = true;
                  this.isTestSchedule = false;
                  this.setDateRange(true, "");
                  // this.dateInput = {
                  //   start: this.startDateSelection(),
                  //   end: this.endDateSelection(),
                  // };
                  this.selectedRange = [
                    this.startDateSelection().toDate(),
                    this.endDateSelection().toDate(),
                  ];
                  this.dateInput = {
                    start: this.selectedRange[0],
                    end: this.selectedRange[1],
                  };
                  this.createSchedule.patchValue({
                    roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
                    jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE,
                  });
                } else {
                  self.EventEmitterService.displayProgress.emit("");
                }
              },
              error: (err:any) => {
                this.EventEmitterService.displayProgress.emit("");
                NProgress.done();
                const message =
                  this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, "failure");
                this.commonService.errorCallback(err, this);
              },
              complete: () => {
                console.log("Completed");
              },
            });
              
        } else {
          this.validateAllFormFields(this.createSchedule);
        }
      }
      return
    });


}

hasNoSpecialCharacters(inputString: string): boolean {
  const regex = /^[a-zA-Z0-9 _]*$/;  
  return regex.test(inputString);
}
  areAllDealerIdSame(dealerIdList: any) {
    const referenceValue = dealerIdList[0];
    const result = dealerIdList.every(
      (element: any) => element === referenceValue
    );
    return result;
  }
  getSecondProjectId(companyId: any, cb: any) {
    this.commonService.getSecondProjectId(companyId, (result: any) => {
      if (cb) {
        cb(result);
      }
    });
  }

  validateAllFormFields(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach((field) => {
      const control = formGroup.get(field);
      if (control instanceof FormControl) {
        control.markAsTouched({ onlySelf: true });
      } else if (control instanceof FormGroup) {
        this.validateAllFormFields(control);
      }
    });
  }

  isFieldValidCreateSchedule(field: string) {
    let retValue: any = null;
    retValue =
      !this.createSchedule.get(field)?.valid &&
      this.createSchedule.get(field)?.touched;
    return retValue;
  }

  displayFieldCssCreateSchedule(field: string) {
    return {
      "has-danger": this.isFieldValidCreateSchedule(field),
    };
  }

  /**
   * flash message style set for success and error
   *
   */
  showStatusMessage(message: any, statusType: any) {
    if (statusType === "success") {
      this.toastrService.success (message);
    } else {
      this.toastrService.error(message);
    }
  }

  timeFormat(timeString: any) {
    var hourEnd = timeString.indexOf(":");
    var H = +timeString.substr(0, hourEnd);
    var h = H % 12 || 12;
    var ampm = H < 12 ? " AM" : " PM";
    timeString = h + timeString.substr(hourEnd, 3) + ampm;
    return timeString;
  }

  getAllJobs(callback: any) {
    this.compareObjArray = [];
    this.processQueueList = [];
    this.processQueueListCompleted = [];
    const allStoreGroupsList = this.apollo
      .use("manageSchedule")
      .query({
        query: getAllCDKExtractJobs,
        fetchPolicy: "network-only",
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          console.log("processQueueList---------------------->",result)
          let obj: any = {};
          this.timeFrameZone =
            result["data"]["getAllCDKExtractJobs"]["timeFrameZone"];
          let startTime =
            result["data"]["getAllCDKExtractJobs"]["timeFrameStartTime"];
          let endTime =
            result["data"]["getAllCDKExtractJobs"]["timeFrameEndTime"];
          this.poolTime = result["data"]["getAllCDKExtractJobs"]["poolTime"];
          this.timeFrameStartTime = this.utcToLocalTime(startTime);
          this.timeFrameEndTime = this.utcToLocalTime(endTime);
          this.timeFrameTooltipInfo = this.utcTotimeFrameZoneDisplay(
            startTime,
            endTime,
            this.timeFrameZone
          );
          this.processQueueList = [];
          this.processQueueListCompleted = [];
          this.compareObjArray = [];
          $.each(
            result["data"]["getAllCDKExtractJobs"]["jobArray"],
            (key: any, val: any) => {
              console.log("processQueueList---------------------->",val);
              let groupName = val.data.groupName;
              let scheduledDate : any = val.nextRunAt
                ? val.nextRunAt
                : val.lastRunAt
                ? val.lastRunAt
                : null;
                console.log("processQueueList---------------------->scheduledDate",scheduledDate);
              let date : any = "";
                scheduledDate
                ? (date = moment
                    .unix(scheduledDate / 1000)
                    .format("MM-DD-YYYY HH:mm:ss"))
                : null;

                console.log("processQueueList---------------------->date",date);
              let nextRunAt = "";
              val.nextRunAt
                ? (nextRunAt = moment.unix(val.nextRunAt / 1000).fromNow())
                : null;
              let groupStatus: any = null;
              let mongoDbId = val._id;
              groupStatus = val;
              let failedReason = val.failReason;
              $.each(val.data["storeDataArray"], (key: any, val: any) => {
                obj = {};
                let jobStartDate = null;
                let jobEndDate = null;
                let status = null;
                let jobType = null;
                let solve360Update;
                let glAccountCompanyID;
                let buildProxies;
                let includeMetaData;
                let extractAccountingData;
                let dualProxy;
                let metaData;
                let projectId;
                let secondProjectId;
                let mageManufacturer;
                let seperator = "/";
                let projectIds;
                let secondProjectIdList;
                let companyIds;
                let testData;
                let companyObj;
                let parentName;
                let processFileName;
                let brands;
               

                console.log("mongoDbId#####################################################",mongoDbId);
                console.log("val##############################################################",val);
                console.log("Parent name##############################################################",parentName);

                if (val.startDate.includes("-")) {
                  seperator = "-";
                }
                status = val.status;
                const dateArray = val.startDate.split(seperator);
                const startDate =
                  dateArray[0] + "-" + dateArray[1] + "-" + dateArray[2];
                if (val.endDate.includes("-")) {
                  seperator = "-";
                }
                const dateArrayEnd = val.endDate.split(seperator);
                const ClosedROOption = val.closedROOption;
                const endtDate =
                  dateArrayEnd[0] +
                  "-" +
                  dateArrayEnd[1] +
                  "-" +
                  dateArrayEnd[2];
                let range = startDate + " - " + endtDate;
                jobStartDate = val.startTime;
                jobEndDate = val.endTime;
                jobType = val.jobType;
                processFileName = val.processFileName;
                solve360Update = val.solve360Update;
                glAccountCompanyID = val.glAccountCompanyID;
                buildProxies = val.buildProxies;
                projectId = val.projectId;
                secondProjectId = val.secondProjectId;
                projectIds = val.projectIds;
                secondProjectIdList = val.secondProjectIdList;
                companyIds = val.companyIds;
                companyObj = val.companyObj;
                testData = val.testData;
                includeMetaData = val.includeMetaData;
                extractAccountingData = val.extractAccountingData;
                dualProxy = val.dualProxy;
                mageManufacturer = val.mageManufacturer;
                parentName = val.parentName;
                brands=val.brands;
                this.metaDataArray.push({
                  thirdpartyUserName: val.dealerId,
                  metaData: val.metaData,
                });
                metaData = val.metaData;
                let jobStatus = false;
                let statusFlag = null;
                if (jobStartDate && jobEndDate && status) {
                  jobStatus = true;
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.COMPLETED;
                } else if (jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.RUNNING;
                } else if (!jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.SCHEDULED;
                } else if (jobStartDate && jobEndDate && !status) {
                  jobStatus = true;
                  statusFlag = this.SchedulerConstantService.STATUS_FLAG.FAILED;
                  failedReason = val.message;
                } else {
                  statusFlag = this.getJobStatus(groupStatus);
                }
                obj = {
                  groupName: groupName,
                  store: val.dealerId,
                  range: range,
                  date: date,
                  status: statusFlag,
                  override: this.SchedulerConstantService.RUN_NOW,
                  failedReason: failedReason,
                  nextRunAt: nextRunAt,
                  jobStartDate: jobStartDate,
                  jobEndDate: jobEndDate,
                  ClosedROOption: ClosedROOption,
                  jobType: jobType,
                  storeName: val.mageStoreCode,
                  solve360Update: solve360Update,
                  buildProxies: buildProxies,
                  includeMetaData: includeMetaData,
                  metaData: metaData,
                  projectId: projectId,
                  secondProjectId: secondProjectId,
                  glAccountCompanyID: glAccountCompanyID,
                  extractAccountingData: extractAccountingData,
                  dualProxy: dualProxy,
                  mageManufacturer: mageManufacturer,
                  projectIds: projectIds,
                  secondProjectIdList: secondProjectIdList,
                  companyIds:companyIds,
                  testData:testData,
                  mongoDbId:mongoDbId,
                  companyObj:companyObj,
                  parentName:parentName,
                  processFileName:processFileName,
                  brands:brands
                 
                };
                if (
                  jobStatus &&
                  (statusFlag ===
                    this.SchedulerConstantService.STATUS_FLAG.COMPLETED ||
                    statusFlag ==
                      this.SchedulerConstantService.STATUS_FLAG.FAILED)
                ) {
                  this.processQueueListCompleted.push(obj);
                  console.log("processQueueList---------------------->processQueueListCompleted",this.processQueueListCompleted);

                  this.compareObjArray.push(obj);
                } else {
                  this.processQueueList.push(obj);
                  console.log("processQueueList---------------------->processQueueListe",this.processQueueList);

                }
              });
            }
          );
          callback();
        },
        error: (err:Error) => {
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  getDealerIdFromStoreName(sName: any) {
    let storeFilterListCopy: any[] = [];
    storeFilterListCopy = Object.assign([], this.storeGroupList);
    storeFilterListCopy = storeFilterListCopy
      .filter((item: any, index: number) => index < storeFilterListCopy.length)
      .filter((item: any, index: number) => item.companyName === sName);
    let dealerId = "";
    if (storeFilterListCopy.length) {
      dealerId = storeFilterListCopy[0].thirdPartyUsername;
    }
    return dealerId;
  }

  getStoreNameFromDealerId(dealerID: any) {
    let storeFilterListCopy: any[] = [];
    storeFilterListCopy = Object.assign([], this.jobGroupList);
    storeFilterListCopy = storeFilterListCopy
      .filter((item: any, index: number) => index < storeFilterListCopy.length)
      .filter(
        (item: any, index: number) => item.thirdPartyUsername === dealerID
      );
    let dealerId = "";
    if (storeFilterListCopy.length) {
      dealerId = storeFilterListCopy[0].companyName;
    }
    return dealerId;
  }
  /**
   * showSouceBundleDetails function will show the Scheduled Process
   *
   */
  showScheduledProcessList(loaderStatus: any) {
    loaderStatus ? (this.scheduleProcessQueueLoading = true) : "";
    if ($("#scheduleProcessQueue").data('datatable')) {
      $("#scheduleProcessQueue").dataTable().fnDestroy();
    }  
    table1 = $("#scheduleProcessQueue").dataTable().fnClearTable();
    const elm = this;
    let i = 0;
    setTimeout(() => {
      $(document).ready(function () {
        if ($("#scheduleProcessQueue").length > 0) {
        table1 = $("#scheduleProcessQueue").dataTable({
          language: {
            decimal: ".",
            thousands: ",",
          },
          columnDefs: [
            { type: "numeric-comma", targets: "_all" },
            { orderable: false, targets: [5] },
            { orderable: true, targets: [0, 1, 2, 3] },
          ],
          fixedHeader: {
            header: true,
            footer: true,
            headerOffset: $(".cat__top-bar").outerHeight() - 11,
          },
          bSort: false,
          order: [0, "asc"],
          responsive: true,
          scrollX: false,
          scrollY: "200px",
          destroy: true,
          paging: true,
          deferRender: true,
          ordering: true,
          info: true,
          filter: true,
          length: true,
          processing: true,
          lengthMenu: [
            [50, 25, 10, 5],
            [50, 25, 10, 5],
          ],
          autoWidth: false,
          fnRowCallback: function (settings: any, aData: any) {
            const pagination = $(this)
              .closest(".dataTables_wrapper")
              .find(".dataTables_paginate");
            pagination.toggle(this.api().page.info().pages > 1);
          },
          drawCallback: function (settings: any) {
            table1 = $("#scheduleProcessQueue").DataTable();
            $("td:eq(1)", settings).css("width", "24%");
            $("td:eq(1)", settings).css("width", "10%");
            $("td:eq(2)", settings).css("width", "13%");
            $("td:eq(3)", settings).css("width", "23%");
            $("td:eq(4)", settings).css("width", "15%");
            $("td:eq(5)", settings).css("width", "15%");
            var api = this.api();
            var rows = api.rows({ page: "current" }).nodes();
            var last: any = null;
            api
              .column(0, { page: "current" })
              .data()
              .each(function (group: any, i: any) {
                if (last !== group) {
                  $(rows)
                    .eq(i)
                    .before(
                      '<tr class="group"><td colspan="8" style="BACKGROUND-COLOR:rgb(86, 85, 78);font-weight:700;color:#f5f5f5;">' +
                        "Store Group: " +
                        group +
                        "</td></tr>"
                    );
                  last = group;
                }
              });
          },
          columns: [
            {
              title: "Store",
              width: "24%",
              className: "dt-head-left",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                console.log("rowData for extraction queue!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!",rowData);
                let dealerId = elm.getDealerIdFromStoreName(rowData[1]);
                let storeName = elm.getStoreNameFromDealerId(rowData[1]);

                storeName = storeName ? storeName : rowData[24];
                if(rowData[4] == 'Running'){
                  storeName = rowData[31]?rowData[31]:rowData[0];
                }else{
                  storeName = rowData[31]?rowData[31]:rowData[0];
                }
             
                let toolTipForDealerId = "";
                let toolTipForJobType = "";
                if (dealerId) {
                  toolTipForDealerId = "DealerId: " + dealerId;
                }
                if (rowData[11]) {
                  toolTipForJobType = "Job Type: " + rowData[11];
                }
                let cursorStyle = toolTipForDealerId ? 'cursor:pointer;' : '';
                let tooltipAttributes = toolTipForDealerId 
                  ? `data-toggle="tooltip" data-placement="top" title="${toolTipForDealerId}" data-animation="false"`
                  : '';                
                data =
                  `<span style="${cursorStyle}" ${tooltipAttributes} data-info="${rowData}">
                    ${storeName}
                  </span>`;                
                return data !== null ? data : null;
              },
            },
            {
              title: "Extraction Type",
              width: "13%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                let toolTipForJobType = "";
                rowData[11] = rowData[11] ? rowData[11][0].toUpperCase() + rowData[11].slice(1) : "";
                if (rowData[11]) {
                  toolTipForJobType = rowData[11];
                }
                data =
                  '<span data-placement="top" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  toolTipForJobType +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Data Extraction Range",
              width: "13%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                data =
                  '<span data-placement="top" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  rowData[2] +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Schedule Date",
              width: "23%",
              type: "formatted-num",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                console.log("rowData>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",rowData);
                let toolTipTitle = "";
                if (rowData[4] == "Locked") {
                  toolTipTitle = elm.SchedulerConstantService.LOCKED_MESSAGE;
                }
                if (rowData[4] == "Running") {
                  toolTipTitle = rowData[4];
                }
                let b = rowData[3].split(" ");
                let r = b[0].split("-");
                let op = r[2] + "-" + r[0] + "-" + r[1] + " " + b[1];
                let scheduleDateDisplay = moment(op).format(
                  elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                );
                let scheduleDateOp = scheduleDateDisplay
                  ? scheduleDateDisplay
                  : rowData[3];
                data =
                  '<spandata-placement="top" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  scheduleDateOp +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Status",
              width: "15%",
              type: "formatted-alphabet",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                var className = "";
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.COMPLETED
                  ? (className = "label-success")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.SCHEDULED
                  ? (className = "label-scheduled")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.RUNNING
                  ? (className = "label-running")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.REPEATING
                  ? (className = "label-repeating")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.FAILED
                  ? (className = "label-failed")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.QUEUED
                  ? (className = "label-queued")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.LOCKED
                  ? (className = "label-locked")
                  : null;
                if (rowData[4] === "Rescheduled") {
                  data =
                    '<span style="float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="label label-scheduled">Scheduled</span>';
                  data +=
                    '<span aria-hidden="true" data-toggle="tooltip" data-placement="top" title="' +
                    rowData[6] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '" style="margin-left:5px;" class="label label-failed">Failed</span>';
                } else {
                  data =
                    '<span style="float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="label ' +
                    className +
                    '">' +
                    rowData[4] +
                    "</span>";
                }
                return data;
              },
            },
            {
              title: "Actions",
              width: "15%",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                const d = data;
                let rowData = [];
                rowData = Object.assign([], rows);
                const cancelSchedule = "Cancel Schedule";
                const overrideSchedule = "Run Now";
                if (
                  rowData[4] === "Completed" ||
                  rowData[4] === "Locked" ||
                 // rowData[4] === "Running" ||
                  rowData[4] === "Queued"
                ) {
                  data =
                    '<a style="font-size: 18px;" class="overrideScheduleCancel" style="color: #b9b5b5;cursor: not-allowed;" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-play-circle overrideScheduleCancel"  data-toggle="tooltip" data-placement="top" title="' +
                    rowData[4] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  style="color: #b9b5b5;cursor: not-allowed;"></i></a>';
                  data +=
                    '<a style="margin: 18px;font-size: 18px;" class="scheduleCancel" style="color: #b9b5b5;cursor: not-allowed;" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-ban scheduleCancel"  data-toggle="tooltip" data-placement="top" title="' +
                    rowData[4] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '" style="color: #b9b5b5;cursor: not-allowed;" ></i></a>';
                } else if(rowData[4] === "Running"){
                   data = `
                    <a style="margin: 18px; font-size: 18px;" class="cancelSchedule" href="javascript:void(0);" data-note="${d}">
                      </a>
                     `;                 
                 } else {
                  data =
                    '<a style="font-size: 18px;" class="overrideSchedule" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-play-circle overrideSchedule"  data-toggle="tooltip" data-placement="top" title="' +
                    overrideSchedule +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  ></i></a>';
                  data +=
                    '<a style="margin: 18px;font-size: 18px;" class="cancelSchedule" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-ban cancelSchedule"  data-toggle="tooltip" data-placement="top" title="' +
                    cancelSchedule +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  ></i></a>';
                }
                return data;
              },
            },
          ],
          rowGroup: {
            dataSrc: "Store Group",
          },
        });
        // tslint:disable-next-line:no-unused-expression
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
          "alphanumeric-sort-asc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "alphanumeric-sort-desc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? 1 : x > y ? -1 : 0;
          },
          "formatted-num-pre": function (a: any) {
            a = a === "-" || a === "" ? 0 : a.replace(/[^\d\-\.]/g, "");
            return parseFloat(a);
          },
          "formatted-num-asc": function (a: any, b: any) {
            return a - b;
          },
          "formatted-num-desc": function (a: any, b: any) {
            return b - a;
          },
          "formatted-alphabet-asc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "formatted-alphabet-desc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? 1 : x > y ? -1 : 0;
          },
        });
        elm.reDrawScheduleTable(elm.processQueueList, loaderStatus);
      }
      });
    }, 200);
  }

  /**
   * reDrawPortalStoreTable function will redraw the datatable using new table values
   *
   */
  reDrawScheduleTable(temp: any, loaderStatus: any) {
    console.log(
      "******************************************************Temp*******************************",
      temp
    );
    table1 = $("#scheduleProcessQueue").DataTable();
    table1.search("").draw();
    table1 = $("#scheduleProcessQueue").dataTable();
    table1.fnClearTable();
    table1 = $("#scheduleProcessQueue").dataTable();
    const tempArr = [];
    for (let i = 0; i < temp.length; i++) {
      let rpt = [];
      const t = temp[i];
      rpt = [
        t.groupName,
        t.store,
        t.range,
        t.date,
        t.status,
        t.override,
        t.failedReason,
        t.nextRunAt,
        t.ClosedROOption,
        t.jobStartDate,
        t.jobEndDate,
        t.jobType,
        t.projectId,
        t.solve360Update,
        t.buildProxies,
        t.includeMetaData,
        t.secondProjectId,
        t.glAccountCompanyID,
        t.extractAccountingData,
        t.dualProxy,
        t.mageManufacturer,
        t.projectIds,
        t.secondProjectIdList,
        t.testData,
        t.storeName,
        t.stateCode,
        t.mageGroupCode,
        t.companyIds,
        t.mongoDbId,
        t.companyObj,
        t.priority,
        t.parentName,
        t.processFileName,
        t.brands
        
      ];
      tempArr.push(rpt);
    }
    if (tempArr.length > 0) {
      table1.fnAddData(tempArr, false); // Add new data
    }
    table1.fnDraw(); // Redraw the DataTable
    loaderStatus ? (this.scheduleProcessQueueLoading = false) : "";
    if (temp.length > 0) {
      setTimeout(() => {
        $("#scheduleProcessQueue").DataTable().columns.adjust().draw(false);
      }, 100);
    }
  }

  getToolTipInfoCompletedJobs(rowData: any) {
    let toolTip = "";
    let className = "";
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.COMPLETED
      ? (className = "label-success")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.SCHEDULED
      ? (className = "label-scheduled")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.RUNNING
      ? (className = "label-running")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.REPEATING
      ? (className = "label-repeating")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.FAILED
      ? (className = "label-failed")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.QUEUED
      ? (className = "label-queued")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.LOCKED
      ? (className = "label-locked")
      : null;
    status = '<span class="label ' + className + '">' + rowData[4] + "</span>";
    if (rowData[3]) {
      let b = rowData[3].split(" ");
      let r = b[0].split("-");
      let op = r[2] + "-" + r[0] + "-" + r[1] + " " + b[1];
      const time = moment.duration(
        `00:${this.poolTime ? this.poolTime : "00"}:00`
      );
      let scheduleDateDisplay = moment(op)
        .subtract(time)
        .format(this.SchedulerConstantService.SCHEDULE_DATE_FORMAT);
      let scheduleDateOp = scheduleDateDisplay
        ? scheduleDateDisplay
        : rowData[3];
      toolTip += "Scheduled Date: " + scheduleDateOp + " \n";
    }
    toolTip += "Last Run At: " + rowData[3] + " \n";
    if (rowData[6]) {
      toolTip +=
        "Extraction Start Time: " +
        moment(rowData[6] * 1).format(
          this.SchedulerConstantService.SCHEDULE_DATE_FORMAT
        ) +
        " \n";
    }
    if (rowData[7]) {
      toolTip +=
        "Extraction End Time: " +
        moment(rowData[7] * 1).format(
          this.SchedulerConstantService.SCHEDULE_DATE_FORMAT
        ) +
        " \n";
    }
    toolTip += "Status: " + status + " \n";
    if (rowData[5] && rowData[4] === "Failed") {
      toolTip += "Failed Reason: " + rowData[5] + " \n";
    }
    return toolTip;
  }

  /**
   * showScheduledProcessListCompleted function will show the Scheduled Process
   *
   */
  showScheduledProcessListCompleted(loaderStatus: any) {
    loaderStatus ? (this.scheduleProcessQueueCompletedLoading = true) : "";
    if ($("#scheduleProcessCompleted").data('datatable')) {
      $("#scheduleProcessCompleted").dataTable().fnDestroy();
    }  
    table1 = $("#scheduleProcessCompleted").dataTable().fnClearTable();
    const elm = this;
    let i = 0;
    setTimeout(() => {
      $(document).ready(function () {
        table1 = $("#scheduleProcessCompleted").dataTable({
          language: {
            decimal: ".",
            thousands: ",",
          },
          columnDefs: [{ type: "numeric-comma", targets: "_all" }],
          fixedHeader: {
            header: true,
            footer: true,
            headerOffset: $(".cat__top-bar").outerHeight() - 11,
          },
          bSort: false,
          order: [4, "desc"],
          responsive: true,
          scrollX: false,
          destroy: true,
          paging: true,
          deferRender: true,
          ordering: true,
          info: true,
          filter: true,
          length: true,
          processing: true,
          lengthMenu: [
            [50, 25, 10, 5],
            [50, 25, 10, 5],
          ],
          autoWidth: false,
          scrollY: "200px",
          // initialization params as usual
          fnRowCallback: function (settings: any, aData: any) {
            const pagination = $(this)
              .closest(".dataTables_wrapper")
              .find(".dataTables_paginate");
            pagination.toggle(this.api().page.info().pages > 1);
          },
          drawCallback: function (settings: any) {
            table1 = $("#scheduleProcessQueue").DataTable();
            $("td:eq(1)", settings).css("width", "28%");
            $("td:eq(2)", settings).css("width", "17%");
            $("td:eq(3)", settings).css("width", "25%");
            $("td:eq(4)", settings).css("width", "20%");
            $("td:eq(5)", settings).css("width", "10%");
            var api1 = this.api();
            var rows1 = api1.rows({ page: "current" }).nodes();
            var last: any = null;

            api1
              .column(0, { page: "current" })
              .data()
              .each(function (group: any, i: any) {
                if (last !== group) {
                  $(rows1)
                    .eq(i)
                    .before(
                      '<tr class="group"><td colspan="8" style="BACKGROUND-COLOR:rgb(86, 85, 78);font-weight:700;color:#f5f5f5;">' +
                        "Store Group: " +
                        group +
                        "</td></tr>"
                    );
                  last = group;
                }
              });
          },
          columns: [
            {
              title: "Store",
              width: "28%",
              className: "dt-head-left",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                let storeName = elm.getStoreNameFromDealerId(rowData[1]);
                storeName = rowData[9] ? rowData[9] : rowData[1];
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[1] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  storeName +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Extraction Type",
              width: "17%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                let toolTipForJobType = "";
                rowData[8] = rowData[8] ? rowData[8].charAt(0).toUpperCase() + rowData[8].slice(1) : "";
                if (rowData[8]) {
                  toolTipForJobType = rowData[8];
                }
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[8] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  toolTipForJobType +
                  "</span>";
                return toolTipForJobType !== "" ? data : null;
              },
            },
            {
              title: "Data Extraction Range",
              width: "25%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[2] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  rowData[2] +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Completed Date",
              width: "20%",
              type: "formatted-date",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
            
                let completedDate = "";
                 let tempDate = new Date(rowData[3]);
                 rowData[6] = tempDate.getTime()

                 let tempDate1 = new Date(rowData[7]);
                 rowData[7] = tempDate1.getTime();
                 console.log("Completed date$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",rowData);
                if (rowData[3]) {
                  let time = rowData[3].split(" ");
                  let HourFormat = elm.convertTimeFormat(time[1]);
                  completedDate = time[0] + " " + HourFormat;
                }
                if (rowData[9]) {
                  completedDate = moment(rowData[7] * 1).format(
                    elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                  );
                }
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  completedDate +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  completedDate +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Status",
              width: "10%",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                var className = "";
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.COMPLETED
                  ? (className = "label-success")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.SCHEDULED
                  ? (className = "label-scheduled")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.RUNNING
                  ? (className = "label-running")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.REPEATING
                  ? (className = "label-repeating")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.FAILED
                  ? (className = "label-failed")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.QUEUED
                  ? (className = "label-queued")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.LOCKED
                  ? (className = "label-locked")
                  : null;
                let toolTip = "";
                if (rowData[3]) {
                  let b = rowData[3].split(" ");
                  let r = b[0].split("-");
                  let op = r[2] + "-" + r[0] + "-" + r[1] + " " + b[1];
                  const time = moment.duration(
                    `00:${elm.poolTime ? elm.poolTime : "00"}:00`
                  );
                  let scheduleDateDisplay = moment(op)
                    .subtract(time)
                    .format(elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT);
                  let scheduleDateOp = scheduleDateDisplay
                    ? scheduleDateDisplay
                    : rowData[3];
                  toolTip += "Scheduled Date: " + scheduleDateOp + " \n";
                }
                toolTip += "Last Run At: " + rowData[3] + " \n";
                if (rowData[6]) {
                  toolTip +=
                    "Extraction Start Time: " +
                    moment(rowData[6] * 1).format(
                      elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                    ) +
                    " \n";
                }
                if (rowData[7]) {
                  toolTip +=
                    "Extraction End Time: " +
                    moment(rowData[7] * 1).format(
                      elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                    ) +
                    " \n";
                }
                toolTip += "Status: " + rowData[4] + " \n";
                if (rowData[5] && rowData[4] === "Failed") {
                  toolTip +=
                    "Failed Reason: " + rowData[5]
                      ? rowData[5].replace(/[^a-zA-Z ]/g, " ")
                      : "" + " \n";
                }
                rowData[5] = rowData[5]
                  ? rowData[5].replace(/[^a-zA-Z ]/g, " ")
                  : "";
                if (
                  rowData[4] === "Failed" &&
                  rowData[5] === elm.SchedulerConstantService.EXCEED_TIME
                ) {
                  rowData[4] = "Completed";
                  className = "label-success";
                }
                data =
                  '<span style="cursor:pointer;float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="popUpInfoCompletedJobs label ' +
                  className +
                  '"  data-toggle="tooltip" data-placement="top" title="' +
                  toolTip +
                  '" data-info="' +
                  rowData +
                  '">' +
                  rowData[4] +
                  "</span>";
                if (rowData[5]) {
                  data =
                    '<span style="cursor:pointer;float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="popUpInfoCompletedJobs label ' +
                    className +
                    '" data-toggle="tooltip" data-placement="top" title="' +
                    toolTip +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '">' +
                    rowData[4] +
                    "</span>";
                }

                return data;
              },
            },
            {
              title: "",
              width: "5%",
              className: "dt-head-left pl-0",
              render: function (data: any, type: any, rows: any, meta: any) {
                const d = data;
                let rowData = [];
                rowData = Object.assign([], rows);
                const enableReRunFlag = rowData[11];
                console.log(
                  "?????????????????????????????????????????????????rowdata",
                  rowData
                );
                if (
                  rowData[2] ===
                    elm.SchedulerConstantService.STATUS_FLAG.COMPLETED &&
                  enableReRunFlag && rowData[20] !== false
                ) {
                  const updateParentGroup = "Re-Run";
                  data =
                    '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-caret-square-o-up text-success mt-2 fetchPayTypeDetails" style="font-size: 18px;color: #516e84;"  data-toggle="tooltip" data-placement="top" title="' +
                    updateParentGroup +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  ></i></a>';
                } else {
                  const updateParentGroup = "Resume";
                  if (rowData[2] == "HALT") {
                    data =
                      '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                      d +
                      '">' +
                      '<i aria-hidden="true" class="fa fa-caret-square-o-up text-success mt-2 haltAndResumeDetails" style="font-size: 18px;color: #80047d !important;"  data-toggle="tooltip" data-placement="top" title="' +
                      updateParentGroup +
                      '" data-animation="false" data-info="' +
                      rowData +
                      '"  ></i></a>';
                  }  else if(rowData[4]=='Completed'){
                    let updateParentGroup = 'Re-process Store';
                
                    data = '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                        + '<i aria-hidden="true" class="fa fa-upload text-primary mt-2 requeue" style="font-size: 18px; margin-right: 10px;" data-toggle="tooltip" data-placement="top" title="'
                        + updateParentGroup + '" data-animation="false" data-info="' + rowData + '" ></i></a>';
                    // data += '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                    //     + '<i aria-hidden="true" class="fa fa-trash text-danger mt-2 removeQueueItem" style="font-size: 18px;" data-toggle="tooltip" data-placement="top" title="'
                    //     + updateParentGroup1 + '" data-animation="false" data-info="' + rowData + '" ></i></a>';
                  } else {
                    data = "";
                  }
                }
                return data;
              },
            },
          ],
        });
        // tslint:disable-next-line:no-unused-expression
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
          "alphanumeric-sort-asc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "alphanumeric-sort-desc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? 1 : x > y ? -1 : 0;
          },
          "formatted-date-asc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a ? new Date(a).getTime() : 0;
            const y = b ? new Date(b).getTime() : 0;
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "formatted-date-desc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a ? new Date(a.toString()).getTime() : 0;
            const y = b ? new Date(b.toString()).getTime() : 0;
            return x < y ? 1 : x > y ? -1 : 0;
          },
        });
        elm.reDrawScheduleTableCompleted(
          elm.processQueueListCompleted,
          loaderStatus
        );
      });
    }, 200);
  }

  /**
   * reDrawScheduleTableCompleted function will redraw the datatable using new table values
   *
   */
  reDrawScheduleTableCompleted(temp: any, loaderStatus: any) {
    table1 = $("#scheduleProcessCompleted").DataTable();
    table1.search("").draw();
    table1 = $("#scheduleProcessCompleted").dataTable();
    table1.fnClearTable();
    table1 = $("#scheduleProcessCompleted").dataTable();

    const tempArr = [];
    for (let i = 0; i < temp.length; i++) {
      const t = temp[i];
      const rpt = [
        t.groupName,
        t.store,
        t.range,
        t.date,
        t.status,
        t.failedReason,
        t.jobStartDate,
        t.jobEndDate,
        t.jobType,
        t.storeName,
	      t.priority,
        t.processFileName
      ];
      tempArr.push(rpt);
    }
    if (tempArr.length > 0) {
      table1.fnAddData(tempArr, false); // Add new data
    }
    table1.fnDraw(); // Redraw the DataTable
    loaderStatus ? (this.scheduleProcessQueueCompletedLoading = false) : "";
    if (temp.length > 0) {
      setTimeout(() => {
        $("#scheduleProcessCompleted").DataTable().columns.adjust().draw(false);
      }, 100);
    }
  }

  refreshScheduleList() {
    this.closeToolTip();
    this.loadingSchedule = true;
    this.getAllJobs(() => {
      this.showScheduledProcessList(true);
      this.loadingSchedule = false;
    });
  }

  refreshScheduleListCompleted() {
    this.closeToolTip();
    this.loadingScheduleCompleted = true;
    this.getAllJobs(() => {
      this.showScheduledProcessListCompleted(true);
      this.loadingScheduleCompleted = false;
    });
  }

  refreshProcessXmlList() {
    this.closeToolTip();
    this.loadingProcessXml = true;
    this.getAllProcessXmlJobs(() => {
      this.showProcessXmlList();
      this.loadingProcessXml = false;
    });
  }

  runNowSchedule(data: any) {
    console.log("run now data",data);
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      var userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
    }
    let rowData = data.split(",");
    console.log("rowData",rowData);
    let adhocStorecode = rowData[24] ;
    let adhocgroup = rowData[0];
    let mongoDbId = rowData[28];
    const payload = { id: mongoDbId,mageStoreCode:adhocStorecode};
    let companyObj: any = null;
    let url = environment.getExtractJobUrl;
    const token = localStorage.getItem("token");
    // let headers = new HttpHeaders({ "Content-Type": "application/json" });
    // headers.append("Authorization", `Bearer ${token}`);
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
    console.log("get Extract Job data",res);
    companyObj = res.status ? res.data : [];
    if (rowData[23] || companyObj.length) {
    const thridPartyUserName = rowData[1].toString();
    let metaDataObj = this.metaDataArray.filter(function (item) {
      return item.thirdpartyUserName == thridPartyUserName;
    });
    let metaData = metaDataObj[0].metaData;
    let filteredData: any = Object.assign([], this.jobGroupList);
    
    let mageGrpCode = "";
    let mageStrCode = "";
    let steCode = "St";
    let projectId = "";
    let secondProjectId = "";
            let projectType="";
            let secondaryProjectType  ="";
    let solve360Update;
    let glAccountCompanyID: any;
    let mageManufacturer: any;
    let projectIds: any;
    let secondProjectIdList: any;
    let testData: any;
    let companyIds: any;
    let brands:any;
     testData = res.testData;
     let parentName = res.parentName;
     mageGrpCode = res.mageGroupCode;
     mageStrCode = res.mageStoreCode;
     
     let storeCode = testData == true ? adhocStorecode : mageStrCode;
     let resObj = filteredData.filter(
      (item: any) => item.thirdPartyUsername === thridPartyUserName && 
      item.mageStoreCode === storeCode
    );

   
    if (resObj.length) {
      // mageGrpCode = resObj[0].mageGroupName;
      // mageStrCode = resObj[0].mageStoreCode;
      steCode = resObj[0].state;
      projectId = resObj[0].projectId;
      solve360Update = resObj[0].solve360Update;
      projectType = resObj[0].projectType;     
      secondaryProjectType = resObj[0].secondaryProjectType;   
    }
    var date = moment(rowData[3]);
    var now = moment();
    if (false) {
      swal({
        title: this.SchedulerConstantService.JOB_STARTED,
        type: "warning",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: this.constantService.CLOSE,
      });
      this.refreshScheduleList();
    } else {
      swal(
        {
          title: this.constantService.AREYOUSURE,
          text: this.SchedulerConstantService.RUN_SCHEDULE_NOW,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default pointer",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: "Run",
          closeOnConfirm: true,
          showLoaderOnConfirm: true,
        },
        () => {
          this.EventEmitterService.displayProgress.emit(
            this.SchedulerConstantService.EVENT_EMITTER.STEP1
          );
          let rowData = data.split(",");
          const groupName = rowData[0];
          let activityData = {
            activityName: "Manage CDK3PA",
            activityType: "Run CDK Jobs ",
            activityDescription: `Run Schedule for Group ${groupName} (Group: ${mageGrpCode}, Store: ${mageStrCode})`,
          };
          this.commonService.saveActivity("Manage CDK3PA", activityData);
          const dealerId = rowData[1].toString();
          // let jobSchedule: any = moment(
          //   rowData[3],
          //   this.SchedulerConstantService.DATE_FORMAT
          // );
           let jobSchedule: any = moment(
            rowData[3]
         ).format('MM-DD-YYYY HH:mm:ss');
          
          let scheduleOpSplit: any = jobSchedule.split(" ");
          let scheduleOp: any = scheduleOpSplit[0].split("-");
          jobSchedule =
            scheduleOp[2] +
            "-" +
            scheduleOp[0] +
            "-" +
            scheduleOp[1] +
            " " +
            scheduleOpSplit[1];
          let solve360Update = rowData[13].toLowerCase() == "true";
          let buildProxies = rowData[14].toLowerCase() == "true";
          let includeMetaData = rowData[15].toLowerCase() == "true";
          let extractAccountingData = rowData[18].toLowerCase() == "true";
          let dualProxy = rowData[19].toLowerCase() == "true";
          console.log("RUN NOW rowData:", rowData);
          if (rowData[12]) {
            projectId = rowData[12].toString();
          }
          if (rowData[16]) {
            secondProjectId = rowData[16].toString();
          }

          if (rowData[17]) {
            glAccountCompanyID = rowData[17].toString();
          }

          if (rowData[20]) {
            mageManufacturer = rowData[20].toString();
          }

          if (rowData[21]) {
            projectIds = rowData[21].toString();
          }

          if (rowData[22]) {
            secondProjectIdList = rowData[22].toString();
          }

          // if(rowData[23]){
          //   testData = Boolean(rowData[23]);
          // }

          if(rowData[27]){
            companyIds = rowData[27];
          }
             if(rowData[33]){
            brands = rowData[33];
          }

          const dateRange = rowData[2].split(" - ");
          const startDate = dateRange[0].trim();
          const endDate = dateRange[1].trim();
          let date = new Date(jobSchedule);
          const closedRoOption = rowData[8];
          let jobType = rowData[11]
            ? rowData[11]
            : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
          let now_utc = Date.UTC(
            date.getUTCFullYear(),
            date.getUTCMonth(),
            date.getUTCDate(),
            date.getUTCHours(),
            date.getUTCMinutes(),
            date.getUTCSeconds()
          );
          const now_utcOP = new Date(now_utc);
          console.log("Test Adta!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!",testData);
          const scheduleObj: any = {
            jobSchedule: moment(now_utcOP).toISOString(),
            jobData: {
              groupName: groupName.trim(),
              storeData: {
                dealerId: dealerId.trim(),
                projectId: projectId,
                secondProjectId: secondProjectId,
                glAccountCompanyID: glAccountCompanyID,
                mageManufacturer: mageManufacturer,
                solve360Update: testData == true ? false : solve360Update,
                buildProxies: buildProxies,
                includeMetaData: includeMetaData,
                extractAccountingData: extractAccountingData,
                dualProxy: dualProxy,
                userName: userName,
                startDate: startDate,
                endDate: endDate,
                closedROOption: closedRoOption,
                jobType: jobType,
                mageGroupCode: testData == true ? adhocgroup : mageGrpCode,
                mageStoreCode: testData == true ? adhocStorecode : mageStrCode,
                stateCode: steCode ? steCode : 'St',
                metaData: metaData,
                projectIds: testData == true ? '0' : projectIds,
                secondProjectIdList: testData ==true ? '0' : secondProjectIdList,
                companyIds:testData == true? '0' : companyIds,
                testData:testData,
                companyObj:companyObj,
                parentName:parentName,
                projectType,
                secondaryProjectType,
                brands:brands 

              },
            },
          };
          let self = this;
          
          console.log("runNowCDKExtractJobByStore...........",scheduleObj);
          
          this.apollo
            .use("manageSchedule")
            .mutate({
              mutation: runNowCDKExtractJobByStore,
              variables: scheduleObj,
            })
            .pipe(takeUntil(this.subscription$))
            .subscribe({
              next: (listdata: any) => {
                const result: any = listdata.data;
                NProgress.done();
                const status = result.runNowCDKExtractJobByStore.status;
                const message = result.runNowCDKExtractJobByStore.message;
                if (status) {
                  this.EventEmitterService.displayProgress.emit(
                    this.SchedulerConstantService.EVENT_EMITTER.STEP_RELOAD_LIST
                  );
                  this.refreshScheduleList();
                  setTimeout(function () {
                    self.refreshScheduleList();
                    self.EventEmitterService.displayProgress.emit("");
                    self.showStatusMessage(message, "success");
                  }, 3000);
                } else {
                  self.EventEmitterService.displayProgress.emit("");
                }
              },
              error: (err:Error) => {
                this.EventEmitterService.displayProgress.emit("");
                NProgress.done();
                const message =
                  this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, "failure");
                this.commonService.errorCallback(err, this);
              },
              complete: () => {
                console.log("Completed");
              },
            });
        }
      );
    }
      }else{
        swal({
          title: this.SchedulerConstantService.JOB_STARTED,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      }
    })
  }

  cancelSchedule(data: any) {
    let userName: any;
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
    }

    let rowData = data.split(",");
    console.log(
      "################################################################cancel schedule rowdata  ######################"
    );
    console.log(rowData);
    const thridPartyUserName = rowData[1].toString();
    console.log(rowData);
    let filteredData: any = Object.assign([], this.jobGroupList);
    let resObj = filteredData.filter(
      (item: any) => item.thirdPartyUsername === thridPartyUserName
    );
    let mageGrpCode = "";
    let mageStrCode = "";
    let steCode = "";
    let projectId = "";
    let secondProjectId = "";
    let glAccountCompanyID: any;
    let mageManufacturer: any;

    if (resObj.length) {
      mageGrpCode = resObj[0].mageGroupName;
      mageStrCode = resObj[0].mageStoreCode;
      steCode = resObj[0].state;
      projectId = resObj[0].projectId;
    }
    var date = moment(rowData[3]);
    var now = moment();
    if (now > date) {
      swal({
        title: this.SchedulerConstantService.JOB_STARTED,
        type: "warning",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: this.constantService.CLOSE,
      });
      this.refreshScheduleList();
    } else {
      swal(
        {
          title: this.constantService.AREYOUSURE,
          text: this.SchedulerConstantService.CANCEL_SCHEDULE_NOW,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default pointer",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: "Continue",
          closeOnConfirm: true,
          showLoaderOnConfirm: true,
        },
        () => {
          this.EventEmitterService.displayProgress.emit(
            this.SchedulerConstantService.EVENT_EMITTER.CANCEL_SCHEDULE
          );
          let rowData = data.split(",");
          const groupName = rowData[0];
          let activityData = {
            activityName: "Manage CDK3PA",
            activityType: "Cancel CDK Jobs ",
            activityDescription: `Cancel Schedule for Group ${groupName} (Group: ${mageGrpCode}, Store: ${mageStrCode})`,
          };
          this.commonService.saveActivity("Manage CDK3PA", activityData);
          const dealerId = rowData[1].toString();
          // let jobSchedule: any = moment(
          //   rowData[3],
          //   this.SchedulerConstantService.DATE_FORMAT
          // );
          let jobSchedule: any = moment(
            rowData[3],
          ).
          format('MM-DD-YYYY HH:mm:ss');
          console.log("jobSchedule---------------->format",jobSchedule)
          'mm-dd-yyyy undefined'
          let scheduleOpSplit: any = jobSchedule.split(" ");
          let scheduleOp = scheduleOpSplit[0].split("-");
          jobSchedule =
            scheduleOp[2] +
            "-" +
            scheduleOp[0] +
            "-" +
            scheduleOp[1] +
            " " +
            scheduleOpSplit[1];
          const dateRange = rowData[2].split(" - ");
          const startDate = dateRange[0].trim();
          const endDate = dateRange[1].trim();
          const closedRoOption = rowData[8];

          let solve360Update = rowData[13].toLowerCase() == "true";
          let buildProxies = rowData[14].toLowerCase() == "true";
          let includeMetaData = rowData[15].toLowerCase() == "true";
          let extractAccountingData = rowData[18].toLowerCase() == "true";
          let dualProxy = rowData[19].toLowerCase() == "true";
          if (rowData[12]) {
            projectId = rowData[12].toString();
          }
          if (rowData[16]) {
            secondProjectId = rowData[16].toString();
          }

          if (rowData[17]) {
            glAccountCompanyID = rowData[17].toString();
          }

          if (rowData[20]) {
            mageManufacturer = rowData[20].toString();
          }

          console.log("jobSchedule---------------->format",jobSchedule)

          let date = new Date(jobSchedule);
          console.log("jobSchedule---------------->date",jobSchedule)

          let now_utc = Date.UTC(
            date.getUTCFullYear(),
            date.getUTCMonth(),
            date.getUTCDate(),
            date.getUTCHours(),
            date.getUTCMinutes(),
            date.getUTCSeconds()
          );
          const now_utcOP = new Date(now_utc);
          console.log("jobSchedule---------------->format",now_utcOP)

          const scheduleObj: any = {
            jobSchedule: moment(now_utcOP).toISOString(),
            jobData: {
              groupName: groupName,
              storeData: {
                dealerId: dealerId,
                projectId: projectId,
                secondProjectId: secondProjectId,
                glAccountCompanyID: glAccountCompanyID,
                mageManufacturer: mageManufacturer,
                solve360Update: solve360Update,
                buildProxies: buildProxies,
                includeMetaData: includeMetaData,
                extractAccountingData: extractAccountingData,
                dualProxy: dualProxy,
                userName: userName,
                startDate: startDate,
                endDate: endDate,
                closedROOption: closedRoOption,
                mageGroupCode: mageGrpCode,
                mageStoreCode: mageStrCode,
                stateCode: steCode,
              },
            },
          };
          let self = this;
          this.apollo
            .use("manageSchedule")
            .mutate({
              mutation: cancelCDKExtractJobByStore,
              variables: scheduleObj,
            })
            .pipe(takeUntil(this.subscription$))
            .subscribe({
              next: (listdata: any) => {
                const result: any = listdata.data;
                NProgress.done();
                // const result: any = data;
                const status = result.cancelCDKExtractJobByStore.status;
                const message = result.cancelCDKExtractJobByStore.message;
                if (status) {
                  this.EventEmitterService.displayProgress.emit(
                    this.SchedulerConstantService.EVENT_EMITTER.CANCEL_SCHEDULE
                  );
                  this.refreshScheduleList();
                  setTimeout(function () {
                    self.EventEmitterService.displayProgress.emit("");
                    self.showStatusMessage(message, "success");
                  }, 3000);
                } else {
                  self.EventEmitterService.displayProgress.emit("");
                }
              },
              error: (err:Error) => {
                this.EventEmitterService.displayProgress.emit("");
                NProgress.done();
                const message =
                  this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, "failure");
                this.commonService.errorCallback(err, this);
              },
              complete: () => {
                console.log("Completed");
              },
            });
        }
      );
    }
  }

  processQueueToggle() {
    this.processQueueListCollapsed = !this.processQueueListCollapsed;
    if (!this.processQueueListCollapsed) {
      this.setActiveFixedHeader();
    }
  }

  completedJobsToggle() {
    this.completedListCollapsed = !this.completedListCollapsed;
    if (!this.completedListCollapsed) {
      this.refreshScheduleListCompleted();
      this.completedProcessxmlListCollapsed = true;
      this.setActiveFixedHeader();
    }
  }

  processXmlToggle() {
    this.completedProcessxmlListCollapsed =
      !this.completedProcessxmlListCollapsed;
    if (!this.completedProcessxmlListCollapsed) {
      this.completedListCollapsed = true;
      this.refreshProcessXmlList();
      this.setActiveFixedHeader();
    }
  }

  getAllProcessXmlJobs(callback: any) {
    this.processXmlListArray = [];
    const allStoreGroupsList = this.apollo
      .use("manageSchedule")
      .query({
        query: getAllProcessXMLJobs,
        fetchPolicy: "network-only",
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          console.log("Process Xml Jobs");
          console.log(result);
          // console.log('End Process Xml Jobs');
          let obj: any = {};
          this.processXmlListArray = [];
          if (
            result["data"]["getAllProcessXMLJobs"] &&
            result["data"]["getAllProcessXMLJobs"]["processXMLJobsQueue"]
          ) {
            console.log("listData processXMLJobsQueue");
            $.each(
              result["data"]["getAllProcessXMLJobs"]["processXMLJobsQueue"],
              (key: any, val: any) => {
                console.log("val==========>getAllProcessXMLJobs");
                let groupName = "test";
                let date = "";
                let nextRunAt = "";
                let statusFlag =
                  this.SchedulerConstantService.STATUS_FLAG.QUEUED;

                let storeNameFromInputFilePath = val.fileToProcess
                  .split("/")
                  .reverse()[0];
                let storeNameFromInputFileName = "";
                storeNameFromInputFileName =
                  storeNameFromInputFilePath.split("-")[1];
                // console.log('storeNameFromInputFileName:', storeNameFromInputFileName);

                obj = {
                  groupName: groupName,
                  storeID: val.storeID,
                  statusFlag: statusFlag,
                  lastRunAt: null,
                  fileProcessed: null,
                  outputFile: null,
                  message: null,
                  failedReason: null,
                  nextRunAt: nextRunAt,
                  statusInfo: "",
                  uniqueId: "",
                  storeName: storeNameFromInputFileName,
                  reRun: false,
		  priority:val.priority
                };
                obj.statusInfo =
                  "File To Process: " + val.fileToProcess + " \n";
                this.processXmlListArray.push(obj);
              }
            );
          }
          if (
            result["data"]["getAllProcessXMLJobs"] &&
            result["data"]["getAllProcessXMLJobs"]["processXMLJobs"]
          ) {
            console.log("listData processXMLJobs");
            $.each(
              result["data"]["getAllProcessXMLJobs"]["processXMLJobs"],
              (key: any, val: any) => {
                console.log("val============>", val);
                let groupName = "test";
                let scheduledDate = val.nextRunAt
                  ? val.nextRunAt
                  : val.lastRunAt
                  ? val.lastRunAt
                  : null;
                let date = "";
                scheduledDate
                  ? (date = moment
                      .unix(scheduledDate / 1000)
                      .format("MM-DD-YYYY HH:mm:ss"))
                  : null;
                let nextRunAt = "";
                val.nextRunAt
                  ? (nextRunAt = moment.unix(val.nextRunAt / 1000).fromNow())
                  : null;
                let statusFlag = this.getJobStatus(val);
		let priority;
                // console.log('++++++++++++++++++++++++++++++++++++');
                // console.log('val',val);
                // console.log('++++++++++++++++++++++++++++++++++++');

                let statusMessage = val.failReason;
                // let haltIdentifier = false;
                // if(statusMessage == 'Error: Halt' || statusMessage == 'Error: Dead'){
                //   haltIdentifier = true;
                // }

                // if(haltIdentifier){
                // if (statusMessage == "Error: Halt") {
                //   statusFlag = "HALT";
                //   this.haltState = "Halted";
                //   val.failReason = "Exceptions in core";
                // } else if (statusMessage == "Error: Dead") {
                //   statusFlag = "DEAD";
                //   this.haltState = "Held";
                //   val.failReason = "Exceptions in core";
                // }
                // }
                //let failedReason = val.failReason;

                let failReason: string =  '';
                if (statusMessage == "Error: Halt") {
                  statusFlag = "HALT";
                  this.haltState = "Halted";
                  failReason = "Exceptions in core";
                } else if (statusMessage == "Error: Dead") {
                  statusFlag = "DEAD";
                  this.haltState = "Held";
                  failReason = "Exceptions in core";
                }

                let failedReason =  failReason ? failReason : val.failReason;
                if (failedReason) {
                  failedReason = failedReason.replace(/,/g, "; ");
                }
                const uniqueId = val._id;
                val.scheduled && val.failed
                  ? (statusFlag =
                      this.SchedulerConstantService.STATUS_FLAG.RESCHEDULED)
                  : null;
                let storeName = "";
                if (val.data.storeID && val.data.inputFile) {
                  let sp1 = val.data.inputFile.split(val.data.storeID);
                  storeName = sp1 && sp1.length ? sp1[0].split("-")[1] : "";
                }

                let coreReturnExceptionCount;
                let coreChargeExceptionCount;
                let coreReturnNotEqualCoreChargeExceptionCount;
                let coreChargeWithNoSaleCount;
                let invalidCoreCostSaleMismatchCount;
                let invalidCoreAmountMismatchCount;
                let partDetailsNullExceptionCount;
                let processorUniqueId;
                let processorRunningStatus;

                if (val.data) {
                  // console.log('******************************SHARK TRACK*********************************************')
                  if (val.data.hasOwnProperty("coreReturnExceptionCount")) {
                    coreReturnExceptionCount =
                      val.data.coreReturnExceptionCount;
                  }

                  if (val.data.hasOwnProperty("coreChargeExceptionCount")) {
                    coreChargeExceptionCount =
                      val.data.coreChargeExceptionCount;
                  }

                  if (
                    val.data.hasOwnProperty(
                      "coreReturnNotEqualCoreChargeExceptionCount"
                    )
                  ) {
                    coreReturnNotEqualCoreChargeExceptionCount =
                      val.data.coreReturnNotEqualCoreChargeExceptionCount;
                  }

                  if (val.data.hasOwnProperty("coreChargeWithNoSaleCount")) {
                    coreChargeWithNoSaleCount =
                      val.data.coreChargeWithNoSaleCount;
                  }

                  if (
                    val.data.hasOwnProperty("invalidCoreCostSaleMismatchCount")
                  ) {
                    invalidCoreCostSaleMismatchCount =
                      val.data.invalidCoreCostSaleMismatchCount;
                  }

                  if (
                    val.data.hasOwnProperty("invalidCoreAmountMismatchCount")
                  ) {
                    invalidCoreAmountMismatchCount =
                      val.data.invalidCoreAmountMismatchCount;
                  }

                  if (
                    val.data.hasOwnProperty("partDetailsNullExceptionCount")
                  ) {
                    partDetailsNullExceptionCount =
                      val.data.partDetailsNullExceptionCount;
                  }

                  if (
                    val.data.hasOwnProperty("processorUniqueId")
                  ) {
                    processorUniqueId =
                      val.data.processorUniqueId;
                  }

                  if (
                    val.data.hasOwnProperty("processorRunningStatus")
                  ) {
                    processorRunningStatus =
                      val.data.processorRunningStatus;
                  }

                  

                  
                }

                // console.log('++++++++++++++++++++++++++++++++++++');
                // console.log('val.data.message:',val.data.message);
                // console.log('++++++++++++++++++++++++++++++++++++');

                // let statusMessage = val.data.message;
                // let haltIdentifier = false;
                // if(statusMessage == 'Halt'){
                //   haltIdentifier = true;
                // }

                // if(haltIdentifier){
                //   statusFlag = 'HALT';
                // }

                let reRunFlag = true;
                if (
                  val.data.inputFile &&
                  val.data.inputFile.includes("-RERUN")
                ) {
                  if (
                    val.data.outputFile != "" &&
                    val.data.outputFile != null
                  ) {
                    let tmp = val.data.outputFile.split("&");
                    val.data.outputFile = tmp[0];
                  }
                  reRunFlag = false;
                }
                obj = {
                  groupName: groupName,
                  storeID: val.data.storeID,
                  statusFlag: statusFlag,
                  lastRunAt: date,
                  fileProcessed: val.inputFile,
                  outputFile: val.data.outputFile,
                  message: val.data.message,
                  failedReason: failedReason,
                  nextRunAt: nextRunAt,
                  statusInfo: "",
                  uniqueId: uniqueId,
                  storeName: storeName,
                  reRun: reRunFlag,
                  projectId: val.data.projectId,
                  solve360Update: val.data.solve360Update,
                  buildProxies: val.data.buildProxies,
                  includeMetaData: val.data.includeMetaData,
                  metaData: val.data.metaData,
                  glAccountCompanyID: val.data.glAccountCompanyID,
                  extractAccountingData: val.data.extractAccountingData,
                  dualProxy: val.data.dualProxy,
                  coreReturnExceptionCount: coreReturnExceptionCount,
                  coreChargeExceptionCount: coreChargeExceptionCount,
                  coreReturnNotEqualCoreChargeExceptionCount:
                    coreReturnNotEqualCoreChargeExceptionCount,
                  coreChargeWithNoSaleCount: coreChargeWithNoSaleCount,
                  invalidCoreCostSaleMismatchCount:
                    invalidCoreCostSaleMismatchCount,
                  invalidCoreAmountMismatchCount:
                    invalidCoreAmountMismatchCount,
                  partDetailsNullExceptionCount: partDetailsNullExceptionCount,
                  uploadStatus: val.uploadStatus,
                  processorUniqueId:processorUniqueId,
                  processorRunningStatus:processorRunningStatus
                };
                let updatedData = { ...val.data, uploadStatus: val.uploadStatus };
                obj.statusInfo = this.getStatusInfo(updatedData);
                if (
                  val.data.operation === "xml-processing" &&
                  val.data.storeID
                ) {
                  this.processXmlListArray.push(obj);
                }
                this.sortXMLArray(this.processXmlListArray);
              }
            );
          }
          callback();
        },
        error: (err:Error) => {
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  getStatusInfo(inputData: any) {
    let data = "";
    data += "Input File: " + inputData.inputFile + " \n";
    if (inputData.outputFile) {
      data += "Output File: " + inputData.outputFile + " \n";
    }
    let createdAt = null;
    if (
      inputData.createdAt &&
      inputData.createdAt.length > 10 &&
      !isNaN(inputData.createdAt)
    ) {
      let dt = inputData.createdAt;
      var y = dt.substr(0, 4);
      var t = dt.substr(4, dt.length);
      let remArray = t.match(/.{1,2}/g);
      if (remArray.length) {
        remArray[0] = (remArray[0] * 1 - 1).toString();
        remArray.unshift(y);
        createdAt = moment(remArray).format("MM-DD-YYYY HH:mm:ss");
        data += "Created At: " + createdAt + " \n";
      }
    }
    if (inputData.errorWarnningMessage) {
      data += "Warning: Error in " + inputData.errorWarnningMessage;
    }
    return data;
  }
  /**
   * showProcessXmlList function will show the Process XML List
   *
   */
  showProcessXmlList() {
    this.processXMLLoading = true;
    $("#processXmlList").dataTable().fnDestroy();
    table1 = $("#processXmlList").dataTable().fnClearTable();
    const elm = this;
    let i = 0;
    setTimeout(() => {
      $(document).ready(function () {
        table1 = $("#processXmlList").dataTable({
          language: {
            decimal: ".",
            thousands: ",",
          },
          columnDefs: [
            { type: "numeric-comma", targets: "_all" },
            { orderable: false, targets: [3] },
            { orderable: true, targets: [0, 1, 2] },
          ],
          fixedHeader: {
            header: true,
            footer: true,
            headerOffset: $(".cat__top-bar").outerHeight() - 11,
          },
          bSort: false,
          order: [1, "desc"],
          responsive: true,
          scrollX: false,
          destroy: true,
          paging: true,
          deferRender: true,
          ordering: true,
          info: true,
          filter: true,
          length: true,
          processing: true,
          lengthMenu: [
            [50, 25, 10, 5],
            [50, 25, 10, 5],
          ],
          autoWidth: false,
          scrollY: "200px",
          createdRow: function (row: any, data: any, dataIndex: any) {
            if (data[2] === "Completed" && !data[11]) {
              $(row).addClass("re-run-proxy-data");
            }
          },
          fnRowCallback: function (settings: any, aData: any) {
            const pagination = $(this)
              .closest(".dataTables_wrapper")
              .find(".dataTables_paginate");
            pagination.toggle(this.api().page.info().pages > 1);
          },
          drawCallback: function (settings: any) {
            table1 = $("#processXmlList").DataTable();
            $("td:eq(1)", settings).css("width", "25%");
            $("td:eq(2)", settings).css("width", "45%");
            $("td:eq(3)", settings).css("width", "25%");
            $("td:eq(4)", settings).css("width", "5%");
          },
          columns: [
            {
              title: "Store",
              width: "24%",
              className: "dt-head-left",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);

                console.log("rowdata>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", rowData);
                let storeName = elm.getStoreNameFromDealerId(rowData[1]);
                storeName = storeName ? storeName : rowData[1];
                storeName =
                  storeName === rowData[1]
                    ? rowData[10]
                      ? rowData[10]
                      : rowData[1]
                    : storeName;
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[1] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  storeName +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Last Run At",
              width: "15%",
              type: "formatted-date",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);
                let lastRunAt = "";
                if (rowData[3]) {
                  let time = rowData[3].split(" ");
                  let HourFormat = elm.convertTimeFormat(time[1]);
                  lastRunAt = time[0] + " " + HourFormat;
                }
                data = "<span>" + lastRunAt + "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Status",
              width: "15%",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData = [];
                rowData = Object.assign([], rows);

                 console.log('Ohhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh')
                 console.log('rowData:', rowData);
                 console.log('rowData[2]:', rowData[2]);
                 console.log('Ohhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh')

                var className = "";
                var toolTipMessage = rowData[8];
                rowData[7]
                  ? (toolTipMessage += "Failed Reason: " + rowData[7] + " \n")
                  : "";                                
                rowData[2] ===
                elm.SchedulerConstantService.STATUS_FLAG.COMPLETED
                  ? (className = "label-success")
                  : null;
                  if (rowData[20] === false) {
                    toolTipMessage += "Sharepoint file upload failed \n";
                    rowData[2]="Upload Halt";
                }
                rowData[2] ===
                elm.SchedulerConstantService.STATUS_FLAG.SCHEDULED
                  ? (className = "label-scheduled")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.RUNNING
                  ? (className = "label-running")
                  : null;
                rowData[2] ===
                elm.SchedulerConstantService.STATUS_FLAG.REPEATING
                  ? (className = "label-repeating")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.FAILED
                  ? (className = "label-failed")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.QUEUED
                  ? (className = "label-queued")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.LOCKED
                  ? (className = "label-locked")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.UPLOADHALT
                ? (className = "label-upload-halt")
                : null;
                rowData[2] == "HALT" ? (className = "label-halt") : null;
                rowData[2] == "DEAD" ? (className = "label-dead") : null;

                if (rowData[2] == "DEAD") {
                  rowData[2] = "HELD";
                }

                data =
                  '<span style="cursor:pointer;float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="statusMessageDisplay label ' +
                  className +
                  '" data-toggle="tooltip" data-info="' +
                  rowData +
                  '" data-placement="left" title="' +
                  toolTipMessage +
                  '" data-animation="false">' +
                  rowData[2] +
                  "</span>";
                return data;
              },
            },
            {
              title: "",
              width: "5%",
              className: "dt-head-left pl-0",
              render: function (data: any, type: any, rows: any, meta: any) {
                const d = data;
                let rowData = [];
                rowData = Object.assign([], rows);
                const enableReRunFlag = rowData[11];
                console.log(
                  "?????????????????????????????????????????????????rowdata",
                  rowData
                );
                if (
                  rowData[2] ===
                    elm.SchedulerConstantService.STATUS_FLAG.COMPLETED &&
                  enableReRunFlag && rowData[20] !== false
                ) {
                  const updateParentGroup = "Re-Run";
                  data =
                    '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-caret-square-o-up text-success mt-2 fetchPayTypeDetails" style="font-size: 18px;color: #516e84;"  data-toggle="tooltip" data-placement="top" title="' +
                    updateParentGroup +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  ></i></a>';
                } else {
                  const updateParentGroup = "Resume";
                  if (rowData[2] == "HALT") {
                    data =
                      '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                      d +
                      '">' +
                      '<i aria-hidden="true" class="fa fa-caret-square-o-up text-success mt-2 haltAndResumeDetails" style="font-size: 18px;color: #80047d !important;"  data-toggle="tooltip" data-placement="top" title="' +
                      updateParentGroup +
                      '" data-animation="false" data-info="' +
                      rowData +
                      '"  ></i></a>';
                  }  else if(rowData[2] == "Queued"){
                    let updateParentGroup = 'Change Priority';
                    let updateParentGroup1 = 'Remove Item';
                    data = '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                        + '<i aria-hidden="true" class="fa fa-edit text-primary mt-2 changePriority" style="font-size: 18px; margin-right: 10px;" data-toggle="tooltip" data-placement="top" title="'
                        + updateParentGroup + '" data-animation="false" data-info="' + rowData + '" ></i></a>';
                    data += '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                        + '<i aria-hidden="true" class="fa fa-trash text-danger mt-2 removeQueueItem" style="font-size: 18px;" data-toggle="tooltip" data-placement="top" title="'
                        + updateParentGroup1 + '" data-animation="false" data-info="' + rowData + '" ></i></a>';
                  } else if(rowData[2] == "Running"){
                    let updateParentGroup = 'Show Processor Status';
              
                      data = '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                        + '<i aria-hidden="true" class="fa fa-info-circle text-dark mt-2 showProcessorStatus" style="font-size: 18px; margin-right: 10px;" data-toggle="tooltip" data-placement="top" title="'
                        + updateParentGroup + '" data-animation="false" data-info="' + rowData + '" ></i></a>';
                   
                   }
                   else {
                    data = "";
                  }
                }
                return data;
              },
            },
          ],
          rowGroup: {
            dataSrc: "Store Group",
          },
        });
        // tslint:disable-next-line:no-unused-expression
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
          "formatted-num-pre": function (a: any) {
            a = a === "-" || a === "" ? 0 : a.replace(/[^\d\-\.]/g, "");
            return parseFloat(a);
          },
          "formatted-num-asc": function (a: any, b: any) {
            return a - b;
          },
          "formatted-num-desc": function (a: any, b: any) {
            return b - a;
          },
          "formatted-date-asc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a ? new Date(a).getTime() : 0;
            const y = b ? new Date(b).getTime() : 0;
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "formatted-date-desc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a
              ? new Date(a.toString()).getTime()
              : moment().unix() * 1000;
            const y = b
              ? new Date(b.toString()).getTime()
              : moment().unix() * 1000;
            return x < y ? 1 : x > y ? -1 : 0;
          },
        });
        console.log("this.processXmlListArray", elm.processXmlListArray);
        elm.reDrawProcessXmlTable(elm.processXmlListArray);
      });
    }, 200);
  }

  /**
   * reDrawProcessXmlTable function will redraw the datatable using new table values
   *
   */
  reDrawProcessXmlTable(temp: any) {
    console.log(
      "******************************************************Temp***************************reDrawProcessXmlTable",
      temp
    );

    table1 = $("#processXmlList").DataTable();
    table1.search("").draw();
    table1 = $("#processXmlList").dataTable();
    table1.fnClearTable();
    table1 = $("#processXmlList").dataTable();
    const tempArr = [];

    for (let i = 0; i < temp.length; i++) {
      const t = temp[i];

      const rpt = [
        t.groupName,
        t.storeID,
        t.statusFlag,
        t.lastRunAt,
        t.fileProcessed,
        t.outputFile,
        t.message,
        t.failedReason,
        t.statusInfo,
        t.uniqueId,
        t.storeName,
        t.reRun,
        t.coreReturnExceptionCount,
        t.coreChargeExceptionCount,
        t.coreReturnNotEqualCoreChargeExceptionCount,
        t.coreChargeWithNoSaleCount,
        t.invalidCoreCostSaleMismatchCount,
        t.invalidCoreAmountMismatchCount,
        t.partDetailsNullExceptionCount,
        t.processorUniqueId,
	t.priority,
        t.uploadStatus,
        t.processorRunningStatus
      ];

      tempArr.push(rpt);
    }
    if (tempArr.length > 0) {
      table1.fnAddData(tempArr, false); // Add new data
    }
    table1.fnDraw(); // Redraw the DataTable
    this.processXMLLoading = false;
    if (temp.length > 0) {
      setTimeout(() => {
        $("#processXmlList").DataTable().columns.adjust().draw(false);
      }, 100);
    }
  }

  setActiveFixedHeader() {
    if (
      navigator.userAgent.indexOf("MSIE") !== -1 ||
      navigator.appVersion.indexOf("Trident/") > 0
    ) {
      const evt = document.createEvent("UIEvents");
      evt.initUIEvent("resize", true, false, window, 0);
      window.dispatchEvent(evt);
    } else {
      window.dispatchEvent(new Event("resize"));
    }
  }

  startDateSelection() {
    let date: any = moment().subtract(6, "months");
    if (date.format("D") <= this.SchedulerConstantService.DAY_NUMBER_CHECK) {
      return moment(date).startOf("month");
    } else {
      return moment(date).add(1, "months").startOf("month");
    }
  }

  endDateSelection() {
    return moment();
  }

  utcToLocalTime(time: any) {
    let localTime = null;
    localTime = moment(time * 1)
      .local()
      .format(this.SchedulerConstantService.SCHEDULE_DATE_FORMAT);
    return localTime;
  }

  utcTotimeFrameZoneDisplay(startTime: any, endTime: any, timeFrameZone: any) {
    let tooltipInfo = null;
    tooltipInfo = "Time Zone: " + timeFrameZone + "\n";
    tooltipInfo +=
      "Start Time: " +
      moment
        .utc(startTime * 1)
        .tz(timeFrameZone)
        .format(this.SchedulerConstantService.SCHEDULE_DATE_FORMAT) +
      "\n";
    tooltipInfo +=
      "End Time: " +
      moment
        .utc(endTime * 1)
        .tz(timeFrameZone)
        .format(this.SchedulerConstantService.SCHEDULE_DATE_FORMAT) +
      "\n";
    return tooltipInfo;
  }

  getRoOptionList() {
    this.roOptionList = [];
    let data = this.SchedulerConstantService.RO_OPTION;
    for (let i = 0; i < data.length; i++) {
      this.roOptionList.push({ id: data[i], itemName: data[i] });
    }
  }

  onChangeRoOptionList(item: any) {
    if (!this.containsObject(item, this.onChangeRoOption)) {
      this.onChangeRoOption.push(item);
    }
  }

  getAllJobsForUiUpdate(callback: any) {
    this.compareObjArrayLatest = [];
    const allStoreGroupsList = this.apollo
      .use("manageSchedule")
      .query({
        query: getAllCDKExtractJobs,
        fetchPolicy: "network-only",
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          let obj: any = {};
          $.each(
            result["data"]["getAllCDKExtractJobs"]["jobArray"],
            (key: any, val: any) => {
              let groupName = val.data.groupName;
              let scheduledDate = val.nextRunAt
                ? val.nextRunAt
                : val.lastRunAt
                ? val.lastRunAt
                : null;
              let date = "";
              scheduledDate
                ? (date = moment
                    .unix(scheduledDate / 1000)
                    .format("MM-DD-YYYY HH:mm:ss"))
                : null;
              let nextRunAt = "";
              val.nextRunAt
                ? (nextRunAt = moment.unix(val.nextRunAt / 1000).fromNow())
                : null;
              let groupStatus = val;
              const failedReason = val.failReason;
              $.each(val.data["storeDataArray"], (key: any, val: any) => {
                let jobStartDate = null;
                let jobEndDate = null;
                let rpt = [];
                let keyVal = null;
                let status = null;
                let seperator = "/";
                if (val.startDate.includes("-")) {
                  seperator = "-";
                }
                const dateArray = val.startDate.split(seperator);
                const startDate =
                  dateArray[0] + "-" + dateArray[1] + "-" + dateArray[2];
                if (val.endDate.includes("-")) {
                  seperator = "-";
                }
                const dateArrayEnd = val.endDate.split(seperator);
                const endDate =
                  dateArrayEnd[0] +
                  "-" +
                  dateArrayEnd[1] +
                  "-" +
                  dateArrayEnd[2];
                let range = startDate + " - " + endDate;
                jobStartDate = val.startTime;
                jobEndDate = val.endTime;
                rpt = [groupName, val.dealerId, range, date];
                keyVal = rpt.join();
                let jobStatus = false;
                status = val.status;
                let statusFlag = "";
                if (jobStartDate && jobEndDate && status) {
                  jobStatus = true;
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.COMPLETED;
                } else if (jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.RUNNING;
                } else if (!jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.SCHEDULED;
                } else if (jobStartDate && jobEndDate && !status) {
                  jobStatus = true;
                  statusFlag = this.SchedulerConstantService.STATUS_FLAG.FAILED;
                } else {
                  statusFlag = this.getJobStatus(groupStatus);
                }
                status = statusFlag;
                let objNew: any = {};
                objNew[keyVal] = status;
                this.compareObjArrayLatest.push(objNew);
              });
            }
          );
          callback();
        },
        error: (err:Error) => {
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  compareObjectValue() {
    let dataTableObjects = [];
    let data = null;
    let self = this;
    table1 = $("#scheduleProcessQueue").DataTable();
    data = table1.rows().data();
    let i = 0;
    data.each(function (value: any, index: number) {
      let status = null;
      let keyVal = "";
      let res = null;
      keyVal = value[0] + "," + value[1] + "," + value[2] + "," + value[3];
      status = value[4];
      res = self.compareKey(keyVal);
      if (res !== status) {
        self.reloadExtractionQueue(res);
      }
      i++;
    });
  }

  reloadExtractionQueue(res: any) {
    this.refreshScheduleList();
    if (
      res === this.SchedulerConstantService.STATUS_FLAG.COMPLETED ||
      res === this.SchedulerConstantService.STATUS_FLAG.FAILED
    ) {
      this.refreshScheduleList();
      this.refreshScheduleListCompleted();
    }
  }

  compareKey(key: any) {
    let status = null;
    var itemsProcessed = 0;
    let length = this.compareObjArrayLatest.length;
    this.compareObjArrayLatest.forEach(function (obj, index: number, self) {
      itemsProcessed++;
      if (obj.hasOwnProperty(key)) {
        status = obj[key];
      }
    });
    if (itemsProcessed === length) {
      return status;
    }
    return;
  }

  processXMLReloadList(callback: any) {
    this.compareObjArrayProcessXMLList = [];
    this.processXMLJobsQueueLength = 0;
    const allStoreGroupsList = this.apollo
      .use("manageSchedule")
      .query({
        query: getAllProcessXMLJobs,
        fetchPolicy: "network-only",
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          if (
            result["data"]["getAllProcessXMLJobs"] &&
            result["data"]["getAllProcessXMLJobs"]["processXMLJobs"]
          ) {
            $.each(
              result["data"]["getAllProcessXMLJobs"]["processXMLJobs"],
              (key: any, val: any) => {
                let statusFlag = this.getJobStatus(val);
                let obj = { statusFlag: statusFlag };
                if (
                  val.data.operation === "xml-processing" &&
                  val.data.storeID
                ) {
                  let keyVal = null;
                  keyVal = val._id;
                  let objNew: any = {};
                  objNew[keyVal] = statusFlag;
                  this.compareObjArrayProcessXMLList.push(objNew);
                }
              }
            );
            if (
              result["data"]["getAllProcessXMLJobs"] &&
              result["data"]["getAllProcessXMLJobs"]["processXMLJobsQueue"]
            ) {
              this.processXMLJobsQueueLength = result["data"][
                "getAllProcessXMLJobs"
              ]["processXMLJobsQueue"].length
                ? result["data"]["getAllProcessXMLJobs"]["processXMLJobsQueue"]
                    .length
                : 0;
            }
            callback();
          }
        },
        error: (err:Error) => {
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  compareObjectValueProcessXML() {
    let reloadStatus = false;
    let data = null;
    let self = this;
    table1 = $("#processXmlList").DataTable();
    data = table1.rows().data();
    let incQueuedList = 0;
    let incProcessList = 0;
    data.each(function (value: any, index: number) {
      let status = null;
      let keyVal = "";
      let res = null;
      if (value[2] != self.SchedulerConstantService.STATUS_FLAG.QUEUED) {
        // console.log('%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%')
        // console.log('value[2] != self.SchedulerConstantService.STATUS_FLAG.QUEUED')
        // console.log('%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%')
        keyVal = value[9];
        status = value[2];
        incProcessList++;
      } else {
        incQueuedList++;
      }
      res = self.compareProcessXMLKey(keyVal);

      if (status == "HALT" || status == "DEAD") {
        status = "Failed";
      }

      if (res !== status) {
        // console.log('%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%')
        // console.log('res !== status')
        // console.log('res:', res)
        // console.log('status:', status)
        // console.log('%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%')
        reloadStatus = true;
      }
    });
    if (
      this.processXMLJobsQueueLength !== incQueuedList &&
      this.processXMLJobsQueueLength > incQueuedList
    ) {
      // console.log('%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%')
      // console.log('this.processXMLJobsQueueLength !== incQueuedList && this.processXMLJobsQueueLength > incQueuedList')
      // console.log('%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%')
      reloadStatus = true;
    }
    if (this.compareObjArrayProcessXMLList.length !== incProcessList) {
      // console.log('%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%')
      // console.log('this.compareObjArrayProcessXMLList.length !== incProcessList')
      // console.log('%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%')
      reloadStatus = true;
    }
    if (reloadStatus) {
      this.getAllProcessXmlJobs(() => {
        this.showProcessXmlList();
      });
    }
  }

  compareProcessXMLKey(key: any) {
    let status = null;
    var itemsProcessed = 0;
    let length = this.compareObjArrayProcessXMLList.length;
    this.compareObjArrayProcessXMLList.forEach(function (
      obj,
      index: number,
      self
    ) {
      itemsProcessed++;
      if (obj.hasOwnProperty(key)) {
        status = obj[key];
      }
    });
    if (itemsProcessed === length) {
      return status;
    }
    return;
  }

  getJobStatus(val: any) {
    let statusFlag: any = "";
    val.lockedAt
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.LOCKED)
      : null;
    val.scheduled
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.SCHEDULED)
      : null;
    val.queued
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.QUEUED)
      : null;
    val.completed
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.COMPLETED)
      : null;
    val.failed
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.FAILED)
      : null;
    val.repeating
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.REPEATING)
      : null;
    val.running
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.RUNNING)
      : null;
    val.scheduled && val.failed
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.RESCHEDULED)
      : null;
    return statusFlag;
  }

  convertTimeFormat(timeString: any) {
    var H = +timeString.substr(0, 2);
    var h = H % 12 || 12;
    var ampm = H < 12 || H === 24 ? " AM" : " PM";
    timeString = h + timeString.substr(2, 3) + ampm;
    return timeString;
  }

  setDateRange(type: any, jobType: any) {
    if (type) {
      this.createSchedule.patchValue({
        roOption: "all",
      });
    }
    this.setExtractionModeSelection(jobType);
    if (this.jobTypeStatus !== type) {
      this.jobTypeStatus = type;
      if (!type) {
        this.selectMultiDateRangeOption = false;
        this.createSchedule.patchValue({
          roOption: "all",
        });
        this.setExtractionModeSelection(jobType);
        let date = moment().subtract(31, "days");
   
        // this.dateInput = {
        //   start: date,
        //   end: this.endDateSelection(),
        // };
        // this.dateInput = {
        //   start: date,
        //   end: this.selectedRange[1],
        // };
        this.selectedRange = [date.toDate(), this.endDateSelection().toDate()];
        console.log("initial start------------", this.selectedRange);

        this.dateInput = {
          start: this.selectedRange[0],
          end: this.selectedRange[1],
        };
      } else {
        this.selectMultiDateRangeOption = true;
        this.selectedRange = [
          this.startDateSelection().toDate(),
          this.endDateSelection().toDate(),
        ];
        this.dateInput = {
          start: this.selectedRange[0],
          end: this.selectedRange[1],
        };
        // this.dateInput = {
        //   start: this.startDateSelection(),
        //   end: this.endDateSelection(),
        // };
      }
    }
  }

  setExtractionModeSelection(jobType: any) {
    this.displayOnDemand = false;
    if (jobType === "current") {
      this.displayOnDemand = true;
      this.createSchedule.patchValue({
        roOption: "current",
      });
    }
    if (jobType === "refresh") {
      this.createSchedule.patchValue({
        roOption: "all",
      });
    }
  }

  checkJobExistInExtractionQueue(
    groupName: any,
    dealerCode: any,
    dateRange: any,
    mageStrCode:any
  ) {
    console.log("groupName@@@@@@@@@@@@@@@@@@@@@@@@@@@",groupName);
    console.log("dealerCode@@@@@@@@@@@@@@@@@@@@@@@@@@@",dealerCode);
    console.log("dateRange@@@@@@@@@@@@@@@@@@@@@@@@@@@",dateRange);
    console.log("mageStrCode@@@@@@@@@@@@@@@@@@@@@@@@@@@",mageStrCode);
    let processQueueListCopy = [];
    processQueueListCopy = Object.assign([], this.processQueueList);
    console.log("processQueueListCopy@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",processQueueListCopy);
    processQueueListCopy = processQueueListCopy.filter((item: any) => {
      return (
        item.groupName.trim().toLowerCase() === groupName.trim().toLowerCase() &&
        item.store.trim().toLowerCase() === dealerCode.trim().toLowerCase() &&
        item.range.trim() === dateRange.trim() &&
        item.status !== "Scheduled" &&
        item.storeName.trim().toLowerCase() === mageStrCode.trim().toLowerCase()
      );
    });
    console.log("processQueueListCopy$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",processQueueListCopy);
    return processQueueListCopy.length;
  }
  sortXMLArray(temp: any) {
    this.processXmlListArray = temp
      .filter((item: any, index: number) => index < temp.length)
      .sort((a: any, b: any): any => {
        const x = a["statusFlag"].toLowerCase().replace(/^[^a-z0-9]*/g, "");
        const y = b["statusFlag"].toLowerCase().replace(/^[^a-z0-9]*/g, "");
        return x > y ? -1 : x < y ? 1 : 0;
      });
    return temp;
  }

  reloadGroupList() {
    this.closeToolTip();
    this.reloadGroup = true;
    this.commonService.allS360Jobs("CDK3PA", "production", (result: any) => {
      this.storeGroupList = result.storeGroupList;
      this.jobGroupList = result.jobGroupList;
      this.getGroupFilterList();
      this.reloadGroup = false;
    });
  }

  stopTimer() {
    this.autoReloadCDKStatus = false;
    this.isPaused = false;
  }

  startTimer() {
    this.autoReloadCDKStatus = true;
    this.isPaused = true;
    this.getNotificationForUi();
  }

  closeToolTip() {
    $("[rel=tooltip]").tooltip("enable");
    $(".tooltip").tooltip("hide");
  }

  preSelectGroupAndStore() {
    const storeObj = localStorage.getItem("selectedStoreObj")
      ? JSON.parse(localStorage.getItem("selectedStoreObj")!)
      : null;
    const groupObj = localStorage.getItem("selectedGroupObj")
      ? JSON.parse(localStorage.getItem("selectedGroupObj")!)
      : null;
    if (storeObj && groupObj) {
      const grpObj = {
        id: groupObj.mageGroupName,
        itemName: groupObj.mageGroupName,
        mageGroupCode: groupObj.mageGroupCode,
        mageGroupName: groupObj.mageGroupName,
        mageStoreCode: groupObj.mageStoreCode,
        mageStoreName: groupObj.mageStoreName,
        projectId: groupObj.projectId,
        companyId: groupObj.companyId,
      };
      const strObj = {
        id: storeObj.companyName,
        itemName: storeObj.companyName,
        mageGroupCode: storeObj.mageGroupName,
        mageStoreCode: storeObj.mageStoreCode,
        mageStoreName: storeObj.mageStoreName,
        stateCode: storeObj.state,
        projectId: storeObj.projectId,
        thirdPartyUsername: storeObj.thirdPartyUsername,
        secondProjectId: storeObj.secondProjectId,
        companyID: storeObj.companyId,
        glAccountCompanyID: storeObj.glAccountCompanyID,
      };
      this.onSelectStoreGroup(grpObj);
      this.onSelectStore(strObj);
    }
  }

  fetchPayTypeDetails(data: any) {
    this.errorMessage = "";
    this.modalDisplayFlag = false;
    this.items = [];
    $("#payTypeModal").modal("show");
    let res = data.split(",");
    let splitFile = res[5].split("&");
    const distFile = splitFile[0];
    const etlFile = splitFile[1];
    let distFile_temp1 = distFile.split("/").reverse()[0];
    distFile_temp1 = distFile_temp1.replace("PROC-", "");
    let distFile_temp2 = distFile_temp1.split("-");
    distFile_temp2.pop();
    // distFile_temp2.pop();
    let distFile_temp3 = distFile_temp2.join("-");
    let childExistFlag = 0;
    this.processXmlListArray.forEach((element) => {
      let temp1 = element.statusInfo.split("/").reverse()[0];
      let temp2 = temp1.split("-");
      temp2.pop();
      temp2.pop();
      let temp3 = temp2.join("-");
      if (temp3.trim() === distFile_temp3.trim()) {
        childExistFlag = 1;
      }
    });

    if (childExistFlag == 1) {
      this.updatePaytypeFlag = true;
    }

    this.loadFileTypes(distFile, etlFile, () => {
      this.modalDisplayFlag = true;
    });
  }

  closePayTypeModal() {
    $("#payTypeModal").modal("hide");
  }

  loadFileTypes(distFile: any, etlFile: any, callback: any) {
    const payload = { filePath: distFile };
    // this.doProxyFilePath = etlFile.trim();
    this.doProxyFilePath = distFile.trim();
    let url = environment.payTypeListUrl;
    const token = localStorage.getItem("token");
    // let headers = new HttpHeaders({ "Content-Type": "application/json" });
    // headers.append("Authorization", `Bearer ${token}`);
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      // const resData = JSON.parse(res["_body"]);
      const resData = res;
      if (resData.status) {
        // const payTypeData = JSON.parse(res["_body"]).result;
         const payTypeData = res.result;
        this.items = payTypeData.filter(
          (item: any, index: number) => item.filename != ""
        );
        this.items.sort((a: any, b: any) =>
          a.filename.localeCompare(b.filename)
        );
        callback();
      } else {
        this.errorMessage = resData.message;
        swal({
          title: resData.message,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
        callback();
      }
    });
  }

  isChecked(event: any, item: any) {
    if (event.target.checked) {
      for (var i = 0; i < this.items.length; i++) {
        if (this.items[i].filename == item.filename) {
          this.items[i].selected = true;
        }
      }
    } else {
      for (var i = 0; i < this.items.length; i++) {
        if (this.items[i].filename == item.filename) {
          this.items[i].selected = false;
        }
      }
    }
  }

  checkAll(event: any) {
    if (event.target.checked) {
      for (var i = 0; i < this.items.length; i++) {
        this.items[i].selected = true;
      }
    } else {
      for (var i = 0; i < this.items.length; i++) {
        this.items[i].selected = false;
      }
    }
  }

  updatePayTypeInfo() {
    swal(
      {
        title: this.constantService.AREYOUSURE,
        text: this.SchedulerConstantService.DO_PROXY_NOW,
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default pointer",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: "Run",
        closeOnConfirm: true,
        showLoaderOnConfirm: true,
      },
      () => {
        this.updatePayTypeInfoSubmit();
      }
    );
  }

  updatePayTypeInfoModify() {
    var cflag: boolean = false;
    this.items.forEach((element) => {
      if (element.selected) {
        cflag = true;
      }
    });
    if (cflag) {
      swal(
        {
          title: this.constantService.AREYOUSURE,
          text: this.SchedulerConstantService.DO_PAYTYPE_UPDATE,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default pointer",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: "Update",
          closeOnConfirm: true,
          showLoaderOnConfirm: true,
        },
        () => {
          this.showSpinnerButton = true;
          this.writeFileTypes(() => {
            this.showSpinnerButton = false;
            this.closePayTypeModal();
            this.refreshProcessXmlList();
          });
        }
      );
    } else {
      swal({
        title: this.SchedulerConstantService.PAY_TYPE_FILTER.WARNING_MESSAGE,
        type: "warning",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: this.constantService.CLOSE,
      });
    }
  }

  updatePayTypeInfoSubmit() {
    this.showSpinnerButton = true;
    this.writeFileTypes((payTypeFileName: any) => {
      this.showSpinnerButton = false;
      if (payTypeFileName && this.doProxyFilePath) {
        this.scheduleJobUsingPgDump(
          payTypeFileName,
          this.doProxyFilePath,
          (result: any) => {
            if (result["createProxyWithSqlDump"].status) {
              swal({
                title:
                  this.SchedulerConstantService.PAY_TYPE_FILTER
                    .JOB_RERUN_SUCCESS,
                type: "success",
                confirmButtonClass: "btn-success pointer",
                confirmButtonText: this.constantService.CLOSE,
              });
            } else {
              swal({
                title: result["createProxyWithSqlDump"].message,
                type: "warning",
                confirmButtonClass: "btn-warning pointer",
                confirmButtonText: this.constantService.CLOSE,
              });
            }
            this.showSpinnerButton = false;
            this.closePayTypeModal();
            this.refreshProcessXmlList();
          }
        );
      } else {
        this.closePayTypeModal();
        swal({
          title:
            this.SchedulerConstantService.PAY_TYPE_FILTER
              .WARNING_MESSAGE_DO_PROXY,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      }
    });
  }

  writeFileTypes(callback: any) {
    const payload = this.items;

    alert(payload);
    console.log(
      "?????????????????????????????????????????????????????payload????????????????????????????????????????????????????????????"
    );
    console.log(payload);

    let obj, obj1;
    if (this.accountingProxy) {
      obj = {
        accountingProxy: true,
        type: "accountingProxy",
      };
    } else {
      obj = {
        accountingProxy: false,
        type: "accountingProxy",
      };
    }

    if (this.dualProxyDecider) {
      obj1 = {
        dualProxy: true,
        type: "dualProxy",
      };
    } else {
      obj1 = {
        dualProxy: false,
        type: "dualProxy",
      };
    }
    payload.push(obj);
    payload.push(obj1);

    let url = environment.writePayType;
    const token = localStorage.getItem("token");
    // let headers = new HttpHeaders({ "Content-Type": "application/json" });
    // headers.append("Authorization", `Bearer ${token}`);
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      this.accountingProxyDecider = false;
      this.dualProxyDecider = false;
      // const resData = JSON.parse(res["_body"]);
      const resData = res;
      if (resData.status) {
        callback(resData.fileName);
      } else {
        callback("");
      }
    });
  }

  scheduleJobUsingPgDump(
    payTypeFileName: any,
    doProxyFilePath: any,
    callback: any
  ) {
    const scheduleObj: any = {
      proxyJobData: {
        zipPath: doProxyFilePath,
        payType: payTypeFileName,
      },
    };
    let self = this;
    this.apollo
      .use("manageSchedule")
      .mutate({
        mutation: createProxyWithSqlDump,
        variables: scheduleObj,
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata: any) => {
          NProgress.done();
          const result: any = listdata.data;
          callback(result);
        },
        error: (err: Error) => {
          NProgress.done();
          this.closePayTypeModal();
          swal({
            title:
              this.SchedulerConstantService.PAY_TYPE_FILTER.WARNING_MESSAGE,
            type: "warning",
            confirmButtonClass: "btn-warning pointer",
            confirmButtonText: this.constantService.CLOSE,
          });
          this.showSpinnerButton = false;
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  clearTypes(event: any) {
    for (var i = 0; i < this.items.length; i++) {
      this.items[i].selected = false;
    }
  }

  validatePaytypeLabel(e: any, val: any, index: number) {
    this.items[index].pay_type = this.items[index].pay_type.toUpperCase();
    if (this.items[index].pay_type != "") {
      this.items[index].selected = true;
    } else {
      this.items[index].selected = false;
    }
  }

  haltAndResumeDetails(data: any) {
    let res;
    // console.log('++++++++++++++++++++++++++++++++++++++');
    // console.log(data);
    // console.log('++++++++++++++++++++++++++++++++++++++');

    res = data.split(",");
    console.log("res--------------------", res);
    this.coreReturnExceptionCountMsg = res[12];
    this.coreChargeExceptionCountMsg = res[13];
    this.coreReturnNotEqualCoreChargeExceptionCountMsg = res[14];
    this.coreChargeWithNoSaleCountMsg = res[15];

    this.invalidCoreCostSaleMismatchCountMsg = res[16];
    this.invalidCoreAmountMismatchCountMsg = res[17];
    this.partDetailsNullExceptionCountMsg = res[18];

    this.resumeProcessorInput = data;
    $("#haltAndResumeModal").modal("show");
  }

  closeHaltAndResumeModal() {
    $("#haltAndResumeModal").modal("hide");
  }

  resumeProcessor() {
    let res, tmp, tmp1, tmp2, inputFile;
    // console.log('********************************************');
    // console.log(this.resumeProcessorInput)
    console.log(typeof this.resumeProcessorInput);
    res = this.resumeProcessorInput.split(",");
    console.log("res@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",res);
    let processorUniqueId = res[19];
    tmp = res[8];
    console.log("tmp:", tmp);
    tmp1 = tmp.split("\n")[0];
    console.log("tmp1", tmp1);
    tmp2 = tmp1.split("Input File:")[1];
    console.log("tmp2:", tmp2);
    console.log("********************************************");
    if (tmp2) {
      inputFile = tmp2.trim();
    }
    const payload = { extractFile: inputFile, dms: "CDK3PA",processorUniqueId:processorUniqueId };
    let url = environment.haltAndResume;
    const token = localStorage.getItem("token");
    // let headers = new HttpHeaders({ "Content-Type": "application/json" });
    // headers.append("Authorization", `Bearer ${token}`);
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      // const resData = JSON.parse(res["_body"]);
      const resData = res;
      this.closeHaltAndResumeModal();
      this.refreshProcessXmlList();
      if (resData.status) {
        console.log(resData);
        swal({
          title: "Processor job resumed successfully",
          type: "success",
          confirmButtonClass: "btn-success pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      } else {
        console.log(resData);
        swal({
          title: "Something went wrong",
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      }
    });
  }

  switchServer(toggleMockServer =false) {
    if (this.solve360ServerDecider) {
      let serverType = "test";
      this.commonService.allS360Jobs("CDK3PA", serverType, (result: any) => {
        this.loading = false;
        this.storeGroupList = result.storeGroupList;
        this.storeList = result.storeList;

        this.jobGroupList = result.jobGroupList;
        for (let i = 0; i < this.jobGroupList.length; i++) {
          if (this.jobGroupList[i].dmsCode == "CDK3PA") {
            this.allStoreList.push(this.jobGroupList[i]);
          }
        }
        this.getGroupFilterList();
        this.preSelectGroupAndStore();
        this.getAllStoreFilterList();
        if(toggleMockServer){
          this.toastrService.success ("Switched to Mock Server");
        }
      });
    } else {
      let serverType = "production";
      this.commonService.allS360Jobs("CDK3PA", serverType, (result: any) => {
        this.loading = false;
        this.storeGroupList = result.storeGroupList;
        this.storeList = result.storeList;

        this.jobGroupList = result.jobGroupList;
        for (let i = 0; i < this.jobGroupList.length; i++) {
          if (this.jobGroupList[i].dmsCode == "CDK3PA") {
            this.allStoreList.push(this.jobGroupList[i]);
          }
        }
        this.getGroupFilterList();
        this.preSelectGroupAndStore();
        this.getAllStoreFilterList();
        if(toggleMockServer){
          this.toastrService.success ("Switched to Production Server");
        }
      });
    }
  }
  changePriority(data: string){
    let res = data.split(",");
    console.log('res?????????????????????????????',res);
    this.currentPriority = res[20]
    this.changePriorityForProcessorJob = res[20];
    this.fileNameForPriorityChange = res[8].split("/")[7]  
    console.log('>>>>>>>>>>>>>fileNameForPriorityChange>>>>>>>>>>>>>>>>>>>',this.fileNameForPriorityChange);
    $('#changePriorityModal').modal('show');
    
  
  }
  processorSteps = [
    "Unzipping Input to Work",
  "Creating Schema from Model",
  "Iterating Over Zip File Contents",
  "Loading Individual ROs",
  "Detecting Problematic ROs",
  "Detecting Open/Void RO Data and Reporting",
  "Generating Report of All Customer Paytypes",
  "Generating Report of Total Discount",
  "Generating Exception Report",
  "Checking for Missing ROs in Original Raw Data",
  "Generating Scheduler ID",
  "Generate GL Report for Analysis",
  "Generating Customerpay Report",
  "Copy Tables",
  "Generate Config File",
  "Load from Scheduler DB",
  "Compressing Directory",
  "Generate Exception Analysis",
  "Loading Exception Analysis",
  "Generating Proxy Repair Orders per Request",
  "Extracting Text ROs to TSV",
  "Extracting Text ROs",
  "Compressing Proxy Directory",
  "Generate Report Analysis",
  "Pre-import Halt Detection",
  "Moving Work to Bundle Directory"
  ];
  currentStepIndex: number = -1;

  showProcessorStatus(data: string) {
    let res = data.split(",");
    console.log('showProcessorStatus:', res);
    let jobId = res[9];
    
    const payload = { mongoId: jobId };
    let url = environment.fetchProcessStatus;
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` });
  
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      const resData = res?.data?.response[0];
      console.log("resData:", resData);
  
      if (resData) {
        this.processRunningStatus = resData.processorRunningStatus;
        let formattedDate = moment.utc(resData.processorStatusUpdatedAt).format('MM/DD/YYYY HH:mm:ss');
        this.processorStatusUpdatedAt = formattedDate;
        console.log("Process Running status$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.processRunningStatus);
        // Determine the current step index
        console.log("Process Running status$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.processRunningStatus.split('/'));
        this.currentStepIndex = Number(this.processRunningStatus.split('/')[0])
        console.log("currentStepIndex$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.currentStepIndex);
      } else {
        console.log("Error:", resData);
        this.showStatusMessage('Something Went Wrong!', 'failure');
      }
    });
  
    $('#showProcessorStatusModal').modal('show');
  }

  changePriorityOfProcessorJob(){
    const payload = { fileNameForPriorityChange: this.fileNameForPriorityChange.trim() ,priority:this.changePriorityForProcessorJob, dms: 'CDK3PA'};
    let url = environment.changePriority;
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      const resData = res;
      this.closeChangePriorityModal();
      this.refreshProcessXmlList();
      if (resData && resData.status) {
        console.log(resData)
        this.showStatusMessage('Priority Updated Successfully', 'success')
      } else {
        console.log(resData);
        this.showStatusMessage('Somethinbg Went Wrong!', 'failure')
      }
    }
    );   
  
  }
  
  closeChangePriorityModal(){
    $('#changePriorityModal').modal('hide');
  }

  closeProcessorStatusModal(){
    $('#showProcessorStatusModal').modal('hide');
  }

  onDateRangeChange(range: any) {
    console.log("Selected Date Range:", range);
    if (range) {
      this.dateInput.start = dayjs(range[0]).format("MM-DD-YYYY");
      this.dateInput.end = dayjs(range[1]).format("MM-DD-YYYY");
      console.log("Selected Date Range:", this.dateInput);
    }
  }

  removeQueueItem(data:any){

    swal(
      {
        title: this.constantService.AREYOUSURE,
        text: "",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default pointer",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: "Continue",
        closeOnConfirm: true,
        showLoaderOnConfirm: true,
      },
      () => {
        let res = data.split(",");
    console.log("reomve item!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!",res);
    let filePath = res[8].split(":")[1].trim();
    
  
    const url = environment.reomoveQueueItem;
    const token = localStorage.getItem('token');
    
    // Use HttpHeaders instead of Headers
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });
  
    const payload = { 
       filePath:filePath
    };
  
    this.http.post(url, payload, { headers }).subscribe((res: any) => {
      // Assuming res is already parsed JSON
      const resData = res;
      this.refreshProcessXmlList();
  
      if (resData && resData.status) {
        console.log(resData);
        this.showStatusMessage('Item  removed Successfully', 'success');
      } else {
        console.log(resData);
        this.showStatusMessage('Something Went Wrong!', 'failure');
      }
    });
  
      }
    );
  
    
  }
  requeue(data:any){

    

    swal(
      {
        title: this.constantService.AREYOUSURE,
        text: "",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default pointer",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: "Continue",
        closeOnConfirm: true,
        showLoaderOnConfirm: true,
      },
      () => {
           
         
    console.log("REQUEUE DATA$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",data.split(','));
    const url = environment.reQueueItem;
    const token = localStorage.getItem('token');
    let filePath = data.split(',')[11];
    // Use HttpHeaders instead of Headers
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });
  
    const payload = { 
       dms:'cdk3pa',
       filePath:filePath
    };
  
    this.http.post(url, payload, { headers }).subscribe((res: any) => {
      // Assuming res is already parsed JSON
      const resData = res;
      this.refreshProcessXmlList();
  
      if (resData && resData.status) {
        console.log(resData);
        this.showStatusMessage('Job Added to Processor Queue', 'success');
      } else {
        console.log(resData);
        this.showStatusMessage('Something Went Wrong!', 'failure');
      }
    });
      }
    );

  
  }
}

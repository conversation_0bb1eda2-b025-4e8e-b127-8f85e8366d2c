import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManagereynoldsComponent } from "./managereynolds.component";

const routes: Routes = [
  {
    path: "",
    component: ManagereynoldsComponent,
    data: {
      title: "ManageReynolds",
      breadcrumb: [{ label: "ManageReynolds", url: "" }],
    },
  },
];

@NgModule({
  exports: [RouterModule],
  imports: [RouterModule.forChild(routes)],
})
export class ManagereynoldsRoutingModule {}

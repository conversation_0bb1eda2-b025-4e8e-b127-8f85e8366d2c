import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { SharedModule } from "../shared/shared.module";
// import { NgWizardModule, NgWizardConfig, THEME } from "ng-wizard";
import { CheckboxCellComponent_ } from "./checkbox-cell.component";
import { CheckboxCellComponentModel } from "./checkbox-cellmodel.component";
import { SchedulerDmsImportComponent } from "./schedulerdmsimport.component";
import { SchedulerDmsImportRoutingModule } from "./schedulerdmsimport.routing.module";

// const ngWizardConfig: NgWizardConfig = {
//   theme: THEME.default,
// };

@NgModule({
  declarations: [
    // SchedulerDmsImportComponent,
    CheckboxCellComponent_,
    CheckboxCellComponentModel,
  ],
  imports: [
    CommonModule,
    SchedulerDmsImportRoutingModule,
    SchedulerDmsImportComponent,
    SharedModule,
    // NgWizardModule.forRoot(ngWizardConfig),
  ],
})
export class SchedulerDmsImportModule {}

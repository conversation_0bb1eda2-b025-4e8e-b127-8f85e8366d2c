import { Component, EventEmitter, On<PERSON><PERSON>roy, Output } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { ICellRendererParams } from "ag-grid-community";

@Component({
  selector: "checkboxmodel-cell",
  template: `
    <div class="pull-left" style="padding-right: 25px;">
      <label
        class="switch"
        title=""
        data-toggle="tooltip"
        data-animation="false"
        data-placement="left"
        data-note="Changed"
        id="hasDuJobsEnabled1"
        style="float: right;"
      >
        <input type="checkbox" (change)="onChange($event)" [checked]="true" />
        <span class="slider round 3"></span>
      </label>
    </div>
  `,
})
export class CheckboxCellComponentModel implements ICellRendererAngularComp {
  public params: any;
  public isChecked: boolean = false;
  gridIdentifier: any;
  @Output() public statusChanged: EventEmitter<boolean> =
    new EventEmitter<boolean>();
  constructor() {}
  refresh(params: any): boolean {
    this.params = params;
    return true;
  }
  agInit(params: any): void {
    this.params = params;
    this.isChecked = this.params.data[this.params.colDef.field];
  }
  public onChange(event: any) {
    // Set the value of the checkbox in the data item
    this.params.data[this.params.colDef.field] = event.currentTarget.checked;
    if (this.params.onClick instanceof Function) {
      const result: any = {
        rowdata: this.params.data,
      };

      const params = { ...result };
      this.params.onClick(params);
    }
    // Access the current state of the checkbox
    console.log("Checkbox state:", this.params.data, this.params);
    //  const result:any = {
    //   rowdata: this.params.data,
    // };
    // this.statusChanged.emit(result);
    // // this.params.context.componentParent.onCheckBoxStatus(result);
    //  console.log(this.params)
  }
}

import { Component, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { ICellRendererParams } from "ag-grid-community";

@Component({
  selector: "checkbox-cell",
  template: `
    <input
      type="checkbox"
      (click)="onChange($event)"
      [checked]="params.data.is_allowed || params.data.isAllowed"
    />
  `,
})
export class CheckboxCellComponent_ implements ICellRendererAngularComp {
  public params: any;
  public isChecked: boolean = false;
  checkedItems: any[] = []; // Array to store checked items
  constructor() {}
  refresh(params: any): boolean {
    this.params = params;
    return true;
  }
  agInit(params: any): void {
    this.params = params;
    this.isChecked = this.params.data[this.params.colDef.field];
  }
  public onChange(event: any) {
    this.params.data[this.params.colDef.field] = event.currentTarget.checked;
    const result = {
      value: this.params.value,
      event: event,
      data: this.params.data,
      rowdata: this.params.data,
    };
    console.log(this.params, "=====================datatest");

    this.params.context.componentParent.onCheckBoxChange(result);
  }
}

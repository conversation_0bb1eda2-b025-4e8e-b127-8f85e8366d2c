import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { SchedulerDmsImportComponent } from "./schedulerdmsimport.component";

const routes: Routes = [
  {
    path: "",
    component: SchedulerDmsImportComponent,
    data: {
      title: "SchedulerDmsImport",
      breadcrumb: [{ label: "SchedulerDmsImport", url: "" }],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SchedulerDmsImportRoutingModule {}

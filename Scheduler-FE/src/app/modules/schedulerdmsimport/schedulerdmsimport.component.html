<!------ Include the above in your HEAD tag ---------->
<div class="row">
    <div class="col-lg-12">
        <div *ngIf="isAuthenticated" class="ms-tab-main">
            <div class="tab-pane step-content">
                <div class="container ms-tab-container">
                    <div class="ms-tab-demo">
                        <div class="">
                            <ul class="breadcrumb">
                                <li class="" id="import-tab" *ngIf="!base64QueryString" [class.active]="currentTab === 'import-tab'" [class.completed]="completeImportStatus" [class.disabled]="
                    (isImportDisabled && currentTab !== 'import-tab') ||
                    showDisableFinishButton
                  " [class.error]="tabValidationErrors['import-tab']" (click)="onTabClick('import-tab')">
                                    <a href="javascript:void(0);">Import</a>
                                </li>
                                <li id="make-tab" [class.active]="currentTab === 'make-tab'" [class.completed]="completeMakeStatus" [class.disabled]="
                    (isMakeDisabled && currentTab !== 'make-tab') ||
                    showDisableFinishButton
                  " [class.error]="tabValidationErrors['make-tab']" (click)="onTabClick('make-tab')">
                                    <a href="javascript:void(0);">Make </a>
                                </li>

                                <li id="paytype-tab" [class.active]="currentTab === 'paytype-tab'" [class.disabled]="
                    (isPaytypeDisabled && currentTab !== 'paytype-tab') ||
                    showDisableFinishButton
                  " [class.completed]="completePaytypeStatus" [class.error]="tabValidationErrors['paytype-tab']" (click)="onTabClick('paytype-tab')">
                                    <a href="javascript:void(0);">PayType</a>
                                </li>
                                <li id="department-tab" [class.active]="currentTab === 'department-tab'" [class.disabled]="
                    (isDepartmentDisabled && currentTab !== 'department-tab') ||
                    showDisableFinishButton
                  " [class.completed]="completeDeptStatus" [class.error]="tabValidationErrors['department-tab']" (click)="onTabClick('department-tab')">
                                    <a href="javascript:void(0);">Department</a>
                                </li>
                                <li id="invoice-tab" [class.active]="currentTab === 'invoice-tab'" [class.error]="tabValidationErrors['invoice-tab']" [class.disabled]="
                    (isInvoiceDisabled && currentTab !== 'invoice-tab') ||
                    showDisableFinishButton
                  " [class.completed]="completeInvoiceStatus" (click)="onTabClick('invoice-tab')">
                                    <a href="javascript:void(0);">Invoice Sequence</a>
                                </li>
                                <li id="finish-tab" [class.active]="currentTab === 'finish-tab'" [class.disabled]="
                    (isFinishDisabled && currentTab !== 'finish-tab') ||
                    showDisableFinishButton
                  " [class.completed]="completeFinishStatus" (click)="onTabClick('finish-tab')">
                                    <a href="javascript:void(0);">Finish</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="" *ngIf="currentTab === 'import-tab' && !base64QueryString">
                    <div class="col-lg-12 row">
                        <div class="col-lg-3">
                            <label class="col-form-label" style="font-weight: 700">Store Group <span style="color: red">*</span>
              </label>
                            <span style="padding-top: 10px" class="pull-right text-warning" *ngIf="storeGroup.length !== 0"><a
                  href="https://secure.solve360.com/company/{{
                    storeFilterList[0].companyID
                  }}"
                  target="_blank"
                  class="text-warning"
                  >s360 Group
                  <em class="fa fa-external-link" aria-hidden="true"></em></a
              ></span>
                            <div id="storeGroup-select" *ngIf="!loading" [ngClass]="displayFieldCssCreateSchedule('storeGroup')">
                                <ng-multiselect-dropdown appFocusOnClick class="select-sp-report searchicon-dropdown filter-type-status multi-search" [placeholder]="'Select Item'" [settings]="singleDropdownSettings" [data]="storeGroupFilterList" [(ngModel)]="storeGroup" (onSelect)="onSelectStoreGroup($event)"
                                    (onDeSelect)="onDeSelectStoreGroup($event)">
                                </ng-multiselect-dropdown>
                            </div>
                            <em *ngIf="showSpinnerStoreButton" class="fa fa-spinner fa-pulse fa-1x fa-fw"></em>
                            <div id="storeGroup-select" *ngIf="loading" [ngClass]="displayFieldCssCreateSchedule('storeGroup')">
                                <ng-multiselect-dropdown appFocusOnClick class="searchicon-dropdown" [settings]="singleDropdownSettingsDisable" [data]="storeGroupFilterList" [(ngModel)]="storeGroup" (onSelect)="onSelectStoreGroup($event)"></ng-multiselect-dropdown>
                            </div>
                            <app-field-error-display [displayError]="isFieldValidCreateSchedule('storeGroup')" errorMsg="{{ constantService.ERROR_MESSAGE_FOR_REQUIRED }}">
                            </app-field-error-display>
                            <span class="pull-right" data-placement="top" data-toggle="tooltip" style="z-index: 1; position: relative; top: -28px; right: -30px" data-original-title="Click to refresh" data-animation="false">
                <em
                  class="plus fa fa-refresh fa-spin fa-2x"
                  style="font-size: 20px"
                  *ngIf="storeGroupList.length == 0"
                ></em>
              </span>
                            <div id="storeGroup-select" *ngIf="loading">
                                <ng-multiselect-dropdown appFocusOnClick [placeholder]="'Select Item'" class="searchicon-dropdown-dis" [disabled]="true" [settings]="singleDropdownSettingsDisable" [data]="storeGroupFilterList" [(ngModel)]="storeGroup" (onSelect)="onSelectStoreGroup($event)"
                                    (onDeSelect)="OnDeSelectStoreGroup($event)"></ng-multiselect-dropdown>
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <span style="padding-top: 10px" class="pull-right text-warning" *ngIf="store.length !== 0"><a
                  *ngIf="store[0].companyId"
                  href="https://secure.solve360.com/company/{{
                    store[0].companyId
                  }}"
                  target="_blank"
                  class="text-warning"
                  >s360 Store
                  <em class="fa fa-external-link" aria-hidden="true"></em
                ></a>
              </span>
                            <label class="col-form-label" style="font-weight: 700; padding-left: 50px">Store <span style="color: red">*</span>
              </label>
                            <div style="padding-left: 50px" id="store-select" *ngIf="!loading" [ngClass]="displayFieldCssCreateSchedule('store')">
                                <ng-multiselect-dropdown appFocusOnClick class="select-sp-report searchicon-dropdown filter-type-status multi-search" [placeholder]="'Select Item'" [settings]="singleDropdownSettings" [data]="storeFilterList" [(ngModel)]="store" (onSelect)="onSelectStore($event)"
                                    (onDeSelect)="OnDeSelectStore($event)" required></ng-multiselect-dropdown>
                            </div>
                            <div id="store-select" style="padding-left: 50px" *ngIf="loading" [ngClass]="displayFieldCssCreateSchedule('store')">
                                <ng-multiselect-dropdown appFocusOnClick class="searchicon-dropdown" [data]="storeFilterList" [(ngModel)]="store" [settings]="singleDropdownSettingsDisable" (onSelect)="onSelectStore($event)">
                                </ng-multiselect-dropdown>
                            </div>
                            <app-field-error-display [displayError]="isFieldValidCreateSchedule('store')" errorMsg="{{ constantService.ERROR_MESSAGE_FOR_REQUIRED }}">
                            </app-field-error-display>
                        </div>
                        <div class="col-lg-1">
                            <label class="col-form-label" style="font-weight: 700">DMS<span style="color: red">*</span></label
              >
              <div id="projectType-select">
                <ng-multiselect-dropdown appFocusOnClick
                  class="searchicon-dropdown-dms-dis-selected"
                  [placeholder]="'Select Item'"
                  [settings]="singleDropdownSettings"
                  [data]="versionFilterList"
                  [(ngModel)]="version"
                  (onSelect)="onSelectversion($event)"
                  (onDeSelect)="OnDeSelectversion($event)"
                  [disabled]="true"
                ></ng-multiselect-dropdown>
              </div>
            </div>
            <div class="col-lg-4">
              <label class="col-form-label" style="font-weight: 700"
                >File<span style="color: red">*</span></label
              >
              <label *ngIf="!fileLoader && storeGroup.length && store.length" style="display: flex; align-items: center;margin-top: 10px;
              float: right;">
                <input
                  type="checkbox"
                  (change)="onShowAllChange($event)"
                  [checked]="showAll"
                  style="margin-right: 5px;"
                />
                <span style="font-weight: 700;">Show All</span>
              </label>
                            <div id="projectType-select" *ngIf="!loading">
                                <ng-multiselect-dropdown appFocusOnClick class="select-sp-report searchicon-dropdown filter-type-status multi-search" [placeholder]="'Select File'" [settings]="singleDropdownSettings" [data]="storeFileList" [(ngModel)]="fileList" (onSelect)="onSelectFileversion($event)"
                                    (onDeSelect)="OnDeSelectFileversion($event)" [disabled]="fileLoader" [ngClass]="{
                    'searchicon-dropdown-dms-dis-selected': fileLoader
                  }"></ng-multiselect-dropdown>
                            </div>
                        </div>
                        <div class="col-lg-1">
                            <em *ngIf="fileLoader" class="plus fa fa-refresh fa-spin fa-2x" style="font-size: 20px; margin-top: 50px"></em>
                        </div>
                    </div>
                </div>
                <div class="" *ngIf="currentTab == 'make-tab'">
                    <div class="col-lg-12 row haltdivmessage" *ngIf="isMakeHalt">
                        <label class="font-weight-bold haltmessage">Unassigned Make Halt Detected
              </label
            >
          </div>
          <div class="col-lg-12 row" style="right: -12px">
            <div class="col-lg-3"><b>Store Name: </b>{{ (selectedStoreFilterList && selectedStoreFilterList[0] && selectedStoreFilterList[0].itemName) || companyListName }}</div>
            <div class="col-lg-2"><b>DMS: </b> {{ (selectedStoreFilterList && selectedStoreFilterList[0] && selectedStoreFilterList[0].dmsCode) || dmsList }}</div>
            <div class="col-lg-5"><b>State: </b>{{(selectedStoreFilterList && selectedStoreFilterList[0] && selectedStoreFilterList[0].stateCode) || stateList  }}</div>
            <div class="col-lg-4">
              <span style="float: left; font-weight: bold; margin-bottom: 10px"
                >Manufacturer
              </span>
            </div>
            <div class="col-lg-4">
              <span style="float: left; font-weight: bold; margin-bottom: 10px"
                >Makes
              </span>
            </div>
            <div class="col-lg-4">
              <span
                [ngClass]="{
                  'grid-highlight-yellow':
                    rowDataUnassignMake && rowDataUnassignMake.length > 0
                }"
                style="float: left; font-weight: bold; margin-bottom: 10px"
                >Unassigned Make
              </span>
            </div>
          </div>
          <div class="row aging-report-grid make-container">
            <div class="col-lg-4">
              <div></div>
              <button *ngIf="rowDataMakes.length > 20" (click)="toggleExpandCollapseMakes()" class="expand-collapse-btn-make" title="Click to expand/collapse">
                {{ isExpandedGrid3 ? '-' : '+' }} 
              </button>
              <ag-grid-angular
                style="width: 100%; height: {{(isExpandedGrid3 || rowDataMakes.length <= 20) ? '' : '400px' }}"
                class="ag-theme-balham"
                [suppressScrollOnNewData]="true"
                [animateRows]="true"
                [quickFilterText]=""
                [enableRangeSelection]="true"
                [suppressCellSelection]="true"
                [rowData]="rowDataMakes"
                [domLayout]="(rowDataMakes.length <= 20 || isExpandedGrid3) ? 'autoHeight' : 'normal'"
                [overlayLoadingTemplate]="overlayLoadingTemplate"
                [overlayNoRowsTemplate]="overlayNoRowsTemplate"
                [defaultColDef]="defaultColDef"
                [columnDefs]="columnDefsMakes"
                (firstDataRendered)="onFirstDataRenderedMakes($event)"
                [rowClassRules]="rowClassRules"
                [enableCellTextSelection]="true"
                (gridReady)="onGridReadyMakes($event)"
                [enableRangeSelection]="true"
                [suppressCellSelection]="true"
                [headerHeight]="50"
                [rowHeight]="35"
                [rowMultiSelectWithClick]="true"
                [context]="gridContext"
                (cellClicked)="gridCellClickedMakes($event)"
                [frameworkComponents]="frameworkComponents"
              >
              </ag-grid-angular>
            </div>
            <div class="col-lg-4">
              <div></div>
              <button *ngIf="rowDataModel.length > 20" (click)="toggleExpandCollapseModel()" class="expand-collapse-btn-make" title="Click to expand/collapse">
                {{ isExpandedGrid2 ? '-' : '+' }} 
              </button>
              <ag-grid-angular
                id="modelGrid"
                style="width: 100%; height:{{ (isExpandedGrid2 || rowDataModel.length <= 20) ? '' : '400px' }}"
                class="ag-theme-balham"
                [suppressScrollOnNewData]="true"
                [animateRows]="true"
                [quickFilterText]=""
                [enableRangeSelection]="true"
                [suppressCellSelection]="true"
                [rowData]="rowDataModel"
                [rowHeight]="35"
                [overlayLoadingTemplate]="overlayLoadingTemplate"
                [overlayNoRowsTemplate]="overlayNoRowsTemplate"
                [defaultColDef]="defaultColDef"
                [columnDefs]="columnDefsModel"
                (firstDataRendered)="onFirstDataRenderedModel($event)"
                [rowClassRules]="rowClassRules"
                [enableCellTextSelection]="true"
                (gridReady)="onGridReadyModel($event)"
                [enableRangeSelection]="true"
                [suppressCellSelection]="true"
                [rowMultiSelectWithClick]="true"
                [headerHeight]="50"
                [context]="gridContext"
                [domLayout]="(rowDataModel.length <= 20 ||isExpandedGrid2) ? 'autoHeight' : 'normal'"
                (filterChanged)="onModelFilterChanged()"
              >
              </ag-grid-angular>
            </div>
            <div class="col-lg-4">
              <div></div>
              <button *ngIf="rowDataUnassignMake.length > 20" (click)="toggleExpandCollapseUnassignMakes()" class="expand-collapse-btn-make" title="Click to expand/collapse">
                {{ isExpandedGrid4 ? '-' : '+' }} 
              </button>
              <ag-grid-angular
                id="modelGrid"
                style="width: 100%; height:  {{ (isExpandedGrid4 || rowDataUnassignMake.length <= 20) ? '' : '400px' }}"
                class="ag-theme-balham"
                [suppressScrollOnNewData]="true"
                [animateRows]="true"
                [quickFilterText]=""
                [enableRangeSelection]="true"
                [suppressCellSelection]="true"
                [rowData]="rowDataUnassignMake"
                [overlayLoadingTemplate]="overlayLoadingTemplate"
                [overlayNoRowsTemplate]="overlayNoRowsTemplate"
                [defaultColDef]="defaultColDef"
                [columnDefs]="columnDefsUnassignMake"
                (firstDataRendered)="onFirstDataRenderedUnassignMake($event)"
                [rowClassRules]="rowClassRules"
                [enableCellTextSelection]="true"
                [rowHeight]="40"
                (gridReady)="onGridReadyUnassignMake($event)"
                [enableRangeSelection]="true"
                [suppressCellSelection]="true"
                [rowMultiSelectWithClick]="true"
                [headerHeight]="50"
                [context]="gridContext"
                [domLayout]="(rowDataUnassignMake.length <= 20 ||isExpandedGrid4) ? 'autoHeight' : 'normal'"
              >
              </ag-grid-angular>
            </div>
          </div>
        </div>
        <div class="" *ngIf="currentTab == 'paytype-tab'">
          <div class="col-lg-12 row haltdivmessage" *ngIf="isPaytypeHalt">
            <label class="font-weight-bold haltpaymessage"
              >Pay-Type Halt Detected
              </label
            >
          </div>      

          <div class="col-lg-12 row" style="padding-bottom: 11px">
            <div class="col-lg-2"><b>Store Name: </b>{{ (selectedStoreFilterList && selectedStoreFilterList[0] && selectedStoreFilterList[0].itemName) || companyListName }}</div>
            <div class="col-lg-2"><b>DMS: </b> {{ (selectedStoreFilterList && selectedStoreFilterList[0] && selectedStoreFilterList[0].dmsCode) || dmsList }}</div>
            <div class="col-lg-3"><b>State: </b>{{ (selectedStoreFilterList && selectedStoreFilterList[0] && selectedStoreFilterList[0].stateCode) || stateList  }}</div>
            <div class="col-lg-3 d-flex align-items-center">
              <div class="dropdown" style="position: relative;">
                  <button class="btn btn-primary dropdown-toggle large-button" type="button" data-toggle="dropdown" [ngClass]="{ 'spacing-right': true, 'button-partstype': isPaytypeHalt, 'button-parts-default': !isPaytypeHalt }" (click)="getAllPayList()">Parts Pay-Type
            <span class="caret"></span></button>
                  <ul class="dropdown-menu parts" [ngClass]="{ 'margin-left-halt': isPaytypeHalt, 'margin-left-default': !isPaytypeHalt }">
                      <li (click)="displayPartPayTypeSummary()" class="typeFilter"><b>Pay-Type Summary</b></li>
                      <li *ngFor="let pay of combinedPaytypeLists" (click)="displayAllPartsPaytypes(pay.paytype,'parts')" class='typeFilter'><b>{{pay.paytype}}</b>
                      </li>

                  </ul>
              </div>
              <div class="dropdown">
                  <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown" [ngClass]="{ 'spacing-left': true,'button-labortype': isPaytypeHalt, 'button-labor-default': !isPaytypeHalt  }" (click)="getAllPayList()">Labor Pay-Type
          <span class="caret"></span></button>
                  <ul class="dropdown-menu labor">
                      <li (click)="displayLaborPayTypeSummary()" class='typeFilter'><b>Pay-Type Summary</b></li>
                      <li *ngFor="let pays of combinedPaytypeLists " (click)="displayAllLaborPaytypes(pays.paytype, 'labor')" class='typeFilter'><b>{{pays.paytype}}</b></li>

                  </ul>
              </div>
          </div>
            <div class="col-lg-1" *ngIf="!warrantyAllList?.length && !customAllList?.length">
              <div
                *ngIf="!warrantyAllList?.length && !customAllList?.length"
                class="full-screen-overlay"
              >
                <div class="loader"></div>
              </div>
             
            </div>
          </div>
          <br />
          <div class="col-lg-12">
            <div class="">
              <div
                class="col-lg-12"
                style="
                  border: 1px solid #d8d5d5;
                  padding: 7px 6px 36px 0px;
                  margin-right: 6px;
                "
              >
                <div class="col-lg-12 mt-3">
                  <div class="row">
                    <div class="col-lg-12">
                      <h5>
                        <strong style="color: hsl(206, 99%, 50%)"
                          >Customer Pay-Types</strong
                        >
                      </h5>
                    </div>
                  </div>
                  <div class="row">
                    <div
                      class=""
                      style="
                        width: 21.5% !important;
                        margin-right: 10px;
                        margin-left: 18px;
                      "
                    >
                      <label class="font-weight-bold">Available: </label>
                        <select name="cust-paytype-all" class="form-control selectcustom" [(ngModel)]="selectedCustomerPay" multiple [attr.size]="getSelectSize(custPaytypeAvailable)">
                        <option
                          title="{{ cPay.paytype }}"
                          *ngFor="let cPay of custPaytypeAvailable"
                          [value]="cPay.id"
                          [ngClass]="{
                            'grid-highlight-yellow':
                              cPay.is_highlight && !cPay.is_determined
                          }"
                        >
                          {{ cPay.paytype }}
                          <ng-container *ngIf="cPay.description && cPay.description !== '[NULL]' && cPay.description !== 'null'">
                            ({{ cPay.description }})
                        </ng-container>
                          
                        </option>
                      </select>
                    </div>
                    <div class="text-center" style="
                        width: 9% !important;
                        position: relative;
                        top: 24px;
                      ">
                        <div>
                            <button (click)="moveCPayRight()" style="margin-bottom: 10px" class="btn btn-primary pointer" [ngClass]="{
                            pointer: selectedCustomerPay.length > 0
                          }" [disabled]="selectedCustomerPay.length == 0">
                          Both Exclude <i class="fa fa-angle-right"></i>
                        </button>
                        </div>

                        <div>
                            <button (click)="moveCPayRightPartsOnly()" style="margin-bottom: 10px" class="btn btn-primary pointer" [ngClass]="{
                            pointer: selectedCustomerPay.length > 0
                          }" [disabled]="selectedCustomerPay.length == 0">
                          Parts Exclude <i class="fa fa-angle-right"></i>
                        </button>
                        </div>
                        <div>
                            <button (click)="moveCPayRightlaborOnly()" style="margin-bottom: 10px" class="btn btn-primary pointer" [ngClass]="{
                            pointer: selectedCustomerPay.length > 0
                          }" [disabled]="selectedCustomerPay.length == 0">
                          Labor Exclude <i class="fa fa-angle-right"></i>
                        </button>
                        </div>

                        <div>
                            <button (click)="moveCPayLeft()" class="btn btn-primary pointer width-50" [ngClass]="{
                            pointer:
                              deSelectedCustomerPay.length > 0 ||
                              LselectedCustomerPay.length > 0 ||
                              LdeSelectedCustomerPay.length > 0
                          }" [disabled]="
                            (deSelectedCustomerPay.length == 0 &&
                              LselectedCustomerPay.length == 0 &&
                              LdeSelectedCustomerPay.length == 0) ||
                            selectedCustomerPay.length
                          ">
                          <i class="fa fa-angle-left"> </i>
                        </button>
                        </div>
                    </div>
                    <div class="" style="width: 21.5% !important; margin-right: 10px">
                        <label class="font-weight-bold">Both Excluded:</label>
                        <select name="cust-paytype-selected" class="form-control selectcustom" [(ngModel)]="deSelectedCustomerPay" multiple [attr.size]="getSelectSize(custPaytypeSelected)" (click)="cPayDeSelection(1)">
                        <option
                          title="{{ cPaySel.paytype }}"
                          *ngFor="let cPaySel of custPaytypeSelected"
                          [value]="cPaySel.id"
                          [ngClass]="{
                            'grid-highlight-yellow':
                              cPaySel.is_highlight && !cPaySel.is_determined
                          }"
                        >
                          {{ cPaySel.paytype }}
                          <ng-container
                            *ngIf="
                              cPaySel.description &&
                              cPaySel.description !== '[NULL]' &&
                              cPaySel.description !== 'null'
                            "
                          >
                            ({{ cPaySel.description }})
                          </ng-container>
                        </option>
                      </select>
                    </div>
                    <div class="" style="width: 21.5% !important; margin-right: 10px">
                        <label class="font-weight-bold">Parts Excluded:</label>
                        <select name="cust-paytype-all" class="form-control selectcustom" [(ngModel)]="LselectedCustomerPay" multiple [attr.size]="
                          getSelectSize(custPaytypeSelectedOnlyParts)
                        " (click)="cPayDeSelection(2)">
                        <ng-container
                          *ngFor="let cPay of custPaytypeSelectedOnlyParts"
                        >
                          <option
                            [value]="cPay.id"
                            [ngClass]="{
                              'grid-highlight-yellow':
                                cPay.is_highlight && !cPay.is_determined
                            }"
                          >
                            {{ cPay.paytype }}
                            <ng-container
                              *ngIf="
                                cPay.description &&
                                cPay.description !== '[NULL]' &&
                                cPay.description !== 'null'
                              "
                            >
                              ({{ cPay.description }})
                            </ng-container>
                          </option>
                        </ng-container>
                      </select>
                    </div>
                    <div class="" style="width: 21.5% !important; margin-right: 10px">
                        <label class="font-weight-bold">Labor Excluded:</label>
                        <select name="cust-paytype-selected" class="form-control selectcustom" [(ngModel)]="LdeSelectedCustomerPay" multiple [attr.size]="
                          getSelectSize(custPaytypeSelectedOnlyLabor)
                        " (click)="cPayDeSelection(3)">
                        <ng-container
                          *ngFor="let cPaySel of custPaytypeSelectedOnlyLabor"
                        >
                          <option
                            [value]="cPaySel.id"
                            [ngClass]="{
                              'grid-highlight-yellow':
                                cPaySel.is_highlight && !cPaySel.is_determined
                            }"
                          >
                            {{ cPaySel.paytype }}
                            <ng-container
                              *ngIf="
                                cPaySel.description &&
                                cPaySel.description !== '[NULL]' &&
                                cPaySel.description !== 'null'
                              "
                            >
                              ({{ cPaySel.description }})
                            </ng-container>
                          </option>
                        </ng-container>
                      </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-lg-12" style="padding-top: 18px">
    <div class="">
        <div class="col-lg-12" style="
                  border: 1px solid #d8d5d5;
                  padding: 7px 6px 36px 0px;
                  margin-right: 6px;
                ">
            <div class="col-lg-12 mt-3">
                <div class="row">
                    <div class="col-md-12">
                        <h5>
                            <strong style="color: #0190fe">Warranty Pay-Types</strong
                        >
                      </h5>
                    </div>
                  </div>
                  <div class="row">
                    <div
                      class=""
                      style="
                        width: 21.5% !important;
                        margin-right: 10px;
                        margin-left: 18px;
                      "
                    >
                      <label class="font-weight-bold">Available:</label>
                      <select
                        name="warr-paytype-all"
                        class="form-control selectcustom"
                        [(ngModel)]="selectedWarrantyPay"
                        multiple
                        [attr.size]="getSelectSize(warrPaytypeAvailable)"
                      >
                        <option
                          title="{{ wPay.paytype }}"
                          *ngFor="let wPay of warrPaytypeAvailable"
                          [value]="wPay.id"
                          [ngClass]="{
                            'grid-highlight-yellow':
                              wPay.is_highlight && !wPay.is_determined
                          }"
                        >
                          {{ wPay.paytype }}
                          <ng-container
                            *ngIf="
                              
                              wPay.description &&
                              wPay.description !== '[NULL]' &&
                              wPay.description !== 'null'
                            "
                          >
                            ({{ wPay.description }})
                          </ng-container>
                        </option>
                      </select>
                    </div>
                    <div
                      class="text-center"
                      style="width: 9%; position: relative; top: 20px"
                    >
                      <div>
                        <button
                          (click)="moveWPayRight()"
                          class="btn btn-primary pointer"
                          [ngClass]="{
                            pointer: selectedWarrantyPay.length > 0
                          }"
                          style="margin-bottom: 10px"
                          [disabled]="selectedWarrantyPay.length == 0"
                        >
                          Both Exclude<i class="fa fa-angle-right"></i>
                        </button>
                      </div>

                      <div>
                        <button
                          (click)="moveWPayRightParts()"
                          class="btn btn-primary pointer"
                          [ngClass]="{
                            pointer: selectedWarrantyPay.length > 0
                          }"
                          style="margin-bottom: 10px"
                          [disabled]="selectedWarrantyPay.length == 0"
                        >
                          Parts Exclude<i class="fa fa-angle-right"></i>
                        </button>
                      </div>
                      <div>
                        <button
                          (click)="moveWPayRightLabor()"
                          class="btn btn-primary pointer"
                          [ngClass]="{
                            pointer: selectedWarrantyPay.length > 0
                          }"
                          style="margin-bottom: 10px"
                          [disabled]="selectedWarrantyPay.length == 0"
                        >
                          Labor Exclude<i class="fa fa-angle-right"></i>
                        </button>
                      </div>
                      <div>
                        <button
                          (click)="moveWPayLeft()"
                          class="btn btn-primary pointer width-50"
                          [ngClass]="{
                            pointer:
                              deSelectedWarrantyPay.length > 0 ||
                              LselectedWarrantyPay.length > 0 ||
                              LdeSelectedWarrantyPay.length > 0
                          }"
                          [disabled]="
                            (deSelectedWarrantyPay.length == 0 &&
                              LselectedWarrantyPay.length == 0 &&
                              LdeSelectedWarrantyPay.length == 0) ||
                            selectedWarrantyPay.length
                          "
                        >
                          <i class="fa fa-angle-left"></i>
                        </button>
                      </div>
                    </div>
                    <div
                      class=""
                      style="width: 21.5% !important; margin-right: 10px"
                    >
                      <label class="font-weight-bold">Both Excluded:</label>
                      <select
                        name="warr-paytype-selected"
                        class="form-control selectcustom"
                        [(ngModel)]="deSelectedWarrantyPay"
                        multiple
                        [attr.size]="getSelectSize(warrPaytypeSelected)"
                        (click)="warrantyDeSelection(1)"
                      >
                        <ng-container
                          *ngFor="let wPaySel of warrPaytypeSelected"
                        >
                          <option
                            [value]="wPaySel.id"
                            [ngClass]="{
                              'grid-highlight-yellow':
                                wPaySel.is_highlight && !wPaySel.is_determined
                            }"
                          >
                            {{ wPaySel.paytype }}
                            <ng-container
                              *ngIf="
                                wPaySel.description &&
                                wPaySel.description &&
                                wPaySel.description !== '[NULL]' &&
                                wPaySel.description !== 'null'
                              "
                            >
                              ({{ wPaySel.description }})
                            </ng-container>
                          </option>
                        </ng-container>
                      </select>
                    </div>
                    <div
                      class=""
                      style="width: 21.5% !important; margin-right: 10px"
                    >
                      <label class="font-weight-bold">Parts Excluded:</label>
                      <select
                        name="warr-paytype-all"
                        class="form-control selectcustom"
                        [(ngModel)]="LselectedWarrantyPay"
                        multiple
                        [attr.size]="getSelectSize(warrPaytypeSelectedParts)"
                        (click)="warrantyDeSelection(2)"
                      >
                        <ng-container
                          *ngFor="let wPay of warrPaytypeSelectedParts"
                        >
                          <option
                            [value]="wPay.id"
                            [ngClass]="{
                              'grid-highlight-yellow':
                                wPay.is_highlight && !wPay.is_determined
                            }"
                          >
                            {{ wPay.paytype }}
                            <ng-container
                              *ngIf="
                                wPay.description &&
                                wPay.description &&
                                wPay.description !== '[NULL]' &&
                                wPay.description !== 'null'
                              "
                            >
                              ({{ wPay.description }})
                            </ng-container>
                          </option>
                        </ng-container>
                      </select>
                    </div>
                    <div
                      class=""
                      style="width: 21.5% !important; margin-right: 10px"
                    >
                      <label class="font-weight-bold">Labor Excluded:</label>
                      <select
                        name="warr-paytype-selected"
                        class="form-control selectcustom"
                        [(ngModel)]="LdeSelectedWarrantyPay"
                        multiple
                        [attr.size]="getSelectSize(warrPaytypeSelectedLabor)"
                        (click)="warrantyDeSelection(3)"
                      >
                        <ng-container
                          *ngFor="let wPaySel of warrPaytypeSelectedLabor"
                        >
                          <option
                            [value]="wPaySel.id"
                            [ngClass]="{
                              'grid-highlight-yellow':
                                wPaySel.is_highlight && !wPaySel.is_determined
                            }"
                          >
                            {{ wPaySel.paytype }}
                            <ng-container
                              *ngIf="
                                wPaySel.description &&
                             
                                wPaySel.description !== '[NULL]' &&
                                wPaySel.description !== 'null'
                              "
                            >
                              ({{ wPaySel.description }})
                            </ng-container>
                          </option>
                        </ng-container>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="" *ngIf="currentTab == 'department-tab'">
          <div class="col-lg-12 row haltdivmessage" *ngIf="isDeptHalt">
            <label class="font-weight-bold haltdeptmessage"
              >Department Halt Detected</label
            >
          </div>
          <div class="col-lg-12 row" style="right: -2px">
            <div class="col-lg-2"><b>Store Name: </b>{{ (selectedStoreFilterList && selectedStoreFilterList[0] && selectedStoreFilterList[0].itemName) || companyListName }}</div>
            <div class="col-lg-2"><b>DMS: </b> {{ (selectedStoreFilterList && selectedStoreFilterList[0] && selectedStoreFilterList[0].dmsCode) || dmsList }}</div>
            <div class="col-lg-3"><b>State: </b>{{ (selectedStoreFilterList && selectedStoreFilterList[0] && selectedStoreFilterList[0].stateCode) || stateList  }}</div>
            <br /><br />
            <div class="col-lg-6">
              <span style="float: left; font-weight: bold; margin-bottom: 10px"
                >Department
              </span>
            </div>
          </div>
          <div class="row aging-report-grid make-container">
            <div class="col-lg-6">
              <div></div>
              <ag-grid-angular
                style="width: 100%; height: ''"
                class="ag-theme-balham"
                [suppressScrollOnNewData]="true"
                [pagination]="false"
                [animateRows]="true"
                [quickFilterText]=""
                [enableRangeSelection]="true"
                [suppressCellSelection]="true"
                [rowData]="rowDataDept"
                [overlayLoadingTemplate]="overlayLoadingTemplate"
                [overlayNoRowsTemplate]="overlayNoRowsDeptTemplate"
                [defaultColDef]="defaultColDef"
                [columnDefs]="columnDefsDept"
                [rowHeight]="35"
                (firstDataRendered)="onFirstDataRenderedPRate($event)"
                [rowClassRules]="rowClassRules"
                [enableCellTextSelection]="true"
                (gridReady)="onGridReadyPRate($event)"
                [enableRangeSelection]="true"
                [suppressCellSelection]="true"
                [rowMultiSelectWithClick]="true"
                [context]="gridContext"
                [domLayout]="'autoHeight'"
                (cellClicked)="gridCellClicked($event)"
                [frameworkComponents]="frameworkDeptComponents"
              >
                >
              </ag-grid-angular>
            </div>
            <div class="col-lg-4">
              <ag-grid-angular
                style="width: 100%; height: ''"
                class="ag-theme-balham"
                [suppressScrollOnNewData]="true"
                [pagination]="false"
                [animateRows]="true"
                [quickFilterText]=""
                [enableRangeSelection]="true"
                [suppressCellSelection]="true"
                [rowData]="rowDataDesc"
                [rowHeight]="35"
                [overlayLoadingTemplate]="overlayLoadingTemplate"
                [overlayNoRowsTemplate]="overlayNoRowsDeptTemplate"
                [defaultColDef]="defaultColDef"
                [columnDefs]="columnDefsDeptDesc"
                (firstDataRendered)="onFirstDataRenderedDept($event)"
                [rowClassRules]="rowClassRules"
                [enableCellTextSelection]="true"
                (gridReady)="onGridReadyDept($event)"
                [enableRangeSelection]="true"
                [suppressCellSelection]="true"
                [rowMultiSelectWithClick]="true"
                [context]="gridContext"
                [domLayout]="'autoHeight'"
              >
              </ag-grid-angular>
            </div>
          </div>
        </div>
        <div class="" *ngIf="currentTab == 'invoice-tab'">
          <div *ngIf="showOverlay" class="full-screen-overlay">
            <div class="loader"></div>
          </div>
          <div class="col-lg-12 row haltdivmessage" *ngIf="invSeqHalt">
            <label class="font-weight-bold haltmessage"
              >Invoice Sequence Halt Detected
              </label
            >
          </div>
          <div class="col-lg-12 row" style="padding-bottom: 5px">
            <div class="col-lg-2"><b>Store Name: </b>{{ (selectedStoreFilterList && selectedStoreFilterList[0] && selectedStoreFilterList[0].itemName) || companyListName}}</div>
            <div class="col-lg-2"><b>DMS: </b> {{ (selectedStoreFilterList && selectedStoreFilterList[0] && selectedStoreFilterList[0].dmsCode) || dmsList }}</div>
            <div class="col-lg-3"><b>State: </b>{{ (selectedStoreFilterList && selectedStoreFilterList[0] && selectedStoreFilterList[0].stateCode) || stateList }}</div>
          </div>
          <div class="col-lg-12 row import-grid" style="margin-top: 20px">
            <div class="col-lg-9">
              <button *ngIf="rowDataInvoice?.length > 5" (click)="toggleExpandCollapseInvoice()" class="expand-collapse-btn-invoice-2" title="Click to expand/collapse">
                {{ isExpandedGrid ? '-' : '+' }} 
              </button>
              <ag-grid-angular
                id="grid1"
                style="width: 100%; height: {{ (isExpandedGrid  || (rowDataInvoice && rowDataInvoice.length <= 5) ) ? '' : '280px' }}"
                class="ag-theme-balham"
                [columnDefs]="columnDefs1"
                [defaultColDef]="defaultColDef"
                [domLayout]="((rowDataInvoice && rowDataInvoice.length <= 5) || isExpandedGrid) ? 'autoHeight' :'normal'"
                [overlayLoadingTemplate]="overlayLoadingTemplate"
                [overlayNoRowsTemplate]="overlayNoRowsRoSeqTemplate"
                [rowDragManaged]="true"
                [animateRows]="true"
                [rowData]="rowDataInvoice"
                [rowHeight]="35"
                [gridOptions]="gridOptions1"
                [rowSelection]="rowSelection"
                [headerHeight]="45"
                (gridReady)="onGridReady1($event)"
                (firstDataRendered)="onFirstDataRenderedInvoice($event)"
                [frameworkComponents]="frameworkInvoiceComponents"
              >
              </ag-grid-angular>
              <button *ngIf="rowDataRoInvoice?.length > 5" (click)="toggleExpandCollapseRoInvoice()" class="expand-collapse-btn-invoice" title="Click to expand/collapse">
                {{ isExpandedGrid1 ? '-' : '+' }} 
              </button>
              <ag-grid-angular
                id="grid2"
                style="width: 100%; height: {{ (isExpandedGrid1 || (rowDataRoInvoice && rowDataRoInvoice.length <= 5))  ? '' : '280px' }}; margin-top: 20px"
                class="ag-theme-balham"
                [columnDefs]="columnDefs2"
                [defaultColDef]="defaultInvoiceColDef"
                [overlayLoadingTemplate]="overlayLoadingTemplate"
                [overlayNoRowsTemplate]="overlayNoRowsRoSeqTemplate"
                [rowDragManaged]="true"
                [animateRows]="true"
                [rowData]="rowDataRoInvoice"
                [headerHeight]="45"
                [rowHeight]="35"
                [domLayout]="((rowDataRoInvoice && rowDataRoInvoice.length <= 5) || isExpandedGrid1) ? 'autoHeight' : 'normal'"
                (gridReady)="onGridReady2($event)"
                (firstDataRendered)="onFirstDataRenderedRoInvoice($event)"
              >
              </ag-grid-angular>
            </div>
            <div class="col-lg-3" style="top:-20px">
              <input style="top:15px"
                type="search"
                #searchinvoice
                class="input-sm form-control ag-grid-custom-search-invoice"
                placeholder="Search.."
                (keyup)="searchInvoiceData(searchinvoice.value)"
              />
              <button *ngIf="rowData3?.length > 5" (click)="toggleExpandCollapseRoInvoiceOrphan()" class="expand-collapse-btn-invoice" title="Click to expand/collapse">
                {{ isExpandedGrid5 ? '-' : '+' }} 
              </button>
              
              <ag-grid-angular
                id="grid3"
                style="width: 100%; height: {{ (isExpandedGrid5 || (rowData3 && rowData3.length <= 5))  ? '' : '280px' }}; margin-top: 20px"
                class="ag-theme-balham"
                [columnDefs]="columnDefs3"
                [defaultColDef]="defaultColDef"
                [rowDragManaged]="true"
                [overlayLoadingTemplate]="overlayLoadingTemplate"
                [overlayNoRowsTemplate]="overlayNoRowsSeqTemplate"
                [animateRows]="true"
                [rowData]="rowData3"
                [headerHeight]="45"
                [rowHeight]="35"
                [domLayout]="((rowData3 && rowData3.length <= 5) || isExpandedGrid5) ? 'autoHeight' : 'normal'"
                (gridReady)="onGridReady3($event)"
                (firstDataRendered)="onFirstDataRenderedSumInvoice($event)"
                [pagination]="true"
                [paginationPageSize]="paginationPageSize"
              >
              </ag-grid-angular>
            </div>
          </div>
          <div class="col-lg-12 row" style="margin-top: 20px"></div>
        </div>
        <div
          class="col-lg-12 row"
          *ngIf="currentTab == 'finish-tab'"
          style=" display: flex;
          justify-content: center;
          align-items: center;
            "
        >
          <div class="container-box">
            <p class="content">
              The store is configured and prepared for import. Clicking the
              <b>"Finish"</b> button will initiate the import process. Please
              click the <b>"Finish"</b> button to proceed or return to the
              previous pages to make any necessary edits.
            </p>
            <button
              class="btn btn-primary finishbuttons"
              (click)="addDumpData()"
              [disabled]="showDisableFinishButton || !storeFileList"
            >
              Finish!
              <em
                *ngIf="showSpinnerStoreButton || !storeFileList"
                class="fa fa-spinner fa-pulse fa-1x fa-fw"
              ></em>
            </button>
          </div>
        </div>
      </div>
      <div class="next-prev-button-sec">
        <button
          *ngIf="
            (base64QueryString && currentTab !== 'make-tab') ||
            (!base64QueryString && currentTab !== 'import-tab')
          "
          class="btn btn-primary import-previous"
          [disabled]="showDisableFinishButton"
          (click)="onPreviousClick()"
        >
          Previous
        </button>
        <button
          *ngIf="currentTab !== 'finish-tab'"
          class="btn btn-primary import-next"
          (click)="onNextClick()"
        >
          Next
        </button>
      </div>
    </div>
  </div>
</div>

<div
  class="modal fade"
  id="haltReasonModal1"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel1"
  aria-hidden="true"
  data-backdrop="static"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width: 700px">
      <div class="modal-header">
        <h5 class="modal-title" id="requestModalLabel">
          Add {{ makeFetchList }} as a Make
        </h5>
        <button
          type="button"
          class="close"
          (click)="closeHaltReasonModal1()"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <ng-container>
          <div class="col-md-12">
            <div #dropdownContainer class="makecustom" *ngIf="!loading" [ngClass]="">
              <!-- Checkbox Added Here -->
              <div class="form-check mb-3 ml-1">
                <input 
                  type="checkbox" 
                  id="selectAllCheckbox" 
                  class="form-check-input" 
                  [(ngModel)]="isMakeAsGlobal" 
                />
                <label *ngIf="isAdmin; else notAdmin" for="selectAllCheckbox" class="form-check-label">
                  Add as Global
                </label>
                
                <ng-template #notAdmin>
                  <label for="selectAllCheckbox" class="form-check-label">
                    Send to Admin Approval
                  </label>
                </ng-template>
                
 
              </div>
      
              <!-- Multi-select Dropdown -->
              <ng-multiselect-dropdown
                appFocusOnClick
                #dropdownRef
                [placeholder]="'Select Item'"
                class="searchicon-dropdown rounded-selection rouded-sel-label multi-search filter-type-status"
                [settings]="singleDropdownSettings"
                [data]="importMakeGroupFilterList"
                [(ngModel)]="makeDataGroup"
              ></ng-multiselect-dropdown>
            </div>
          </div>
        </ng-container>
        
        <span style="color: red" *ngIf="showValidationErr">*Please select an Item</span>
      
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            (click)="closeHaltReasonModal1()"
          >
            Cancel
          </button>
          <button
            class="btn btn-primary"
            (click)="addMakeList(makeDataGroup, makeFetchList)"
            [ngClass]="{
              'disabled-pointer': !makeDataGroup || makeDataGroup.length == 0
            }"
            [disabled]="!makeDataGroup || makeDataGroup.length == 0"
          >
            Add
            <em
              *ngIf="showSpinnerStoreButton"
              class="fa fa-spinner fa-pulse fa-1x fa-fw"
            ></em>
          </button>
        </div>
      </div>
      
    </div>
  </div>
</div>
<div
  class="modal fade"
  id="haltReasonModal2"
  tabindex="-1"
  role="dialog"
  aria-labelledby="myModalLabel2"
  aria-hidden="true"
  data-backdrop="static"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width: 700px">
      <div class="modal-header">
        <h5 class="modal-title" id="requestModalLabel">
          Assign {{ makeFetchList }} as an Alias
        </h5>
        <button
          type="button"
          class="close"
          (click)="closeHaltReasonModal2()"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <ng-container>
          <div class="form-check mb-3 ml-3">
            <input 
              type="checkbox" 
              id="selectAllCheckbox" 
              class="form-check-input" 
              [(ngModel)]="isManufacturerAsGlobal" 
            />
            <label for="selectAllCheckbox" class="form-check-label">Save as Global</label>
          </div>
          
          <div #dropdownContainer class="col-md-12 makecustom">
           <ng-multiselect-dropdown appFocusOnClick
              [placeholder]="'Select Item'"
              class="searchicon-dropdown rounded-selection rouded-sel-label multi-search filter-type-status"
              [settings]="singleDropdownSettings"
              [data]="importModelGroupFilterList"
              [(ngModel)]="modelDataGroup"
            ></ng-multiselect-dropdown>
          </div>
        </ng-container>
        <span style="color: red" *ngIf="showValidationErr"
          >*Please select an Item</span
        >
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            (click)="closeHaltReasonModal2()"
          >
            Cancel
          </button>

          <button
            class="btn btn-primary"
            (click)="addOtherMake(makeFetchList, modelDataGroup)"
            [ngClass]="{
              'disabled-pointer': !modelDataGroup || modelDataGroup.length == 0
            }"
            [disabled]="!modelDataGroup || modelDataGroup.length == 0"
          >
            Add
            <em
              *ngIf="showSpinnerStoreButton"
              class="fa fa-spinner fa-pulse fa-1x fa-fw"
            ></em>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="partPaytype" tabindex="-1" role="dialog" aria-labelledby="myModalLabel3" aria-hidden="true" data-backdrop="static">
  <div class="modal-dialog" role="document"  style="max-width: 680px">
      <div class="modal-content" style="width: 700px;">
          <div class="modal-header">
              <h5 class="modal-title" id="requestModalLabel">Parts in Pay-Type {{payTypeList}}</h5>
              <button type="button" class="close" (click)="closePartPaytypeModal()" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button> </div>
          <div class="modal-body">
              <ag-grid-angular style="width: 100%; height: 500px" class="ag-theme-balham" [suppressScrollOnNewData]="true" [animateRows]="true" [quickFilterText]="" [enableRangeSelection]="true" [suppressCellSelection]="true" [rowData]="rowDataPayList" [domLayout]="'normal'"
                  [overlayLoadingTemplate]="overlayLoadingTemplate" [overlayNoRowsTemplate]="overlayNoRowsTemplate" [defaultColDef]="defaultColDef" [columnDefs]="columnDefsPayList" [rowClassRules]="rowClassRules" [enableCellTextSelection]="true" [enableRangeSelection]="true"
                  [suppressCellSelection]="true" [headerHeight]="50" [rowHeight]="35" [rowMultiSelectWithClick]="true" [context]="gridContext" [frameworkComponents]="frameworkComponents">
              </ag-grid-angular>
          </div>
      </div>
  </div>
</div>
<div class="modal fade" id="laborPaytype" tabindex="-1" role="dialog" aria-labelledby="myModalLabel4" aria-hidden="true" data-backdrop="static">
  <div class="modal-dialog" role="document"  style="max-width: 680px">
      <div class="modal-content" style="width: 700px;">
          <div class="modal-header">
              <h5 class="modal-title" id="requestModalLabel">Labor in Pay-Type {{payTypeList}}</h5>
              <button type="button" class="close" (click)="closeLaborPaytypeModal()" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button> </div>
          <div class="modal-body">
              <ag-grid-angular style="width: 100%; height: 500px" class="ag-theme-balham" [suppressScrollOnNewData]="true" [animateRows]="true" [quickFilterText]="" [enableRangeSelection]="true" [suppressCellSelection]="true" [rowData]="rowDataLaborPayList" [domLayout]="'normal'"
                  [overlayLoadingTemplate]="overlayLoadingTemplate" [overlayNoRowsTemplate]="overlayNoRowsTemplate" [defaultColDef]="defaultColDef" [columnDefs]="columnDefsLaborPayList" [rowClassRules]="rowClassRules" [enableCellTextSelection]="true"
                  [enableRangeSelection]="true" [suppressCellSelection]="true" [headerHeight]="50" [rowHeight]="35" [rowMultiSelectWithClick]="true" [context]="gridContext" [frameworkComponents]="frameworkComponents">
              </ag-grid-angular>
          </div>
      </div>
  </div>
</div>
<div class="modal fade" id="partSummary" tabindex="-1" role="dialog" aria-labelledby="myModalLabel3" aria-hidden="true" data-backdrop="static">
  <div class="modal-dialog" role="document"  style="max-width: 680px">
      <div class="modal-content" style="width: 700px;">
          <div class="modal-header">
              <h5 class="modal-title" id="requestModalLabel">Pay-Type Summary</h5>
              <button type="button" class="close" (click)="closePartSummaryModal()" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button> </div>
          <div class="modal-body">
              <ag-grid-angular style="width: 100%; height: ''" class="ag-theme-balham" [suppressScrollOnNewData]="true" [animateRows]="true" [quickFilterText]="" [enableRangeSelection]="true" [suppressCellSelection]="true" [rowData]="rowDataPartSummary" [domLayout]="'autoHeight'"
                  [overlayLoadingTemplate]="overlayLoadingTemplate" [overlayNoRowsTemplate]="overlayNoRowsTemplate" [defaultColDef]="defaultColDef" [columnDefs]="columnDefsPartSummary" [rowClassRules]="rowClassRules" [enableCellTextSelection]="true"
                  [enableRangeSelection]="true" [suppressCellSelection]="true" [headerHeight]="50" [rowHeight]="35" [rowMultiSelectWithClick]="true" [context]="gridContext" [frameworkComponents]="frameworkComponents">
              </ag-grid-angular>
          </div>
      </div>
  </div>
</div>
<div class="modal fade" id="laborSummary" tabindex="-1" role="dialog" aria-labelledby="myModalLabel3" aria-hidden="true" data-backdrop="static">
  <div class="modal-dialog" role="document"  style="max-width: 680px">
      <div class="modal-content" style="width: 700px;">
          <div class="modal-header">
              <h5 class="modal-title" id="requestModalLabel">Pay-Type Summary</h5>
              <button type="button" class="close" (click)="closeLaborSummaryModal()" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button> </div>
          <div class="modal-body">
              <ag-grid-angular style="width: 100%; height: ''" class="ag-theme-balham" [suppressScrollOnNewData]="true" [animateRows]="true" [quickFilterText]="" [enableRangeSelection]="true" [suppressCellSelection]="true" [rowData]="rowDataLaborSummary" [domLayout]="'autoHeight'"
                  [overlayLoadingTemplate]="overlayLoadingTemplate" [overlayNoRowsTemplate]="overlayNoRowsTemplate" [defaultColDef]="defaultColDef" [columnDefs]="columnDefsLaborSummary" [rowClassRules]="rowClassRules" [enableCellTextSelection]="true"
                  [enableRangeSelection]="true" [suppressCellSelection]="true" [headerHeight]="50" [rowHeight]="35" [rowMultiSelectWithClick]="true" [context]="gridContext" [frameworkComponents]="frameworkComponents">
              </ag-grid-angular>
          </div>
      </div>
  </div>
</div>

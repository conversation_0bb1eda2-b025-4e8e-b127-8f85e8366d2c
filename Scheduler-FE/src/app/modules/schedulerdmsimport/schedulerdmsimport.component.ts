import {
  <PERSON><PERSON><PERSON>,
  <PERSON>Child,
  ChangeDetector<PERSON>ef,
  OnInit,
  ElementRef,
  Renderer2,
} from "@angular/core";
import { Subscription, of } from "rxjs";
import * as moment from "moment-timezone";
import { <PERSON>umn<PERSON>pi, GridApi, GridOptions } from "ag-grid-community";
import { Router, ActivatedRoute } from "@angular/router";
import { CommonService } from "../../structure/services/common.service";
import { Apollo, gql } from "apollo-angular";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { ConstantService } from "../../structure/constants/constant.service";
import { DmFormGroupService } from "src/app/structure/services/dm.formgroup.services";
import { ToastrService } from "ngx-toastr";
import { CheckboxCellComponent_ } from "./checkbox-cell.component";
import { CheckboxCellComponentModel } from "./checkbox-cellmodel.component";
import { CheckboxCellDeptComponent } from "./checkbox-cell-dept.component";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SubscriptionConstantService } from "./../../structure/constants/subscription.constant.service";
import { environment } from "src/environments/environment";
import { HttpHeaders, HttpClient } from "@angular/common/http";
import { SharedModule } from "../shared/shared.module";
declare var NProgress: any;
declare var swal: any;
declare var $: any;
const allManufacturers = gql`
  query {
    allManufacturers {
      edges {
        node {
          manufacturer
          makeList
        }
      }
    }
  }
`;
const storePayType = gql`
  query getStorePaytype($companyId: BigInt) {
    getStorePaytype(inCompanyId: $companyId)
  }
`;
const getDepartmentDetails = gql`
  query getDepartmentDetails($inDms: String) {
    getDepartmentDetails(inDms: $inDms)
  }
`;
const getStoreCompanyDetails = gql`
  query getCompanyDetails($inCompanyId: BigInt) {
    getCompanyDetails(inCompanyId: $inCompanyId)
  }
`;
const schedulerPreImportStatusUpdation = gql`
  mutation schedulerPreImportStatusUpdation(
    $inSchedulerId: String
    $inCompanyId: BigInt
    $inResumedOn: Datetime!
    $inResumedBy: String
  ) {
    schedulerPreImportStatusUpdation(
      input: {
        inSchedulerId: $inSchedulerId
        inCompanyId: $inCompanyId
        inResumedOn: $inResumedOn
        inResumedBy: $inResumedBy
      }
    ) {
      json
    }
  }
`;
const storePaytypeDetailsUpdation = gql`
  mutation storePaytypeDetailsUpdation($payTypeDet: JSON) {
    storePaytypeDetailsUpdation(input: { payTypeDet: $payTypeDet }) {
      json
    }
  }
`;
const departmentDetailsInfo = gql`
  mutation departmentDetailsInfo($depDet: JSON) {
    departmentDetailsInfo(input: { depDet: $depDet }) {
      json
    }
  }
`;
@Component({
  selector: "app-scheduler-dms-import",
  templateUrl: "./schedulerdmsimport.component.html",
  styleUrls: ["./schedulerdmsimport.component.css"],
  standalone: true,
  imports: [
    // FormsModule, // Add this to enable ngModel support
    SharedModule,
  ],
})
export class SchedulerDmsImportComponent implements OnInit {
  @ViewChild("selectElement") selectElement!: ElementRef;
  @ViewChild(CheckboxCellComponent_)
  checkboxCellInstance!: CheckboxCellComponent_;
  @ViewChild(CheckboxCellComponentModel)
  checkboxCellModelInstance!: CheckboxCellComponentModel;
  @ViewChild(CheckboxCellDeptComponent)
  checkboxCellDeptInstance!: CheckboxCellDeptComponent;
  isStepDisabled = true;
  public rowDataInvoice: any;
  public columnDefs1: any;
  public gridOptions1: GridOptions;
  public rowSelection: any;
  public defaultColDef: any;
  public overlayLoadingTemplate: any;
  public overlayNoRowsTemplate: any;
  public overlayNoRowsSeqTemplate: any;
  public overlayNoRowsRoSeqTemplate: any;
  public gridApi1: any;
  public gridColumnApi1: any;
  selectedStepIndex: any;
  stepIndexes = [0, 1, 2, 3, 4, 5, 6];
  public currentPageName = null;
  private subscription: any;
  private subscription$ = new Subject();
  private subscriptionList: any = [];
  public rowDataDept: any;
  public columnDefsDept: any;
  public rowDataMakes: any[] = [];
  public columnDefsMakes: any;
  public custPaytypeAll: any = [];
  public selectedCustomerPay: any = [];
  public deSelectedCustomerPay: any = [];
  public custPaytypeAvailable: any;
  public custPaytypeSelected: any[] = [];
  public custPaytypeSelectedOnlyParts: any = [];
  public custPaytypeSelectedOnlyLabor: any = [];
  public selectedCTypeLeftBox: any = [];
  public paginationPageSize = 100;
  private selectedCTypeLeftBoxPartsOnly: any = [];
  private selectedCTypeLeftBoxLaborOnly: any = [];
  private warrPaytypeAll: any = [];
  private intPaytypeAll = [];
  public selectedWarrantyPay: any = [];
  public deSelectedWarrantyPay: any = [];
  public warrPaytypeAvailable: any;
  public warrPaytypeSelected: any = [];
  public warrPaytypeSelectedParts: any = [];
  public warrPaytypeSelectedLabor: any = [];
  public selectedWTypeLeftBox: any = [];
  public selectedWTypeLeftBoxParts: any = [];
  public selectedWTypeLeftBoxLabor: any = [];
  public showSpinner = false;
  public stepCounter = 0;
  /** Labor listing */
  public LcustPaytypeAll: any = [];
  public LselectedCustomerPay: any[] = [];
  public LdeSelectedCustomerPay: any[] = [];
  public LcustPaytypeAvailable = [];
  public LcustPaytypeSelected = [];
  public LselectedCTypeLeftBox = [];
  public LwarrPaytypeAll: any = [];
  public LintPaytypeAll: any = [];
  public LselectedWarrantyPay: any[] = [];
  public LdeSelectedWarrantyPay: any[] = [];
  public singleDropdownSettings: any;
  public singleDropdownSettingsDisable: any;
  public multiDropdownSettings: any;
  public multiDropdownSettingsDisable: any;
  public gridOptions2: GridOptions;
  public gridApi2: any;
  public gridColumnApi2: any;
  public rowDataRoInvoice: any;
  public columnDefs2: any;
  public gridOptions3: GridOptions;
  public gridApi3: any;
  public gridColumnApi3: any;
  public rowData3: any = [];
  public columnDefs3: any;
  public gridApiPRate: any;
  public gridColumnApiPRate: any;
  public rowClassRules: any;
  public domLayout: any;
  public gridContext: any;
  private noteHeading = "";
  private allNotes = "";
  public gridApiMakes: any;
  public gridColumnApiMakes: any;
  public rowDataModel: any = [];
  public columnDefsModel: any;
  public gridApiModel!: GridApi;
  public gridColumnApiModel!: ColumnApi;
  public storeGroup: any[] = [];
  loading: any = false;
  public importMakeGroupFilterList: any[] = [];
  public importModelGroupFilterList: any[] = [];
  public storeGroupFilterList: any[] = [];
  public storeGroupList: any[] = [];
  public store: any[] = [];
  public companyId: any = null;
  private selectedGroup: any = null;
  private selectedStore: any = null;
  public showSpinnerStoreButton = false;
  public version: any;
  public fileList: any[] = [];
  public versionFilterList: any[] = [];
  public storeFilterList: any[] = [];
  public selectedStoreFilterList: any[] = [];
  public columnDefsUnassignMake: any;
  public rowDataUnassignMake: any = [];
  public storeFileList: any[] = [];
  public allManufacturersList: any[] = [];
  private unassignedData: any;
  private companyImportId: any;
  private schedulerImportId: any;
  private manufacturer: any;
  public listener: any;
  public makeDataGroup: any;
  public modelDataGroup: any;
  private allMakeManufacturersList: any;
  public allMakeList:any;
  private resMakeListData: any;
  private resPayListData: any;
  public allPaytypeList: any;
  private allPaytypeWarrantyList: any;
  private allPaytypeCustomerList: any = [];
  public customAllList: any = [];
  public warrantyAllList: any = [];
  public gridApiPay!: GridApi;
  public gridColumnApiPayl!: ColumnApi;
  public makeFetchList: any;
  public allDepartmentList: any;
  public deptList: any[] = [];
  public rowUnassignedDataDept: any;
  public frameworkComponents: any;
  public frameworkInvoiceComponents: any;
  public frameworkDeptComponents: any;
  public base64QueryString: any;
  private gridApiUnassignMake: any;
  private gridColumnApiUnassignMake: any;
  private checkedListItems: any;
  public defaultColDefPayType: any;
  private storeList: any[] = [];
  private jobGroupList: any[] = [];
  public allStoreList: any[] = [];
  public invoiceData: any;
  public companyFileId: any = null;
  public schedulerFileId: any = null;
  public schedulerCommonId: any;
  public companyCommonId: any;
  public createSchedule!: FormGroup;
  private storeFlag = false;
  public loadAllStore = false;
  private storeLoading: any = false;
  private storeGroupFlag = false;
  private companyIds: any;
  public stepDisabledStatus: any;
  public dmsList: any;
  public companyListName: any;
  public statusMessage: any;
  public isAuthenticated = true;
  public fileName: any;
  public stateList: any;
  public flagNextStep = false;
  private stepChangedSubscription: Subscription | null = null;
  public selectedManufacturerList: any = [];
  public rowDataDesc: any = [];
  public columnDefsDeptDesc: any;
  public selectedDeptList: any;
  public showValidationErr: any = false;
  public tabVisit: any = {
    import: false,
    make: false,
    paytype: false,
    dept: false,
    iseq: false,
    finish: false,
  };
  public showDisableFinishButton: any;
  public localDataStore: any;
  public overlayNoRowsDeptTemplate: any;
  public fileLoader: boolean = false;
  private currentMake: any;
  private newMake: any;
  public showOverlay: any = false;
  public invSeqHalt = false;
  public isPaytypeHalt = false;
  public isMakeHalt = false;
  public isDeptHalt = false;
  public departmentFetchData: any;
  public noChangedDepartments = false;
  private custCombinePayList: any;
  private warrCombinePayList: any;
  private stayOnCurrentTab: any;
  currentTab = "import-tab"; // Start with 'Make' as active tab
  isImportDisabled = false;
  isMakeDisabled: boolean = true;
  isPaytypeDisabled: boolean = true;
  isDepartmentDisabled: boolean = true;
  isInvoiceDisabled: boolean = true;
  isFinishDisabled: boolean = true;
  previousTabId: any = ""; // Variable to store the previous tab
  tabValidationErrors: { [key: string]: boolean } = {};
  private scrollPosition = 0;
  public completeImportStatus: boolean = false;
  public completeMakeStatus: boolean = false;
  public completePaytypeStatus: boolean = false;
  public completeDeptStatus: boolean = false;
  public completeInvoiceStatus: boolean = false;
  public completeFinishStatus: boolean = false;
  private tabPosition: any;
  public defaultInvoiceColDef: any;
  isExpandedGrid:any= false;  
  isExpandedGrid1:any= false;  
  isExpandedGrid2:any= false; 
  isExpandedGrid3:any= false; 
  isExpandedGrid4:any= false; 
  isExpandedGrid5:any= false; 
  public rowDataPayList:any;
  public columnDefsPayList:any;
  public combinedPaytypeLists:any;
  public rowDataLaborPayList:any;
  public columnDefsLaborPayList:any;
  public rowDataLaborSummary:any;
  public columnDefsLaborSummary:any;
  public rowDataPartSummary:any;
  public columnDefsPartSummary:any;
  public payTypeList:any;
  public showAll: boolean = false;
  public fileShowList:any;
  public showAllStatus:boolean = false;
  public stateShowList: any;
  public showManufacturer: any;
  public tempRowData3:any;
  public isMakeAsGlobal:any = true;
  public isManufacturerAsGlobal:any = true;
  public isAdmin:Boolean = false;
    constructor(
    // private ngWizardService: NgWizardService,
    private commonService: CommonService,
    private router: Router,
    private apollo: Apollo,
    private route: ActivatedRoute,
    public constantService: ConstantService,
    private DmFormGroupService: DmFormGroupService,
    private changeDetectorRef: ChangeDetectorRef,
    private toastrService: ToastrService,
    public SubscriptionConstantService: SubscriptionConstantService,
    private http: HttpClient,
    private renderer: Renderer2,
    private elRef: ElementRef
  ) {
    this.rowSelection = "single";
    this.gridOptions1 = <GridOptions>{};
    this.gridOptions2 = <GridOptions>{};
    this.gridOptions3 = <GridOptions>{};
    this.gridContext = {
      componentParent: this,
    };
    this.defaultColDef = {
      resizable: true,
      filter: true,
      sortable: true,
      suppressExcelExport: true,
      filterParams: {
        newRowsAction: "keep",
      },
    };
    this.defaultInvoiceColDef = {
      resizable: true,
      filter: true,
      // sortable: true,
      suppressMultiSort: true,
      suppressExcelExport: true,
      filterParams: {
        newRowsAction: "keep",
      },
    };
    this.defaultColDefPayType = {
      resizable: true,
      filter: true,
      sortable: true,
      editType: "fullRow",
      cellDataType: false,
      stopEditingWhenCellsLoseFocus: true,
    };
    this.frameworkComponents = {
      checkboxRenderer: CheckboxCellComponent_,
    };
    this.frameworkInvoiceComponents = {
      checkboxModelRenderer: CheckboxCellComponentModel,
    };
    this.frameworkDeptComponents = {
      checkboxDeptRenderer: CheckboxCellDeptComponent,
    };
    this.overlayLoadingTemplate =
      '<span class="ag-overlay-loading-center">Loading <em aria-hidden="true" class="fa fa-spinner fa-pulse"></em></span>';
    this.overlayNoRowsTemplate =
      '<span class="no-rows-overlay">No data found</span>';
    this.overlayNoRowsDeptTemplate =
      '<span class="no-rows-dept-overlay">No data found</span>';
    this.overlayNoRowsSeqTemplate =
      '<span class="no-rows-seq-overlay">No data found</span>';
    this.overlayNoRowsRoSeqTemplate =
      '<span class="no-rows-roseq-overlay">No data found</span>';
  }
  ngOnInit() {
    // this.commonService.getGroups(() => {
    //   this.commonService.checkGroups((flag) => {
    //     if (!flag) {
    //       return;
    //     }
    //     this.isAuthenticated = true;
    this.init();
    //   });
    // });
    /*For multi dropdown settings*/
    this.singleDropdownSettings =
      this.DmFormGroupService.singleDropdownSettings();
    this.singleDropdownSettingsDisable =
      this.DmFormGroupService.singleDropdownSettingsDisable();
    this.multiDropdownSettings =
      this.DmFormGroupService.multiDropdownSettings();
    this.multiDropdownSettingsDisable =
      this.DmFormGroupService.multiDropdownSettingsDisable();
    this.SubscriptionConstantService.pageTitle = " - Import";
    // this.selectedStepIndex = this.config.selected;
  }
  ngAfterViewInit() {
    // Using jQuery to detect modal close event
    $("#haltReasonModal1").on("hidden.bs.modal", () => {
      this.closeHaltReasonModal1();
    });
    $("#haltReasonModal2").on("hidden.bs.modal", () => {
      this.closeHaltReasonModal2();
    });
  }
  init() {
    this.base64QueryString = "";
    // From URL automation
    this.route.paramMap.subscribe((params: any) => {
      // Retrieve the value of the "base64QueryString" parameter from URL base
      this.base64QueryString = params.get("base64QueryString");
    });
    /*for url automation*/
    if (this.base64QueryString) {
      this.currentTab = "make-tab";
      this.isMakeDisabled = false;

      this.getSchemaData("", "fromURI");
      let base64String = this.base64QueryString;
      let previousString;
      do {
        previousString = base64String;
        base64String = decodeURIComponent(base64String);
      } while (base64String !== previousString);
      // Decode the Base64 string
      const decodedString = atob(base64String);
      const decodedObject = JSON.parse(decodedString);
      this.schedulerImportId = decodedObject.schedulerId;
      this.companyImportId = decodedObject.companyId;
      this.manufacturer = decodedObject.manufacturer;
      this.dmsList = decodedObject.dmsList;
      this.companyListName = decodedObject.companyName;
      this.stateList = decodedObject.state;
      this.invSeqHalt = decodedObject.invSeqHalt;
      this.isPaytypeHalt = decodedObject.isPaytypeHalt;
      this.isDeptHalt = decodedObject.isDeptHalt;
      this.isMakeHalt = decodedObject.isMakeHalt;
      const requestUriData = {
        schedulerId: this.schedulerImportId,
        companyImportId: this.companyImportId,
        operationName: "getFetchData",
      };
      let strictMode = true;
      let schemaData: any = this.getSchemaURL(this.dmsList,strictMode );
      let url = schemaData.url;
      let dmsList: any = schemaData.dmsCode;
      if(url !== null){
      const token = localStorage.getItem("token");
      const headers = new HttpHeaders({
        authorization: token ? `Bearer ${token}` : "",
        "Content-Type": "application/json",
        // requestdata: JSON.stringify(requestUriData),
      });
      this.http
        .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
        .subscribe((res: any) => {
          if (res && res.data && res.data.schemaResult == "Connected Schema") {
            if (res.status == "success") {
              this.getAllManufacturersList(() => {
                this.setManufacturerListGrid(true);
                this.setMakeListGrid(true);
                this.setUnassignedListGrid();
              });
            } else {
              this.showStatusMessage("Please Try Again", "failed");
            }
          } else {
            let activityDataSchedule = {
              activityName: "init data loader",
              activityType: "getFetchData",
              activityDescription: `Fetch getFetchData with request params ${JSON.stringify(
                requestUriData
              )}.
            )})`,
            };
            this.commonService.saveActivity(
              "init data loader",
              activityDataSchedule
            );
            setTimeout(() => {
              this.gridApiMakes.hideOverlay();
              this.gridApiModel.hideOverlay();
              this.gridApiUnassignMake.hideOverlay();
              this.showStatusMessage(
                "Error while fetching data. please check the BE Connection.",
                "failed"
              );
              this.rowDataMakes = [];
              this.rowDataModel = [];
              this.rowDataUnassignMake = [];
            }, 200);
          }
        });
      }else{
        let activityDataSchedule = {
          activityName: "init data loader",
          activityType: "getFetchData",
          activityDescription: `Fetch getFetchData with request params ${JSON.stringify(
            requestUriData
          )}.
        )})`,
        };
        this.commonService.saveActivity(
          "init data loader",
          activityDataSchedule
        );
        setTimeout(() => {
          this.gridApiMakes.hideOverlay();
          this.gridApiModel.hideOverlay();
          this.gridApiUnassignMake.hideOverlay();
          swal({
            title: `The DMS "${this.dmsList}" does not exist.`,
            type: "warning",
            confirmButtonClass: "btn-warning pointer",
            confirmButtonText: this.constantService.CLOSE,
          })
          this.rowDataMakes = [];
          this.rowDataModel = [];
          this.rowDataUnassignMake = [];
        }, 200);        
      }
    } else {
      this.currentTab = "import-tab";
      this.isMakeDisabled = true;
      this.tabValidationErrors["import-tab"] = false;
    }
    this.createSchedule = new FormGroup({
      storeGroup: new FormControl("", Validators.required),
      store: new FormControl("", Validators.required),
    });
    if (!this.listener) {
      this.listener = this.renderer.listen(
        this.elRef.nativeElement,
        "click",
        (evt: any) => {
          if (evt.target.className == "fa fa-plus manufactureicon") {
            this.makeFetchList = evt.target.querySelector("span").innerText;
            this.openHaltReasonModal();
          } else if (evt.target.className == "badge halt-import1") {
            this.showSpinnerStoreButton = false;
            this.makeFetchList = evt.target.querySelector("span").innerText;
            // Access the inner text of the nested span element
            this.openHaltReasonModal1();
          } else if (evt.target.className == "badge halt-import2") {
            this.showSpinnerStoreButton = false;
            this.makeFetchList = evt.target.querySelector("span").innerText;
            this.openHaltReasonModal2();
          } else if (evt.target.className == "hasDuJobsEnabled") {
            if (evt.target.matches(".hasDuJobsEnabled")) {
              const hiddenSpan = evt.target
                .closest("label")
                .querySelector('span[style="display:none;"]');
              const paramsData = hiddenSpan
                ? JSON.parse(hiddenSpan.innerText)
                : null;

              this.tempRowData3 = paramsData;
              if (evt.target.checked) {
                this.showOverlay = true;
                setTimeout(() => {
                  this.tempRowData3 = paramsData;
                  this.onStatusChanged(paramsData, true);
                 
                 
                }, 200);
              } else {
                this.showOverlay = true;
                setTimeout(() => {
                  this.tempRowData3 = paramsData;
                  this.onStatusChanged(paramsData, false);
                }, 200);
              }
            }
          }
        }
      );
    }
    //fetch common solve queries
    this.commonService.allS360Jobs("", "production", (result: any) => {
      this.loading = false;
      this.storeGroupList = result.storeGroupList;
      this.storeList = result.storeList;
      this.jobGroupList = result.jobGroupList;
      for (let i = 0; i < this.jobGroupList.length; i++) {
        this.allStoreList.push(this.jobGroupList[i]);
      }
      this.getGroupFilterList();
    });
    setTimeout(() => {
      if (this.base64QueryString) {
        this.currentTab = "make-tab";
      } else {
        let button: any = document.querySelector(
          ".btn.btn-secondary.ng-wizard-btn-prev"
        );
        if (button) {
          // Check if the button element exists
          // Hide the button
          button.style.display = "none";
        }
        this.currentTab = "import-tab";
      }
    }, 100);
  }
  private isAtLeastOneMakeAllowed(): boolean {
    const allowedCheckedData = this.checkedListItems?.filter(
      (item: any) => item.is_allowed
    );

    if (!allowedCheckedData || allowedCheckedData.length === 0) {
      this.tabValidationErrors["make-tab"] = true;
      this.showStatusMessage("At least one make should be allowed.", "failed");
      return false;
    }
    // Check if all counts are zero
    const allCountsZero = allowedCheckedData.every(
      (item: any) => Number(item.count) === 0
    );
    const isSingleItem = allowedCheckedData.length === 1;
    const count = isSingleItem ? Number(allowedCheckedData[0].count) : null;
    const isSingleItemCountZero = isSingleItem && count === 0;
    if (allCountsZero || isSingleItemCountZero) {
      this.tabValidationErrors["make-tab"] = true;
      this.showStatusMessage(
        "Please Select any valid Make with RO count.",
        "failed"
      );
      return false;
    }
    this.tabValidationErrors["make-tab"] = false;
    // Check if at least one make is allowed check later temporary comment
    // const makeAllowedList =
    //   this.allMakeManufacturersList?.some((item: any) => item.is_allowed) ?? false;
    // if (!makeAllowedList) {
    //   this.stepDisabledMakeStatus = this.stepStates.error;
    //   this.showStatusMessage("At least one make should be alloweds.", "failed");
    //   return false;
    // }
    // Check if there are unassigned makes
    if (this.rowDataUnassignMake.length > 0) {
      this.showStatusMessage(
        "Please Complete the Unassigned Make Issue.",
        "failed"
      );
      this.tabValidationErrors["make-tab"] = true;
      return false;
    }
    // Final validation state
    this.tabValidationErrors["make-tab"] = false;
    return true;
  }
  private checkDeptAllowedItems(items: any[], errorMessage: string): boolean {
    if (items && items.length) {
      const allowedItems = items.filter((item: any) => item.is_allowed);
      // Remove duplicates based on 'dept' if applicable
      this.rowDataDept = this.removeDeptDuplicates(items, "dept");
      // Check if there are allowed items
      if (allowedItems.length) {
        // Handle existing status messages
        if (this.statusMessage) {
          this.tabValidationErrors["department-tab"] = true;
          this.showStatusMessage(this.statusMessage.message, "failed");
          return false;
        } else {
          this.tabValidationErrors["department-tab"] = false;
          return true;
        }
      } else {
        // If no allowed items
        this.tabValidationErrors["department-tab"] = true;
        this.showStatusMessage(errorMessage, "failed");
        return false;
      }
    } else {
      // If items array is empty or undefined
      this.tabValidationErrors["department-tab"] = true;
      this.showStatusMessage(errorMessage, "failed");
      return false;
    }
  }
  private getDeptItems(): any[] {
    return this.rowDataDept?.length ? this.rowDataDept : this.selectedDeptList;
  }
  private validateInvoiceSequence(): boolean {
    const containsTrue = this.rowDataInvoice?.some(
      (item: any) => item.status === true
    );
    if (containsTrue) {
      this.tabValidationErrors["invoice-tab"] = false;
      if (this.statusMessage?.error) {
        this.tabValidationErrors["invoice-tab"] = true;
        this.showStatusMessage(this.statusMessage.message, "failed");
        return false;
      }
      return true;
    } else {
      this.tabValidationErrors["invoice-tab"] = true;
      this.showStatusMessage(
        "At least one sequence should have status enabled.",
        "failed"
      );
      return false;
    }
  }
  private validateImportFields(): boolean {
    const hasEmptyFields =
      this.storeGroup.length == 0 ||
      this.store.length == 0 ||
      this.version.length == 0 ||
      this.fileList.length == 0;
    if (hasEmptyFields) {
      this.tabValidationErrors["import-tab"] = true; // Clear error if valid
      this.showStatusMessage("Please fill all mandatory fields", "failed");
      return false;
     }else{
      let showStatus:any;
      showStatus = this.showAllStatus ? "showAll" : "showNotAll";
      if(showStatus == "showAll"){
        const result = this.allStoreList?.find((item:any) => item.companyId == this.companyCommonId);
        const dmsCode = result ? result.dmsCode : null;
        console.log("dmsCode0000",this.allStoreList,dmsCode,this.storeGroupList,this.companyCommonId,dmsCode,this.storeFilterList)
        if(dmsCode){
          if (this.storeFilterList && this.storeFilterList[0] && this.storeFilterList[0].dmsCode != dmsCode) {
            this.tabValidationErrors["import-tab"] = true; // Clear error if valid
            this.showStatusMessage("Please select matching DMS", "failed");
            return false;
          }
        }
        // else{
        //   this.showStatusMessage("Please try again", "failed");
        //   return false;

        // }
      }
      }
    this.tabValidationErrors["import-tab"] = false; // Clear error if valid
    return true;
  }
  private validatePayType(mode: any): boolean {
    let combinedPaytypeArray: any[] = this.custPaytypeAvailable.concat(
      this.custPaytypeSelected,
      this.custPaytypeSelectedOnlyParts,
      this.custPaytypeSelectedOnlyLabor,
      this.warrPaytypeAvailable,
      this.warrPaytypeSelected,
      this.warrPaytypeSelectedParts,
      this.warrPaytypeSelectedLabor
    );
    const updatedPayType = combinedPaytypeArray.map((item) => {
      // Update the category and delete base_paytype if it exists
      if (item.base_paytype) {
        item.category = item.base_paytype.toUpperCase();
        delete item.base_paytype;
      }
      return item;
    });
    // Check if the item with the same id already exists in custPaytypeAvailable
    // Combine the two arrays
    const combinedPaytypeList = [
      ...this.custCombinePayList,
      ...this.warrCombinePayList,
    ];
    let differences: any = [];
    combinedPaytypeArray.forEach((item1) => {
      const matchingItem = combinedPaytypeList.find(
        (item2: any) => item2.paytype === item1.paytype
      );
      if (matchingItem) {
        const diff: any = {};
        if (item1.is_parts_allowed !== matchingItem.is_parts_allowed) {
          diff.is_parts_allowed = {
            previous: matchingItem.is_parts_allowed,
            current: item1.is_parts_allowed,
          };
          // Update the combinedPaytypeList with the new value
          matchingItem.is_parts_allowed = item1.is_parts_allowed;
        }
        if (item1.is_labor_allowed !== matchingItem.is_labor_allowed) {
          diff.is_labor_allowed = {
            previous: matchingItem.is_labor_allowed,
            current: item1.is_labor_allowed,
          };
          // Update the combinedPaytypeList with the new value
          matchingItem.is_labor_allowed = item1.is_labor_allowed;
        }
        if (Object.keys(diff).length > 0) {
          differences.push({
            paytype: item1.paytype,
            differences: diff,
          });
        }
       
      }
    });
    // Log the updated combinedPaytypeList
    let requestUriData: any;
    if (combinedPaytypeArray.length) {
      requestUriData = {
        schedulerId: this.schedulerCommonId,
        companyImportId: this.companyCommonId,
        operationName: "allSelSavePaytype",
        selectedPayList: updatedPayType,
      };
    } else {
      requestUriData = {
        schedulerId: this.schedulerCommonId,
        companyImportId: this.companyCommonId,
        operationName: "allSelSavePaytype",
        selectedPayList: [],
      };
    }
    let activityDataSchedule = {
      activityName: "CASE:::: pay type",
      activityType: "allSelSavePaytype",
      activityDescription: `Add selected Pay type list with request params ${JSON.stringify(
        requestUriData
      )}.
      )})`,
    };
    this.commonService.saveActivity(
      "Pay type selection Case",
      activityDataSchedule
    );
    // old code
    if (differences.length == 0 && this.isPaytypeHalt) {
      let self = this;
      self.stayOnCurrentTab = false;
      this.currentTab = "paytype-tab";
      swal(
        {
          title: "Do you want to continue?",
          text: "You haven't made any modifications.",
          type: "warning",
          confirmButtonClass: "btn-success pointer",
          confirmButtonText: "OK",
          closeOnConfirm: true,
          showCancelButton: true,
          cancelButtonClass: "btn-default pointer",
          closeOnCancel: true,
          allowOutsideClick: false
        },
        function (isConfirm: any) {
          // Using arrow function here
          if (isConfirm) {
            if (!self.stayOnCurrentTab) {
              combinedPaytypeArray = combinedPaytypeArray.map(({ is_highlight, ...rest }) => rest);

              self.saveSelectPayType(updatedPayType, requestUriData, mode);
              self.stayOnCurrentTab = true;
            }
          } else {
            self.stayOnCurrentTab = false;
          }
        }
      );
      return self.stayOnCurrentTab;
    } else {
      combinedPaytypeArray = combinedPaytypeArray.map(({ is_highlight, ...rest }) => rest);
      this.saveSelectPayType(updatedPayType, requestUriData, mode);
    }
    this.tabValidationErrors["paytype-tab"] = false;
    return true;
  }
  private validateDeptType(departmentFetchData: any, mode: any): boolean {
    let allowedDepartments: any;
    if (this.rowDataDept && this.rowDataDept.length) {
      allowedDepartments = this.rowDataDept;
      // Filter unique departments only if necessary
      allowedDepartments = this.removeDeptDuplicates(
        allowedDepartments,
        "dept"
      );
    }
    if (allowedDepartments) {
      allowedDepartments.forEach((item: any) => {
        item.is_determined = false; // Always set is_determined to true
      });
    }
    this.noChangedDepartments =
      allowedDepartments.length == departmentFetchData.length &&
      allowedDepartments.every(
        (obj: any, index: any) =>
          Object.keys(obj).every((key) => {
            if (key == "is_determined") return true; // Skip 'is_highlight' field
            return obj[key] === departmentFetchData[index][key];
          }) && obj.is_allowed === departmentFetchData[index].is_allowed
      );
   
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      operationName: "selDepartmentsList",
      departmentSelList: !allowedDepartments ? [] : allowedDepartments,
    };
    let activityDataSchedule = {
      activityName: "CASE:::: Department",
      activityType: "selDepartmentsList",
      activityDescription: `Add selected Department list with request params ${JSON.stringify(
        requestUriData
      )}.
  )})`,
    };
    this.commonService.saveActivity(
      "Department selection Case",
      activityDataSchedule
    );
    if (this.noChangedDepartments && !this.isDeptHalt) {
      let self = this;
      swal(
        {
          title: "Do you want to continue?",
          text: "You haven't made any modifications.",
          type: "warning",
          confirmButtonClass: "btn-success pointer",
          confirmButtonText: "OK",
          closeOnConfirm: true,
          showCancelButton: true,
          cancelButtonClass: "btn-default pointer",
          closeOnCancel: true,
          allowOutsideClick: false
        },
        function (isConfirm: any) {
          if (isConfirm) {
            if (allowedDepartments) {
              allowedDepartments.forEach((item: any) => {
                item.is_determined = true; // Always set is_determined to true
              });
            }
            self.saveSelectDepartment(allowedDepartments, requestUriData, mode);
            self.stayOnCurrentTab = true;
          } else {
            self.currentTab = "department-tab";
            self.stayOnCurrentTab = false;
          }
        }
      );
      return self.stayOnCurrentTab;
    } else {
      if (allowedDepartments) {
        allowedDepartments.forEach((item: any) => {
          item.is_determined = true; // Always set is_determined to true
        });
      }
      this.saveSelectDepartment(allowedDepartments, requestUriData, mode);
    }
    return true;
  }
  private getPayTypeValidationMessage(): string {
    const custCondition =
      this.custPaytypeSelected.length &&
      !this.custPaytypeAvailable.length &&
      !this.custPaytypeSelectedOnlyParts.length &&
      !this.custPaytypeSelectedOnlyLabor.length;
    const warrCondition =
      this.warrPaytypeSelected.length &&
      !this.warrPaytypeAvailable.length &&
      !this.warrPaytypeSelectedParts.length &&
      !this.warrPaytypeSelectedLabor.length;
    if (custCondition && warrCondition) {
      return "Customer and Warranty paytypes should not be excluded.";
    } else if (custCondition) {
      return "Customer paytype should not be excluded.";
    } else if (warrCondition) {
      return "Warranty paytype should not be excluded.";
    }
    return "";
  }

  /* start make grid function*/
  getAllManufacturersList(callback: any) {
    let activityDataSchedule = {
      activityName: "Fetch manufacturer list",
      activityType: "Fetch manufacturers",
      activityDescription: `Fetch manufacturers list for displaying make list
    )})`,
    };
    this.commonService.saveActivity("Scheduler Import", activityDataSchedule);

    this.subscription = this.apollo
      .use("manageScheduler")
      .query({
        query: allManufacturers,
        fetchPolicy: "network-only",
        variables: {},
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          const allManufacturers = result.data.allManufacturers.edges;
          this.allMakeList = result.data.allManufacturers.edges;
          console.log("all make list***********************************",this.allMakeList);
          /** get manufactire list from Local DB */
          this.getManufactureListFromLocalDD(allManufacturers, () => {
            callback(true);
          });
          /** End Section */
        },
        error: (err: any) => {
          callback(false);
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
    return [
      this.allManufacturersList,
      this.unassignedData,
      this.resMakeListData,
      this.allMakeManufacturersList,
    ];
  }
  getManufactureListFromLocalDD(allManufacturers: any, callback: any) {
    if (this.schedulerImportId && this.companyImportId) {
      this.schedulerCommonId = this.schedulerImportId;
      this.companyCommonId = this.companyImportId;
    } else {
      this.schedulerCommonId = this.schedulerFileId;
      this.companyCommonId = this.companyFileId;
      this.manufacturer = this.manufacturer;
    }
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      operationName: "allManufacturers",
    };
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      // requestData: JSON.stringify(requestUriData),
      "Content-Type": "application/json",
    });
    let schemaData: any = this.getSchemaURL(this.dmsList);
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
      .subscribe((res: any) => {
        let activityDataSchedule = {
          activityName: "allManufacturers",
          activityType: "getSchemaData",
          activityDescription: `Fetch getSchemaData with request params ${JSON.stringify(
            requestUriData
          )}.
        )})`,
        };
        this.commonService.saveActivity(
          "All Manufacturers List",
          activityDataSchedule
        );
        if (res.status == "success") {
          this.resMakeListData = res.data.makeAllData;
        } else {
          if (res.status == "error") {
            const message = "Error while fetching data";
            this.statusMessage = { message: message, error: true };
            this.showStatusMessage(message, "failed");
          }
        }
        let allManufacturersList: any[] = []; // Initialize as an empty array
        if (this.manufacturer) {
          this.selectedManufacturerList.push(this.manufacturer.toUpperCase());
        }
        $.each(allManufacturers, (key: any, val: any) => {
          if (val && val.node) {
           //Function to check if the state is valid
           const isValidState = (state: string) => {
            const validStates = ["N/A", "OH", "MD", "MT", "UT"];
            return validStates.includes(state);
          };
          // Determine if the manufacturer and state conditions are met
          const isCheckState = this.manufacturer == "GM"
            ? isValidState(this.stateList) // For "GM", check if the state is valid
            : true; // For other manufacturers, always set `isCheckState` to true
          // Map the selected manufacturers list for comparison
          const isSelectedManufacturer = this.selectedManufacturerList
            ?.map((manufacturer: any) => manufacturer.replace(/\s/g, "").toUpperCase())
            .includes(val.node.manufacturer.replace(/\s/g, "").toUpperCase());
            allManufacturersList.push({
              manufacturer: val.node.manufacturer,
              makeList: val.node.makeList,
              count: "",
              is_allowed: isCheckState ? isSelectedManufacturer : true, // Only apply selection restriction for GA
            });
          }
        });
        const aggregatedCounts: any = {};
        this.resMakeListData?.forEach((item: any) => {
          if (item.make) {
            // Check if make is not null or undefined
            const make = item.make.toUpperCase();
            aggregatedCounts[make] =
              (aggregatedCounts[make] || 0) + parseInt(item.count);
          } else {
            console.log("Make is null or undefined for item:", item);
            // Handle the case where make is null or undefined if necessary
          }
        });
        allManufacturersList.forEach((item1) => {
          const make = item1.manufacturer.toUpperCase();
          if (aggregatedCounts[make] !== undefined) {
            item1.count = aggregatedCounts[make].toString();
          }
        });
        this.allMakeManufacturersList = allManufacturersList;
        // Assuming you want to invoke a callback function after processing the data
        if (callback) {
          callback(true);
        }
      });
    return [this.resMakeListData];
  }
  /*fetch make list function */
  setMakeListGrid(flag: any) {
    const self = this;
    let makesAllowed: any = [];
    // Filter unique manufacturers where is_allowed is true
    console.log("this.allMakeManufacturersList$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.allMakeManufacturersList);
    this.allMakeManufacturersList.forEach((item: any) => {
      // Check if the manufacturer is allowed and not already in makesAllowed
      if (
        item.is_allowed &&
        !makesAllowed.some((maker: any) => maker.makeList === item.makeList)
      ) {
        makesAllowed.push(item);
      }
    });
    const transformedData: any[] = [];
    makesAllowed?.forEach((item: any) => {
      if (Array.isArray(item.makeList) && item.makeList.length) {
        item.makeList?.forEach((make: any) => {
          //    Each item in makeList will be a separate array
          transformedData?.push({
            manufacturer: item.manufacturer,
            makeList: make,
            count: "0",
            is_allowed: item.is_allowed,
          });
        });
      }
    });
    // Use reduce to filter out duplicate entries based on the "manufacturer" property
    const filteredData = makesAllowed.reduce(
      (accumulator: any, currentValue: any) => {
        // Check if the manufacturer already exists in the accumulator array
        const existingManufacturer = accumulator.find(
          (item: any) => item.manufacturer === currentValue.manufacturer
        );
        // If manufacturer does not exist, add it to the accumulator
        if (!existingManufacturer) {
          accumulator.push(currentValue);
        }
        return accumulator;
      },
      []
    );
    makesAllowed = filteredData;
    if (transformedData && transformedData.length) {
      const totalmakeList = new Set(
        this.resMakeListData?.map((item: any) => item.make)
      );
      // Update counts in arr1 based on arr2
      const updatedModel = transformedData.map((item: any) => {
        const count = totalmakeList.has(item.makeList)
          ? this.resMakeListData.find((i: any) => i.make == item.makeList).count
          : "0";
        return {
          ...item,
          count,
        };
      });
      if (this.rowDataMakes.length) {
        const hasNonZeroCount = updatedModel?.some((item) => Number(item.count) > 0);
        // Display the full array if at least one count is non-zero, otherwise log a message
        if (hasNonZeroCount) {
          const filteredItems = updatedModel?.filter(item => {
            // Check if the count is zero
            if (Number(item.count) == 0) {
              // Check if the item's manufacturer exists in rowDataMakes
              return this.rowDataMakes?.some(row => row.manufacturer == item.manufacturer);
            }
            // If count is not zero, include the item in filteredItems
            return true;
          });
          this.rowDataModel = filteredItems;
        } else {
          this.rowDataModel = [];
        }
      } else {
        this.rowDataModel = [];
      }
    } else {
      this.rowDataModel = [];
    }
    this.checkedListItems = this.rowDataModel;
    this.changeDetectorRefCheck();
    // this.importModelGroupFilterList = [];
    // for (let i = 0; i < this.rowDataModel.length; i++) {
    //   const make = this.rowDataModel[i].manufacturer;
    //   const model = this.rowDataModel[i].makeList;
    //   const count = this.rowDataModel[i].count;
    //   if (make) {
    //     const obj = {
    //       id: model,
    //       itemName: model,
    //       model: model,
    //       count: count,
    //     };
    //     if (!this.containsObject(obj, this.importModelGroupFilterList)) {
    //       this.importModelGroupFilterList.push(obj);
    //     }
    //   }
    // }
    const allMakes1 = [
      ...new Set(
        this.allMakeList.flatMap((edge: any) => edge.node.makeList)
      )
    ];
    
    
    
    console.log("Filtered make all test1>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",allMakes1);
    

      for (let i = 0; i < allMakes1.length; i++) {
      const make = allMakes1[i];
      const model = allMakes1[i];
      const count = 0;
      if (make) {
        const obj = {
          id: model,
          itemName: model,
          model: model,
          count: count,
        };
        if (!this.containsObject(obj, this.importModelGroupFilterList)) {
          this.importModelGroupFilterList.push(obj);
        }
      }
    }
   
    this.importModelGroupFilterList = this.sortListAsc(
      this.importModelGroupFilterList
    );
    console.log("this.importModelGroupFilterList test1$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.importModelGroupFilterList);
    this.columnDefsModel = [
      {
        headerName: "Manufacturer",
        field: "manufacturer",
        width: 120,
        cellClass: () => {
          return "left-aligned-cell-t";
        },
        cellRenderer: function (params: any) {
          if (params.data) {
            return params.data.manufacturer;
          } else {
            return "";
          }
        },
      },
      {
        headerName: "Make",
        field: "makeList",
        width: 140,
        cellClass: () => {
          return "left-aligned-cell-t";
        },
        cellRenderer: function (params: any) {
          if (params.data) {
            if (Array.isArray(params.data.makeList)) {
              return params.data.makeList[0];
            } else {
              return params.data.makeList;
            }
          } else {
            return "";
          }
        },
      },
      {
        headerName: "RO Count",
        field: "count",
        width: 120,
        sort: "desc",
        type: "rightAligned",
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;

          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
        cellRenderer: function (params: any) {
          if (params.data.count) {
            return params.data.count;
          } else {
            return "";
          }
        },
      },
      {
        headerName: "Allowed?",
        field: "is_allowed",
        width: 100,
        cellRendererFramework: CheckboxCellComponent_,
        gridIdentifier: "gridModel",
        cellClass: () => {
          return "left-aligned-cell-t";
        },
      },
    ];
    this.changeDetectorRefCheck();
  }
  /*fetch unassignedMake list */
  setUnassignedListGrid() {
    this.unassignedData = this.resMakeListData?.filter(
      (item: any) =>
        !this.allMakeManufacturersList.some((arr1Item: any) =>
          arr1Item.makeList.includes(item.make)
        )
    );
    this.rowDataUnassignMake = this.unassignedData?.filter(
      (item: any) => item.make && parseInt(item.count) > 0
    );
    this.columnDefsUnassignMake = [
      {
        headerName: "Make",
        field: "make",
        sortable: true,
        width: 100,
        cellRenderer: function (params: any) {
          if (params.data.make && params.data.make != null) {
            return params.data.make;
          } else {
            return "";
          }
        },
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
      },
      {
        headerName: "RO Count",
        field: "count",
        sortable: true,
        width: 130,
        sort: "desc",
        type: "rightAligned",
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return "";
          } else {
            return numericValueA - numericValueB;
          }
        },
        cellRenderer: function (params: any) {
          if (params.data.count && params.data.count != 0) {
            return params.data.count;
          } else {
            return "";
          }
        },
      },
      {
        headerName: "Add as a Make",
        field: "",
        width: 135,
        cellRenderer: function (params: any) {
          let html = "<span></span>";
          html += `<a href="javascript:void(0);" 
                  data-toggle="tooltip" 
                  data-animation="false" 
                  data-placement="left" 
                  data-note="" 
                  title="Add As A Make">
                  <span class="badge halt-import1" style="line-height:2;vertical-align:middle;">
                  Add as Make<span style="display:none">${params.data.make}</span>
                  </span>
                  </a>`;
          return html;
        },
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
      },
      {
        headerName: "Assign as an Alias",
        field: "",
        width: 135,
        cellRenderer: function (params: any) {
          let html = "<span></span>";
          html += `<a href="javascript:void(0);" 
                  data-toggle="tooltip" 
                  data-animation="false" 
                  data-placement="left" 
                  data-note="" 
                  title="Assign as an Alias">
                  <span class="badge halt-import2" style="line-height:2;vertical-align:middle;">
                      Assign as Alias<span style="display:none">${params.data.make}</span>
                  </span>
                  </a>`;
          return html;
        },
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
      },
    ];
    // Get references to the spans
    const accordion1 = document.getElementById("accordion1");
    const accordion2 = document.getElementById("accordion2");
    const accordion3 = document.getElementById("accordion3");
    // Add click event listeners
    accordion1?.addEventListener("click", () => {
      this.toggleAccordion(1);
    });
    accordion2?.addEventListener("click", () => {
      this.toggleAccordion(2);
    });

    accordion3?.addEventListener("click", () => {
      this.toggleAccordion(3);
    });
  }
  /*fetch manufacture list */
  setManufacturerListGrid(flag: any) {
    const token = localStorage.getItem("token");
    if (this.schedulerImportId && this.companyImportId) {
      this.schedulerCommonId = this.schedulerImportId;
      this.companyCommonId = this.companyImportId;
    } else {
      this.schedulerCommonId = this.schedulerFileId;
      this.companyCommonId = this.companyFileId;
    }
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      operationName: "allMakesList",
      makeListData: this.allMakeManufacturersList,
    };
    let activityDataSchedule = {
      activityName: "Fetch MakesList from back end point",
      activityType: "allMakesList",
      activityDescription: `Fetch MakesList with request params ${requestUriData}.
    )})`,
    };
    this.commonService.saveActivity(
      "Scheduler Import Make Tab",
      activityDataSchedule
    );
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      "Content-Type": "application/json",
    });
    let schemaData: any = this.getSchemaURL(this.dmsList);
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
      .subscribe((res: any) => {
        if (res.status == "error") {
          this.showStatusMessage(res.message, "failed");
          this.statusMessage = { message: res.message, error: true };
          this.currentTab = "make-tab";
          !flag ? this.currentTab : "";
        } else {
          !flag ? this.currentTab : "";
        }
      });
    const self = this;
    this.rowDataMakes = this.allMakeManufacturersList;

    this.importMakeGroupFilterList = [];
    for (let i = 0; i < this.rowDataMakes.length; i++) {
      const make = this.rowDataMakes[i].manufacturer;
      const model = this.rowDataMakes[i].makeList;
      const count = this.rowDataMakes[i].count;
      const isAllowed = this.rowDataMakes[i].is_allowed;
      if (make) {
        const obj = {
          id: make,
          itemName: make,
          model: model,
          count: count,
        };
        if (!this.containsObject(obj, this.importMakeGroupFilterList)) {
          this.importMakeGroupFilterList.push(obj);
        }
      }
    }
    this.importMakeGroupFilterList = this.sortListAsc(
      this.importMakeGroupFilterList
    );
      // Iterate through arr1
      this.rowDataMakes.forEach((item1: any) => {
        // Initialize total count for the current manufacturer
        let totalCount = 0;
        // Iterate through makeList of arr1
        item1.makeList.forEach((make: any) => {
          // Check if the make is present in arr2
          if (this.resMakeListData) {
            const matchingMake = this.resMakeListData.find(
              (item2: any) => item2.make === make
            );
            if (matchingMake) {
              // Add the count to total count
              totalCount += parseInt(matchingMake.count);
            }
          } else {
            this.resMakeListData = [];
          }
        });
        // Update the count in arr1
        item1.count = totalCount.toString();
      });
      const filteredRowData = this.rowDataMakes.filter(
        (item: any) => item.count != "0"
      );
      this.rowDataMakes = filteredRowData;
    this.columnDefsMakes = [
      {
        headerName: "Manufacturer",
        field: "manufacturer",
        sortable: true,
        width: 110,
        cellRenderer: function (params: any) {
          if (params.data) {
            return params.data.manufacturer;
          } else {
            return "";
          }
        },
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
      },
      {
        headerName: "Make List",
        field: "makeList",
        sortable: true,
        width: 100,
        filter: "agTextColumnFilter",
        filterParams: {
          filterOptions: ["contains"], // configure it to use 'contains' by default
          debounceMs: 200, // delay in milliseconds before filtering
        },
        valueGetter: function (params: any) {
          return params.data ? params.data.makeList.join(", ") : "";
        },
        cellRenderer: function (params: any) {
          if (params.data) {
            return params.data.makeList.join(", ");
          } else {
            return "";
          }
        },
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
        tooltipValueGetter: function (params: any) {
          if (params.data) {
            return params.data.makeList.join(", ");
          } else {
            return "";
          }
        },
      },
      {
        headerName: "RO Count",
        field: "count",
        sortable: true,
        sort: "desc",
        type: "rightAligned",
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;

          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
        width: 130,
        cellRenderer(params: any) {
          if (params.data) {
            return params.data.count;
          } else {
            return "";
          }
        },
      },
      {
        headerName: "Allowed?",
        field: "is_allowed",
        width: 135,
        cellRendererFramework: CheckboxCellComponent_,
        gridIdentifier: "gridMake",
        cellClass: (params: any) => {
          return "left-aligned-cell-t";
        },
      },
    ];
    this.changeDetectorRefCheck();
  }
  /* end make grid function*/
  /* start paytype function*/
  getStoreTypes(callback: any) {
    if (this.schedulerImportId && this.companyImportId) {
      this.schedulerCommonId = this.schedulerImportId;
      this.companyCommonId = this.companyImportId;
    } else {
      this.schedulerCommonId = this.schedulerFileId;
      this.companyCommonId = this.companyFileId;
    }
    let activityDataSchedule = {
      activityName: "Fetch pay type",
      activityType: "Pay Type List",
      activityDescription: `Fetch pay type list for displaying customer and warranty types
    )})`,
    };
    this.commonService.saveActivity("Scheduler Import", activityDataSchedule);
    this.subscription = this.apollo
      .use("manageScheduler")
      .query({
        query: storePayType,
        fetchPolicy: "network-only",
        variables: {
          companyId: this.companyCommonId, // Pass your variable here
        },
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          const jsonData = result;
          // Extract the array from the object
          const jsonArrayPaytype = jsonData.data.getStorePaytype;
          // Parse the array from the strin
          const jsonTypeArray = JSON.parse(jsonArrayPaytype);
          this.allPaytypeList = jsonTypeArray;
          if (jsonTypeArray) {
            this.allPaytypeList = jsonTypeArray;
          } else {
            this.allPaytypeList = [];
          }
          //fetch paytype in localDB
          /** get manufactire list from Local DB */
          this.getPaytypesFromLocalDD(() => {
            callback(true);
          });
        },
        error: (err: any) => {
          callback(false); // Call the callback function with status false
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
    return [
      this.allPaytypeList,
      this.allPaytypeWarrantyList,
      this.allPaytypeCustomerList,
    ];
  }
  getPaytypesFromLocalDD(callback: any) {
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      operationName: "storePayType",
    };
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      // requestData: JSON.stringify(requestUriData),
      "Content-Type": "application/json",
    });
    let schemaData: any = this.getSchemaURL(this.dmsList);
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
      .subscribe((res: any) => {
        let activityDataSchedule = {
          activityName: "Fetch pay type",
          activityType: "Pay Type List",
          activityDescription: `Fetch pay type list for displaying customer and warranty types in local DB
              )})`,
        };
        this.commonService.saveActivity(
          "Scheduler Import",
          activityDataSchedule
        );
        this.resPayListData = res.data?.paytypeAllData;
        if (res) {
          // this.ngWizardService.show(2);
        }
        // Assuming you want to invoke a callback function after processing the data
        if (callback) {
          callback(true);
        }
      });
  }
  /*fetch paytype list */
  loadPayTypes() {
    this.selectedCustomerPay = [];
    this.deSelectedCustomerPay = [];
    this.selectedWarrantyPay = [];
    this.custPaytypeAvailable = [];
    this.custPaytypeSelected = [];
    this.selectedCTypeLeftBox = [];
    this.selectedWarrantyPay = [];
    this.deSelectedWarrantyPay = [];
    this.warrPaytypeAvailable = [];
    this.warrPaytypeSelected = [];
    this.selectedWTypeLeftBox = [];
    this.showSpinner = true;
    this.custPaytypeAll = [];
    this.warrPaytypeAll = [];
    this.intPaytypeAll = [];
    /**
     * labor section
     */
    this.LselectedCustomerPay = [];
    this.LdeSelectedCustomerPay = [];
    this.LselectedWarrantyPay = [];
    this.LcustPaytypeAvailable = [];
    this.LcustPaytypeSelected = [];
    this.LselectedCTypeLeftBox = [];
    this.LselectedWarrantyPay = [];
    this.LdeSelectedWarrantyPay = [];
    this.LcustPaytypeAll = [];
    this.LwarrPaytypeAll = [];
    this.LintPaytypeAll = [];

    /** End section */
    /**remove after demo */
    this.getAvailableAndSelectedCustPayTypes();
    this.getAvailableAndSelectedCustPayTypesOnlyParts();
    this.getAvailableAndSelectedCustPayTypesOnlyLabor();
    this.getAvailableAndSelectedWarrPayTypes();
    this.getAvailableAndSelectedWarrPayTypesParts();
    this.getAvailableAndSelectedWarrPayTypesLabor();
  }
  /*fetch paytype list */
  getStoreCustTypes() {
    const filterByCategory = (items: any, category: any) =>
      items?.filter((item: any) => item.category === category);
    // Filter result items by base paytype
    const filterByBasePaytype = (items: any, basePaytype: any) =>
      items?.filter((item: any) => item.base_paytype === basePaytype);
    // Add IDs to items
    const addIds = (items: any) => {
      return items?.map((item: any, index: any) => {
        const id = `${index}:${index + 1}`;
        return { ...item, id };
      });
    };
    let warrantyItems: any;
    let payTypeItems: any[] = [];
    let OtherItems: any;
    let paytypeSet: any;
    let warrantySet: any;
    if (!this.allPaytypeList || this.allPaytypeList.length === 0) {
      this.allPaytypeList = this.resPayListData;
      // Filter items by  scheduler category in empty cases
      warrantyItems = filterByBasePaytype(this.allPaytypeList, "Warranty");
      payTypeItems = filterByBasePaytype(this.allPaytypeList, "Customer");
      OtherItems = filterByBasePaytype(this.allPaytypeList, "Other");
      const payTypeAllItems = [...payTypeItems, ...OtherItems]; // Assuming payTypeItems contains both lists
      for (let item of payTypeAllItems) {
        let newItem = {
          ...item, // Spread operator to include all properties from item
          company_id: this.companyCommonId,
          is_determined: false,
          is_labor_allowed: true,
          is_parts_allowed: true,
          is_highlight: true,
        };
        const matchingOtherPaytypes: any = warrantyItems?.filter((warranty: any) =>
          OtherItems?.some((other: any) => other.paytype === warranty.paytype)
        );
        if (matchingOtherPaytypes.length == 0) {
          if (item.base_paytype == "Other") {
            newItem.base_paytype = "Customer";
            newItem.is_labor_allowed = false;
            newItem.is_parts_allowed = false;
          }
        }
        // If the item is from OtherItems, replace base_paytype and update labor and parts allowed
        this.customAllList.push(newItem);
      }
      for (let item of warrantyItems) {
        this.warrantyAllList.push({
          ...item, // Spread operator to include all properties from resyptyr item
          company_id: this.companyCommonId,
          is_determined: false,
          is_labor_allowed: true,
          is_parts_allowed: true,
          is_highlight: true,
        });
      }
    } else {
      // Filter items by  scheduler category
      const warrantyDataItems = filterByCategory(
        this.allPaytypeList,
        "WARRANTY"
      );
      const payTypeDataItems = filterByCategory(
        this.allPaytypeList,
        "CUSTOMER"
      );
      payTypeItems = payTypeDataItems?.filter(
        (item: any) => item.is_determined
      );
      payTypeItems = payTypeItems?.filter((item: any) => 
      this.resPayListData?.some((resItem: any) => resItem.paytype == item.paytype)
      );
      warrantyItems = warrantyDataItems?.filter(
        (item: any) => item.is_determined
      );
      warrantyItems = warrantyItems?.filter((item: any) => 
      this.resPayListData?.some((resItem: any) => resItem.paytype == item.paytype)
      );
      if (payTypeItems.length) {
        let payTypeCombineItems: any[] = filterByBasePaytype(
          this.resPayListData,
          "Customer"
        );
        OtherItems = filterByBasePaytype(this.resPayListData, "Other");
        // Transform and push OtherItems into payTypeCombineItems with updated properties
        // Updating and combining the arrays
        const matchingOtherPaytypes: any = warrantyItems?.filter((warranty: any) =>
          OtherItems?.some((other: any) => other.paytype === warranty.paytype)
        );

        if (matchingOtherPaytypes.length == 0) {
          if (OtherItems) {
            for (let item of OtherItems) {
              payTypeItems.push({
                ...item,
                base_paytype: "Customer", // Update base_paytype to "Customer"
                company_id: this.companyCommonId,
                is_determined: false,
                is_labor_allowed: false, // Update as specified
                is_parts_allowed: false, // Update as specified
                is_highlight: true,
              });
            }
          }
        } else {
          console.log(" matching paytypes found.");
        }
        // Iterate over payTypeCombineItems to add items to payTypeItems if they are not already present
        for (let item of payTypeCombineItems) {
          if (!payTypeItems.some((existingItem: any) => existingItem.paytype == item.paytype)) {
            payTypeItems.push({
              ...item,
              company_id: this.companyCommonId,
              is_determined: false,
              is_labor_allowed: true,
              is_parts_allowed: true,
              is_highlight: true,
            });
          }
        }
        // Update this.customAllList with the final payTypeItems
        this.customAllList = payTypeItems;
      } else {
        this.allPaytypeList = this.resPayListData;
        // Filter items by  scheduler category in empty cases
        payTypeItems = filterByBasePaytype(this.allPaytypeList, "Customer");
        OtherItems = filterByBasePaytype(this.allPaytypeList, "Other");
        const payTypeAllItems = [...payTypeItems, ...OtherItems];
        let filteredCustomerItems = payTypeAllItems?.filter((item: any) => {
          return !warrantyItems.some(
            (warrantypaytypeItem: any) =>
              warrantypaytypeItem.paytype === item.paytype
          );
        });
        for (let item of filteredCustomerItems) {
          let newItem = {
            ...item, // Spread operator to include all properties from item
            company_id: this.companyCommonId,
            is_determined: false,
            is_labor_allowed: true,
            is_parts_allowed: true,
            is_highlight: true,
          };
          const matchingOtherPaytypes: any = warrantyItems?.filter((warranty: any) =>
            OtherItems?.some((other: any) => other.paytype == warranty.paytype)
          );
          if (matchingOtherPaytypes.length == 0) {
            // If the item is from OtherItems, replace base_paytype and update labor and parts allowed
            if (item.base_paytype == "Other") {
              newItem.base_paytype = "Customer";
              newItem.is_labor_allowed = false;
              newItem.is_parts_allowed = false;
            }
          }
          this.customAllList.push(newItem);
        }
      }
      if (warrantyItems.length) {
        const warrantyCombineItems: any = filterByBasePaytype(
          this.resPayListData,
          "Warranty"
        );
        for (let item of warrantyCombineItems) {
          if (
            !warrantyItems.some(
              (existingItem: any) => existingItem.paytype === item.paytype
            )
          ) {
            warrantyItems.push({
              ...item, // Spread operator to include all properties from resyptyr item
              company_id: this.companyCommonId,
              is_determined: false,
              is_labor_allowed: true,
              is_parts_allowed: true,
              is_highlight: true,
            });
          }
        }
        this.warrantyAllList = warrantyItems;
      } else {
        this.allPaytypeList = this.resPayListData;
        // Filter items by  scheduler category in empty cases
        warrantyItems = filterByBasePaytype(this.allPaytypeList, "Warranty");
        let filteredWarrantyItems = warrantyItems?.filter((item: any) => {
          return !payTypeItems.some(
            (paytypeItem) => paytypeItem.paytype === item.paytype
          );
        });
        for (let item of filteredWarrantyItems) {
          this.warrantyAllList.push({
            ...item, // Spread operator to include all properties from resyptyr item
            company_id: this.companyCommonId,
            is_determined: false,
            is_labor_allowed: true,
            is_parts_allowed: true,
            is_highlight: true,
          });
        }
      }
    }
    // Filter result items by  local category
    const warrantyResultItems = filterByBasePaytype(
      this.resPayListData,
      "Warranty"
    );
    const payTypeResultItems = filterByBasePaytype(
      this.resPayListData,
      "Customer"
    );
    //merge local and scheduler data
    let mergedCustomerArray: any;
    let mergedWarrantyArray: any;
    if (payTypeItems.length) {
      mergedCustomerArray = payTypeItems?.map((item: any) => ({
        company_id: this.companyCommonId,
        paytype: item.paytype,
        category: "CUSTOMER",
        is_parts_allowed: true,
        is_labor_allowed: true,
        description: item.description,
        is_determined: false,
      }));
    } else {
      mergedCustomerArray = payTypeResultItems?.map((item: any) => ({
        company_id: this.companyCommonId,
        paytype: item.paytype,
        category: "CUSTOMER",
        is_parts_allowed: true,
        is_labor_allowed: true,
        description: item.description,
        is_determined: false,
      }));
    }
    let finalCustomerType: any;
    let finalWarrantyType: any;
    if (mergedCustomerArray.length) {
      if (this.customAllList) {
        finalCustomerType = this.customAllList;
      } else {
        finalCustomerType = this.customAllList?.concat(mergedCustomerArray);
      }
    }
    // Concatenate the first array with the modified second array
    if (warrantyItems.length) {
      mergedWarrantyArray = warrantyItems?.map((item: any) => ({
        company_id: this.companyCommonId,
        paytype: item.paytype,
        category: "WARRANTY",
        is_parts_allowed: true,
        is_labor_allowed: true,
        description: item.description,
        is_determined: false,
      }));
    } else {
      mergedWarrantyArray = warrantyResultItems?.map((item: any) => ({
        company_id: this.companyCommonId,
        paytype: item.paytype,
        category: "WARRANTY",
        is_parts_allowed: true,
        is_labor_allowed: true,
        description: item.description,
        is_determined: false,
      }));
    }
    if (mergedWarrantyArray.length) {
      if (this.warrantyAllList) {
        finalWarrantyType = this.warrantyAllList;
      } else {
        // Concatenate the first array with the modified second array
        finalWarrantyType = this.warrantyAllList?.concat(mergedWarrantyArray);
      }
    }
    //Assign lists
    this.warrantyAllList = addIds(finalWarrantyType);
    this.allPaytypeWarrantyList = warrantyItems;
    this.customAllList = addIds(finalCustomerType);
    this.allPaytypeCustomerList = payTypeItems;
    // Remove items from custlist if their paytype starts with "C" and the same paytype exists in warranylist
    this.customAllList = this.customAllList?.filter((custItem: any) => {
      return !(
        custItem.paytype?.startsWith("C") &&
        this.warrantyAllList?.some(
          (warrantyItem: any) => warrantyItem.paytype == custItem.paytype
        )
      );
    });
    this.warrantyAllList = this.warrantyAllList?.filter((warrItem: any) => {
      return !(
        warrItem.paytype?.startsWith("W") &&
        this.customAllList?.some(
          (customItem: any) => customItem.paytype == warrItem.paytype
        )
      );
    });
    this.customAllList = this.customAllList ? reduceToUniqueByPaytype(this.customAllList) : [];
    this.warrantyAllList = this.warrantyAllList
      ? reduceToUniqueByPaytype(this.warrantyAllList)
      : [];
    function reduceToUniqueByPaytype(list: any[]): any[] {
      return Array.from(
        list
          ?.reduce(
            (map, item) =>
              (!item.base_paytype && map.set(item.paytype, item)) ||
              (!map.has(item.paytype) && map.set(item.paytype, item)) ||
              map,
            new Map()
          )
          .values()
      );
    }
    // For customer description push item from schdeuler DB,when no data in local DB
    const customerDescription = new Map<string, string>();
    this.allPaytypeList?.forEach((item: any) => {
      customerDescription.set(item.paytype, item.description);
    });
    // Iterate through resPayListData and update customAllList if description is empty
    this.resPayListData?.forEach((resPayItem: any) => {
      if (!resPayItem.description) {
        // Find the corresponding description from allPaytypeList
        const description = customerDescription.get(resPayItem.paytype);
        if (description != undefined) {
          // Update the matching items in customAllList
          this.customAllList?.forEach((customItem: any) => {
            if (customItem.paytype === resPayItem.paytype) {
              customItem.description = description;
            }
          });
        }
      } else {
        payTypeItems?.forEach((customItem: any) => {
          if (customItem.paytype == resPayItem.paytype) {
            customItem.description = resPayItem.description;
          }
        });
      }
    });
    // For warranty Description push item from schdeuler DB,when no data in local DB
    const warrantyDescription = new Map<string, string>();
    this.allPaytypeList?.forEach((item: any) => {
      warrantyDescription.set(item.paytype, item.description);
    });
    // Iterate through resPayListData and update warrantyAllList if description is empty
    this.resPayListData?.forEach((resPayItem: any) => {
      if (!resPayItem.description) {
        // Find the corresponding description from allPaytypeList
        const description = warrantyDescription.get(resPayItem.paytype);
        if (description != undefined) {
          // Update the matching items in warrantyAllList
          this.warrantyAllList?.forEach((customItem: any) => {
            if (customItem.paytype === resPayItem.paytype) {
              customItem.description = description;
            }
          });
        }
      } else {
        warrantyItems?.forEach((customItem: any) => {
          if (customItem.paytype == resPayItem.paytype) {
            customItem.description = resPayItem.description;
          }
        });
      }
    });
    this.custCombinePayList = JSON.parse(JSON.stringify(this.customAllList));
    this.warrCombinePayList = JSON.parse(JSON.stringify(this.warrantyAllList));
    //for selected pay type list
    const filteredPayAllData: any = this.customAllList?.concat(
      this.warrantyAllList
    );
    // Filter items by category
    const filteredPayData = filteredPayAllData?.filter((item: any) => {
      // Filter by is_determined, category, and paytype
      return (
        item.is_determined &&
        (item.category === "WARRANTY" || item.category === "CUSTOMER")
      );
    });
    if (this.schedulerImportId && this.companyImportId) {
      this.schedulerCommonId = this.schedulerImportId;
      this.companyCommonId = this.companyImportId;
    } else {
      this.schedulerCommonId = this.schedulerFileId;
      this.companyCommonId = this.companyFileId;
    }
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      payListData: filteredPayData,
      operationName: "payListData",
    };
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      // requestData: JSON.stringify(requestUriData),
      "Content-Type": "application/json",
    });
    let schemaData: any = this.getSchemaURL(this.dmsList);
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
      .subscribe((res: any) => {
        if (res && res.data && res.data.paytypeAllData) {
          this.resPayListData = res.data.paytypeAllData;
        }
      });
    let activityDataSchedule = {
      activityName: "Fetch Custom Paytypes",
      activityType: "payListData ",
      activityDescription: `Filter pay type list for displaying customer and warranty types.
      )})`,
    };
    this.commonService.saveActivity("Scheduler Import", activityDataSchedule);
    // Add IDs
    this.getAvailableAndSelectedWarrPayTypes();
    this.getAvailableAndSelectedCustPayTypes();
    this.getAvailableAndSelectedCustPayTypesOnlyParts();
    this.getAvailableAndSelectedCustPayTypesOnlyLabor();
    this.getAvailableAndSelectedWarrPayTypesParts();
    this.getAvailableAndSelectedWarrPayTypesLabor();
  }
  getAvailableAndSelectedCustPayTypes() {
    this.custPaytypeAvailable = [];
    const custBothTypeData = this.customAllList?.filter(
      (item: any) =>
        item.is_labor_allowed == false && item.is_parts_allowed == false
    );
    this.custPaytypeSelected = custBothTypeData;
    this.custPaytypeAll = this.customAllList;
    this.warrPaytypeAll = this.warrantyAllList;
    for (let i = 0; i < this.custPaytypeAll.length; i++) {
      if (this.selectedCTypeLeftBox.indexOf(this.custPaytypeAll[i].id) == -1) {
      } else {
        this.custPaytypeSelected.push(this.custPaytypeAll[i]);
      }
    }
    this.getAllcustPaytypeAvailable();
    this.selectedCustomerPay = [];
    this.deSelectedCustomerPay = [];
    this.changeDetectorRefCheck();
  }
  getAvailableAndSelectedCustPayTypesOnlyParts() {
    this.custPaytypeAvailable = [];
    const partsData = this.customAllList.filter(
      (item: any) =>
        item.is_parts_allowed == false && item.is_labor_allowed == true
    );
    this.custPaytypeSelectedOnlyParts = partsData;
    for (let i = 0; i < this.custPaytypeAll?.length; i++) {
      if (
        this.selectedCTypeLeftBoxPartsOnly.indexOf(
          this.custPaytypeAll[i].id
        ) === -1
      ) {
      } else {
        this.custPaytypeSelectedOnlyParts.push(this.custPaytypeAll[i]);
      }
    }
    this.getAllcustPaytypeAvailable();
    this.selectedCustomerPay = [];
    this.deSelectedCustomerPay = [];
    this.changeDetectorRefCheck();
  }
  getAvailableAndSelectedCustPayTypesOnlyLabor() {
    this.custPaytypeAvailable = [];
    const laborData = this.customAllList.filter(
      (item: any) =>
        item.is_labor_allowed == false && item.is_parts_allowed == true
    );
    this.custPaytypeSelectedOnlyLabor = laborData;
    for (let i = 0; i < this.custPaytypeAll?.length; i++) {
      if (
        this.selectedCTypeLeftBoxLaborOnly.indexOf(this.custPaytypeAll[i].id) ==
        -1
      ) {
        //this.custPaytypeAvailable.push(this.custPaytypeAll[i]);
      } else {
        this.custPaytypeSelectedOnlyLabor.push(this.custPaytypeAll[i]);
      }
    }
    this.getAllcustPaytypeAvailable();
    this.selectedCustomerPay = [];
    this.deSelectedCustomerPay = [];
    this.changeDetectorRefCheck();
  }
  // Warranty Pay Type Dual Select Box
  getAvailableAndSelectedWarrPayTypes() {
    this.warrPaytypeAvailable = [];
    const warrPaytypeAll = this.warrantyAllList;
    this.warrPaytypeAll = warrPaytypeAll?.filter(
      (item: any) =>
        item.is_labor_allowed == false && item.is_parts_allowed == false
    );
    this.warrPaytypeSelected = this.warrPaytypeAll;
    const uniqueData = this.warrPaytypeSelected.filter(
      (item: any, index: any, self: any) =>
        index ===
        self.findIndex((t: any) => JSON.stringify(t) === JSON.stringify(item))
    );
    this.warrPaytypeSelected = uniqueData;
    for (let i = 0; i < this.warrPaytypeAll.length; i++) {
      if (this.selectedWTypeLeftBox.indexOf(this.warrPaytypeAll[i].id) == -1) {
        //this.warrPaytypeAvailable.push(this.warrPaytypeAll[i]);
      } else {
        this.warrPaytypeSelected.push(this.warrPaytypeAll[i]);
      }
    }
    this.getAllWarrPaytypeAvailable();
    this.selectedWarrantyPay = [];
    this.deSelectedWarrantyPay = [];
    this.changeDetectorRefCheck();
  }
  getAvailableAndSelectedWarrPayTypesParts() {
    this.warrPaytypeAvailable = [];
    const warrPaytypeAll = this.warrantyAllList;
    this.warrPaytypeAll = warrPaytypeAll?.filter(
      (item: any) =>
        item.is_parts_allowed == false && item.is_labor_allowed == true
    );
    this.warrPaytypeSelectedParts = this.warrPaytypeAll;
    const uniqueData = this.warrPaytypeSelectedParts.filter(
      (item: any, index: any, self: any) =>
        index ===
        self.findIndex((t: any) => JSON.stringify(t) === JSON.stringify(item))
    );
    this.warrPaytypeSelectedParts = uniqueData;
    for (let i = 0; i < this.warrPaytypeAll.length; i++) {
      if (
        this.selectedWTypeLeftBoxParts.indexOf(this.warrPaytypeAll[i].id) == -1
      ) {
        //this.warrPaytypeAvailable.push(this.warrPaytypeAll[i]);
      } else {
        this.warrPaytypeSelectedParts.push(this.warrPaytypeAll[i]);
      }
    }
    this.getAllWarrPaytypeAvailable();
    this.selectedWarrantyPay = [];
    this.deSelectedWarrantyPay = [];
    this.changeDetectorRefCheck();
  }
  getAvailableAndSelectedWarrPayTypesLabor() {
    this.warrPaytypeAvailable = [];
    const warrPaytypeAll = this.warrantyAllList;
    this.warrPaytypeAll = warrPaytypeAll?.filter(
      (item: any) =>
        item.is_labor_allowed == false && item.is_parts_allowed == true
    );
    this.warrPaytypeSelectedLabor = this.warrPaytypeAll;
    const uniqueData = this.warrPaytypeSelectedLabor.filter(
      (item: any, index: any, self: any) =>
        index ===
        self.findIndex((t: any) => JSON.stringify(t) === JSON.stringify(item))
    );
    this.warrPaytypeSelectedLabor = uniqueData;
    for (let i = 0; i < this.warrPaytypeAll.length; i++) {
      if (
        this.selectedWTypeLeftBoxLabor.indexOf(this.warrPaytypeAll[i].id) == -1
      ) {
        //this.warrPaytypeAvailable.push(this.warrPaytypeAll[i]);
      } else {
        this.warrPaytypeSelectedLabor.push(this.warrPaytypeAll[i]);
      }
    }
    this.getAllWarrPaytypeAvailable();
    this.selectedWarrantyPay = [];
    this.deSelectedWarrantyPay = [];
    this.changeDetectorRefCheck();
  }
  getAllcustPaytypeAvailable() {
    let remResultArray: any = [];
    const resultArrayRem = (this.custPaytypeSelected || []).concat(
      this.custPaytypeSelectedOnlyParts,
      this.custPaytypeSelectedOnlyLabor
    );
    if (resultArrayRem && resultArrayRem.length) {
      resultArrayRem.forEach((element: any) => {
        remResultArray.push(element.id);
      });
    }
    let includedList = [];
    const custTypeData = this.customAllList;
    this.custPaytypeAll = custTypeData?.filter(
      (item: any) =>
        item.is_labor_allowed == true && item.is_parts_allowed == true
    );
    for (let i = 0; i < this.custPaytypeAll?.length; i++) {
      if (remResultArray.includes(this.custPaytypeAll[i].id)) {
        includedList.push(this.custPaytypeAll[i]);
      } else {
        this.custPaytypeAvailable.push(this.custPaytypeAll[i]);
        
      }
    }
    this.changeDetectorRefCheck();
  }
  getAllWarrPaytypeAvailable() {
    let remResultArray: any = [];
    const resultArrayRem = this.warrPaytypeSelected.concat(
      this.warrPaytypeSelectedParts,
      this.warrPaytypeSelectedLabor
    );
    resultArrayRem.forEach((element: any) => {
      remResultArray.push(element.id);
    });
    let includedList = [];
    const warrPaytypeAll = this.warrantyAllList;
    this.warrPaytypeAll = warrPaytypeAll?.filter(
      (item: any) =>
        item.is_labor_allowed == true && item.is_parts_allowed == true
    );
    for (let i = 0; i < this.warrPaytypeAll.length; i++) {
      if (remResultArray.includes(this.warrPaytypeAll[i].id)) {
        includedList.push(this.custPaytypeAll[i]);
      } else {
        this.warrPaytypeAvailable.push(this.warrPaytypeAll[i]);
        const uniqueData = this.warrPaytypeAvailable.filter(
          (item: any, index: any, self: any) =>
            index ===
            self.findIndex(
              (t: any) => JSON.stringify(t) === JSON.stringify(item)
            )
        );
        this.warrPaytypeAvailable = uniqueData;
      }
    }
    this.changeDetectorRefCheck();
  }
  moveCPayRight() {
    const selectedItems = this.customAllList.filter((item: any) => {
      return this.selectedCustomerPay.includes(item.id);
    });
    selectedItems.forEach((item: any) => {
      // Check if the item doesn't already exist in custPaytypeSelected
      if (
        !this.custPaytypeSelected.some(
          (selectedItem) => selectedItem.id === item.id
        )
      ) {
        // Update the is_allowed_part field to false
        item.is_labor_allowed = false;
        item.is_parts_allowed = false;
        // Push the item into custPaytypeSelected
        this.custPaytypeSelected.push(item);
      }
    });
    this.getAvailableAndSelectedCustPayTypes();
    this.changeDetectorRefCheck();
  }
  moveCPayRightPartsOnly() {
    const selectedItems = this.customAllList.filter((item: any) => {
      return this.selectedCustomerPay.includes(item.id);
    });
    selectedItems.forEach((item: any) => {
      // Check if the item doesn't already exist in custPaytypeSelectedOnlyParts
      if (
        !this.custPaytypeSelectedOnlyParts.some(
          (selectedItem: any) => selectedItem.id === item.id
        )
      ) {
        // Update the is_allowed_part field to false
        item.is_labor_allowed = true;
        item.is_parts_allowed = false;
        // Push the item into custPaytypeSelectedOnlyParts
        this.custPaytypeSelectedOnlyParts.push(item);
      }
    });
    this.getAvailableAndSelectedCustPayTypesOnlyParts();
    this.changeDetectorRefCheck();
  }
  moveCPayRightlaborOnly() {
    const selectedItems = this.customAllList.filter((item: any) => {
      return this.selectedCustomerPay.includes(item.id);
    });
    selectedItems.forEach((item: any) => {
      // Check if the item doesn't already exist in custPaytypeSelectedOnlyParts
      if (
        !this.custPaytypeSelectedOnlyLabor.some(
          (selectedItem: any) => selectedItem.id === item.id
        )
      ) {
        // Update the is_allowed_part field to false
        item.is_labor_allowed = false;
        item.is_parts_allowed = true;
        // Push the item into custPaytypeSelectedOnlyParts
        this.custPaytypeSelectedOnlyLabor.push(item);
      }
    });
    this.getAvailableAndSelectedCustPayTypesOnlyLabor();
    this.changeDetectorRefCheck();
  }
  moveAllCPayRight() {
    this.selectedCustomerPay = [];
    for (let i = 0; i < this.custPaytypeAll?.length; i++) {
      this.selectedCustomerPay.push(this.custPaytypeAll[i].id);
    }
    for (let i = 0; i < this.selectedCustomerPay.length; i++) {
      this.selectedCTypeLeftBox.push(this.selectedCustomerPay[i]);
    }
    this.getAvailableAndSelectedCustPayTypes();
    this.changeDetectorRefCheck();
  }
  moveCPayLeft() {
    if (this.deSelectedCustomerPay) {
      const filteredItems = this.customAllList.filter((item: any) => {
        return item.is_parts_allowed == false && item.is_labor_allowed == false;
      });
      if (filteredItems.length == 0) {
        for (let i = 0; i < this.deSelectedCustomerPay.length; i++) {
          let index = this.selectedCTypeLeftBox.indexOf(
            this.deSelectedCustomerPay[i]
          );
          if (index > -1) {
            this.selectedCTypeLeftBox.splice(index, 1);
          }
        }
      } else {
        const selectedItems = this.customAllList.filter((item: any) => {
          return this.deSelectedCustomerPay.includes(item.id);
        });
        selectedItems.forEach((item: any) => {
          // Check if the item doesn't already exist in custPaytypeSelectedOnlyParts
          if (
            !this.custPaytypeAvailable.some(
              (selectedItem: any) => selectedItem.id === item.id
            )
          ) {
            // Update the is_allowed_part field to false
            item.is_labor_allowed = true;
            item.is_parts_allowed = true;
            // Push the item into custPaytypeSelectedOnlyParts
            this.custPaytypeAvailable.push(item);
          }
        });
      }
    }
    if (this.LselectedCustomerPay) {
      const filteredItems = this.customAllList.filter((item: any) => {
        return item.is_parts_allowed == false && item.is_labor_allowed == true;
      });
      if (filteredItems.length == 0) {
        for (let i = 0; i < this.LselectedCustomerPay.length; i++) {
          let index = this.selectedCTypeLeftBoxPartsOnly.indexOf(
            this.LselectedCustomerPay[i]
          );
          if (index > -1) {
            this.selectedCTypeLeftBoxPartsOnly.splice(index, 1);
          }
        }
      } else {
        const selectedItems = this.customAllList.filter((item: any) => {
          return this.LselectedCustomerPay.includes(item.id);
        });
        selectedItems.forEach((item: any) => {
          // Check if the item doesn't already exist in custPaytypeSelectedOnlyParts
          if (
            !this.custPaytypeAvailable.some(
              (selectedItem: any) => selectedItem.id === item.id
            )
          ) {
            // Update the is_allowed_part field to false
            item.is_labor_allowed = true;
            item.is_parts_allowed = true;
            // Push the item into custPaytypeSelectedOnlyParts
            this.custPaytypeAvailable.push(item);
          }
        });
      }
    }
    if (this.LdeSelectedCustomerPay) {
      const filteredItems = this.customAllList.filter((item: any) => {
        return item.is_parts_allowed == true && item.is_labor_allowed == false;
      });
      if (filteredItems.length == 0) {
        for (let i = 0; i < this.LdeSelectedCustomerPay.length; i++) {
          let index = this.selectedCTypeLeftBoxLaborOnly.indexOf(
            this.LdeSelectedCustomerPay[i]
          );
          if (index > -1) {
            this.selectedCTypeLeftBoxLaborOnly.splice(index, 1);
          }
        }
      } else {
        const selectedItems = this.customAllList.filter((item: any) => {
          return this.LdeSelectedCustomerPay.includes(item.id);
        });
        selectedItems.forEach((item: any) => {
          // Check if the item doesn't already exist in custPaytypeSelectedOnlyParts
          if (
            !this.custPaytypeAvailable.some(
              (selectedItem: any) => selectedItem.id === item.id
            )
          ) {
            // Update the is_allowed_part field to false
            item.is_labor_allowed = true;
            item.is_parts_allowed = true;
            // Push the item into custPaytypeSelectedOnlyParts
            this.custPaytypeAvailable.push(item);
          }
        });
      }
    }
    this.getAllcustPaytypeAvailable();
    this.getAvailableAndSelectedCustPayTypes();
    this.getAvailableAndSelectedCustPayTypesOnlyParts();
    this.getAvailableAndSelectedCustPayTypesOnlyLabor();
    this.changeDetectorRefCheck();
  }
  cPayDeSelection(type: any) {
    if (type === 1) {
      this.LselectedCustomerPay = [];
      this.LdeSelectedCustomerPay = [];
    } else if (type === 2) {
      this.deSelectedCustomerPay = [];
      this.LdeSelectedCustomerPay = [];
    } else if (type === 3) {
      this.deSelectedCustomerPay = [];
      this.LselectedCustomerPay = [];
    }
    this.changeDetectorRefCheck();
  }
  moveWPayRight() {
    const selectedItems = this.warrantyAllList.filter((item: any) => {
      return this.selectedWarrantyPay.includes(item.id);
    });
    selectedItems.forEach((item: any) => {
      // Check if the item doesn't already exist in custPaytypeSelected
      if (
        !this.warrPaytypeSelected.some(
          (selectedItem: any) => selectedItem.id === item.id
        )
      ) {
        // Update the is_allowed_part field to false
        item.is_labor_allowed = false;
        item.is_parts_allowed = false;
        // Push the item into custPaytypeSelected
        this.warrPaytypeSelected.push(item);
      }
    });
    this.getAvailableAndSelectedWarrPayTypes();
    this.changeDetectorRefCheck();
  }
  moveWPayRightParts() {
    const selectedItems = this.warrantyAllList.filter((item: any) => {
      return this.selectedWarrantyPay.includes(item.id);
    });
    selectedItems.forEach((item: any) => {
      // Check if the item doesn't already exist in warrPaytypeSelectedParts
      if (
        !this.warrPaytypeSelectedParts.some(
          (selectedItem: any) => selectedItem.id === item.id
        )
      ) {
        // Update the is_allowed_part field to false
        item.is_labor_allowed = true;
        item.is_parts_allowed = false;
        // Push the item into warrPaytypeSelectedParts
        this.warrPaytypeSelectedParts.push(item);
      }
    });
    this.getAvailableAndSelectedWarrPayTypesParts();
    this.changeDetectorRefCheck();
  }
  moveWPayRightLabor() {
    const selectedItems = this.warrantyAllList.filter((item: any) => {
      return this.selectedWarrantyPay.includes(item.id);
    });
    selectedItems.forEach((item: any) => {
      // Check if the item doesn't already exist in warrPaytypeSelectedParts
      if (
        !this.warrPaytypeSelectedLabor.some(
          (selectedItem: any) => selectedItem.id === item.id
        )
      ) {
        // Update the is_allowed_part field to false
        item.is_labor_allowed = false;
        item.is_parts_allowed = true;
        // Push the item into warrPaytypeSelectedParts
        this.warrPaytypeSelectedLabor.push(item);
      }
    });
    this.getAvailableAndSelectedWarrPayTypesLabor();
    this.changeDetectorRefCheck();
  }
  moveAllWPayRight() {
    this.selectedWarrantyPay = [];
    for (let i = 0; i < this.warrPaytypeAll.length; i++) {
      this.selectedWarrantyPay.push(this.warrPaytypeAll[i].id);
    }
    for (let i = 0; i < this.selectedWarrantyPay.length; i++) {
      this.selectedWTypeLeftBox.push(this.selectedWarrantyPay[i]);
    }
    this.getAvailableAndSelectedWarrPayTypes();
    this.changeDetectorRefCheck();
  }
  moveWPayLeft() {
    if (this.deSelectedWarrantyPay) {
      const filteredItems = this.warrantyAllList.filter((item: any) => {
        return item.is_parts_allowed == false && item.is_labor_allowed == false;
      });
      if (filteredItems.length == 0) {
        for (let i = 0; i < this.deSelectedWarrantyPay.length; i++) {
          let index = this.selectedWTypeLeftBox.indexOf(
            this.deSelectedWarrantyPay[i]
          );
          if (index > -1) {
            this.selectedWTypeLeftBox.splice(index, 1);
          }
        }
      } else {
        const selectedItems = this.warrantyAllList.filter((item: any) => {
          return this.deSelectedWarrantyPay.includes(item.id);
        });
        selectedItems.forEach((item: any) => {
          // Check if the item doesn't already exist in warrPaytypeSelectedParts
          if (
            !this.warrPaytypeAvailable.some(
              (selectedItem: any) => selectedItem.id === item.id
            )
          ) {
            // Update the is_allowed_part field to false
            item.is_labor_allowed = true;
            item.is_parts_allowed = true;
            // Push the item into warrPaytypeSelectedParts
            this.warrPaytypeAvailable.push(item);
          }
        });
      }
    }
    if (this.LselectedWarrantyPay) {
      const filteredItems = this.warrantyAllList.filter((item: any) => {
        return item.is_parts_allowed == false && item.is_labor_allowed == true;
      });
      if (filteredItems.length == 0) {
        for (let i = 0; i < this.LselectedWarrantyPay.length; i++) {
          let index_ = this.selectedWTypeLeftBoxParts.indexOf(
            this.LselectedWarrantyPay[i]
          );
          if (index_ > -1) {
            this.selectedWTypeLeftBoxParts.splice(index_, 1);
          }
        }
      } else {
        const selectedItems = this.warrantyAllList.filter((item: any) => {
          return this.LselectedWarrantyPay.includes(item.id);
        });
        selectedItems.forEach((item: any) => {
          // Check if the item doesn't already exist in warrPaytypeSelectedParts
          if (
            !this.warrPaytypeAvailable.some(
              (selectedItem: any) => selectedItem.id === item.id
            )
          ) {
            // Update the is_allowed_part field to false
            item.is_labor_allowed = true;
            item.is_parts_allowed = true;
            // Push the item into warrPaytypeSelectedParts
            this.warrPaytypeAvailable.push(item);
          }
        });
      }
    }
    if (this.LdeSelectedWarrantyPay) {
      const filteredItems = this.warrantyAllList.filter((item: any) => {
        return item.is_parts_allowed == true && item.is_labor_allowed == false;
      });
      if (filteredItems.length === 0) {
        for (let i = 0; i < this.LdeSelectedWarrantyPay.length; i++) {
          let index_ = this.selectedWTypeLeftBoxLabor.indexOf(
            this.LdeSelectedWarrantyPay[i]
          );
          if (index_ > -1) {
            this.selectedWTypeLeftBoxLabor.splice(index_, 1);
          }
        }
      } else {
        const selectedItems = this.warrantyAllList.filter((item: any) => {
          return this.LdeSelectedWarrantyPay.includes(item.id);
        });
        selectedItems.forEach((item: any) => {
          // Check if the item doesn't already exist in warrPaytypeSelectedParts
          if (
            !this.warrPaytypeAvailable.some(
              (selectedItem: any) => selectedItem.id === item.id
            )
          ) {
            // Update the is_allowed_part field to false
            item.is_labor_allowed = true;
            item.is_parts_allowed = true;
            // Push the item into warrPaytypeSelectedParts
            this.warrPaytypeAvailable.push(item);
          }
        });
      }
    }
    for (let i = 0; i < this.deSelectedWarrantyPay.length; i++) {
      let index = this.selectedWTypeLeftBox.indexOf(
        this.deSelectedWarrantyPay[i]
      );
      if (index > -1) {
        this.selectedWTypeLeftBox.splice(index, 1);
      }
    }
    for (let i = 0; i < this.LselectedWarrantyPay.length; i++) {
      let index = this.selectedWTypeLeftBoxParts.indexOf(
        this.LselectedWarrantyPay[i]
      );
      if (index > -1) {
        this.selectedWTypeLeftBoxParts.splice(index, 1);
      }
    }

    for (let i = 0; i < this.LdeSelectedWarrantyPay.length; i++) {
      let index = this.selectedWTypeLeftBoxLabor.indexOf(
        this.LdeSelectedWarrantyPay[i]
      );
      if (index > -1) {
        this.selectedWTypeLeftBoxLabor.splice(index, 1);
      }
    }

    this.getAvailableAndSelectedWarrPayTypes();
    this.getAvailableAndSelectedWarrPayTypesParts();
    this.getAvailableAndSelectedWarrPayTypesLabor();
    this.changeDetectorRefCheck();
  }
  moveAllWPayLeft() {
    this.selectedWarrantyPay = [];
    this.selectedWTypeLeftBox = [];
    this.getAvailableAndSelectedWarrPayTypes();
    this.changeDetectorRefCheck();
  }
  warrantyDeSelection(type: any) {
    if (type === 1) {
      this.LselectedWarrantyPay = [];
      this.LdeSelectedWarrantyPay = [];
    } else if (type === 2) {
      this.deSelectedWarrantyPay = [];
      this.LdeSelectedWarrantyPay = [];
    } else if (type === 3) {
      this.deSelectedWarrantyPay = [];
      this.LselectedWarrantyPay = [];
    }
    this.changeDetectorRefCheck();
  }
  /*end pay type */
  /*start department function */
  /*fetch department list */
  setDepartmentGrid() {
    const self = this;
    let data = [];
    this.getDepartment((status: boolean) => {
      // if (status) {
      let matchingDeptObjects: any;
      // this.rowDataDept = [];
      this.gridApiPRate?.showLoadingOverlay();
      //this.rowDataDept = this.deptList;
      let finalRowDataDept: any[] = []; // Initialize as an empty array
      if (this.allDepartmentList && this.allDepartmentList.length) {
        this.deptList.forEach((item2) => {
          // Find the matching item in allDepartmentList
          let matchingItem = this.allDepartmentList.find(
            (element: any) => element.dept === item2.dept
          );
          if (matchingItem) {
            // Update the item with additional fields from allDepartmentList
            Object.assign(item2, matchingItem);
          } else {
            // Apply default properties if no match is found
            Object.assign(item2, {
              is_allowed: true,
              is_determined: false,
              is_highlight: true,
              description: null,
            });
          }
        });
        this.rowDataDept = [...this.deptList];
      } else {
        // If allDepartmentList is empty or null, update each item in deptList with the specified fields
        this.rowDataDept = this.deptList.map((item) => ({
          ...item,
          is_allowed: true,
          is_determined: false,
          is_highlight: true,
        }));
      }
      // Check if finalRowDataDept has any items
      if (this.rowDataDept.length > 0) {
        this.gridApiPRate?.hideOverlay(); // Hide the loading overlay
      } else {
        this.gridApiPRate?.showNoRowsOverlay(); // Show "no data found" overlay
      }
      if (this.rowDataDept && this.rowDataDept.length > 0) {
        this.departmentFetchData = JSON.parse(JSON.stringify(this.rowDataDept));
      } else {
        console.log("rowDataDept is either undefined or empty");
      }
      // // Filter deptList to get objects where is_allowed is true
      const allowedDeptList = this.rowDataDept?.filter(
        (obj: any) => obj.is_allowed == true
      );
      // Push each of these objects into selectedDeptList
      if (allowedDeptList) {
        allowedDeptList.forEach((obj: any) => {
          this.selectedDeptList?.push(obj);
        });
      }
      let combinedDeptData = this.deptList?.filter((obj: any) => obj.dept);
      // Find corresponding objects in arr2
      matchingDeptObjects = this.allDepartmentList?.filter((obj: any) =>
        combinedDeptData?.some((naObj: any) => naObj.dept === obj.dept)
      );
      matchingDeptObjects?.forEach((department: any) => {
        const existingIndex = this.selectedDeptList?.findIndex(
          (item: any) => item.department_name == department.department_name
        );
        if (existingIndex != -1) {
          if (this.selectedDeptList && this.selectedDeptList[existingIndex]) {
            // Update is_allowed for existing entries
            this.selectedDeptList[existingIndex].is_allowed = true;
          }
        } else {
          // Push new unique entries to selectedDeptList
          this.selectedDeptList.push(matchingDeptObjects);
        }
      });
      const token = localStorage.getItem("token");
      if (this.schedulerImportId && this.companyImportId) {
        this.schedulerCommonId = this.schedulerImportId;
        this.companyCommonId = this.companyImportId;
      } else {
        this.schedulerCommonId = this.schedulerFileId;
        this.companyCommonId = this.companyFileId;
      }
      let schemaData: any = this.getSchemaURL(this.dmsList);
      let url = schemaData.url;
      let dmsList: any = schemaData.dmsCode;
      const requestUriData = {
        schedulerId: this.schedulerCommonId,
        companyImportId: this.companyCommonId,
        operationName: "allDepartmentsList",
        departmentList: !this.allDepartmentList ? [] : this.allDepartmentList,
      };
      const headers = new HttpHeaders({
        authorization: token ? `Bearer ${token}` : "",
        // requestData: JSON.stringify(requestUriData),
        "Content-Type": "application/json",
      });
      if (schemaData && schemaData.url && schemaData.dmsCode) {
        if (requestUriData) {
          this.http
            .post<any>(url, JSON.stringify(requestUriData), {
              headers: headers,
            })
            .subscribe((res: any) => {
              let activityDataSchedule = {
                activityName: "Fetch Departments List",
                activityType: "allDepartmentsList",
                activityDescription: `Fetch Departments List.
                  )})`,
              };
              this.commonService.saveActivity(
                "Scheduler Import",
                activityDataSchedule
              );
              if (res.status == "error") {
                this.showStatusMessage(res.message, "failed");
              } else {
                // this.ngWizardService.show(tabIndex);
              }
            });
        }
      } else {
        this.showStatusMessage("Request data is undefined", "failed");
      }
      this.rowDataDesc = this.allDepartmentList;
      this.columnDefsDeptDesc = [
        {
          headerName: "Department",
          field: "dept",
          sortable: true,
          width: 130,
          cellClass: (params: any) => {
            return "left-aligned-cell-t";
          },
          cellRenderer: function (params: any) {
            if (params.data.dept) {
              return params.data.dept;
            } else {
              return "";
            }
          },
        },
        {
          headerName: "Description",
          field: "description",
          sortable: true,
          width: 130,
          cellClass: (params: any) => {
            return "left-aligned-cell-t";
          },
          cellRenderer: function (params: any) {
            if (params.data.description) {
              return params.data.description;
            } else {
              return "";
            }
          },
        },
      ];
      //main department grid
      this.columnDefsDept = [
        {
          headerName: "Department",
          field: "dept",
          sortable: true,
          width: 100,
          cellClass: (params: any) => {
            return "left-aligned-cell-t";
          },
          cellStyle: (params: any) => {
            return params.data.is_highlight && !params.data.is_determined
              ? { backgroundColor: "#FFDB58" }
              : null;
          },
          cellRenderer: function (params: any) {
            if (params.data.dept) {
              return params.data.dept;
            } else {
              return "";
            }
          },
        },
        {
          headerName: "Allowed?",
          field: "is_allowed",
          width: 135,
          cellRendererFramework: CheckboxCellDeptComponent,
          cellClass: (params: any) => {
            return "left-aligned-cell-t";
          },
        },
      ];
    });
  }
  //get department details from RDS
  getDepartment(callback: any) {
    let dmsList;
    if (this.dmsList == "CDK Drive") {
      dmsList = "CDK3PA";
    } else {
      dmsList = this.dmsList;
    }
    let activityDataSchedule = {
      activityName: "Fetch Department from back end point",
      activityType: "Department List",
      activityDescription: `Fetch Department List from api.
      )})`,
    };
    this.commonService.saveActivity("Scheduler Import", activityDataSchedule);
    this.subscription = this.apollo
      .use("manageScheduler")
      .query({
        query: getDepartmentDetails,
        fetchPolicy: "network-only",
        variables: { inDms: dmsList },
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata: any) => {
          const result: any = listdata;
          const jsonData = result;
          //Extract the array from the object
          const jsonArrayDepartmenttype = JSON.parse(
            result.data.getDepartmentDetails
          );
          const filteredDeptData = jsonArrayDepartmenttype?.filter(
            (item: any) => item.is_determined
          );
          this.allDepartmentList = filteredDeptData;
          const token = localStorage.getItem("token");
          if (this.schedulerImportId && this.companyImportId) {
            this.schedulerCommonId = this.schedulerImportId;
            this.companyCommonId = this.companyImportId;
          } else {
            this.schedulerCommonId = this.schedulerFileId;
            this.companyCommonId = this.companyFileId;
          }
          const requestUriData = {
            schedulerId: this.schedulerCommonId,
            companyImportId: this.companyCommonId,
            // manufacturer:this.manufacturer,
            operationName: "allDepartments",
          };
          const headers = new HttpHeaders({
            authorization: token ? `Bearer ${token}` : "",
            // requestData: JSON.stringify(requestUriData),
            "Content-Type": "application/json",
          });
          let schemaData: any = this.getSchemaURL(this.dmsList);
          let url = schemaData.url;
          let dmsList: any = schemaData.dmsCode;
          if (schemaData && schemaData.url && schemaData.dmsCode) {
            if (requestUriData) {
              this.http
                .post<any>(url, JSON.stringify(requestUriData), {
                  headers: headers,
                })
                .subscribe((res: any) => {
                  let activityDataSchedule = {
                    activityName: "Fetch Departments List",
                    activityType: "allDepartments",
                    activityDescription: `Fetch Departments List in local.
                    )})`,
                  };
                  this.commonService.saveActivity(
                    "Scheduler Import",
                    activityDataSchedule
                  );
                  if (res.status == "success") {
                    this.deptList = res.data.departmentAllData;
                    if (!this.deptList?.length) {
                      this.deptList = [{ dept: "NA" }];
                    }
                  } else {
                    this.showStatusMessage(res.message, "failed");
                  }
                  // Assuming you want to invoke a callback function after processing the data
                  if (callback) {
                    callback(true);
                  }
                });
            }
          } else {
            this.showStatusMessage("Request data is undefined", "failed");
          }
          callback(true); // Call the callback function with status true
        },
        error: (err: any) => {
          callback(false); // Call the callback function with status false
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
    //return [this.allDepartmentList, this.deptList];
  }
  /*end department */
  /*start invoice region */
  getAllInvoiceData() {
    if (this.schedulerImportId && this.companyImportId) {
      this.schedulerCommonId = this.schedulerImportId;
      this.companyCommonId = this.companyImportId;
    } else {
      this.schedulerCommonId = this.schedulerFileId;
      this.companyCommonId = this.companyFileId;
    }
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      // manufacturer:this.manufacturer,
      operationName: "invoiceAllSequence",
    };
    let activityDataSchedule = {
      activityName: "Fetch Invoice List from back end point",
      activityType: "invoiceAllSequence",
      activityDescription: `Fetch Invoice List with request params ${requestUriData}.
    )})`,
    };
    this.commonService.saveActivity("Scheduler Import", activityDataSchedule);
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      // requestData: JSON.stringify(requestUriData),
      "Content-Type": "application/json",
    });
    let schemaData: any = this.getSchemaURL(this.dmsList);
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    this.rowData3 = [];
    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
      .subscribe((res: any) => {
        this.invoiceData = res?.data?.invoiceAllData;
        this.rowData3 = [];
        if (this.invoiceData && this.invoiceData.length) {
          this.invoiceData.forEach((item: any) => {
            item["status"] = true; // Replace "new_field" and "new_value" with your desired field name and value
            let endRoDate =  item["end_ro_date"]; 
            if (endRoDate) {
              const sixMonthsAgo = moment().subtract(6, "months"); 
              const endDate = moment(endRoDate);
              if (endDate.isBefore(sixMonthsAgo)) {
                item["status"] = false; 
                item.ro_list.forEach((ro: any) => {
                  this.rowData3.push(ro);
                  this.showOverlay = false;
                });
              }
            }
            
          });
        }
        this.rowDataInvoice = this.invoiceData;
        this.columnDefs1 = [
          {
            headerName: "",
            children: [
              {
                headerName: "#",
                field: "range_seq",
                width: 70,
                type: "rightAligned",
                rowDrag: true,
                sortable:true,
                filter:true,
                sort: 'asc', // Set initial sort order to ascending
                comparator: (valueA: any, valueB: any) => {
                  const numA = Number(valueA);
                  const numB = Number(valueB);
                  
                  // Handle cases where the values are not numbers (NaN)
                  if (isNaN(numA) && isNaN(numB)) {
                    return 0; // Both are not numbers, treat them as equal
                  } else if (isNaN(numA)) {
                    return 1; // valueA is not a number, so it should come after valueB
                  } else if (isNaN(numB)) {
                    return -1; // valueB is not a number, so it should come after valueA
                  }
              
                  return numA - numB; // Perform numeric comparison
                }
              },
              {
                headerName: "RO# Start",
                field: "start_ro",
                width: 110,
                type: "leftAligned",
              },
              {
                headerName: "RO# End",
                field: "end_ro",
                width: 110,
                type: "leftAligned",
              },
              {
                headerName: "Time Start",
                field: "start_ro_date",
                width: 120,              
                sortable: true,
                filter: true,
                comparator: (valueA: any, valueB: any) => {
                  const numericPartA = parseInt(valueA, 10);
                  const numericPartB = parseInt(valueB, 10);
                  if (isNaN(numericPartA) && isNaN(numericPartB)) {
                    // If both values are not numbers, compare them as strings
                    return valueA.localeCompare(valueB);
                  } else if (isNaN(numericPartA)) {
                    // If only valueA is not a number, it should come after valueB
                    return 1;
                  } else if (isNaN(numericPartB)) {
                    // If only valueB is not a number, it should come after valueA
                    return -1;
                  } else if (numericPartA === numericPartB) {
                    // If the numeric parts are equal, compare the entire strings
                    return valueA.localeCompare(valueB);
                  } else {
                    // Otherwise, compare the numeric parts
                    return numericPartA - numericPartB;
                  }
                },
                cellRenderer(params: any) {
                  const startRoOpenDate = params.data.start_ro_date;
                  return startRoOpenDate
                    ? moment(startRoOpenDate).format("MM-DD-YYYY")
                    : "";
                },
                type: "leftAligned",
              },
              {
                headerName: "Time End",
                field: "end_ro_date",
                width: 120,
                sortable: true,
                filter: true,
                comparator: (valueA: any, valueB: any) => {
                  const numericPartA = parseInt(valueA, 10);
                  const numericPartB = parseInt(valueB, 10);
                  if (isNaN(numericPartA) && isNaN(numericPartB)) {
                    // If both values are not numbers, compare them as strings
                    return valueA.localeCompare(valueB);
                  } else if (isNaN(numericPartA)) {
                    // If only valueA is not a number, it should come after valueB
                    return 1;
                  } else if (isNaN(numericPartB)) {
                    // If only valueB is not a number, it should come after valueA
                    return -1;
                  } else if (numericPartA === numericPartB) {
                    // If the numeric parts are equal, compare the entire strings
                    return valueA.localeCompare(valueB);
                  } else {
                    // Otherwise, compare the numeric parts
                    return numericPartA - numericPartB;
                  }
                },               
                cellRenderer(params: any) {
                  const endRoOpenDate = params.data.end_ro_date;
                  return endRoOpenDate
                    ? moment(endRoOpenDate).format("MM-DD-YYYY")
                    : "";
                },
                type: "leftAligned",
              },
              {
                headerName: "Count",
                field: "cnt_ro_list",
                sortable: true,
                width: 90,
                type: "rightAligned",
              },
              {
                headerName: "Suffix Count",
                field: "cnt_suffix_list",
                sortable: true,
                width: 100,
                type: "rightAligned",
              },
              {
                headerName: "Status",
                field: "status",
                width: 80,
                cellRenderer(params: any) {
                  let title = "";
                  let status = params.data.status;
                  
              
                  let html = `
                    <span class="pull-left" style="padding-right: 25px;">
                      <label class="switch" title="${title}" data-toggle="tooltip" data-animation="false" data-placement="left" data-note="Changed" id="hasDuJobsEnabled1" style="float: right;">
                        <input type="checkbox" value="${status}" id="hasDuJobsEnabled" class="hasDuJobsEnabled" ${
                          status ? "checked" : ""
                        }>
                        <span class="slider round 3"></span>
                        <span style="display:none;">
                          ${JSON.stringify(params.data)}
                        </span>
                      </label>
                    </span>
                  `;
                  return html;
                },
              },
            ],
          },
        ];

        let totalRows;
        let headerName;
        totalRows = this.rowData3.length;
        console.log('.totalRows=====VIL', totalRows);
        // Construct the headerName dynamically based on totalRows
        headerName = "Orphan ROs (Total: " + totalRows + ")";
   
        this.columnDefs3 = [
          {
            headerName: headerName,
            children: [
              {
                headerName: "RO#",
                field: "ro_no",
                type: "rightAligned",
                width: 110,
                 sortable: true,
                filter: true,
              },
              {
                headerName: "Open Date",
                field: "open_date",
                sortable: true,
                filter: true,
                comparator: (valueA: any, valueB: any) => {
                  // Ensure the date is parsed correctly, handling invalid or empty values
                  const dateA = moment(valueA, "MM-DD-YYYY", true); // 'true' ensures strict parsing
                  const dateB = moment(valueB, "MM-DD-YYYY", true);
    
                  if (!dateA.isValid() && !dateB.isValid()) return 0; // Both invalid, consider equal
                  if (!dateA.isValid()) return 1; // Invalid date should come after valid dates
                  if (!dateB.isValid()) return -1; // Invalid date should come after valid dates
    
                  if (dateA.isBefore(dateB)) return -1;
                  if (dateA.isAfter(dateB)) return 1;
                  return 0;
                },
                width: 130,
                cellRenderer: function (params: any) {
                  const data = params.value
                    ? moment(params.value).format("MM-DD-YYYY")
                    : "";
                  return data;
                },
              },
              {
                headerName: "Close Date",
                field: "close_date",
                width: 130,
                sortable: true,
                filter: true,
                comparator: (valueA: any, valueB: any) => {
                  // Ensure the date is parsed correctly, handling invalid or empty values
                  const dateA = moment(valueA, "MM-DD-YYYY", true); // 'true' ensures strict parsing
                  const dateB = moment(valueB, "MM-DD-YYYY", true);
    
                  if (!dateA.isValid() && !dateB.isValid()) return 0; // Both invalid, consider equal
                  if (!dateA.isValid()) return 1; // Invalid date should come after valid dates
                  if (!dateB.isValid()) return -1; // Invalid date should come after valid dates
    
                  if (dateA.isBefore(dateB)) return -1;
                  if (dateA.isAfter(dateB)) return 1;
                  return 0;
                },
                cellRenderer: function (params: any) {
                  const data = params.value
                    ? moment(params.value).format("MM-DD-YYYY")
                    : "";
                  return data;
                },
              },
            ],
          },
        ];
      });
  }
  //get ro count fetch function from local
  getROInvoiceData() {
    if (this.schedulerImportId && this.companyImportId) {
      this.schedulerCommonId = this.schedulerImportId;
      this.companyCommonId = this.companyImportId;
    } else {
      this.schedulerCommonId = this.schedulerFileId;
      this.companyCommonId = this.companyFileId;
    }
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      operationName: "invoiceROSequence",
    };
    let activityDataSchedule = {
      activityName: "Fetch Invoice RO List from back end point",
      activityType: "invoiceROSequence",
      activityDescription: `Fetch invoice RO sequence with request params ${requestUriData}.
    )})`,
    };
    this.commonService.saveActivity("Scheduler Import", activityDataSchedule);
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      // requestData: JSON.stringify(requestUriData),
      "Content-Type": "application/json",
    });
    let schemaData: any = this.getSchemaURL(this.dmsList);
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
      .subscribe((res: any) => {
        let activityDataSchedule = {
          activityName: "Fetch invoice sequence list",
          activityType: "invoiceROSequence",
          activityDescription: `Fetch invoice list in local.
          )})`,
        };
        this.commonService.saveActivity(
          "Scheduler Import",
          activityDataSchedule
        );
        if (res && res.data && res.data.invoiceROData) {
          this.rowDataRoInvoice = res.data.invoiceROData;
        } else {
          this.rowDataRoInvoice = []; // Or handle the error appropriately
        }
        this.columnDefs2 = [
          {
            headerName: "100 RO sequence band summary",
            children: [
              {
                headerName: "RO# Start",
                field: "start_ro",
                width: 110,
                type: "leftAligned",
                sortable: true,
                filter: true,
                comparator: (valueA: any, valueB: any) => {
                  const numericPartA = parseInt(valueA, 10);
                  const numericPartB = parseInt(valueB, 10);
                  if (isNaN(numericPartA) && isNaN(numericPartB)) {
                    // If both values are not numbers, compare them as strings
                    return valueA.localeCompare(valueB);
                  } else if (isNaN(numericPartA)) {
                    // If only valueA is not a number, it should come after valueB
                    return 1;
                  } else if (isNaN(numericPartB)) {
                    // If only valueB is not a number, it should come after valueA
                    return -1;
                  } else if (numericPartA === numericPartB) {
                    // If the numeric parts are equal, compare the entire strings
                    return valueA.localeCompare(valueB);
                  } else {
                    // Otherwise, compare the numeric parts
                    return numericPartA - numericPartB;
                  }
                },
              },
              {
                headerName: "RO# End",
                field: "end_ro",
                width: 110,
                type: "leftAligned",
                sortable: true,
                filter: true,

                comparator: (valueA: any, valueB: any) => {
                  const numericPartA = parseInt(valueA, 10);
                  const numericPartB = parseInt(valueB, 10);
                  if (isNaN(numericPartA) && isNaN(numericPartB)) {
                    // If both values are not numbers, compare them as strings
                    return valueA.localeCompare(valueB);
                  } else if (isNaN(numericPartA)) {
                    // If only valueA is not a number, it should come after valueB
                    return 1;
                  } else if (isNaN(numericPartB)) {
                    // If only valueB is not a number, it should come after valueA
                    return -1;
                  } else if (numericPartA === numericPartB) {
                    // If the numeric parts are equal, compare the entire strings
                    return valueA.localeCompare(valueB);
                  } else {
                    // Otherwise, compare the numeric parts
                    return numericPartA - numericPartB;
                  }
                },
              },
              {
                headerName: "Total RO# Count",
                field: "total_ro_cnt",
                width: 110,
                type: "rightAligned",
                sortable: true,
                filter: true,
                comparator: (valueA: any, valueB: any) => {
                  const numA = Number(valueA);
                  const numB = Number(valueB);
                  return numA - numB; // Numeric comparison
                },
              },
              {
                headerName: "Misssing RO# Count",
                field: "missing_ro_cnt",
                width: 115,
                type: "rightAligned",
                sortable: true,
                filter: true,
                comparator: (valueA: any, valueB: any) => {
                  const numA = Number(valueA);
                  const numB = Number(valueB);
                  return numA - numB; // Numeric comparison
                },
              },
              {
                headerName: "Present RO# Count",
                field: "present_ro_cnt",
                width: 115,
                type: "rightAligned",
                sortable: true,
                filter: true,
                comparator: (valueA: any, valueB: any) => {
                  const numA = Number(valueA);
                  const numB = Number(valueB);
                  return numA - numB; // Numeric comparison
                },
              },
              {
                headerName: "Min Open Date",
                field: "min_open_date",
                width: 120,
                sortable: true,
                filter: true,
                comparator: (valueA: any, valueB: any) => {
                  const numericPartA = parseInt(valueA, 10);
                  const numericPartB = parseInt(valueB, 10);
                  if (isNaN(numericPartA) && isNaN(numericPartB)) {
                    // If both values are not numbers, compare them as strings
                    return valueA.localeCompare(valueB);
                  } else if (isNaN(numericPartA)) {
                    // If only valueA is not a number, it should come after valueB
                    return 1;
                  } else if (isNaN(numericPartB)) {
                    // If only valueB is not a number, it should come after valueA
                    return -1;
                  } else if (numericPartA === numericPartB) {
                    // If the numeric parts are equal, compare the entire strings
                    return valueA.localeCompare(valueB);
                  } else {
                    // Otherwise, compare the numeric parts
                    return numericPartA - numericPartB;
                  }
                },
                cellRenderer(params: any) {
                  const minOpenDate = params.data.min_open_date;
                  return minOpenDate
                    ? moment(minOpenDate).format("MM-DD-YYYY")
                    : "";
                },
                type: "leftAligned",
              },
              {
                headerName: "Max Open Date",
                field: "max_open_date",
                width: 120,
                sortable: true,
                filter: true,
                comparator: (valueA: any, valueB: any) => {
                  const numericPartA = parseInt(valueA, 10);
                  const numericPartB = parseInt(valueB, 10);
                  if (isNaN(numericPartA) && isNaN(numericPartB)) {
                    // If both values are not numbers, compare them as strings
                    return valueA.localeCompare(valueB);
                  } else if (isNaN(numericPartA)) {
                    // If only valueA is not a number, it should come after valueB
                    return 1;
                  } else if (isNaN(numericPartB)) {
                    // If only valueB is not a number, it should come after valueA
                    return -1;
                  } else if (numericPartA === numericPartB) {
                    // If the numeric parts are equal, compare the entire strings
                    return valueA.localeCompare(valueB);
                  } else {
                    // Otherwise, compare the numeric parts
                    return numericPartA - numericPartB;
                  }
                },
                cellRenderer(params: any) {
                  const maxOpenDate = params.data.max_open_date;
                  return maxOpenDate
                    ? moment(maxOpenDate).format("MM-DD-YYYY")
                    : "";
                },
                type: "leftAligned",
              },
              {
                headerName: "Open RO# Count",
                field: "open_ro_count",
                width: 110,
                type: "rightAligned",
                sortable: true,
                filter: true,
                comparator: (valueA: any, valueB: any) => {
                  const numA = Number(valueA);
                  const numB = Number(valueB);
                  return numA - numB; // Numeric comparison
                },
              },
              {
                headerName: "Closed RO# Count",
                field: "closed_ro_count",
                width: 115,
                type: "rightAligned",
                sortable: true,
                comparator: (valueA: any, valueB: any) => {
                  const numA = Number(valueA);
                  const numB = Number(valueB);
                  return numA - numB; // Numeric comparison
                },
              },
              {
                headerName: "Void RO# Count",
                field: "void_ro_count",
                width: 110,
                type: "rightAligned",
                sortable: true,
                filter: true,
                comparator: (valueA: any, valueB: any) => {
                  const numA = Number(valueA);
                  const numB = Number(valueB);
                  return numA - numB; // Numeric comparison
                },
              },
            ],
          },
        ];
      });
  
  }
  //for status toggle section
  onStatusChanged(result: any, status: any) {
    result.status = status;
    this.rowData3 = [];
    setTimeout(() => {
      this.gridApi1.sizeColumnsToFit();
      this.gridApi2.sizeColumnsToFit();
      this.gridApi3.sizeColumnsToFit();
    },0);
        this.invoiceData.forEach((data: any) => {
      // Check if start_ro matches and update the status accordingly
      if (data.start_ro == result.start_ro) {
        if (result.status == false) {
          data.status = false; // or handle this according to your needs
        } else {
          data.status = true;
        }
      }
      if (data.status == false) {
        data.ro_list.forEach((ro: any) => {
          this.rowData3.push(ro);
          this.showOverlay = false;
        });
      } else {
        this.showOverlay = false;
      }
    });
    let totalRows;
    let headerName;
    if (this.rowData3) {
      totalRows = this.rowData3.length;
      // Construct the headerName dynamically based on totalRows
      headerName = "Orphan ROs (Total: " + totalRows + ")";
    } else {
      setTimeout(() => {
        this.gridApi1.hideOverlay();
      }, 500);
    }
    this.columnDefs3 = [
      {
        headerName: headerName,
        children: [
          {
            headerName: "RO#",
            field: "ro_no",
            type: "leftAligned",
            width: 110,
            sortable: true,
            filter: true,
            comparator: (valueA: any, valueB: any) => {
              const numericPartA = parseInt(valueA, 10);
              const numericPartB = parseInt(valueB, 10);
              if (isNaN(numericPartA) && isNaN(numericPartB)) {
                // If both values are not numbers, compare them as strings
                return valueA.localeCompare(valueB);
              } else if (isNaN(numericPartA)) {
                // If only valueA is not a number, it should come after valueB
                return 1;
              } else if (isNaN(numericPartB)) {
                // If only valueB is not a number, it should come after valueA
                return -1;
              } else if (numericPartA === numericPartB) {
                // If the numeric parts are equal, compare the entire strings
                return valueA.localeCompare(valueB);
              } else {
                // Otherwise, compare the numeric parts
                return numericPartA - numericPartB;
              }
            },
          },
          {
            headerName: "Open Date",
            field: "open_date",
            width: 130,
            cellRenderer: function (params: any) {
              const data = params.value
                ? moment(params.value).format("MM-DD-YYYY")
                : "";
              return data;
            },
          },
          {
            headerName: "Close Date",
            field: "close_date",
            width: 130,
            cellRenderer: function (params: any) {
              const data = params.value
                ? moment(params.value).format("MM-DD-YYYY")
                : "";
              return data;
            },
          },
        ],
      },
    ];
    // Handle the status change event here
  }
  onGridReady1(params: any) {
    this.gridApi1 = params.api;
    this.gridColumnApi1 = params.columnApi;
  }
  onFirstDataRenderedInvoice(params: any) {
    this.gridApi1 = params.api;
    this.gridApi1.sizeColumnsToFit();
    this.changeDetectorRefCheck();
  }
  onGridReady2(params: any) {
    this.gridApi2 = params.api;
    this.gridColumnApi2 = params.columnApi;
  }
  onFirstDataRenderedRoInvoice(params: any) {
    this.gridApi2 = params.api;
    this.gridApi2.sizeColumnsToFit();
    this.changeDetectorRefCheck();
  }
  onGridReady3(params: any) {
    this.gridApi3 = params.api;
    this.gridColumnApi3 = params.columnApi;
  }
  onFirstDataRenderedSumInvoice(params: any) {
    this.gridApi3 = params.api;
    this.gridApi3.sizeColumnsToFit();
    this.changeDetectorRefCheck();
  }
  savePayTypes(paytypeList: any, callback: any) {
    const filteredSchedulerData = this.allPaytypeList.filter(
      (item: any) =>
        item.category === "CUSTOMER" || item.category === "WARRANTY"
    );
    const filteredLocalData = paytypeList.filter(
      (item: any) =>
        item.base_paytype === "Customer" || item.base_paytype === "Warranty"
    );
    const newDataToAdd: any[] = [];
    const paytypesInArr1 = filteredSchedulerData.map(
      (item: any) => item.paytype
    );
    // Filter arr2 to include only those items whose paytype is not in arr1
    const newArray = filteredLocalData.filter(
      (item: any) => !paytypesInArr1.includes(item.paytype)
    );
    const updatedPayListArray = paytypeList.map((item: any) => {
      // Destructure the item to extract fields
      const { base_paytype, ...rest } = item;
      // Determine if base_paytype exists
      if (base_paytype) {
        // Add category field with uppercase value of base_paytype
        rest.category = base_paytype.toUpperCase();
      }
      return rest; // Return updated object
    });
    const activityData1 = {
      activityName: "update storePaytypeDetailsUpdation",
      activityDescription: "update storePaytypeDetailsUpdation: started",
      activityData: [JSON.stringify(updatedPayListArray)],
    };
    this.commonService.saveActivity(this.currentPageName, activityData1);
    const subscription = this.apollo.use("manageScheduler").mutate({
      mutation: storePaytypeDetailsUpdation,
      variables: {
        payTypeDet: JSON.stringify(updatedPayListArray),
      },
    });
    subscription.pipe(takeUntil(this.subscription$)).subscribe(
      ({ data }) => {
        NProgress.done();
        const result: any = data;
        const activityData2 = {
          activityName: "update storePaytypeDetailsUpdation",
          activityDescription: "update storePaytypeDetailsUpdation: completed",
          activityData: data,
        };
        this.commonService.saveActivity(this.currentPageName, activityData2);
        const obj = JSON.parse(result["storePaytypeDetailsUpdation"]["json"]);
        if (obj.status === this.constantService.SUCCESS_MESSAGE) {
          if (callback) {
            callback(true);
          }
        } else {
          const message = this.constantService.CANNOT_FETCH_DATA;
          swal({
            title: message,
            type: "warning",
            confirmButtonClass: "btn-warning pointer",
            confirmButtonText: this.constantService.CLOSE,
            allowOutsideClick: false
          });
          if (callback) {
            callback(false);
          }
        }
      },
      (err: any) => {
        NProgress.done();
        const message = this.constantService.CANNOT_FETCH_DATA;
        swal({
          title: message,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
          allowOutsideClick: false
        });
        this.commonService.errorCallback(err, this);
        if (callback) {
          callback(false);
        }
      }
    );
    this.subscriptionList.push(subscription);
    this.changeDetectorRefCheck();
    return subscription;
  }
  updateDeptType(allowedDepartments: any) {
    if (this.dmsList == "CDK Drive") {
      this.dmsList = "CDK3PA";
    }
    const updatedDeptData = allowedDepartments?.map((item: any) => ({
      ...item,
      dms: this.dmsList,
      is_determined: true, // Ensure this is set correctly
    }));

    this.showSpinnerStoreButton = true;
    // NProgress.start();
    const subscription = this.apollo
      .use("manageScheduler")
      .mutate({
        mutation: departmentDetailsInfo,
        variables: {
          depDet: JSON.stringify(updatedDeptData),
        },
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata:any) => {
          NProgress.done();
          const result: any = listdata;
          if (listdata) {
            const data = listdata.data as {
              departmentDetailsInfo: { json: string };
            };
            const status = JSON.parse(data.departmentDetailsInfo.json).status;
            // Now you can check the value of 'status' to see if it's "success"
            if (status == "success") {
              this.showSpinnerStoreButton = false;
            } else {
            }
          }
        },
        error: (err:any) => {
          NProgress.done();
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });

    this.subscriptionList.push(subscription);
    return subscription;
  }
  onFirstDataRenderedPRate(params: any) {
    this.gridApiPRate = params.api;
    this.gridApiPRate.sizeColumnsToFit();
    this.changeDetectorRefCheck();
  }
  onGridReadyDept(params: any) {
    this.gridApiPRate = params.api;
    this.gridColumnApiPRate = params.columnApi;
    this.gridApiPRate.showLoadingOverlay();
    this.changeDetectorRefCheck();
  }
  onFirstDataRenderedDept(params: any) {
    this.gridApiPRate = params.api;
    this.gridApiPRate.sizeColumnsToFit();
    this.changeDetectorRefCheck();
  }
  onGridReadyPRate(params: any) {
    this.gridApiPRate = params.api;
    this.gridColumnApiPRate = params.columnApi;
    this.changeDetectorRefCheck();
  }
  gridCellClicked(params: any) {
    if (params.colDef.headerName === "RO Count") {
      const showCustomPopover = this.columnDefsDept.filter(
        (column: any) =>
          column.headerName === params.colDef.headerName &&
          column.showCustomPopover !== undefined
      );
      if (showCustomPopover.length) {
        const splitBy =
          showCustomPopover.length > 0
            ? showCustomPopover[0].showCustomPopover.splitBy
            : ",";
        let clickedCellValue = params.data.ro_list;
        if (clickedCellValue) {
          clickedCellValue = clickedCellValue.toString();
          const regex = new RegExp(eval("/" + splitBy + "/g"));
          const result = clickedCellValue.replace(regex, "\r\n");
          this.noteHeading = "RO Numbers";
          this.allNotes = result;
        }
        params.event.preventDefault();
        params.event.stopPropagation();
      }
    } else if (params.colDef.headerName === "Qual. RO Count") {
      const showCustomPopover = this.columnDefsDept.filter(
        (column: any) =>
          column.headerName === params.colDef.headerName &&
          column.showCustomPopover !== undefined
      );
      if (showCustomPopover.length) {
        const splitBy =
          showCustomPopover.length > 0
            ? showCustomPopover[0].showCustomPopover.splitBy
            : ",";
        let clickedCellValue = params.data.qulaified_ro_list;
        if (clickedCellValue) {
          clickedCellValue = clickedCellValue.toString();
          const regex = new RegExp(eval("/" + splitBy + "/g"));
          const result = clickedCellValue.replace(regex, "\r\n");
          this.noteHeading = "Qual. RO Numbers";
          this.allNotes = result;
        }
        params.event.preventDefault();
        params.event.stopPropagation();
      }
    }
  }
  /* section for makes */
  onGridReadyMakes(params: any) {
    this.gridApiMakes = params.api;
    this.gridColumnApiMakes = params.columnApi;
    this.gridApiMakes.showLoadingOverlay();
    this.changeDetectorRefCheck();
  }
  onFirstDataRenderedMakes(params: any) {
    this.gridApiMakes = params.api;
    this.gridApiMakes.sizeColumnsToFit();
    this.changeDetectorRefCheck();
  }
  showStatusMessage(message: any, statusType: any) {
    if (statusType === "success") {
      this.toastrService.success(message);
    } else {
      this.toastrService.error(message);
    }
     if (
      message === "Customer paytype should not be excluded." ||
      message === "Warranty paytype should not be excluded."
    ) {
      this.toastrService.error(message, '', {
        timeOut: 1800, // 1800ms for the specific message
      });
    } else {
      this.toastrService.error(message, '', {
        timeOut: 5000, // 5000ms for other messages
      });
    }
    this.changeDetectorRefCheck();
  }
  gridCellClickedMakes(params: any) {
    if (params.colDef.headerName == "Allowed?") {
      this.updateModelGridData(params.data);
    }
    this.changeDetectorRefCheck();
  }
  changeDetectorRefCheck() {
    this.changeDetectorRef.markForCheck();
  }
  /* section for model */
  onGridReadyModel(params: any) {
    this.gridApiModel = params.api;
    this.gridColumnApiModel = params.columnApi;
    this.gridApiModel.showLoadingOverlay();
    this.changeDetectorRefCheck();
  }
  onFirstDataRenderedUnassignMake(params: any) {
    this.gridApiUnassignMake = params.api;
    this.gridApiUnassignMake.sizeColumnsToFit();
    this.changeDetectorRefCheck();
  }
  /* section for model */
  onGridReadyUnassignMake(params: any) {
    this.gridApiUnassignMake = params.api;
    this.gridColumnApiUnassignMake = params.columnApi;
    this.gridApiUnassignMake.showLoadingOverlay();
    this.changeDetectorRefCheck();
  }
  onFirstDataRenderedModel(params: any) {
    this.gridApiModel = params.api;
    this.gridApiModel.sizeColumnsToFit();
    this.changeDetectorRefCheck();
  }
  /**
   * callback function for store group deselection
   *
   */
  OnDeSelectStoreGroup(item: any) {
    this.store = [];
    this.storeFilterList = [];
    this.getGroupFilterList();
  }
  containsObject(obj: any, list: any) {
    let i;
    if (list && list.length && obj) {
      for (i = 0; i < list.length; i++) {
        if (list[i].id === obj.id) {
          return true;
        }
      }
    }
    return false;
  }
  /**
   * getGroupFilterList function will collect the group list for filtering purpose
   *
   */
  getGroupFilterList() {
    this.storeGroupFilterList = [];
    for (let i = 0; i < this.storeGroupList.length; i++) {
      const companyName = this.storeGroupList[i].mageGroupName;
      const mageGroupCode = this.storeGroupList[i].mageGroupCode;
      const mageGroupName = this.storeGroupList[i].mageGroupName
        ? this.storeGroupList[i].mageGroupName
        : "";
      const mageStoreCode = this.storeGroupList[i].mageStoreCode;
      const mageStoreName = this.storeGroupList[i].mageStoreName;
      const projectId = this.storeGroupList[i].projectId;
      const secondProjectId = this.storeGroupList[i].secondaryProjectId;
      const companyId = this.storeGroupList[i].companyId;
      const parentName = this.storeGroupList[i].companyName;
      const mageManufacturer = this.storeGroupList[i].mageManufacturer;
      const state =  this.storeGroupList[i].state;
      const mageProjectName = this.storeGroupList[i].projectName;
      const mageProjectType =  this.storeGroupList[i].projectType;
      const secondaryProjectType = this.storeGroupList[i].secondaryProjectType;
      const secondaryProjectName =  this.storeGroupList[i].secondaryProjectName;
      if (companyName) {
        const obj = {
          id: companyName,
          itemName: companyName,
          mageGroupCode: mageGroupCode,
          mageGroupName: mageGroupName,
          mageStoreCode: mageStoreCode,
          mageStoreName: mageStoreName,
          projectId: projectId,
          companyId: companyId,
          parentName: parentName,
          secondProjectId: secondProjectId,
          mageManufacturer:mageManufacturer,
          state:state,
          mageProjectName:mageProjectName,
          mageProjectType:mageProjectType,
          secondaryProjectType:secondaryProjectType,
          secondaryProjectName:secondaryProjectName
        };
        if (!this.containsObject(obj, this.storeGroupFilterList)) {
          this.storeGroupFilterList.push(obj);
        }
      }
    }
    this.storeGroupFilterList = this.sortListAsc(this.storeGroupFilterList);
    if (this.storeGroupList) {
      this.showSpinnerStoreButton = false;
    } else {
      this.showSpinnerStoreButton = true;
    }
  }
  /**
   * sortListAsc function will sort the list in ascending order.
   *
   */
  sortListAsc(temp: any) {
    temp = temp
      .filter((item: any, index: number) => index < temp.length && item && typeof item.itemName === 'string')
      .sort((a: any, b: any): any => {
        const x = (a?.itemName ?? "").toLowerCase().replace(/^[^a-z0-9]*/g, "");
        const y = (b?.itemName ?? "").toLowerCase().replace(/^[^a-z0-9]*/g, "");
        return x < y ? -1 : x > y ? 1 : 0;
      });
    return temp;
  }
  
  OnDeSelectStore(item: any) {
    this.storeFileList=[];
    this.fileList = [];
    this.showAll = false;
    this.showAll=false;
    if (!this.containsObject(item, this.store)) {
      this.store.push(item);
    }
    this.changeDetectorRefCheck();
  }

  /**
   * callback function for DM version selection
   *
   */
  onSelectversion(item: any) {
    let itemFiltered: any = this.versionFilterList.filter(
      (res) => res.id == item.id
    );
    this.version = itemFiltered;
    if (!this.containsObject(item, this.version)) {
      this.version.push(itemFiltered[0]);
    }
  }


  /**
   * callback function for DM version deselection
   *
   */
  OnDeSelectversion(item: any) {
    if (!this.containsObject(item, this.version)) {
      this.version.push(item);
    }
    this.changeDetectorRefCheck();
  }
  /**
   * callback function for store group selection
   *
   */
  onSelectStoreGroup(item: any) {
    if (this.fileList) {
      this.fileList = [];
    }
    this.showAll = false;
    if (this.version) {
      this.version = [];
    }
    this.selectStoreGroup(item, () => {
      this.changeDetectorRefCheck();
    });
  }
  /**
   * callback function for store group deselection
   *
   */
  onDeSelectStoreGroup(item: any) {
    if (this.fileList) {
      this.fileList = [];
    }
    this.showAll = false;
    if (this.version) {
      this.version = [];
    }
    if (this.store) {
      this.store = [];
    }
    this.showAll = false; 
  }
  selectStoreGroup(item: any, callback: any) {
    this.loadAllStore = true;
    this.storeList = [];
    this.storeFilterList = [];
    this.store = [];
    this.storeFlag = false;
    if (!this.containsObject(item, this.storeGroup)) {
      this.storeGroup.push(item);
    }
    let selectedGroupFilter: any = this.storeGroupList.filter(function (res) {
      return res.sgId == item.id;
    });
    this.selectedGroup = selectedGroupFilter[0];
    let itemFilter: any = this.storeGroupFilterList.filter(function (res) {
      return res.id == item.id; //updated as per new array list
    });
    this.getStoreList("", itemFilter[0], () => {
      this.loadAllStore = false;
      this.getStoreFilterList();
      this.storeGroupFlag = true;
      if (callback) {
        callback();
      }
    });
  }
  /*end invoice region */
  closeHaltReasonModal() {
    if ($("#haltReasonModal").is(":visible")) {
      $("#haltReasonModal").modal("hide");
    }
  }
  openHaltReasonModal() {
    if (!$("#haltReasonModal").is(":visible")) {
      $("#haltReasonModal").modal("show");
    }
  }
  closeHaltReasonModal1() {
    this.makeDataGroup = [];

    if ($("#haltReasonModal1").is(":visible")) {
      $("#haltReasonModal1").modal("hide");
    }
  }
  openHaltReasonModal1() {
    this.isMakeAsGlobal = true;
    if (!$("#haltReasonModal1").is(":visible")) {
      $("#haltReasonModal1").modal("show");
    }
  }
  closeHaltReasonModal2() {
    this.modelDataGroup = [];
    if ($("#haltReasonModal2").is(":visible")) {
      $("#haltReasonModal2").modal("hide");
    }
  }
  openHaltReasonModal2() {
    this.isManufacturerAsGlobal=true;
    if (!$("#haltReasonModal2").is(":visible")) {
      $("#haltReasonModal2").modal("show");
    }
  }
  toggleAccordion(index: any) {}
  onCheckBoxChange(result: any, flag: boolean): void {
    this.selectedManufacturerList.push(
      result.rowdata.manufacturer.toUpperCase()
    );
    if (
      !this.selectedManufacturerList.includes(
        result.rowdata.manufacturer.toUpperCase()
      )
    ) {
      this.selectedManufacturerList.push(
        result.rowdata.manufacturer.toUpperCase()
      );
    } else {
      if (!result.rowdata.is_allowed) {
        let filteredArray = this.selectedManufacturerList.filter(
          (element: any) =>
            element !== result.rowdata.manufacturer.toUpperCase()
        );
        this.selectedManufacturerList = filteredArray;
      }
    }
    if (result["gridIdentifier"] == "gridModel") {
      this.onModelFilterChanged();
    } else {
    }
  }
  onModelFilterChanged(): void {
    let allValuesTrue = true;
    this.gridApiModel.forEachNodeAfterFilter((node: any) => {
      const columnValue = node.data["is_allowed"];
      if (columnValue !== true) {
        allValuesTrue = false;
        return;
      }
    });
  }
  /* section for model */
  onGridReadyPay(params: any) {
    this.gridApiPay = params.api;
    this.gridColumnApiModel = params.columnApi;
    // this.gridApiModel.showLoadingOverlay();
    this.changeDetectorRefCheck();
  }
  onFirstDataRenderedPay(params: any) {
    this.gridApiPay = params.api;
    this.gridApiPay.sizeColumnsToFit();
    this.changeDetectorRefCheck();
  }
  closepayTypeModal() {
    if ($("#paytypemodal").is(":visible")) {
      $("#paytypemodal").modal("hide");
    }
  }
  openpayTypeModal() {
    if (!$("#paytypemodal").is(":visible")) {
      $("#paytypemodal").modal("show");
    }
  }
  openDeptTypeModal() {
    if (!$("depttypemodal").is(":visible")) {
      $("#depttypemodal").modal("show");
    }
  }
  closeDeptTypeModal() {
    if ($("#depttypemodal").is(":visible")) {
      $("#depttypemodal").modal("hide");
    }
  }
  addMakeList(makeDataGroup: any, makeList: any) {
    console.log("aDD MAKE lIST$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",makeDataGroup);
    if (!makeDataGroup) {
      this.showValidationErr = true;
      this.showSpinnerStoreButton = true;
      return;
    }
    this.showSpinnerStoreButton = true;
    this.schedulerCommonId = this.schedulerImportId || this.schedulerFileId;
    this.companyCommonId = this.companyImportId || this.companyFileId;
    // Define the data to be sent to the backend
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      inManufacturerList: makeDataGroup[0].itemName,
      inMakeList: makeList,
      operationName: "addMakeList",
      companyName:this.companyListName
    };
    const rowMakedata = this.rowDataMakes;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      Authorization: token ? `Bearer ${token}` : "",
      "Content-Type": "application/json",
    });
    let schemaData: any = this.getSchemaURL(this.dmsList);
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    this.http
      .post<any>(url, requestUriData, { headers: headers })
      .subscribe((res: any) => {
        console.log("aDD MAKE LIST RES####################################################",res);
        let activityDataSchedule = {
          activityName: "Add new make list",
          activityType: "addMakeList",
          activityDescription: `Add new make list in manufactur table"
          )})`,
        };
        this.commonService.saveActivity(
          "Scheduler Import",
          activityDataSchedule
        );
        let allMakeData: any = this.allMakeManufacturersList;
        // Update the is_allowed property based on manufacturersToUpdate
        this.rowDataMakes.forEach((item) => {
          if (this.selectedManufacturerList.includes(item.manufacturer)) {
            item.is_allowed = true;
          } else {
            item.is_allowed = false;
          }
        });
        rowMakedata.forEach((item2: any) => {
          // Find the corresponding item in arr1 based on the manufacturer
          const matchedItem = rowMakedata.find(
            (item1: any) => item1.manufacturer === item2.manufacturer
          );

          // If a matching item is found, replace is_allowed in arr2
          if (matchedItem) {
            item2.is_allowed = matchedItem.is_allowed;
          }
        });
        if (res.status == "success") {
          this.closeHaltReasonModal1();
          this.showSpinnerStoreButton = false;
          let makeNewList = makeList;
          let inManufacturerList = makeDataGroup[0].id;
          //For make grid setrowdata
          // Find the target manufacturer in rowDataMakes
          const targetManufacturer = this.rowDataMakes.find(
            (manufacturer: any) =>
              manufacturer.manufacturer == inManufacturerList
          );
          if (targetManufacturer) {
            // Find the make in resmakedata
            const resMake = this.resMakeListData.find(
              (res: any) => res.make == makeNewList
            );
            if (resMake) {
              // Add the make to the makeList of the target manufacturer
              const newMakeList = [...targetManufacturer.makeList];
              if (!newMakeList.includes(resMake.make)) {
                newMakeList.push(resMake.make);
              }
              // Update the targetManufacturer object
              targetManufacturer.makeList = newMakeList;

              // Update the count of the target manufacturer
              targetManufacturer.count = (
                parseInt(targetManufacturer.count, 10) +
                parseInt(resMake.count, 10)
              ).toString();
            }
          
          } else {
            // Add new manufacturer if not found
            const resMake = this.resMakeListData.find(
              (res: any) => res.make === makeNewList
            );
            const newManufacturer = {
              manufacturer: inManufacturerList,
              makeList: [makeNewList],
              count: resMake ? resMake.count : "1", // Fetch the count from resMake or default to '1'
              is_allowed: false,
            };

            this.rowDataMakes.push(newManufacturer);
          }
          const uniqueManufacturers = [
            ...new Set(this.rowDataModel.map((item: any) => item.manufacturer)),
          ];

          //Update is_allowed property in rowDataMakes
          let nonZeroManufacturersList: any = this.rowDataMakes.map(
            (item: any) => ({
              ...item,
              is_allowed: uniqueManufacturers.includes(item.manufacturer),
            })
          );
          this.rowDataMakes = nonZeroManufacturersList.filter(
            (item: any) => parseInt(item.count) > 0
          );
          this.gridApiMakes.setRowData(this.rowDataMakes);
          //For model grid setrowdata
          // Check if inMakeList exists in resmakedata
          let foundMake = this.resMakeListData.find(
            (item: any) => item.make == makeNewList
          );
          if (
            foundMake &&
            this.selectedManufacturerList.includes(inManufacturerList)
          ) {
            // Create a new row with the found make and count
            let newMakeRow = {
              manufacturer: inManufacturerList,
              makeList: foundMake.make,
              count: foundMake.count,
              is_allowed: true,
            };

            // Add the new row to rowdatamodel
            this.rowDataModel.push(newMakeRow);
          }
          this.gridApiModel.setRowData(this.rowDataModel);
          this.importModelGroupFilterList = [];
          for (let i = 0; i < this.rowDataModel.length; i++) {
            const make = this.rowDataModel[i].manufacturer;
            const model = this.rowDataModel[i].makeList;
            const count = this.rowDataModel[i].count;
            if (make) {
              const obj = {
                id: model,
                itemName: model,
                model: model,
                count: count,
              };
              if (!this.containsObject(obj, this.importModelGroupFilterList)) {
                this.importModelGroupFilterList.push(obj);
              }
            }
          }
          console.log(" this.importModelGroupFilterList%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%", this.importModelGroupFilterList);
          this.importModelGroupFilterList = this.sortListAsc(
            this.importModelGroupFilterList
          );
          console.log("this.importModelGroupFilterList test2$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.importModelGroupFilterList);
          //For unassignmake grid setrowdata
          this.rowDataUnassignMake = this.rowDataUnassignMake.filter(
            (item: any) => item.make != makeNewList
          );
          this.gridApiUnassignMake.setRowData(this.rowDataUnassignMake);
       console.log("test@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
        if(this.isMakeAsGlobal){
                    
            this.addmakeAsGlobal(requestUriData,(resData:any)=>{
              if(resData.status =="success"){
                swal({
                  title: resData.message,
                  type: "success",
                  confirmButtonClass: "btn-success pointer",
                  confirmButtonText: this.constantService.CLOSE,
                  allowOutsideClick: false
                });

              }else if(resData.status=="failed"){
                swal({
                  title: resData.message,
                  type: "warning",
                  confirmButtonClass: "btn-warning pointer",
                  confirmButtonText: this.constantService.CLOSE,
                  allowOutsideClick: false
                });

              }else{
                swal({
                  title: resData.message,
                  type: "warning",
                  confirmButtonClass: "btn-warning pointer",
                  confirmButtonText: this.constantService.CLOSE,
                  allowOutsideClick: false
                });

              }
          });
  
          }else{
            swal({
              title: "Added " + makeList + "  as a Make Successfully",
              type: "success",
              confirmButtonClass: "btn-success pointer",
              confirmButtonText: this.constantService.CLOSE,
              allowOutsideClick: false
            });

          }

        }else{
           
          swal({
            title: "Assign make failed",
            type: "Something went wrong",
            confirmButtonClass: "btn-warning pointer",
            confirmButtonText: this.constantService.CLOSE,
            allowOutsideClick: false
          });

          
        }
      });
  }
  updateModelGridData(data: any): void {
    const rowMakeData = this.rowDataModel;
    //dropdown for other make
    const isMakeExists = this.rowDataModel?.some(
      (item: any) => item["manufacturer"] == data?.manufacturer
    );
    let modelData = this.rowDataModel;
    console.log("modelData$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",modelData);
    this.getManufactureListFromLocalDD("", "");
    if (data?.is_allowed && !isMakeExists && data?.makeList?.length) {
      const newData = data.makeList.map((make: any) => ({
        manufacturer: data.manufacturer,
        makeList: make,
        count: data.count,
        is_allowed: true,
      }));
      modelData = [...modelData, ...newData];
      const uniqueEntries: any = {};
      const newDataMakeList: any = [];
      modelData.forEach((item: any) => {
        if (!uniqueEntries[item.makeList]) {
          uniqueEntries[item.makeList] = item;
        }
      });
      Object.values(uniqueEntries).forEach((item: any) => {
        if (Array.isArray(item.makeList) && item.makeList) {
          item.makeList.forEach((make: any) => {
            newDataMakeList.push({
              manufacturer: item.manufacturer,
              makeList: make,
              count: item.count,
              is_allowed: item.is_allowed,
            });
          });
        } else {
          newDataMakeList.push(item);
        }
      });

      const totalMakeList = new Set(
        this.resMakeListData.map((item: any) => item.make)
      );
      const updatedModel = newDataMakeList.map((item: any) => ({
        ...item,
        count:
          Array.isArray(item.makeList) && item.makeList.length === 1
            ? item.count
            : totalMakeList.has(item.makeList)
            ? this.resMakeListData.find((i: any) => i.make === item.makeList)
                .count
            : "0",
      }));
      updatedModel.forEach((updateItem: any) => {
        const matchingRow = this.rowDataModel.find(
          (rowItem: any) =>
            rowItem.manufacturer === updateItem.manufacturer &&
            rowItem.makeList === updateItem.makeList
        );
        if (matchingRow) {
          updateItem.count = matchingRow.count;
        }
      });
      modelData = updatedModel;
    } else if (!data?.is_allowed && isMakeExists) {
      modelData = this.rowDataModel.filter(
        (item: any) => item["manufacturer"] != data?.manufacturer
      );
    }
    this.rowDataModel = modelData;
    this.checkedListItems = this.rowDataModel;
    this.gridApiModel.setRowData(this.rowDataModel);
    
    //dropdown for other make
    console.log("this.rowDataModel$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.rowDataModel);
    console.log("this.allmanufactureList$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.allMakeList);
    this.importModelGroupFilterList = [];
    // for (let i = 0; i < this.rowDataModel.length; i++) {
    //   const make = this.rowDataModel[i].manufacturer;
    //   const model = this.rowDataModel[i].makeList;
    //   const count = this.rowDataModel[i].count;
    //   if (make) {
    //     const obj = {
    //       id: model,
    //       itemName: model,
    //       model: model,
    //       count: count,
    //     };
    //     if (!this.containsObject(obj, this.importModelGroupFilterList)) {
    //       this.importModelGroupFilterList.push(obj);
    //     }
    //   }
    // }
    const allMakes1 = [
      ...new Set(
        this.allMakeList.flatMap((edge: any) => edge.node.makeList)
      )
    ];
    
    
    
    console.log("Filtered make all>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",allMakes1);
    

      for (let i = 0; i < allMakes1.length; i++) {
      const make = allMakes1[i];
      const model = allMakes1[i];
      const count = 0;
      if (make) {
        const obj = {
          id: model,
          itemName: model,
          model: model,
          count: count,
        };
        if (!this.containsObject(obj, this.importModelGroupFilterList)) {
          this.importModelGroupFilterList.push(obj);
        }
      }
    }
   

    this.importModelGroupFilterList = this.sortListAsc(
      this.importModelGroupFilterList
    );
    console.log("this.importModelGroupFilterList test3$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.importModelGroupFilterList);
  }
  addOtherMake(currentMakeList: any, newMakeList: any) {
    if (newMakeList == undefined || newMakeList == null) {
      this.showValidationErr = true;
      this.showSpinnerStoreButton = true;
    } else {
      this.showSpinnerStoreButton = false;
    }
    this.showSpinnerStoreButton = true;
    this.currentMake = currentMakeList;
    this.newMake = newMakeList[0].itemName;
    if (this.schedulerImportId && this.companyImportId) {
      this.schedulerCommonId = this.schedulerImportId;
      this.companyCommonId = this.companyImportId;
    } else {
      this.schedulerCommonId = this.schedulerFileId;
      this.companyCommonId = this.companyFileId;
    }
    // Define the data to be sent to the backend
    const requestUriData = {
      currentMake: this.currentMake,
      newMake: this.newMake,
      operationName: "addOtherMake",
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
    };
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      // requestData: JSON.stringify(requestUriData),
      "Content-Type": "application/json",
    });
    let schemaData: any = this.getSchemaURL(this.dmsList);
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    this.http
      .post<any>(url, requestUriData, { headers: headers })
      .subscribe((res: any) => {
        let allMakeData: any = this.allMakeManufacturersList;
        let activityDataSchedule = {
          activityName: "Add other make List",
          activityType: "addOtherMake",
          activityDescription: `Add other make list in makes table"
          )})`,
        };
        this.commonService.saveActivity(
          "Scheduler Import",
          activityDataSchedule
        );
        if (res.status == "success") {
          this.showSpinnerStoreButton = false;
          this.closeHaltReasonModal2();
          // Find the current make count in resmakedata
          let currentMakeObj = this.resMakeListData.find(
            (item: any) => item.make == this.currentMake
          );
          let currentMakeCount = currentMakeObj
            ? parseInt(currentMakeObj.count)
            : 0;
          // Find the manufacturer in rowDataMake that has the newMake in its makeList
          let manufacturerWithNewMake = this.rowDataMakes.find((item) =>
            item.makeList.includes(this.newMake)
          );
          if (manufacturerWithNewMake) {
            // Add the current make count to the manufacturer's count
            manufacturerWithNewMake.count = (
              parseInt(manufacturerWithNewMake.count) + currentMakeCount
            ).toString();
            this.gridApiMakes.setRowData(this.rowDataMakes);
          }
          // Find the new make in rowDataModel
          let newMakeModel = this.rowDataModel.find(
            (item: any) => item.makeList == this.newMake
          );

          if (newMakeModel) {
            // Update the count of the new make
            newMakeModel.count = (
              parseInt(newMakeModel.count) + currentMakeCount
            ).toString();
          }
          this.gridApiModel.setRowData(this.rowDataModel);
          //For unassignmake grid setrowdata
          this.rowDataUnassignMake = this.rowDataUnassignMake.filter(
            (item: any) => item.make != this.currentMake
          );
          this.gridApiUnassignMake.setRowData(this.rowDataUnassignMake);
        
          if(this.isManufacturerAsGlobal){
            this.saveManufacturer(requestUriData,(resData:any)=>{
              if(resData.status =="success"){
                swal({
                  title: resData.message,
                  type: "success",
                  confirmButtonClass: "btn-success pointer",
                  confirmButtonText: this.constantService.CLOSE,
                  allowOutsideClick: false
                });
              }else if(resData.status =="failed"){
                swal({
                  title: resData.message,
                  type: "warning",
                  confirmButtonClass: "btn-warning pointer",
                  confirmButtonText: this.constantService.CLOSE,
                  allowOutsideClick: false
                });
              }else{
                swal({
                  title: "Something went wrong",
                  type: "warning",
                  confirmButtonClass: "btn-warning pointer",
                  confirmButtonText: this.constantService.CLOSE,
                  allowOutsideClick: false
                });
              }                      
            });
          }else{
            swal({
              title:
                "Assigned " +
                currentMakeList +
                " " +
                "to" +
                " " +
                this.newMake +
                " " +
                "successfully",
              type: "success",
              confirmButtonClass: "btn-success pointer",
              confirmButtonText: this.constantService.CLOSE,
              allowOutsideClick: false
            });
          }
        }else{
          swal({
            title: "Assign manufacturer failed",
            type: "warning",
            confirmButtonClass: "btn-warning pointer",
            confirmButtonText: this.constantService.CLOSE,
            allowOutsideClick: false
          });
        }

      });
  }
  getSchemaData(selectedStore: any, mode: any) {
    console.log(selectedStore,"selectedStoreselectedStore")
    this.fileLoader = true;
    if (this.fileLoader) {
      this.fileList = [];
    }
    //schema creation
    let urlSchema = environment.fileImportCdkUrl;
    const tokenSchema = localStorage.getItem("token");
    const fileHeaders = new HttpHeaders({
      authorization: tokenSchema ? `Bearer ${tokenSchema}` : "",
      requestData: "",
      "Content-Type": "application/json",
    });

// Conditionally create the request body
const requestBody = mode ? { mode: mode } : "";
    this.http
      .post<any>(urlSchema, requestBody, { headers: fileHeaders })
      .subscribe((res: any) => {
        let companyID: any = "";
        this.storeFileList = [];
        let activityDataSchedule = {
          activityName: "getFileList",
          activityType: "Get Schema Data",
          activityDescription: `Fecth filename based on schema"
          )})`,
        };
        this.commonService.saveActivity(
          "Scheduler Import",
          activityDataSchedule
        );
        if (mode == "fromURI") {
          if (this.base64QueryString) {
            let base64String = this.base64QueryString;
            let previousString;
            do {
              previousString = base64String;
              base64String = decodeURIComponent(base64String);
            } while (base64String !== previousString);
            const decodedString = atob(base64String);
            const decodedObject = JSON.parse(decodedString);
            companyID = decodedObject.companyId;
          }
        } else {
          const matchingItem = this.storeFilterList.find(
            (item) => item.itemName == selectedStore
          );
          console.log(matchingItem,this.storeFilterList,"selectedStoreselectedStore")

          companyID = matchingItem?.companyID;
          this.loading = false;
        }
        this.selectedStoreFilterList = this.storeFilterList
        .filter((item) => item.companyID == companyID); // Returns an array
      
        let matchingFiles = res.data.filter((file: any) => {
          this.fileShowList=res.data;
          // Extract the companyID from the fileName
          const match = file.fileName.match(/_(\d+)-ETL\.zip$/);
          console.log(companyID,match,"selectedStoreselectedStore")

          if (match) {
            this.fileLoader = false;
            const companyId = match[1];
            return companyId == companyID;
            }
          return false;
        });
        if (matchingFiles.length) {
          // Function to extract date part from the filename
          function extractDate(filename: string): string {
            const datePattern = /(\d{14,})_/;
            const match = filename.match(datePattern);
            return match ? match[1] : "";
          }
          // Sort the matchingFiles array based on the extracted date
          matchingFiles.sort((a: any, b: any) => {
            const dateA = extractDate(a.fileName);
            const dateB = extractDate(b.fileName);
            return dateB.localeCompare(dateA); // Reverse order for descending
          });
          this.storeFileList = [];
          matchingFiles.forEach((file: any) => {
            // Push each file into the storeFileList
            this.storeFileList.push({
              id: file.fileName,
              itemName: file.fileName,
            });
          });
          console.log(  this.storeFileList,"selectedStoreselectedStore")

        } else {
          if (mode !== "fromURI") {
            this.showStatusMessage(
              "No files available for import for this store",
              "failed"
            );
          }
        }
      });
  }
  getFetchSchemaCheck() {
    if (this.companyFileId && this.schedulerFileId) {
      const requestUriData = {
        schedulerId: this.schedulerFileId,
        companyImportId: this.companyFileId,
        operationName: "getFetchData",
      };
      let url: any;
      if (!this.tabVisit.import) {
        this.gridApiMakes.showLoadingOverlay();
        this.gridApiModel.showLoadingOverlay();
        this.gridApiUnassignMake.showLoadingOverlay();
      }

      this.getStoreCompanyDetails(this.companyFileId, (res: any) => {
        if(res){
        //common fetch query operation
        let schemaData: any = this.getSchemaURL(this.dmsList);
        let url = schemaData.url;
        let dmsList: any = schemaData.dmsCode;
        const token = localStorage.getItem("token");
        const headers = new HttpHeaders({
          authorization: token ? `Bearer ${token}` : "",
          "Content-Type": "application/json",
          // requestdata: JSON.stringify(requestUriData),
        });
        this.http
          .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
          .subscribe((res: any) => {
            if (res.data.schemaResult == "Connected Schema") {
              this.getAllManufacturersList(() => {
                this.setManufacturerListGrid(false);
                this.setMakeListGrid(false);
                this.setUnassignedListGrid();
              });
            }
          });
        }
      });
    }
  }
  /* start store region* */
  isFieldValidCreateSchedule(field: string) {
    let retValue: any = null;
    retValue =
      !this.createSchedule.get(field)?.valid &&
      this.createSchedule.get(field)?.touched;
    return retValue;
  }
  displayFieldCssCreateSchedule(field: string) {
    return {
      "has-danger": this.isFieldValidCreateSchedule(field),
    };
  }
  /**
   * callback function for file  selection
   *
   */
  onSelectFileversion(item: any) {
    // Unsubscribe from previous subscription if it exists
    if (this.stepChangedSubscription) {
      this.stepChangedSubscription.unsubscribe();
    }
    //for refreshing make grid
    this.selectedManufacturerList = [];
    this.tabVisit.import = false;
    this.tabVisit.make = false;
    this.tabVisit.paytype = false;
    this.tabVisit.dept = false;
    this.tabVisit.iseq = false;
    this.tabVisit.finish = false;
    /** set scheduler ID */
    const parts = item.id.split("_");
    const schedulerCommonId = parts[parts.length - 2]; // Get the second last part
    const companyCommonId = parts[parts.length - 1].split("-")[0];
    this.schedulerCommonId = schedulerCommonId;
    this.companyCommonId = companyCommonId;
    this.fileName = item.id;
    /** End Section */
  }
  /**
   * callback function for file deselection
   *
   */
  OnDeSelectFileversion(item: any) {
    this.changeDetectorRefCheck();
  }

  addmakeAsGlobal(data:any,callback:any){
    console.log("Add make as global$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.isMakeAsGlobal);

   if(this.isMakeAsGlobal){
      try {
        const currentUserObj = JSON.parse(localStorage.getItem("currentUser") || "{}");
        const userName = currentUserObj?.displayName || "";
        data.userName = userName;
        data.isAdmin = false;
        const payload = data;
        const token = localStorage.getItem("token");
        console.log("currentUserObj$$$$$$$$$$$$$$$$$$$$$$$$$$$$",currentUserObj);
        console.log("payLoad$$$$$$$$$$$$$$$$$$$$$$$$$$$$",payload);
        const headers = new HttpHeaders({
          authorization: token ? `Bearer ${token}` : "",
        });
        this.http.post(environment.updateMake, payload, { headers }).subscribe(
          (res: any) => {

            if(res.status){
             
              callback(res);
            }else{
              callback(res);
        
            }
  
  
        },
          (error) => {
            console.error("Error saving user:", error);
            let res ={
              status:false,
              message:"Something Went Wrong"
            };

            callback(res);

          }
        );
      } catch (error) {
        console.error("Error parsing user data:", error);
        let res ={
          status:false,
          message:"Something Went Wrong"
        };
        callback(res);
      }
    }
}

saveManufacturer(data:any,callback:any){
  if(this.isManufacturerAsGlobal){
    try {
      const currentUserObj = JSON.parse(localStorage.getItem("currentUser") || "{}");
      const userName = currentUserObj?.userPrincipalName || "";
      data.userName = userName;
      const payload = data;
      const token = localStorage.getItem("token");
      const headers = new HttpHeaders({
        authorization: token ? `Bearer ${token}` : "",
      });
      this.http.post(environment.updateManufacturer, payload, { headers }).subscribe(
        (res: any) => {
          if(res.status){
            callback(res);
          }else{
            callback(res);
          }

      },
        (error) => {
          console.error("Error saving user:", error);
          let res ={
            status:false,
            message:"Internal Server Error"
          }
          callback(res);
        }
      );
    } catch (error) {
      console.error("Error parsing user data:", error);
             

      let res ={
        status:false,
        message:"Internal Server Error"
      }
     callback(res);



    }
  }
}
  /**
   * callback function for store selection
   *
   */
  onSelectStore(items: any) {
    this.loading = true;
    // this.store = [];
    this.getSchemaData(items.itemName, "direct");
    this.getStoreFilterList();
    if (!this.containsObject(items, this.store)) {
      this.store.push(items);
    }
    const selectedStoreFilter: any = this.storeList.filter(function (res) {
      return res.stId == items.id;
    });
    this.selectedStore = selectedStoreFilter[0];
    const matchedItems = this.storeList.filter(
      (storeitem) => storeitem.companyName == items.id
    );
    if (matchedItems.length > 0) {
      const obj = {
        id: matchedItems[0].dmsCode,
        itemName: matchedItems[0].dmsCode,
      };
      if (!this.containsObject(obj, this.versionFilterList)) {
        this.versionFilterList.push(obj);
      }
      this.loading = false;
      this.onSelectversion(obj);
    }
    this.versionFilterList = this.sortListAsc(this.versionFilterList);
    const filteredData = this.storeList.filter((item) =>
      items.id.includes(item.companyName)
    );
    const companyIds = filteredData.map((item) => item.companyId);
    this.manufacturer = filteredData.map((item) => item.mageManufacturer);
    this.companyIds = companyIds;
    this.storeFlag = true;
  }

  /**
   * getStoreFilterList function will collect the store list for filtering purpose
   *
   */
  getStoreFilterList() {
    this.storeFilterList = [];
    for (let i = 0; i < this.storeList.length; i++) {
      const companyID = this.storeList[i].companyId;
      const storeName = this.storeList[i].companyName;
      const stId = this.storeList[i].companyName;
      const dmsCode = this.storeList[i].dmsCode;
      const mageGroupCode = this.storeList[i].mageGroupName;
      const mageStoreName = this.storeList[i].mageStoreName;
      const mageStoreCode = this.storeList[i].mageStoreCode;
      const thirdPartyUsername = this.storeList[i].thirdPartyUsername;
      const glAccountCompanyID = this.storeList[i].dealerbuiltSourceId;
      const stateCode = this.storeList[i].state;
      const projectId = this.storeList[i].projectId;
      const secondProjectId = this.storeList[i].secondaryProjectId;
      const solve360Update = this.storeList[i].solve360Update;
      const buildProxies = this.storeList[i].buildProxies;
      const includeMetaData = this.storeList[i].includeMetaData;
      const extractAccountingData = this.storeList[i].extractAccountingData;
      const dualProxy = this.storeList[i].dualProxy;
      const mageManufacturer = this.storeList[i].mageManufacturer;
      if (stId) {
        const obj = {
          id: stId,
          itemName: `${storeName} [${thirdPartyUsername}]`,
          mageGroupCode: mageGroupCode,
          mageStoreName: mageStoreName,
          mageStoreCode: mageStoreCode,
          thirdPartyUsername: thirdPartyUsername,
          stateCode: stateCode,
          projectId: projectId,
          solve360Update: solve360Update,
          buildProxies: buildProxies,
          includeMetaData: includeMetaData,
          companyID: companyID,
          glAccountCompanyID: glAccountCompanyID,
          extractAccountingData: extractAccountingData,
          mageManufacturer: mageManufacturer,
          dualProxy: dualProxy,
          secondProjectId: secondProjectId,
          dmsCode: dmsCode,
        };
        if (!this.containsObject(obj, this.storeFilterList)) {
          this.storeFilterList.push(obj);
        }
      }
    }
    this.storeFilterList = this.sortListAsc(this.storeFilterList);
    let activityDataSchedule = {
      activityName: "CDK3PA : Store filter list",
      activityType: "Store filter list",
      activityDescription: `CDK3PA Store filter list: ${JSON.stringify(
        this.storeFilterList
      )}`,
    };
    this.commonService.saveActivity("Manage CDK3PA", activityDataSchedule);
  }

  /**
   * getStoreList function fetch Stores list for the selected StoreGroup
   *
   */
  getStoreList(type: any, item: any, callback: any) {
    // const groupCode = item.mageGroupCode;
    const groupCode = item.mageGroupName;
    this.loading = true;
    this.storeLoading = true;
    this.storeList = [];
    this.storeList = this.jobGroupList
      .filter((item: any, index: number) => index < this.jobGroupList.length)
      .filter(
        (item: any, index: number) =>
          item.mageGroupName != null &&
          item.mageGroupName === groupCode &&
          item.thirdPartyUsername !== null
      );
    //.filter((item: any, index: number) => item.dmsCode.toLowerCase() == type.toLowerCase());
    this.loading = false;
    this.storeLoading = false;
    if (callback) {
      callback();
    }
  }
  /* end store region* */

  addDumpData() {
    let showStatus:any;
    showStatus = this.showAllStatus ? "showAll" : "showNotAll";
    setTimeout(() => {
      this.showSpinnerStoreButton = true;
      this.showDisableFinishButton = true;
    }, 500);
    // Adjust time as needed
    let url = environment.cdkDumpFile;
    let userName: any;
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      userName = currentUserObj.userPrincipalName
        ? currentUserObj.userPrincipalName
        : "";
    }
    if (this.base64QueryString) {
      let urlDecodedString = this.base64QueryString;
      let previousString;
      do {
        previousString = urlDecodedString;
        urlDecodedString = decodeURIComponent(urlDecodedString);
      } while (urlDecodedString !== previousString);
      // Decode the Base64 string
      const decodedString = atob(urlDecodedString);
      // Parse the decoded JSON string
      const decodedObject = JSON.parse(decodedString);
      const token = localStorage.getItem("token");
      let matchingItem = this.storeFileList?.find((item) => {
        return (
          item.id.includes(decodedObject.schedulerId) &&
          item.id.includes(decodedObject.companyId)
        );
      });
      if (matchingItem) {
      const requestUriData = {
        schedulerId: decodedObject.schedulerId,
        companyImportId: decodedObject.companyId,
        dumpFileName:matchingItem.id,
        status: "Finish",
        dms: this.dmsList,
        inProjectId: decodedObject.projectId,
        inPerformedOn: this.getCurrentDateTime(),
        inPerformedBy: userName,
        mode: "Automatic",
        storeName: this.companyListName,
        showAllStatus:showStatus
      };
      let tokenDpt = localStorage.getItem("token");
      const headers = new HttpHeaders({
        authorization: tokenDpt ? `Bearer ${tokenDpt}` : "",
        "Content-Type": "application/json",
        // requestdata: JSON.stringify(requestUriData),
      });
      this.http
        .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
        .subscribe((res: any) => {
          let activityDataSchedule = {
            activityName: "Finish Tab",
            activityType: "Finish Dump Data in URI ",
            activityDescription: `Fetch dump file data and zipped successfully"
              )})`,
          };
          this.commonService.saveActivity(
            "Scheduler Import",
            activityDataSchedule
          );
          if (res.status == "success") {
            this.showDisableFinishButton = false;
            this.tabValidationErrors["import-tab"] = false;
            this.tabValidationErrors["make-tab"] = false;
            this.tabValidationErrors["department-tab"] = false;
            this.tabValidationErrors["paytype-tab"] = false;
            this.tabValidationErrors["invoice-tab"] = false;
            //resume staus portal call
            if (decodedObject.projectId != null) {
              decodedObject.projectId.forEach((projectId: any) => {
                const requestUriData = {
                  inData: {},
                  inProjectId: projectId,
                  inSource: "SchedulerImport",
                  inAction: "resume",
                  inPerformedOn: this.getCurrentDateTime(),
                  inPerformedBy: userName,
                  inDms: decodedObject.dmsList,
                };
                const headers = new HttpHeaders({
                  authorization: token ? `Bearer ${token}` : "",
                  "Content-Type": "application/json",
                });
                this.http
                  .post<any>(environment.payloadPortalUrl, requestUriData, {
                    headers: headers,
                  })
                  .subscribe((res: any) => {
                  });
              });
            } else {
              const requestUriData = {
                inData: {},
                inProjectId: decodedObject.projectId,
                inSource: "SchedulerImport",
                inAction: "resume",
                inPerformedOn: this.getCurrentDateTime(),
                inPerformedBy: userName,
                inDms: decodedObject.dmsList,
              };
              const headers = new HttpHeaders({
                authorization: token ? `Bearer ${token}` : "",
                "Content-Type": "application/json",
                // requestdata: JSON.stringify(requestUriData),
              });

              this.http
                .post<any>(
                  environment.payloadPortalUrl,
                  JSON.stringify(requestUriData),
                  {
                    headers: headers,
                  }
                )
                .subscribe((res: any) => {
                });
            }
            this.updateSchedulerImportDetails(
              decodedObject.schedulerId,
              decodedObject.companyId,
              ""
            );
            this.storeGroup = [];
            this.store = [];
            this.version = [];
            this.fileList = [];
            this.showSpinnerStoreButton = false;
            const message = res.message;
            let self = this;
            swal(
              {
                title: message,
                type: "success",
                confirmButtonClass: "btn-success pointer",
                confirmButtonText: "Close",
              },
              function (isConfirm: any) {
                if (isConfirm) {
                  //navigation for halt cases only
                  self.router.navigate(
                    [self.constantService.IMPORTSTATUS_URL],
                    {}
                  );
                }
              }
            );
          } else {
            setTimeout(() => {
              this.showDisableFinishButton = false;
              this.showSpinnerStoreButton = false;
            }, 500);
            this.showStatusMessage(res.message, "failed");
          }
        });

      }
        else {
        setTimeout(() => {
          this.showDisableFinishButton = false;
          this.showSpinnerStoreButton = false;
        }, 500);
        this.showStatusMessage("Matching File Missing", "failed");
      }
    } else {
      let requestUriData:any ;  
      //manual import finish
      if(this.showAllStatus){
        
        const matchingObject = this.jobGroupList.filter(item => item.companyId == this.selectedStoreFilterList[0].companyID);
        console.log("Matching Object:", matchingObject,this.storeFilterList,this.selectedStoreFilterList,this.allStoreList,this.companyCommonId);
        if (matchingObject && matchingObject[0]) {
          let mageGrpData ={
            state:matchingObject[0].state,
            mageGroupCode:matchingObject[0].mageGroupCode ? matchingObject[0].mageGroupCode
              : "",
            mageGroupName:matchingObject[0].mageGroupName ? matchingObject[0].mageGroupName
              : "",
            mageStoreName:matchingObject[0].mageStoreName ? matchingObject[0].mageStoreName
              : "",
            mageStoreCode:matchingObject[0].mageStoreCode ? matchingObject[0].mageStoreCode
              : "",
            mageProjectId:matchingObject[0].projectId ? matchingObject[0].projectId
              : "",
            mageSecondaryId:matchingObject[0].secondProjectId ? matchingObject[0].secondProjectId
              : "",
            mageManufacturer:matchingObject[0].mageManufacturer ? matchingObject[0].mageManufacturer
              : "",
            mageProjectName:matchingObject[0].projectName ? matchingObject[0].projectName
              : "",
            mageProjectType:matchingObject[0].projectType ? matchingObject[0].projectType
              : "",
            secondaryProjectId:matchingObject[0].secondaryProjectId ? matchingObject[0].secondaryProjectId
              : "",              
            secondaryProjectType:matchingObject[0].secondaryProjectType ? matchingObject[0].secondaryProjectType
              : "",
            secondaryProjectName:matchingObject[0].secondaryProjectName ? matchingObject[0].secondaryProjectName
              : "",
            companyId: matchingObject[0].companyId ? matchingObject[0].companyId : "",

          }
           requestUriData = {
            schedulerId: this.schedulerCommonId,
            companyImportId: this.companyCommonId,
            dumpFileName: this.fileName,
            status: "Finish",
            dms: this.dmsList,
            inPerformedBy: userName,
            mode: "Manual",
            storeName: this.companyListName,
            showAllStatus:showStatus,
            mageGrpData:mageGrpData
          };
        }
      }else{
        requestUriData = {
          schedulerId: this.schedulerCommonId,
          companyImportId: this.companyCommonId,
          dumpFileName: this.fileName,
          status: "Finish",
          dms: this.dmsList,
          inPerformedBy: userName,
          mode: "Manual",
          storeName: this.companyListName,
          showAllStatus:showStatus
        };
      }
      console.log(requestUriData,"requestUriData")
      let tokenDpt = localStorage.getItem("token");
      const headers = new HttpHeaders({
        authorization: tokenDpt ? `Bearer ${tokenDpt}` : "",
        "Content-Type": "application/json",
        // requestdata: JSON.stringify(requestUriData),
      });
      headers.append("Authorization", `Bearer ${tokenDpt}`);
      this.http
        .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
        .subscribe((res: any) => {
          let activityDataSchedule = {
            activityName: "Finish Tab",
            activityType: "Finish Dump Data in manual import ",
            activityDescription: `Fetch dump file data and zipped successfully"
            )})`,
          };
          this.commonService.saveActivity(
            "Scheduler Import",
            activityDataSchedule
          );
          if (res.status == "success") {
            this.showDisableFinishButton = false;
            this.tabValidationErrors["import-tab"] = false;
            this.tabValidationErrors["make-tab"] = false;
            this.tabValidationErrors["department-tab"] = false;
            this.tabValidationErrors["paytype-tab"] = false;
            this.tabValidationErrors["invoice-tab"] = false;
            this.completeImportStatus = false;
            this.completeMakeStatus = false;
            this.completePaytypeStatus = false;
            this.completeDeptStatus = false;
            this.completeInvoiceStatus = false;
            this.completeFinishStatus = false;
            this.storeGroup = [];
            this.store = [];
            this.version = [];
            this.fileList = [];
            this.currentTab = "import-tab";
            this.flagNextStep = false;
            this.showSpinnerStoreButton = false;
            const message = res.message;
            swal({
              title: message,
              type: "success",
              confirmButtonClass: "btn-success pointer",
              confirmButtonText: "Close",
              allowOutsideClick: false
            });

            this.changeDetectorRefCheck();
            const currentUrl = this.router.url;
            // Navigate to the same route to refresh
            this.router
              .navigateByUrl("/", { skipLocationChange: true })
              .then(() => {
                this.router.navigate([currentUrl]);
              });
          } else {
            setTimeout(() => {
              this.showDisableFinishButton = false;
              this.showSpinnerStoreButton = false;
            }, 500);
            this.showStatusMessage(
              "Error zipping files,Please Try Again!",
              "failed"
            );
          }
        });
    }
  }
  getStoreCompanyDetails(compId: any, callback: any) {
    let activityDataSchedule = {
      activityName: "Fetch company details",
      activityType: "get company details",
      activityDescription: `Fetch company details based on company id"
      )})`,
    };
    this.commonService.saveActivity("Scheduler Import", activityDataSchedule);
    this.subscription = this.apollo
      .use("manageScheduler")
      .query({
        query: getStoreCompanyDetails,
        fetchPolicy: "network-only",
        variables: {
          inCompanyId: compId,
        },
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata:any) => {
          const result: any = listdata;
          // Extract the array from the object
          const jsonArrayPaytype = result.data.getCompanyDetails;
          // Parse the array from the strin
          const jsonTypeArray = JSON.parse(jsonArrayPaytype);
          this.companyListName = jsonTypeArray[0]["company_name"];          
          this.dmsList = jsonTypeArray[0]["dms"];
          this.stateList = jsonTypeArray[0]["state"];
          this.manufacturer = jsonTypeArray[0]["brand_list"];
          console.log(this.dmsList,"this.dmsList");
          console.log(this.selectedStoreFilterList[0].dmsCode,"this.selectedStoreFilterList dmsCode");
          if (this.isMatchingDMS(this.dmsList, this.selectedStoreFilterList[0].dmsCode)) {         
            callback(true);
          }else{
            setTimeout(() => {
               this.gridApiMakes.hideOverlay();
               this.gridApiModel.hideOverlay();
               this.gridApiUnassignMake.hideOverlay();           
            swal({
              title: `The DMS "${this.dmsList}" mismatch with selected store.`,
              type: "warning",
              confirmButtonClass: "btn-warning pointer",
              confirmButtonText: this.constantService.CLOSE,
            })
             this.rowDataMakes = [];
             this.rowDataModel = [];
             this.rowDataUnassignMake = [];
          }, 200); 
            callback(false);
          }
        },
        error: (err:any) => {
          callback(false); // Call the callback function with status false
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }
  //for checbox change scenarios in manufacture grid section
  handleCheckboxChange(updatedData: any) {
    this.selectedDeptList = [];
    this.selectedDeptList = updatedData.rowdata;
    this.rowDataDept = this.rowDataDept.map((item: any) => {
      if (item.dept == this.selectedDeptList.dept) {
        return { ...item, is_allowed: this.selectedDeptList.is_allowed };
      }
      return item;
    });
    this.selectedDeptList = [];
    this.selectedDeptList = this.selectedDeptList?.map((item: any) =>
      item.dept === this.rowDataDept.dept
        ? { ...item, ...this.rowDataDept }
        : item
    );
    // Check if rowDataDept's dept does not exist in selectedDeptList
    const deptExists = this.selectedDeptList.some(
      (item: any) => item.dept === this.rowDataDept.dept
    );
    if (!deptExists) {
      this.selectedDeptList.push(this.rowDataDept);
    }
  }
  // for resume update in import status module
  updateSchedulerImportDetails(
    schedulerId: any,
    companyId: any,
    callback: any
  ) {

    let userName: any;
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      userName = currentUserObj.userPrincipalName
        ? currentUserObj.userPrincipalName
        : "";
    }

     

    const dbObj: any = {
      inSchedulerId: schedulerId,
      inCompanyId: companyId,
      inResumedOn: this.getCurrentDateTime(), //moment().format("YYYY-MM-DD HH:mm:ss"),
      inResumedBy:userName
    };
    const activityData1 = {
      activityName: "update schedulerPreImportStatusUpdation",
      activityDescription: "update schedulerPreImportStatusUpdation: started",
      activityData: dbObj,
    };
    this.commonService.saveActivity(this.currentPageName, activityData1);
    const subscription = this.apollo.use("manageScheduler").mutate({
      mutation: schedulerPreImportStatusUpdation,
      variables: dbObj,
    });
    subscription.pipe(takeUntil(this.subscription$)).subscribe(
      ({ data }) => {
        NProgress.done();
        // this.showSpinnerOnSaveMake = false;
        const result: any = data;
        const activityData2 = {
          activityName: "update schedulerPreImportStatusUpdation",
          activityDescription:
            "update schedulerPreImportStatusUpdation: completed",
          activityData: data,
        };
        this.commonService.saveActivity(this.currentPageName, activityData2);
        const obj = JSON.parse(
          result["schedulerPreImportStatusUpdation"]["json"]
        );
        if (obj.status === this.constantService.SUCCESS_MESSAGE) {
          if (callback) {
            callback(true);
          }
        } else {
          const message = this.constantService.CANNOT_FETCH_DATA;
          swal({
            title: message,
            type: "warning",
            confirmButtonClass: "btn-warning pointer",
            confirmButtonText: this.constantService.CLOSE,
            allowOutsideClick: false
          });
          if (callback) {
            callback(false);
          }
        }
      },
      (err: any) => {
        NProgress.done();
        const message = this.constantService.CANNOT_FETCH_DATA;
        swal({
          title: message,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
          allowOutsideClick: false
        });
        this.commonService.errorCallback(err, this);
        if (callback) {
          callback(false);
        }
      }
    );
    this.subscriptionList.push(subscription);
    return subscription;
  }
  //for schema url navigation for BE access
  getSchemaURL(dmsList: any,strictMode= false) {
    let activityDataSchedule = {
      activityName: "getSchemaURL",
      activityType: "getSchemaURL",
      activityDescription: `getSchemaURL based on DMS"
      )})`,
    };
    this.commonService.saveActivity("Scheduler Import", activityDataSchedule);
    const isCDKPresent = ["CDK", "CDK3PA", "CDKDash", "CDK Drive"].some(dms => dmsList?.includes(dms));
    let url: any;
    let dmsCode: any;
    if (isCDKPresent) {
      dmsCode = dmsList;
      url = environment.cdkImportUrl;
    } else if (dmsList?.includes("DealerTrack")) {
      dmsCode = dmsList;
      url = environment.dealerTrackImportUrl;
    } else if (dmsList?.includes("Reynolds") || dmsList?.includes("UCS")) {
      url = environment.reynoldsImportUrl;
      dmsCode = dmsList;
    } else if (dmsList?.includes("DealerBuilt")) {
      dmsCode = dmsList;
      url = environment.dealerBuiltImportUrl;
    } else if (dmsList?.includes("Auto/Mate") || dmsList?.includes("AutoMate")) {
      url = environment.automateImportUrl;
      dmsCode = dmsList;
    } else if(dmsList?.includes("TekionAPI")){
      url = environment.importTekionAPIUrl;
      dmsCode = dmsList;
    }else if (dmsList?.includes("Tekion")) {
      url = environment.tekionImportUrl;
      dmsCode = dmsList;
    } else if (dmsList?.includes("PBS")) {
      url = environment.pbsImportUrl;
      dmsCode = dmsList;
    } else if (dmsList?.includes("AutoSoft")) {
      url = environment.autoSoftImportUrl;
      dmsCode = dmsList;
    } else if (dmsList?.includes("MPK")) {
      url = environment.importMPKUrl;
      dmsCode = dmsList;
    } else if (dmsList?.includes("Dominion / VUE") || dmsList?.includes("Dominion / ACS" ) || dmsList?.includes("Dominion") || dmsList?.includes("DominionVue")) {
      url = environment.importDominionVueUrl;
      dmsCode = dmsList;
    } else if (dmsList?.includes("Quorum")) {
      url = environment.importQuorumUrl;
      dmsCode = dmsList;
    } else if (dmsList?.includes("ADAMS")) {
      url = environment.importADAMUrl;
      dmsCode = dmsList;
    } else if (dmsList?.includes("ReynoldsRCI")) {
      url = environment.importReynoldsUrl;
      dmsCode = dmsList;
    }else if(dmsList?.includes("Fortellis")){
      url = environment.importFortellisUrl;
      dmsCode = dmsList;
    }
    
    if (strictMode && !url) {
      return { dmsCode: null, url: null };
    }
    return { dmsCode, url };
  }
  //for paytype height adjustment
  getSelectSize(array: any[]): number {
    return array?.length > 0 ? array.length : 1;
  }
  // Function to remove duplicates in dept tab
  removeDeptDuplicates = (array: any, key: any) => {
    return array?.filter(
      (item: any, index: any, self: any) =>
        index === self.findIndex((t: any) => t[key] === item[key])
    );
  };
  getCurrentDateTime(): string {
    const date = new Date();
    const pad = (num: number, size: number = 2): string =>
      ("000" + num).slice(-size);
    const year = date.getUTCFullYear();
    const month = pad(date.getUTCMonth() + 1);
    const day = pad(date.getUTCDate());
    const hours = pad(date.getUTCHours());
    const minutes = pad(date.getUTCMinutes());
    const seconds = pad(date.getUTCSeconds());
    const milliseconds = pad(date.getUTCMilliseconds(), 3);
    const microseconds = pad(Number(milliseconds) * 1000, 6);
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${microseconds}+00:00`;
  }
  //search filter for invoice tab
  searchInvoiceData(params: any) {
    this.gridApi3.setFilterModel(null);
    this.gridApi3.onFilterChanged();
    this.gridApi3.setQuickFilter(params);
  }
  saveSelectPayType(updatedPayType: any, requestUriData: any, mode: any) {
    requestUriData.selectedPayList.forEach((item: any) => {
      if (item.is_determined === false) {
        item.is_determined = true;
      }
    });
    let token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      "Content-Type": "application/json",
      // requestdata: JSON.stringify(requestUriData),
    });
    let schemaData: any = this.getSchemaURL(this.dmsList);
    let url = schemaData.url;
    this.savePayTypes(updatedPayType, "");
    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
      .subscribe((res: any) => {
        let activityDataSchedule = {
          activityName: "CASE:::: Paytype",
          activityType: "getFetchPaytypeData",
          activityDescription: `Fetch getFetchPaytypeData with request params ${JSON.stringify(
            requestUriData
          )}.
  )})`,
        };
        this.commonService.saveActivity("Pay Types", activityDataSchedule);
        if (res.status == "success") {
          this.completeDeptStatus = true;
          if (this.rowDataDept?.length > 0) {
            setTimeout(() => {
              this.gridApiPRate?.hideOverlay(); // Hide the loading overlay
            }, 250);
          }
          this.stayOnCurrentTab = true;
          this.currentTab = mode === "onNextClick" ? "department-tab" : this.tabPosition;
          if (!this.tabVisit.paytype) {
            this.setDepartmentGrid();
          }
          const message = "Paytype Saved Successfully";
          setTimeout(() => {
            this.showStatusMessage(message, "success");
          }, 2000);
          this.tabVisit.paytype = true;
          swal.close(); 
        } else {
          if (res.status == "error") {
            this.currentTab = "paytype-tab";
            this.tabVisit.paytype = false;
            const message = "Error: Paytype not Saved";
            this.statusMessage = { message: message, error: true };
            this.showStatusMessage(message, "failed");
            swal.close(); 
          }
        }
        this.stayOnCurrentTab = false;
      });
  }
  saveSelectDepartment(allowedDepartments: any, requestUriData: any, mode: any) {
    let schemaData: any = this.getSchemaURL(this.dmsList);
    let url = schemaData.url;
    let tokenDpt = localStorage.getItem("token");

    const headersDept = new HttpHeaders({
      authorization: tokenDpt ? `Bearer ${tokenDpt}` : "",
      "Content-Type": "application/json",
      // requestdata: JSON.stringify(requestUriData),
    });
    this.updateDeptType(allowedDepartments); // Ensure this is called only once
    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers: headersDept })
      .subscribe((res: any) => {
        let activityDataSchedule = {
          activityName: "CASE:::: Department",
          activityType: "getFetchDepartmentData",
          activityDescription: `Fetch getFetchPaytypeData  with request params ${JSON.stringify(
            requestUriData
          )}.
    )})`,
        };
        this.commonService.saveActivity("Departments", activityDataSchedule);
        if (res.status == "success") {
          this.completeInvoiceStatus = true;
          this.stayOnCurrentTab = true;
          this.currentTab = mode === "onNextClick" ? "invoice-tab" : this.tabPosition;
          if (!this.tabVisit.dept) {
            this.getAllInvoiceData();
            this.getROInvoiceData();
          }
          const message = "Department Saved Successfully";
          setTimeout(() => {
            this.showStatusMessage(message, "success");
          }, 2000);
          this.tabVisit.dept = true;
          swal.close(); 
        } else {
          if (res.status == "error") {
            this.tabVisit.dept = false;
            this.currentTab = "department-tab";
            const message = "Error: Department not Saved";
            this.statusMessage = { message: message, error: true };
            this.showStatusMessage(message, "failed");
            swal.close();
          }
        }
        this.stayOnCurrentTab = false;
        swal.close(); 
      });
  }
  onPreviousClick() {
    switch (this.currentTab) {
      case "finish-tab":
        this.currentTab = "invoice-tab";
        break;
      case "invoice-tab":

        if (this.rowDataDept.length > 0) {
          setTimeout(() => {
            this.gridApiPRate.hideOverlay(); // Hide the loading overlay
          }, 250);
        }
        this.currentTab = "department-tab";
        break;
      case "department-tab":
        this.currentTab = "paytype-tab";
        break;
      case "paytype-tab":
        setTimeout(() => {
          if (this.rowDataMakes && this.rowDataModel) {
            this.gridApiMakes.hideOverlay();
            this.gridApiModel.hideOverlay();
          }
          if (this.rowDataUnassignMake) {
            this.gridApiUnassignMake.showNoRowsOverlay();
          }
        }, 250);

        this.currentTab = "make-tab";
        break;
      case "make-tab":

        this.currentTab = "import-tab";
        break;
    }
  }
  onNextClick() {
    if (this.schedulerImportId && this.companyImportId) {
      this.schedulerCommonId = this.schedulerImportId;
      this.companyCommonId = this.companyImportId;
    }
    let schemaData: any = this.getSchemaURL(this.dmsList);
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    switch (this.currentTab) {
      case "import-tab":
        this.currentTab = "make-tab";
        this.processImportTab(dmsList);
        break;
      case "make-tab":
        this.currentTab = "paytype-tab";
        this.isPaytypeDisabled = false;
        this.processMakeTab(url, dmsList);
        break;
      case "paytype-tab":
        this.currentTab = "department-tab";
        this.isDepartmentDisabled = false;
        this.processPaytypeTab(url, dmsList,"onNextClick");
        break;
      case "department-tab":
        this.currentTab = "invoice-tab"; // Set default tab transition
        this.isInvoiceDisabled = false;
        this.processDepartmentTab(url, dmsList,"onNextClick");
        break;
      case "invoice-tab":
        this.currentTab = "finish-tab";
        this.isFinishDisabled = false;
        this.processInvoiceTab(url, dmsList);
        break;
    }
  }
  onTabClick(tabId: string) {
    this.tabPosition = tabId;
    // Save the current tab as the previous tab before handling the new click
    this.previousTabId = this.currentTab || "";
    if (!this.isImportDisabled || tabId !== "import-tab") {
      this.currentTab = tabId;
    }
    // Prevent clicking on any tab except the active one
    if (this.base64QueryString) {
      if (
        (tabId === "paytype-tab" && this.isPaytypeDisabled) ||
        (tabId === "department-tab" && this.isDepartmentDisabled) ||
        (tabId === "invoice-tab" && this.isInvoiceDisabled) ||
        (tabId === "finish-tab" && this.isFinishDisabled)
      ) {
        return;
      }
    } else {
      if (this.currentTab === "import-tab") {
        // Disable all forward tabs if "Import" tab is active
        if (
          (tabId === "make-tab" && this.isMakeDisabled) ||
          (tabId === "paytype-tab" && this.isPaytypeDisabled) ||
          (tabId === "department-tab" && this.isDepartmentDisabled) ||
          (tabId === "invoice-tab" && this.isInvoiceDisabled) ||
          (tabId === "finish-tab" && this.isFinishDisabled)
        ) {
          return; // Stop navigation if the tab is disabled
        }
      }
    }
    this.currentTab = tabId;
    if (this.rowDataDesc?.length > 0) {
      setTimeout(() => {
        this.gridApiPRate?.hideOverlay();
      }, 150);
    }
    if (this.schedulerImportId && this.companyImportId) {
      this.schedulerCommonId = this.schedulerImportId;
      this.companyCommonId = this.companyImportId;
    }
    let schemaData: any = this.getSchemaURL(this.dmsList);
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    if (
      this.previousTabId === "import-tab" &&
      this.isTargetTab(["make-tab", "paytype-tab", "department-tab", "invoice-tab", "finish-tab"])
    ) {
      this.processImportTab(dmsList);
    }else if (
      this.previousTabId === "make-tab" &&
      this.isTargetTab(["paytype-tab", "department-tab", "invoice-tab", "finish-tab"])
    ) {
      this.processMakeTab(url, dmsList);
    } else if (
      this.previousTabId === "paytype-tab" &&
      this.isTargetTab(["department-tab", "invoice-tab", "finish-tab"])
    ) {
      this.processPaytypeTab(url, dmsList, "tabClick");
    } else if (
      this.previousTabId === "department-tab" &&
      this.isTargetTab(["invoice-tab", "finish-tab"])
    ) {
      this.processDepartmentTab(url, dmsList, "tabClick");
    } else if (this.previousTabId === "invoice-tab" && this.isTargetTab(["finish-tab"])) {
      this.processInvoiceTab(url, dmsList);
    } else {
      setTimeout(() => {
        this.gridApiMakes?.hideOverlay();
        this.gridApiModel?.hideOverlay();
        if (this.rowDataUnassignMake.length && this.rowDataUnassignMake.length > 0) {
          this.gridApiUnassignMake?.hideOverlay();
        } else {
          this.gridApiUnassignMake?.showNoRowsOverlay();
        }
      }, 150);
      if (this.rowDataDept && this.rowDataDept.length > 0) {
        setTimeout(() => {
          this.gridApiPRate?.hideOverlay(); // Hide the loading overlay
        }, 250);
      }
    }

    
  }
  onImportClick() {
    // Set import tab as disabled and move to 'Make' tab
    this.isImportDisabled = true;
    this.currentTab = "make-tab";
  }
  isTargetTab(validTabs: string[]) {
    return validTabs.includes(this.currentTab);
  }
  processImportTab(dmsList: any) {
    const importValidationResult = this.validateImportFields();
    if (!importValidationResult) {
      // Stay on 'import-tab' if validation fails
      this.currentTab = "import-tab";
    } else {
      this.isMakeDisabled = false;
      try {
        let urlSchema = environment.schemaCdkUrl;
        const requestSchemaData = {
          dms: dmsList,
          fileName: this.fileName,
          companyId: this.companyCommonId,
          schedulerId: this.schedulerCommonId,
        };
        this.dmsList = dmsList;
        const tokenSchema = localStorage.getItem("token");
        const fileHeaders = new HttpHeaders({
          authorization: tokenSchema ? `Bearer ${tokenSchema}` : "",
          "Content-Type": "application/json",
          requestdata: JSON.stringify(requestSchemaData),
        });
        this.http
          .post<any>(urlSchema, JSON.stringify(requestSchemaData), { headers: fileHeaders })
          .subscribe((res: any) => {
            let activityDataSchedule = {
              activityName: "CASE:::: Import",
              activityType: "getFetchFileData",
              activityDescription: `Fetch getFetchFileData with request params ${JSON.stringify(
                requestSchemaData
              )}.
            )})`,
            };
            this.commonService.saveActivity("Import file Case", activityDataSchedule);
            if (res.status == "success") {
              this.completeImportStatus = true;
              this.companyFileId = res.data.companyId;
              this.schedulerFileId = res.data.schedulerId;
              this.gridApiMakes.hideOverlay();
              this.gridApiModel.hideOverlay();
              this.gridApiUnassignMake.hideOverlay();
              this.completeMakeStatus = true;
              if (!this.tabVisit.import) {
                console.log("CASE:::: Import");
                this.getFetchSchemaCheck();
              }
              this.stepCounter++;
              this.tabVisit.import = true;
            } else {
              this.statusMessage = { message: res.message, error: true };
              this.showStatusMessage(res.message, "failed");
            }
          });
      } catch (error) {
        this.tabVisit.import = false;
        console.log("Error occurred in the Import case:", error);
      }
    }
  }
  processMakeTab(url: any, dmsList: any) {
    const makeValidationResult = this.isAtLeastOneMakeAllowed();
    if (!makeValidationResult) {
      this.currentTab = "make-tab";
      return;
    }

    const token = localStorage.getItem("token");
    const allowedCheckedData = this.checkedListItems.filter(
      (item: any) => item.is_allowed
    );
    const combinedData = allowedCheckedData.reduce((acc: any, curr: any) => {
      const existing = acc.find(
        (item: any) => item.manufacturer === curr.manufacturer
      );
      if (existing) {
        existing.makeList = Array.isArray(existing.makeList)
          ? [...existing.makeList, curr.makeList]
          : [existing.makeList, curr.makeList];
      } else {
        acc.push({
          manufacturer: curr.manufacturer,
          makeList: [].concat(curr.makeList || []),
          count: curr.count,
          is_allowed: curr.is_allowed,
        });
      }
      return acc;
    }, []);

    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      operationName: "makeSelList",
      makeSelListData: combinedData,
    };

    this.commonService.saveActivity("Make selection Case", {
      activityName: "CASE:::: Make",
      activityType: "makeSelList",
      activityDescription: `Add selected make list with request params ${JSON.stringify(
        requestUriData
      )}`,
    });

    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      "Content-Type": "application/json",
      // requestdata: JSON.stringify(requestUriData),
    });

    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers })
      .subscribe((res: any) => this.handleMakeResponse(res, requestUriData));
  }

  handleMakeResponse(res: any, requestUriData: any) {
    if (res.status === "success") {
      if (this.base64QueryString) {
        this.completeMakeStatus = true;
      }
      this.completePaytypeStatus = true;

      if (!this.tabVisit.make) {
        this.loadPayTypes();
        this.getStoreTypes(() => this.getStoreCustTypes());
      }
      this.tabVisit.make = true;
      this.showStatusMessage("Makes Saved Successfully", "success");
    } else {
      this.tabVisit.make = false;
      this.showStatusMessage("Error: Makes not Saved", "failed");
    }
    this.commonService.saveActivity("Make List", {
      activityName: "CASE:::: Make",
      activityType: "getFetchMakeData",
      activityDescription: `Fetch getFetchMakeData with request params ${JSON.stringify(
        requestUriData
      )}`,
    });
  }

  processPaytypeTab(url: any, dmsList: any, mode: any) {
   
    let message = this.getPayTypeValidationMessage();
    if (message) {
      setTimeout(() => this.showStatusMessage(message, "failed"), 500);
    }
    if (this.statusMessage) {
      this.showStatusMessage(this.statusMessage.message, "failed");
    }
    let combinedPaytypeArray = [
      ...this.custPaytypeAvailable,
      ...this.custPaytypeSelected,
      ...this.custPaytypeSelectedOnlyParts,
      ...this.custPaytypeSelectedOnlyLabor,
      ...this.warrPaytypeAvailable,
      ...this.warrPaytypeSelected,
      ...this.warrPaytypeSelectedParts,
      ...this.warrPaytypeSelectedLabor,
    ];
    let updatedPayType = combinedPaytypeArray.map((item) => ({
      ...item,

      is_determined: true, // Always update to true
    }));
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      operationName: "allSelSavePaytype",
      selectedPayList: updatedPayType,
    };
    this.commonService.saveActivity("Pay type selection Case", {
      activityName: "CASE:::: pay type",
      activityType: "allSelSavePaytype",
      activityDescription: `Add selected Pay type list with request params ${JSON.stringify(
        requestUriData
      )}`,
    });
    const headers = new HttpHeaders({
      authorization: localStorage.getItem("token")
        ? `Bearer ${localStorage.getItem("token")}`
        : "",
      "Content-Type": "application/json",
     
    });
    if (!this.isPaytypeHalt) {
      // Select all option elements with the class "grid-highlight-yellow" and remove the class
      combinedPaytypeArray.forEach((item: any) => {
        if (item.is_determined === false) {
          item.is_determined = true;
        }
      });
      this.savePayTypes(updatedPayType, "");
      this.http
        .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
        .subscribe((res: any) => this.handlePaytypeResponse(res, requestUriData));
    } else {
      if (this.base64QueryString) {
        const paytypeValidationResult = this.validatePayType(mode);
        if (!paytypeValidationResult) {
          this.currentTab = "paytype-tab";
          return;
        } else {
          this.currentTab = this.tabPosition;
        }
      }
    }
  }
  handlePaytypeResponse(res: any, requestUriData: any) {
    if (res.status === "success") {
      this.completeDeptStatus = true;

      if (this.rowDataDept?.length > 0) {
        this.gridApiPRate?.hideOverlay();
      }
      if (!this.tabVisit.paytype) {
        this.setDepartmentGrid();
      }
      this.tabVisit.paytype = true;
      setTimeout(
        () => this.showStatusMessage("Paytype Saved Successfully", "success"),
        2000
      );
    } else {
      this.tabVisit.paytype = false;
      this.showStatusMessage("Error: Paytype not Saved", "failed");
    }

    this.commonService.saveActivity("Pay Types", {
      activityName: "CASE:::: Paytype",
      activityType: "getFetchPaytypeData",
      activityDescription: `Fetch getFetchPaytypeData with request params ${JSON.stringify(
        requestUriData
      )}`,
    });
  }
  processDepartmentTab(url: any, dmsList: any, mode: any) {
    const departmentValidationResult = this.checkDeptAllowedItems(
      this.getDeptItems(),
      "At least one department should be allowed."
    );
    if (!departmentValidationResult) {
      this.currentTab = "department-tab";
      return;
    }

    const allowedDepartments = this.rowDataDept?.length
      ? this.removeDeptDuplicates(this.rowDataDept, "dept")
      : [];

    allowedDepartments.forEach((item: any) => (item.is_determined = true));
    // this.departmentFetchData.forEach((deptFetchObj: any) => {
    //   const matchingDept = allowedDepartments.find(
    //     (allowedDept: any) => allowedDept.dept === deptFetchObj.dept
    //   );
    //   if (matchingDept && deptFetchObj.is_allowed !== matchingDept.is_allowed) {
    //     deptFetchObj.is_allowed = matchingDept.is_allowed;
    //   }
    // });
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      operationName: "selDepartmentsList",
      departmentSelList: allowedDepartments,
    };

    this.commonService.saveActivity("Department selection Case", {
      activityName: "CASE:::: Department",
      activityType: "selDepartmentsList",
      activityDescription: `Add selected Department list with request params ${JSON.stringify(
        requestUriData
      )}`,
    });

    const headersDept = new HttpHeaders({
      authorization: localStorage.getItem("token")
        ? `Bearer ${localStorage.getItem("token")}`
        : "",
      "Content-Type": "application/json",
      // requestdata: JSON.stringify(requestUriData),
    });

    if (!this.isDeptHalt) {
      this.updateDeptType(allowedDepartments);
      this.http
        .post<any>(url, JSON.stringify(requestUriData), {
          headers: headersDept,
        })
        .subscribe((res: any) =>
          this.handleDepartmentResponse(res, requestUriData)
        );
    } else {
      if (this.base64QueryString) {
        const deptValidationResult = this.validateDeptType(this.departmentFetchData, mode);
        if (!deptValidationResult) {
          this.currentTab = "department-tab";
        } else {
          this.currentTab = this.tabPosition;
        }
      }
    }
  }
  handleDepartmentResponse(res: any, requestUriData: any) {
    if (res.status === "success") {
      this.completeInvoiceStatus = true;

      if (!this.tabVisit.dept) {
        this.getAllInvoiceData();
        this.getROInvoiceData();
      }
      setTimeout(
        () =>
          this.showStatusMessage("Department Saved Successfully", "success"),
        2000
      );
      this.tabVisit.dept = true;
    } else {
      this.tabVisit.dept = false;
      this.showStatusMessage("Error: Department not Saved", "failed");
    }

    this.commonService.saveActivity("Departments", {
      activityName: "CASE:::: Department",
      activityType: "getFetchDepartmentData",
      activityDescription: `Fetch getFetchDepartmentData with request params ${JSON.stringify(
        requestUriData
      )}`,
    });
  }
  processInvoiceTab(url: any, dmsList: any) {
    const invoiceValidationResult = this.validateInvoiceSequence();
    if (!invoiceValidationResult) {
      this.currentTab = "invoice-tab";
      return;
    }
    // Filter out unchecked invoice data
    const filteredInvoiceData =
      this.rowDataInvoice?.filter((item: any) => item.status === false) || [];

    // Prepare the request data by removing unnecessary properties
    const modifiedSequenceData = filteredInvoiceData.map(
      ({ ro_list, suffix_ro_list, ...rest }: any) => rest
    );
    const requestUriSeqData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      operationName: "InvoiceSeqSelList",
      sequenceSelListData: modifiedSequenceData,
    };
    // Save activity for logging purposes
    this.commonService.saveActivity("Invoice selection Case", {
      activityName: "CASE:::: Invoice",
      activityType: "InvoiceSeqSelList",
      activityDescription: `Add selected Invoice Sequence list with request params ${JSON.stringify(
        requestUriSeqData
      )}`,
    });

    // Set up headers for the HTTP request
    const token = localStorage.getItem("token");
    const headersInvoice = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      "Content-Type": "application/json",
      requestdata: JSON.stringify(requestUriSeqData),
    });
    // Send the HTTP request to save invoice sequence data
    this.http
      .post<any>(url, JSON.stringify(requestUriSeqData), {
        headers: headersInvoice,
      })
      .subscribe((res: any) =>
        this.handleInvoiceResponse(res, requestUriSeqData)
      );
  }
  handleInvoiceResponse(res: any, requestUriSeqData: any) {
    if (res.status === "success") {
      this.completeFinishStatus = true;
      this.currentTab = "finish-tab";
     
      // Display success message and set tab visit status
      setTimeout(
        () => this.showStatusMessage("Invoice Sequence Saved Successfully", "success"),
        1200
      );
      this.tabVisit.iseq = true;
    } else {
      // Display error message if saving failed
      this.tabVisit.iseq = false;
      this.currentTab = "invoice-tab";
      this.showStatusMessage("Error: Invoice Sequence not Saved", "failed");
    }

    // Save activity for logging purposes
    this.commonService.saveActivity("Invoice", {
      activityName: "CASE:::: Invoice",
      activityType: "getFetchInvoiceData",
      activityDescription: `Fetch getFetchInvoiceData with request params ${JSON.stringify(
        requestUriSeqData
      )}`,
    });
  }
  toggleExpandCollapseInvoice() {
    setTimeout(() => {
      this.gridApi1.sizeColumnsToFit();
       this.gridApi2.sizeColumnsToFit();
       this.gridApi3.sizeColumnsToFit();
     },0);
    this.isExpandedGrid = !this.isExpandedGrid;
  }
  toggleExpandCollapseRoInvoice() {
    setTimeout(() => {
     this.gridApi1.sizeColumnsToFit();
      this.gridApi2.sizeColumnsToFit();
      this.gridApi3.sizeColumnsToFit();
  
    },0);
    this.isExpandedGrid1 = !this.isExpandedGrid1;
  }
   
  toggleExpandCollapseRoInvoiceOrphan() {
    setTimeout(() => {
     this.gridApi1.sizeColumnsToFit();
      this.gridApi2.sizeColumnsToFit();
      this.gridApi3.sizeColumnsToFit();
  
    },0);
    this.isExpandedGrid5 = !this.isExpandedGrid5;
  }
  
  toggleExpandCollapseModel(){
    setTimeout(() => {
      this.gridApiMakes.sizeColumnsToFit();
      this.gridApiUnassignMake.sizeColumnsToFit();
     },0);
    this.isExpandedGrid2 = !this.isExpandedGrid2;

  }
  toggleExpandCollapseMakes(){
    setTimeout(() => {
      this.gridApiModel.sizeColumnsToFit();
      this.gridApiUnassignMake.sizeColumnsToFit();

     },0);
    this.isExpandedGrid3 = !this.isExpandedGrid3;

  }
  toggleExpandCollapseUnassignMakes(){
    setTimeout(() => {
      this.gridApiMakes.sizeColumnsToFit();
      this.gridApiModel.sizeColumnsToFit();
     },0);
    this.isExpandedGrid4 = !this.isExpandedGrid4;

  }
  getAllPayList(){
    this.combinedPaytypeLists=[...this.customAllList, ...this.warrantyAllList];
  }
  displayAllPartsPaytypes(paytype:any,types:any){
    this.payTypeList=paytype;
    let schemaData: any = this.getSchemaURL(this.dmsList);
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      partPayType:paytype,
      operationName: "PayAllPartsList",
    };
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      "Content-Type": "application/json",
      // requestdata: JSON.stringify(requestUriData),
    });
    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
      .subscribe((res: any) => {
        if (res) {
          this.rowDataPayList = res.data?.paytypeAllPartListData;
        }
      });
   // this.columnDefsPayList=[];
    this.combinedPaytypeLists=[...this.customAllList, ...this.warrantyAllList];
    this.columnDefsPayList = [
      {
      headerName: "RO Number",
      field: "ro_number",
      sortable: true,
      width: 90,
      type: "leftAligned",
      filter: true,
      },
      {
        headerName: "Part No",
        field: "part_no",
        sortable: true,
        width: 110,
        filter: true,
      },
      {
      headerName: "Part Desc",
      field: "part_desc",
      sortable: true,
      width: 90,
      filter: true,
      },
      {
        headerName: "Cost",
        field: "cost",
        sortable: true,
        type: "rightAligned",
        width: 80,
        filter: true,
        valueFormatter: (params: any) => {
          return params.value ? params.value.toLocaleString() : '';
        },
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;

          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
      {
        headerName: "Sale",
        field: "sale",
        sortable: true,
        width: 80,
        type: "rightAligned",
        filter: true,
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;

          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
      {
        headerName: "Qty",
        field: "quantity",
        width: 80,
        sortable: true,
        type: "rightAligned",
        filter: true,
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
      {
        headerName: "Pay-Types",
        field: "pay_type",
        sortable: true,
        width: 110,
        filter: true,
        valueFormatter: (params:any) => params.value ? params.value : "N/A",
      },
    ];
    if (!$("#partPaytype").is(":visible")) {
      $("#partPaytype").modal("show");
    }
  }
  displayAllLaborPaytypes(paytype:any,types:any){
    this.payTypeList=paytype;
    let schemaData: any = this.getSchemaURL(this.dmsList);
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      laborPayType:paytype,
      operationName: "PayAllLaborList",
    };
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      "Content-Type": "application/json",
      // requestdata: JSON.stringify(requestUriData),
    });
    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
      .subscribe((res: any) => {
        if (res) {
          this.rowDataLaborPayList = res.data?.paytypeAllLaborListData;
        }
      });
   // this.columnDefsPayList=[];
    this.combinedPaytypeLists=[...this.customAllList, ...this.warrantyAllList];
    this.columnDefsLaborPayList = [
      {
        headerName: "RO Number",
      field: "ro_number",
      sortable: true,
      width: 90,
      type: "leftAligned",
      filter: true,
      },
      {
        headerName: "OP Code",
        field: "op_code",
        sortable: true,
        width: 110, 
        filter: true,
      },
      
      {
        headerName: "OP Code Desc",
      field: "opcode_desc",
      sortable: true,
      width: 90,
      },
      
      {
        headerName: "Hours",
        field: "hours",
        sortable: true,
        type: "rightAligned",
        width: 90,
        filter: true,
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
      {
        headerName: "Sale",
        field: "sale",
        width: 80,
        type: "rightAligned",
        sortable: true,
        filter: true,
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
      {
        headerName: "Pay-Types",
        field: "pay_type",
        sortable: true,
        width: 110,
        filter: true,
        valueFormatter: (params:any) => params.value ? params.value : "N/A",

      },
    ];
    if (!$("#laborPaytype").is(":visible")) {
      $("#laborPaytype").modal("show");
    }
  }
  displayPartPayTypeSummary(){
    let schemaData: any = this.getSchemaURL(this.dmsList);
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      operationName: "partSummaryList",
    };
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      "Content-Type": "application/json",
      // requestdata: JSON.stringify(requestUriData),
    });
    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
      .subscribe((res: any) => {
        if (res) {
          this.rowDataPartSummary = res.data.paytypeAllPartSummary

        }
      });
   // this.columnDefsPayList=[];
    this.combinedPaytypeLists=[...this.customAllList, ...this.warrantyAllList];
    this.columnDefsPartSummary = [
      {
        headerName: "Pay-Types",
        field: "pay_type",
        sortable: true,
        filter: true,
        width: 130,
        valueFormatter: (params:any) => params.value ? params.value : "N/A",
      },
      {
        headerName: "MarkUp",
        field: "markup",
        sortable: true,
        width: 110,
        filter: true,
        type: "rightAligned", 
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },      
      },
      {
        headerName: "Cost",
        field: "cost",
        sortable: true,
        type: "rightAligned",
        width: 100,
        filter: true,
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
        valueFormatter: (params: any) => {
          return params.value ? params.value.toLocaleString() : '';
        },
    
      },
      {
        headerName: "Sale",
        field: "sale",
        width: 100,
        type: "rightAligned",
        sortable: true,
        filter: true,
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
      {
        headerName: "Count",
        field: "count",
        width: 100,
        type: "rightAligned",
        sortable: true,
        filter: true,
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
      {
        headerName: "Qty",
        field: "quantity",
        width: 100,
        type: "rightAligned",
        sortable: true,
        filter: true,
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
    ];
    if (!$("#partSummary").is(":visible")) {
      $("#partSummary").modal("show");
    }
  }
  displayLaborPayTypeSummary(){
    let schemaData: any = this.getSchemaURL(this.dmsList);
    const requestUriData = {
      schedulerId: this.schedulerCommonId,
      companyImportId: this.companyCommonId,
      operationName: "laborSummaryList",
    };
    let url = schemaData.url;
    let dmsList: any = schemaData.dmsCode;
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
      "Content-Type": "application/json",
      // requestdata: JSON.stringify(requestUriData),
    });
    this.http
      .post<any>(url, JSON.stringify(requestUriData), { headers: headers })
      .subscribe((res: any) => {
        if (res) {
          this.rowDataLaborSummary= res.data.paytypeAllLaborSummary
        }
      });
   // this.columnDefsPayList=[];
    this.combinedPaytypeLists=[...this.customAllList, ...this.warrantyAllList];
    this.columnDefsLaborSummary = [
      {
        headerName: "Pay-Types",
        field: "pay_type",
        sortable: true,
        width: 130,
        filter: true,
        valueFormatter: (params:any) => params.value ? params.value : "N/A",
      },
      {
        headerName: "Rate",
        field: "rate",
        sortable: true,
        width: 110,
        filter: true,
        type: "rightAligned",
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
      {
        headerName: "Hours",
        field: "hours",
        sortable: true,
        type: "rightAligned",
        width: 100,
        filter: true,
        valueFormatter: (params: any) => {
          return params.value ? params.value.toLocaleString() : '';
        },
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
      {
        headerName: "Sale",
        field: "sale",
        width: 100,
        type: "rightAligned",
        sortable: true,
        filter: true,
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
      {
        headerName: "Count",
        field: "count",
        width: 100,
        type: "rightAligned",
        sortable: true,
        filter: true,
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
      {
        headerName: "Qty",
        field: "quantity",
        width: 100,
        type: "rightAligned",
        sortable: true,
        filter: true,
        comparator: (valueA: any, valueB: any) => {
          const numericValueA = Number(valueA) + 1;
          const numericValueB = Number(valueB) + 1;
          if (isNaN(numericValueA) || isNaN(numericValueB)) {
            return 0;
          } else {
            return numericValueA - numericValueB;
          }
        },
      },
    ];
    if (!$("#laborSummary").is(":visible")) {
      $("#laborSummary").modal("show");
    }
  }
  closePartPaytypeModal(){
    if ($("#partPaytype").is(":visible")) {
      $("#partPaytype").modal("hide");
    }
  }
  closeLaborPaytypeModal(){
    if ($("#laborPaytype").is(":visible")) {
      $("#laborPaytype").modal("hide");
    }
  }
  closePartSummaryModal(){
    if ($("#partSummary").is(":visible")) {
      $("#partSummary").modal("hide");
    }
  }
  closeLaborSummaryModal(){
    if ($("#laborSummary").is(":visible")) {
      $("#laborSummary").modal("hide");
    }
  }
  onShowAllChange(event: Event): void {
    this.showAllStatus=true;
    console.log(this.fileShowList,event, "fileShowList")
    this.storeFileList=[];
    this.showAll = (event.target as HTMLInputElement).checked;
    if (this.showAll) {
      // Logic to show all files
     this.fileShowList.forEach((file: any) => {
        // Push each file into the storeFileList
        // this.storeFileList = [...this.fileShowList]; 
        this.storeFileList.push({ id: file.fileName, itemName: file.fileName });
        console.log("this.storeFileList", this.storeFileList);
     });
      console.log(this.fileList,this.fileShowList,this.storeFileList, "testdataaaaaaaaaaa")
    } else {
      // Logic to reset to default view
      this.fileList = []; // Adjust as per your requirement
      console.log(this.store[0],"store---")
      this.getSchemaData(this.store[0].itemName, "direct");
    }
  }  
  isMatchingDMS(selectedFiledmsCode: string, selectedStoredmsCode: string): boolean {
    console.log(selectedStoredmsCode,"selectedStoredmsCode");
    console.log(selectedFiledmsCode,"selectedFiledmsCode");
    const dmsMapping = new Map<string, string[]>([
        ["DealerTrack", ["DealerTrack"]],
        ["ReynoldsRCI", ["ReynoldsRCI", "Reynolds"]],
        ["MPK", ["MPK"]],
        ["AutoMate", ["AutoMate", "Auto/Mate"]],
        ["Tekion", ["Tekion"]],
        ["TekionAPI", ["TekionAPI"]],
        ["PBS", ["PBS"]],
        ["DealerBuilt", ["DealerBuilt"]],
        ["AutoSoft", ["AutoSoft"]],
        ["DominionVue", ["DominionVue", "Dominion", "Dominion / VUE", "Dominion / ACS"]],
        ["CDK", ["CDK", "CDK3PA","CDKDash","CDK Drive"]],
        ["CDKFLEX", ["CDKFLEX"]],
        ["FORTELLIS", ["FORTELLIS"]],
        ["ADAM", ["ADAM"]],
        ["Quorum", ["Quorum"]],
        ["Fortellis", ["Fortellis"]],
    ]);

    for (const values of dmsMapping.values()) {
        if (values.includes(selectedFiledmsCode) && values.includes(selectedStoredmsCode)) {
          console.log("matching")
            return true;
        }
    }
    return false;
}

  
}

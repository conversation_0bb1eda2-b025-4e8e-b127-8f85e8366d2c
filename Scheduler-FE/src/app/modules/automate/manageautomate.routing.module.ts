import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManageautomateComponent } from './manageautomate.component';

const routes: Routes = [
    { path:"", component: ManageautomateComponent ,
        data: {
            title: "ManageAutomate",
            breadcrumb: [{ label: "ManageAutomate", url: "" }],
        },
    }
];

@NgModule({
    exports: [RouterModule],
    imports:[RouterModule.forChild(routes)]
})
export class ManageAutomateRoutingModule{}
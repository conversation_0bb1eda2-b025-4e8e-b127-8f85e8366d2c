import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManageucsComponent } from './manageucs.component';

const routes: Routes = [
    { path:"", component: ManageucsComponent,
    data: {
        title: "ManageUCS",
        breadcrumb: [{ label: "ManageUCS", url: "" }],
      }, }
];

@NgModule({
    exports: [RouterModule],
    imports:[RouterModule.forChild(routes)]
})
export class ManageucsRoutingModule{}
import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManagedominionComponent } from './managedominion.component';
const routes: Routes = [
    {
      path: "",
      component: ManagedominionComponent,
      data: {
        title: "ManageDominion",
        breadcrumb: [{ label: "ManageDominion", url: "" }],
      },
    },
  ];
@NgModule({
    exports: [RouterModule],
    imports:[RouterModule.forChild(routes)]
})
export class ManagedominionRoutingModule{}
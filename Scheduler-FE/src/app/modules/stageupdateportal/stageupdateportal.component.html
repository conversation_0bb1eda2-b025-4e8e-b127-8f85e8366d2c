<div class="row">
  <div class="col-lg-12 import-grid">
    <div *ngIf="isAuthenticated">
      <section class="card" order-id="card-1">
        <div class="card-header cursor_default" style="display: none">
          <span class="cat__core__title">
            <div style="float: right; font-size: 16px; color: #000">
              <span> </span>
              <span
                style="
                  position: absolute;
                  left: 85px;
                  top: 10px;
                  cursor: pointer;
                  color: gray;
                "
              >
              </span>
              <span style="position: absolute; left: 20px; top: 10px"> </span>
            </div>
          </span>
        </div>
        <div class="card-block">
          <form [formGroup]="stagePortalForm">
            <div class="row">
              <div class="col-lg-1">
                <div class="form-group">
                  <label class="col-form-label" style="font-weight: 700"
                    >Project Id <span class="required"> *</span>
                  </label>
                  <div>
                    <div
                      class="typeahead__container scenario-dropdown"
                      ondragstart="return false"
                      draggable="false"
                    >
                      <div class="typeahead__field">
                        <div class="typeahead__query">
                          <div class="input-group">
                            <input
                              id="inProjectId"
                              class="form-control"
                              formControlName="inProjectId"
                              name=""
                              type="text"
                              placeholder="Enter Project Id"
                              autocomplete="off"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-2">
                <div class="form-group">
                  <label class="col-form-label" style="font-weight: 700"
                    >Source <span class="required"> *</span>
                  </label>
                  <div>
                    <div
                      class="typeahead__container scenario-dropdown"
                      ondragstart="return false"
                      draggable="false"
                    >
                      <div class="typeahead__field">
                        <div class="typeahead__query">
                          <div class="input-group">
                            <select
                              class="form-control"
                              id="inSource"
                              formControlName="inSource"
                            >
                              <option
                                *ngFor="let option of sourceList"
                                [ngValue]="option.id"
                                [attr.selected]="$index == 1 ? true : null"
                              >
                                {{ option.itemName }}
                              </option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-1">
                <div class="form-group">
                  <label class="col-form-label" style="font-weight: 700"
                    >Action <span class="required"> *</span>
                  </label>
                  <div>
                    <div
                      class="typeahead__container scenario-dropdown"
                      ondragstart="return false"
                      draggable="false"
                    >
                      <div class="typeahead__field">
                        <div class="typeahead__query">
                          <div class="input-group">
                            <select
                              class="form-control"
                              id="inAction"
                              formControlName="inAction"
                            >
                              <option
                                *ngFor="let option of actionList"
                                [ngValue]="option.id"
                                [attr.selected]="$index == 1 ? true : null"
                              >
                                {{ option.itemName }}
                              </option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-1">
                <div class="form-group">
                  <label class="col-form-label" style="font-weight: 700"
                    >Performed On <span class="required"> *</span>
                  </label>
                  <div>
                    <div
                      class="typeahead__container scenario-dropdown"
                      ondragstart="return false"
                      draggable="false"
                    >
                      <div class="typeahead__field">
                        <div class="typeahead__query">
                          <div class="input-group">                         
                            <input
                              type="text"
                              formControlName="inPerformedOn"
                              class="form-control datepicker-only-init"
                              id="inPerformedOn"
                              name="inPerformedOn"
                              #inPerformedOn
                              (blur)="setDtinvoiceDate(inPerformedOn)"
                              placeholder="Select Date"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-1">
                <div class="form-group">
                  <label class="col-form-label" style="font-weight: 700"
                    >Assignee <span class="required"> *</span>
                  </label>
                  <div>
                    <div
                      class="typeahead__container scenario-dropdown"
                      ondragstart="return false"
                      draggable="false"
                    >
                      <div class="typeahead__field">
                        <div class="typeahead__query">
                          <div class="input-group">
                            <input
                              id="inAssignee"
                              class="form-control"
                              formControlName="inAssignee"
                              name=""
                              type="text"
                              placeholder="Enter Assignee"
                              autocomplete="off"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-1">
                <div class="form-group">
                  <label class="col-form-label" style="font-weight: 700"
                    >Performed By <span class="required"> *</span>
                  </label>
                  <div>
                    <div
                      class="typeahead__container scenario-dropdown"
                      ondragstart="return false"
                      draggable="false"
                    >
                      <div class="typeahead__field">
                        <div class="typeahead__query">
                          <div class="input-group">
                            <input
                              id="inPerformedBy"
                              class="form-control"
                              formControlName="inPerformedBy"
                              name=""
                              type="text"
                              placeholder="Enter Performed By"
                              autocomplete="off"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-2">
                <div class="form-group">
                  <label class="col-form-label" style="font-weight: 700"
                    >Dms <span class="required"> *</span>
                  </label>
                  <div>
                    <div
                      class="typeahead__container scenario-dropdown"
                      ondragstart="return false"
                      draggable="false"
                    >
                      <div class="typeahead__field">
                        <div class="typeahead__query">
                          <div class="input-group">
                            <select
                              class="form-control"
                              id="inDms"
                              formControlName="inDms"
                            >
                              <option
                                *ngFor="let option of dmsList"
                                [ngValue]="option.id"
                                [attr.selected]="$index == 1 ? true : null"
                              >
                                {{ option.itemName }}
                              </option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-1">
                <div class="form-group">
                  <label class="col-form-label" style="font-weight: 700"
                    >Submission Id <span class="required"> *</span>
                  </label>
                  <div>
                    <div
                      class="typeahead__container scenario-dropdown"
                      ondragstart="return false"
                      draggable="false"
                    >
                      <div class="typeahead__field">
                        <div class="typeahead__query">
                          <div class="input-group">
                            <input
                              id="inSubmissionId"
                              class="form-control"
                              formControlName="inSubmissionId"
                              name=""
                              type="text"
                              placeholder="Enter Submission Id"
                              autocomplete="off"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div style="margin-top: 37px">
                <button
                  class="btn btn-primary pointer ml-2"
                  [disabled]="stagePortalForm.invalid"
                  (click)="onSubmit()"
                >
                  Send
                </button>

                <button
                  class="btn btn-secondary pointer ml-2"
                  [disabled]="stagePortalForm.invalid"
                  (click)="onClear()"
                >
                  Clear
                </button>
              </div>
            </div>
          </form>
        </div>
        <!-- <flash-messages></flash-messages> -->
      </section>
    </div>
  </div>
</div>

import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { CommonService } from "../../structure/services/common.service";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Subject, takeUntil } from "rxjs";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import moment from "moment-timezone";
import { environment } from "src/environments/environment";
import { ToastrService } from "ngx-toastr";
import { SharedModule } from "../shared/shared.module";
import { ConstantService } from "src/app/structure/constants/constant.service";
import { SubscriptionConstantService } from "src/app/structure/constants/subscription.constant.service";
declare var $: any;

@Component({
  selector: "app-stageupdateportal",
  templateUrl: "./stageupdateportal.component.html",
  styleUrls: ["./stageupdateportal.component.css"],
  standalone: true,

  imports: [SharedModule],
})
export class StageupdateportalComponent implements OnInit {
  public isAuthenticated = true;
  private subscription$ = new Subject();
  public stagePortalForm!: FormGroup;
  public $index: any;
  sourceList: any[] = [];
  actionList: any[] = [];
  dmsList: any[] = [];

  constructor(
    private router: Router,
    private commonService: CommonService,
    public formBuilder: FormBuilder,
    private httpClient: HttpClient,
    private toastrService: ToastrService,
    public constantService: ConstantService,
    public subscriptionConstantService: SubscriptionConstantService
  ) {}
  ngOnInit() {
    this.stagePortalForm = this.formBuilder.group({
      inProjectId: ["", Validators.required],
      inSource: ["", Validators.required],
      inAction: ["", Validators.required],
      inPerformedOn: ["", Validators.required],
      inAssignee: ["", Validators.required],
      inPerformedBy: ["", Validators.required],
      inDms: ["", Validators.required],
      inSubmissionId: ["", Validators.required],
    });
    this.sourceList.push(
      { id: "Scheduler", itemName: "Scheduler" },
      { id: "SchedulerImport", itemName: "SchedulerImport" },
      { id: "SchedulerFileImport", itemName: "SchedulerFileImport" }
    );
    this.actionList.push(
      { id: "assign", itemName: "assign" },
      { id: "fail", itemName: "fail" },
      { id: "resume", itemName: "resume" },
      { id: "halt", itemName: "halt" },
      { id: "complete", itemName: "complete" }
    );
    this.dmsList = [
      { id: "CDK3PA", itemName: "CDK3PA" },
      { id: "DealerTrack", itemName: "DealerTrack" },
      { id: "DealerBuilt", itemName: "DealerBuilt" },
      { id: "Reynolds", itemName: "Reynolds" },
      { id: "Auto/Mate", itemName: "Auto/Mate" },
      { id: "Tekion", itemName: "Tekion" },
      { id: "PBS", itemName: "PBS" },
      { id: "MPK", itemName: "MPK" },
      { id: "Dominion / VUE", itemName: "Dominion / VUE" },
      { id: "QUORUM", itemName: "QUORUM" },
      { id: "ADAMS", itemName: "ADAMS" },
      { id: "AutoSoft", itemName: "AutoSoft" },
    ];
    let activityData = {
      activityName: "Stage Update Portal",
      activityType: "Stage Update Portal",
      activityDescription: "Current Url: " + this.router.url,
    };
    this.subscriptionConstantService.pageTitle = " - Update Portal Stage";
    this.commonService.saveActivity("Stage Update Portal", activityData);
    this.init();
  }
  ngOnDestroy() {
    this.subscription$.next(void 0);
    this.subscription$.complete();
  }
  getCalenderPropertyObject(maxDate: any) {
    let settings: any = {
      useCurrent: true,
      widgetPositioning: {
        horizontal: "left",
      },
      icons: {
        time: "fa fa-clock-o",
        date: "fa fa-calendar",
        up: "fa fa-arrow-up",
        down: "fa fa-arrow-down",
        previous: "fa fa-arrow-left",
        next: "fa fa-arrow-right",
      },
      format: "MM-DD-YYYY",
    };
    if (maxDate) {
      settings["maxDate"] = new Date();
    }
    return settings;
  }
  init() {
    const elm = this;

    $(function () {
      let objPropertyCalender: { [k: string]: any } = {};
      objPropertyCalender = elm.getCalenderPropertyObject(false);
      $("#inPerformedOn").datetimepicker(objPropertyCalender);
    });
    this.stagePortalForm.patchValue({
      inPerformedOn: moment().format("MM-DD-YYYY"),
    });
  }
  setDtinvoiceDate(e: any) {
    if (e.value && e.value !== "") {
      this.stagePortalForm.patchValue({
        inPerformedOn: e.value,
      });
    } else {
      this.stagePortalForm.patchValue({
        inPerformedOn: null,
      });
    }
  }
  onClear() {
    this.stagePortalForm.reset();
    this.stagePortalForm.patchValue({
      inPerformedOn: moment().format("MM-DD-YYYY"),
    });
  }
  onSubmit() {
    if (this.stagePortalForm.valid) {
      console.log(this.stagePortalForm.value);
      const token = localStorage.getItem("token");
      const authenticationHeader = new HttpHeaders({
        authorization: "Bearer " + token,
        "Content-Type": "application/json",
      });
      const variables = this.stagePortalForm.value;
      this.httpClient
        .post(
          environment.redirectUri + "scheduler/stageUpdatePortal", //change after API creation
          JSON.stringify(variables),
          {
            headers: authenticationHeader,
          }
        )
        .subscribe({
          next: (listdata) => {
            let result: any = listdata;
            // console.log("result---------------", result);
            if (result.data.status === this.constantService.SUCCESS_MESSAGE) {
              const message = "Added to Process Queue";
              this.showStatusMessage(message, "success");
              this.onClear();
            } else {
              const message = "Process Failed";
              this.showStatusMessage(message, "failed");
            }
          },
          error: (err: any) => {
            this.commonService.errorCallback(err, this);
          },
          complete: () => {
            console.log("Completed");
          },
        });
    } else {
      console.log("Form is invalid");
    }
  }
  showStatusMessage(message: any, statusType: any) {
    if (statusType === "success") {
      this.toastrService.success(message);
    } else {
      this.toastrService.error(message);
    }
  }
}

import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManagetekionComponent } from './managetekion.component';

const routes: Routes = [
    { path:"", component: ManagetekionComponent,
    data: {
        title: "ManageTekion",
        breadcrumb: [{ label: "ManageTekion", url: "" }],
      },
 }
];

@NgModule({
    exports: [RouterModule],
    imports:[RouterModule.forChild(routes)]
})
export class ManagetekionRoutingModule{}
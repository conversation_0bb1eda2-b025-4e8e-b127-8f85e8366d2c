import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ementRef, Renderer2 } from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { ConstantService } from "../../structure/constants/constant.service";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Apollo } from "apollo-angular";
import gql from "graphql-tag";
import { Router } from "@angular/router";
import { CommonService } from "../../structure/services/common.service";
import * as moment from "moment-timezone";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { EventEmitterService } from "../../structure/services/event.emitter.services";
import { SchedulerConstantService } from "../../structure/constants/scheduler.constant.service";
import { DmFormGroupService } from "../../structure/services/dm.formgroup.services";
import { environment } from "../../../environments/environment";

import { SubscriptionConstantService } from "./../../structure/constants/subscription.constant.service";
import { IDropdownSettings } from "ng-multiselect-dropdown";
import { Subject, takeUntil } from "rxjs";
import { SharedModule } from "../shared/shared.module";

let table1;
declare var $: any;
declare var jQuery: any;
declare var swal: any;
declare var NProgress: any;

const getAllTekionExtractJobs = gql`
  query getAllTekionExtractJobs {
    getAllTekionExtractJobs {
      timeFrameZone
      timeFrameStartTime
      timeFrameEndTime
      poolTime
      jobArray {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
        running
        scheduled
        queued
        completed
        failed
        repeating
        failReason
        data {
          groupName
          storeDataArray {
            locationId
            sourceId
            activityStoreId
            projectId
            secondProjectId
            mageManufacturer
            solve360Update
            buildProxies
            userName
            inputFilePath
            fileDate
            invoiceMasterCSVFilePath
            switchBranch
            customBranchName
            etlDMSType
            startDate
            endDate
            message
            startTime
            endTime
            closedROOption
            status
            jobType
            mageGroupCode
            mageStoreCode
            projectIds
            secondProjectIdList
            companyObj
          }
        }
      }
    }
  }
`;

const getAllTekionProcessJSONJobs = gql`
  query getAllTekionProcessJSONJobs {
    getAllTekionProcessJSONJobs {
      processJSONJobs {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
        running
        scheduled
        queued
        completed
        failed
        repeating
        failReason
        data {
          inputFile
          outputFile
          status
          message
          operation
          createdAt
          splitJobExceptionCount
          lessSpecialDiscountCount
        }
      }
      processJSONJobsQueue {
        storeID
        fileToProcess
      }
    }
  }
`;

const createNewTekion = gql`
  mutation scheduleTekionExtractJob(
    $jobSchedule: DateTime!
    $jobData: JobData!
  ) {
    scheduleTekionExtractJob(
      input: { jobSchedule: $jobSchedule, jobData: $jobData }
    ) {
      status
      message
      job {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
      }
    }
  }
`;

/**
 * Mutation to cancel schedule
 *
 */
const cancelTekionExtractJobByStore = gql`
  mutation cancelTekionExtractJobByStore(
    $jobSchedule: DateTime!
    $jobData: SingleStoreJobData!
  ) {
    cancelTekionExtractJobByStore(
      input: { jobSchedule: $jobSchedule, jobData: $jobData }
    ) {
      status
      message
      job {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
      }
    }
  }
`;

const runNowTekionExtractJobByStore = gql`
  mutation runNowTekionExtractJobByStore(
    $jobSchedule: DateTime!
    $jobData: SingleStoreJobData!
  ) {
    runNowTekionExtractJobByStore(
      input: { jobSchedule: $jobSchedule, jobData: $jobData }
    ) {
      status
      message
      job {
        name
        type
        priority
        nextRunAt
        _id
        lastModifiedBy
        lockedAt
        lastRunAt
        lastFinishedAt
      }
    }
  }
`;

@Component({
  selector: "app-managetekion",
  templateUrl: "./managetekion.component.html",
  styleUrls: ["./managetekion.component.css"],
  standalone: true,
  imports: [
    SharedModule,
  ],
})
export class ManagetekionComponent implements OnInit {
  public isAuthenticated = false;
  private subscription$ = new Subject();
  loading: any = false;
  private storeGroupList: any[] = [];
  private storeList: any[] = [];
  private storeFlag = false;
  private selectedGroup = null;
  private selectedStore = null;
  private storeGroupFlag = false;
  private storeLoading: any = false;
  private scheduleDate = null;
  private processQueueList: any[] = [];
  private processQueueListCompleted: any[] = [];
  private listener: any;
  private jobGroupList: any[] = [];
  private timeFrameZone: any;
  private processJsonListArray: any[] = [];
  private roOptionList: any;
  private onChangeRoOption: any[] = [];
  private compareObjArray: any[] = [];
  private compareObjArrayLatest: any[] = [];
  private compareObjArrayProcessJSONList: any[] = [];
  private processJSONJobsQueueLength = 0;
  private jobTypeStatus = true;
  private autoReloadTekion3PAStatus = true;
  public storeGroupFilterList: any[] = [];
  public storeFilterList: any[] = [];
  public inputFileList: any[] = [];
  public archiveFileList: any[] = [];
  public temp: any[] = [];
  public inputFileFilterList: any[] = [];
  public invoiceMasterFileList: any[] = [];
  public inputFile: any[] = [];
  public invoiceMasterFile: any[] = [];
  public store: any[] = [];
  public storeGroup: any[] = [];
  public scheduleProcessQueueLoading = false;
  public scheduleProcessQueueCompletedLoading = false;
  public processJSONLoading = false;
  public createSchedule!: FormGroup;
  public loadingSchedule = false;
  public loadingScheduleCompleted = false;
  public loadingProcessJson = false;
  public loadAllStore = false;
  public completedListCollapsed = true;
  public processQueueListCollapsed = false;
  public completedProcessjsonListCollapsed = false;
  public timeFrameStartTime: any;
  public timeFrameEndTime: any;
  public tootlTipInfo = null;
  public tootlTipInfoTitle = null;
  public selectMultiDateRangeOption = true;
  public reloadGroup = false;
  public isPaused = true;
  public isProcessing = false;
  public displayOnDemand = false;
  public archiveDecider!: boolean;
  public solve360ServerDecider!: boolean;
  public isMockServer: boolean = true;
  public splitJobExceptionCountMsg: any;
  public lessSpecialDiscountCountMsg: any;
  public suffixedInvoices = [];
  public processRunningStatus ='';
  public processorStatusUpdatedAt ='';
  public dateInput: any = {
    start: this.startDateSelection(),
    end: this.endDateSelection(),
  };
  public tekionObject = {
    locationId: "44102",
    sourceId: "208",
    activityStoreId: "440",
  };

  public dateRangePickerOptions: any = {
    startDate: this.dateInput.start,
    endDate: this.dateInput.end,
    showDropdowns: true,
    autoApply: true,
    opens: "left",
    maxDate: new Date(),
    disabled: false,
  };
  public singleDropdownSettings: any;
  public dropdownSettings: any;
  public singleDropdownSettingsDisable: any;
  public multiDropdownSettings: any;
  public multiDropdownSettingsDisable: any;

  // singleDropdownFileSettings = {
  //   singleSelection: true,
  //   text: "Select File",
  //   selectAllText: "Select All",
  //   unSelectAllText: "UnSelect All",
  //   enableSearchFilter: true,
  //   classes: "single-selection",
  //   labelKey: "filename",
  //   primaryKey: "filename",
  // };
  singleDropdownFileSettings: IDropdownSettings = {
    idField: "filename",
    textField: "filename",
    allowSearchFilter: true,
    singleSelection: true,
    // text: "Select File",
    selectAllText: "Select All",
    unSelectAllText: "UnSelect All",
    // classes: "single-selection",
    // labelKey: "filename",
    // primaryKey: "filename",
    closeDropDownOnSelection: true
  };
  public agendaDashboardUrl = "";
  public shows360Link: boolean = false;
  public s360CompanyId: any;
  public resumeProcessorInput: any;
  public haltState!: string;
  public invalidmiscpaytypeCountMsg: any;
  public estimateCountMsg: any;
  public suffixedInvoicesCountMsg: any;
  public punchTimeMissingMsg: any;
  public miscExceptionCountMsg: any;
  public poolTime: any;
  public dealerNumberID!: string;
  public storeId!: string;
  public branchNumber!: string;
  public storeNameLabel!: string;
  public switchBranch: boolean = false;
  public suffixedInvoicesCsvData!: String;
  constructor(
    private http: HttpClient,
    private apollo: Apollo,
    public constantService: ConstantService,
    private elRef: ElementRef,
    private renderer: Renderer2,
    private toastrService: ToastrService,
    private commonService: CommonService,
    private EventEmitterService: EventEmitterService,
    private router: Router,
    private DmFormGroupService: DmFormGroupService,
    private SchedulerConstantService: SchedulerConstantService,
    public SubscriptionConstantService: SubscriptionConstantService,
    // private modal: Modal
  ) {}

  ngOnDestroy() {
    this.autoReloadTekion3PAStatus = false;
    localStorage.removeItem("selectedStoreObj");
    localStorage.removeItem("selectedGroupObj");
  }
  ngOnInit() {
    let activityData = {
      activityName: "Manage Tekion",
      activityType: "Manage Tekion",
      activityDescription: "Current Url: " + this.router.url,
    };
    this.commonService.saveActivity("Manage Tekion", activityData);
    this.SubscriptionConstantService.pageTitle = " - TEKION";
    let scheduleUrl = environment.jobListUrl;
    scheduleUrl = scheduleUrl.replace(
      this.SchedulerConstantService.END_POINTS.GRAPHQL_END_POINT,
      this.SchedulerConstantService.END_POINTS.AGENDA_END_POINT
    );
    this.agendaDashboardUrl = scheduleUrl;
    this.dropdownSettings = this.DmFormGroupService.dropdownSettings();
    this.singleDropdownSettings =
      this.DmFormGroupService.singleDropdownSettings();
    this.singleDropdownSettingsDisable =
      this.DmFormGroupService.singleDropdownSettingsDisable();
      this.multiDropdownSettings =
      this.DmFormGroupService.multiDropdownSettings();
    this.commonService.getGroups(() => {
      this.commonService.checkGroups((flag) => {
        if (!flag) {
          return;
        }
        this.isAuthenticated = true;
        this.init();
      });
    });
  }

  init() {
    console.log("init----------");
    this.getRoOptionList();
    this.createSchedule = new FormGroup({
      storeGroup: new FormControl("", Validators.required),
      store: new FormControl("", Validators.required),
      inputFile: new FormControl("", Validators.required),
      invoiceMasterFile: new FormControl(""),
      roOption: new FormControl(
        this.SchedulerConstantService.DEFAULT_RO_OPTION
      ),
      updateRetreiveROinSolve360: new FormControl(true),
      buildProxies: new FormControl(true),
      switchBranch: new FormControl(false),
      branchName: new FormControl(""),
      jobType: new FormControl(this.SchedulerConstantService.DEFAULT_JOB_TYPE),
      archiveDecider: new FormControl(false),
      solve360ServerDecider: new FormControl(false),
    });
    this.getAllJobs(() => {
      this.showScheduledProcessList(true);
      this.showScheduledProcessListCompleted(true);
    });
    this.getAllProcessJsonJobs(() => {
      this.showProcessJsonList();
    });
    this.loading = true;
    this.commonService.allS360Jobs("Tekion", "production", (result: any) => {
      this.loading = false;
      this.storeGroupList = result.storeGroupList;
      this.storeList = result.storeList;
      this.jobGroupList = result.jobGroupList;
      this.getGroupFilterList();
      this.preSelectGroupAndStore();

      this.getTekionInputFiles(
        environment.tekionInputFileFolderPath,
        (result: any) => {
          result.forEach((data: any, index: any) => {
            // let dat, date, dateLabel, len;
            // dat = data.filename.split("_");
            // // date = dat.reverse()[0].substring(0, 8);
            // console.log(':::::::::::::::::::::::::::::::::::::DAT',dat);
            // if(dat){
            //   len = dat.length-1;
            // }
            // if(len){
            //   date = dat[len].substring(0, 8);
            // }
            // console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>date',date);
            // if(date){
            //   dateLabel = date.substring(4,6) + "/" + date.substring(6, 8) + "/" + date.substring(0, 4);
            //   console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>datelabel',dateLabel);
            // }

            // data.filename = dat[0] + "_" + dat[1] + "-" + dateLabel;
            // data.tmpFilename= dat[0] + "_" + dat[1] + "-" + dateLabel;
            data.filename = data.filename;
            data.tmpFilename = data.filename;
          });

          const filteredArr = result.reduce((thing: any, current: any) => {
            const x = thing.find(
              (item: any) => item.tmpFilename == current.tmpFilename
            );
            if (!x) {
              return thing.concat([current]);
            } else {
              return thing;
            }
          }, []);
          console.log(filteredArr, "filteredArr");
          this.inputFileList = filteredArr;

          console.log(this.inputFileList);
          console.log("this.inputFile---------->storeGroupList",this.storeGroupList);

          let tmp;
          this.inputFileList.forEach((item1) => {
            tmp = this.storeGroupList.filter((item2) => {
              return item1.filename.split("__")[0] == item2.thirdPartyUsername;
            });
            if (tmp) {
              if (tmp.length > 0) {
                item1.filename =
                  item1.filename + "[" + tmp[0].mageStoreName + "]";
              }
            }
          });
          console.log("this.inputFile---------->inputFileList",this.inputFileList);

        }
      );
    });

    let objPropertyCalender: { [k: string]: any } = {};
    objPropertyCalender = this.commonService.getCalenderPropertyObject();
    objPropertyCalender["minDate"] = new Date();
    $(() => {
      $('[data-toggle="tooltip"]').tooltip({
        trigger: "hover",
        html: true,
      });
    });
    if (!this.listener) {
      this.listener = this.renderer.listen(
        this.elRef.nativeElement,
        "click",
        (evt) => {
          if (evt.target.className === "fa fa-play-circle overrideSchedule") {
            this.runNowSchedule(evt.target.dataset.info);
          } else if (evt.target.className === "fa fa-ban cancelSchedule") {
            this.cancelSchedule(evt.target.dataset.info);
          } else if (
            evt.target.className.indexOf("statusMessageDisplay") != -1
          ) {
            let res = evt.target.dataset.info.split(",");
            let toolTipInfo = this.getProcessJsonToolTipData(res);
            this.showAlert(toolTipInfo, "Process JSON Status");
          } else if (
            evt.target.className.indexOf("popUpInfoCompletedJobs") != -1
          ) {
            let res = evt.target.dataset.info.split(",");
            var toolTipInfo = this.getToolTipInfoCompletedJobs(res);
            this.showAlert(toolTipInfo, "Completed Extractions");
          } else if (
            evt.target.className ===
            "fa fa-caret-square-o-up text-success mt-2 haltAndResumeDetails"
          ) {
            this.haltAndResumeDetails(evt.target.dataset.info);
          }
          else if (
            evt.target.className ===
            "fa fa-info-circle text-dark mt-2 showProcessorStatus"
          ) {
            this.showProcessorStatus(evt.target.dataset.info);
          }
        }
      );
    }
    this.getNotificationForUi();
  }
  
  getNotificationForUi() {
    let self = this;
    if (this.autoReloadTekion3PAStatus) {
      this.getAllJobsForUiUpdate((data: any) => {
        this.compareObjectValue();
        this.processJSONReloadList((result: any) => {
          this.compareObjectValueProcessJSON();
          setTimeout(() => {
            self.getNotificationForUi();
          }, 3000);
        });
      });
    }
  }

  getProcessJsonToolTipData(rowData: any) {
    let toolTip = "";
    let className = "";
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.COMPLETED
      ? (className = "label-success")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.SCHEDULED
      ? (className = "label-scheduled")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.RUNNING
      ? (className = "label-running")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.REPEATING
      ? (className = "label-repeating")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.FAILED
      ? (className = "label-failed")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.QUEUED
      ? (className = "label-queued")
      : null;
    rowData[2] === this.SchedulerConstantService.STATUS_FLAG.LOCKED
      ? (className = "label-locked")
      : null;
    status = '<span class="label ' + className + '">' + rowData[2] + "</span>";
    toolTip += rowData[8] + " \n";
    if (rowData[7] && rowData[2] === "Failed") {
      toolTip += "Failed Reason: " + rowData[7] + " \n";
    }
    toolTip += "Status: " + status + " \n";
    return toolTip;
  }

  showAlert(toolTipText: any, title: any) {
    this.tootlTipInfo = toolTipText.split("\n").join("<br>");
    this.tootlTipInfoTitle = title;
    $("#resolveProjectModal").modal({ show: true, backdrop: "static" });
  }

  closeToolTipModal() {
    $("#resolveProjectModal").modal("hide");
  }

  /**
   * callback function for store group deselection
   *
   */
  OnDeSelectStoreGroup(item: any) {
    // if (!this.containsObject(item, this.storeGroup)) {
    //   this.storeGroup.push(item);
    // }
    // this.getGroupFilterList();
    this.store = [];
    this.storeFilterList = [];
    this.inputFile =[];
    this.getGroupFilterList();
  }
 
  OnDeSelectStore(item: any) {
    if (!this.containsObject(item, this.store)) {
      this.store.push(item);
    }
    this.getStoreFilterList();
  }

  /**
   * getGroupFilterList function will collect the group list for filtering purpose
   *
   */
  getGroupFilterList() {
    this.storeGroupFilterList = [];
    for (let i = 0; i < this.storeGroupList.length; i++) {
      const companyName = this.storeGroupList[i].mageGroupName;
      const mageGroupCode = this.storeGroupList[i].mageGroupCode;
      const mageGroupName = this.storeGroupList[i].mageGroupName
        ? this.storeGroupList[i].mageGroupName
        : "";
      const mageStoreCode = this.storeGroupList[i].mageStoreCode;
      const mageStoreName = this.storeGroupList[i].mageStoreName;
      const companyId = this.storeGroupList[i].companyId;
      const thirdPartyUsername = this.storeGroupList[i].thirdPartyUsername; //locationID
      const sourceId = this.storeGroupList[i].dealerbuiltSourceId;
      const activityStoreId = this.storeGroupList[i].dealerbuiltStoreId;
      const projectId = this.storeGroupList[i].projectId;
      const secondProjectId = this.storeGroupList[i].secondaryProjectId;
      const branchNo = this.storeGroupList[i].branchNo;
      if (companyName) {
        const obj = {
          id: companyName,
          itemName: companyName,
          mageGroupCode: mageGroupCode,
          mageGroupName: mageGroupName,
          mageStoreCode: mageStoreCode,
          mageStoreName: mageStoreName,
          companyId: companyId,
          thirdPartyUsername: thirdPartyUsername,
          sourceId: sourceId,
          activityStoreId: activityStoreId,
          branchNo: branchNo,
          projectId: projectId,
          secondProjectId: secondProjectId,
        };
        if (!this.containsObject(obj, this.storeGroupFilterList)) {
          this.storeGroupFilterList.push(obj);
        }
      }
    }
    this.storeGroupFilterList = this.sortListAsc(this.storeGroupFilterList);
  }
  containsObject(obj: any, list: any) {
    let i;
    for (i = 0; i < list.length; i++) {
      if (list[i].id === obj.id) {
        return true;
      }
    }
    return false;
  }

  /**
   * sortListAsc function will sort the list in ascending order.
   *
   */
  sortListAsc(temp: any) {
    temp = temp
      .filter((item: any, index: number) => index < temp.length)
      .sort((a: any, b: any): any => {
        const x = a["itemName"]
          ? a["itemName"].toLowerCase().replace(/^[^a-z0-9]*/g, "")
          : "";
        const y = b["itemName"]
          ? b["itemName"].toLowerCase().replace(/^[^a-z0-9]*/g, "")
          : "";
        return x < y ? -1 : x > y ? 1 : 0;
      });
    return temp;
  }
  /**
   * callback function for store group selection
   *
   */
  onSelectStoreGroup(item: any) {
    // this.inputFile = []
    this.storeFilterList = [];
    this.selectStoreGroup(item, () => {
      this.shows360Link = true;
      // this.s360CompanyId = item.companyId;
      let itemFilter = this.storeGroupFilterList.find(res => res.id == item.id);
      console.log('s360CompanyId',itemFilter, "storeGroupList" ,this.storeGroupList)
      this.s360CompanyId = itemFilter.companyId;
    });
  }

  selectStoreGroup(item: any, callback: any) {
    this.loadAllStore = true;
    this.storeList = [];
    this.storeFilterList = [];
    this.store = [];
    this.storeFlag = false;
    if (!this.containsObject(item, this.storeGroup)) {
      this.storeGroup.push(item);
    }
    let selectedGroupFilter: any = this.storeGroupList.filter(function (res) {
      return res.sgId == item.id;
    });
    this.selectedGroup = selectedGroupFilter[0];
    //this.getGroupFilterList();
    let itemFilter: any = this.storeGroupFilterList.filter(function (res) {
      return res.id == item.id; //updated as per new array list
    });
    console.log("this.storeGroupFilterList", itemFilter);
    this.getStoreList(itemFilter[0], () => {
      this.loadAllStore = false;
      this.getStoreFilterList();
      this.storeGroupFlag = true;
      if (callback) {
        callback();
      }
    });
  }

  /**
   * getStoreList function fetch Stores list for the selected StoreGroup
   *
   */
  getStoreList(item: any, callback: any) {
    const groupCode = item.mageGroupCode;
    this.loading = true;
    this.storeLoading = true;
    this.storeList = [];
    this.storeList = this.jobGroupList
      .filter((item: any, index: number) => index < this.jobGroupList.length)
      .filter(
        (item: any, index: number) =>
          item.mageGroupCode != null &&
          item.mageGroupCode === groupCode &&
          item.thirdPartyUsername !== null
      );    
    if (callback) {
      callback();
    }
  }

  /**
   * getStoreFilterList function will collect the store list for filtering purpose
   *
   */

  getStoreFilterList() {
    this.storeFilterList = [];
    for (let i = 0; i < this.storeList.length; i++) {
      const companyID = this.storeList[i].companyId;
      const projectId = this.storeList[i].projectId;
      const storeName = this.storeList[i].companyName;
      const stId = this.storeList[i].companyName;
      const mageGroupCode = this.storeList[i].mageGroupName;
      const mageStoreName = this.storeList[i].mageStoreName;
      const mageStoreCode = this.storeList[i].mageStoreCode;
      const thirdPartyUsername = this.storeList[i].thirdPartyUsername; //locationID
      const sourceId = this.storeList[i].dealerbuiltSourceId;
      const activityStoreId = this.storeList[i].dealerbuiltStoreId;
      const stateCode = this.storeList[i].state;
      const etlDMSType = this.storeList[i].etlDms;
      const mageManufacturer = this.storeList[i].mageManufacturer;
      const branchNo = this.storeList[i].branchNo;
      this.dealerNumberID = this.storeList[i].thirdPartyUsername;
      this.storeId = this.storeList[i].dealerbuiltStoreId;
      this.branchNumber = this.storeList[i].branchNo;
      const secondProjectId = this.storeList[i].secondaryProjectId;
      this.storeNameLabel = mageStoreName;
      const projectName = this.storeList[i].projectName;
      const secondaryProjectName = this.storeList[i].secondaryProjectName;

      if (stId) {
        const obj = {
          id: stId,
          itemName: `${storeName} [${thirdPartyUsername}]`,
          mageGroupCode: mageGroupCode,
          mageStoreName: mageStoreName,
          mageStoreCode: mageStoreCode,
          projectId: projectId,
          companyID: companyID,
          thirdPartyUsername: thirdPartyUsername,
          stateCode: stateCode,
          sourceId: sourceId,
          activityStoreId: activityStoreId,
          etlDMSType: etlDMSType,
          mageManufacturer: mageManufacturer,
          branchNo: branchNo,
          secondProjectId: secondProjectId,
          projectName:projectName,
          secondaryProjectName:secondaryProjectName
        };
        if (!this.containsObject(obj, this.storeFilterList)) {
          this.storeFilterList.push(obj);
        }
      }
    }
    this.loading = false;
    this.storeLoading = false;
    this.storeFilterList = this.sortListAsc(this.storeFilterList);
  }

  /**
   * callback function for store selection
   *
   */

  onSelectStore(item: any) {
    // this.store = [];
    this.inputFile = [];
    this.getStoreFilterList();
    if (!this.containsObject(item, this.store)) {
      this.store.push(item);
    }
    const selectedStoreFilter: any = this.storeList.filter(function (res) {
      return res.stId == item.id;
    });
    this.selectedStore = selectedStoreFilter[0];
    // console.log("this.inputFile---------->store",this.store);
    // console.log("this.inputFile---------->dealerNumberID",this.dealerNumberID);
    // console.log("this.inputFile---------->storeTemp",this.storeList);

    //Code for filter webhook dropdown
    // let storeTemp: any = this.storeGroupFilterList.filter( (res)=> {
    //   return res.id == this.store[0].id; //updated as per new array list
    // });
    // console.log("this.inputFile---------->storeTemp",storeTemp);
     this.inputFileFilterList = this.inputFileList;
    this.inputFileFilterList = this.inputFileList.filter(
      (e) =>
        e.filename.split("__")[0] == this.dealerNumberID &&
        e.filename.split("_")[3] == this.store[0].branchNo &&
        e.filename.split("_")[2] == this.store[0].activityStoreId

      // (e) => e.filename.split('__')[0] == this.dealerNumberID && e.filename.split('_')[3] == this.branchNumber && e.filename.split('_')[2] == this.storeId
    );
    console.log("this.inputFile---------->inputFileFilterList",this.inputFileFilterList);

    if (this.inputFileFilterList) {
      if (this.inputFileFilterList.length == 1) {
        this.inputFile.push(this.inputFileFilterList[0]);
        console.log("this.inputFile---------->---",this.inputFile);
      }
    }
    console.log("this.inputFile---------->select",this.inputFile);

    this.storeFlag = true;
  }

  tekionInputFilesDecider() {
    if (this.archiveDecider) {
      this.temp = this.inputFileList;
      this.inputFileList = this.archiveFileList;
    } else {
      this.inputFileList = this.temp;
    }
  }

  /**
   *  Select the date from datepicker in the Filter
   *
   */
  public selectedDate(value: any, dateInput: any) {
    dateInput.start = value.start;
    dateInput.end = value.end;
  }

  saveSchedule() {
    var userName: any;
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
    }
    // const mageGrpCode =
    //   this.createSchedule.get("store")?.value[0].mageGroupCode;
    // const mageStrCode =
    //   this.createSchedule.get("store")?.value[0].mageStoreCode;
    // const steCode = this.createSchedule.get("store")?.value[0].stateCode;
    // const etlDMSType = this.createSchedule.get("store")?.value[0].etlDMSType;

    // const locationId =
    //   this.createSchedule.get("store")?.value[0].thirdPartyUsername;
    // const sourceId = this.createSchedule.get("store")?.value[0].sourceId;
    // const activityStoreId =
    //   this.createSchedule.get("store")?.value[0].activityStoreId;

    // let projectId = this.createSchedule.get("store")?.value[0].projectId
    //   ? this.createSchedule.get("store")?.value[0].projectId
    //   : "";
    let storeFilterListTemp = this.storeFilterList;
    console.log("storelist1 temp",storeFilterListTemp);
    console.log("storelist1 selected",this.createSchedule.get("store"));

    let storeTemp1: any = storeFilterListTemp.filter(
      (x) => x.id == this.createSchedule.get("store")?.value[0].id
    );
    console.log("storelist1 temp1",storeFilterListTemp);

    const mageGrpCode = storeTemp1[0].mageGroupCode;
    const mageStrCode = storeTemp1[0].mageStoreCode;
    const steCode = storeTemp1[0].stateCode;
    const etlDMSType = storeTemp1[0].etlDMSType;

    const locationId = storeTemp1[0].thirdPartyUsername;
    const sourceId = storeTemp1[0].sourceId;
    const activityStoreId = storeTemp1[0].activityStoreId;

    let projectId = storeTemp1[0].projectId ? storeTemp1[0].projectId : "";
    console.log("storeFilterList$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",storeFilterListTemp);

    // const storeTemp = this.createSchedule.get("store")?.value;
    let storeTemp: any = storeFilterListTemp.filter((c: any) =>
      this.createSchedule.get("store")?.value.some((s: any) => s.id === c.id)
    );
    console.log("storeTemp$$$$$$$$$$$$$$$$$$$$$$",storeTemp);

    let tempProject = storeTemp.map((store: any) => store.projectId);

    console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$",tempProject);
    let tempSecond = storeTemp.map((store: any) => store.secondProjectId);
    let companyIdList = storeTemp.map((store: any) => store.companyID);

    let companyObj = storeTemp.map((store: any) => ({
      companyId: store.companyID,
      projectId: store.projectId,
      secondProjectId: store.secondProjectId,
      projectType: store.projectType,
      secondaryProjectType: store.secondaryProjectType,
      projectName: store.projectName,
      secondaryProjectName: store.secondaryProjectName  
    }));
    console.log("companyObj?????????????????????????????????????????????????????????",companyObj);  

    let projectIds = "";
    let secondProjectIdList = "";

    for (let i = 0; i < tempProject.length; i++) {
      projectIds += tempProject[i] + "*";
    }

    for (let i = 0; i < tempSecond.length; i++) {
      if (tempSecond[i] != undefined) {
        secondProjectIdList += tempSecond[i] + "*";
      }
    }

    let dealerIdList = storeTemp.map((store: any) => store.thirdPartyUsername);

    let allDealerIdSame = this.areAllDealerIdSame(dealerIdList);

    //Get second projectId from local storage
    let secondProjectId = "";
    // if (
    //   this.createSchedule
    //     .get("store")
    //     ?.value[0].hasOwnProperty("secondProjectId")
    // ) {
    //   secondProjectId = storeTemp1[0].secondProjectId;
    // }

    if(tempSecond.length>0 && tempSecond[0]!=undefined && tempSecond[0]!=''){
      secondProjectId =  tempSecond[0];    
    }
    const parentID = storeTemp1[0].companyID;
    const companyId: number = parseInt(parentID);

    const mageManufacturer = storeTemp1[0].mageManufacturer;
    console.log(mageManufacturer);

    // for(let i=0;i<companyIdList.length;i++){
    //   this.getSecondProjectId(companyIdList[i],(result)=>{
    //    if(result){
    //      if (result[1]) {
    //        if (result[1].hasOwnProperty('project_id')) {
    //          secondProjectIdList+=result[1].project_id.toString()+'*';
    //        }
    //      }
    //      if (result[0]) {
    //       if (result[0].hasOwnProperty('project_id')) {
    //         projectIds+=result[0].project_id.toString()+'*';
    //       }
    //     }
    //    }
    //   })
    // }
    // console.log('Second project id list after callback',secondProjectIdList)
    let companyIds = '';
    
    // for(let i=0;i<tempProject.length;i++){
    //   projectIds+=tempProject[i]+'*';
    // }
    
    // for(let i=0;i<tempSecond.length;i++){
    //   if(tempSecond[i]!=undefined){
    //   secondProjectIdList+=tempSecond[i]+'*';
    //   }
    // }    

    for(let i=0;i<companyIdList.length;i++){
      if(companyIdList[i]!=undefined){
        companyIds+=companyIdList[i]+'*';
      }
      
    }

    this.getSecondProjectId(companyId, (result: any) => {
      // if (result) {
      //   if (result[0]) {
      //     if (result[0].hasOwnProperty('project_id')) {
      //       projectId = result[0].project_id.toString();
      //     }
      //   }
      //   if (result[1]) {
      //     if (result[1].hasOwnProperty('project_id')) {
      //       secondProjectId = result[1].project_id.toString();
      //     }
      //   }
      // }

      // console.log('projectId:', projectId);
      // console.log('secondProjectId:', secondProjectId);

      let s: string = moment(new Date(this.dateInput.start)).format(
        this.SchedulerConstantService.DATE_FORMAT
      );
      let e: string = moment(new Date(this.dateInput.end)).format(
        this.SchedulerConstantService.DATE_FORMAT
      );
      let dateRangeCompare = s + " - " + e;
      let isExist = this.checkJobExistInExtractionQueue(
        this.createSchedule.get("storeGroup")?.value[0].itemName.trim(),
        locationId,
        dateRangeCompare
      );
      if (isExist) {
        swal({
          title: this.SchedulerConstantService.JOB_STARTED,
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      } else {
        if (!allDealerIdSame) {
          this.toastrService.error (
            "Please select stores with same DealerID");
        }
        if (this.createSchedule.valid && allDealerIdSame) {
          this.isProcessing = true;
          let activityData = {
            activityName: "Manage Tekion",
            activityType: "Create New Schedule",
            activityDescription: `Create New Schedule for Group ${this.createSchedule
              .get("storeGroup")
              ?.value[0].itemName.trim()} (Group: ${mageGrpCode}, Store: ${mageStrCode})`,
          };
          this.commonService.saveActivity("Manage Tekion", activityData);
          let roOptionValue = this.createSchedule.get("roOption")?.value;
          let jobType = this.createSchedule.get("jobType")?.value;
          let solve360Update;
          let buildProxies, switchBranch, customBranchName;
          solve360Update = this.createSchedule.get(
            "updateRetreiveROinSolve360"
          )?.value;
          buildProxies = this.createSchedule.get("buildProxies")?.value;
          switchBranch = this.createSchedule.get("switchBranch")?.value;
          customBranchName = this.createSchedule.get("branchName")?.value;

          if (switchBranch) {
            if (
              customBranchName === undefined ||
              !customBranchName ||
              customBranchName == null
            ) {
              this.EventEmitterService.displayProgress.emit("");
              NProgress.done();
              const messageValidate =
                this.SchedulerConstantService
                  .VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_TEKION;
              this.showStatusMessage(messageValidate, "failure");
              return false;
            }
          }

          if (!buildProxies) {
            solve360Update = false;
          }

          let inputFileArray, inputFile, fileDate;
          inputFileArray = this.inputFile;
          console.log("this.inputFile---------->save",this.inputFile);
          if (inputFileArray) {
            if (inputFileArray[0]) {
              if (inputFileArray[0].hasOwnProperty("filename")) {
                let filterFileName;
                filterFileName = inputFileArray[0].filename;
                fileDate = inputFileArray[0].filename.split("-")[1];
                if (this.archiveDecider) {
                  inputFile =
                    environment.tekionInputFileFolderPath +
                    "archive/" +
                    filterFileName;
                } else {
                  inputFile =
                    environment.tekionInputFileFolderPath + filterFileName;
                }
              }
            }
          }
          console.log(inputFile);

          roOptionValue = roOptionValue
            ? roOptionValue
            : this.SchedulerConstantService.DEFAULT_RO_OPTION;
          jobType = jobType
            ? jobType
            : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
          this.EventEmitterService.displayProgress.emit(
            this.SchedulerConstantService.EVENT_EMITTER.STEP_1
          );
          let scheduleDate = "";
          scheduleDate = moment().format(
            this.SchedulerConstantService.DATE_FORMAT
          );
          let startDate: string = moment(
            new Date(this.dateInput.start)
          ).format(this.SchedulerConstantService.DATE_FORMAT);
          let endDate: string = moment(new Date(this.dateInput.end)).format(
            this.SchedulerConstantService.DATE_FORMAT
          );
          let date = new Date();
          let dateArray: any = scheduleDate.split("-");
          date.setMonth(dateArray[0] * 1 - 1);
          date.setDate(dateArray[1] * 1);
          date.setFullYear(dateArray[2] * 1);
          let now_utc = Date.UTC(
            date.getUTCFullYear(),
            date.getUTCMonth(),
            date.getUTCDate(),
            date.getUTCHours(),
            date.getUTCMinutes(),
            date.getUTCSeconds()
          );
          const now_utcOP = new Date(now_utc);
          let now_utcOutPut = now_utcOP.toUTCString();
          const scheduleObj: any = {
            jobSchedule: moment(now_utcOutPut).toISOString(),
            jobData: {
              groupName: this.createSchedule.get("storeGroup")?.value.length
                ? this.createSchedule
                    .get("storeGroup")
                    ?.value[0].itemName.trim()
                : "xyz",
              storeDataArray: {
                locationId: locationId,
                sourceId: sourceId,
                activityStoreId: activityStoreId,
                projectId: projectId === undefined ? "" : projectId,
                secondProjectId:
                  secondProjectId === undefined ? "" : secondProjectId,
                mageManufacturer: mageManufacturer,
                solve360Update: solve360Update,
                buildProxies: buildProxies,
                userName: userName,
                inputFilePath: inputFile,
                fileDate: fileDate,
                invoiceMasterCSVFilePath: null,
                switchBranch: switchBranch,
                customBranchName: customBranchName,
                etlDMSType: etlDMSType,
                startDate: startDate,
                endDate: endDate,
                closedROOption: roOptionValue,
                jobType: jobType,
                mageGroupCode: mageGrpCode?mageGrpCode.replace(/-/g, '_'):'',
                mageStoreCode: mageStrCode?mageStrCode.replace(/-/g, '_'):'',
                stateCode: steCode,
                projectIds: projectIds,
                secondProjectIdList: secondProjectIdList,
                testData:false,
                companyIds:companyIds,
                companyObj:JSON.stringify(companyObj)
              },
            },
          };
          console.log("scheduleObj:", scheduleObj);
          let self = this;
          this.apollo
            .use("manageTekionSchedule")
            .mutate({
              mutation: createNewTekion,
              variables: scheduleObj,
            })
            .pipe(takeUntil(this.subscription$))
            .subscribe({
              next: (listdata) => {
                NProgress.done();
                const result: any = listdata.data;
                const status = result.scheduleTekionExtractJob.status;
                let message = "";
                message = result.scheduleTekionExtractJob.message;
                if (status) {
                  this.solve360ServerDecider = false;
                  this.switchServer();
                  this.EventEmitterService.displayProgress.emit(
                    this.SchedulerConstantService.EVENT_EMITTER
                      .STEP_SAVE_SCHEDULE
                  );
                  this.refreshScheduleList();
                  setTimeout(() => {
                    self.EventEmitterService.displayProgress.emit("");
                    this.isProcessing = false;
                    self.showStatusMessage(message, "success");
                  }, 3000);
                  this.createSchedule.reset();
                  this.createSchedule.patchValue({
                    updateRetreiveROinSolve360: true,
                    buildProxies: true,
                    switchBranch: false,
                  });
                  this.switchBranch = false;
                  this.createSchedule.patchValue({
                    roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
                    jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE,
                  });
                  this.storeFilterList = [];
                  // this.selectMultiDateRangeOption = !this.selectMultiDateRangeOption;
                  this.selectMultiDateRangeOption = true;
                  this.setDateRange(true, "");
                  this.dateInput = {
                    start: this.startDateSelection(),
                    end: this.endDateSelection(),
                  };
                  this.createSchedule.patchValue({
                    roOption: this.SchedulerConstantService.DEFAULT_RO_OPTION,
                    jobType: this.SchedulerConstantService.DEFAULT_JOB_TYPE,
                  });
                } else {
                  self.EventEmitterService.displayProgress.emit("");
                }
              },
              error: (err) => {
                this.EventEmitterService.displayProgress.emit("");
                NProgress.done();
                const message =
                  this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, "failure");
                this.commonService.errorCallback(err, this);
              },
              complete: () => {
                console.log("Completed");
              },
            });
        } else {
          this.isProcessing = false;
          this.validateAllFormFields(this.createSchedule);
        }
      }
      return;
    });
  }

  areAllDealerIdSame(dealerIdList: any) {
    const referenceValue = dealerIdList[0];
    const result = dealerIdList.every(
      (element: any) => element === referenceValue
    );
    return result;
  }

  getSecondProjectId(companyId: any, cb: any) {
    this.commonService.getSecondProjectId(companyId, (result: any) => {
      if (cb) {
        cb(result);
      }
    });
  }

  validateAllFormFields(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach((field) => {
      const control = formGroup.get(field);
      if (control instanceof FormControl) {
        control.markAsTouched({ onlySelf: true });
      } else if (control instanceof FormGroup) {
        this.validateAllFormFields(control);
      }
    });
  }

  isFieldValidCreateSchedule(field: string) {
    let retValue: any = null;
    retValue =
      !this.createSchedule.get(field)?.valid &&
      this.createSchedule.get(field)?.touched;
    return retValue;
  }

  displayFieldCssCreateSchedule(field: string) {
    return {
      "has-danger": this.isFieldValidCreateSchedule(field),
    };
  }

  /**
   * flash message style set for success and error
   *
   */
  showStatusMessage(message: any, statusType: any) {
    if (statusType === "success") {
      this.toastrService.success (message);
    } else {
      this.toastrService.error(message);
    }
  }

  timeFormat(timeString: any) {
    var hourEnd = timeString.indexOf(":");
    var H = +timeString.substr(0, hourEnd);
    var h = H % 12 || 12;
    var ampm = H < 12 ? " AM" : " PM";
    timeString = h + timeString.substr(hourEnd, 3) + ampm;
    return timeString;
  }

  getAllJobs(callback: any) {
    this.compareObjArray = [];
    this.processQueueList = [];
    this.processQueueListCompleted = [];
    const allStoreGroupsList = this.apollo
      .use("manageTekionSchedule")
      .query({
        query: getAllTekionExtractJobs,
        fetchPolicy: "network-only",
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata) => {
          const result: any = listdata;
          let obj: any = {};
          this.processQueueList = [];
          this.processQueueListCompleted = [];
          this.compareObjArray = [];
          this.poolTime =
            result["data"]["getAllTekionExtractJobs"]["poolTime"];
          $.each(
            result["data"]["getAllTekionExtractJobs"]["jobArray"],
            (key: any, val: any) => {
              console.log("data.............111.......................");
              console.log(val);
              let groupName = val.data.groupName;
              let scheduledDate = val.nextRunAt
                ? val.nextRunAt
                : val.lastRunAt
                ? val.lastRunAt
                : null;
              let date = "";
              scheduledDate
                ? (date = moment
                    .unix(scheduledDate / 1000)
                    .format("MM-DD-YYYY HH:mm:ss"))
                : null;
              let nextRunAt = "";
              val.nextRunAt
                ? (nextRunAt = moment.unix(val.nextRunAt / 1000).fromNow())
                : null;
              let groupStatus: any = null;
              groupStatus = val;
              let failedReason = val.failReason;
              $.each(val.data["storeDataArray"], (key: any, val: any) => {
                obj = {};
                let jobStartDate = null;
                let jobEndDate = null;
                let status = null;
                let jobType = null;
                let seperator = "/";
                let solve360Update;
                let buildProxies;
                let projectId;
                let secondProjectId;
                let inputFilePath;
                let invoiceMasterCSVFilePath;
                let switchBranch;
                let customBranchName;
                let projectIds;
                let secondProjectIdList;

                let mageManufacturer;
                let fileDate;

                if (val.startDate && val.startDate.includes("-")) {
                  seperator = "-";
                }
                status = val.status;
                const dateArray = val.startDate.split(seperator);
                const startDate =
                  dateArray[0] + "-" + dateArray[1] + "-" + dateArray[2];
                if (val.endDate.includes("-")) {
                  seperator = "-";
                }
                const dateArrayEnd = val.endDate.split(seperator);
                const ClosedROOption = val.closedROOption;
                const endtDate =
                  dateArrayEnd[0] +
                  "-" +
                  dateArrayEnd[1] +
                  "-" +
                  dateArrayEnd[2];
                let range = startDate + " - " + endtDate;
                jobStartDate = val.startTime;
                jobEndDate = val.endTime;
                jobType = val.jobType;
                solve360Update = val.solve360Update;
                buildProxies = val.buildProxies;
                switchBranch = val.switchBranch;
                customBranchName = val.customBranchName;
                projectId = val.projectId;
                secondProjectId = val.secondProjectId;
                inputFilePath = val.inputFilePath;
                invoiceMasterCSVFilePath = val.invoiceMasterCSVFilePath;
                projectIds = val.projectIds;
                secondProjectIdList = val.secondProjectIdList;

                mageManufacturer = val.mageManufacturer;

                fileDate = val.fileDate;
                let jobStatus = false;
                let statusFlag = null;
                if (jobStartDate && jobEndDate && status) {
                  jobStatus = true;
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.COMPLETED;
                } else if (jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.RUNNING;
                } else if (!jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.SCHEDULED;
                } else if (jobStartDate && jobEndDate && !status) {
                  jobStatus = true;
                  statusFlag = this.SchedulerConstantService.STATUS_FLAG.FAILED;
                  failedReason = val.message;
                } else {
                  statusFlag = this.getJobStatus(groupStatus);
                }
                obj = {
                  groupName: groupName,
                  store: val.locationId,
                  range: range,
                  date: date,
                  status: statusFlag,
                  override: this.SchedulerConstantService.RUN_NOW,
                  failedReason: failedReason,
                  nextRunAt: nextRunAt,
                  jobStartDate: jobStartDate,
                  jobEndDate: jobEndDate,
                  ClosedROOption: ClosedROOption,
                  jobType: jobType,
                  sourceId: val.sourceId,
                  activityStoreId: val.activityStoreId,
                  storeName: val.mageStoreCode,
                  solve360Update: solve360Update,
                  buildProxies: buildProxies,
                  projectId: projectId,
                  secondProjectId: secondProjectId,
                  inputFilePath: inputFilePath,
                  invoiceMasterCSVFilePath: invoiceMasterCSVFilePath,
                  switchBranch: switchBranch,
                  customBranchName: customBranchName,
                  mageManufacturer: mageManufacturer,
                  fileDate: fileDate,
                  projectIds: projectIds,
                  secondProjectIdList: secondProjectIdList,
                };
                if (
                  jobStatus &&
                  (statusFlag ===
                    this.SchedulerConstantService.STATUS_FLAG.COMPLETED ||
                    statusFlag ==
                      this.SchedulerConstantService.STATUS_FLAG.FAILED)
                ) {
                  this.processQueueListCompleted.push(obj);
                  this.compareObjArray.push(obj);
                } else {
                  this.processQueueList.push(obj);
                }
              });
            }
          );
          callback();
        },
        error: (err) => {
          this.commonService.errorCallback(err, this);
        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  getDealerIdFromStoreName(sName: any) {
    let storeFilterListCopy: any = [];
    storeFilterListCopy = Object.assign([], this.storeGroupList);
    storeFilterListCopy = storeFilterListCopy
      .filter((item: any, index: number) => index < storeFilterListCopy.length)
      .filter((item: any, index: number) => item.companyName === sName);
    let dealerId: any = "";
    if (storeFilterListCopy.length) {
      dealerId = storeFilterListCopy[0].thirdPartyUsername;
    }
    return dealerId;
  }

  getStoreNameFromDealerId(dealerID: any) {
    let storeFilterListCopy: any[] = [];
    storeFilterListCopy = Object.assign([], this.jobGroupList);
    storeFilterListCopy = storeFilterListCopy
      .filter((item: any, index: number) => index < storeFilterListCopy.length)
      .filter(
        (item: any, index: number) => item.thirdPartyUsername === dealerID
      );
    let dealerId = "";
    if (storeFilterListCopy.length) {
      dealerId = storeFilterListCopy[0].companyName;
    }
    return dealerId;
  }
  /**
   * showSouceBundleDetails function will show the Scheduled Process
   *
   */
  showScheduledProcessList(loaderStatus: any) {
    loaderStatus ? (this.scheduleProcessQueueLoading = true) : "";
    if ($("#scheduleProcessQueueTekion").data('datatable')) {
      $("#scheduleProcessQueueTekion").dataTable().fnDestroy();
    }
    table1 = $("#scheduleProcessQueueTekion").dataTable().fnClearTable();
    const elm = this;
    let i = 0;
    setTimeout(() => {
      $(document).ready(() => {
        table1 = $("#scheduleProcessQueueTekion").dataTable({
          language: {
            decimal: ".",
            thousands: ",",
          },
          columnDefs: [
            { type: "numeric-comma", targets: "_all" },
            { orderable: false, targets: [5] },
            { orderable: true, targets: [0, 1, 2, 3] },
          ],
          fixedHeader: {
            header: true,
            footer: true,
            headerOffset: $(".cat__top-bar").outerHeight() - 11,
          },
          bSort: false,
          order: [0, "asc"],
          responsive: true,
          scrollX: false,
          scrollY: "200px",
          destroy: true,
          paging: true,
          deferRender: true,
          ordering: true,
          info: true,
          filter: true,
          length: true,
          processing: true,
          lengthMenu: [
            [50, 25, 10, 5],
            [50, 25, 10, 5],
          ],
          autoWidth: false,
          fnRowCallback: function (settings: any, aData: any) {
            const pagination = $(this)
              .closest(".dataTables_wrapper")
              .find(".dataTables_paginate");
            pagination.toggle(this.api().page.info().pages > 1);
          },
          drawCallback: function (settings: any) {
            table1 = $("#scheduleProcessQueueTekion").DataTable();
            $("td:eq(1)", settings).css("width", "24%");
            $("td:eq(1)", settings).css("width", "10%");
            $("td:eq(2)", settings).css("width", "13%");
            $("td:eq(3)", settings).css("width", "23%");
            $("td:eq(4)", settings).css("width", "15%");
            $("td:eq(5)", settings).css("width", "15%");
            var api = this.api();
            var rows = api.rows({ page: "current" }).nodes();
            var last: any = null;
            api
              .column(0, { page: "current" })
              .data()
              .each(function (group: any, i: any) {
                if (last !== group) {
                  $(rows)
                    .eq(i)
                    .before(
                      '<tr class="group"><td colspan="8" style="BACKGROUND-COLOR:rgb(86, 85, 78);font-weight:700;color:#f5f5f5;">' +
                        "Store Group: " +
                        group +
                        "</td></tr>"
                    );
                  last = group;
                }
              });
          },
          columns: [
            {
              title: "Store",
              width: "24%",
              className: "dt-head-left",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                console.log("Tekion process JSon jobs####################",rowData);
                let dealerId = elm.getDealerIdFromStoreName(rowData[1]);
                let storeName = elm.getStoreNameFromDealerId(rowData[1]);
                storeName = storeName ? storeName : rowData[1];
                let toolTipForDealerId = "";
                let toolTipForJobType = "";
                if (dealerId) {
                  toolTipForDealerId = "DealerId: " + dealerId;
                }
                if (rowData[11]) {
                  toolTipForJobType = "Job Type: " + rowData[11];
                }
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  toolTipForDealerId +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  storeName +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Extraction Type",
              width: "26%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let toolTipForJobType = "";
                if (rowData[11]) {
                  toolTipForJobType = rowData[11];
                }
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[11] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  toolTipForJobType +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: " ",
              width: "7%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  " " +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Schedule Date",
              width: "29%",
              type: "formatted-num",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let toolTipTitle = "";
                if (rowData[4] == "Locked") {
                  toolTipTitle = elm.SchedulerConstantService.LOCKED_MESSAGE;
                }
                if (rowData[4] == "Running") {
                  toolTipTitle = rowData[4];
                }
                let b = rowData[3].split(" ");
                let r = b[0].split("-");
                let op = r[2] + "-" + r[0] + "-" + r[1] + " " + b[1];
                let scheduleDateDisplay = moment(op).format(
                  elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                );
                let scheduleDateOp = scheduleDateDisplay
                  ? scheduleDateDisplay
                  : rowData[3];
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  toolTipTitle +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  scheduleDateOp +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Status",
              width: "15%",
              type: "formatted-alphabet",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                var className = "";
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.COMPLETED
                  ? (className = "label-success")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.SCHEDULED
                  ? (className = "label-scheduled")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.RUNNING
                  ? (className = "label-running")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.REPEATING
                  ? (className = "label-repeating")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.FAILED
                  ? (className = "label-failed")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.QUEUED
                  ? (className = "label-queued")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.LOCKED
                  ? (className = "label-locked")
                  : null;
                if (rowData[4] === "Rescheduled") {
                  data =
                    '<span style="float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="label label-scheduled">Scheduled</span>';
                  data +=
                    '<span aria-hidden="true" data-toggle="tooltip" data-placement="top" title="' +
                    rowData[6] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '" style="margin-left:5px;" class="label label-failed">Failed</span>';
                } else {
                  data =
                    '<span style="float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="label ' +
                    className +
                    '">' +
                    rowData[4] +
                    "</span>";
                }
                return data;
              },
            },
            {
              title: "Actions",
              width: "15%",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                const d = data;
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                const cancelSchedule = "Cancel Schedule";
                // const overrideSchedule = 'Run Now';
                if (
                  rowData[4] === "Completed" ||
                  rowData[4] === "Locked" ||
                  //rowData[4] === "Running" ||
                  rowData[4] === "Queued"
                ) {
                  data =
                    '<a style="font-size: 18px;" class="overrideScheduleCancel" style="color: #b9b5b5;cursor: not-allowed;" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-play-circle overrideScheduleCancel"  data-toggle="tooltip" data-placement="top" title="' +
                    rowData[4] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  style="color: #b9b5b5;cursor: not-allowed;"></i></a>';
                  data +=
                    '<a style="margin: 18px;font-size: 18px;" class="scheduleCancel" style="color: #b9b5b5;cursor: not-allowed;" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-ban scheduleCancel"  data-toggle="tooltip" data-placement="top" title="' +
                    rowData[4] +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '" style="color: #b9b5b5;cursor: not-allowed;" ></i></a>';
                }else if(rowData[4] === "Running"){
                   data = `
                    <a style="margin: 18px; font-size: 18px;" class="cancelSchedule" href="javascript:void(0);" data-note="${d}">
                      </a>
                     `; 
                } 
                else {
                  // data = '<a style="font-size: 18px;" class="overrideSchedule" href="javascript:void(0);" data-note="' + d + '">'
                  //   + '<i aria-hidden="true" class="fa fa-play-circle overrideSchedule"  data-toggle="tooltip" data-placement="top" title="'
                  //   + overrideSchedule + '" data-animation="false" data-info="' + rowData + '"  ></i></a>';
                  data =
                    '<a style="margin: 18px;font-size: 18px;" class="cancelSchedule" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-ban cancelSchedule"  data-toggle="tooltip" data-placement="top" title="' +
                    cancelSchedule +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  ></i></a>';
                }
                return data;
              },
            },
          ],
          rowGroup: {
            dataSrc: "Store Group",
          },
        });
        // tslint:disable-next-line:no-unused-expression
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
          "alphanumeric-sort-asc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "alphanumeric-sort-desc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? 1 : x > y ? -1 : 0;
          },
          "formatted-num-pre": function (a: any) {
            a = a === "-" || a === "" ? 0 : a.replace(/[^\d\-\.]/g, "");
            return parseFloat(a);
          },
          "formatted-num-asc": function (a: any, b: any) {
            return a - b;
          },
          "formatted-num-desc": function (a: any, b: any) {
            return b - a;
          },
          "formatted-alphabet-asc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "formatted-alphabet-desc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? 1 : x > y ? -1 : 0;
          },
        });
        elm.reDrawScheduleTable(elm.processQueueList, loaderStatus);
      });
    }, 200);
  }

  /**
   * reDrawPortalStoreTable function will redraw the datatable using new table values
   *
   */
  reDrawScheduleTable(temp: any, loaderStatus: any) {
    table1 = $("#scheduleProcessQueueTekion").DataTable();
    table1.search("").draw();
    table1 = $("#scheduleProcessQueueTekion").dataTable();
    table1.fnClearTable();
    table1 = $("#scheduleProcessQueueTekion").dataTable();
    const tempArr: any[] = [];
    for (let i = 0; i < temp.length; i++) {
      let rpt: any[] = [];
      const t = temp[i];
      rpt = [
        t.groupName,
        t.store,
        t.range,
        t.date,
        t.status,
        t.override,
        t.failedReason,
        t.nextRunAt,
        t.ClosedROOption,
        t.jobStartDate,
        t.jobEndDate,
        t.jobType,
        t.sourceId,
        t.activityStoreId,
        t.projectId,
        t.solve360Update,
        t.buildProxies,
        t.secondProjectId,
        t.inputFilePath,
        t.invoiceMasterCSVFilePath,
        t.switchBranch,
        t.customBranchName,
        t.mageManufacturer,
        t.fileDate,
        ,
        t.projectIds,
        t.secondProjectIdList,
      ];
      tempArr.push(rpt);
    }
    if (tempArr.length > 0) {
      table1.fnAddData(tempArr, false); // Add new data
    }
    table1.fnDraw(); // Redraw the DataTable
    loaderStatus ? (this.scheduleProcessQueueLoading = false) : "";
    if (temp.length > 0) {
      setTimeout(() => {
        $("#scheduleProcessQueueTekion")
          .DataTable()
          .columns.adjust()
          .draw(false);
      }, 100);
    }
  }

  getToolTipInfoCompletedJobs(rowData: any) {
    let toolTip = "";
    let className = "";
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.COMPLETED
      ? (className = "label-success")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.SCHEDULED
      ? (className = "label-scheduled")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.RUNNING
      ? (className = "label-running")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.REPEATING
      ? (className = "label-repeating")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.FAILED
      ? (className = "label-failed")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.QUEUED
      ? (className = "label-queued")
      : null;
    rowData[4] === this.SchedulerConstantService.STATUS_FLAG.LOCKED
      ? (className = "label-locked")
      : null;
    status = '<span class="label ' + className + '">' + rowData[4] + "</span>";
    if (rowData[3]) {
      let b = rowData[3].split(" ");
      let r = b[0].split("-");
      let op = r[2] + "-" + r[0] + "-" + r[1] + " " + b[1];
      const time = moment.duration(
        `00:${this.poolTime ? this.poolTime : "00"}:00`
      );
      let scheduleDateDisplay = moment(op)
        .subtract(time)
        .format(this.SchedulerConstantService.SCHEDULE_DATE_FORMAT);
      let scheduleDateOp = scheduleDateDisplay
        ? scheduleDateDisplay
        : rowData[3];
      toolTip += "Scheduled Date: " + scheduleDateOp + " \n";
    }
    toolTip += "Last Run At: " + rowData[3] + " \n";
    if (rowData[6]) {
      toolTip +=
        "Extraction Start Time: " +
        moment(rowData[6] * 1).format(
          this.SchedulerConstantService.SCHEDULE_DATE_FORMAT
        ) +
        " \n";
    }
    if (rowData[7]) {
      toolTip +=
        "Extraction End Time: " +
        moment(rowData[7] * 1).format(
          this.SchedulerConstantService.SCHEDULE_DATE_FORMAT
        ) +
        " \n";
    }
    toolTip += "Status: " + status + " \n";
    if (rowData[5] && rowData[4] === "Failed") {
      toolTip += "Failed Reason: " + rowData[5] + " \n";
    }
    return toolTip;
  }

  /**
   * showScheduledProcessListCompleted function will show the Scheduled Process
   *
   */
  showScheduledProcessListCompleted(loaderStatus: any) {
    loaderStatus ? (this.scheduleProcessQueueCompletedLoading = true) : "";
    if ($("#scheduleProcessCompleted").data('datatable')) {
      $("#scheduleProcessCompleted").dataTable().fnDestroy();
    }
    table1 = $("#scheduleProcessCompleted").dataTable().fnClearTable();
    const elm = this;
    let i = 0;
    setTimeout(() => {
      $(document).ready(() => {
        table1 = $("#scheduleProcessCompleted").dataTable({
          language: {
            decimal: ".",
            thousands: ",",
          },
          columnDefs: [{ type: "numeric-comma", targets: "_all" }],
          fixedHeader: {
            header: true,
            footer: true,
            headerOffset: $(".cat__top-bar").outerHeight() - 11,
          },
          bSort: false,
          order: [4, "desc"],
          responsive: true,
          scrollX: false,
          destroy: true,
          paging: true,
          deferRender: true,
          ordering: true,
          info: true,
          filter: true,
          length: true,
          processing: true,
          lengthMenu: [
            [50, 25, 10, 5],
            [50, 25, 10, 5],
          ],
          autoWidth: false,
          scrollY: "200px",
          // initialization params as usual
          fnRowCallback: function (settings: any, aData: any) {
            const pagination = $(this)
              .closest(".dataTables_wrapper")
              .find(".dataTables_paginate");
            pagination.toggle(this.api().page.info().pages > 1);
          },
          drawCallback: function (settings: any) {
            table1 = $("#scheduleProcessQueueTekion").DataTable();
            $("td:eq(1)", settings).css("width", "28%");
            $("td:eq(2)", settings).css("width", "17%");
            $("td:eq(3)", settings).css("width", "25%");
            $("td:eq(4)", settings).css("width", "20%");
            $("td:eq(5)", settings).css("width", "10%");
            var api1 = this.api();
            var rows1 = api1.rows({ page: "current" }).nodes();
            var last: any = null;

            api1
              .column(0, { page: "current" })
              .data()
              .each(function (group: any, i: any) {
                if (last !== group) {
                  $(rows1)
                    .eq(i)
                    .before(
                      '<tr class="group"><td colspan="8" style="BACKGROUND-COLOR:rgb(86, 85, 78);font-weight:700;color:#f5f5f5;">' +
                        "Store Group: " +
                        group +
                        "</td></tr>"
                    );
                  last = group;
                }
              });
          },
          columns: [
            {
              title: "Store",
              width: "28%",
              className: "dt-head-left",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let storeName = elm.getStoreNameFromDealerId(rowData[1]);
                storeName = storeName ? storeName : rowData[1];
                storeName = rowData[9] ? rowData[9] : rowData[1];
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[1] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  storeName +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Extraction Type",
              width: "17%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let toolTipForJobType = "";
                if (rowData[8]) {
                  toolTipForJobType = rowData[8];
                }
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[8] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  toolTipForJobType +
                  "</span>";
                return toolTipForJobType !== "" ? data : null;
              },
            },
            {
              title: "Data Extraction Range",
              width: "25%",
              type: "alphanumeric-sort",
              className: "dt-head-center",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  rowData[2] +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  rowData[2] +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Completed Date",
              width: "20%",
              type: "formatted-date",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let completedDate = "";
                let tempDate = new Date(rowData[3]);
                rowData[6] = tempDate.getTime()

                let tempDate1 = new Date(rowData[7]);
                rowData[7] = tempDate1.getTime();
                if (rowData[3]) {
                  let time = rowData[3].split(" ");
                  let HourFormat = elm.convertTimeFormat(time[1]);
                  completedDate = time[0] + " " + HourFormat;
                }
                if (rowData[9]) {
                  completedDate = moment(rowData[7] * 1).format(
                    elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                  );
                }
                data =
                  '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="' +
                  completedDate +
                  '" data-animation="false" data-info="' +
                  rowData +
                  '">' +
                  completedDate +
                  "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Status",
              width: "10%",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                var className = "";
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.COMPLETED
                  ? (className = "label-success")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.SCHEDULED
                  ? (className = "label-scheduled")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.RUNNING
                  ? (className = "label-running")
                  : null;
                rowData[4] ===
                elm.SchedulerConstantService.STATUS_FLAG.REPEATING
                  ? (className = "label-repeating")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.FAILED
                  ? (className = "label-failed")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.QUEUED
                  ? (className = "label-queued")
                  : null;
                rowData[4] === elm.SchedulerConstantService.STATUS_FLAG.LOCKED
                  ? (className = "label-locked")
                  : null;
                let toolTip = "";
                if (rowData[3]) {
                  let b = rowData[3].split(" ");
                  let r = b[0].split("-");
                  let op = r[2] + "-" + r[0] + "-" + r[1] + " " + b[1];
                  const time = moment.duration(
                    `00:${elm.poolTime ? elm.poolTime : "00"}:00`
                  );
                  let scheduleDateDisplay = moment(op)
                    .subtract(time)
                    .format(elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT);
                  let scheduleDateOp = scheduleDateDisplay
                    ? scheduleDateDisplay
                    : rowData[3];
                  toolTip += "Scheduled Date: " + scheduleDateOp + " \n";
                }
                toolTip += "Last Run At: " + rowData[3] + " \n";
                if (rowData[6]) {
                  toolTip +=
                    "Extraction Start Time: " +
                    moment(rowData[6] * 1).format(
                      elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                    ) +
                    " \n";
                }
                if (rowData[7]) {
                  toolTip +=
                    "Extraction End Time: " +
                    moment(rowData[7] * 1).format(
                      elm.SchedulerConstantService.SCHEDULE_DATE_FORMAT
                    ) +
                    " \n";
                }
                toolTip += "Status: " + rowData[4] + " \n";
                if (rowData[5] && rowData[4] === "Failed") {
                  toolTip +=
                    "Failed Reason: " + rowData[5]
                      ? rowData[5].replace(/[^a-zA-Z ]/g, " ")
                      : "" + " \n";
                }
                rowData[5] = rowData[5]
                  ? rowData[5].replace(/[^a-zA-Z ]/g, " ")
                  : "";
                if (
                  rowData[4] === "Failed" &&
                  rowData[5] === elm.SchedulerConstantService.EXCEED_TIME
                ) {
                  rowData[4] = "Completed";
                  className = "label-success";
                }
                data =
                  '<span style="cursor:pointer;float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="popUpInfoCompletedJobs label ' +
                  className +
                  '"  data-toggle="tooltip" data-placement="top" title="' +
                  toolTip +
                  '" data-info="' +
                  rowData +
                  '">' +
                  rowData[4] +
                  "</span>";
                if (rowData[5]) {
                  data =
                    '<span style="cursor:pointer;float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="popUpInfoCompletedJobs label ' +
                    className +
                    '" data-toggle="tooltip" data-placement="top" title="' +
                    toolTip +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '">' +
                    rowData[4] +
                    "</span>";
                }

                return data;
              },
            },
          ],
        });
        // tslint:disable-next-line:no-unused-expression
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
          "alphanumeric-sort-asc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "alphanumeric-sort-desc": function (a: any, b: any) {
            const x =
              a && a !== null && a.trim() !== ""
                ? a.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            const y =
              b && b !== null && b.trim() !== ""
                ? b.toLowerCase().replace(/^[^a-z0-9]*/g, "")
                : "";
            return x < y ? 1 : x > y ? -1 : 0;
          },
          "formatted-date-asc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a ? new Date(a).getTime() : 0;
            const y = b ? new Date(b).getTime() : 0;
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "formatted-date-desc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a ? new Date(a.toString()).getTime() : 0;
            const y = b ? new Date(b.toString()).getTime() : 0;
            return x < y ? 1 : x > y ? -1 : 0;
          },
        });
        elm.reDrawScheduleTableCompleted(
          elm.processQueueListCompleted,
          loaderStatus
        );
      });
    }, 200);
  }

  /**
   * reDrawScheduleTableCompleted function will redraw the datatable using new table values
   *
   */
  reDrawScheduleTableCompleted(temp: any, loaderStatus: any) {
    table1 = $("#scheduleProcessCompleted").DataTable();
    table1.search("").draw();
    table1 = $("#scheduleProcessCompleted").dataTable();
    table1.fnClearTable();
    table1 = $("#scheduleProcessCompleted").dataTable();

    const tempArr: any[] = [];
    for (let i = 0; i < temp.length; i++) {
      const t = temp[i];
      const rpt = [
        t.groupName,
        t.store,
        t.range,
        t.date,
        t.status,
        t.failedReason,
        t.jobStartDate,
        t.jobEndDate,
        t.jobType,
        t.storeName,
      ];
      tempArr.push(rpt);
    }
    if (tempArr.length > 0) {
      table1.fnAddData(tempArr, false); // Add new data
    }
    table1.fnDraw(); // Redraw the DataTable
    loaderStatus ? (this.scheduleProcessQueueCompletedLoading = false) : "";
    if (temp.length > 0) {
      setTimeout(() => {
        if ($("#scheduleProcessCompleted").data('datatable')) {
        $("#scheduleProcessCompleted").DataTable().columns.adjust().draw(false);
        }
      }, 100);
    }
  }

  refreshScheduleList() {
    this.closeToolTip();
    this.loadingSchedule = true;
    this.getAllJobs(() => {
      this.showScheduledProcessList(true);
      this.loadingSchedule = false;
    });
  }

  refreshScheduleListCompleted() {
    this.closeToolTip();
    this.loadingScheduleCompleted = true;
    this.getAllJobs(() => {
      this.showScheduledProcessListCompleted(true);
      this.loadingScheduleCompleted = false;
    });
  }

  refreshProcessJsonList() {
    this.closeToolTip();
    this.loadingProcessJson = true;
    this.getAllProcessJsonJobs(() => {
      this.showProcessJsonList();
      this.loadingProcessJson = false;
    });
  }

  runNowSchedule(data: any) {
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      var userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
    }
    let rowData = data.split(",");
    const thridPartyUserName = rowData[1].toString();
    let filteredData: any = Object.assign([], this.jobGroupList);
    let resObj = filteredData.filter(
      (item: any) => item.thirdPartyUsername === thridPartyUserName
    );
    let mageGrpCode = "";
    let mageStrCode = "";
    let steCode = "";
    let sourceId = rowData[12];
    let activityStoreId = rowData[13];
    let projectId = "";
    let secondProjectId = "";
    let inputFile: any;
    let invoiceMasterCSVFile: any;
    let etlDMSType: any;
    let mageManufacturer: any;
    let fileDate: any;
    let projectIds: any;
    let secondProjectIdList: any;

    if (resObj.length) {
      mageGrpCode = resObj[0].mageGroupCode;
      mageStrCode = resObj[0].mageStoreCode;
      steCode = resObj[0].state;
      projectId = resObj[0].projectId;
      etlDMSType = resObj[0].etlDms;
    }

    if (rowData[14]) {
      projectId = rowData[14].toString();
    }
    if (rowData[17]) {
      secondProjectId = rowData[17].toString();
    }

    if (rowData[18]) {
      inputFile = rowData[18].toString();
    }

    if (rowData[19]) {
      invoiceMasterCSVFile = rowData[19].toString();
    }
    if (rowData[23]) {
      fileDate = rowData[23].toString();
    }

    if (rowData[24]) {
      projectIds = rowData[24].toString();
    }

    if (rowData[25]) {
      secondProjectIdList = rowData[25].toString();
    }

    // if(rowData[21]){
    //   projectIds = rowData[21].toString();
    // }

    // if(rowData[22]){
    //   secondProjectIdList = rowData[22].toString();
    // }

    console.log("projectId:", projectId);
    console.log("secondProjectId:", secondProjectId);
    console.log("inputFile:", inputFile);
    console.log("invoiceMasterCSVFile:", invoiceMasterCSVFile);

    let solve360Update = rowData[15].toLowerCase() == "true";
    let buildProxies = rowData[16].toLowerCase() == "true";

    let switchBranch = rowData[20].toLowerCase() == "true";
    let customBranchName: any;
    customBranchName = rowData[21].toString();

    if (rowData[22]) {
      mageManufacturer = rowData[22].toString();
    }
    console.log("mageManufacturer:", mageManufacturer);

    var date = moment(rowData[3]);
    var now = moment();

    if (now > date) {
      swal({
        title: this.SchedulerConstantService.JOB_STARTED,
        type: "warning",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: this.constantService.CLOSE,
      });
      this.refreshScheduleList();
    } else {
      swal(
        {
          title: this.constantService.AREYOUSURE,
          text: this.SchedulerConstantService.RUN_SCHEDULE_NOW,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default pointer",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: "Run",
          closeOnConfirm: true,
          showLoaderOnConfirm: true,
        },
        () => {
          this.EventEmitterService.displayProgress.emit(
            this.SchedulerConstantService.EVENT_EMITTER.STEP1
          );
          let rowData = data.split(",");
          const groupName = rowData[0];
          let activityData = {
            activityName: "Manage Tekion",
            activityType: "RunNow Tekion",
            activityDescription: `Run Schedule for Group ${groupName} (Group: ${mageGrpCode}, Store: ${mageStrCode})`,
          };
          this.commonService.saveActivity("Manage Tekion", activityData);
          const locationId = rowData[1].toString();
          // let jobSchedule: any = moment(
          //   rowData[3],
          //   this.SchedulerConstantService.DATE_FORMAT
          // );
          let jobSchedule: any = moment(
            rowData[3],
          ).
          format('MM-DD-YYYY HH:mm:ss');
          let scheduleOpSplit = jobSchedule.split(" ");
          let scheduleOp = scheduleOpSplit[0].split("-");
          jobSchedule =
            scheduleOp[2] +
            "-" +
            scheduleOp[0] +
            "-" +
            scheduleOp[1] +
            " " +
            scheduleOpSplit[1];
          const dateRange = rowData[2].split(" - ");
          const startDate = dateRange[0].trim();
          const endDate = dateRange[1].trim();
          let date = new Date(jobSchedule);
          const closedRoOption = rowData[8];
          let jobType = rowData[11]
            ? rowData[11]
            : this.SchedulerConstantService.DEFAULT_JOB_TYPE;
          let now_utc = Date.UTC(
            date.getUTCFullYear(),
            date.getUTCMonth(),
            date.getUTCDate(),
            date.getUTCHours(),
            date.getUTCMinutes(),
            date.getUTCSeconds()
          );
          const now_utcOP = new Date(now_utc);
          const scheduleObj: any = {
            jobSchedule: moment(now_utcOP).toISOString(),
            jobData: {
              groupName: groupName.trim(),
              storeData: {
                locationId: locationId,
                sourceId: sourceId,
                activityStoreId: activityStoreId,
                projectId: projectId,
                secondProjectId: secondProjectId,
                mageManufacturer: mageManufacturer,
                solve360Update: solve360Update,
                buildProxies: buildProxies,
                userName: userName,
                inputFilePath: inputFile,
                invoiceMasterCSVFilePath: invoiceMasterCSVFile,
                switchBranch: switchBranch,
                customBranchName: customBranchName,
                etlDMSType: etlDMSType,
                startDate: startDate,
                endDate: endDate,
                closedROOption: closedRoOption,
                jobType: jobType,
                mageGroupCode: mageGrpCode,
                mageStoreCode: mageStrCode,
                stateCode: steCode,
                fileDate: fileDate,
                projectIds: projectIds,
                secondProjectIdList: secondProjectIdList,
              },
            },
          };

          let self = this;
          this.apollo
            .use("manageTekionSchedule")
            .mutate({
              mutation: runNowTekionExtractJobByStore,
              variables: scheduleObj,
            })
            .pipe(takeUntil(this.subscription$))
            .subscribe({
              next: (listdata) => {
                NProgress.done();
                const result: any = listdata.data;
                const status = result.runNowTekionExtractJobByStore.status;
                const message = result.runNowTekionExtractJobByStore.message;
                if (status) {
                  this.EventEmitterService.displayProgress.emit(
                    this.SchedulerConstantService.EVENT_EMITTER.STEP_RELOAD_LIST
                  );
                  this.refreshScheduleList();
                  setTimeout(() => {
                    self.refreshScheduleList();
                    self.EventEmitterService.displayProgress.emit("");
                    self.showStatusMessage(message, "success");
                  }, 3000);
                } else {
                  self.EventEmitterService.displayProgress.emit("");
                }
              },
              error: (err) => {
                this.EventEmitterService.displayProgress.emit("");
                NProgress.done();
                const message =
                  this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, "failure");
                this.commonService.errorCallback(err, this);
              },
              complete: () => {
                console.log("Completed");
              },
            });
        }
      );
    }
  }

  cancelSchedule(data: any) {
    const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
    if (currentUserObj) {
      var userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
    }

    let rowData = data.split(",");
    const thridPartyUserName = rowData[1].toString();
    let filteredData: any = Object.assign([], this.jobGroupList);
    let resObj = filteredData.filter(
      (item: any) => item.thirdPartyUsername === thridPartyUserName
    );
    let mageGrpCode = "";
    let mageStrCode = "";
    let steCode = "";
    let sourceId = rowData[12];
    let activityStoreId = rowData[13];

    let projectId = "";
    let secondProjectId = "";
    let inputFile: any;
    let invoiceMasterCSVFile: any;
    let etlDMSType: any;

    let mageManufacturer: any;

    if (resObj.length) {
      mageGrpCode = resObj[0].mageGroupCode;
      mageStrCode = resObj[0].mageStoreCode;
      steCode = resObj[0].state;
      projectId = resObj[0].projectId;
      etlDMSType = resObj[0].etlDms;
    }
    if (rowData[14]) {
      projectId = rowData[14].toString();
    }
    if (rowData[17]) {
      secondProjectId = rowData[17].toString();
    }
    if (rowData[18]) {
      inputFile = rowData[18].toString();
    }
    if (rowData[19]) {
      invoiceMasterCSVFile = rowData[19].toString();
    }

    console.log("projectId:", projectId);
    console.log("secondProjectId:", secondProjectId);
    console.log("inputFile:", inputFile);
    console.log("invoiceMasterCSVFile:", invoiceMasterCSVFile);

    let solve360Update = rowData[15].toLowerCase() == "true";
    let buildProxies = rowData[16].toLowerCase() == "true";

    let switchBranch = rowData[20].toLowerCase() == "true";
    let customBranchName: any;
    customBranchName = rowData[21].toString();

    if (rowData[22]) {
      mageManufacturer = rowData[22].toString();
    }
    console.log("mageManufacturer:", mageManufacturer);

    var date = moment(rowData[3]);
    var now = moment();
    if (now > date) {
      swal({
        title: this.SchedulerConstantService.JOB_STARTED,
        type: "warning",
        confirmButtonClass: "btn-warning pointer",
        confirmButtonText: this.constantService.CLOSE,
      });
      this.refreshScheduleList();
    } else {
      swal(
        {
          title: this.constantService.AREYOUSURE,
          text: this.SchedulerConstantService.CANCEL_SCHEDULE_NOW,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default pointer",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: "Continue",
          closeOnConfirm: true,
          showLoaderOnConfirm: true,
        },
        () => {
          this.EventEmitterService.displayProgress.emit(
            this.SchedulerConstantService.EVENT_EMITTER.CANCEL_SCHEDULE
          );
          let rowData = data.split(",");
          let jobType = this.createSchedule.get("jobType")?.value;
          const groupName = rowData[0];
          let activityData = {
            activityName: "Manage Tekion",
            activityType: "Cancel Tekion",
            activityDescription: `Cancel Schedule for Group - ${groupName} (Group: ${mageGrpCode}, Store: ${mageStrCode})`,
          };
          this.commonService.saveActivity("Manage Tekion", activityData);
          const locationId = rowData[1].toString();
          // let jobSchedule: any = moment(
          //   rowData[3],
          //   this.SchedulerConstantService.DATE_FORMAT
          // );
          let jobSchedule: any = moment(
            rowData[3],
          ).
          format('MM-DD-YYYY HH:mm:ss');
          let scheduleOpSplit = jobSchedule.split(" ");
          let scheduleOp = scheduleOpSplit[0].split("-");
          jobSchedule =
            scheduleOp[2] +
            "-" +
            scheduleOp[0] +
            "-" +
            scheduleOp[1] +
            " " +
            scheduleOpSplit[1];

          const dateRange = rowData[2].split(" - ");
          const startDate = dateRange[0].trim();
          const endDate = dateRange[1].trim();
          const closedRoOption = rowData[8];
          let date = new Date(jobSchedule);
          let now_utc = Date.UTC(
            date.getUTCFullYear(),
            date.getUTCMonth(),
            date.getUTCDate(),
            date.getUTCHours(),
            date.getUTCMinutes(),
            date.getUTCSeconds()
          );
          const now_utcOP = new Date(now_utc);
          const scheduleObj: any = {
            jobSchedule: moment(now_utcOP).toISOString(),
            jobData: {
              groupName: groupName,
              storeData: {
                locationId: locationId,
                sourceId: sourceId,
                activityStoreId: activityStoreId,
                projectId: projectId,
                secondProjectId: secondProjectId,
                mageManufacturer: mageManufacturer,
                solve360Update: solve360Update,
                buildProxies: buildProxies,
                userName: userName,
                inputFilePath: inputFile,
                invoiceMasterCSVFilePath: invoiceMasterCSVFile,
                switchBranch: switchBranch,
                customBranchName: customBranchName,
                etlDMSType: etlDMSType,
                startDate: startDate,
                endDate: endDate,
                closedROOption: closedRoOption,
                jobType: jobType,
                mageGroupCode: mageGrpCode,
                mageStoreCode: mageStrCode,
                stateCode: steCode,
              },
            },
          };
          let self = this;
          this.apollo
            .use("manageTekionSchedule")
            .mutate({
              mutation: cancelTekionExtractJobByStore,
              variables: scheduleObj,
            })
            .pipe(takeUntil(this.subscription$))
            .subscribe({
              next: (listdata) => {
                NProgress.done();
                const result: any = listdata.data;
                const status = result.cancelTekionExtractJobByStore.status;
                const message = result.cancelTekionExtractJobByStore.message;
                if (status) {
                  this.EventEmitterService.displayProgress.emit(
                    this.SchedulerConstantService.EVENT_EMITTER.CANCEL_SCHEDULE
                  );
                  this.refreshScheduleList();
                  setTimeout(() => {
                    self.EventEmitterService.displayProgress.emit("");
                    self.showStatusMessage(message, "success");
                  }, 3000);
                } else {
                  self.EventEmitterService.displayProgress.emit("");
                }
              },
              error: (err) => {
                this.EventEmitterService.displayProgress.emit("");
                NProgress.done();
                const message =
                  this.SchedulerConstantService.ERROR_IN_SCHEDULE_JOB;
                this.showStatusMessage(message, "failure");
                this.commonService.errorCallback(err, this);
              },
              complete: () => {
                console.log("Completed");
              },
            });
        }
      );
    }
  }

  processQueueToggle() {
    this.processQueueListCollapsed = !this.processQueueListCollapsed;
    if (!this.processQueueListCollapsed) {
      this.setActiveFixedHeader();
    }
  }

  completedJobsToggle() {
    this.completedListCollapsed = !this.completedListCollapsed;
    if (!this.completedListCollapsed) {
      this.refreshScheduleListCompleted();
      this.completedProcessjsonListCollapsed = true;
      this.setActiveFixedHeader();
    }
  }

  processJsonToggle() {
    this.completedProcessjsonListCollapsed =
      !this.completedProcessjsonListCollapsed;
    if (!this.completedProcessjsonListCollapsed) {
      this.completedListCollapsed = true;
      this.refreshProcessJsonList();
      this.setActiveFixedHeader();
    }
  }

  getAllProcessJsonJobs(callback: any) {
    this.processJsonListArray = [];
    const allStoreGroupsList = this.apollo
      .use("manageTekionSchedule")
      .query({
        query: getAllTekionProcessJSONJobs,
        fetchPolicy: "network-only",
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata) => {
          const result: any = listdata;
          let obj: any = {};
          this.processJsonListArray = [];
          if (
            result["data"]["getAllTekionProcessJSONJobs"] &&
            result["data"]["getAllTekionProcessJSONJobs"][
              "processJSONJobsQueue"
            ]
          ) {
            $.each(
              result["data"]["getAllTekionProcessJSONJobs"][
                "processJSONJobsQueue"
              ],
              (key: any, val: any) => {
                let groupName = "test";
                let date = "";
                let nextRunAt = "";
                let statusFlag =
                  this.SchedulerConstantService.STATUS_FLAG.QUEUED;
                obj = {
                  groupName: groupName,
                  storeID: val.storeID,
                  statusFlag: statusFlag,
                  lastRunAt: null,
                  fileProcessed: null,
                  outputFile: null,
                  message: null,
                  failedReason: null,
                  nextRunAt: nextRunAt,
                  statusInfo: "",
                  uniqueId: "",
                };
                obj.statusInfo =
                  "File To Process: " + val.fileToProcess + " \n";
                this.processJsonListArray.push(obj);
              }
            );
          }
          if (
            result["data"]["getAllTekionProcessJSONJobs"] &&
            result["data"]["getAllTekionProcessJSONJobs"]["processJSONJobs"]
          ) {
            $.each(
              result["data"]["getAllTekionProcessJSONJobs"][
                "processJSONJobs"
              ],
              (keyval: any, val: any) => {
                let groupName = "test";
                let scheduledDate = val.nextRunAt
                  ? val.nextRunAt
                  : val.lastRunAt
                  ? val.lastRunAt
                  : null;
                let date = "";
                scheduledDate
                  ? (date = moment
                      .unix(scheduledDate / 1000)
                      .format("MM-DD-YYYY HH:mm:ss"))
                  : null;
                let nextRunAt = "";
                val.nextRunAt
                  ? (nextRunAt = moment.unix(val.nextRunAt / 1000).fromNow())
                  : null;
                let statusFlag = this.getJobStatus(val);
                let statusMessage = val.failReason;
                let haltIdentifier = false;
                if (
                  statusMessage == "Error: Halt" ||
                  statusMessage == "Error: Dead"
                ) {
                  haltIdentifier = true;
                }
                let failReason: string =  '';
                if (haltIdentifier) {
                  if (statusMessage == "Error: Halt") {
                    statusFlag = "HALT";
                    this.haltState = "Halted";
                    failReason = "Exceptions in core";
                  } else if (statusMessage == "Error: Dead") {
                    statusFlag = "DEAD";
                    this.haltState = "Held";
                    failReason = "Exceptions in core";
                  }
                }
                let failedReason = failReason ?  failReason : val.failReason;
                if (failedReason) {
                  failedReason = failedReason.replace(/,/g, "; ");
                }
                const uniqueId = val._id;
                val.scheduled && val.failed
                  ? (statusFlag =
                      this.SchedulerConstantService.STATUS_FLAG.RESCHEDULED)
                  : null;
                let storeName = "";
                if (val.data.storeID && val.data.inputFile) {
                  let sp1 = val.data.inputFile.split(val.data.storeID);
                  storeName = sp1 && sp1.length ? sp1[0].split("-")[1] : "";
                }
                let invalidmiscpaytypeCount;
                let estimateCount;
                let punchTimeMissingCount;
                let inputFilePath1;
                let suffixedInvoicesCount;
                let suffixedInvoicesCsvData;
                let splitJobExceptionCount;
                let lessSpecialDiscountCount;
                if (val.data) {
                  console.log(
                    "******************************SHARK TRACK*********************************************"
                  );
                  if (val.data.hasOwnProperty("invalidmiscpaytypeCount")) {
                    invalidmiscpaytypeCount = val.data.invalidmiscpaytypeCount;
                  }
                  if (val.data.hasOwnProperty("estimateCount")) {
                    estimateCount = val.data.estimateCount;
                  }

                  if (val.data.hasOwnProperty("punchTimeMissingCount")) {
                    punchTimeMissingCount = val.data.punchTimeMissingCount;
                  }
                  if (val.data.hasOwnProperty("punchTimeMissingCount")) {
                    punchTimeMissingCount = val.data.punchTimeMissingCount;
                  }
                  if (val.data.hasOwnProperty("inputFilePath1")) {
                    inputFilePath1 = val.data.inputFilePath1;
                  }
                  if (val.data.hasOwnProperty("suffixedInvoicesCount")) {
                    suffixedInvoicesCount = val.data.suffixedInvoicesCount;
                  }
                  if (val.data.hasOwnProperty("suffixedInvoicesCsvData")) {
                    suffixedInvoicesCsvData = val.data.suffixedInvoicesCsvData;
                  }

                  if (val.data.hasOwnProperty("splitJobExceptionCount")) {
                    splitJobExceptionCount = val.data.splitJobExceptionCount;
                  }

                  if (val.data.hasOwnProperty("lessSpecialDiscountCount")) {
                    lessSpecialDiscountCount =
                      val.data.lessSpecialDiscountCount;
                  }
                }
                obj = {
                  groupName: groupName,
                  storeID: val.data.storeID,
                  statusFlag: statusFlag,
                  lastRunAt: date,
                  fileProcessed: val.inputFile,
                  outputFile: val.data.outputFile,
                  message: val.data.message,
                  failedReason: failedReason,
                  nextRunAt: nextRunAt,
                  statusInfo: "",
                  uniqueId: uniqueId,
                  invalidmiscpaytypeCount: invalidmiscpaytypeCount,
                  estimateCount: estimateCount,
                  punchTimeMissingCount: punchTimeMissingCount,
                  inputFilePath1: inputFilePath1,
                  suffixedInvoicesCount: suffixedInvoicesCount,
                  suffixedInvoicesCsvData: suffixedInvoicesCsvData,
                  splitJobExceptionCount: splitJobExceptionCount,
                  lessSpecialDiscountCount: lessSpecialDiscountCount,
                };
                obj.statusInfo = this.getStatusInfo(val.data);
                if (val.data.operation === "json-processing") {
                  this.processJsonListArray.push(obj);
                }
                this.sortJSONArray(this.processJsonListArray);
              }
            );
          }
          
          callback();
        },
        error: (err) => {
          this.commonService.errorCallback(err, this);

        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  getStatusInfo(inputData: any) {
    let data = "";
    data += "Input File: " + inputData.inputFile + " \n";
    if (inputData.outputFile) {
      data += "Output File: " + inputData.outputFile + " \n";
    }
    let createdAt = null;
    if (inputData.createdAt) {
      let dt = inputData.createdAt;
      var y = dt.substr(0, 4);
      var t = dt.substr(4, dt.length);
      let remArray = t.match(/.{1,2}/g);
      remArray[0] = (remArray[0] * 1 - 1).toString();
      remArray.unshift(y);
      createdAt = moment(remArray).format("MM-DD-YYYY HH:mm:ss");
      data += "Created At: " + createdAt + " \n";
    }
    return data;
  }
  /**
   * showProcessJsonList function will show the Process JSON List
   *
   */
  showProcessJsonList() {
    this.processJSONLoading = true;
    if ($("#processJsonList").data('datatable')) {
      $("#processJsonList").dataTable().fnDestroy();
    }
    table1 = $("#processJsonList").dataTable().fnClearTable();
    const elm = this;
    let i = 0;
    setTimeout(() => {
      $(document).ready(() => {
        table1 = $("#processJsonList").dataTable({
          language: {
            decimal: ".",
            thousands: ",",
          },
          columnDefs: [
            { type: "numeric-comma", targets: "_all" },
            { orderable: false, targets: [3] },
            { orderable: true, targets: [0, 1, 2] },
          ],
          fixedHeader: {
            header: true,
            footer: true,
            headerOffset: $(".cat__top-bar").outerHeight() - 11,
          },
          bSort: false,
          order: [1, "desc"],
          responsive: true,
          scrollX: false,
          destroy: true,
          paging: true,
          deferRender: true,
          ordering: true,
          info: true,
          filter: true,
          length: true,
          processing: true,
          lengthMenu: [
            [50, 25, 10, 5],
            [50, 25, 10, 5],
          ],
          autoWidth: false,
          scrollY: "200px",
          fnRowCallback: function (settings: any, aData: any) {
            const pagination = $(this)
              .closest(".dataTables_wrapper")
              .find(".dataTables_paginate");
            pagination.toggle(this.api().page.info().pages > 1);
          },
          drawCallback: function (settings: any) {
            table1 = $("#processJsonList").DataTable();
            $("td:eq(1)", settings).css("width", "24%");
            $("td:eq(2)", settings).css("width", "13%");
            $("td:eq(3)", settings).css("width", "15%");
            $("td:eq(4)", settings).css("width", "5%");
          },
          columns: [
            {
              title: "Store Name",
              width: "24%",
              className: "dt-head-left",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                console.log("row data$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",rowData);
                let mage_Store_Name;

                if (rowData[8] && typeof rowData[8] === 'string' && rowData[8].includes("File To Process")) {
                  let temp1 = rowData[8].split("/")[7];
                  if (temp1) { 
                    let temp2 = temp1.split("_");
                    if (temp2.length >= 2) { console.log(temp2[0] + "_" + temp2[1]);
                      mage_Store_Name = temp2[0] + "_" + temp2[1];
                    }
                  }
                } if (rowData[8]) {
                  let test2 = rowData[8].split(":")[1] || '';
                
                  let test3 = test2.split("\n");
                
                  if (test3[0]) {
                    let test4 = test3[0].split("_");
                
                    if (test4.length > 1) {
                      mage_Store_Name = test4[0] + "_" + test4[1];
                    } else {
                      // Handle cases where there is no valid `_` split (optional fallback)
                      mage_Store_Name = test3[0];  // or set to another fallback value
                    }
                  }
                }

                let storeName = elm.getStoreNameFromDealerId(rowData[1]);
                storeName = storeName ? storeName : rowData[1];
                storeName =
                  storeName === rowData[1]
                    ? rowData[10]
                      ? rowData[10]
                      : rowData[1]
                    : storeName;
                // data = '<span style="cursor:pointer;" data-toggle="tooltip" data-placement="top" title="'
                //   + rowData[1] + '" data-animation="false" data-info="' + rowData + '">' + storeName + '</span>';
                //  data = '<span>' + rowData[1] + '|' +' Store Id' + '|' + 'Branch Id' + '</span>';
                let tempFilePath = rowData[14];
             
                if (rowData[8]) {
                  let parts = rowData[8].split("-");
                  if (parts.length > 9) {
                    mage_Store_Name = parts[9];
                  }
                  if (rowData[2] === "Running" || rowData[2] === "Failed") {
                    if (parts.length > 1) {
                      mage_Store_Name = parts[1];
                    }
                  }
                }
      
                if (tempFilePath) {
                  let ar = tempFilePath.split("_");
                  data = `<span>${mage_Store_Name}</span>`;
                } else {
                  data = "<span>" + mage_Store_Name + "</span>";
                }

                return data !== null ? data : null;
              },
            },
            {
              title: "Last Run At",
              width: "15%",
              type: "formatted-date",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                let lastRunAt = "";
                if (rowData[3]) {
                  let time = rowData[3].split(" ");
                  let HourFormat = elm.convertTimeFormat(time[1]);
                  lastRunAt = time[0] + " " + HourFormat;
                }
                data = "<span>" + lastRunAt + "</span>";
                return data !== null ? data : null;
              },
            },
            {
              title: "Status",
              width: "15%",
              className: "dt-head-right",
              render: function (data: any, type: any, rows: any, meta: any) {
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                var className = "";
                var toolTipMessage = rowData[8];
                rowData[7]
                  ? (toolTipMessage += "Failed Reason: " + rowData[7] + " \n")
                  : "";
                rowData[2] ===
                elm.SchedulerConstantService.STATUS_FLAG.COMPLETED
                  ? (className = "label-success")
                  : null;
                rowData[2] ===
                elm.SchedulerConstantService.STATUS_FLAG.SCHEDULED
                  ? (className = "label-scheduled")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.RUNNING
                  ? (className = "label-running")
                  : null;
                rowData[2] ===
                elm.SchedulerConstantService.STATUS_FLAG.REPEATING
                  ? (className = "label-repeating")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.FAILED
                  ? (className = "label-failed")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.QUEUED
                  ? (className = "label-queued")
                  : null;
                rowData[2] === elm.SchedulerConstantService.STATUS_FLAG.LOCKED
                  ? (className = "label-locked")
                  : null;
                rowData[2] == "HALT" ? (className = "label-halt") : null;
                data =
                  '<span style="cursor:pointer;float: right;margin: 0px;cursor: pointer;width: 53px !important;vertical-align: inherit;padding: 5px;" class="statusMessageDisplay label ' +
                  className +
                  '" data-toggle="tooltip" data-info="' +
                  rowData +
                  '" data-placement="left" title="' +
                  toolTipMessage +
                  '" data-animation="false">' +
                  rowData[2] +
                  "</span>";
                return data;
              },
            },
            {
              title: "",
              width: "5%",
              className: "dt-head-left pl-0",
              render: function (data: any, type: any, rows: any, meta: any) {
                const updateParentGroup = "Resume";
                const d = data;
                let rowData: any[] = [];
                rowData = Object.assign([], rows);
                if (rowData[2] == "HALT") {
                  data =
                    '<a class="runProxyNow" href="javascript:void(0);" data-note="' +
                    d +
                    '">' +
                    '<i aria-hidden="true" class="fa fa-caret-square-o-up text-success mt-2 haltAndResumeDetails" style="font-size: 18px;color: #80047d !important;"  data-toggle="tooltip" data-placement="top" title="' +
                    updateParentGroup +
                    '" data-animation="false" data-info="' +
                    rowData +
                    '"  ></i></a>';
                }else if(rowData[2] == "Running"){
                  let updateParentGroup = 'Show Processor Status';
            
                    data = '<a class="runProxyNow" href="javascript:void(0);" data-note="' + d + '">'
                      + '<i aria-hidden="true" class="fa fa-info-circle text-dark mt-2 showProcessorStatus" style="font-size: 18px; margin-right: 10px;" data-toggle="tooltip" data-placement="top" title="'
                      + updateParentGroup + '" data-animation="false" data-info="' + rowData + '" ></i></a>';
                 
                 } else {
                  data = "";
                }
                return data;
              },
            },
          ],
          rowGroup: {
            dataSrc: "Store Group",
          },
        });
        // tslint:disable-next-line:no-unused-expression
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
          "formatted-num-pre": function (a: any) {
            a = a === "-" || a === "" ? 0 : a.replace(/[^\d\-\.]/g, "");
            return parseFloat(a);
          },
          "formatted-num-asc": function (a: any, b: any) {
            return a - b;
          },
          "formatted-num-desc": function (a: any, b: any) {
            return b - a;
          },
          "formatted-date-asc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a ? new Date(a).getTime() : 0;
            const y = b ? new Date(b).getTime() : 0;
            return x < y ? -1 : x > y ? 1 : 0;
          },
          "formatted-date-desc": function (a: any, b: any) {
            a = $(a).html().trim();
            b = $(b).html().trim();
            const x = a
              ? new Date(a.toString()).getTime()
              : moment().unix() * 1000;
            const y = b
              ? new Date(b.toString()).getTime()
              : moment().unix() * 1000;
            return x < y ? 1 : x > y ? -1 : 0;
          },
        });
        elm.reDrawProcessJsonTable(elm.processJsonListArray);
      });
    }, 200);
  }

  /**
   * reDrawProcessJsonTable function will redraw the datatable using new table values
   *
   */
  reDrawProcessJsonTable(temp: any) {
    console.log("temp@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",temp);
    table1 = $("#processJsonList").DataTable();
    table1.search("").draw();
    table1 = $("#processJsonList").dataTable();
    table1.fnClearTable();
    table1 = $("#processJsonList").dataTable();
    const tempArr: any[] = [];
    for (let i = 0; i < temp.length; i++) {
      const t = temp[i];
      const rpt = [
        t.groupName,
        t.storeID,
        t.statusFlag,
        t.lastRunAt,
        t.fileProcessed,
        t.outputFile,
        t.message,
        t.failedReason,
        t.statusInfo,
        t.uniqueId,
        t.storeName,
        t.invalidmiscpaytypeCount,
        t.estimateCount,
        t.punchTimeMissingCount,
        t.inputFilePath1,
        t.suffixedInvoicesCount,
        t.suffixedInvoicesCsvData,
        t.splitJobExceptionCount,
        t.lessSpecialDiscountCount,
      ];
      tempArr.push(rpt);
    }
    if (tempArr.length > 0) {
      table1.fnAddData(tempArr, false); // Add new data
    }
    table1.fnDraw(); // Redraw the DataTable
    this.processJSONLoading = false;
    if (temp.length > 0) {
      setTimeout(() => {
        if ($("#processJsonList").data('datatable')) {
        $("#processJsonList").DataTable().columns.adjust().draw(false);
        }
      }, 100);
    }
  }

  setActiveFixedHeader() {
    if (
      navigator.userAgent.indexOf("MSIE") !== -1 ||
      navigator.appVersion.indexOf("Trident/") > 0
    ) {
      const evt = document.createEvent("UIEvents");
      evt.initUIEvent("resize", true, false, window, 0);
      window.dispatchEvent(evt);
    } else {
      window.dispatchEvent(new Event("resize"));
    }
  }

  startDateSelection() {
    let date: any = moment().subtract(6, "months");
    if (+date.format("D") <= this.SchedulerConstantService?.DAY_NUMBER_CHECK) {
      return moment(date).startOf("month");
    } else {
      return moment(date).add(1, "months").startOf("month");
    }
  }

  endDateSelection() {
    return moment();
  }

  utcToLocalTime(time: any) {
    let localTime = null;
    localTime = moment(time * 1)
      .local()
      .format(this.SchedulerConstantService.SCHEDULE_DATE_FORMAT);
    return localTime;
  }

  getRoOptionList() {
    this.roOptionList = [];
    let data = this.SchedulerConstantService.RO_OPTION;
    for (let i = 0; i < data.length; i++) {
      this.roOptionList.push({ id: data[i], itemName: data[i] });
    }
  }

  onChangeRoOptionList(item: any) {
    if (!this.containsObject(item, this.onChangeRoOption)) {
      this.onChangeRoOption.push(item);
    }
  }

  getAllJobsForUiUpdate(callback: any) {
    this.compareObjArrayLatest = [];
    const allStoreGroupsList = this.apollo
      .use("manageTekionSchedule")
      .query({
        query: getAllTekionExtractJobs,
        fetchPolicy: "network-only",
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata) => {
          const result: any = listdata;
          let obj: any = {};
          $.each(
            result["data"]["getAllTekionExtractJobs"]["jobArray"],
            (key: any, val: any) => {
              console.log("data.............2222.......................");
              console.log(val);
              let groupName = val.data.groupName;
              let scheduledDate = val.nextRunAt
                ? val.nextRunAt
                : val.lastRunAt
                ? val.lastRunAt
                : null;
              let date = "";
              scheduledDate
                ? (date = moment
                    .unix(scheduledDate / 1000)
                    .format("MM-DD-YYYY HH:mm:ss"))
                : null;
              let nextRunAt = "";
              val.nextRunAt
                ? (nextRunAt = moment.unix(val.nextRunAt / 1000).fromNow())
                : null;
              let groupStatus = val;
              const failedReason = val.failReason;
              $.each(val.data["storeDataArray"], (key: any, val: any) => {
                let jobStartDate = null;
                let jobEndDate = null;
                let rpt: any[] = [];
                let keyVal = null;
                let status = null;
                let seperator = "/";
                if (val.startDate.includes("-")) {
                  seperator = "-";
                }
                const dateArray = val.startDate.split(seperator);
                const startDate =
                  dateArray[0] + "-" + dateArray[1] + "-" + dateArray[2];
                if (val.endDate.includes("-")) {
                  seperator = "-";
                }
                const dateArrayEnd = val.endDate.split(seperator);
                const endDate =
                  dateArrayEnd[0] +
                  "-" +
                  dateArrayEnd[1] +
                  "-" +
                  dateArrayEnd[2];
                let range = startDate + " - " + endDate;
                jobStartDate = val.startTime;
                jobEndDate = val.endTime;
                rpt = [groupName, val.locationId, range, date];
                keyVal = rpt.join();
                let jobStatus = false;
                status = val.status;
                let statusFlag = "";
                if (jobStartDate && jobEndDate && status) {
                  jobStatus = true;
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.COMPLETED;
                } else if (jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.RUNNING;
                } else if (!jobStartDate && !jobEndDate) {
                  statusFlag =
                    this.SchedulerConstantService.STATUS_FLAG.SCHEDULED;
                } else if (jobStartDate && jobEndDate && !status) {
                  jobStatus = true;
                  statusFlag = this.SchedulerConstantService.STATUS_FLAG.FAILED;
                } else {
                  statusFlag = this.getJobStatus(groupStatus);
                }
                status = statusFlag;
                let objNew: any = {};
                objNew[keyVal] = status;
                this.compareObjArrayLatest.push(objNew);
              });
            }
          );
          callback();
        },
        error: (err) => {
          this.commonService.errorCallback(err, this);

        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  compareObjectValue() {
    let dataTableObjects: any[] = [];
    let data = null;
    let self = this;
    table1 = $("#scheduleProcessQueueTekion").DataTable();
    data = table1.rows().data();
    let i = 0;
    data.each(function (value: any, index: any) {
      let status = null;
      let keyVal = "";
      let res = null;
      keyVal = value[0] + "," + value[1] + "," + value[2] + "," + value[3];
      status = value[4];
      res = self.compareKey(keyVal);
      if (res !== status) {
        self.reloadExtractionQueue(res);
      }
      i++;
    });
  }

  reloadExtractionQueue(res: any) {
    this.refreshScheduleList();
    if (
      res === this.SchedulerConstantService.STATUS_FLAG.COMPLETED ||
      res === this.SchedulerConstantService.STATUS_FLAG.FAILED
    ) {
      this.refreshScheduleList();
      this.refreshScheduleListCompleted();
    }
  }

  compareKey(key: any) {
    let status = null;
    var itemsProcessed = 0;
    let length = this.compareObjArrayLatest.length;
    this.compareObjArrayLatest.forEach(function (obj, index, self) {
      itemsProcessed++;
      if (obj.hasOwnProperty(key)) {
        status = obj[key];
      }
    });
    if (itemsProcessed === length) {
      return status;
    }
    return;
  }

  processJSONReloadList(callback: any) {
    this.compareObjArrayProcessJSONList = [];
    this.processJSONJobsQueueLength = 0;
    const allStoreGroupsList = this.apollo
      .use("manageTekionSchedule")
      .query({
        query: getAllTekionProcessJSONJobs,
        fetchPolicy: "network-only",
      })
      .pipe(takeUntil(this.subscription$))
      .subscribe({
        next: (listdata) => {
          const result: any = listdata;
          if (
            result["data"]["getAllTekionProcessJSONJobs"] &&
            result["data"]["getAllTekionProcessJSONJobs"]["processJSONJobs"]
          ) {
            $.each(
              result["data"]["getAllTekionProcessJSONJobs"][
                "processJSONJobs"
              ],
              (key: any, val: any) => {
                let statusFlag = this.getJobStatus(val);
                let obj = { statusFlag: statusFlag };
                if (val.data.operation === "json-processing") {
                  let keyVal = null;
                  keyVal = val._id;
                  let objNew: any = {};
                  objNew[keyVal] = statusFlag;
                  this.compareObjArrayProcessJSONList.push(objNew);
                }
              }
            );
            if (
              result["data"]["getAllTekionProcessJSONJobs"] &&
              result["data"]["getAllTekionProcessJSONJobs"][
                "processJSONJobsQueue"
              ]
            ) {
              this.processJSONJobsQueueLength = result["data"][
                "getAllTekionProcessJSONJobs"
              ]["processJSONJobsQueue"].length
                ? result["data"]["getAllTekionProcessJSONJobs"][
                    "processJSONJobsQueue"
                  ].length
                : 0;
            }
            callback();
          }
        },
        error: (err) => {
          this.commonService.errorCallback(err, this);

        },
        complete: () => {
          console.log("Completed");
        },
      });
  }

  compareObjectValueProcessJSON() {
    let reloadStatus = false;
    let data = null;
    let self = this;
    table1 = $("#processJsonList").DataTable();
    data = table1.rows().data();
    let incQueuedList = 0;
    let incProcessList = 0;
    data.each(function (value: any, index: any) {
      let status = null;
      let keyVal = "";
      let res = null;
      if (value[2] != self.SchedulerConstantService.STATUS_FLAG.QUEUED) {
        keyVal = value[9];
        status = value[2];
        incProcessList++;
      } else {
        incQueuedList++;
      }
      res = self.compareProcessJSONKey(keyVal);
      if (status == "HALT" || status == "DEAD") {
        status = "Failed";
      }
      if (res !== status) {
        reloadStatus = true;
      }
    });
    if (
      this.processJSONJobsQueueLength !== incQueuedList &&
      this.processJSONJobsQueueLength > incQueuedList
    ) {
      reloadStatus = true;
    }

    if (this.compareObjArrayProcessJSONList.length !== incProcessList) {
      reloadStatus = true;
    }
    if (reloadStatus) {
      this.getAllProcessJsonJobs(() => {
        this.showProcessJsonList();
      });
    }
  }

  compareProcessJSONKey(key: any) {
    let status = null;
    var itemsProcessed = 0;
    let length = this.compareObjArrayProcessJSONList.length;
    this.compareObjArrayProcessJSONList.forEach(function (obj, index, self) {
      itemsProcessed++;
      if (obj.hasOwnProperty(key)) {
        status = obj[key];
      }
    });
    if (itemsProcessed === length) {
      return status;
    }
    return;
  }

  getJobStatus(val: any) {
    let statusFlag: any = "";
    val.lockedAt
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.LOCKED)
      : null;
    val.scheduled
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.SCHEDULED)
      : null;
    val.queued
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.QUEUED)
      : null;
    val.completed
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.COMPLETED)
      : null;
    val.failed
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.FAILED)
      : null;
    val.repeating
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.REPEATING)
      : null;
    val.running
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.RUNNING)
      : null;
    val.scheduled && val.failed
      ? (statusFlag = this.SchedulerConstantService.STATUS_FLAG.RESCHEDULED)
      : null;
    return statusFlag;
  }

  convertTimeFormat(timeString: any) {
    var H = +timeString.substr(0, 2);
    var h = H % 12 || 12;
    var ampm = H < 12 || H === 24 ? " AM" : " PM";
    timeString = h + timeString.substr(2, 3) + ampm;
    return timeString;
  }

  setDateRange(type: any, jobType: any) {
    if (type) {
      this.createSchedule.patchValue({
        roOption: "all",
      });
    }
    this.setExtractionModeSelection(jobType);
    if (this.jobTypeStatus !== type) {
      this.jobTypeStatus = type;
      if (!type) {
        this.selectMultiDateRangeOption = false;
        this.createSchedule.patchValue({
          roOption: "all",
        });
        this.setExtractionModeSelection(jobType);
        let date = moment().subtract(31, "days");
        this.dateRangePickerOptions = {
          startDate: date,
          endDate: moment(),
          showDropdowns: true,
          autoApply: true,
          opens: "left",
          minDate: date,
          maxDate: this.endDateSelection(),
          disabled: true,
        };
        this.dateInput = {
          start: date,
          end: this.endDateSelection(),
        };
      } else {
        this.selectMultiDateRangeOption = true;
        this.dateRangePickerOptions = {
          startDate: this.startDateSelection(),
          endDate: moment(),
          showDropdowns: true,
          autoApply: true,
          opens: "left",
          maxDate: new Date(),
          disabled: false,
        };
        this.dateInput = {
          start: this.startDateSelection(),
          end: this.endDateSelection(),
        };
      }
    }
  }

  setExtractionModeSelection(jobType: any) {
    this.displayOnDemand = false;
    if (jobType === "current") {
      this.displayOnDemand = true;
      this.createSchedule.patchValue({
        roOption: "current",
      });
    }
    if (jobType === "refresh") {
      this.createSchedule.patchValue({
        roOption: "all",
      });
    }
  }

  checkJobExistInExtractionQueue(
    groupName: any,
    storeCode: any,
    dateRange: any
  ) {
    let processQueueListCopy: any[] = [];
    processQueueListCopy = Object.assign([], this.processQueueList);
    processQueueListCopy = processQueueListCopy
      .filter((item: any, index: number) => index < processQueueListCopy.length)
      .filter(
        (item: any, index: number) =>
          item.groupName === groupName &&
          item.store === storeCode &&
          item.range === dateRange &&
          item.status !== "Scheduled"
      );
    return processQueueListCopy.length;
  }
  sortJSONArray(temp: any) {
    this.processJsonListArray = temp
      .filter((item: any, index: number) => index < temp.length)
      .sort((a: any, b: any): any => {
        const x = a["statusFlag"].toLowerCase().replace(/^[^a-z0-9]*/g, "");
        const y = b["statusFlag"].toLowerCase().replace(/^[^a-z0-9]*/g, "");
        return x > y ? -1 : x < y ? 1 : 0;
      });
    return temp;
  }

  reloadGroupList() {
    this.closeToolTip();
    this.reloadGroup = true;
    this.commonService.allS360Jobs("Tekion", "production", (result: any) => {
      this.storeGroupList = result.storeGroupList;
      this.jobGroupList = result.jobGroupList;
      this.getGroupFilterList();
      this.reloadGroup = false;
    });
  }

  stopTimer() {
    this.autoReloadTekion3PAStatus = false;
    this.isPaused = false;
  }

  startTimer() {
    this.autoReloadTekion3PAStatus = true;
    this.isPaused = true;
    this.getNotificationForUi()
  }

  closeToolTip() {
    $("[rel=tooltip]").tooltip("enable");
    $(".tooltip").tooltip("hide");
  }

  preSelectGroupAndStore() {
    const storeObj = localStorage.getItem("selectedStoreObj")
      ? JSON.parse(localStorage.getItem("selectedStoreObj")!)
      : null;
    const groupObj = localStorage.getItem("selectedGroupObj")
      ? JSON.parse(localStorage.getItem("selectedGroupObj")!)
      : null;
    if (storeObj && groupObj) {
      const grpObj = {
        id: groupObj.mageGroupName,
        itemName: groupObj.mageGroupName,
        mageGroupCode: groupObj.mageGroupCode,
        mageGroupName: groupObj.mageGroupName,
        mageStoreCode: groupObj.mageStoreCode,
        mageStoreName: groupObj.mageStoreName,
        companyId: groupObj.companyId,
      };
      const strObj = {
        id: storeObj.companyName,
        itemName: storeObj.companyName,
        mageGroupCode: storeObj.mageGroupName,
        mageStoreCode: storeObj.mageStoreCode,
        mageStoreName: storeObj.mageStoreName,
        stateCode: storeObj.state,
        thirdPartyUsername: storeObj.thirdPartyUsername,
        etlDMSType: storeObj.etlDMSType,
      };
      this.onSelectStoreGroup(grpObj);
      this.onSelectStore(strObj);
    }
  }

  haltAndResumeDetails(data: any) {
    this.suffixedInvoices = [];
    let res;
    console.log("halt and resume details", data);
    res = data.split(",").reverse();

    console.log("::::::::::::::::::::::::::::::::::::res", res);

    this.invalidmiscpaytypeCountMsg = res[4];
    this.estimateCountMsg = res[4];
    this.punchTimeMissingMsg = res[3];
    // this.suffixedInvoicesCsvData = res[0];
    this.splitJobExceptionCountMsg = res[1];
    this.lessSpecialDiscountCountMsg = res[0];
    // let newArr =this.suffixedInvoicesCsvData.split('*')

    //   for(let i=0;i<newArr.length;i++){
    //   let tempObj: {source:string; month: string; suffixedROCount: string } = {
    //     source:'',
    //     month: '',
    //     suffixedROCount: ''
    //   };
    //   tempObj.source =newArr[i].split(':')[0]
    //   tempObj.month =newArr[i].split(':')[1]
    //   tempObj.suffixedROCount = newArr[i].split(':')[2]
    //   this.suffixedInvoices.push(tempObj)

    // }

    // this.suffixedInvoicesCountMsg=res[1]
    this.resumeProcessorInput = data;
    $("#haltAndResumeModal").modal("show");
  }

  closeHaltAndResumeModal() {
    $("#haltAndResumeModal").modal("hide");
  }

  resumeProcessor() {
    let res, tmp, tmp1, tmp2, inputFile;
    console.log(this.resumeProcessorInput);
    console.log(typeof this.resumeProcessorInput);
    res = this.resumeProcessorInput.split(",");
    console.log(res);
    tmp = res[8];
    console.log("tmp:", tmp);
    tmp1 = tmp.split("\n")[0];
    console.log("tmp1", tmp1);
    tmp2 = tmp1.split("Input File:")[1];
    console.log("tmp2:", tmp2);
    if (tmp2) {
      inputFile = tmp2.trim();
    }
    console.log("inputFile?????????????????????", inputFile);
    const payload = { extractFile: inputFile, dms: "TEKION" };
    let url = environment.haltAndResume;
     const token = localStorage.getItem("token");
    // let headers = new HttpHeaders({ "Content-Type": "application/json" });
    // headers.append("Authorization", `Bearer ${token}`);
    
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
     // const resData = JSON.parse(res["_body"]);
     const resData = res;
      this.closeHaltAndResumeModal();
      this.refreshProcessJsonList();
      if (resData.status) {
        console.log("resData",resData);
        swal({
          title: "Processor job resumed successfully",
          type: "success",
          confirmButtonClass: "btn-success pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      } else {
        console.log(resData);
        swal({
          title: "Something went wrong",
          type: "warning",
          confirmButtonClass: "btn-warning pointer",
          confirmButtonText: this.constantService.CLOSE,
        });
      }
    });
  }

  getTekionInputFiles(directoryPath: any, callback: any) {
    const payload = { folderPath: directoryPath };
    let url = environment.getTekionAccountingCSVFilesUrl;
    const token = localStorage.getItem("token");
    // let headers = new HttpHeaders({ "Content-Type": "application/json" });
    // headers.append("Authorization", `Bearer ${token}`);
    const headers = new HttpHeaders({
      authorization: token ? `Bearer ${token}` : "",
    });
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
    //  const resData = JSON.parse(res["_body"]);
    const resData = res;
      if (resData.status) {
        console.log("resData",resData);
        // const inputFiles = JSON.parse(res["_body"]).data;
        // this.archiveFileList = JSON.parse(res["_body"]).archiveData;
        const inputFiles = res.data;
        this.archiveFileList = res.archiveData;
        this.archiveFileList.forEach((data, index) => {
          data.tmpFilename = data.filename;
        });
        const filteredArr = this.archiveFileList.reduce((thing, current) => {
          const x = thing.find(
            (item: any) => item.tmpFilename == current.tmpFilename
          );
          if (!x) {
            return thing.concat([current]);
          } else {
            return thing;
          }
        }, []);

        this.archiveFileList = filteredArr;
        callback(inputFiles);
      } else {
        callback();
      }
    });
  }

  switchServer(toggleMockServer =false) {
    if (this.solve360ServerDecider) {
      let serverType = "test";
      this.commonService.allS360Jobs("Tekion", serverType, (result: any) => {
        this.loading = false;
        this.storeGroupList = result.storeGroupList;
        this.storeList = result.storeList;
        this.jobGroupList = result.jobGroupList;
        this.getGroupFilterList();
        this.preSelectGroupAndStore();
        this.preSelectGroupAndStore();
        if(toggleMockServer){
          this.toastrService.success ("Switched to Mock Server");
        }
      });
    } else {
      let serverType = "production";
      this.commonService.allS360Jobs("Tekion", serverType, (result: any) => {
        this.loading = false;
        this.storeGroupList = result.storeGroupList;
        this.storeList = result.storeList;
        this.jobGroupList = result.jobGroupList;
        this.getGroupFilterList();
        this.preSelectGroupAndStore();
        if(toggleMockServer){
          this.toastrService.success ("Switched to Production Server");
        }
      });
    }
  }

  processorSteps = [
    "Unzipping Input to Work",
  "Creating Schema from Model",
  "Iterating Over Zip File Contents",
  "Detecting Problematic ROs",
  "Detecting Open/Void RO Data and Reporting",
  "Generate Config File",
  "Load From Scheduler DB",
  "Compressing Directory",
  "Pre-import Halt Detection",
  "Moving Work to Bundle Directory"
  ];
  currentStepIndex: number = -1;

  showProcessorStatus(data: string) {
    let res = data.split(",");
    console.log('showProcessorStatus:', res);
    let jobId = res[9];
    
    const payload = { mongoId: jobId };
    let url = environment.fetchProcessStatus;
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` });
  
    this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
      const resData = res?.data?.response[0];
      console.log("resData:", resData);
  
      if (resData) {
        this.processRunningStatus = resData.processorRunningStatus;
        let formattedDate = moment.utc(resData.processorStatusUpdatedAt).format('MM/DD/YYYY HH:mm:ss');
        this.processorStatusUpdatedAt = formattedDate;
        console.log("Process Running status$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.processRunningStatus);
        // Determine the current step index
        console.log("Process Running status$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.processRunningStatus.split('/'));
        this.currentStepIndex = Number(this.processRunningStatus.split('/')[0])
        console.log("currentStepIndex$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",this.currentStepIndex);
      } else {
        console.log("Error:", resData);
        this.showStatusMessage('Something Went Wrong!', 'failure');
      }
    });
  
    $('#showProcessorStatusModal').modal('show');
  }

    
 closeProcessorStatusModal(){
  $('#showProcessorStatusModal').modal('hide');
}
}

import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ManagequorumComponent } from './managequorum.component';

const routes: Routes = [
    { path:"", component: ManagequorumComponent ,
    data: {
        title: "ManageQuorum",
        breadcrumb: [{ label: "ManageQuorum", url: "" }],
      },}
];

@NgModule({
    exports: [RouterModule],
    imports:[RouterModule.forChild(routes)]
})
export class ManagequorumRoutingModule{}
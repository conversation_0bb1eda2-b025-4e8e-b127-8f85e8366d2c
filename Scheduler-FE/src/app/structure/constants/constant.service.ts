import { Injectable } from "@angular/core";

// @Injectable()
@Injectable({
  providedIn: "root", // This ensures the service is available application-wide
})
export class ConstantService {
  releaseVerion = '';
  HOME_URL: string;
  CDKJOBS_URL: string;
  CDKFLEX_URL: string;
  TEST_ENV: string;
  UAT_ENV: string;
  DEV_ENV: string;
  PROD_ENV: string;
  AREYOUSURE: string;
  CLOSE: string;
  OK: string;
  ACCESS_VALIDATION_FAIL: string;
  ACCESS_VALIDATION_SUCCESS: string;
  ACCESS_VALIDATION_FAIL_MSG: string;
  ACCESS_VALIDATION: string;
  SKIP_ERROR_INFO: string;
  ERROR_MESSAGE_FOR_REQUIRED: string;
  ENTERPRISE_NOT_FOUND_ERROR_MSG: string;
  COMPANY_NOT_FOUND_ERROR_MSG: string;
  SOMETHING_WENT_WRONG: string;
  SITE_COPYRIGHT_MESSAGE: String;
  AUTOMATE_URL: string;
  DEALERBUILT_URL: string;
  DEALERTRACK_URL: string;
  ADAM_URL: string;
  REYNOLDS_URL: string;
  DOMINION_URL: string;
  AUTOSOFT_URL: string;
  PBS_URL: string;
  QUORUM_URL: string;
  UCS_URL: string;
  MPK_URL: string;
  TEKION_URL: string;
  TEKIONAPI_URL: string;
  FORTELLIS_URL: string;
  MANAGE_SCHEDULE_WARNING_MSG: string;
  DMS_TYPE: any;
  DEFAULT_SKIP_COUNT: number;
  IMPORTSTATUS_URL: string;
  SCHEDULERIMPORT_URL: string;
  SUCCESS_MESSAGE!: string;
  CANNOT_FETCH_DATA!: string;
  PAYTYPESETTING_URL: string;
  MANAGESCHEDULEFIELD_URL: string;
  PORTALSTAGEUPDATE :string;
    constructor() {
    this.releaseVerion = "v2.14.0";
    /**
    * Urls
    */
    this.HOME_URL = '/home';
    this.CDKJOBS_URL = '/ManageCDKJobs';
    this.CDKFLEX_URL = '/ManageCDKFlex';
    this.AUTOMATE_URL = '/ManageAutoMate';
    this.DEALERBUILT_URL = '/ManageDealerBuilt';
    this.DEALERTRACK_URL = '/ManageDealerTrack';
    this.ADAM_URL = '/ManageAdam';
    this.REYNOLDS_URL = '/ManageReynolds';
    this.DOMINION_URL = '/ManageDominion';
    this.AUTOSOFT_URL = '/ManageAutosoft';
    this.PBS_URL = '/ManagePbs';
    this.QUORUM_URL = '/ManageQuorum';
    this.UCS_URL = '/ManageUcs';
    this.MPK_URL = '/ManageMpk';
    this.TEKION_URL = '/ManageTekion';
    this.TEKIONAPI_URL = '/ManageTekionApi'
    this.FORTELLIS_URL = "/ManageFortellis";
    this.IMPORTSTATUS_URL = "/ImportStatus";
    this.SCHEDULERIMPORT_URL = "/SchedulerDmsImport";
    this.PAYTYPESETTING_URL = "/ManageSchedule-Setting";
    this.MANAGESCHEDULEFIELD_URL= "/ManageScheduleFields";
    this.PORTALSTAGEUPDATE = "/StageUpdatePortal"
    this.UAT_ENV = "uat";
    this.TEST_ENV = "test";
    this.DEV_ENV = "dev";
    this.PROD_ENV = "dev";
    this.AREYOUSURE = "Are you sure?";
    this.CLOSE = "Close";
    this.OK="OK";
    this.ACCESS_VALIDATION_FAIL = "Vendor does not have validation";
    this.ACCESS_VALIDATION_SUCCESS = "Access validation success";
    this.ACCESS_VALIDATION_FAIL_MSG = "Vendor does not have access.";
    this.ACCESS_VALIDATION = "Validation status";
    this.SKIP_ERROR_INFO =
      "The maximum no. of ROs we can skip due to bad data.";
    this.ENTERPRISE_NOT_FOUND_ERROR_MSG = "Enterprise not found.";
    this.COMPANY_NOT_FOUND_ERROR_MSG = "Company not found.";
    this.SOMETHING_WENT_WRONG = "Something went wrong, please try again";
    this.ERROR_MESSAGE_FOR_REQUIRED = "This entry can't be empty";
    this.SITE_COPYRIGHT_MESSAGE =
      "Copyright: Armatus Dealer Uplift All rights reserved.";
      this.SUCCESS_MESSAGE = "success";
      this.CANNOT_FETCH_DATA = "Error occur while fetching data";
    this.DMS_TYPE = {
      CDK: 'cdk',
      CDK3PA: 'cdk3pa',
      AUTOMATE: 'automate',
      DEALERTRACK: 'dealertrack',
      DEALERBUILT: 'dealerbuilt',
      ADAM: 'adam',
      REYNOLDS:'reynolds',
      UCS: 'ucs',
      DOMINION:'dominionvue',
      AUTOSOFT:'autosoft',
      TEKIONAPI: 'tekionapi',
      FORTELLIS: 'fortellis',
    };
    this.DEFAULT_SKIP_COUNT = 10;
    this.MANAGE_SCHEDULE_WARNING_MSG = 'The selected DMS {{dmsCode}} not available to perform schedule';
  }

}

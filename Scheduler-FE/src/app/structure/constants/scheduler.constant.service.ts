import { Injectable } from "@angular/core";

@Injectable({
  providedIn: "root", // This ensures the service is available application-wide
})
export class SchedulerConstantService {
  ERROR_IN_SCHEDULE_JOB: string;
  VALIDATION_MESSAGE_GL_COMPANY_ID: string;
  VAL<PERSON>ATION_MESSAGE_CUSTOM_BRANCH_NAME_REYNOLDS: string;
  VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_AUTOSOFT: string;
  VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_PBS: string;
  VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_QUORUM: string;
  VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_UCS: string;
  VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_MPK: string;
  VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_TEKION: string;
  EVENT_EMITTER: any = {};
  SAVE_SCHEDULE_SUCCESS_MESSAGE: string;
  RUN_SCHEDULE_NOW: string;
  DO_PROXY_NOW: string;
  DO_PAYTYPE_UPDATE: string;
  CANCEL_SCHEDULE_NOW: string;
  EXCEED_TIME: string;
  STATUS_FLAG: any = {};
  RUN_NOW: string;
  RO_OPTION: any = {};
  DAY_NUMBER_CHECK!: number;
  LOCKED_MESSAGE: string;
  JOB_STARTED: string;
  SCHEDULE_DATE_FORMAT: string;
  DATE_FORMAT: string;
  DEFAULT_RO_OPTION: string;
  DEFAULT_JOB_TYPE: string;
  END_POINTS: any = {};
  JOB_TYPES: any = {};
  JOB_FILTER_ARRAY: any;
  PAY_TYPE_FILTER: any;
  DEALER_ADDRESS_EMPTY: string;
  WARNING_MESSAGE_DA: string;
  WARNING_MESSAGE_DA_PROXY: string;
  constructor() {
    this.DAY_NUMBER_CHECK = 15;
    this.ERROR_IN_SCHEDULE_JOB = "Error in scheduling Job";
    this.VALIDATION_MESSAGE_GL_COMPANY_ID =
      "Please provide company id for GL account pull";
    this.VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_REYNOLDS =
      "Please provide a branch name";
    this.VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_AUTOSOFT =
      "Please provide a branch name";
    this.VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_PBS=
      "Please provide a branch name";
      this.VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_QUORUM=
      "Please provide a branch name";
      this.VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_UCS=
      "Please provide a branch name";
      this.VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_TEKION=
      "Please provide a branch name";
      this.VALIDATION_MESSAGE_CUSTOM_BRANCH_NAME_MPK=
      "Please provide a branch name";
    this.RUN_SCHEDULE_NOW = "Run this Job schedule now";
    this.DO_PROXY_NOW = "Re-Run Proxy now";
    this.DO_PAYTYPE_UPDATE = "Update pay type list";
    this.CANCEL_SCHEDULE_NOW = "Cancel this Job schedule now";
    this.SAVE_SCHEDULE_SUCCESS_MESSAGE = "Job Schedule saved successfully";
    this.RUN_NOW = "Run Now";
    this.EXCEED_TIME = "Time exceeded, remaining stores scheduled to next day.";
    this.LOCKED_MESSAGE = "Execution starts in a minute";
    this.JOB_STARTED = "Job already started";
    this.DEALER_ADDRESS_EMPTY = "Dealer address is empty!";
    this.EVENT_EMITTER = {
      STEP_1: "Saving...",
      STEP_SAVE_SCHEDULE: "Saving Schedule ...",
      STEP_RELOAD_LIST: "Reloading schedule list...",
      CANCEL_SCHEDULE: "Cancel job schedule ..",
    };
    this.STATUS_FLAG = {
      RUNNING: "Running",
      SCHEDULED: "Scheduled",
      QUEUED: "Queued",
      COMPLETED: "Completed",
      FAILED: "Failed",
      REPEATING: "Repeating",
      LOCKED: "Locked",
      RESCHEDULED: "Rescheduled",
      HALTED: "Halted",
      UPLOADHALT: "Upload Halt"
    };
    this.RO_OPTION = ["all", "monthly", "weekly"];
    this.SCHEDULE_DATE_FORMAT = "MM-DD-YYYY h:mm A";
    this.DATE_FORMAT = "MM-DD-YYYY";
    this.DEFAULT_RO_OPTION = "monthly";
    this.DEFAULT_JOB_TYPE = "initial";
    this.END_POINTS = {
      GRAPHQL_END_POINT: "graphqlCustom",
      AGENDA_END_POINT: "dashboard",
    };
    this.JOB_TYPES = {
      CDK3PA: "CDK3PA",
      AUTOMATE: "AUTOMATE",
      DEALERTRACK: "DEALERTRACK",
      REYNOLDS: "REYNOLDS",
      UCS: "UCS",
      AUTOSOFT: "AUTOSOFT",
    };
    this.JOB_FILTER_ARRAY = [
      { id: "All", itemName: "All" },
      { id: "CDK3PA", itemName: "CDK3PA" },
      { id: "AUTOMATE", itemName: "AUTOMATE" },
      { id: "DEALERTRACK", itemName: "DEALERTRACK" },
      { id: "REYNOLDS", itemName: "REYNOLDS" },
      { id: "UCS", itemName: "UCS" },
      { id: "AUTOSOFT", itemName: "AUTOSOFT" },
    ];
    this.PAY_TYPE_FILTER = {
      WARNING_MESSAGE: "Please select PayTypes",
      WARNING_MESSAGE_DO_PROXY: "Error while generating Proxy. Missing inputs!",
      JOB_CREATION_SUCCESS: "Process XML Job created successfully.",
      JOB_RERUN_SUCCESS: "Rerun Schedule Successfully",
    };
    this.WARNING_MESSAGE_DA = "Please Fill Dealer Address";
    this.WARNING_MESSAGE_DA_PROXY = "Error while generating Proxy";
  }
}

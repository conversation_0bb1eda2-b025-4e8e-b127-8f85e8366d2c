import { Injectable } from "@angular/core";
// import { Http, Headers, Response } from '@angular/http';
// import "rxjs/add/operator/map";
// import { Logger } from 'angular2-logger/core';
import { ConstantService } from "../constants/constant.service";
import { CommonService } from "../services/common.service";
import { HttpClient } from "@angular/common/http";
import { IDropdownSettings } from "ng-multiselect-dropdown";

declare var $: any;
declare var jQuery: any;
declare var XLSX: any;

@Injectable()
export class DmFormGroupService {
  constructor(
    private http: HttpClient,
    // private _logger: Logger,
    private constantService: ConstantService,
    private commonService: CommonService
  ) {}
  init() {}
  /** dropdown section starting */

  dropdownSettings() {
    const dropdownSettings: IDropdownSettings = {
      singleSelection: false,
      idField: "id",
      textField: "itemName",
      selectAllText: "Select All",
      unSelectAllText: "UnSelect All",
      allowSearchFilter: true,
      enableCheckAll: true,
      // disabled: true,
    };
    return dropdownSettings;
  }

  singleDropdownSettings() {
    const dropdownSettings: IDropdownSettings = {
      singleSelection: true,
      idField: "id",
      textField: "itemName",
      allowSearchFilter: true,
      closeDropDownOnSelection: true,
    };
    return dropdownSettings;
  }
  singleDropdownSettingsDisable() {
    const dropdownSettings: IDropdownSettings = {
      singleSelection: true,
      idField: "id",
      textField: "itemName",
      allowSearchFilter: true,
      //disabled: true,
    };
    return dropdownSettings;
  }
  singleDropdownSettingsState() {
    const dropdownSettings: IDropdownSettings = {
      singleSelection: true,
      idField: "id",
      textField: "itemName",
      allowSearchFilter: true,
    };
    return dropdownSettings;
  }

  multiDropdownSettingsDisable() {
    const dropdownSettings: IDropdownSettings = {
      singleSelection: false,
      idField: "id",
      textField: "itemName",
      allowSearchFilter: true,
      //disabled: true,
    };
    return dropdownSettings;
  }
  multiDropdownSettings() {
    const dropdownSettings: IDropdownSettings = {
      singleSelection: false,
      idField: "id",
      textField: "itemName",
      allowSearchFilter: true,
    };
    return dropdownSettings;
  }
}

import { Injectable } from "@angular/core";
import { HttpHeaders } from "@angular/common/http";

import { Router} from "@angular/router";
import { environment } from "../../../environments/environment";
import { ConstantService } from "../constants/constant.service";
import { Apollo } from "apollo-angular";
import gql from "graphql-tag";

import * as moment from "moment-timezone";
const isProduction = environment.envName === "prod" ? true : false;
import { HttpClient } from "@angular/common/http";
import { Subject, takeUntil ,throwError} from "rxjs";
import { map, catchError } from "rxjs/operators";
import { MsalService } from "@azure/msal-angular";
const statusTestData = environment.showTestData === true;
let allS360Jobs: any;
let allReleaseDetails =gql`
query allReleaseDetails {
  allReleaseDetails(condition: { isActive: true }) {
    edges {
      node {
        versionNo
      }
    }
  }
}
`;
if (statusTestData) {
  allS360Jobs = gql`
    query allEtlJobExtractImportIncludeTestData {
      allEtlJobExtractImportIncludeTestData {
        edges {
          node {
            companyName
            projectId
            assignedtoCn
            mageGroupCode
            mageGroupName
            mageStoreCode
            mageStoreName
            mageManufacturer
            state
            thirdPartyUsername
            dmsCode
            etlDms
            errors
            companyId
            address
            dealerbuiltSourceId
            dealerbuiltStoreId
            branchNo
            streetaddress
            city
            zipcode
            secondaryProjectId
            projectName
            projectType
            secondaryProjectType
            secondaryProjectName
          }
        }
      }
    }
  `;
} else {
  allS360Jobs = gql`
    query allEtlJobExtractImports {
      allEtlJobExtractImports {
        edges {
          node {
            companyName
            projectId
            assignedtoCn
            mageGroupCode
            mageGroupName
            mageStoreCode
            mageStoreName
            mageManufacturer
            state
            thirdPartyUsername
            dmsCode
            etlDms
            errors
            companyId
            address
            dealerbuiltSourceId
            dealerbuiltStoreId
            branchNo
            streetaddress
            city
            zipcode
            secondaryProjectId
            projectName
            projectType
            secondaryProjectType
            secondaryProjectName
          }
        }
      }
    }
  `;
}
const getProjectsByCompany = gql`
  query getProjectsByCompany($inCompanyId: BigInt) {
    getProjectsByCompany(inCompanyId: $inCompanyId)
  }
`;

declare var $: any;
declare var analytics: any;
@Injectable({
  providedIn: "root", // This ensures the service is available application-wide
})
export class CommonService {
  loading: any = false;
  azureGroup: any[] = [];
  // pageTitle = "";
  userName: string = "";
  constructor(
    private http: HttpClient,
    private router: Router,
    private apollo: Apollo,
    private constantService: ConstantService,
    private authService: MsalService
  ) {}

  public isMockServer = true;
  private subscription$ = new Subject();
  
  showInfo(infoId: any) {
    const divId: any = document.getElementById(infoId);
    if (divId.style.display === "none") {
      divId.style.display = "block";
    } else {
      divId.style.display = "none";
    }
  }
    errorCallback(err: any, elm: any) {
      console.log('error:', err);
      const activityData = {
          activityName: 'Error occured on DB process',
          activityDescription: 'Current Page: ' + this.router.url,
          activityData: err
      };
      this.saveActivity('Error', activityData);
      if (elm.loading) {
          elm.loading = false;
      }
      if (err && err.length && err[0] && err[0].message) {
          console.log('==============> message', err[0].message, err[0].status);
          if (err[0] && err[0].status == '[ERR: 5001]') {
              console.log('==============> message 5001', err[0].message, err[0].status);
              this.logoutRedirect();
              return;
          } else {
              alert(err[0].message);
          }
      } else if (err.errors && err.errors.length && err.errors[0] && err.errors[0].message) {
          console.log('===============>> message2 ', err.errors[0], err.errors[0].message)
          // alert(err.errors[0].message);
          if (err.errors[0].status == '[ERR: 5001]') {
              console.log('==============> message2 5001', err.errors[0].message, err.errors[0].status);
              this.logoutRedirect();
              return;
          }
      } else if (err.error && err.error.errors && err.error.errors.length && err.error.errors[0] && err.error.errors[0].message) {
          console.log('===============>>> message3', err.error.errors[0], err.error.errors[0].message)
          // alert(err.error.errors[0].message);
          if (err.error.errors[0].status == '[ERR: 5001]') {
              console.log('==============> message2 5001', err.error.errors[0].message, err.error.errors[0].status);
              this.logoutRedirect();
              return;
          }
      } else if (err && err.toString().includes('Unexpected token')) {
          // alert(this.constantService.DATABASE_NOT_ACCESSABLE_UNEXPECTED_TOKEN_MESSAGE);
          // this.logoutRedirect();
      } else if (
          err &&
          (err.indexOf("Failed to fetch") === -1 && err.indexOf("Failed to execute") === -1)
      ) {
          console.log('else:::', err)
          if (err && err.indexOf("Received status code 400") !== -1) {
              console.log("Error: Network error===>>", err);
              // alert("Sorry, Your Token Expired");
              // this.logoutRedirect();
          } else if (err && err.status && err.statusText) {
              alert(err.statusText);
          } else {
              alert(err);
          }
      }
  }
    saveActivity(activityPage: any, activityObj: any) {
      const currentUserObj = JSON.parse(localStorage.getItem("currentUser")!);
      if (currentUserObj) {
        this.userName = currentUserObj.userPrincipalName ? currentUserObj.userPrincipalName : "";
      }
      let obj = {
        activityName: activityObj.activityName ? activityObj.activityName : "",
        activityType: activityObj.activityType ? activityObj.activityType : "",
        activityUser: this.userName,
        activityTime: moment().format(),
        activityDescription: activityObj.activityDescription
          ? activityObj.activityDescription
          : "",
      };
      analytics.page(activityPage);
      analytics.track("List", obj);
    }

    createAuthorizationHeader(headers: HttpHeaders) {
      const token = localStorage.getItem("token");
      headers.append("Authorization", `Bearer ${token}`);
    }

    get(url: any) {
      // let headers = new HttpHeaders({
      //   "Cache-Control": "no-cache",
      //   Pragma: "no-cache",
      //   Expires: -1,
      // });
      // this.createAuthorizationHeader(headers);
      // return (
      //   this.http
      //     .get(url, { headers: headers })
      //     .pipe(map((res: any) => res.json())),
      //   catchError((error: any) => {
      //     //return throwError(new Error(error.status));
      //     return throwError(() => new Error(error.status));
      //   })
      // );
    }

    allS360Jobs(type: any, serverType: any, callback: any) {
      console.log("type:", type);
      let activityData = {
        activityName: "Manage Jobs",
        activityType: "List Jobs",
        activityDescription: "Current Page: " + this.router.url,
      };
      this.saveActivity("Manage Schedule", activityData);
      let commonObject: any = {
        storeGroupList: [],
        storeList: [],
        jobGroupList: [],
      };
      this.loading = true;
      commonObject.storeGroupList = [];
      commonObject.jobGroupList = [];
      const allStoreGroupsList = this.apollo.use("manageJobs").query({
        query: allS360Jobs,
        fetchPolicy: "network-only",
      });

      if (serverType == "test") {
        this.getInputFilesFormockServer(
          environment.mockServerFilepath,
          type,
          (result: number) => {
            callback(result);
          }
        );
        // console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>mockServerData',mockServerData)
      } else {
        allStoreGroupsList.subscribe(
          (listData: any) => {
            console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>listData", listData);
            let obj: any = {};
            // $.each(
            //   listData["data"]["allEtlJobExtractImports"]["edges"],
            //   (key: any, val: any) => {
            //     commonObject.jobGroupList.push(val.node);
            //   }
            // );
            if (statusTestData) {
              $.each(
                listData["data"]["allEtlJobExtractImportIncludeTestData"]["edges"],
                (key: any, val: any) => {
                  commonObject.jobGroupList.push(val.node);
                }
              );
            } else {
              $.each(listData["data"]["allEtlJobExtractImports"]["edges"], (key: any, val: any) => {
                commonObject.jobGroupList.push(val.node);
              });
            }
            console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>1", commonObject);
            for (let i = 0; i < commonObject.jobGroupList.length; i++) {
              // if (commonObject.jobGroupList[i].dmsCode == "CDK3PA") {
                if (commonObject.jobGroupList[i].dealerbuiltSourceId) {
                  // commonObject.jobGroupList[i].dealerbuiltSourceId =
                  //   commonObject.jobGroupList[i].dealerbuiltSourceId
                  //     .split(",")
                  //     .join("-");
                  const originalObject = commonObject.jobGroupList[i];
                  const modifiedObject = { ...originalObject }; // Create a shallow copy
                  modifiedObject.dealerbuiltSourceId =
                    modifiedObject.dealerbuiltSourceId.split(",").join("-");
                  commonObject.jobGroupList[i] = modifiedObject; // Replace the original object with the modified one
                }
              // }
            }
            console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>2", commonObject);
            commonObject.storeGroupList = commonObject.jobGroupList
              .filter(
                (item: any, index: number) =>
                  index < commonObject.jobGroupList.length
              )
              .filter((item: any, index: number) => item.companyName !== null)
              .filter(
                (item: any, index: number) =>
                  item.mageGroupName !== null &&
                  item.thirdPartyUsername !== null &&
                  item.companyName !== null
              );
            if (type) {
              if (type.includes(" / ")) {
                let dmsCode1: any, dmsCode2: any;
                dmsCode1 = type.split(" / ")[0];
                dmsCode2 = type.split(" / ")[1];
                commonObject.storeGroupList = commonObject.storeGroupList.filter(
                  (item: any, index: number) =>
                    item.dmsCode.toLowerCase() === dmsCode1.toLowerCase() ||
                    item.dmsCode.toLowerCase() === dmsCode2.toLowerCase()
                );
              } else {
                commonObject.storeGroupList = commonObject.storeGroupList.filter(
                  (item: any, index: number) =>
                    item.dmsCode.toLowerCase() === type.toLowerCase()
                );
              }
            }
            this.loading = false;
            let groupArray: any[] = [];
            let groupArrayList: any[] = [];
            commonObject.storeGroupList.forEach((data: any) => {
              if (groupArray.indexOf(data.mageGroupName) === -1) {
                groupArrayList.push(data);
                groupArray.push(data.mageGroupName);
              }
            });
            groupArrayList = groupArrayList
              //.filter((item, index) => item.errors == '')
              // .filter((item, index) => (item.etlDms && item.etlDms !== ''));
              .filter((item, index) => item.dmsCode && item.dmsCode !== "");

            //If any error happens then need to uncomment
            // if (type) {
            //     groupArrayList = groupArrayList
            //         // .filter((item, index) => (item.etlDms == type));
            //         .filter((item, index) => (item.dmsCode.toLowerCase() == type.toLowerCase()));
            // }

            for (let i = 0; i < groupArrayList.length; i++) {
              // if (groupArrayList[i].dmsCode == "CDK3PA") {
                if (groupArrayList[i].dealerbuiltSourceId) {
                  groupArrayList[i].dealerbuiltSourceId = groupArrayList[
                    i
                  ].dealerbuiltSourceId
                    .split(",")
                    .join("-");
                }
              // }
            }



            for (let i = 0; i < commonObject.jobGroupList?.length; i++) {
              const jobGroup = commonObject.jobGroupList[i];
            
              if (typeof jobGroup?.mageGroupName) {
                commonObject.jobGroupList[i] = {
                  ...jobGroup,
                  mageGroupName: jobGroup.mageGroupName?.replace(/[^a-zA-Z0-9_]/g, ''),
                };
              }
            
              if (typeof jobGroup?.mageStoreCode) {
                commonObject.jobGroupList[i] = {
                  ...jobGroup,
                  mageStoreCode: jobGroup.mageStoreCode?.replace(/[^a-zA-Z0-9_]/g, ''),
                };
              }
            
              if (commonObject.jobGroupList[i]?.mageStoreCode) {
                commonObject.jobGroupList[i].mageStoreCode = commonObject.jobGroupList[i].mageStoreCode.replace(/[^a-zA-Z0-9_]/g, '');
              }
            }




            commonObject.storeGroupList = groupArrayList;

            this.loading = false;
            if (callback) {
              console.log("commonObject:", commonObject);
              callback(commonObject);
            }
          },
          (err) => {
            this.errorCallback(err, this);
          }
        );
      }

      return allStoreGroupsList;
    }

    getSecondProjectId(companyId: any, callback: any) {
      console.log(" companyId list after common ", companyId);
      try {
        let activityData = {
          activityName: "Manage Jobs",
          activityType: "Get  Second project ID",
          activityDescription: "Current Page: " + this.router.url,
        };
        this.saveActivity("Manage Schedule", activityData);
        let queryName = "getProjectsByCompany";
        const companyList = this.apollo
          .use("manageJobs")
          .query({
            query: getProjectsByCompany,
            fetchPolicy: "network-only",
            variables: {
              inCompanyId: companyId,
            },
          })
          .pipe(takeUntil(this.subscription$))
          .subscribe({
            next: (listdata) => {
              const result: any = listdata;
              const resultObject = JSON.parse(result.data[queryName]);
              if (callback) {
                callback(resultObject);
              }
            },
            error: (err) => {
              callback();
            },
            complete: () => {
              console.log("Completed");
            },
          });
        return companyList;
      } catch (error) {
        console.log(error);
        return callback(error);
      }
    }

    getInputFilesFormockServer(directoryPath: any, type: any, callback: any) {
      console.log("type:", type);
      let activityData = {
        activityName: "Manage Jobs",
        activityType: "List Jobs",
        activityDescription: "Current Page: " + this.router.url,
      };
      this.saveActivity("Manage Schedule", activityData);
      let commonObject: any = {
        storeGroupList: [],
        storeList: [],
        jobGroupList: [],
      };
      this.loading = true;
      commonObject.storeGroupList = [];
      commonObject.jobGroupList = [];
      const allStoreGroupsList = this.apollo.use("manageJobs").query({
        query: allS360Jobs,
        fetchPolicy: "network-only",
      });

      const payload = { folderPath: directoryPath };
      let url = environment.inputFilesForMockServer;
     const token = localStorage.getItem("token");
      // let headers = new HttpHeaders({ "Content-Type": "application/json" });
      // headers.append("Authorization", `Bearer ${token}`);
      const headers = new HttpHeaders({
        authorization: token ? `Bearer ${token}` : "",
      });
      this.http.post(url, payload, { headers: headers }).subscribe((res: any) => {
        // const resData = JSON.parse(res["_body"]);
        const resData = res;
        if (resData.status) {
          console.log("resData::::::::::::::::::::::", resData.data);      
          let listData = resData.data;
         
         

          let obj: any = {};
          // $.each(
          //   listData["data"]["allEtlJobExtractImports"]["edges"],
          //   (key: any, val: any) => {
          //     commonObject.jobGroupList.push(val.node);
          //   }
          // );
          
            $.each(listData["data"]["allEtlJobExtractImports"]["edges"], (key: any, val: any) => {
              commonObject.jobGroupList.push(val.node);
            });
          
          for (let i = 0; i < commonObject.jobGroupList.length; i++) {
            // if (commonObject.jobGroupList[i].dmsCode == "CDK3PA") {
              if (commonObject.jobGroupList[i].dealerbuiltSourceId) {
                commonObject.jobGroupList[i].dealerbuiltSourceId =
                  commonObject.jobGroupList[i].dealerbuiltSourceId
                    .split(",")
                    .join("-");
              }
            // }
          }



          for (let i = 0; i < commonObject.jobGroupList?.length; i++) {
            const jobGroup = commonObject.jobGroupList[i];
          
            if (typeof jobGroup?.mageGroupName) {
              commonObject.jobGroupList[i] = {
                ...jobGroup,
                mageGroupName: jobGroup.mageGroupName?.replace(/[^a-zA-Z0-9_]/g, ''),
              };
            }
          
            if (typeof jobGroup?.mageStoreCode) {
              commonObject.jobGroupList[i] = {
                ...jobGroup,
                mageStoreCode: jobGroup.mageStoreCode?.replace(/[^a-zA-Z0-9_]/g, ''),
              };
            }
          }



          

          commonObject.storeGroupList = commonObject.jobGroupList
            .filter(
              (item: any, index: number) =>
                index < commonObject.jobGroupList.length
            )
            .filter((item: any, index: number) => item.companyName !== null)
            .filter(
              (item: any, index: number) =>
                item.mageGroupName !== null &&
                item.thirdPartyUsername !== null &&
                item.companyName !== null
            );

          if (type) {
            if (type.includes(" / ")) {
              let dmsCode1: any, dmsCode2: any;
              dmsCode1 = type.split(" / ")[0];
              dmsCode2 = type.split(" / ")[1];
              commonObject.storeGroupList = commonObject.storeGroupList.filter(
                (item: any, index: number) =>
                  item.dmsCode.toLowerCase() === dmsCode1.toLowerCase() ||
                  item.dmsCode.toLowerCase() === dmsCode2.toLowerCase()
              );
            } else {
              commonObject.storeGroupList = commonObject.storeGroupList.filter(
                (item: any, index: number) =>
                  item.dmsCode.toLowerCase() === type.toLowerCase()
              );
            }
          }
          this.loading = false;
          let groupArray: any = [];
          let groupArrayList: any[] = [];
          commonObject.storeGroupList.forEach((data: any) => {
            if (groupArray.indexOf(data.mageGroupName) === -1) {
              groupArrayList.push(data);
              groupArray.push(data.mageGroupName);
            }
          });
          groupArrayList = groupArrayList
            //.filter((item, index) => item.errors == '')
            // .filter((item, index) => (item.etlDms && item.etlDms !== ''));
            .filter((item, index) => item.dmsCode && item.dmsCode !== "");

          //If any error happens then need to uncomment
          // if (type) {
          //     groupArrayList = groupArrayList
          //         // .filter((item, index) => (item.etlDms == type));
          //         .filter((item, index) => (item.dmsCode.toLowerCase() == type.toLowerCase()));
          // }

          for (let i = 0; i < groupArrayList.length; i++) {
            // if (groupArrayList[i].dmsCode == "CDK3PA") {
              if (groupArrayList[i].dealerbuiltSourceId) {
                groupArrayList[i].dealerbuiltSourceId = groupArrayList[
                  i
                ].dealerbuiltSourceId
                  .split(",")
                  .join("-");
              }
            // }
          }

          commonObject.storeGroupList = groupArrayList;

          callback(commonObject);

          console.log(
            "DATA FROM SOLVE 360 MOCK SERVER*******************************************",
            commonObject
          );

          // callback(inputFiles);
        } else {
          // callback();
        }
      });
    }

    getCalenderPropertyObject() {
      return {
        useCurrent: true,
        widgetPositioning: {
          horizontal: "left",
        },
        icons: {
          time: "fa fa-clock-o",
          date: "fa fa-calendar",
          up: "fa fa-arrow-up",
          down: "fa fa-arrow-down",
          previous: "fa fa-arrow-left",
          next: "fa fa-arrow-right",
        },
        format: "MM-DD-YYYY",
      };
    }

    /**
   * function to check user group permissions
   */
    checkGroups(callback: (flag: any) => void) {
      const groupList = [
        { menu: "HOME", url: this.constantService.HOME_URL },
  
        { menu: "CDK", url: this.constantService.CDKJOBS_URL },
        { menu: "CF", url: this.constantService.CDKFLEX_URL },
        { menu: "AM", url: this.constantService.AUTOMATE_URL },
        { menu: "DB", url: this.constantService.DEALERBUILT_URL },
        { menu: "DT", url: this.constantService.DEALERTRACK_URL },
        { menu: "AD", url: this.constantService.ADAM_URL },
        { menu: "RY", url: this.constantService.REYNOLDS_URL },
        { menu: "DO", url: this.constantService.DOMINION_URL },
        { menu: "AS", url: this.constantService.AUTOSOFT_URL },
        { menu: "PBS", url: this.constantService.PBS_URL },
        { menu: "UCS", url: this.constantService.UCS_URL },
        { menu: "MPK", url: this.constantService.MPK_URL },
        { menu: "TK", url: this.constantService.TEKION_URL },
        { menu: "QR", url: this.constantService.QUORUM_URL },
        { menu: "TA", url: this.constantService.TEKIONAPI_URL },
        { menu: "MF", url: this.constantService.FORTELLIS_URL },
        { menu: "IS", url: this.constantService.IMPORTSTATUS_URL },
        { menu: "SI", url: this.constantService.SCHEDULERIMPORT_URL },
        { menu: "MS", url: this.constantService.PAYTYPESETTING_URL },
        { menu: "PSU", url: this.constantService.PORTALSTAGEUPDATE },
        { menu: "DF", url: this.constantService.MANAGESCHEDULEFIELD_URL },
      ];
      console.log("this.azureGroup=============+++>groupList",groupList)
      let flag = false;
      const currentMenuItems = JSON.parse(localStorage.getItem("menuItems")!);
      console.log("this.azureGroup=============+++>currentMenuItems",currentMenuItems)
      if (currentMenuItems && currentMenuItems.length > 0) {
        for (let i = 0; i < currentMenuItems.length; i++) {
          for (let j = 0; j < groupList.length; j++) {
            if (currentMenuItems[i].menu === groupList[j].menu) {
              if (this.router.url === groupList[j].url) {
                flag = true;
                this.router.navigate([groupList[j].url]);
                break;
              } else if (currentMenuItems[i].menu == "HOME") {
                const res = this.router.url.split("home");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "CDK") {
                const res = this.router.url.split("ManageCDKJobs");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "CF") {
                const res = this.router.url.split("ManageCDKFlex");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "AM") {
                const res = this.router.url.split("ManageAutoMate");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "DB") {
                const res = this.router.url.split("ManageDealerBuilt");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "DT") {
                const res = this.router.url.split("ManageDealerTrack");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "AD") {
                const res = this.router.url.split("ManageAdam");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "RY") {
                const res = this.router.url.split("ManageReynolds");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "DO") {
                const res = this.router.url.split("ManageDominion");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "AS") {
                const res = this.router.url.split("ManageAutosoft");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              }else if (currentMenuItems[i].menu == "QR") {
                const res = this.router.url.split("ManageQuorum");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              }
              else if (currentMenuItems[i].menu == "TK") {
                const res = this.router.url.split("ManageTekion");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              }
              else if (currentMenuItems[i].menu == "IS") {
                const res = this.router.url.split("ImportStatus");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "SI") {
                const res = this.router.url.split("SchedulerDmsImport");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "SI") {
                const res = this.router.url.split("SchedulerDmsImport/");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "MS") {
                const res = this.router.url.split("ManageSchedule-Setting");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              } else if (currentMenuItems[i].menu == "PSU") {
                const res = this.router.url.split("/StageUpdatePortal");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              }
              else if (currentMenuItems[i].menu == "DF") {
                const res = this.router.url.split("/ManageScheduleFields");
                if (res.length > 1) {
                  flag = true;
                  this.router.navigate([this.router.url]);
                  break;
                }
              }   
            }
          }
        }
        if (!flag) {
          for (let j = 0; j < groupList.length; j++) {
            if (groupList[j].menu == currentMenuItems[0].menu) {
              this.router.navigate([groupList[j].url]);
            }
          }
        }
        if (callback) {
          callback(flag);
        }
      } else {
        this.authService.logout();
        localStorage.removeItem("currentUser");
        localStorage.removeItem("token");
        localStorage.removeItem("menuItems");
      }
    }
  
    getJwtToken(callback: any) {
      console.log("authenticated started");
      if (this.authService.instance.getAllAccounts().length > 0) {
        console.log("authenticated true");
        this.silentLogin(callback);
      } else {
        console.log("authenticated false");
        if (callback) {
          callback(false);
        }
      }
    }
  
    silentLogin(callback: any) {
      console.log("silentLogin started");
      const account = this.authService.instance.getAllAccounts()[0];
      const accessTokenRequest = {
        scopes: ["user.read", "openid", "profile"],
        account: account,
      };
      this.authService.instance
        .acquireTokenSilent(accessTokenRequest)
        .then((result:any) => {
          console.log("acquireTokenSilent=========================>>", result);
          const token = result.idToken;
          console.log("Id Token silent=========================>>", token);
          localStorage.setItem("token", token);
          console.log("silent token =====>>>", localStorage.getItem("token"));
          const activityData2 = {
            activityName: "save token",
            activityDescription: "acquireTokenSilent getJwtToken",
            activityData: {
              token: localStorage.getItem("token"),
            },
          };
          this.saveActivity("getJwtToken", activityData2);
          if (callback) {
            callback(true);
          }
        })
        .catch((error:any) => {
          console.log("acquireTokenSilent Error======>>", error);
          if (callback) {
            callback(false);
          }
        });
    }
    /**
     *  function to get user accessible menu list
     */
  
    getGroups(callback: any) {
      console.log("getGroups started");
  
      if (localStorage.getItem("menuItems")) {
        const currentMenuItems = JSON.parse(
          localStorage.getItem("menuItems") || ""
        );
        this.azureGroup = currentMenuItems;
  
        if (!this.azureGroup.length) {
          this.router.navigate(["/access-denied"]);
        }
      this.getReleaseDetails(() => {
        console.log("Release Version if");
        if (callback) {
          callback(this.azureGroup);
        }
      });
      } else if (localStorage.getItem("token")){
        const res = this.getMenuItemAuth(
          environment.redirectUri + "scheduler/auth"
        );
        res.subscribe((result) => {
          if (result["data"] && result["data"]["getUserMenuInfo"]) {
            const temp = JSON.parse(result["data"]["getUserMenuInfo"]);
            if (temp) {
              this.azureGroup = [];
              for (let i = 0; i < temp.length; i++) {
                this.azureGroup.push(temp[i]);
              }
            }
          }
          console.log("this.azureGroup=============+++>",this.azureGroup)
          if (this.azureGroup.length > 0) {
            localStorage.setItem("menuItems", JSON.stringify(this.azureGroup));
            this.getReleaseDetails(() => {
            console.log("Release Version else");
            if (callback) {
              callback(this.azureGroup);
            }
          	});
          } else {
            this.router.navigate(["/access-denied"]);
          }
        });
      }
    }
  
    getMenuItemAuth(url: string) {
      const token = localStorage.getItem("token");
      const authenticationHeader = new HttpHeaders({
        authorization: token ? `Bearer ${token}` : "",
        'Cache-Control': 'no-cache, no-store'
      });
      return this.http
        .get(url, {
          headers: authenticationHeader,
        })
        .pipe(map((res: any) => res))
        .pipe(
          catchError((error: any) => {
            console.log("errr", error);
            if (error.status === 502) {
              const activityData1 = {
                activityName: "502 Bad Gateway",
                activityDescription: "Current Page: " + this.router.url,
                activityData: error,
              };
              this.saveActivity("DB Bad Gateway", activityData1);
              // alert(this.constantService.DATABASE_NOT_ACCESSABLE_MESSAGE);
              //this.logoutRedirect();
            } else {
              const activityData1 = {
                activityName: "DB Error",
                activityDescription: "Current Page: " + this.router.url,
                activityData: error,
              };
              this.saveActivity("DB Error", activityData1);
              // alert(JSON.parse(error._body).errors[0].message);
              // this.logoutRedirect();
            }
            this.errorCallback(error, this);
            return throwError(() => error.status);
          })
        );
    }
    logout() {
      localStorage.removeItem("currentUser");
      localStorage.removeItem("token");
      localStorage.removeItem("menuItems");
    }
  
    logoutRedirect() {
      console.log("LOGOUT REDIRECT...........................");
      console.log("Storage clear......");
      const previousUrl = localStorage.getItem("previousUrl");
      localStorage.removeItem("currentUser");
      localStorage.removeItem("token");
      localStorage.removeItem("userName");
      localStorage.removeItem('menuItems');
      if (previousUrl) {
        localStorage.setItem("previousUrl", previousUrl);
      }
      this.authService.loginRedirect();
    }
    hardRefresh() {
      window.location.reload();
    }

    getReleaseDetails(callback:any) {
      const activityData = {
        activityName: " fetch allReleaseDetails",
        activityDescription: "Current Page: " + this.router.url,
      };
      this.saveActivity(this.router.url, activityData);
      const allReleaseDetailsList = this.apollo.use("manageScheduler").query({
        query: allReleaseDetails,
        fetchPolicy: "network-only",
      });
      allReleaseDetailsList.subscribe(
        (listData:any) => {
          const releaseList = listData["data"]["allReleaseDetails"]["edges"];
          const latestVersion = releaseList?.length ? releaseList[0].node["versionNo"] : "";
          const currentVersion = this.constantService.releaseVerion;
          const versionMismatch = latestVersion != currentVersion ? true : false;
          const verObj = {
            currentVersion: currentVersion,
            latestVersion: latestVersion,
            versionMismatch: versionMismatch,
          };
          console.log("Release Version", verObj);
          if (callback) {
            callback(verObj);
          }
          const activityData1 = {
            activityName: " fetch allReleaseDetails successfully",
            activityDescription: "Current Page: " + this.router.url,
          };
          this.saveActivity(this.router.url, activityData1);
        },
        (err) => {
          const activityData2 = {
            activityName: "allReleaseDetails error",
            activityDescription: "Current Page: " + this.router.url,
          };
          if (callback) {
            callback(null);
          }
          this.saveActivity(this.router.url, activityData2);
          this.errorCallback(err, this);
        }
      );
    }
}

<nav class="cat__menu-left">
  <div class="cat__menu-left__lock cat__menu-left__action--menu-toggle">
      <div class="cat__menu-left__pin-button">
          <div>
              <!-- -->
          </div>
      </div>
  </div>
  <div class="cat__menu-left__logo">
      <a>
          <img alt="" src="assets/modules/dummy-assets/common/img/Armatus-logo.svg" />
      </a>
  </div>
  <div class="pull-right text-success" style="
    font-size: smaller;
    font-style: italic;
    font-weight: 700;
    margin-right: 10px;
  ">
      {{ RELEASE_VERSION }}
  </div>
  <div class="cat__menu-left__inner" *ngIf="loadMenulistFlag">
      <ul class="cat__menu-left__list cat__menu-left__list--root">
          <div>
              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'HOME'" class="cat__menu-left__item dashboard" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="HOME_URL">
                          <span class="cat__menu-left__icon icmn-home"></span> Home
                      </a>
                  </li>
              </div>
              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'CDK'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="CDKJOBS_URL">
                          <span class="cat__menu-left__icon">CDK</span> Manage CDK Jobs
                      </a>
                  </li>
              </div>

              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'CF'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="CDKFLEX_URL">
                          <span class="cat__menu-left__icon">CF</span> Manage CDK Flex Jobs
                      </a>
                  </li>
              </div>

              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'AM'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="AUTOMATE_URL">
                          <span class="cat__menu-left__icon">AM</span> Manage Automate Jobs
                      </a>
                  </li>
              </div>

              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'DB'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="DEALERBUILT_URL">
                          <span class="cat__menu-left__icon">DB</span> Manage DealerBuilt Jobs
                      </a>
                  </li>
              </div>

              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'DT'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="DEALERTRACK_URL">
                          <span class="cat__menu-left__icon">DT</span> Manage DealerTrack Jobs
                      </a>
                  </li>
              </div>

              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'AD'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="ADAM_URL">
                          <span class="cat__menu-left__icon">AD</span> Manage Adam Jobs
                      </a>
                  </li>
              </div>

              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'RY'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="REYNOLDS_URL">
                          <span class="cat__menu-left__icon">RY</span> Manage ReynoldsRCI Jobs
                      </a>
                  </li>
              </div>

              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'DO'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="DOMINION_URL">
                          <span class="cat__menu-left__icon">DO</span> Manage Dominion Jobs
                      </a>
                  </li>
              </div>

              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'AS'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="AUTOSOFT_URL">
                          <span class="cat__menu-left__icon">AS</span> Manage AutoSoft Jobs
                      </a>
                  </li>
              </div>
              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'PBS'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="PBS_URL">
                          <span class="cat__menu-left__icon">PBS</span> Manage PBS Jobs
                      </a>
                  </li>
              </div>
              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'QR'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="QUORUM_URL">
                          <span class="cat__menu-left__icon">QR</span> Manage Quorum Jobs
                      </a>
                  </li>
              </div>
              <!-- <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'UCS'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="UCS_URL">
                          <span class="cat__menu-left__icon">UCS</span> Manage UCS Jobs
                      </a>
                  </li>
              </div> -->
              <div *ngFor="let data of menulist; let i = index">
                  <li *ngIf="data.menu === 'TK'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                      <a [routerLink]="TEKION_URL">
                          <span class="cat__menu-left__icon">TK</span> Manage Tekion Jobs
                      </a>
                  </li>
              </div>
              <div *ngFor="let data of menulist; let i = index">
                <li *ngIf="data.menu === 'TA'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                    <a [routerLink]="TEKIONAPI_URL">
                        <span class="cat__menu-left__icon">TA</span> Manage Tekion API Jobs
                    </a>
                </li>
            </div>

            <div *ngFor="let data of menulist; let i = index">
                <li *ngIf="data.menu === 'MF'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                    <a [routerLink]="FORTELLIS_URL">
                        <span class="cat__menu-left__icon">FOR</span> Manage Fortellis Jobs
                    </a>
                </li>
            </div>            
                <div *ngFor="let data of menulist; let i = index">
                    <li *ngIf="data.menu === 'MPK'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="MPK_URL">
                            <span class="cat__menu-left__icon">MPK</span> Manage Mpk Jobs
                        </a>
                    </li>
                </div>                           
                <div *ngFor="let data of menulist; let i = index">
                    <li *ngIf="data.menu === 'SI'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="SCHEDULERIMPORT_URL">
                            <span class="cat__menu-left__icon">SI</span> Scheduler DMS Import
                        </a>
                    </li>
                </div>
                <div *ngFor="let data of menulist; let i = index">
                    <li *ngIf="data.menu === 'MS'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="PAYTYPESETTING_URL">
                            <span class="cat__menu-left__icon">MS</span>Manage Schedule Settings
                        </a>
                    </li>
                </div>
                <div *ngFor="let data of menulist; let i = index">
                    <li *ngIf="data.menu === 'DF'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="MANAGESCHEDULEFIELD_URL">
                            <span class="cat__menu-left__icon">DF</span>Manage Schedule DMS Fields
                        </a>
                    </li>
                </div>
            </div>
        </ul>
  </div>
  <div *ngIf="!loadMenulistFlag">
    <div style="text-align: right;padding-top: 50px;padding-right: 7px;">
        <em class="fa fa-spinner fa-pulse fa-2x fa-fw"> </em
        ><span class="sr-only">Loading...</span>
    </div>
  </div>
</nav>

import { Component, OnInit,<PERSON><PERSON><PERSON>, ChangeDetectorRef,inject,ChangeDetectionStrategy
} from "@angular/core";
import { Router ,ActivatedRoute,} from "@angular/router";
import { ConstantService } from "../../constants/constant.service";
import { CommonService } from "../../services/common.service";
import { environment } from "./../../../../environments/environment";
import { HttpClient } from "@angular/common/http";
import { CommonModule } from '@angular/common';
 import { RouterLink,RouterLinkActive,RouterOutlet} from '@angular/router';

declare var analytics: any;
declare var $: any;
declare var jQuery: any;

@Component({
  selector: "app-menu-left",
  templateUrl: "./menu-left-vertical.component.html",
  standalone: true,
  imports: [CommonModule, RouterLink, RouterLinkActive, RouterOutlet],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ConstantService, CommonService,],
})
export class MenuLeftComponent implements OnInit {
  MAGEURL: any;
  SOLVE360: any;
  public HOME_URL: any;
  public RELEASE_VERSION: any;
  public CDKJOBS_URL: any;
  public CDKFLEX_URL: any;
  public AUTOMATE_URL: any;
  public DEALERBUILT_URL: any;
  public DEALERTRACK_URL: any;
  public ADAM_URL: any;
  public REYNOLDS_URL: any;
  public DOMINION_URL: any;
  public AUTOSOFT_URL: any;
  public PBS_URL: any;
  public QUORUM_URL: any;
  public UCS_URL: any;
  public TEKION_URL: any;
  public MPK_URL: any;
  public TEKIONAPI_URL: any;
  public FORTELLIS_URL: any;
  public IMPORTSTATUS_URL: any;
  public SCHEDULERIMPORT_URL: any;
  public PAYTYPESETTING_URL: any;
  public MANAGESCHEDULEFIELD_URL: any;
  public environmentName = environment.envName;
  azureGroup: any[] = [];
  public loadMenulistFlag = false;
  constructor(
    private http: HttpClient,
    private constantService: ConstantService,
    public commonService: CommonService,
    private router: Router,
    private ngZone: NgZone,
    public changeDetectorRef: ChangeDetectorRef,
  ) {}
  activeRoute :ActivatedRoute = inject(ActivatedRoute)
  ngOnInit() {
    console.log('Query Parameter:',  this.activeRoute.snapshot.data);
    this.activeRoute.snapshot.data['azureGroup']
    console.log("menuleft............");
    this.loadMenulistFlag = false;

    this.RELEASE_VERSION = this.constantService.releaseVerion;
    this.CDKJOBS_URL = this.constantService.CDKJOBS_URL;
    this.CDKFLEX_URL = this.constantService.CDKFLEX_URL;
    this.AUTOMATE_URL = this.constantService.AUTOMATE_URL;
    this.DEALERBUILT_URL = this.constantService.DEALERBUILT_URL;
    this.DEALERTRACK_URL = this.constantService.DEALERTRACK_URL;
    this.ADAM_URL = this.constantService.ADAM_URL;
    this.REYNOLDS_URL = this.constantService.REYNOLDS_URL;
    this.DOMINION_URL = this.constantService.DOMINION_URL;
    this.AUTOSOFT_URL = this.constantService.AUTOSOFT_URL;
    this.PBS_URL =  this.constantService.PBS_URL;
    this.QUORUM_URL =  this.constantService.QUORUM_URL;
    this.UCS_URL =  this.constantService.UCS_URL;
    this.TEKION_URL =  this.constantService.TEKION_URL;
    this.MPK_URL =  this.constantService.MPK_URL;
    this.HOME_URL = this.constantService.HOME_URL;
    this.TEKIONAPI_URL =  this.constantService.TEKIONAPI_URL;
    this.FORTELLIS_URL = this.constantService.FORTELLIS_URL;
    this.IMPORTSTATUS_URL = this.constantService.IMPORTSTATUS_URL;
    this.SCHEDULERIMPORT_URL = this.constantService.SCHEDULERIMPORT_URL;
    this.PAYTYPESETTING_URL = this.constantService.PAYTYPESETTING_URL;
    this.MANAGESCHEDULEFIELD_URL = this.constantService.MANAGESCHEDULEFIELD_URL;
    this.changeDetectorRefCheck();

    this.ngZone.run(() => {
     setTimeout(async () => {
        this.commonService.getJwtToken((status: any) => {
            console.log('getJwtToken Response status==>>', status);
            this.commonService.getGroups((azureGroup: any) => {
                /**
                 *  set cookie and expiry-time
                 */
                setInterval(setExpiry, 60 * 1000);
                function setExpiry() {
                    const expirydate = new Date();
                    expirydate.setMinutes(expirydate.getMinutes() + 2);
                    document.cookie = "username=login; expires=" + expirydate.toUTCString();
                }
                this.azureGroup = azureGroup;
                setTimeout(() => {
                  const currentUser = localStorage.getItem("currentUser") ? JSON.parse(localStorage.getItem("currentUser")!) : "";
                  let userName = "";
                  if (currentUser) {
                      if (currentUser && currentUser.displayName) {
                          userName = currentUser.displayName;
                      }
                      analytics.identify(userName, {
                          name: userName,
                          email: currentUser.userPrincipalName,
                      });
                  }
                  this.loadMenulistFlag = true;
                  this.changeDetectorRefCheck();
                  console.log('Leftmenu list azureGroup =====>>>', azureGroup, this.loadMenulistFlag);
                  this.init();
                }, 500);
            }); 
        });  
     }, 1000);
    });
  }

  init(){
    $(() => {
      // scripts for "menu-left" module
      /////////////////////////////////////////////////////////////////////////////////////////
      // add backdrop

      $(".cat__menu-left").after(
        '<div class="cat__menu-left__backdrop cat__menu-left__action--backdrop-toggle"><!-- --></div>'
      );
      /////////////////////////////////////////////////////////////////////////////////////////
      // submenu
      $(".cat__menu-left__submenu").on("click", function () {
        const active = $(".cat__menu-left__item");
        if (active.hasClass("cat__menu-left__item--active")) {
          active.removeClass("cat__menu-left__item--active");
        }
      });

      $(".dashboard").on("click", () => {
        const dashboard = $(".dashboard");
        if (!dashboard.hasClass("cat__menu-left__item--active")) {
          dashboard.addClass("cat__menu-left__item--active");
        }
        if (
          $("body").hasClass("cat__theme--light") ||
          $("body").width() < 768
        ) {
          const parent = $(this).parent(),
            opened = $(".cat__menu-left__submenu--toggled");

          if (
            !parent.hasClass("cat__menu-left__submenu--toggled") &&
            !parent.parent().closest(".cat__menu-left__submenu").length
          ) {
            opened
              .removeClass("cat__menu-left__submenu--toggled")
              .find("> .cat__menu-left__list")
              .slideUp(200);
          }
          parent.toggleClass("cat__menu-left__submenu--toggled");
          parent.find("> .cat__menu-left__list").slideToggle(200);
        }
      });

      $(".cat__menu-left__submenu > a").on("click", () => {
        if (
          $("body").hasClass("cat__theme--light") ||
          $("body").width() < 768
        ) {
          const parent = $(this).parent(),
            opened = $(".cat__menu-left__submenu--toggled");
          if (
            !parent.hasClass("cat__menu-left__submenu--toggled") &&
            !parent.parent().closest(".cat__menu-left__submenu").length
          ) {
            opened
              .removeClass("cat__menu-left__submenu--toggled")
              .find("> .cat__menu-left__list")
              .slideUp(200);
          }
          parent.toggleClass("cat__menu-left__submenu--toggled");
          parent.find("> .cat__menu-left__list").slideToggle(200);
        }
      });

      /////////////////////////////////////////////////////////////////////////////////////////
      // custom scroll init

      // if ($("body").hasClass("cat__theme--light")) {
      //   if (!/Mobi/.test(navigator.userAgent) && jQuery().jScrollPane) {
      //     $(".cat__menu-left__inner").each(() => {
      //       $(this).jScrollPane({
      //         contentWidth: "0px",
      //         autoReinitialise: false,
      //         autoReinitialiseDelay: 100,
      //       });
      //       const api = $(this).data("jsp");
      //       let throttleTimeout: any;
      //       $(window).bind("resize", function () {
      //         if (!throttleTimeout) {
      //           throttleTimeout = setTimeout(function () {
      //             api.reinitialise();
      //             throttleTimeout = null;
      //           }, 50);
      //         }
      //       });
      //     });
      //   }
      // }
      if ($("body").hasClass("cat__theme--light")) {
        if (!/Mobi/.test(navigator.userAgent) && jQuery().jScrollPane) {
          $(".cat__menu-left__inner").each(function (this: any) {
            $(this).jScrollPane({
              contentWidth: "0px",
              autoReinitialise: false,
              autoReinitialiseDelay: 100,
            });
            const api = $(this).data("jsp");
            let throttleTimeout: any;
            $(window).bind("resize", function () {
              if (!throttleTimeout) {
                throttleTimeout = setTimeout(function () {
                  api.reinitialise();
                  throttleTimeout = null;
                }, 50);
              }
            });
          });
        }
      }
      /////////////////////////////////////////////////////////////////////////////////////////
      // toggle menu
      $(".cat__menu-left__action--menu-toggle").on("click", function () {
        if ($("body").width() < 768) {
          $("body").toggleClass("cat__menu-left--visible--mobile");
        } else {
          $("body").toggleClass("cat__menu-left--visible");
          $(".breadcrumb").toggleClass("breadcrumb_resize");
        }
        $("#dueDateReportTable").DataTable().columns.adjust().draw(false);
        $("#storeDetailsTable").DataTable().columns.adjust().draw(false);
        $("#controlPanelStoreTable").DataTable().columns.adjust().draw(false);
        $("#souceBundlesTable").DataTable().columns.adjust().draw(false);
        $("#snapshotDetailsTable").DataTable().columns.adjust().draw(false);
        if (
          navigator.userAgent.indexOf("MSIE") !== -1 ||
          navigator.appVersion.indexOf("Trident/") > 0
        ) {
          const evt = document.createEvent("UIEvents");
          evt.initUIEvent("resize", true, false, window, 0);
          window.dispatchEvent(evt);
        } else {
          window.dispatchEvent(new Event("resize"));
        }
      });

      $(".cat__menu-left__action--backdrop-toggle").on("click", function () {
        $("body").removeClass("cat__menu-left--visible--mobile");
      });

      /////////////////////////////////////////////////////////////////////////////////////////
      // colorful menu
      let colorfulClasses =
        "cat__menu-left--colorful--primary cat__menu-left--colorful--secondary cat__menu-left--colorful--primary ";
      colorfulClasses +=
        "cat__menu-left--colorful--default cat__menu-left--colorful--info cat__menu-left--colorful--success ";
      colorfulClasses +=
        "cat__menu-left--colorful--warning cat__menu-left--colorful--danger cat__menu-left--colorful--yellow";
      const colorfulClassesArray = colorfulClasses.split(" ");

      const setColorfulClasses = () => {
        $(".cat__menu-left__list--root > .cat__menu-left__item").each(() => {
          const randomClass =
            colorfulClassesArray[
              Math.floor(Math.random() * colorfulClassesArray.length)
            ];
          $(this).addClass(randomClass);
        });
      };

      function removeColorfulClasses() {
        $(".cat__menu-left__list--root > .cat__menu-left__item").removeClass(
          colorfulClasses
        );
      }

      if ($("body").hasClass("cat__menu-left--colorful")) {
        setColorfulClasses();
      }

      $("body").on("setColorfulClasses", function () {
        setColorfulClasses();
      });

      $("body").on("removeColorfulClasses", function () {
        removeColorfulClasses();
      });
    });
  }

  get menulist(){
    return this.azureGroup;
  }
  changeDetectorRefCheck() {
    this.changeDetectorRef.markForCheck();
  }
}

import { Component, OnInit } from "@angular/core";
import { RouterLink } from "@angular/router";
import { SubscriptionConstantService } from "./../../constants/subscription.constant.service";
import { EventEmitterService } from "./../../services/event.emitter.services";
import { ConstantService } from "../../constants/constant.service";
import { MsalService } from "@azure/msal-angular";
import { NgIf } from "@angular/common";
import { CommonService } from "../../services/common.service";

@Component({
  selector: "app-top-bar",
  templateUrl: "./top-bar.component.html",
  standalone: true,
  imports: [NgIf,RouterLink],
})
export class TopBarComponent {
  given_name: string = "";
  public loaderText!: string;
  public screenType!: string;
  public subscriptionError!: boolean;
  public displayNotification: boolean = false;
  public HOME_URL: any;
  constructor(
    private EventEmitterService: EventEmitterService,
    public SubscriptionConstantService: SubscriptionConstantService,
    private constantService: ConstantService ,
    private authService: MsalService,
    public commonService:CommonService
  ) {
    /**
     * Event Emitter Subscribe Section - Display Progress Overlay
     */
   
  }

  ngOnInit() {
   
    // this.commonService.getReleaseDetails((res:any) => {
    //   if (res) {
    //     this.versionMismatch = res["versionMismatch"];
    //   }
    // });

    this.HOME_URL = this.constantService.HOME_URL;
    this.EventEmitterService.displayProgress.subscribe(
      (displayProgress: any) => {
        if (displayProgress) {
          this.loaderText = displayProgress;
          this.SubscriptionConstantService.LOADER_STATUS = true;
        } else {
          this.SubscriptionConstantService.LOADER_STATUS = false;
        }
      }
    );
    console.log("inIt...........");    
  }


  public get versionMismatch() {
    return localStorage.getItem("versionMismatch") ? localStorage.getItem("versionMismatch") : null;
  }

  /**
   * Event emitter - Refresh Page
   */
  refresh() {
    this.displayNotification = false;
  }

  logout() {
    this.authService.logoutRedirect();
    localStorage.removeItem("currentUser");
    localStorage.removeItem("token");
    localStorage.removeItem("menuItems");
    localStorage.removeItem('userName');
  }

  public get userName() {
    //  console.log("User Name............");    
      return localStorage.getItem('userName')? localStorage.getItem('userName'): null;
    }
    
}


import { NgModule } from "@angular/core";
import { CommonService } from "./structure/services/common.service";
import { environment } from "src/environments/environment";

import { ApolloModule, Apollo } from "apollo-angular";
import {
  InMemoryCache,
  split,
  ApolloLink,
  createHttpLink,
} from "@apollo/client/core";
import { onError } from "@apollo/client/link/error";
import { getMainDefinition } from "apollo-utilities";

let authorizationClient: any;
@NgModule({
  exports: [ApolloModule],
  providers: [CommonService],
})
export class GraphQLModule {
  constructor(apollo: Apollo, public commonService: CommonService) {
    const error = onError(({ graphQLErrors, networkError }) => {
      if (graphQLErrors) {
        this.commonService.errorCallback(graphQLErrors, this);
      }
      if (networkError) {
        this.commonService.errorCallback(networkError.toString(), this);
      }
    });
    const middleware = new ApolloLink((operation: any, forward: any) => {
      if (!localStorage.getItem("token")) {
        location.href = environment.schedulerUrl + "/#/login";
        location.reload();
        return;
      }
      const token = localStorage.getItem("token");
      operation.setContext({
        headers: {
          authorization: token ? `Bearer ${token}` : "",
        },
      });
      return forward(operation);
    });

    const scheduler_middleware = new ApolloLink(
      (operation: any, forward: any) => {
        if (!localStorage.getItem("token")) {
          location.href = environment.schedulerCustomUrl + "/#/login";
          location.reload();
          return;
        }
        const token = localStorage.getItem("token");
        operation.setContext({
          headers: {
            authorization: token ? `Bearer ${token}` : "",
          },
        });
        return forward(operation);
      }
    );

    const httpScheduler = createHttpLink({
      uri: environment.schedulerCustomUrl,
      fetch: fetch,
    });
    const link1 = scheduler_middleware.concat(httpScheduler);
    const schedulerLink = error.concat(link1);
    const scheduleauthclientlink = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, schedulerLink);
    authorizationClient = apollo.createNamed("manageScheduler", {
      link: scheduleauthclientlink,
      cache: new InMemoryCache(),
    });

    const http = createHttpLink({ uri: environment.jobListUrl, fetch: fetch });
    const link = middleware.concat(http);
    const jobListLink = error.concat(link);
    const authclientlink = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, jobListLink);
    authorizationClient = apollo.createNamed("manageJobs", {
      link: authclientlink,
      cache: new InMemoryCache(),
    });

    const portalHttp = createHttpLink({ uri: environment.portalListUrl, fetch: fetch });
    const portalLink = middleware.concat(portalHttp);
    const groupListLink = error.concat(portalLink);
    const portalAuthclientlink = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, groupListLink);
    authorizationClient = apollo.createNamed("manageGroups", {
      link: portalAuthclientlink,
      cache: new InMemoryCache(),
    });
    const duportalHttp = createHttpLink({ uri: environment.duportalListUrl, fetch: fetch });
    const duportalLink = middleware.concat(duportalHttp);
    const storeListLink = error.concat(duportalLink);
    const duportalAuthclientlink = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, storeListLink);
    authorizationClient = apollo.createNamed("duportal", {
      link: duportalAuthclientlink,
      cache: new InMemoryCache(),
    });

    const authHttpClientScheduler = createHttpLink({
      uri: environment.schedulerUrl,
      fetch: fetch,
    });
    const linkManageSchedule = middleware.concat(authHttpClientScheduler);
    const manageScheduleLink = error.concat(linkManageSchedule);
    const ManageSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, manageScheduleLink);
    authorizationClient = apollo.createNamed("manageSchedule", {
      link: ManageSchedule,
      cache: new InMemoryCache(),
    });

    const cdkFlexClientScheduler = createHttpLink({
      uri: environment.cdkFlexSchedulerUrl,
      fetch: fetch,
    });
    const linkCDKFlex = middleware.concat(cdkFlexClientScheduler);
    const manageCDKFlexLink = error.concat(linkCDKFlex);
    const CDKFlexSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, manageCDKFlexLink);
    authorizationClient = apollo.createNamed("manageCDKFLEXSchedule", {
      link: CDKFlexSchedule,
      cache: new InMemoryCache(),
    });

    const automateClientScheduler = createHttpLink({
      uri: environment.autoMateSchedulerUrl,
      fetch: fetch,
    });
    const linkAutomate = middleware.concat(automateClientScheduler);
    const manageAutomateLink = error.concat(linkAutomate);
    const AutomateSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, manageAutomateLink);
    authorizationClient = apollo.createNamed("manageAutomateSchedule", {
      link: AutomateSchedule,
      cache: new InMemoryCache(),
    });

    const dealerBuiltClientScheduler = createHttpLink({
      uri: environment.dealerBuiltSchedulerUrl,
      fetch: fetch,
    });
    const linkDealerBuilt = middleware.concat(dealerBuiltClientScheduler);
    const manageDealerBuiltLink = error.concat(linkDealerBuilt);
    const DealerBuiltSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, manageDealerBuiltLink);
    authorizationClient = apollo.createNamed("manageDealerBuiltSchedule", {
      link: DealerBuiltSchedule,
      cache: new InMemoryCache(),
    });

    const dealerTrackClientScheduler = createHttpLink({
      uri: environment.dealerTrackSchedulerUrl,
      fetch: fetch,
    });
    const linkDealerTrack = middleware.concat(dealerTrackClientScheduler);
    const manageDealerTrackLink = error.concat(linkDealerTrack);
    const DealerTrackSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, manageDealerTrackLink);
    authorizationClient = apollo.createNamed("manageDealerTrackSchedule", {
      link: DealerTrackSchedule,
      cache: new InMemoryCache(),
    });

    const adamClientScheduler = createHttpLink({
      uri: environment.adamSchedulerUrl,
      fetch: fetch,
    });
    const linkAdam = middleware.concat(adamClientScheduler);
    const manageAdamLink = error.concat(linkAdam);
    const AdamTrackSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, manageAdamLink);
    authorizationClient = apollo.createNamed("manageAdamSchedule", {
      link: AdamTrackSchedule,
      cache: new InMemoryCache(),
    });
    
const scheduleAddFieldsLink = createHttpLink({
  uri: environment.scheduleAddFieldUrl, // Ensure this URL is in your environment configuration
  fetch: fetch,
});
const AddFieldsLink = middleware.concat(error).concat(scheduleAddFieldsLink);
const ScheduleAddField = split(
  ({ query }) => {
    const definition = getMainDefinition(query);
    return definition.kind === 'OperationDefinition';
  },AddFieldsLink);
authorizationClient = apollo.createNamed("manageScheduleAddFields", {
  link: ScheduleAddField,
  cache: new InMemoryCache(),
});
    const reynoldsClientScheduler = createHttpLink({
      uri: environment.reynoldsSchedulerUrl,
      fetch: fetch,
    });
    const linkReynolds = middleware.concat(reynoldsClientScheduler);
    const manageReynoldsLink = error.concat(linkReynolds);
    const ReynoldsTrackSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, manageReynoldsLink);
    authorizationClient = apollo.createNamed("manageReynoldsSchedule", {
      link: ReynoldsTrackSchedule,
      cache: new InMemoryCache(),
    });

    const dominionClientScheduler = createHttpLink({
      uri: environment.dominionSchedulerUrl,
      fetch: fetch,
    });
    const linkDominion = middleware.concat(dominionClientScheduler);
    const manageDominionLink = error.concat(linkDominion);
    const DominionTrackSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, manageDominionLink);
    authorizationClient = apollo.createNamed("manageDominionSchedule", {
      link: DominionTrackSchedule,
      cache: new InMemoryCache(),
    });
    const autosoftClientScheduler = createHttpLink({
      uri: environment.autosoftSchedulerUrl,
      fetch: fetch,
    });
    const linkAutosoft = middleware.concat(autosoftClientScheduler);
    const manageAutosoftLink = error.concat(linkAutosoft);
    const AutosoftTrackSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, manageAutosoftLink);
    authorizationClient = apollo.createNamed("manageAutosoftSchedule", {
      link: AutosoftTrackSchedule,
      cache: new InMemoryCache(),
    });
    const tekionClientScheduler = createHttpLink({
      uri: environment.tekionSchedulerUrl,
      fetch: fetch,
    });

    const linkTekion = middleware.concat(tekionClientScheduler);
    const managetekionLink = error.concat(linkTekion);
    const TekionSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, managetekionLink);
    authorizationClient = apollo.createNamed("manageTekionSchedule", {
      link: TekionSchedule,
      cache: new InMemoryCache(),
    });

    const quorumClientScheduler = createHttpLink({
      uri: environment.quorumSchedulerUrl,
      fetch: fetch,
    });

    const linkQuorum = middleware.concat(quorumClientScheduler);
    const managequorumLink = error.concat(linkQuorum);
    const QuorumSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, managequorumLink);
    authorizationClient = apollo.createNamed("manageQuorumSchedule", {
      link: QuorumSchedule,
      cache: new InMemoryCache(),
    });

    const pbsClientScheduler = createHttpLink({
      uri: environment.pbsSchedulerUrl,
      fetch: fetch,
    });

    const linkPbs = middleware.concat(pbsClientScheduler);
    const managepbsLink = error.concat(linkPbs);
    const PbsSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, managepbsLink);
    authorizationClient = apollo.createNamed("managePbsSchedule", {
      link: PbsSchedule,
      cache: new InMemoryCache(),
    });
    const tekionapiClientScheduler = createHttpLink({
      uri: environment.tekionApiSchedulerUrl,
      fetch: fetch,
    });

    const linkTekionapi = middleware.concat(tekionapiClientScheduler);
    const manageTekionapiLink = error.concat(linkTekionapi);
    const TekionapiSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, manageTekionapiLink);
    authorizationClient = apollo.createNamed("manageTekionapiSchedule", {
      link: TekionapiSchedule,
      cache: new InMemoryCache(),
    });  

    const fortellisClientScheduler = createHttpLink({
      uri: environment.fortellisSchedulerUrl,
      fetch: fetch,
    });
    const linkFortellis = middleware.concat(fortellisClientScheduler);
    const manageFortellisLink = error.concat(linkFortellis);
    const FortellisSchedule = split(({ query }) => {
      let definition = getMainDefinition(query);
      return definition.kind === "OperationDefinition";
    }, manageFortellisLink);
    authorizationClient = apollo.createNamed("manageFortellisSchedule", {
      link: FortellisSchedule,
      cache: new InMemoryCache(),
    }); 

  }
}
export function provideClients() {
  return {
    authorizationClient: authorizationClient,
  };
}

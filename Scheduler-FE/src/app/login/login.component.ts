// import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
// import { Router } from "@angular/router";
// import { ActivatedRoute } from "@angular/router";
// //  import { Subscription } from "rxjs/Subscription";
// import { Subscription } from "rxjs";

// // import { MsAdalAngular6Service } from "microsoft-adal-angular6";

// @Component({
//   selector: "app-login",
//   templateUrl: "./login.component.html",
// })
// export class LoginComponent implements OnInit, OnDestroy {
//   public subscription: any;
//   constructor(
//     private router: Router,
//     private route: ActivatedRoute //private adalSvc: MsAdalAngular6Service
//   ) {
//     /**
//      *  set cookie and expiry-time
//      */
//     const expirydate = new Date();
//     expirydate.setMinutes(expirydate.getMinutes() + 2);
//     document.cookie = "username=login; expires=" + expirydate.toUTCString();
//     //console.log("user:::::::::::::::::::", this.adalSvc.userInfo);
//     // if (this.adalSvc.isAuthenticated && this.adalSvc.userInfo) {
//     //     /**
//     //      * store user details and jwt token in local storage to keep user logged in between page refreshes
//     //      */
//     //     localStorage.setItem('currentUser', JSON.stringify(this.adalSvc.userInfo));
//     // }
//   }

//   public ngOnInit() {
//     // if (this.adalSvc.isAuthenticated) {
//     //   const expiry = document.cookie;
//     //   if (expiry === "" || expiry === undefined) {
//     //     this.adalSvc.logout();
//     //     localStorage.removeItem("currentUser");
//     //     localStorage.removeItem("token");
//     //     localStorage.removeItem("menuItems");
//     //     return;
//     //   }
//     // }
//     this.logIn();
//   }

//   ngOnDestroy() {
//     if (this.subscription) {
//       this.subscription.unsubscribe();
//     }
//   }

//   public logIn() {
//     //this.adalSvc.login();
//   }
// }
import { Component, OnInit, Inject, OnDestroy } from "@angular/core";
import { Router } from "@angular/router";
import { Subject } from "rxjs";

@Component({
    selector: "app-login",
    templateUrl: "./login.component.html",
    standalone: true,
})
export class LoginComponent implements OnInit, OnDestroy {
  private readonly _destroying$ = new Subject<void>();

  constructor(private router: Router) {}
  ngOnInit() {
    setTimeout(() => {
      this.goToPrevious();
    }, 200);
  }

  goToPrevious() {
    if (
      localStorage.getItem("previousUrl") &&
      localStorage.getItem("previousUrl") != "/" &&
      localStorage.getItem("previousUrl") != "/login" &&
      localStorage.getItem("previousUrl") != "/access_token"
    ) {
      console.log(
        "previous local storage url=======>>>",
        localStorage.getItem("previousUrl")
      );
      setTimeout(() => {
        this.router.navigate([localStorage.getItem("previousUrl")]);
        return;
      }, 100);
    } else {
      setTimeout(() => {
        this.router.navigate(["/ManageSchedule"]);
      }, 100);
    }
  }

  ngOnDestroy(): void {
    this._destroying$.next(undefined);
    this._destroying$.complete();
  }
}

import { Routes } from "@angular/router";
import { LoginComponent } from './modules/login/login.component';
import { IsAuthGuard } from "./auth.guard";
import { MsalGuard } from "@azure/msal-angular";
import { AccessDeniedComponent } from "./structure/components/access-denied/access-denied.component";
import { UnderConstructionComponent } from "./structure/components/under-construction/under-construction.component";

export const routes: Routes = [

  {
    path: "access-denied",
    component: AccessDeniedComponent,
    data: { title: "Access Denied" },
  },
  {
    path: "login",
    component: LoginComponent,
    data: { title: "login" },
  },
  { path: "", redirectTo: "login", pathMatch: "full" },

  {
    path: "code",
    redirectTo: "login",
  },
  
  // { path: "**", redirectTo: "pages/page-404" },
  {
    path: "home",
    loadChildren: () =>
    import("./modules/importstatus/importstatus.module").then(
      (m) => m.ImportStatusModule
    ),
   canActivate: [IsAuthGuard,MsalGuard],
    data: { title: "Home" },
  },
  {
    path: "ManageCDKJobs",
    loadChildren: () =>
      import("./modules/cdk3pa/managecdkjobs.module").then(
        (m) => m.Cdk3paModule
      ),
    canActivate: [IsAuthGuard,MsalGuard]
  },

  // {
  //   path: "ManageCDKJobs",
  //   loadChildren: () =>
  //     import("./modules/cdk3pa/managecdkjobs.routing.module").then(
  //       (m) => m.default
  //     ),
  //   canActivate: [IsAuthGuard,MsalGuard],
  // },
  {
    path: "ManageCDKFlex",
    loadChildren: () =>
      import("./modules/cdkflex/managecdkflexjobs.module").then((m) => m.CdkFelxModule),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageAutoMate",
    loadChildren: () =>
      import("./modules/automate/manageautomate.module").then(
        (m) => m.AutomateModule
      ),
     canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageDealerBuilt",
    loadChildren: () =>
      import("./modules/dealerbuilt/managedealerbuilt.module").then(
        (m) => m.DealerbuiltModule
      ),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageDealerTrack",
    loadChildren: () =>
      import("./modules/dealertrack/managedealertrack.module").then(
        (m) => m.DealertrackModule
      ),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageAdam",
    loadChildren: () =>
      import("./modules/adam/manageadam.module").then((m) => m.AdamModule),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageReynolds",
    loadChildren: () =>
      import("./modules/reynolds/managereynolds.module").then(
        (m) => m.ReynoldsModule
      ),
    canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageDominion",
    loadChildren: () =>
      import("./modules/dominion/managedominion.module").then(
        (m) => m.DominionModule
      ),
     canActivate: [IsAuthGuard,MsalGuard]

  },
  {
    path: "ManageAutosoft",
    loadChildren: () =>
      import("./modules/autosoft/manageautosoft.module").then(
        (m) => m.AutosoftModule
      ),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManagePbs",
    loadChildren: () =>
      import("./modules/pbs/managepbs.module").then(
        (m) => m.PbsModule
      ),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageQuorum",
    loadChildren: () =>
      import("./modules/quorum/managequorum.module").then(
        (m) => m.QuorumModule
      ),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageUcs",
    loadChildren: () =>
      import("./modules/ucs/manageucs.module").then(
        (m) => m.UcsModule
      ),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageTekion",
    loadChildren: () =>
      import("./modules/tekion/managetekion.module").then(
        (m) => m.TekionModule
      ),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageFortellis",
    loadChildren: () =>
      import("./modules/fortellis/managefortellis.module").then(
        (m) => m.FortellisModule
      ),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageTekionApi",
    loadChildren: () =>
      import("./modules/tekionapi/managetekionapi.module").then(
        (m) => m.TekionapiModule
      ),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageMpk",
    loadChildren: () =>
      import("./modules/mpk/managempk.module").then(
        (m) => m.MpkModule
      ),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ImportStatus",
    loadChildren: () =>
      import("./modules/importstatus/importstatus.module").then(
        (m) => m.ImportStatusModule
      ),
     canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "SchedulerDmsImport/:base64QueryString",
    loadChildren: () =>
      import("./modules/schedulerdmsimport/schedulerdmsimport.module").then(
        (m) => m.SchedulerDmsImportModule
      ),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "SchedulerDmsImport",
    loadChildren: () =>
      import("./modules/schedulerdmsimport/schedulerdmsimport.module").then(
        (m) => m.SchedulerDmsImportModule
      ),
   canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageSchedule-Setting",
    loadChildren: () =>
      import("./modules/paytypesetting/paytypesetting.module").then(
        (m) => m.PayTypeModule
      ),
     canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "ManageScheduleFields",
    loadChildren: () =>
      import("./modules/managescheduleField/managescheduleField.module").then(
        (m) => m.ManageScheduleFieldModule
      ),
      canActivate: [IsAuthGuard,MsalGuard]     
  },
  {
    path: "ManageScheduleFields",
    loadChildren: () =>
      import("./modules/managescheduleField/managescheduleField.module").then(
        (m) => m.ManageScheduleFieldModule
      ),
      canActivate: [IsAuthGuard,MsalGuard]     
  },
  {
    path: "StageUpdatePortal",
    loadChildren: () =>
      import("./modules/stageupdateportal/stageupdateportal.module").then(
        (m) => m.StageupdateportalModule
      ),
      canActivate: [IsAuthGuard,MsalGuard]
  },
  {
    path: "under-construction",
    component: UnderConstructionComponent,
    canActivate: [IsAuthGuard,MsalGuard],
  },
  {
    path: "**",
    redirectTo: "/login",
    pathMatch: "full",
  },
  // {
  //   path: "id_token",
  //   redirectTo: "ManageSchedule",
  // },
  
];

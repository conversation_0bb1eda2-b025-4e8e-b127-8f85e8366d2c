import { Injectable, inject } from "@angular/core";
import {
  Router,
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  CanActivateFn,
} from "@angular/router";

@Injectable({
  providedIn: "root",
})
export class IsAuthGuard implements CanActivate {
  constructor(
    private router: Router // private msalService: MsalService
  ) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    if (state.url && state.url != "/" && state.url != "/login") {
      localStorage.setItem("previousUrl", state.url);
    }
    return true;
  }
}
// class AuthGuard {
//   canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
//     if (state.url && state.url != "/" && state.url != "/login") {
//       console.log("state", state);
//       localStorage.setItem("previousUrl", state.url);
//     }
//     return true;
//   }
// }
// export const IsAuthGuard: CanActivateFn = (
//   route: ActivatedRouteSnapshot,
//   state: RouterStateSnapshot
// ): boolean => {
//   return inject(AuthGuard).canActivate(route, state);
// };

// export const AuthGuard = () => {
//   const state = inject(RouterStateSnapshot); // private msalService: MsalService
//   const route = inject(ActivatedRouteSnapshot);
//   if (state.url && state.url != "/" && state.url != "/login") {
//     localStorage.setItem("previousUrl", state.url);
//   }
//   return true;
// };

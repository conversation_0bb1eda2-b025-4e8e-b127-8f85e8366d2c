import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import {   PreloadAllModules,
  provideRouter,
  withPreloading,withHashLocation } from '@angular/router';
import { routes } from './app-routing';
import { BrowserModule } from '@angular/platform-browser';
import { provideHttpClient, withInterceptorsFromDi, HTTP_INTERCEPTORS, withF<PERSON>ch,HttpClientModule } from '@angular/common/http';
import { provideNoopAnimations } from '@angular/platform-browser/animations';
import { IPublicClientApplication, PublicClientApplication, InteractionType, BrowserCacheLocation, LogLevel } from '@azure/msal-browser';
import { MsalInterceptor, MSAL_INSTANCE, MsalInterceptorConfiguration, MsalGuardConfiguration, MSAL_GUARD_CONFIG, MSAL_INTERCEPTOR_CONFIG, MsalService, MsalGuard, MsalBroadcastService } from '@azure/msal-angular';
import { environment } from '../environments/environment';
import { GraphQLModule } from './graphql.module';
import { provideAnimations } from "@angular/platform-browser/animations";
import { provideToastr } from "ngx-toastr";
import { SubscriptionConstantService } from './structure/constants/subscription.constant.service';

export function loggerCallback(logLevel: LogLevel, message: string) {
 console.log(message);
}
export function MSALInstanceFactory(): IPublicClientApplication {
 return new PublicClientApplication({
   auth: {
     clientId: environment.clientId,
     authority: "https://login.microsoftonline.com/7663244e-06dc-4b2c-adba-cc0980585782",
     redirectUri: environment.redirectUri,
     postLogoutRedirectUri: environment.redirectUri,
     navigateToLoginRequestUrl: false,
   },
   cache: {
     cacheLocation: BrowserCacheLocation.LocalStorage,
   },
   system: {
     allowNativeBroker: false, // Disables WAM Broker
     loggerOptions: {
       loggerCallback,
       logLevel: LogLevel.Trace,
       piiLoggingEnabled: false,
     },
   },
 });
}
export function MSALInterceptorConfigFactory(): MsalInterceptorConfiguration {
 const protectedResourceMap = new Map<string, Array<string>>();
 protectedResourceMap.set("https://graph.microsoft.com/v1.0/me", ["user.read"]);

 // protectedResourceMap.set(environment.apiUrl+'/portal', [environment.authScope]);
 
 return {
   interactionType: InteractionType.Redirect,
   protectedResourceMap,
 };
}
export function MSALGuardConfigFactory(): MsalGuardConfiguration {
 return {
   interactionType: InteractionType.Redirect,
   authRequest: {
     scopes: ["user.read", "openid", "profile"],
   },
   // loginFailedRoute: "/login",
 };
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes,withHashLocation()), 
    importProvidersFrom(BrowserModule,
    GraphQLModule,
    HttpClientModule),
    provideNoopAnimations(),
    provideAnimations(), // Required for toastr animations
    provideToastr({  // Toastr providers
      timeOut: 3000,
      positionClass: "toast-bottom-right",
      preventDuplicates: true,
      // toastClass: "", //for custom class
    }),
    provideHttpClient(withInterceptorsFromDi(), withFetch()),
    {
        provide: HTTP_INTERCEPTORS,
        useClass: MsalInterceptor,
        multi: true
    },
    {
        provide: MSAL_INSTANCE,
        useFactory: MSALInstanceFactory
    },
    {
        provide: MSAL_GUARD_CONFIG,
        useFactory: MSALGuardConfigFactory
    },
    {
        provide: MSAL_INTERCEPTOR_CONFIG,
        useFactory: MSALInterceptorConfigFactory
    },
    MsalService,
    MsalGuard,
    MsalBroadcastService,
    SubscriptionConstantService
  ]
};
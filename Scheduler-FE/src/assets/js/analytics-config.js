var config = { url: 'https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/activityLog' };

var baseUrl = "//" + location.hostname;
if (baseUrl.indexOf('//scheduler') != -1) {
    config = { url: 'https://scheduler.production.dealeruplift.net/scheduler/activityLog' };
}
if (baseUrl.indexOf('//tmp-uat-scheduler') != -1) {
    config = { url: 'https://tmp-uat-scheduler.production.dealeruplift.net/scheduler/activityLog' };
}

if (baseUrl.indexOf('//new-devl-scheduler') != -1) {
    config = { url: 'https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/activityLog' };
}

if (baseUrl.indexOf('//scheduler-backup.production') != -1) {
    config = { url: 'https://scheduler-backup.production.dealeruplift.net/scheduler/activityLog' };
}

if (baseUrl.indexOf('//uat2-scheduler') != -1) {
    config = { url: 'https://uat2-scheduler.production.dealeruplift.net/scheduler/activityLog' };
}
if (baseUrl.indexOf('//uat3-scheduler') != -1) {
    config = { url: 'https://uat3-scheduler.production.dealeruplift.net/scheduler/activityLog' };
}

if (baseUrl.indexOf('//lts-scheduler') != -1) {
    config = { url: 'https://lts-scheduler.production.dealeruplift.net/scheduler/activityLog' };
}
if (baseUrl.indexOf('//legacy-scheduler') != -1) {
    config = { url: 'https://legacy-scheduler.production.dealeruplift.net/scheduler/activityLog' };
}
if (baseUrl.indexOf('//scheduler.bc') != -1) {
    config = { url: 'https://scheduler.bc.dealeruplift.net/scheduler/activityLog' };
}if (baseUrl.indexOf('//scheduler.bc') != -1) {
    config = { url: 'https://scheduler.bc.dealeruplift.net/scheduler/activityLog' };
}
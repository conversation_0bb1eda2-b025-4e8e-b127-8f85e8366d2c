<!doctype html>
<html>

<head>
	<title>Time Scale Point Data</title>
	<script src="http://cdnjs.cloudflare.com/ajax/libs/moment.js/2.13.0/moment.min.js"></script>
	<script src="../../../dist/Chart.js"></script>
	<script src="../../utils.js"></script>
	<style>
    canvas {
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
    }
	</style>
</head>

<body>
	<div style="width:75%;">
		<canvas id="canvas"></canvas>
	</div>
	<br>
	<br>
	<button id="randomizeData">Randomize Data</button>
	<button id="addData">Add Data</button>
	<button id="removeData">Remove Data</button>
	<script>
		function newDate(days) {
			return moment().add(days, 'd').toDate();
		}

		function newDateString(days) {
			return moment().add(days, 'd').format();
		}

		var color = Chart.helpers.color;
		var config = {
			type: 'line',
			data: {
				datasets: [{
					label: "Dataset with string point data",
					backgroundColor: color(window.chartColors.red).alpha(0.5).rgbString(),
					borderColor: window.chartColors.red,
					fill: false,
					data: [{
						x: newDateString(0),
						y: randomScalingFactor()
					}, {
						x: newDateString(2),
						y: randomScalingFactor()
					}, {
						x: newDateString(4),
						y: randomScalingFactor()
					}, {
						x: newDateString(5),
						y: randomScalingFactor()
					}],
				}, {
					label: "Dataset with date object point data",
					backgroundColor: color(window.chartColors.blue).alpha(0.5).rgbString(),
					borderColor: window.chartColors.blue,
					fill: false,
					data: [{
						x: newDate(0),
						y: randomScalingFactor()
					}, {
						x: newDate(2),
						y: randomScalingFactor()
					}, {
						x: newDate(4),
						y: randomScalingFactor()
					}, {
						x: newDate(5),
						y: randomScalingFactor()
					}]
				}]
			},
			options: {
				responsive: true,
                title:{
                    display:true,
                    text:"Chart.js Time Point Data"
                },
				scales: {
					xAxes: [{
						type: "time",
						display: true,
						scaleLabel: {
							display: true,
							labelString: 'Date'
						}
					}],
					yAxes: [{
						display: true,
						scaleLabel: {
							display: true,
							labelString: 'value'
						}
					}]
				}
			}
		};

		window.onload = function() {
			var ctx = document.getElementById("canvas").getContext("2d");
			window.myLine = new Chart(ctx, config);

		};

		document.getElementById('randomizeData').addEventListener('click', function() {
			config.data.datasets.forEach(function(dataset) {
				dataset.data.forEach(function(dataObj) {
					dataObj.y = randomScalingFactor();
				});
			});

			window.myLine.update();
		});

		document.getElementById('addData').addEventListener('click', function() {
			if (config.data.datasets.length > 0) {
				var lastTime = myLine.scales['x-axis-0'].labelMoments[0].length ? myLine.scales['x-axis-0'].labelMoments[0][myLine.scales['x-axis-0'].labelMoments[0].length - 1] : moment();
				var newTime = lastTime
					.clone()
					.add(1, 'day')
					.format('MM/DD/YYYY HH:mm');

				for (var index = 0; index < config.data.datasets.length; ++index) {
					config.data.datasets[index].data.push({
						x: newTime,
						y: randomScalingFactor()
					});
				}

				window.myLine.update();
			}
		});

		document.getElementById('removeData').addEventListener('click', function() {
			config.data.datasets.forEach(function(dataset, datasetIndex) {
				dataset.data.pop();
			});

			window.myLine.update();
		});
	</script>
</body>

</html>

/*!
 * Bootstrap-select v1.12.2 (http://silviomoreto.github.io/bootstrap-select)
 *
 * Copyright 2013-2017 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
!function(a,b){"function"==typeof define&&define.amd?define(["jquery"],function(a){return b(a)}):"object"==typeof module&&module.exports?module.exports=b(require("jquery")):b(a.jQuery)}(this,function(a){!function(a){a.fn.selectpicker.defaults={noneSelectedText:"Ingen valgt",noneResultsText:"Søket gir ingen treff {0}",countSelectedText:function(a,b){return 1==a?"{0} alternativ valgt":"{0} alternativer valgt"},maxOptionsText:function(a,b){return["<PERSON><PERSON><PERSON> nådd (maks {n} valg)","G<PERSON>se for grupper nådd (maks {n} grupper)"]},selectAllText:"Merk alle",deselectAllText:"Fjern alle",multipleSeparator:", "}}(a)});
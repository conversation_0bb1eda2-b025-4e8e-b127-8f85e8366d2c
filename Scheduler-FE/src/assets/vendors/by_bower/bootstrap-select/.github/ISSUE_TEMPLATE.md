Before posting, please see [guidelines for contributing](https://github.com/silviomoreto/bootstrap-select/blob/master/CONTRIBUTING.md). If you're submitting a bug report, see below.

## Bug reports

A bug is a _demonstrable problem_ that is caused by the code in the repository.
Good bug reports are extremely helpful - thank you!

Guidelines for bug reports:

1. **Use the GitHub issue search.** Check if the issue has already been
   reported.

2. **Check if the issue has been fixed.** Try to reproduce it using the
   latest `master` or development branch in the repository.

3. **Provide environment details.** Provide your operating system, browser(s),
   jQuery version, Bootstrap version, and bootstrap-select version.

4. **Create an isolated and reproducible test case.** Create a [reduced test
   case](http://css-tricks.com/6263-reduced-test-cases/).

5. **Include a live example.** Use [this Plunker debugging template](http://silviomoreto.github.io/bootstrap-select/playground/) to share your isolated test cases. You can also make use of [jsFiddle](http://jsfiddle.net/) or [jsBin](http://jsbin.com/).

A good bug report shouldn't leave others needing to chase you up for more
information. Please try to be as detailed as possible in your report. What is
your environment? What steps will reproduce the issue? What browser(s) and OS
experience the problem? What would you expect to be the outcome? All these
details will help people to fix any potential bugs.

Example:

> Short and descriptive example bug report title
>
> A summary of the issue and the browser/OS environment in which it occurs. If
> suitable, include the steps required to reproduce the bug.
>
> 1. This is the first step
> 2. This is the second step
> 3. Further steps, etc.
>
> `<url>` - a link to the reduced test case
>
> Any other information you want to share that is relevant to the issue being
> reported. This might include the lines of code that you have identified as
> causing the bug, and potential solutions (and your opinions on their
> merits).

## Erase the above text and being typing. Thanks!
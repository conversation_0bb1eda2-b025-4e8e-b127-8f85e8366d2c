html {
  position: relative;
  min-height: 100%;
}
body {
  padding-top: 51px;
  /* Margin bottom by footer height */
  margin-bottom: 60px;
}
label {
  display: block;
}
/* hide "Home" in navbar */
.nav.navbar-nav:first-child > li:first-child {
    display: none;
}

ul.nav li.main {
  font-weight: normal;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  /* Set the fixed height of the footer here */
  height: 60px;
  background-color: #f5f5f5;
}

.footer .container .text-muted {
  margin: 20px 0;
}

.footer .container {
  padding-right: 15px;
  padding-left: 15px;
}

/* Outline button for use within the docs */
.btn-outline {
  color: #337ab7;
  background-color: transparent;
  border-color: #337ab7;
}
.btn-outline:hover,
.btn-outline:focus,
.btn-outline:active {
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
}

/* Inverted outline button (white on dark) */
.btn-outline-inverse {
  color: #fff;
  background-color: transparent;
  border-color: #fff;
}
.btn-outline-inverse:hover,
.btn-outline-inverse:focus,
.btn-outline-inverse:active {
  color: #337ab7;
  text-shadow: none;
  background-color: #fff;
  border-color: #fff;
}

.bs-docs-header {
  margin-bottom: 0;
  background: #337ab7;
  color: #fff;
}

.bs-docs-header .btn {
  padding: 15px 30px;
  font-size: 20px
}

.bs-docs-header h1 {
  margin-bottom: 30px;
}

.bs-docs-header .lead {
  margin: 0 auto 30px;
}

.bs-docs-sub-header {
  padding-top: 20px;
  padding-bottom: 20px;
}

.gh-btns {
  margin: 48px 0 -30px;
  background: rgba(0,0,0,.1);
  padding: 20px 0 15px;
}

.content h1:first-of-type,
.content h1:first-of-type + p:first-of-type {
  text-align: center;
}

.bs-docs-example > p {
  margin-top: 20px;
}

.bs-docs-example > p:last-child {
  margin-bottom: 0;
}

.bs-docs-example .table,
.bs-docs-example .progress,
.bs-docs-example .well,
.bs-docs-example .alert,
.bs-docs-example .hero-unit,
.bs-docs-example .pagination,
.bs-docs-example .navbar,
.bs-docs-example > .nav,
.bs-docs-example blockquote {
  margin-bottom: 5px;
}

.bs-docs-example .pagination {
  margin-top: 0;
}

.special {
  font-weight: bold !important;
  color: #fff !important;
  background: #bc0000 !important;
  text-transform: uppercase;
}

.bs-docs-example {
  position: relative;
  padding: 45px 15px 15px;
  margin: 0 -15px 15px;
  border-color: #e5e5e5 #eee #eee;
  border-style: solid;
  border-width: 1px 0;
  -webkit-box-shadow: inset 0 3px 6px rgba(0,0,0,.05);
  box-shadow: inset 0 3px 6px rgba(0,0,0,.05);
}

/* Echo out a label for the example */
.bs-docs-example:after {
  position: absolute;
  top: 15px;
  left: 15px;
  font-size: 12px;
  font-weight: 700;
  color: #959595;
  text-transform: uppercase;
  letter-spacing: 1px;
  content: "Example";
}

.highlight {
  padding: 9px 14px;
  margin-bottom: 14px;
  background-color: #f7f7f9;
  border: 1px solid #e1e1e8;
  border-radius: 4px;
}

.bs-docs-example + .highlight {
  margin: -15px -15px 15px;
  border-width: 0 0 1px;
  border-radius: 0;
}

.carbonad {
  margin-top: 80px;
}

.carbonad-inner {
  width: auto!important;
  height: auto!important;
  padding: 20px!important;
  margin: 30px -15px 0!important;
  overflow: hidden;
  font-size: 13px!important;
  line-height: 16px!important;
  text-align: left;
  background: 0 0!important;
  border: solid rgba(255, 255, 255, 0.50) !important;
  border-width: 1px 0!important;
}

.carbon-poweredby,
.carbon-text {
  display: block!important;
  float: none!important;
  width: auto!important;
  height: auto!important;
  margin-left: 145px!important;
  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif!important;
  color: #fff!important;
}

.carbon-poweredby {
  float: left;
  margin-top: 9px;
  text-align: left;
  width: 142px;
  opacity: 0.5;
}

.charity {
  opacity: 0.5;
  padding: 4px;
  margin-bottom: -31px;
}

.charity a {
  color: #fff;
}

.carbon-img img {
    border: none;
    display: inline;
    float: left;
    height: 100px;
    margin: 0 !important;
    width: 130px;
}

.logo-block a {
  display: block;
  padding: 5px;
  margin: 5px 0;
}

.logo-block img {
  height: auto;
  max-width: 100%;
  max-height: 40px;
}

.logo-container {
  float: left;
  max-width: 25%;
  margin: 0 15px;
}

.logo-block + div {
  margin: 5px 0;
}

@media (min-width: 480px){
  .carbonad-inner {
    width: 330px!important;
    margin: 50px auto 0 !important;
    border-width: 1px!important;
    border-radius: 4px;
  }

  .charity {
    margin-bottom: 0;
  }

  .logo-container {
    max-width: 60%;
  }
}

@media (min-width: 768px) {
  .bs-docs-example {
    margin-right: 0;
    margin-left: 0;
    background-color: #fff;
    border-color: #ddd;
    border-width: 1px;
    border-radius: 4px 4px 0 0;
    -webkit-box-shadow: none;
    box-shadow: none;
  }

  .bs-docs-example.no-code {
    border-radius: 4px;
  }

  .bs-docs-example + .highlight {
    margin-top: -16px;
    margin-right: 0;
    margin-left: 0;
    border-width: 1px;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
  }

  .logo-container {
    max-width: 33.3333%;
  }

  .gh-btns {
    margin-bottom: -48px;
  }
}

@media (min-width: 992px){
  .bs-docs-header .lead {
      width: 80%;
  }

  .carbonad-inner {
    top: 0;
    right: 15px;
    width: 330px!important;
    padding: 15px!important;
  }

  .logo-container {
    max-width: 25%;
  }
}
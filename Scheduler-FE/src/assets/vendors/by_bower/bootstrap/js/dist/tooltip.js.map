{"version": 3, "sources": ["../src/tooltip.js"], "names": ["<PERSON><PERSON><PERSON>", "$", "<PERSON><PERSON>", "Error", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "fn", "TRANSITION_DURATION", "CLASS_PREFIX", "<PERSON><PERSON><PERSON>", "animation", "template", "trigger", "title", "delay", "html", "selector", "placement", "offset", "constraints", "container", "DefaultType", "AttachmentMap", "TOP", "RIGHT", "BOTTOM", "LEFT", "HoverState", "SHOW", "OUT", "Event", "HIDE", "HIDDEN", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "ClassName", "FADE", "Selector", "TOOLTIP", "TOOLTIP_INNER", "TetherClass", "element", "enabled", "<PERSON><PERSON>", "HOVER", "FOCUS", "MANUAL", "config", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "_isTransitioning", "_tether", "_getConfig", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "toggle", "event", "dataKey", "constructor", "context", "currentTarget", "data", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "hasClass", "dispose", "clearTimeout", "cleanupTether", "removeData", "off", "closest", "remove", "show", "css", "showEvent", "isWithContent", "isInTheDom", "contains", "ownerDocument", "documentElement", "isDefaultPrevented", "tipId", "<PERSON><PERSON>", "getUID", "setAttribute", "<PERSON><PERSON><PERSON><PERSON>", "addClass", "call", "attachment", "_getAttachment", "document", "body", "appendTo", "target", "classes", "classPrefix", "addTargetClasses", "reflow", "position", "complete", "prevHoverState", "supportsTransitionEnd", "one", "TRANSITION_END", "emulateTransitionEnd", "_TRANSITION_DURATION", "hide", "callback", "hideEvent", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "removeAttribute", "removeClass", "Boolean", "getTitle", "$tip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "$element", "content", "nodeType", "j<PERSON>y", "parent", "is", "empty", "append", "text", "getAttribute", "destroy", "toUpperCase", "triggers", "split", "for<PERSON>ach", "on", "eventIn", "eventOut", "extend", "_fixTitle", "titleType", "type", "setTimeout", "typeCheckConfig", "key", "_jQueryInterface", "each", "_config", "test", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;AAKA;;;;;;;AAOA,IAAMA,UAAW,UAACC,CAAD,EAAO;;AAEtB;;;;AAIA,MAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;AACjC,UAAM,IAAIC,KAAJ,CAAU,uDAAV,CAAN;AACD;;AAGD;;;;;;AAMA,MAAMC,OAAsB,SAA5B;AACA,MAAMC,UAAsB,eAA5B;AACA,MAAMC,WAAsB,YAA5B;AACA,MAAMC,kBAA0BD,QAAhC;AACA,MAAME,qBAAsBP,EAAEQ,EAAF,CAAKL,IAAL,CAA5B;AACA,MAAMM,sBAAsB,GAA5B;AACA,MAAMC,eAAsB,WAA5B;;AAEA,MAAMC,UAAU;AACdC,eAAc,IADA;AAEdC,cAAc,yCACA,yCAHA;AAIdC,aAAc,aAJA;AAKdC,WAAc,EALA;AAMdC,WAAc,CANA;AAOdC,UAAc,KAPA;AAQdC,cAAc,KARA;AASdC,eAAc,KATA;AAUdC,YAAc,KAVA;AAWdC,iBAAc,EAXA;AAYdC,eAAc;AAZA,GAAhB;;AAeA,MAAMC,cAAc;AAClBX,eAAc,SADI;AAElBC,cAAc,QAFI;AAGlBE,WAAc,2BAHI;AAIlBD,aAAc,QAJI;AAKlBE,WAAc,iBALI;AAMlBC,UAAc,SANI;AAOlBC,cAAc,kBAPI;AAQlBC,eAAc,mBARI;AASlBC,YAAc,QATI;AAUlBC,iBAAc,OAVI;AAWlBC,eAAc;AAXI,GAApB;;AAcA,MAAME,gBAAgB;AACpBC,SAAS,eADW;AAEpBC,WAAS,aAFW;AAGpBC,YAAS,YAHW;AAIpBC,UAAS;AAJW,GAAtB;;AAOA,MAAMC,aAAa;AACjBC,UAAO,MADU;AAEjBC,SAAO;AAFU,GAAnB;;AAKA,MAAMC,QAAQ;AACZC,mBAAoB3B,SADR;AAEZ4B,uBAAsB5B,SAFV;AAGZwB,mBAAoBxB,SAHR;AAIZ6B,qBAAqB7B,SAJT;AAKZ8B,2BAAwB9B,SALZ;AAMZ+B,qBAAqB/B,SANT;AAOZgC,yBAAuBhC,SAPX;AAQZiC,2BAAwBjC,SARZ;AASZkC,+BAA0BlC,SATd;AAUZmC,+BAA0BnC;AAVd,GAAd;;AAaA,MAAMoC,YAAY;AAChBC,UAAO,MADS;AAEhBb,UAAO;AAFS,GAAlB;;AAKA,MAAMc,WAAW;AACfC,aAAgB,UADD;AAEfC,mBAAgB;AAFD,GAAjB;;AAKA,MAAMC,cAAc;AAClBC,aAAU,KADQ;AAElBC,aAAU;AAFQ,GAApB;;AAKA,MAAMC,UAAU;AACdC,WAAS,OADK;AAEdC,WAAS,OAFK;AAGdf,WAAS,OAHK;AAIdgB,YAAS;AAJK,GAAhB;;AAQA;;;;;;AAtGsB,MA4GhBtD,OA5GgB;AA8GpB,qBAAYiD,OAAZ,EAAqBM,MAArB,EAA6B;AAAA;;AAE3B;AACA,WAAKC,UAAL,GAAyB,IAAzB;AACA,WAAKC,QAAL,GAAyB,CAAzB;AACA,WAAKC,WAAL,GAAyB,EAAzB;AACA,WAAKC,cAAL,GAAyB,EAAzB;AACA,WAAKC,gBAAL,GAAyB,KAAzB;AACA,WAAKC,OAAL,GAAyB,IAAzB;;AAEA;AACA,WAAKZ,OAAL,GAAeA,OAAf;AACA,WAAKM,MAAL,GAAe,KAAKO,UAAL,CAAgBP,MAAhB,CAAf;AACA,WAAKQ,GAAL,GAAe,IAAf;;AAEA,WAAKC,aAAL;AAED;;AAGD;;AA+BA;;AAjKoB,sBAmKpBC,MAnKoB,qBAmKX;AACP,WAAKT,UAAL,GAAkB,IAAlB;AACD,KArKmB;;AAAA,sBAuKpBU,OAvKoB,sBAuKV;AACR,WAAKV,UAAL,GAAkB,KAAlB;AACD,KAzKmB;;AAAA,sBA2KpBW,aA3KoB,4BA2KJ;AACd,WAAKX,UAAL,GAAkB,CAAC,KAAKA,UAAxB;AACD,KA7KmB;;AAAA,sBA+KpBY,MA/KoB,mBA+KbC,KA/Ka,EA+KN;AACZ,UAAIA,KAAJ,EAAW;AACT,YAAMC,UAAU,KAAKC,WAAL,CAAiBjE,QAAjC;AACA,YAAIkE,UAAUvE,EAAEoE,MAAMI,aAAR,EAAuBC,IAAvB,CAA4BJ,OAA5B,CAAd;;AAEA,YAAI,CAACE,OAAL,EAAc;AACZA,oBAAU,IAAI,KAAKD,WAAT,CACRF,MAAMI,aADE,EAER,KAAKE,kBAAL,EAFQ,CAAV;AAIA1E,YAAEoE,MAAMI,aAAR,EAAuBC,IAAvB,CAA4BJ,OAA5B,EAAqCE,OAArC;AACD;;AAEDA,gBAAQb,cAAR,CAAuBiB,KAAvB,GAA+B,CAACJ,QAAQb,cAAR,CAAuBiB,KAAvD;;AAEA,YAAIJ,QAAQK,oBAAR,EAAJ,EAAoC;AAClCL,kBAAQM,MAAR,CAAe,IAAf,EAAqBN,OAArB;AACD,SAFD,MAEO;AACLA,kBAAQO,MAAR,CAAe,IAAf,EAAqBP,OAArB;AACD;AAEF,OApBD,MAoBO;;AAEL,YAAIvE,EAAE,KAAK+E,aAAL,EAAF,EAAwBC,QAAxB,CAAiCtC,UAAUZ,IAA3C,CAAJ,EAAsD;AACpD,eAAKgD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACA;AACD;;AAED,aAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACD;AACF,KA7MmB;;AAAA,sBA+MpBI,OA/MoB,sBA+MV;AACRC,mBAAa,KAAK1B,QAAlB;;AAEA,WAAK2B,aAAL;;AAEAnF,QAAEoF,UAAF,CAAa,KAAKpC,OAAlB,EAA2B,KAAKsB,WAAL,CAAiBjE,QAA5C;;AAEAL,QAAE,KAAKgD,OAAP,EAAgBqC,GAAhB,CAAoB,KAAKf,WAAL,CAAiBhE,SAArC;AACAN,QAAE,KAAKgD,OAAP,EAAgBsC,OAAhB,CAAwB,QAAxB,EAAkCD,GAAlC,CAAsC,eAAtC;;AAEA,UAAI,KAAKvB,GAAT,EAAc;AACZ9D,UAAE,KAAK8D,GAAP,EAAYyB,MAAZ;AACD;;AAED,WAAKhC,UAAL,GAAsB,IAAtB;AACA,WAAKC,QAAL,GAAsB,IAAtB;AACA,WAAKC,WAAL,GAAsB,IAAtB;AACA,WAAKC,cAAL,GAAsB,IAAtB;AACA,WAAKE,OAAL,GAAsB,IAAtB;;AAEA,WAAKZ,OAAL,GAAe,IAAf;AACA,WAAKM,MAAL,GAAe,IAAf;AACA,WAAKQ,GAAL,GAAe,IAAf;AACD,KAtOmB;;AAAA,sBAwOpB0B,IAxOoB,mBAwOb;AAAA;;AACL,UAAIxF,EAAE,KAAKgD,OAAP,EAAgByC,GAAhB,CAAoB,SAApB,MAAmC,MAAvC,EAA+C;AAC7C,cAAM,IAAIvF,KAAJ,CAAU,qCAAV,CAAN;AACD;;AAED,UAAMwF,YAAY1F,EAAEgC,KAAF,CAAQ,KAAKsC,WAAL,CAAiBtC,KAAjB,CAAuBF,IAA/B,CAAlB;AACA,UAAI,KAAK6D,aAAL,MAAwB,KAAKpC,UAAjC,EAA6C;AAC3C,YAAI,KAAKI,gBAAT,EAA2B;AACzB,gBAAM,IAAIzD,KAAJ,CAAU,0BAAV,CAAN;AACD;AACDF,UAAE,KAAKgD,OAAP,EAAgBlC,OAAhB,CAAwB4E,SAAxB;;AAEA,YAAME,aAAa5F,EAAE6F,QAAF,CACjB,KAAK7C,OAAL,CAAa8C,aAAb,CAA2BC,eADV,EAEjB,KAAK/C,OAFY,CAAnB;;AAKA,YAAI0C,UAAUM,kBAAV,MAAkC,CAACJ,UAAvC,EAAmD;AACjD;AACD;;AAED,YAAM9B,MAAQ,KAAKiB,aAAL,EAAd;AACA,YAAMkB,QAAQC,KAAKC,MAAL,CAAY,KAAK7B,WAAL,CAAiBnE,IAA7B,CAAd;;AAEA2D,YAAIsC,YAAJ,CAAiB,IAAjB,EAAuBH,KAAvB;AACA,aAAKjD,OAAL,CAAaoD,YAAb,CAA0B,kBAA1B,EAA8CH,KAA9C;;AAEA,aAAKI,UAAL;;AAEA,YAAI,KAAK/C,MAAL,CAAY1C,SAAhB,EAA2B;AACzBZ,YAAE8D,GAAF,EAAOwC,QAAP,CAAgB5D,UAAUC,IAA1B;AACD;;AAED,YAAMxB,YAAa,OAAO,KAAKmC,MAAL,CAAYnC,SAAnB,KAAiC,UAAjC,GACjB,KAAKmC,MAAL,CAAYnC,SAAZ,CAAsBoF,IAAtB,CAA2B,IAA3B,EAAiCzC,GAAjC,EAAsC,KAAKd,OAA3C,CADiB,GAEjB,KAAKM,MAAL,CAAYnC,SAFd;;AAIA,YAAMqF,aAAa,KAAKC,cAAL,CAAoBtF,SAApB,CAAnB;;AAEA,YAAMG,YAAY,KAAKgC,MAAL,CAAYhC,SAAZ,KAA0B,KAA1B,GAAkCoF,SAASC,IAA3C,GAAkD3G,EAAE,KAAKsD,MAAL,CAAYhC,SAAd,CAApE;;AAEAtB,UAAE8D,GAAF,EACGW,IADH,CACQ,KAAKH,WAAL,CAAiBjE,QADzB,EACmC,IADnC,EAEGuG,QAFH,CAEYtF,SAFZ;;AAIAtB,UAAE,KAAKgD,OAAP,EAAgBlC,OAAhB,CAAwB,KAAKwD,WAAL,CAAiBtC,KAAjB,CAAuBI,QAA/C;;AAEA,aAAKwB,OAAL,GAAe,IAAI3D,MAAJ,CAAW;AACxBuG,gCADwB;AAExBxD,mBAAkBc,GAFM;AAGxB+C,kBAAkB,KAAK7D,OAHC;AAIxB8D,mBAAkB/D,WAJM;AAKxBgE,uBAAkBrG,YALM;AAMxBU,kBAAkB,KAAKkC,MAAL,CAAYlC,MANN;AAOxBC,uBAAkB,KAAKiC,MAAL,CAAYjC,WAPN;AAQxB2F,4BAAkB;AARM,SAAX,CAAf;;AAWAd,aAAKe,MAAL,CAAYnD,GAAZ;AACA,aAAKF,OAAL,CAAasD,QAAb;;AAEAlH,UAAE8D,GAAF,EAAOwC,QAAP,CAAgB5D,UAAUZ,IAA1B;;AAEA,YAAMqF,WAAW,SAAXA,QAAW,GAAM;AACrB,cAAMC,iBAAiB,MAAK3D,WAA5B;AACA,gBAAKA,WAAL,GAAqB,IAArB;AACA,gBAAKE,gBAAL,GAAwB,KAAxB;;AAEA3D,YAAE,MAAKgD,OAAP,EAAgBlC,OAAhB,CAAwB,MAAKwD,WAAL,CAAiBtC,KAAjB,CAAuBG,KAA/C;;AAEA,cAAIiF,mBAAmBvF,WAAWE,GAAlC,EAAuC;AACrC,kBAAK+C,MAAL,CAAY,IAAZ;AACD;AACF,SAVD;;AAYA,YAAIoB,KAAKmB,qBAAL,MAAgCrH,EAAE,KAAK8D,GAAP,EAAYkB,QAAZ,CAAqBtC,UAAUC,IAA/B,CAApC,EAA0E;AACxE,eAAKgB,gBAAL,GAAwB,IAAxB;AACA3D,YAAE,KAAK8D,GAAP,EACGwD,GADH,CACOpB,KAAKqB,cADZ,EAC4BJ,QAD5B,EAEGK,oBAFH,CAEwBzH,QAAQ0H,oBAFhC;AAGA;AACD;;AAEDN;AACD;AACF,KA7TmB;;AAAA,sBA+TpBO,IA/ToB,iBA+TfC,QA/Te,EA+TL;AAAA;;AACb,UAAM7D,MAAY,KAAKiB,aAAL,EAAlB;AACA,UAAM6C,YAAY5H,EAAEgC,KAAF,CAAQ,KAAKsC,WAAL,CAAiBtC,KAAjB,CAAuBC,IAA/B,CAAlB;AACA,UAAI,KAAK0B,gBAAT,EAA2B;AACzB,cAAM,IAAIzD,KAAJ,CAAU,0BAAV,CAAN;AACD;AACD,UAAMiH,WAAY,SAAZA,QAAY,GAAM;AACtB,YAAI,OAAK1D,WAAL,KAAqB5B,WAAWC,IAAhC,IAAwCgC,IAAI+D,UAAhD,EAA4D;AAC1D/D,cAAI+D,UAAJ,CAAeC,WAAf,CAA2BhE,GAA3B;AACD;;AAED,eAAKd,OAAL,CAAa+E,eAAb,CAA6B,kBAA7B;AACA/H,UAAE,OAAKgD,OAAP,EAAgBlC,OAAhB,CAAwB,OAAKwD,WAAL,CAAiBtC,KAAjB,CAAuBE,MAA/C;AACA,eAAKyB,gBAAL,GAAwB,KAAxB;AACA,eAAKwB,aAAL;;AAEA,YAAIwC,QAAJ,EAAc;AACZA;AACD;AACF,OAbD;;AAeA3H,QAAE,KAAKgD,OAAP,EAAgBlC,OAAhB,CAAwB8G,SAAxB;;AAEA,UAAIA,UAAU5B,kBAAV,EAAJ,EAAoC;AAClC;AACD;;AAEDhG,QAAE8D,GAAF,EAAOkE,WAAP,CAAmBtF,UAAUZ,IAA7B;;AAEA,WAAK4B,cAAL,CAAoBR,QAAQb,KAA5B,IAAqC,KAArC;AACA,WAAKqB,cAAL,CAAoBR,QAAQE,KAA5B,IAAqC,KAArC;AACA,WAAKM,cAAL,CAAoBR,QAAQC,KAA5B,IAAqC,KAArC;;AAEA,UAAI+C,KAAKmB,qBAAL,MACArH,EAAE,KAAK8D,GAAP,EAAYkB,QAAZ,CAAqBtC,UAAUC,IAA/B,CADJ,EAC0C;AACxC,aAAKgB,gBAAL,GAAwB,IAAxB;AACA3D,UAAE8D,GAAF,EACGwD,GADH,CACOpB,KAAKqB,cADZ,EAC4BJ,QAD5B,EAEGK,oBAFH,CAEwB/G,mBAFxB;AAID,OAPD,MAOO;AACL0G;AACD;;AAED,WAAK1D,WAAL,GAAmB,EAAnB;AACD,KA5WmB;;AA+WpB;;AA/WoB,sBAiXpBkC,aAjXoB,4BAiXJ;AACd,aAAOsC,QAAQ,KAAKC,QAAL,EAAR,CAAP;AACD,KAnXmB;;AAAA,sBAqXpBnD,aArXoB,4BAqXJ;AACd,aAAO,KAAKjB,GAAL,GAAW,KAAKA,GAAL,IAAY9D,EAAE,KAAKsD,MAAL,CAAYzC,QAAd,EAAwB,CAAxB,CAA9B;AACD,KAvXmB;;AAAA,sBAyXpBwF,UAzXoB,yBAyXP;AACX,UAAM8B,OAAOnI,EAAE,KAAK+E,aAAL,EAAF,CAAb;;AAEA,WAAKqD,iBAAL,CAAuBD,KAAKE,IAAL,CAAUzF,SAASE,aAAnB,CAAvB,EAA0D,KAAKoF,QAAL,EAA1D;;AAEAC,WAAKH,WAAL,CAAoBtF,UAAUC,IAA9B,SAAsCD,UAAUZ,IAAhD;;AAEA,WAAKqD,aAAL;AACD,KAjYmB;;AAAA,sBAmYpBiD,iBAnYoB,8BAmYFE,QAnYE,EAmYQC,OAnYR,EAmYiB;AACnC,UAAMtH,OAAO,KAAKqC,MAAL,CAAYrC,IAAzB;AACA,UAAI,QAAOsH,OAAP,yCAAOA,OAAP,OAAmB,QAAnB,KAAgCA,QAAQC,QAAR,IAAoBD,QAAQE,MAA5D,CAAJ,EAAyE;AACvE;AACA,YAAIxH,IAAJ,EAAU;AACR,cAAI,CAACjB,EAAEuI,OAAF,EAAWG,MAAX,GAAoBC,EAApB,CAAuBL,QAAvB,CAAL,EAAuC;AACrCA,qBAASM,KAAT,GAAiBC,MAAjB,CAAwBN,OAAxB;AACD;AACF,SAJD,MAIO;AACLD,mBAASQ,IAAT,CAAc9I,EAAEuI,OAAF,EAAWO,IAAX,EAAd;AACD;AACF,OATD,MASO;AACLR,iBAASrH,OAAO,MAAP,GAAgB,MAAzB,EAAiCsH,OAAjC;AACD;AACF,KAjZmB;;AAAA,sBAmZpBL,QAnZoB,uBAmZT;AACT,UAAInH,QAAQ,KAAKiC,OAAL,CAAa+F,YAAb,CAA0B,qBAA1B,CAAZ;;AAEA,UAAI,CAAChI,KAAL,EAAY;AACVA,gBAAQ,OAAO,KAAKuC,MAAL,CAAYvC,KAAnB,KAA6B,UAA7B,GACN,KAAKuC,MAAL,CAAYvC,KAAZ,CAAkBwF,IAAlB,CAAuB,KAAKvD,OAA5B,CADM,GAEN,KAAKM,MAAL,CAAYvC,KAFd;AAGD;;AAED,aAAOA,KAAP;AACD,KA7ZmB;;AAAA,sBA+ZpBoE,aA/ZoB,4BA+ZJ;AACd,UAAI,KAAKvB,OAAT,EAAkB;AAChB,aAAKA,OAAL,CAAaoF,OAAb;AACD;AACF,KAnamB;;AAsapB;;AAtaoB,sBAwapBvC,cAxaoB,2BAwaLtF,SAxaK,EAwaM;AACxB,aAAOK,cAAcL,UAAU8H,WAAV,EAAd,CAAP;AACD,KA1amB;;AAAA,sBA4apBlF,aA5aoB,4BA4aJ;AAAA;;AACd,UAAMmF,WAAW,KAAK5F,MAAL,CAAYxC,OAAZ,CAAoBqI,KAApB,CAA0B,GAA1B,CAAjB;;AAEAD,eAASE,OAAT,CAAiB,UAACtI,OAAD,EAAa;AAC5B,YAAIA,YAAY,OAAhB,EAAyB;AACvBd,YAAE,OAAKgD,OAAP,EAAgBqG,EAAhB,CACE,OAAK/E,WAAL,CAAiBtC,KAAjB,CAAuBK,KADzB,EAEE,OAAKiB,MAAL,CAAYpC,QAFd,EAGE,UAACkD,KAAD;AAAA,mBAAW,OAAKD,MAAL,CAAYC,KAAZ,CAAX;AAAA,WAHF;AAMD,SAPD,MAOO,IAAItD,YAAYoC,QAAQG,MAAxB,EAAgC;AACrC,cAAMiG,UAAWxI,YAAYoC,QAAQC,KAApB,GACf,OAAKmB,WAAL,CAAiBtC,KAAjB,CAAuBQ,UADR,GAEf,OAAK8B,WAAL,CAAiBtC,KAAjB,CAAuBM,OAFzB;AAGA,cAAMiH,WAAWzI,YAAYoC,QAAQC,KAApB,GACf,OAAKmB,WAAL,CAAiBtC,KAAjB,CAAuBS,UADR,GAEf,OAAK6B,WAAL,CAAiBtC,KAAjB,CAAuBO,QAFzB;;AAIAvC,YAAE,OAAKgD,OAAP,EACGqG,EADH,CAEIC,OAFJ,EAGI,OAAKhG,MAAL,CAAYpC,QAHhB,EAII,UAACkD,KAAD;AAAA,mBAAW,OAAKS,MAAL,CAAYT,KAAZ,CAAX;AAAA,WAJJ,EAMGiF,EANH,CAOIE,QAPJ,EAQI,OAAKjG,MAAL,CAAYpC,QARhB,EASI,UAACkD,KAAD;AAAA,mBAAW,OAAKU,MAAL,CAAYV,KAAZ,CAAX;AAAA,WATJ;AAWD;;AAEDpE,UAAE,OAAKgD,OAAP,EAAgBsC,OAAhB,CAAwB,QAAxB,EAAkC+D,EAAlC,CACE,eADF,EAEE;AAAA,iBAAM,OAAK3B,IAAL,EAAN;AAAA,SAFF;AAID,OAjCD;;AAmCA,UAAI,KAAKpE,MAAL,CAAYpC,QAAhB,EAA0B;AACxB,aAAKoC,MAAL,GAActD,EAAEwJ,MAAF,CAAS,EAAT,EAAa,KAAKlG,MAAlB,EAA0B;AACtCxC,mBAAW,QAD2B;AAEtCI,oBAAW;AAF2B,SAA1B,CAAd;AAID,OALD,MAKO;AACL,aAAKuI,SAAL;AACD;AACF,KA1dmB;;AAAA,sBA4dpBA,SA5doB,wBA4dR;AACV,UAAMC,oBAAmB,KAAK1G,OAAL,CAAa+F,YAAb,CAA0B,qBAA1B,CAAnB,CAAN;AACA,UAAI,KAAK/F,OAAL,CAAa+F,YAAb,CAA0B,OAA1B,KACDW,cAAc,QADjB,EAC2B;AACzB,aAAK1G,OAAL,CAAaoD,YAAb,CACE,qBADF,EAEE,KAAKpD,OAAL,CAAa+F,YAAb,CAA0B,OAA1B,KAAsC,EAFxC;AAIA,aAAK/F,OAAL,CAAaoD,YAAb,CAA0B,OAA1B,EAAmC,EAAnC;AACD;AACF,KAtemB;;AAAA,sBAwepBvB,MAxeoB,mBAwebT,KAxea,EAweNG,OAxeM,EAweG;AACrB,UAAMF,UAAU,KAAKC,WAAL,CAAiBjE,QAAjC;;AAEAkE,gBAAUA,WAAWvE,EAAEoE,MAAMI,aAAR,EAAuBC,IAAvB,CAA4BJ,OAA5B,CAArB;;AAEA,UAAI,CAACE,OAAL,EAAc;AACZA,kBAAU,IAAI,KAAKD,WAAT,CACRF,MAAMI,aADE,EAER,KAAKE,kBAAL,EAFQ,CAAV;AAIA1E,UAAEoE,MAAMI,aAAR,EAAuBC,IAAvB,CAA4BJ,OAA5B,EAAqCE,OAArC;AACD;;AAED,UAAIH,KAAJ,EAAW;AACTG,gBAAQb,cAAR,CACEU,MAAMuF,IAAN,KAAe,SAAf,GAA2BzG,QAAQE,KAAnC,GAA2CF,QAAQC,KADrD,IAEI,IAFJ;AAGD;;AAED,UAAInD,EAAEuE,QAAQQ,aAAR,EAAF,EAA2BC,QAA3B,CAAoCtC,UAAUZ,IAA9C,KACDyC,QAAQd,WAAR,KAAwB5B,WAAWC,IADtC,EAC4C;AAC1CyC,gBAAQd,WAAR,GAAsB5B,WAAWC,IAAjC;AACA;AACD;;AAEDoD,mBAAaX,QAAQf,QAArB;;AAEAe,cAAQd,WAAR,GAAsB5B,WAAWC,IAAjC;;AAEA,UAAI,CAACyC,QAAQjB,MAAR,CAAetC,KAAhB,IAAyB,CAACuD,QAAQjB,MAAR,CAAetC,KAAf,CAAqBwE,IAAnD,EAAyD;AACvDjB,gBAAQiB,IAAR;AACA;AACD;;AAEDjB,cAAQf,QAAR,GAAmBoG,WAAW,YAAM;AAClC,YAAIrF,QAAQd,WAAR,KAAwB5B,WAAWC,IAAvC,EAA6C;AAC3CyC,kBAAQiB,IAAR;AACD;AACF,OAJkB,EAIhBjB,QAAQjB,MAAR,CAAetC,KAAf,CAAqBwE,IAJL,CAAnB;AAKD,KA/gBmB;;AAAA,sBAihBpBV,MAjhBoB,mBAihBbV,KAjhBa,EAihBNG,OAjhBM,EAihBG;AACrB,UAAMF,UAAU,KAAKC,WAAL,CAAiBjE,QAAjC;;AAEAkE,gBAAUA,WAAWvE,EAAEoE,MAAMI,aAAR,EAAuBC,IAAvB,CAA4BJ,OAA5B,CAArB;;AAEA,UAAI,CAACE,OAAL,EAAc;AACZA,kBAAU,IAAI,KAAKD,WAAT,CACRF,MAAMI,aADE,EAER,KAAKE,kBAAL,EAFQ,CAAV;AAIA1E,UAAEoE,MAAMI,aAAR,EAAuBC,IAAvB,CAA4BJ,OAA5B,EAAqCE,OAArC;AACD;;AAED,UAAIH,KAAJ,EAAW;AACTG,gBAAQb,cAAR,CACEU,MAAMuF,IAAN,KAAe,UAAf,GAA4BzG,QAAQE,KAApC,GAA4CF,QAAQC,KADtD,IAEI,KAFJ;AAGD;;AAED,UAAIoB,QAAQK,oBAAR,EAAJ,EAAoC;AAClC;AACD;;AAEDM,mBAAaX,QAAQf,QAArB;;AAEAe,cAAQd,WAAR,GAAsB5B,WAAWE,GAAjC;;AAEA,UAAI,CAACwC,QAAQjB,MAAR,CAAetC,KAAhB,IAAyB,CAACuD,QAAQjB,MAAR,CAAetC,KAAf,CAAqB0G,IAAnD,EAAyD;AACvDnD,gBAAQmD,IAAR;AACA;AACD;;AAEDnD,cAAQf,QAAR,GAAmBoG,WAAW,YAAM;AAClC,YAAIrF,QAAQd,WAAR,KAAwB5B,WAAWE,GAAvC,EAA4C;AAC1CwC,kBAAQmD,IAAR;AACD;AACF,OAJkB,EAIhBnD,QAAQjB,MAAR,CAAetC,KAAf,CAAqB0G,IAJL,CAAnB;AAKD,KAtjBmB;;AAAA,sBAwjBpB9C,oBAxjBoB,mCAwjBG;AACrB,WAAK,IAAM9D,OAAX,IAAsB,KAAK4C,cAA3B,EAA2C;AACzC,YAAI,KAAKA,cAAL,CAAoB5C,OAApB,CAAJ,EAAkC;AAChC,iBAAO,IAAP;AACD;AACF;;AAED,aAAO,KAAP;AACD,KAhkBmB;;AAAA,sBAkkBpB+C,UAlkBoB,uBAkkBTP,MAlkBS,EAkkBD;AACjBA,eAAStD,EAAEwJ,MAAF,CACP,EADO,EAEP,KAAKlF,WAAL,CAAiB3D,OAFV,EAGPX,EAAE,KAAKgD,OAAP,EAAgByB,IAAhB,EAHO,EAIPnB,MAJO,CAAT;;AAOA,UAAIA,OAAOtC,KAAP,IAAgB,OAAOsC,OAAOtC,KAAd,KAAwB,QAA5C,EAAsD;AACpDsC,eAAOtC,KAAP,GAAe;AACbwE,gBAAOlC,OAAOtC,KADD;AAEb0G,gBAAOpE,OAAOtC;AAFD,SAAf;AAID;;AAEDkF,WAAK2D,eAAL,CACE1J,IADF,EAEEmD,MAFF,EAGE,KAAKgB,WAAL,CAAiB/C,WAHnB;;AAMA,aAAO+B,MAAP;AACD,KAxlBmB;;AAAA,sBA0lBpBoB,kBA1lBoB,iCA0lBC;AACnB,UAAMpB,SAAS,EAAf;;AAEA,UAAI,KAAKA,MAAT,EAAiB;AACf,aAAK,IAAMwG,GAAX,IAAkB,KAAKxG,MAAvB,EAA+B;AAC7B,cAAI,KAAKgB,WAAL,CAAiB3D,OAAjB,CAAyBmJ,GAAzB,MAAkC,KAAKxG,MAAL,CAAYwG,GAAZ,CAAtC,EAAwD;AACtDxG,mBAAOwG,GAAP,IAAc,KAAKxG,MAAL,CAAYwG,GAAZ,CAAd;AACD;AACF;AACF;;AAED,aAAOxG,MAAP;AACD,KAtmBmB;;AAymBpB;;AAzmBoB,YA2mBbyG,gBA3mBa,6BA2mBIzG,MA3mBJ,EA2mBY;AAC9B,aAAO,KAAK0G,IAAL,CAAU,YAAY;AAC3B,YAAIvF,OAAYzE,EAAE,IAAF,EAAQyE,IAAR,CAAapE,QAAb,CAAhB;AACA,YAAM4J,UAAU,QAAO3G,MAAP,yCAAOA,MAAP,OAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,YAAI,CAACmB,IAAD,IAAS,eAAeyF,IAAf,CAAoB5G,MAApB,CAAb,EAA0C;AACxC;AACD;;AAED,YAAI,CAACmB,IAAL,EAAW;AACTA,iBAAO,IAAI1E,OAAJ,CAAY,IAAZ,EAAkBkK,OAAlB,CAAP;AACAjK,YAAE,IAAF,EAAQyE,IAAR,CAAapE,QAAb,EAAuBoE,IAAvB;AACD;;AAED,YAAI,OAAOnB,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,cAAImB,KAAKnB,MAAL,MAAiB6G,SAArB,EAAgC;AAC9B,kBAAM,IAAIjK,KAAJ,uBAA8BoD,MAA9B,OAAN;AACD;AACDmB,eAAKnB,MAAL;AACD;AACF,OAnBM,CAAP;AAoBD,KAhoBmB;;AAAA;AAAA;AAAA,0BAoIC;AACnB,eAAOlD,OAAP;AACD;AAtImB;AAAA;AAAA,0BAwIC;AACnB,eAAOO,OAAP;AACD;AA1ImB;AAAA;AAAA,0BA4IF;AAChB,eAAOR,IAAP;AACD;AA9ImB;AAAA;AAAA,0BAgJE;AACpB,eAAOE,QAAP;AACD;AAlJmB;AAAA;AAAA,0BAoJD;AACjB,eAAO2B,KAAP;AACD;AAtJmB;AAAA;AAAA,0BAwJG;AACrB,eAAO1B,SAAP;AACD;AA1JmB;AAAA;AAAA,0BA4JK;AACvB,eAAOiB,WAAP;AACD;AA9JmB;;AAAA;AAAA;;AAqoBtB;;;;;;AAMAvB,IAAEQ,EAAF,CAAKL,IAAL,IAAyBJ,QAAQgK,gBAAjC;AACA/J,IAAEQ,EAAF,CAAKL,IAAL,EAAWiK,WAAX,GAAyBrK,OAAzB;AACAC,IAAEQ,EAAF,CAAKL,IAAL,EAAWkK,UAAX,GAAyB,YAAY;AACnCrK,MAAEQ,EAAF,CAAKL,IAAL,IAAaI,kBAAb;AACA,WAAOR,QAAQgK,gBAAf;AACD,GAHD;;AAKA,SAAOhK,OAAP;AAED,CAppBe,CAopBbuK,MAppBa,CAAhB,C,CAZA", "file": "tooltip.js", "sourcesContent": ["/* global Tether */\n\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-alpha.6): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Tooltip = (($) => {\n\n  /**\n   * Check for Tether dependency\n   * Tether - http://tether.io/\n   */\n  if (typeof Tether === 'undefined') {\n    throw new Error('Bootstrap tooltips require Tether (http://tether.io/)')\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'tooltip'\n  const VERSION             = '4.0.0-alpha.6'\n  const DATA_KEY            = 'bs.tooltip'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n  const CLASS_PREFIX        = 'bs-tether'\n\n  const Default = {\n    animation   : true,\n    template    : '<div class=\"tooltip\" role=\"tooltip\">'\n                + '<div class=\"tooltip-inner\"></div></div>',\n    trigger     : 'hover focus',\n    title       : '',\n    delay       : 0,\n    html        : false,\n    selector    : false,\n    placement   : 'top',\n    offset      : '0 0',\n    constraints : [],\n    container   : false\n  }\n\n  const DefaultType = {\n    animation   : 'boolean',\n    template    : 'string',\n    title       : '(string|element|function)',\n    trigger     : 'string',\n    delay       : '(number|object)',\n    html        : 'boolean',\n    selector    : '(string|boolean)',\n    placement   : '(string|function)',\n    offset      : 'string',\n    constraints : 'array',\n    container   : '(string|element|boolean)'\n  }\n\n  const AttachmentMap = {\n    TOP    : 'bottom center',\n    RIGHT  : 'middle left',\n    BOTTOM : 'top center',\n    LEFT   : 'middle right'\n  }\n\n  const HoverState = {\n    SHOW : 'show',\n    OUT  : 'out'\n  }\n\n  const Event = {\n    HIDE       : `hide${EVENT_KEY}`,\n    HIDDEN     : `hidden${EVENT_KEY}`,\n    SHOW       : `show${EVENT_KEY}`,\n    SHOWN      : `shown${EVENT_KEY}`,\n    INSERTED   : `inserted${EVENT_KEY}`,\n    CLICK      : `click${EVENT_KEY}`,\n    FOCUSIN    : `focusin${EVENT_KEY}`,\n    FOCUSOUT   : `focusout${EVENT_KEY}`,\n    MOUSEENTER : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE : `mouseleave${EVENT_KEY}`\n  }\n\n  const ClassName = {\n    FADE : 'fade',\n    SHOW : 'show'\n  }\n\n  const Selector = {\n    TOOLTIP       : '.tooltip',\n    TOOLTIP_INNER : '.tooltip-inner'\n  }\n\n  const TetherClass = {\n    element : false,\n    enabled : false\n  }\n\n  const Trigger = {\n    HOVER  : 'hover',\n    FOCUS  : 'focus',\n    CLICK  : 'click',\n    MANUAL : 'manual'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Tooltip {\n\n    constructor(element, config) {\n\n      // private\n      this._isEnabled        = true\n      this._timeout          = 0\n      this._hoverState       = ''\n      this._activeTrigger    = {}\n      this._isTransitioning  = false\n      this._tether           = null\n\n      // protected\n      this.element = element\n      this.config  = this._getConfig(config)\n      this.tip     = null\n\n      this._setListeners()\n\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get NAME() {\n      return NAME\n    }\n\n    static get DATA_KEY() {\n      return DATA_KEY\n    }\n\n    static get Event() {\n      return Event\n    }\n\n    static get EVENT_KEY() {\n      return EVENT_KEY\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n\n    // public\n\n    enable() {\n      this._isEnabled = true\n    }\n\n    disable() {\n      this._isEnabled = false\n    }\n\n    toggleEnabled() {\n      this._isEnabled = !this._isEnabled\n    }\n\n    toggle(event) {\n      if (event) {\n        const dataKey = this.constructor.DATA_KEY\n        let context = $(event.currentTarget).data(dataKey)\n\n        if (!context) {\n          context = new this.constructor(\n            event.currentTarget,\n            this._getDelegateConfig()\n          )\n          $(event.currentTarget).data(dataKey, context)\n        }\n\n        context._activeTrigger.click = !context._activeTrigger.click\n\n        if (context._isWithActiveTrigger()) {\n          context._enter(null, context)\n        } else {\n          context._leave(null, context)\n        }\n\n      } else {\n\n        if ($(this.getTipElement()).hasClass(ClassName.SHOW)) {\n          this._leave(null, this)\n          return\n        }\n\n        this._enter(null, this)\n      }\n    }\n\n    dispose() {\n      clearTimeout(this._timeout)\n\n      this.cleanupTether()\n\n      $.removeData(this.element, this.constructor.DATA_KEY)\n\n      $(this.element).off(this.constructor.EVENT_KEY)\n      $(this.element).closest('.modal').off('hide.bs.modal')\n\n      if (this.tip) {\n        $(this.tip).remove()\n      }\n\n      this._isEnabled     = null\n      this._timeout       = null\n      this._hoverState    = null\n      this._activeTrigger = null\n      this._tether        = null\n\n      this.element = null\n      this.config  = null\n      this.tip     = null\n    }\n\n    show() {\n      if ($(this.element).css('display') === 'none') {\n        throw new Error('Please use show on visible elements')\n      }\n\n      const showEvent = $.Event(this.constructor.Event.SHOW)\n      if (this.isWithContent() && this._isEnabled) {\n        if (this._isTransitioning) {\n          throw new Error('Tooltip is transitioning')\n        }\n        $(this.element).trigger(showEvent)\n\n        const isInTheDom = $.contains(\n          this.element.ownerDocument.documentElement,\n          this.element\n        )\n\n        if (showEvent.isDefaultPrevented() || !isInTheDom) {\n          return\n        }\n\n        const tip   = this.getTipElement()\n        const tipId = Util.getUID(this.constructor.NAME)\n\n        tip.setAttribute('id', tipId)\n        this.element.setAttribute('aria-describedby', tipId)\n\n        this.setContent()\n\n        if (this.config.animation) {\n          $(tip).addClass(ClassName.FADE)\n        }\n\n        const placement  = typeof this.config.placement === 'function' ?\n          this.config.placement.call(this, tip, this.element) :\n          this.config.placement\n\n        const attachment = this._getAttachment(placement)\n\n        const container = this.config.container === false ? document.body : $(this.config.container)\n\n        $(tip)\n          .data(this.constructor.DATA_KEY, this)\n          .appendTo(container)\n\n        $(this.element).trigger(this.constructor.Event.INSERTED)\n\n        this._tether = new Tether({\n          attachment,\n          element         : tip,\n          target          : this.element,\n          classes         : TetherClass,\n          classPrefix     : CLASS_PREFIX,\n          offset          : this.config.offset,\n          constraints     : this.config.constraints,\n          addTargetClasses: false\n        })\n\n        Util.reflow(tip)\n        this._tether.position()\n\n        $(tip).addClass(ClassName.SHOW)\n\n        const complete = () => {\n          const prevHoverState = this._hoverState\n          this._hoverState   = null\n          this._isTransitioning = false\n\n          $(this.element).trigger(this.constructor.Event.SHOWN)\n\n          if (prevHoverState === HoverState.OUT) {\n            this._leave(null, this)\n          }\n        }\n\n        if (Util.supportsTransitionEnd() && $(this.tip).hasClass(ClassName.FADE)) {\n          this._isTransitioning = true\n          $(this.tip)\n            .one(Util.TRANSITION_END, complete)\n            .emulateTransitionEnd(Tooltip._TRANSITION_DURATION)\n          return\n        }\n\n        complete()\n      }\n    }\n\n    hide(callback) {\n      const tip       = this.getTipElement()\n      const hideEvent = $.Event(this.constructor.Event.HIDE)\n      if (this._isTransitioning) {\n        throw new Error('Tooltip is transitioning')\n      }\n      const complete  = () => {\n        if (this._hoverState !== HoverState.SHOW && tip.parentNode) {\n          tip.parentNode.removeChild(tip)\n        }\n\n        this.element.removeAttribute('aria-describedby')\n        $(this.element).trigger(this.constructor.Event.HIDDEN)\n        this._isTransitioning = false\n        this.cleanupTether()\n\n        if (callback) {\n          callback()\n        }\n      }\n\n      $(this.element).trigger(hideEvent)\n\n      if (hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      $(tip).removeClass(ClassName.SHOW)\n\n      this._activeTrigger[Trigger.CLICK] = false\n      this._activeTrigger[Trigger.FOCUS] = false\n      this._activeTrigger[Trigger.HOVER] = false\n\n      if (Util.supportsTransitionEnd() &&\n          $(this.tip).hasClass(ClassName.FADE)) {\n        this._isTransitioning = true\n        $(tip)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n\n      } else {\n        complete()\n      }\n\n      this._hoverState = ''\n    }\n\n\n    // protected\n\n    isWithContent() {\n      return Boolean(this.getTitle())\n    }\n\n    getTipElement() {\n      return this.tip = this.tip || $(this.config.template)[0]\n    }\n\n    setContent() {\n      const $tip = $(this.getTipElement())\n\n      this.setElementContent($tip.find(Selector.TOOLTIP_INNER), this.getTitle())\n\n      $tip.removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n\n      this.cleanupTether()\n    }\n\n    setElementContent($element, content) {\n      const html = this.config.html\n      if (typeof content === 'object' && (content.nodeType || content.jquery)) {\n        // content is a DOM node or a jQuery\n        if (html) {\n          if (!$(content).parent().is($element)) {\n            $element.empty().append(content)\n          }\n        } else {\n          $element.text($(content).text())\n        }\n      } else {\n        $element[html ? 'html' : 'text'](content)\n      }\n    }\n\n    getTitle() {\n      let title = this.element.getAttribute('data-original-title')\n\n      if (!title) {\n        title = typeof this.config.title === 'function' ?\n          this.config.title.call(this.element) :\n          this.config.title\n      }\n\n      return title\n    }\n\n    cleanupTether() {\n      if (this._tether) {\n        this._tether.destroy()\n      }\n    }\n\n\n    // private\n\n    _getAttachment(placement) {\n      return AttachmentMap[placement.toUpperCase()]\n    }\n\n    _setListeners() {\n      const triggers = this.config.trigger.split(' ')\n\n      triggers.forEach((trigger) => {\n        if (trigger === 'click') {\n          $(this.element).on(\n            this.constructor.Event.CLICK,\n            this.config.selector,\n            (event) => this.toggle(event)\n          )\n\n        } else if (trigger !== Trigger.MANUAL) {\n          const eventIn  = trigger === Trigger.HOVER ?\n            this.constructor.Event.MOUSEENTER :\n            this.constructor.Event.FOCUSIN\n          const eventOut = trigger === Trigger.HOVER ?\n            this.constructor.Event.MOUSELEAVE :\n            this.constructor.Event.FOCUSOUT\n\n          $(this.element)\n            .on(\n              eventIn,\n              this.config.selector,\n              (event) => this._enter(event)\n            )\n            .on(\n              eventOut,\n              this.config.selector,\n              (event) => this._leave(event)\n            )\n        }\n\n        $(this.element).closest('.modal').on(\n          'hide.bs.modal',\n          () => this.hide()\n        )\n      })\n\n      if (this.config.selector) {\n        this.config = $.extend({}, this.config, {\n          trigger  : 'manual',\n          selector : ''\n        })\n      } else {\n        this._fixTitle()\n      }\n    }\n\n    _fixTitle() {\n      const titleType = typeof this.element.getAttribute('data-original-title')\n      if (this.element.getAttribute('title') ||\n         titleType !== 'string') {\n        this.element.setAttribute(\n          'data-original-title',\n          this.element.getAttribute('title') || ''\n        )\n        this.element.setAttribute('title', '')\n      }\n    }\n\n    _enter(event, context) {\n      const dataKey = this.constructor.DATA_KEY\n\n      context = context || $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      if (event) {\n        context._activeTrigger[\n          event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER\n        ] = true\n      }\n\n      if ($(context.getTipElement()).hasClass(ClassName.SHOW) ||\n         context._hoverState === HoverState.SHOW) {\n        context._hoverState = HoverState.SHOW\n        return\n      }\n\n      clearTimeout(context._timeout)\n\n      context._hoverState = HoverState.SHOW\n\n      if (!context.config.delay || !context.config.delay.show) {\n        context.show()\n        return\n      }\n\n      context._timeout = setTimeout(() => {\n        if (context._hoverState === HoverState.SHOW) {\n          context.show()\n        }\n      }, context.config.delay.show)\n    }\n\n    _leave(event, context) {\n      const dataKey = this.constructor.DATA_KEY\n\n      context = context || $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      if (event) {\n        context._activeTrigger[\n          event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER\n        ] = false\n      }\n\n      if (context._isWithActiveTrigger()) {\n        return\n      }\n\n      clearTimeout(context._timeout)\n\n      context._hoverState = HoverState.OUT\n\n      if (!context.config.delay || !context.config.delay.hide) {\n        context.hide()\n        return\n      }\n\n      context._timeout = setTimeout(() => {\n        if (context._hoverState === HoverState.OUT) {\n          context.hide()\n        }\n      }, context.config.delay.hide)\n    }\n\n    _isWithActiveTrigger() {\n      for (const trigger in this._activeTrigger) {\n        if (this._activeTrigger[trigger]) {\n          return true\n        }\n      }\n\n      return false\n    }\n\n    _getConfig(config) {\n      config = $.extend(\n        {},\n        this.constructor.Default,\n        $(this.element).data(),\n        config\n      )\n\n      if (config.delay && typeof config.delay === 'number') {\n        config.delay = {\n          show : config.delay,\n          hide : config.delay\n        }\n      }\n\n      Util.typeCheckConfig(\n        NAME,\n        config,\n        this.constructor.DefaultType\n      )\n\n      return config\n    }\n\n    _getDelegateConfig() {\n      const config = {}\n\n      if (this.config) {\n        for (const key in this.config) {\n          if (this.constructor.Default[key] !== this.config[key]) {\n            config[key] = this.config[key]\n          }\n        }\n      }\n\n      return config\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' && config\n\n        if (!data && /dispose|hide/.test(config)) {\n          return\n        }\n\n        if (!data) {\n          data = new Tooltip(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (data[config] === undefined) {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Tooltip._jQueryInterface\n  $.fn[NAME].Constructor = Tooltip\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tooltip._jQueryInterface\n  }\n\n  return Tooltip\n\n})(jQuery)\n\nexport default Tooltip\n"]}
{"version": 3, "sources": ["../src/alert.js"], "names": ["<PERSON><PERSON>", "$", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "fn", "TRANSITION_DURATION", "Selector", "DISMISS", "Event", "CLOSE", "CLOSED", "CLICK_DATA_API", "ClassName", "ALERT", "FADE", "SHOW", "element", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "isDefaultPrevented", "_removeElement", "dispose", "removeData", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "parent", "closest", "closeEvent", "trigger", "removeClass", "supportsTransitionEnd", "hasClass", "_destroyElement", "one", "TRANSITION_END", "event", "emulateTransitionEnd", "detach", "remove", "_jQueryInterface", "config", "each", "$element", "data", "_handleDismiss", "alertInstance", "preventDefault", "document", "on", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;AAGA;;;;;;;AAOA,IAAMA,QAAS,UAACC,CAAD,EAAO;;AAGpB;;;;;;AAMA,MAAMC,OAAsB,OAA5B;AACA,MAAMC,UAAsB,eAA5B;AACA,MAAMC,WAAsB,UAA5B;AACA,MAAMC,kBAA0BD,QAAhC;AACA,MAAME,eAAsB,WAA5B;AACA,MAAMC,qBAAsBN,EAAEO,EAAF,CAAKN,IAAL,CAA5B;AACA,MAAMO,sBAAsB,GAA5B;;AAEA,MAAMC,WAAW;AACfC,aAAU;AADK,GAAjB;;AAIA,MAAMC,QAAQ;AACZC,qBAAyBR,SADb;AAEZS,uBAA0BT,SAFd;AAGZU,8BAAyBV,SAAzB,GAAqCC;AAHzB,GAAd;;AAMA,MAAMU,YAAY;AAChBC,WAAQ,OADQ;AAEhBC,UAAQ,MAFQ;AAGhBC,UAAQ;AAHQ,GAAlB;;AAOA;;;;;;AAlCoB,MAwCdnB,KAxCc;AA0ClB,mBAAYoB,OAAZ,EAAqB;AAAA;;AACnB,WAAKC,QAAL,GAAgBD,OAAhB;AACD;;AAGD;;AAOA;;AAtDkB,oBAwDlBE,KAxDkB,kBAwDZF,OAxDY,EAwDH;AACbA,gBAAUA,WAAW,KAAKC,QAA1B;;AAEA,UAAME,cAAc,KAAKC,eAAL,CAAqBJ,OAArB,CAApB;AACA,UAAMK,cAAc,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;AAEA,UAAIE,YAAYE,kBAAZ,EAAJ,EAAsC;AACpC;AACD;;AAED,WAAKC,cAAL,CAAoBL,WAApB;AACD,KAnEiB;;AAAA,oBAqElBM,OArEkB,sBAqER;AACR5B,QAAE6B,UAAF,CAAa,KAAKT,QAAlB,EAA4BjB,QAA5B;AACA,WAAKiB,QAAL,GAAgB,IAAhB;AACD,KAxEiB;;AA2ElB;;AA3EkB,oBA6ElBG,eA7EkB,4BA6EFJ,OA7EE,EA6EO;AACvB,UAAMW,WAAWC,KAAKC,sBAAL,CAA4Bb,OAA5B,CAAjB;AACA,UAAIc,SAAa,KAAjB;;AAEA,UAAIH,QAAJ,EAAc;AACZG,iBAASjC,EAAE8B,QAAF,EAAY,CAAZ,CAAT;AACD;;AAED,UAAI,CAACG,MAAL,EAAa;AACXA,iBAASjC,EAAEmB,OAAF,EAAWe,OAAX,OAAuBnB,UAAUC,KAAjC,EAA0C,CAA1C,CAAT;AACD;;AAED,aAAOiB,MAAP;AACD,KA1FiB;;AAAA,oBA4FlBR,kBA5FkB,+BA4FCN,OA5FD,EA4FU;AAC1B,UAAMgB,aAAanC,EAAEW,KAAF,CAAQA,MAAMC,KAAd,CAAnB;;AAEAZ,QAAEmB,OAAF,EAAWiB,OAAX,CAAmBD,UAAnB;AACA,aAAOA,UAAP;AACD,KAjGiB;;AAAA,oBAmGlBR,cAnGkB,2BAmGHR,OAnGG,EAmGM;AAAA;;AACtBnB,QAAEmB,OAAF,EAAWkB,WAAX,CAAuBtB,UAAUG,IAAjC;;AAEA,UAAI,CAACa,KAAKO,qBAAL,EAAD,IACA,CAACtC,EAAEmB,OAAF,EAAWoB,QAAX,CAAoBxB,UAAUE,IAA9B,CADL,EAC0C;AACxC,aAAKuB,eAAL,CAAqBrB,OAArB;AACA;AACD;;AAEDnB,QAAEmB,OAAF,EACGsB,GADH,CACOV,KAAKW,cADZ,EAC4B,UAACC,KAAD;AAAA,eAAW,MAAKH,eAAL,CAAqBrB,OAArB,EAA8BwB,KAA9B,CAAX;AAAA,OAD5B,EAEGC,oBAFH,CAEwBpC,mBAFxB;AAGD,KA/GiB;;AAAA,oBAiHlBgC,eAjHkB,4BAiHFrB,OAjHE,EAiHO;AACvBnB,QAAEmB,OAAF,EACG0B,MADH,GAEGT,OAFH,CAEWzB,MAAME,MAFjB,EAGGiC,MAHH;AAID,KAtHiB;;AAyHlB;;AAzHkB,UA2HXC,gBA3HW,6BA2HMC,MA3HN,EA2Hc;AAC9B,aAAO,KAAKC,IAAL,CAAU,YAAY;AAC3B,YAAMC,WAAWlD,EAAE,IAAF,CAAjB;AACA,YAAImD,OAAaD,SAASC,IAAT,CAAchD,QAAd,CAAjB;;AAEA,YAAI,CAACgD,IAAL,EAAW;AACTA,iBAAO,IAAIpD,KAAJ,CAAU,IAAV,CAAP;AACAmD,mBAASC,IAAT,CAAchD,QAAd,EAAwBgD,IAAxB;AACD;;AAED,YAAIH,WAAW,OAAf,EAAwB;AACtBG,eAAKH,MAAL,EAAa,IAAb;AACD;AACF,OAZM,CAAP;AAaD,KAzIiB;;AAAA,UA2IXI,cA3IW,2BA2IIC,aA3IJ,EA2ImB;AACnC,aAAO,UAAUV,KAAV,EAAiB;AACtB,YAAIA,KAAJ,EAAW;AACTA,gBAAMW,cAAN;AACD;;AAEDD,sBAAchC,KAAd,CAAoB,IAApB;AACD,OAND;AAOD,KAnJiB;;AAAA;AAAA;AAAA,0BAiDG;AACnB,eAAOnB,OAAP;AACD;AAnDiB;;AAAA;AAAA;;AAwJpB;;;;;;AAMAF,IAAEuD,QAAF,EAAYC,EAAZ,CACE7C,MAAMG,cADR,EAEEL,SAASC,OAFX,EAGEX,MAAMqD,cAAN,CAAqB,IAAIrD,KAAJ,EAArB,CAHF;;AAOA;;;;;;AAMAC,IAAEO,EAAF,CAAKN,IAAL,IAAyBF,MAAMgD,gBAA/B;AACA/C,IAAEO,EAAF,CAAKN,IAAL,EAAWwD,WAAX,GAAyB1D,KAAzB;AACAC,IAAEO,EAAF,CAAKN,IAAL,EAAWyD,UAAX,GAAyB,YAAY;AACnC1D,MAAEO,EAAF,CAAKN,IAAL,IAAaK,kBAAb;AACA,WAAOP,MAAMgD,gBAAb;AACD,GAHD;;AAKA,SAAOhD,KAAP;AAED,CApLa,CAoLX4D,MApLW,CAAd", "file": "alert.js", "sourcesContent": ["import Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v4.0.0-alpha.6): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Alert = (($) => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'alert'\n  const VERSION             = '4.0.0-alpha.6'\n  const DATA_KEY            = 'bs.alert'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n\n  const Selector = {\n    DISMISS : '[data-dismiss=\"alert\"]'\n  }\n\n  const Event = {\n    CLOSE          : `close${EVENT_KEY}`,\n    CLOSED         : `closed${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    ALERT : 'alert',\n    FADE  : 'fade',\n    SHOW  : 'show'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Alert {\n\n    constructor(element) {\n      this._element = element\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n\n    // public\n\n    close(element) {\n      element = element || this._element\n\n      const rootElement = this._getRootElement(element)\n      const customEvent = this._triggerCloseEvent(rootElement)\n\n      if (customEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._removeElement(rootElement)\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n\n    // private\n\n    _getRootElement(element) {\n      const selector = Util.getSelectorFromElement(element)\n      let parent     = false\n\n      if (selector) {\n        parent = $(selector)[0]\n      }\n\n      if (!parent) {\n        parent = $(element).closest(`.${ClassName.ALERT}`)[0]\n      }\n\n      return parent\n    }\n\n    _triggerCloseEvent(element) {\n      const closeEvent = $.Event(Event.CLOSE)\n\n      $(element).trigger(closeEvent)\n      return closeEvent\n    }\n\n    _removeElement(element) {\n      $(element).removeClass(ClassName.SHOW)\n\n      if (!Util.supportsTransitionEnd() ||\n          !$(element).hasClass(ClassName.FADE)) {\n        this._destroyElement(element)\n        return\n      }\n\n      $(element)\n        .one(Util.TRANSITION_END, (event) => this._destroyElement(element, event))\n        .emulateTransitionEnd(TRANSITION_DURATION)\n    }\n\n    _destroyElement(element) {\n      $(element)\n        .detach()\n        .trigger(Event.CLOSED)\n        .remove()\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $element = $(this)\n        let data       = $element.data(DATA_KEY)\n\n        if (!data) {\n          data = new Alert(this)\n          $element.data(DATA_KEY, data)\n        }\n\n        if (config === 'close') {\n          data[config](this)\n        }\n      })\n    }\n\n    static _handleDismiss(alertInstance) {\n      return function (event) {\n        if (event) {\n          event.preventDefault()\n        }\n\n        alertInstance.close(this)\n      }\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(\n    Event.CLICK_DATA_API,\n    Selector.DISMISS,\n    Alert._handleDismiss(new Alert())\n  )\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Alert._jQueryInterface\n  $.fn[NAME].Constructor = Alert\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Alert._jQueryInterface\n  }\n\n  return Alert\n\n})(jQuery)\n\nexport default Alert\n"]}
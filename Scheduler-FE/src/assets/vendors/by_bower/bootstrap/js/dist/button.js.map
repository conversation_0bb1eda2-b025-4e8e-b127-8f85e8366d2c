{"version": 3, "sources": ["../src/button.js"], "names": ["<PERSON><PERSON>", "$", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "fn", "ClassName", "ACTIVE", "BUTTON", "FOCUS", "Selector", "DATA_TOGGLE_CARROT", "DATA_TOGGLE", "INPUT", "Event", "CLICK_DATA_API", "FOCUS_BLUR_DATA_API", "element", "_element", "toggle", "triggerChangeEvent", "rootElement", "closest", "input", "find", "type", "checked", "hasClass", "activeElement", "removeClass", "trigger", "focus", "setAttribute", "toggleClass", "dispose", "removeData", "_jQueryInterface", "config", "each", "data", "document", "on", "event", "preventDefault", "button", "target", "call", "test", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;AAAA;;;;;;;AAOA,IAAMA,SAAU,UAACC,CAAD,EAAO;;AAGrB;;;;;;AAMA,MAAMC,OAAsB,QAA5B;AACA,MAAMC,UAAsB,eAA5B;AACA,MAAMC,WAAsB,WAA5B;AACA,MAAMC,kBAA0BD,QAAhC;AACA,MAAME,eAAsB,WAA5B;AACA,MAAMC,qBAAsBN,EAAEO,EAAF,CAAKN,IAAL,CAA5B;;AAEA,MAAMO,YAAY;AAChBC,YAAS,QADO;AAEhBC,YAAS,KAFO;AAGhBC,WAAS;AAHO,GAAlB;;AAMA,MAAMC,WAAW;AACfC,wBAAqB,yBADN;AAEfC,iBAAqB,yBAFN;AAGfC,WAAqB,OAHN;AAIfN,YAAqB,SAJN;AAKfC,YAAqB;AALN,GAAjB;;AAQA,MAAMM,QAAQ;AACZC,8BAA8Bb,SAA9B,GAA0CC,YAD9B;AAEZa,yBAAsB,UAAQd,SAAR,GAAoBC,YAApB,mBACOD,SADP,GACmBC,YADnB;AAFV,GAAd;;AAOA;;;;;;AArCqB,MA2CfN,MA3Ce;AA6CnB,oBAAYoB,OAAZ,EAAqB;AAAA;;AACnB,WAAKC,QAAL,GAAgBD,OAAhB;AACD;;AAGD;;AAOA;;AAzDmB,qBA2DnBE,MA3DmB,qBA2DV;AACP,UAAIC,qBAAqB,IAAzB;AACA,UAAMC,cAAmBvB,EAAE,KAAKoB,QAAP,EAAiBI,OAAjB,CACvBZ,SAASE,WADc,EAEvB,CAFuB,CAAzB;;AAIA,UAAIS,WAAJ,EAAiB;AACf,YAAME,QAAQzB,EAAE,KAAKoB,QAAP,EAAiBM,IAAjB,CAAsBd,SAASG,KAA/B,EAAsC,CAAtC,CAAd;;AAEA,YAAIU,KAAJ,EAAW;AACT,cAAIA,MAAME,IAAN,KAAe,OAAnB,EAA4B;AAC1B,gBAAIF,MAAMG,OAAN,IACF5B,EAAE,KAAKoB,QAAP,EAAiBS,QAAjB,CAA0BrB,UAAUC,MAApC,CADF,EAC+C;AAC7Ca,mCAAqB,KAArB;AAED,aAJD,MAIO;AACL,kBAAMQ,gBAAgB9B,EAAEuB,WAAF,EAAeG,IAAf,CAAoBd,SAASH,MAA7B,EAAqC,CAArC,CAAtB;;AAEA,kBAAIqB,aAAJ,EAAmB;AACjB9B,kBAAE8B,aAAF,EAAiBC,WAAjB,CAA6BvB,UAAUC,MAAvC;AACD;AACF;AACF;;AAED,cAAIa,kBAAJ,EAAwB;AACtBG,kBAAMG,OAAN,GAAgB,CAAC5B,EAAE,KAAKoB,QAAP,EAAiBS,QAAjB,CAA0BrB,UAAUC,MAApC,CAAjB;AACAT,cAAEyB,KAAF,EAASO,OAAT,CAAiB,QAAjB;AACD;;AAEDP,gBAAMQ,KAAN;AACD;AAEF;;AAED,WAAKb,QAAL,CAAcc,YAAd,CAA2B,cAA3B,EACE,CAAClC,EAAE,KAAKoB,QAAP,EAAiBS,QAAjB,CAA0BrB,UAAUC,MAApC,CADH;;AAGA,UAAIa,kBAAJ,EAAwB;AACtBtB,UAAE,KAAKoB,QAAP,EAAiBe,WAAjB,CAA6B3B,UAAUC,MAAvC;AACD;AACF,KAnGkB;;AAAA,qBAqGnB2B,OArGmB,sBAqGT;AACRpC,QAAEqC,UAAF,CAAa,KAAKjB,QAAlB,EAA4BjB,QAA5B;AACA,WAAKiB,QAAL,GAAgB,IAAhB;AACD,KAxGkB;;AA2GnB;;AA3GmB,WA6GZkB,gBA7GY,6BA6GKC,MA7GL,EA6Ga;AAC9B,aAAO,KAAKC,IAAL,CAAU,YAAY;AAC3B,YAAIC,OAAOzC,EAAE,IAAF,EAAQyC,IAAR,CAAatC,QAAb,CAAX;;AAEA,YAAI,CAACsC,IAAL,EAAW;AACTA,iBAAO,IAAI1C,MAAJ,CAAW,IAAX,CAAP;AACAC,YAAE,IAAF,EAAQyC,IAAR,CAAatC,QAAb,EAAuBsC,IAAvB;AACD;;AAED,YAAIF,WAAW,QAAf,EAAyB;AACvBE,eAAKF,MAAL;AACD;AACF,OAXM,CAAP;AAYD,KA1HkB;;AAAA;AAAA;AAAA,0BAoDE;AACnB,eAAOrC,OAAP;AACD;AAtDkB;;AAAA;AAAA;;AA+HrB;;;;;;AAMAF,IAAE0C,QAAF,EACGC,EADH,CACM3B,MAAMC,cADZ,EAC4BL,SAASC,kBADrC,EACyD,UAAC+B,KAAD,EAAW;AAChEA,UAAMC,cAAN;;AAEA,QAAIC,SAASF,MAAMG,MAAnB;;AAEA,QAAI,CAAC/C,EAAE8C,MAAF,EAAUjB,QAAV,CAAmBrB,UAAUE,MAA7B,CAAL,EAA2C;AACzCoC,eAAS9C,EAAE8C,MAAF,EAAUtB,OAAV,CAAkBZ,SAASF,MAA3B,CAAT;AACD;;AAEDX,WAAOuC,gBAAP,CAAwBU,IAAxB,CAA6BhD,EAAE8C,MAAF,CAA7B,EAAwC,QAAxC;AACD,GAXH,EAYGH,EAZH,CAYM3B,MAAME,mBAZZ,EAYiCN,SAASC,kBAZ1C,EAY8D,UAAC+B,KAAD,EAAW;AACrE,QAAME,SAAS9C,EAAE4C,MAAMG,MAAR,EAAgBvB,OAAhB,CAAwBZ,SAASF,MAAjC,EAAyC,CAAzC,CAAf;AACAV,MAAE8C,MAAF,EAAUX,WAAV,CAAsB3B,UAAUG,KAAhC,EAAuC,eAAesC,IAAf,CAAoBL,MAAMjB,IAA1B,CAAvC;AACD,GAfH;;AAkBA;;;;;;AAMA3B,IAAEO,EAAF,CAAKN,IAAL,IAAyBF,OAAOuC,gBAAhC;AACAtC,IAAEO,EAAF,CAAKN,IAAL,EAAWiD,WAAX,GAAyBnD,MAAzB;AACAC,IAAEO,EAAF,CAAKN,IAAL,EAAWkD,UAAX,GAAyB,YAAY;AACnCnD,MAAEO,EAAF,CAAKN,IAAL,IAAaK,kBAAb;AACA,WAAOP,OAAOuC,gBAAd;AACD,GAHD;;AAKA,SAAOvC,MAAP;AAED,CAtKc,CAsKZqD,MAtKY,CAAf", "file": "button.js", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-alpha.6): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Button = (($) => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'button'\n  const VERSION             = '4.0.0-alpha.6'\n  const DATA_KEY            = 'bs.button'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n\n  const ClassName = {\n    ACTIVE : 'active',\n    BUTTON : 'btn',\n    FOCUS  : 'focus'\n  }\n\n  const Selector = {\n    DATA_TOGGLE_CARROT : '[data-toggle^=\"button\"]',\n    DATA_TOGGLE        : '[data-toggle=\"buttons\"]',\n    INPUT              : 'input',\n    ACTIVE             : '.active',\n    BUTTON             : '.btn'\n  }\n\n  const Event = {\n    CLICK_DATA_API      : `click${EVENT_KEY}${DATA_API_KEY}`,\n    FOCUS_BLUR_DATA_API : `focus${EVENT_KEY}${DATA_API_KEY} `\n                        + `blur${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Button {\n\n    constructor(element) {\n      this._element = element\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n\n    // public\n\n    toggle() {\n      let triggerChangeEvent = true\n      const rootElement      = $(this._element).closest(\n        Selector.DATA_TOGGLE\n      )[0]\n\n      if (rootElement) {\n        const input = $(this._element).find(Selector.INPUT)[0]\n\n        if (input) {\n          if (input.type === 'radio') {\n            if (input.checked &&\n              $(this._element).hasClass(ClassName.ACTIVE)) {\n              triggerChangeEvent = false\n\n            } else {\n              const activeElement = $(rootElement).find(Selector.ACTIVE)[0]\n\n              if (activeElement) {\n                $(activeElement).removeClass(ClassName.ACTIVE)\n              }\n            }\n          }\n\n          if (triggerChangeEvent) {\n            input.checked = !$(this._element).hasClass(ClassName.ACTIVE)\n            $(input).trigger('change')\n          }\n\n          input.focus()\n        }\n\n      }\n\n      this._element.setAttribute('aria-pressed',\n        !$(this._element).hasClass(ClassName.ACTIVE))\n\n      if (triggerChangeEvent) {\n        $(this._element).toggleClass(ClassName.ACTIVE)\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n\n        if (!data) {\n          data = new Button(this)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (config === 'toggle') {\n          data[config]()\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n      event.preventDefault()\n\n      let button = event.target\n\n      if (!$(button).hasClass(ClassName.BUTTON)) {\n        button = $(button).closest(Selector.BUTTON)\n      }\n\n      Button._jQueryInterface.call($(button), 'toggle')\n    })\n    .on(Event.FOCUS_BLUR_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n      const button = $(event.target).closest(Selector.BUTTON)[0]\n      $(button).toggleClass(ClassName.FOCUS, /^focus(in)?$/.test(event.type))\n    })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Button._jQueryInterface\n  $.fn[NAME].Constructor = Button\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Button._jQueryInterface\n  }\n\n  return Button\n\n})(jQuery)\n\nexport default Button\n"]}
{"version": 3, "sources": ["../src/dropdown.js"], "names": ["Dropdown", "$", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "fn", "ESCAPE_KEYCODE", "ARROW_UP_KEYCODE", "ARROW_DOWN_KEYCODE", "RIGHT_MOUSE_BUTTON_WHICH", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "CLICK", "CLICK_DATA_API", "FOCUSIN_DATA_API", "KEYDOWN_DATA_API", "ClassName", "BACKDROP", "DISABLED", "Selector", "DATA_TOGGLE", "FORM_CHILD", "ROLE_MENU", "ROLE_LISTBOX", "NAVBAR_NAV", "VISIBLE_ITEMS", "element", "_element", "_addEventListeners", "toggle", "disabled", "hasClass", "parent", "_getParentFromElement", "isActive", "_clearMenus", "document", "documentElement", "closest", "length", "dropdown", "createElement", "className", "insertBefore", "on", "relatedTarget", "showEvent", "trigger", "isDefaultPrevented", "focus", "setAttribute", "toggleClass", "dispose", "removeData", "off", "_jQueryInterface", "config", "each", "data", "undefined", "Error", "call", "event", "which", "backdrop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "toggles", "makeArray", "i", "type", "test", "target", "tagName", "contains", "hideEvent", "removeClass", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "_dataApiKeydownHandler", "preventDefault", "stopPropagation", "find", "items", "get", "index", "indexOf", "prototype", "e", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;AAGA;;;;;;;AAOA,IAAMA,WAAY,UAACC,CAAD,EAAO;;AAGvB;;;;;;AAMA,MAAMC,OAA2B,UAAjC;AACA,MAAMC,UAA2B,eAAjC;AACA,MAAMC,WAA2B,aAAjC;AACA,MAAMC,kBAA+BD,QAArC;AACA,MAAME,eAA2B,WAAjC;AACA,MAAMC,qBAA2BN,EAAEO,EAAF,CAAKN,IAAL,CAAjC;AACA,MAAMO,iBAA2B,EAAjC,CAfuB,CAea;AACpC,MAAMC,mBAA2B,EAAjC,CAhBuB,CAgBa;AACpC,MAAMC,qBAA2B,EAAjC,CAjBuB,CAiBa;AACpC,MAAMC,2BAA2B,CAAjC,CAlBuB,CAkBY;;AAEnC,MAAMC,QAAQ;AACZC,mBAA0BT,SADd;AAEZU,uBAA4BV,SAFhB;AAGZW,mBAA0BX,SAHd;AAIZY,qBAA2BZ,SAJf;AAKZa,qBAA2Bb,SALf;AAMZc,8BAA2Bd,SAA3B,GAAuCC,YAN3B;AAOZc,kCAA6Bf,SAA7B,GAAyCC,YAP7B;AAQZe,kCAA6BhB,SAA7B,GAAyCC;AAR7B,GAAd;;AAWA,MAAMgB,YAAY;AAChBC,cAAW,mBADK;AAEhBC,cAAW,UAFK;AAGhBR,UAAW;AAHK,GAAlB;;AAMA,MAAMS,WAAW;AACfF,cAAgB,oBADD;AAEfG,iBAAgB,0BAFD;AAGfC,gBAAgB,gBAHD;AAIfC,eAAgB,eAJD;AAKfC,kBAAgB,kBALD;AAMfC,gBAAgB,aAND;AAOfC,mBAAgB,wCACA;AARD,GAAjB;;AAYA;;;;;;AAjDuB,MAuDjB/B,QAvDiB;AAyDrB,sBAAYgC,OAAZ,EAAqB;AAAA;;AACnB,WAAKC,QAAL,GAAgBD,OAAhB;;AAEA,WAAKE,kBAAL;AACD;;AAGD;;AAOA;;AAvEqB,uBAyErBC,MAzEqB,qBAyEZ;AACP,UAAI,KAAKC,QAAL,IAAiBnC,EAAE,IAAF,EAAQoC,QAAR,CAAiBf,UAAUE,QAA3B,CAArB,EAA2D;AACzD,eAAO,KAAP;AACD;;AAED,UAAMc,SAAWtC,SAASuC,qBAAT,CAA+B,IAA/B,CAAjB;AACA,UAAMC,WAAWvC,EAAEqC,MAAF,EAAUD,QAAV,CAAmBf,UAAUN,IAA7B,CAAjB;;AAEAhB,eAASyC,WAAT;;AAEA,UAAID,QAAJ,EAAc;AACZ,eAAO,KAAP;AACD;;AAED,UAAI,kBAAkBE,SAASC,eAA3B,IACD,CAAC1C,EAAEqC,MAAF,EAAUM,OAAV,CAAkBnB,SAASK,UAA3B,EAAuCe,MAD3C,EACmD;;AAEjD;AACA,YAAMC,WAAeJ,SAASK,aAAT,CAAuB,KAAvB,CAArB;AACAD,iBAASE,SAAT,GAAqB1B,UAAUC,QAA/B;AACAtB,UAAE6C,QAAF,EAAYG,YAAZ,CAAyB,IAAzB;AACAhD,UAAE6C,QAAF,EAAYI,EAAZ,CAAe,OAAf,EAAwBlD,SAASyC,WAAjC;AACD;;AAED,UAAMU,gBAAgB;AACpBA,uBAAgB;AADI,OAAtB;AAGA,UAAMC,YAAgBnD,EAAEY,KAAF,CAAQA,MAAMG,IAAd,EAAoBmC,aAApB,CAAtB;;AAEAlD,QAAEqC,MAAF,EAAUe,OAAV,CAAkBD,SAAlB;;AAEA,UAAIA,UAAUE,kBAAV,EAAJ,EAAoC;AAClC,eAAO,KAAP;AACD;;AAED,WAAKC,KAAL;AACA,WAAKC,YAAL,CAAkB,eAAlB,EAAmC,IAAnC;;AAEAvD,QAAEqC,MAAF,EAAUmB,WAAV,CAAsBnC,UAAUN,IAAhC;AACAf,QAAEqC,MAAF,EAAUe,OAAV,CAAkBpD,EAAEY,KAAF,CAAQA,MAAMI,KAAd,EAAqBkC,aAArB,CAAlB;;AAEA,aAAO,KAAP;AACD,KAnHoB;;AAAA,uBAqHrBO,OArHqB,sBAqHX;AACRzD,QAAE0D,UAAF,CAAa,KAAK1B,QAAlB,EAA4B7B,QAA5B;AACAH,QAAE,KAAKgC,QAAP,EAAiB2B,GAAjB,CAAqBvD,SAArB;AACA,WAAK4B,QAAL,GAAgB,IAAhB;AACD,KAzHoB;;AA4HrB;;AA5HqB,uBA8HrBC,kBA9HqB,iCA8HA;AACnBjC,QAAE,KAAKgC,QAAP,EAAiBiB,EAAjB,CAAoBrC,MAAMK,KAA1B,EAAiC,KAAKiB,MAAtC;AACD,KAhIoB;;AAmIrB;;AAnIqB,aAqId0B,gBArIc,6BAqIGC,MArIH,EAqIW;AAC9B,aAAO,KAAKC,IAAL,CAAU,YAAY;AAC3B,YAAIC,OAAO/D,EAAE,IAAF,EAAQ+D,IAAR,CAAa5D,QAAb,CAAX;;AAEA,YAAI,CAAC4D,IAAL,EAAW;AACTA,iBAAO,IAAIhE,QAAJ,CAAa,IAAb,CAAP;AACAC,YAAE,IAAF,EAAQ+D,IAAR,CAAa5D,QAAb,EAAuB4D,IAAvB;AACD;;AAED,YAAI,OAAOF,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,cAAIE,KAAKF,MAAL,MAAiBG,SAArB,EAAgC;AAC9B,kBAAM,IAAIC,KAAJ,uBAA8BJ,MAA9B,OAAN;AACD;AACDE,eAAKF,MAAL,EAAaK,IAAb,CAAkB,IAAlB;AACD;AACF,OAdM,CAAP;AAeD,KArJoB;;AAAA,aAuJd1B,WAvJc,wBAuJF2B,KAvJE,EAuJK;AACxB,UAAIA,SAASA,MAAMC,KAAN,KAAgBzD,wBAA7B,EAAuD;AACrD;AACD;;AAED,UAAM0D,WAAWrE,EAAEwB,SAASF,QAAX,EAAqB,CAArB,CAAjB;AACA,UAAI+C,QAAJ,EAAc;AACZA,iBAASC,UAAT,CAAoBC,WAApB,CAAgCF,QAAhC;AACD;;AAED,UAAMG,UAAUxE,EAAEyE,SAAF,CAAYzE,EAAEwB,SAASC,WAAX,CAAZ,CAAhB;;AAEA,WAAK,IAAIiD,IAAI,CAAb,EAAgBA,IAAIF,QAAQ5B,MAA5B,EAAoC8B,GAApC,EAAyC;AACvC,YAAMrC,SAAgBtC,SAASuC,qBAAT,CAA+BkC,QAAQE,CAAR,CAA/B,CAAtB;AACA,YAAMxB,gBAAgB;AACpBA,yBAAgBsB,QAAQE,CAAR;AADI,SAAtB;;AAIA,YAAI,CAAC1E,EAAEqC,MAAF,EAAUD,QAAV,CAAmBf,UAAUN,IAA7B,CAAL,EAAyC;AACvC;AACD;;AAED,YAAIoD,UAAUA,MAAMQ,IAAN,KAAe,OAAf,IACV,kBAAkBC,IAAlB,CAAuBT,MAAMU,MAAN,CAAaC,OAApC,CADU,IACsCX,MAAMQ,IAAN,KAAe,SAD/D,KAEG3E,EAAE+E,QAAF,CAAW1C,MAAX,EAAmB8B,MAAMU,MAAzB,CAFP,EAEyC;AACvC;AACD;;AAED,YAAMG,YAAYhF,EAAEY,KAAF,CAAQA,MAAMC,IAAd,EAAoBqC,aAApB,CAAlB;AACAlD,UAAEqC,MAAF,EAAUe,OAAV,CAAkB4B,SAAlB;AACA,YAAIA,UAAU3B,kBAAV,EAAJ,EAAoC;AAClC;AACD;;AAEDmB,gBAAQE,CAAR,EAAWnB,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;;AAEAvD,UAAEqC,MAAF,EACG4C,WADH,CACe5D,UAAUN,IADzB,EAEGqC,OAFH,CAEWpD,EAAEY,KAAF,CAAQA,MAAME,MAAd,EAAsBoC,aAAtB,CAFX;AAGD;AACF,KA/LoB;;AAAA,aAiMdZ,qBAjMc,kCAiMQP,OAjMR,EAiMiB;AACpC,UAAIM,eAAJ;AACA,UAAM6C,WAAWC,KAAKC,sBAAL,CAA4BrD,OAA5B,CAAjB;;AAEA,UAAImD,QAAJ,EAAc;AACZ7C,iBAASrC,EAAEkF,QAAF,EAAY,CAAZ,CAAT;AACD;;AAED,aAAO7C,UAAUN,QAAQuC,UAAzB;AACD,KA1MoB;;AAAA,aA4Mde,sBA5Mc,mCA4MSlB,KA5MT,EA4MgB;AACnC,UAAI,CAAC,gBAAgBS,IAAhB,CAAqBT,MAAMC,KAA3B,CAAD,IACD,kBAAkBQ,IAAlB,CAAuBT,MAAMU,MAAN,CAAaC,OAApC,CADH,EACiD;AAC/C;AACD;;AAEDX,YAAMmB,cAAN;AACAnB,YAAMoB,eAAN;;AAEA,UAAI,KAAKpD,QAAL,IAAiBnC,EAAE,IAAF,EAAQoC,QAAR,CAAiBf,UAAUE,QAA3B,CAArB,EAA2D;AACzD;AACD;;AAED,UAAMc,SAAWtC,SAASuC,qBAAT,CAA+B,IAA/B,CAAjB;AACA,UAAMC,WAAWvC,EAAEqC,MAAF,EAAUD,QAAV,CAAmBf,UAAUN,IAA7B,CAAjB;;AAEA,UAAI,CAACwB,QAAD,IAAa4B,MAAMC,KAAN,KAAgB5D,cAA7B,IACC+B,YAAY4B,MAAMC,KAAN,KAAgB5D,cADjC,EACiD;;AAE/C,YAAI2D,MAAMC,KAAN,KAAgB5D,cAApB,EAAoC;AAClC,cAAM0B,SAASlC,EAAEqC,MAAF,EAAUmD,IAAV,CAAehE,SAASC,WAAxB,EAAqC,CAArC,CAAf;AACAzB,YAAEkC,MAAF,EAAUkB,OAAV,CAAkB,OAAlB;AACD;;AAEDpD,UAAE,IAAF,EAAQoD,OAAR,CAAgB,OAAhB;AACA;AACD;;AAED,UAAMqC,QAAQzF,EAAEqC,MAAF,EAAUmD,IAAV,CAAehE,SAASM,aAAxB,EAAuC4D,GAAvC,EAAd;;AAEA,UAAI,CAACD,MAAM7C,MAAX,EAAmB;AACjB;AACD;;AAED,UAAI+C,QAAQF,MAAMG,OAAN,CAAczB,MAAMU,MAApB,CAAZ;;AAEA,UAAIV,MAAMC,KAAN,KAAgB3D,gBAAhB,IAAoCkF,QAAQ,CAAhD,EAAmD;AAAE;AACnDA;AACD;;AAED,UAAIxB,MAAMC,KAAN,KAAgB1D,kBAAhB,IAAsCiF,QAAQF,MAAM7C,MAAN,GAAe,CAAjE,EAAoE;AAAE;AACpE+C;AACD;;AAED,UAAIA,QAAQ,CAAZ,EAAe;AACbA,gBAAQ,CAAR;AACD;;AAEDF,YAAME,KAAN,EAAarC,KAAb;AACD,KA7PoB;;AAAA;AAAA;AAAA,0BAkEA;AACnB,eAAOpD,OAAP;AACD;AApEoB;;AAAA;AAAA;;AAkQvB;;;;;;AAMAF,IAAEyC,QAAF,EACGQ,EADH,CACMrC,MAAMQ,gBADZ,EAC8BI,SAASC,WADvC,EACqD1B,SAASsF,sBAD9D,EAEGpC,EAFH,CAEMrC,MAAMQ,gBAFZ,EAE8BI,SAASG,SAFvC,EAEqD5B,SAASsF,sBAF9D,EAGGpC,EAHH,CAGMrC,MAAMQ,gBAHZ,EAG8BI,SAASI,YAHvC,EAGqD7B,SAASsF,sBAH9D,EAIGpC,EAJH,CAISrC,MAAMM,cAJf,SAIiCN,MAAMO,gBAJvC,EAI2DpB,SAASyC,WAJpE,EAKGS,EALH,CAKMrC,MAAMM,cALZ,EAK4BM,SAASC,WALrC,EAKkD1B,SAAS8F,SAAT,CAAmB3D,MALrE,EAMGe,EANH,CAMMrC,MAAMM,cANZ,EAM4BM,SAASE,UANrC,EAMiD,UAACoE,CAAD,EAAO;AACpDA,MAAEP,eAAF;AACD,GARH;;AAWA;;;;;;AAMAvF,IAAEO,EAAF,CAAKN,IAAL,IAAyBF,SAAS6D,gBAAlC;AACA5D,IAAEO,EAAF,CAAKN,IAAL,EAAW8F,WAAX,GAAyBhG,QAAzB;AACAC,IAAEO,EAAF,CAAKN,IAAL,EAAW+F,UAAX,GAAyB,YAAY;AACnChG,MAAEO,EAAF,CAAKN,IAAL,IAAaK,kBAAb;AACA,WAAOP,SAAS6D,gBAAhB;AACD,GAHD;;AAKA,SAAO7D,QAAP;AAED,CAlSgB,CAkSdkG,MAlSc,CAAjB", "file": "dropdown.js", "sourcesContent": ["import Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v4.0.0-alpha.6): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Dropdown = (($) => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                     = 'dropdown'\n  const VERSION                  = '4.0.0-alpha.6'\n  const DATA_KEY                 = 'bs.dropdown'\n  const EVENT_KEY                = `.${DATA_KEY}`\n  const DATA_API_KEY             = '.data-api'\n  const JQUERY_NO_CONFLICT       = $.fn[NAME]\n  const ESCAPE_KEYCODE           = 27 // KeyboardEvent.which value for Escape (Esc) key\n  const ARROW_UP_KEYCODE         = 38 // KeyboardEvent.which value for up arrow key\n  const ARROW_DOWN_KEYCODE       = 40 // KeyboardEvent.which value for down arrow key\n  const RIGHT_MOUSE_BUTTON_WHICH = 3 // MouseEvent.which value for the right button (assuming a right-handed mouse)\n\n  const Event = {\n    HIDE             : `hide${EVENT_KEY}`,\n    HIDDEN           : `hidden${EVENT_KEY}`,\n    SHOW             : `show${EVENT_KEY}`,\n    SHOWN            : `shown${EVENT_KEY}`,\n    CLICK            : `click${EVENT_KEY}`,\n    CLICK_DATA_API   : `click${EVENT_KEY}${DATA_API_KEY}`,\n    FOCUSIN_DATA_API : `focusin${EVENT_KEY}${DATA_API_KEY}`,\n    KEYDOWN_DATA_API : `keydown${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    BACKDROP : 'dropdown-backdrop',\n    DISABLED : 'disabled',\n    SHOW     : 'show'\n  }\n\n  const Selector = {\n    BACKDROP      : '.dropdown-backdrop',\n    DATA_TOGGLE   : '[data-toggle=\"dropdown\"]',\n    FORM_CHILD    : '.dropdown form',\n    ROLE_MENU     : '[role=\"menu\"]',\n    ROLE_LISTBOX  : '[role=\"listbox\"]',\n    NAVBAR_NAV    : '.navbar-nav',\n    VISIBLE_ITEMS : '[role=\"menu\"] li:not(.disabled) a, '\n                  + '[role=\"listbox\"] li:not(.disabled) a'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Dropdown {\n\n    constructor(element) {\n      this._element = element\n\n      this._addEventListeners()\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n\n    // public\n\n    toggle() {\n      if (this.disabled || $(this).hasClass(ClassName.DISABLED)) {\n        return false\n      }\n\n      const parent   = Dropdown._getParentFromElement(this)\n      const isActive = $(parent).hasClass(ClassName.SHOW)\n\n      Dropdown._clearMenus()\n\n      if (isActive) {\n        return false\n      }\n\n      if ('ontouchstart' in document.documentElement &&\n         !$(parent).closest(Selector.NAVBAR_NAV).length) {\n\n        // if mobile we use a backdrop because click events don't delegate\n        const dropdown     = document.createElement('div')\n        dropdown.className = ClassName.BACKDROP\n        $(dropdown).insertBefore(this)\n        $(dropdown).on('click', Dropdown._clearMenus)\n      }\n\n      const relatedTarget = {\n        relatedTarget : this\n      }\n      const showEvent     = $.Event(Event.SHOW, relatedTarget)\n\n      $(parent).trigger(showEvent)\n\n      if (showEvent.isDefaultPrevented()) {\n        return false\n      }\n\n      this.focus()\n      this.setAttribute('aria-expanded', true)\n\n      $(parent).toggleClass(ClassName.SHOW)\n      $(parent).trigger($.Event(Event.SHOWN, relatedTarget))\n\n      return false\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      $(this._element).off(EVENT_KEY)\n      this._element = null\n    }\n\n\n    // private\n\n    _addEventListeners() {\n      $(this._element).on(Event.CLICK, this.toggle)\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n\n        if (!data) {\n          data = new Dropdown(this)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (data[config] === undefined) {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config].call(this)\n        }\n      })\n    }\n\n    static _clearMenus(event) {\n      if (event && event.which === RIGHT_MOUSE_BUTTON_WHICH) {\n        return\n      }\n\n      const backdrop = $(Selector.BACKDROP)[0]\n      if (backdrop) {\n        backdrop.parentNode.removeChild(backdrop)\n      }\n\n      const toggles = $.makeArray($(Selector.DATA_TOGGLE))\n\n      for (let i = 0; i < toggles.length; i++) {\n        const parent        = Dropdown._getParentFromElement(toggles[i])\n        const relatedTarget = {\n          relatedTarget : toggles[i]\n        }\n\n        if (!$(parent).hasClass(ClassName.SHOW)) {\n          continue\n        }\n\n        if (event && (event.type === 'click' &&\n            /input|textarea/i.test(event.target.tagName) || event.type === 'focusin')\n            && $.contains(parent, event.target)) {\n          continue\n        }\n\n        const hideEvent = $.Event(Event.HIDE, relatedTarget)\n        $(parent).trigger(hideEvent)\n        if (hideEvent.isDefaultPrevented()) {\n          continue\n        }\n\n        toggles[i].setAttribute('aria-expanded', 'false')\n\n        $(parent)\n          .removeClass(ClassName.SHOW)\n          .trigger($.Event(Event.HIDDEN, relatedTarget))\n      }\n    }\n\n    static _getParentFromElement(element) {\n      let parent\n      const selector = Util.getSelectorFromElement(element)\n\n      if (selector) {\n        parent = $(selector)[0]\n      }\n\n      return parent || element.parentNode\n    }\n\n    static _dataApiKeydownHandler(event) {\n      if (!/(38|40|27|32)/.test(event.which) ||\n         /input|textarea/i.test(event.target.tagName)) {\n        return\n      }\n\n      event.preventDefault()\n      event.stopPropagation()\n\n      if (this.disabled || $(this).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      const parent   = Dropdown._getParentFromElement(this)\n      const isActive = $(parent).hasClass(ClassName.SHOW)\n\n      if (!isActive && event.which !== ESCAPE_KEYCODE ||\n           isActive && event.which === ESCAPE_KEYCODE) {\n\n        if (event.which === ESCAPE_KEYCODE) {\n          const toggle = $(parent).find(Selector.DATA_TOGGLE)[0]\n          $(toggle).trigger('focus')\n        }\n\n        $(this).trigger('click')\n        return\n      }\n\n      const items = $(parent).find(Selector.VISIBLE_ITEMS).get()\n\n      if (!items.length) {\n        return\n      }\n\n      let index = items.indexOf(event.target)\n\n      if (event.which === ARROW_UP_KEYCODE && index > 0) { // up\n        index--\n      }\n\n      if (event.which === ARROW_DOWN_KEYCODE && index < items.length - 1) { // down\n        index++\n      }\n\n      if (index < 0) {\n        index = 0\n      }\n\n      items[index].focus()\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.KEYDOWN_DATA_API, Selector.DATA_TOGGLE,  Dropdown._dataApiKeydownHandler)\n    .on(Event.KEYDOWN_DATA_API, Selector.ROLE_MENU,    Dropdown._dataApiKeydownHandler)\n    .on(Event.KEYDOWN_DATA_API, Selector.ROLE_LISTBOX, Dropdown._dataApiKeydownHandler)\n    .on(`${Event.CLICK_DATA_API} ${Event.FOCUSIN_DATA_API}`, Dropdown._clearMenus)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, Dropdown.prototype.toggle)\n    .on(Event.CLICK_DATA_API, Selector.FORM_CHILD, (e) => {\n      e.stopPropagation()\n    })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Dropdown._jQueryInterface\n  $.fn[NAME].Constructor = Dropdown\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Dropdown._jQueryInterface\n  }\n\n  return Dropdown\n\n})(jQuery)\n\nexport default Dropdown\n"]}
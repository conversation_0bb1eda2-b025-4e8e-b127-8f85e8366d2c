{"version": 3, "sources": ["../src/scrollspy.js"], "names": ["ScrollSpy", "$", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "fn", "<PERSON><PERSON><PERSON>", "offset", "method", "target", "DefaultType", "Event", "ACTIVATE", "SCROLL", "LOAD_DATA_API", "ClassName", "DROPDOWN_ITEM", "DROPDOWN_MENU", "NAV_LINK", "NAV", "ACTIVE", "Selector", "DATA_SPY", "LIST_ITEM", "LI", "LI_DROPDOWN", "NAV_LINKS", "DROPDOWN", "DROPDOWN_ITEMS", "DROPDOWN_TOGGLE", "OffsetMethod", "OFFSET", "POSITION", "element", "config", "_element", "_scrollElement", "tagName", "window", "_config", "_getConfig", "_selector", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "on", "event", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "makeArray", "map", "targetSelector", "<PERSON><PERSON>", "getSelectorFromElement", "offsetWidth", "offsetHeight", "top", "filter", "item", "sort", "a", "b", "for<PERSON>ach", "push", "dispose", "removeData", "off", "extend", "id", "attr", "getUID", "typeCheckConfig", "pageYOffset", "scrollTop", "scrollHeight", "Math", "max", "document", "body", "documentElement", "_getOffsetHeight", "innerHeight", "maxScroll", "length", "_activate", "_clear", "i", "isActiveTarget", "undefined", "queries", "split", "selector", "$link", "join", "hasClass", "closest", "find", "addClass", "parents", "trigger", "relatedTarget", "removeClass", "_jQueryInterface", "each", "data", "Error", "scrollSpys", "$spy", "call", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;AAGA;;;;;;;AAOA,IAAMA,YAAa,UAACC,CAAD,EAAO;;AAGxB;;;;;;AAMA,MAAMC,OAAqB,WAA3B;AACA,MAAMC,UAAqB,eAA3B;AACA,MAAMC,WAAqB,cAA3B;AACA,MAAMC,kBAAyBD,QAA/B;AACA,MAAME,eAAqB,WAA3B;AACA,MAAMC,qBAAqBN,EAAEO,EAAF,CAAKN,IAAL,CAA3B;;AAEA,MAAMO,UAAU;AACdC,YAAS,EADK;AAEdC,YAAS,MAFK;AAGdC,YAAS;AAHK,GAAhB;;AAMA,MAAMC,cAAc;AAClBH,YAAS,QADS;AAElBC,YAAS,QAFS;AAGlBC,YAAS;AAHS,GAApB;;AAMA,MAAME,QAAQ;AACZC,2BAA2BV,SADf;AAEZW,uBAAyBX,SAFb;AAGZY,4BAAuBZ,SAAvB,GAAmCC;AAHvB,GAAd;;AAMA,MAAMY,YAAY;AAChBC,mBAAgB,eADA;AAEhBC,mBAAgB,eAFA;AAGhBC,cAAgB,UAHA;AAIhBC,SAAgB,KAJA;AAKhBC,YAAgB;AALA,GAAlB;;AAQA,MAAMC,WAAW;AACfC,cAAkB,qBADH;AAEfF,YAAkB,SAFH;AAGfG,eAAkB,YAHH;AAIfC,QAAkB,IAJH;AAKfC,iBAAkB,aALH;AAMfC,eAAkB,WANH;AAOfC,cAAkB,WAPH;AAQfC,oBAAkB,gBARH;AASfC,qBAAkB;AATH,GAAjB;;AAYA,MAAMC,eAAe;AACnBC,YAAW,QADQ;AAEnBC,cAAW;AAFQ,GAArB;;AAMA;;;;;;AA5DwB,MAkElBnC,SAlEkB;AAoEtB,uBAAYoC,OAAZ,EAAqBC,MAArB,EAA6B;AAAA;;AAAA;;AAC3B,WAAKC,QAAL,GAAsBF,OAAtB;AACA,WAAKG,cAAL,GAAsBH,QAAQI,OAAR,KAAoB,MAApB,GAA6BC,MAA7B,GAAsCL,OAA5D;AACA,WAAKM,OAAL,GAAsB,KAAKC,UAAL,CAAgBN,MAAhB,CAAtB;AACA,WAAKO,SAAL,GAAyB,KAAKF,OAAL,CAAa9B,MAAhB,SAA0BY,SAASK,SAAnC,UACG,KAAKa,OAAL,CAAa9B,MADhB,SAC0BY,SAASO,cADnC,CAAtB;AAEA,WAAKc,QAAL,GAAsB,EAAtB;AACA,WAAKC,QAAL,GAAsB,EAAtB;AACA,WAAKC,aAAL,GAAsB,IAAtB;AACA,WAAKC,aAAL,GAAsB,CAAtB;;AAEA/C,QAAE,KAAKsC,cAAP,EAAuBU,EAAvB,CAA0BnC,MAAME,MAAhC,EAAwC,UAACkC,KAAD;AAAA,eAAW,MAAKC,QAAL,CAAcD,KAAd,CAAX;AAAA,OAAxC;;AAEA,WAAKE,OAAL;AACA,WAAKD,QAAL;AACD;;AAGD;;AAWA;;AAjGsB,wBAmGtBC,OAnGsB,sBAmGZ;AAAA;;AACR,UAAMC,aAAa,KAAKd,cAAL,KAAwB,KAAKA,cAAL,CAAoBE,MAA5C,GACjBR,aAAaE,QADI,GACOF,aAAaC,MADvC;;AAGA,UAAMoB,eAAe,KAAKZ,OAAL,CAAa/B,MAAb,KAAwB,MAAxB,GACnB0C,UADmB,GACN,KAAKX,OAAL,CAAa/B,MAD5B;;AAGA,UAAM4C,aAAaD,iBAAiBrB,aAAaE,QAA9B,GACjB,KAAKqB,aAAL,EADiB,GACM,CADzB;;AAGA,WAAKX,QAAL,GAAgB,EAAhB;AACA,WAAKC,QAAL,GAAgB,EAAhB;;AAEA,WAAKE,aAAL,GAAqB,KAAKS,gBAAL,EAArB;;AAEA,UAAMC,UAAUzD,EAAE0D,SAAF,CAAY1D,EAAE,KAAK2C,SAAP,CAAZ,CAAhB;;AAEAc,cACGE,GADH,CACO,UAACxB,OAAD,EAAa;AAChB,YAAIxB,eAAJ;AACA,YAAMiD,iBAAiBC,KAAKC,sBAAL,CAA4B3B,OAA5B,CAAvB;;AAEA,YAAIyB,cAAJ,EAAoB;AAClBjD,mBAASX,EAAE4D,cAAF,EAAkB,CAAlB,CAAT;AACD;;AAED,YAAIjD,WAAWA,OAAOoD,WAAP,IAAsBpD,OAAOqD,YAAxC,CAAJ,EAA2D;AACzD;AACA,iBAAO,CACLhE,EAAEW,MAAF,EAAU0C,YAAV,IAA0BY,GAA1B,GAAgCX,UAD3B,EAELM,cAFK,CAAP;AAID;AACD,eAAO,IAAP;AACD,OAjBH,EAkBGM,MAlBH,CAkBU,UAACC,IAAD;AAAA,eAAWA,IAAX;AAAA,OAlBV,EAmBGC,IAnBH,CAmBQ,UAACC,CAAD,EAAIC,CAAJ;AAAA,eAAaD,EAAE,CAAF,IAAOC,EAAE,CAAF,CAApB;AAAA,OAnBR,EAoBGC,OApBH,CAoBW,UAACJ,IAAD,EAAU;AACjB,eAAKvB,QAAL,CAAc4B,IAAd,CAAmBL,KAAK,CAAL,CAAnB;AACA,eAAKtB,QAAL,CAAc2B,IAAd,CAAmBL,KAAK,CAAL,CAAnB;AACD,OAvBH;AAwBD,KA5IqB;;AAAA,wBA8ItBM,OA9IsB,sBA8IZ;AACRzE,QAAE0E,UAAF,CAAa,KAAKrC,QAAlB,EAA4BlC,QAA5B;AACAH,QAAE,KAAKsC,cAAP,EAAuBqC,GAAvB,CAA2BvE,SAA3B;;AAEA,WAAKiC,QAAL,GAAsB,IAAtB;AACA,WAAKC,cAAL,GAAsB,IAAtB;AACA,WAAKG,OAAL,GAAsB,IAAtB;AACA,WAAKE,SAAL,GAAsB,IAAtB;AACA,WAAKC,QAAL,GAAsB,IAAtB;AACA,WAAKC,QAAL,GAAsB,IAAtB;AACA,WAAKC,aAAL,GAAsB,IAAtB;AACA,WAAKC,aAAL,GAAsB,IAAtB;AACD,KA1JqB;;AA6JtB;;AA7JsB,wBA+JtBL,UA/JsB,uBA+JXN,MA/JW,EA+JH;AACjBA,eAASpC,EAAE4E,MAAF,CAAS,EAAT,EAAapE,OAAb,EAAsB4B,MAAtB,CAAT;;AAEA,UAAI,OAAOA,OAAOzB,MAAd,KAAyB,QAA7B,EAAuC;AACrC,YAAIkE,KAAK7E,EAAEoC,OAAOzB,MAAT,EAAiBmE,IAAjB,CAAsB,IAAtB,CAAT;AACA,YAAI,CAACD,EAAL,EAAS;AACPA,eAAKhB,KAAKkB,MAAL,CAAY9E,IAAZ,CAAL;AACAD,YAAEoC,OAAOzB,MAAT,EAAiBmE,IAAjB,CAAsB,IAAtB,EAA4BD,EAA5B;AACD;AACDzC,eAAOzB,MAAP,SAAoBkE,EAApB;AACD;;AAEDhB,WAAKmB,eAAL,CAAqB/E,IAArB,EAA2BmC,MAA3B,EAAmCxB,WAAnC;;AAEA,aAAOwB,MAAP;AACD,KA9KqB;;AAAA,wBAgLtBmB,aAhLsB,4BAgLN;AACd,aAAO,KAAKjB,cAAL,KAAwBE,MAAxB,GACH,KAAKF,cAAL,CAAoB2C,WADjB,GAC+B,KAAK3C,cAAL,CAAoB4C,SAD1D;AAED,KAnLqB;;AAAA,wBAqLtB1B,gBArLsB,+BAqLH;AACjB,aAAO,KAAKlB,cAAL,CAAoB6C,YAApB,IAAoCC,KAAKC,GAAL,CACzCC,SAASC,IAAT,CAAcJ,YAD2B,EAEzCG,SAASE,eAAT,CAAyBL,YAFgB,CAA3C;AAID,KA1LqB;;AAAA,wBA4LtBM,gBA5LsB,+BA4LH;AACjB,aAAO,KAAKnD,cAAL,KAAwBE,MAAxB,GACHA,OAAOkD,WADJ,GACkB,KAAKpD,cAAL,CAAoB0B,YAD7C;AAED,KA/LqB;;AAAA,wBAiMtBd,QAjMsB,uBAiMX;AACT,UAAMgC,YAAe,KAAK3B,aAAL,KAAuB,KAAKd,OAAL,CAAahC,MAAzD;AACA,UAAM0E,eAAe,KAAK3B,gBAAL,EAArB;AACA,UAAMmC,YAAe,KAAKlD,OAAL,CAAahC,MAAb,GACjB0E,YADiB,GAEjB,KAAKM,gBAAL,EAFJ;;AAIA,UAAI,KAAK1C,aAAL,KAAuBoC,YAA3B,EAAyC;AACvC,aAAKhC,OAAL;AACD;;AAED,UAAI+B,aAAaS,SAAjB,EAA4B;AAC1B,YAAMhF,SAAS,KAAKkC,QAAL,CAAc,KAAKA,QAAL,CAAc+C,MAAd,GAAuB,CAArC,CAAf;;AAEA,YAAI,KAAK9C,aAAL,KAAuBnC,MAA3B,EAAmC;AACjC,eAAKkF,SAAL,CAAelF,MAAf;AACD;AACD;AACD;;AAED,UAAI,KAAKmC,aAAL,IAAsBoC,YAAY,KAAKtC,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;AAC9E,aAAKE,aAAL,GAAqB,IAArB;AACA,aAAKgD,MAAL;AACA;AACD;;AAED,WAAK,IAAIC,IAAI,KAAKnD,QAAL,CAAcgD,MAA3B,EAAmCG,GAAnC,GAAyC;AACvC,YAAMC,iBAAiB,KAAKlD,aAAL,KAAuB,KAAKD,QAAL,CAAckD,CAAd,CAAvB,IAChBb,aAAa,KAAKtC,QAAL,CAAcmD,CAAd,CADG,KAEf,KAAKnD,QAAL,CAAcmD,IAAI,CAAlB,MAAyBE,SAAzB,IACAf,YAAY,KAAKtC,QAAL,CAAcmD,IAAI,CAAlB,CAHG,CAAvB;;AAKA,YAAIC,cAAJ,EAAoB;AAClB,eAAKH,SAAL,CAAe,KAAKhD,QAAL,CAAckD,CAAd,CAAf;AACD;AACF;AACF,KArOqB;;AAAA,wBAuOtBF,SAvOsB,sBAuOZlF,MAvOY,EAuOJ;AAChB,WAAKmC,aAAL,GAAqBnC,MAArB;;AAEA,WAAKmF,MAAL;;AAEA,UAAII,UAAU,KAAKvD,SAAL,CAAewD,KAAf,CAAqB,GAArB,CAAd;AACAD,gBAAcA,QAAQvC,GAAR,CAAY,UAACyC,QAAD,EAAc;AACtC,eAAUA,QAAH,sBAA4BzF,MAA5B,YACGyF,QADH,eACqBzF,MADrB,QAAP;AAED,OAHa,CAAd;;AAKA,UAAM0F,QAAQrG,EAAEkG,QAAQI,IAAR,CAAa,GAAb,CAAF,CAAd;;AAEA,UAAID,MAAME,QAAN,CAAetF,UAAUC,aAAzB,CAAJ,EAA6C;AAC3CmF,cAAMG,OAAN,CAAcjF,SAASM,QAAvB,EAAiC4E,IAAjC,CAAsClF,SAASQ,eAA/C,EAAgE2E,QAAhE,CAAyEzF,UAAUK,MAAnF;AACA+E,cAAMK,QAAN,CAAezF,UAAUK,MAAzB;AACD,OAHD,MAGO;AACL;AACA;AACA+E,cAAMM,OAAN,CAAcpF,SAASG,EAAvB,EAA2B+E,IAA3B,QAAqClF,SAASK,SAA9C,EAA2D8E,QAA3D,CAAoEzF,UAAUK,MAA9E;AACD;;AAEDtB,QAAE,KAAKsC,cAAP,EAAuBsE,OAAvB,CAA+B/F,MAAMC,QAArC,EAA+C;AAC7C+F,uBAAelG;AAD8B,OAA/C;AAGD,KAhQqB;;AAAA,wBAkQtBmF,MAlQsB,qBAkQb;AACP9F,QAAE,KAAK2C,SAAP,EAAkBuB,MAAlB,CAAyB3C,SAASD,MAAlC,EAA0CwF,WAA1C,CAAsD7F,UAAUK,MAAhE;AACD,KApQqB;;AAuQtB;;AAvQsB,cAyQfyF,gBAzQe,6BAyQE3E,MAzQF,EAyQU;AAC9B,aAAO,KAAK4E,IAAL,CAAU,YAAY;AAC3B,YAAIC,OAAYjH,EAAE,IAAF,EAAQiH,IAAR,CAAa9G,QAAb,CAAhB;AACA,YAAMsC,UAAU,QAAOL,MAAP,yCAAOA,MAAP,OAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,YAAI,CAAC6E,IAAL,EAAW;AACTA,iBAAO,IAAIlH,SAAJ,CAAc,IAAd,EAAoB0C,OAApB,CAAP;AACAzC,YAAE,IAAF,EAAQiH,IAAR,CAAa9G,QAAb,EAAuB8G,IAAvB;AACD;;AAED,YAAI,OAAO7E,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,cAAI6E,KAAK7E,MAAL,MAAiB6D,SAArB,EAAgC;AAC9B,kBAAM,IAAIiB,KAAJ,uBAA8B9E,MAA9B,OAAN;AACD;AACD6E,eAAK7E,MAAL;AACD;AACF,OAfM,CAAP;AAgBD,KA1RqB;;AAAA;AAAA;AAAA,0BAwFD;AACnB,eAAOlC,OAAP;AACD;AA1FqB;AAAA;AAAA,0BA4FD;AACnB,eAAOM,OAAP;AACD;AA9FqB;;AAAA;AAAA;;AAgSxB;;;;;;AAMAR,IAAEwC,MAAF,EAAUQ,EAAV,CAAanC,MAAMG,aAAnB,EAAkC,YAAM;AACtC,QAAMmG,aAAanH,EAAE0D,SAAF,CAAY1D,EAAEuB,SAASC,QAAX,CAAZ,CAAnB;;AAEA,SAAK,IAAIuE,IAAIoB,WAAWvB,MAAxB,EAAgCG,GAAhC,GAAsC;AACpC,UAAMqB,OAAOpH,EAAEmH,WAAWpB,CAAX,CAAF,CAAb;AACAhG,gBAAUgH,gBAAV,CAA2BM,IAA3B,CAAgCD,IAAhC,EAAsCA,KAAKH,IAAL,EAAtC;AACD;AACF,GAPD;;AAUA;;;;;;AAMAjH,IAAEO,EAAF,CAAKN,IAAL,IAAyBF,UAAUgH,gBAAnC;AACA/G,IAAEO,EAAF,CAAKN,IAAL,EAAWqH,WAAX,GAAyBvH,SAAzB;AACAC,IAAEO,EAAF,CAAKN,IAAL,EAAWsH,UAAX,GAAyB,YAAY;AACnCvH,MAAEO,EAAF,CAAKN,IAAL,IAAaK,kBAAb;AACA,WAAOP,UAAUgH,gBAAjB;AACD,GAHD;;AAKA,SAAOhH,SAAP;AAED,CA/TiB,CA+TfyH,MA/Te,CAAlB", "file": "scrollspy.js", "sourcesContent": ["import Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-alpha.6): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst ScrollSpy = (($) => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME               = 'scrollspy'\n  const VERSION            = '4.0.0-alpha.6'\n  const DATA_KEY           = 'bs.scrollspy'\n  const EVENT_KEY          = `.${DATA_KEY}`\n  const DATA_API_KEY       = '.data-api'\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n\n  const Default = {\n    offset : 10,\n    method : 'auto',\n    target : ''\n  }\n\n  const DefaultType = {\n    offset : 'number',\n    method : 'string',\n    target : '(string|element)'\n  }\n\n  const Event = {\n    ACTIVATE      : `activate${EVENT_KEY}`,\n    SCROLL        : `scroll${EVENT_KEY}`,\n    LOAD_DATA_API : `load${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DROPDOWN_ITEM : 'dropdown-item',\n    DROPDOWN_MENU : 'dropdown-menu',\n    NAV_LINK      : 'nav-link',\n    NAV           : 'nav',\n    ACTIVE        : 'active'\n  }\n\n  const Selector = {\n    DATA_SPY        : '[data-spy=\"scroll\"]',\n    ACTIVE          : '.active',\n    LIST_ITEM       : '.list-item',\n    LI              : 'li',\n    LI_DROPDOWN     : 'li.dropdown',\n    NAV_LINKS       : '.nav-link',\n    DROPDOWN        : '.dropdown',\n    DROPDOWN_ITEMS  : '.dropdown-item',\n    DROPDOWN_TOGGLE : '.dropdown-toggle'\n  }\n\n  const OffsetMethod = {\n    OFFSET   : 'offset',\n    POSITION : 'position'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class ScrollSpy {\n\n    constructor(element, config) {\n      this._element       = element\n      this._scrollElement = element.tagName === 'BODY' ? window : element\n      this._config        = this._getConfig(config)\n      this._selector      = `${this._config.target} ${Selector.NAV_LINKS},`\n                          + `${this._config.target} ${Selector.DROPDOWN_ITEMS}`\n      this._offsets       = []\n      this._targets       = []\n      this._activeTarget  = null\n      this._scrollHeight  = 0\n\n      $(this._scrollElement).on(Event.SCROLL, (event) => this._process(event))\n\n      this.refresh()\n      this._process()\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n\n    // public\n\n    refresh() {\n      const autoMethod = this._scrollElement !== this._scrollElement.window ?\n        OffsetMethod.POSITION : OffsetMethod.OFFSET\n\n      const offsetMethod = this._config.method === 'auto' ?\n        autoMethod : this._config.method\n\n      const offsetBase = offsetMethod === OffsetMethod.POSITION ?\n        this._getScrollTop() : 0\n\n      this._offsets = []\n      this._targets = []\n\n      this._scrollHeight = this._getScrollHeight()\n\n      const targets = $.makeArray($(this._selector))\n\n      targets\n        .map((element) => {\n          let target\n          const targetSelector = Util.getSelectorFromElement(element)\n\n          if (targetSelector) {\n            target = $(targetSelector)[0]\n          }\n\n          if (target && (target.offsetWidth || target.offsetHeight)) {\n            // todo (fat): remove sketch reliance on jQuery position/offset\n            return [\n              $(target)[offsetMethod]().top + offsetBase,\n              targetSelector\n            ]\n          }\n          return null\n        })\n        .filter((item)  => item)\n        .sort((a, b)    => a[0] - b[0])\n        .forEach((item) => {\n          this._offsets.push(item[0])\n          this._targets.push(item[1])\n        })\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      $(this._scrollElement).off(EVENT_KEY)\n\n      this._element       = null\n      this._scrollElement = null\n      this._config        = null\n      this._selector      = null\n      this._offsets       = null\n      this._targets       = null\n      this._activeTarget  = null\n      this._scrollHeight  = null\n    }\n\n\n    // private\n\n    _getConfig(config) {\n      config = $.extend({}, Default, config)\n\n      if (typeof config.target !== 'string') {\n        let id = $(config.target).attr('id')\n        if (!id) {\n          id = Util.getUID(NAME)\n          $(config.target).attr('id', id)\n        }\n        config.target = `#${id}`\n      }\n\n      Util.typeCheckConfig(NAME, config, DefaultType)\n\n      return config\n    }\n\n    _getScrollTop() {\n      return this._scrollElement === window ?\n          this._scrollElement.pageYOffset : this._scrollElement.scrollTop\n    }\n\n    _getScrollHeight() {\n      return this._scrollElement.scrollHeight || Math.max(\n        document.body.scrollHeight,\n        document.documentElement.scrollHeight\n      )\n    }\n\n    _getOffsetHeight() {\n      return this._scrollElement === window ?\n          window.innerHeight : this._scrollElement.offsetHeight\n    }\n\n    _process() {\n      const scrollTop    = this._getScrollTop() + this._config.offset\n      const scrollHeight = this._getScrollHeight()\n      const maxScroll    = this._config.offset\n        + scrollHeight\n        - this._getOffsetHeight()\n\n      if (this._scrollHeight !== scrollHeight) {\n        this.refresh()\n      }\n\n      if (scrollTop >= maxScroll) {\n        const target = this._targets[this._targets.length - 1]\n\n        if (this._activeTarget !== target) {\n          this._activate(target)\n        }\n        return\n      }\n\n      if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n        this._activeTarget = null\n        this._clear()\n        return\n      }\n\n      for (let i = this._offsets.length; i--;) {\n        const isActiveTarget = this._activeTarget !== this._targets[i]\n            && scrollTop >= this._offsets[i]\n            && (this._offsets[i + 1] === undefined ||\n                scrollTop < this._offsets[i + 1])\n\n        if (isActiveTarget) {\n          this._activate(this._targets[i])\n        }\n      }\n    }\n\n    _activate(target) {\n      this._activeTarget = target\n\n      this._clear()\n\n      let queries = this._selector.split(',')\n      queries     = queries.map((selector) => {\n        return `${selector}[data-target=\"${target}\"],` +\n               `${selector}[href=\"${target}\"]`\n      })\n\n      const $link = $(queries.join(','))\n\n      if ($link.hasClass(ClassName.DROPDOWN_ITEM)) {\n        $link.closest(Selector.DROPDOWN).find(Selector.DROPDOWN_TOGGLE).addClass(ClassName.ACTIVE)\n        $link.addClass(ClassName.ACTIVE)\n      } else {\n        // todo (fat) this is kinda sus...\n        // recursively add actives to tested nav-links\n        $link.parents(Selector.LI).find(`> ${Selector.NAV_LINKS}`).addClass(ClassName.ACTIVE)\n      }\n\n      $(this._scrollElement).trigger(Event.ACTIVATE, {\n        relatedTarget: target\n      })\n    }\n\n    _clear() {\n      $(this._selector).filter(Selector.ACTIVE).removeClass(ClassName.ACTIVE)\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' && config\n\n        if (!data) {\n          data = new ScrollSpy(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (data[config] === undefined) {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(window).on(Event.LOAD_DATA_API, () => {\n    const scrollSpys = $.makeArray($(Selector.DATA_SPY))\n\n    for (let i = scrollSpys.length; i--;) {\n      const $spy = $(scrollSpys[i])\n      ScrollSpy._jQueryInterface.call($spy, $spy.data())\n    }\n  })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = ScrollSpy._jQueryInterface\n  $.fn[NAME].Constructor = ScrollSpy\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return ScrollSpy._jQueryInterface\n  }\n\n  return ScrollSpy\n\n})(jQuery)\n\nexport default ScrollSpy\n"]}
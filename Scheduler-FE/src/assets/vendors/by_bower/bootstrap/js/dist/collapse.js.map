{"version": 3, "sources": ["../src/collapse.js"], "names": ["Collapse", "$", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "fn", "TRANSITION_DURATION", "<PERSON><PERSON><PERSON>", "toggle", "parent", "DefaultType", "Event", "SHOW", "SHOWN", "HIDE", "HIDDEN", "CLICK_DATA_API", "ClassName", "COLLAPSE", "COLLAPSING", "COLLAPSED", "Dimension", "WIDTH", "HEIGHT", "Selector", "ACTIVES", "DATA_TOGGLE", "element", "config", "_isTransitioning", "_element", "_config", "_getConfig", "_triggerArray", "makeArray", "id", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hasClass", "hide", "show", "Error", "actives", "activesData", "find", "length", "data", "startEvent", "trigger", "isDefaultPrevented", "_jQueryInterface", "call", "dimension", "_getDimension", "removeClass", "addClass", "style", "setAttribute", "attr", "setTransitioning", "complete", "<PERSON><PERSON>", "supportsTransitionEnd", "capitalizedDimension", "toUpperCase", "slice", "scrollSize", "one", "TRANSITION_END", "emulateTransitionEnd", "offsetDimension", "reflow", "isTransitioning", "dispose", "removeData", "extend", "Boolean", "typeCheckConfig", "<PERSON><PERSON><PERSON><PERSON>", "selector", "each", "i", "_getTargetFromElement", "trigger<PERSON><PERSON>y", "isOpen", "toggleClass", "getSelectorFromElement", "$this", "test", "undefined", "document", "on", "event", "preventDefault", "target", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;AAGA;;;;;;;AAOA,IAAMA,WAAY,UAACC,CAAD,EAAO;;AAGvB;;;;;;AAMA,MAAMC,OAAsB,UAA5B;AACA,MAAMC,UAAsB,eAA5B;AACA,MAAMC,WAAsB,aAA5B;AACA,MAAMC,kBAA0BD,QAAhC;AACA,MAAME,eAAsB,WAA5B;AACA,MAAMC,qBAAsBN,EAAEO,EAAF,CAAKN,IAAL,CAA5B;AACA,MAAMO,sBAAsB,GAA5B;;AAEA,MAAMC,UAAU;AACdC,YAAS,IADK;AAEdC,YAAS;AAFK,GAAhB;;AAKA,MAAMC,cAAc;AAClBF,YAAS,SADS;AAElBC,YAAS;AAFS,GAApB;;AAKA,MAAME,QAAQ;AACZC,mBAAwBV,SADZ;AAEZW,qBAAyBX,SAFb;AAGZY,mBAAwBZ,SAHZ;AAIZa,uBAA0Bb,SAJd;AAKZc,8BAAyBd,SAAzB,GAAqCC;AALzB,GAAd;;AAQA,MAAMc,YAAY;AAChBL,UAAa,MADG;AAEhBM,cAAa,UAFG;AAGhBC,gBAAa,YAHG;AAIhBC,eAAa;AAJG,GAAlB;;AAOA,MAAMC,YAAY;AAChBC,WAAS,OADO;AAEhBC,YAAS;AAFO,GAAlB;;AAKA,MAAMC,WAAW;AACfC,aAAc,oCADC;AAEfC,iBAAc;AAFC,GAAjB;;AAMA;;;;;;AArDuB,MA2DjB7B,QA3DiB;AA6DrB,sBAAY8B,OAAZ,EAAqBC,MAArB,EAA6B;AAAA;;AAC3B,WAAKC,gBAAL,GAAwB,KAAxB;AACA,WAAKC,QAAL,GAAwBH,OAAxB;AACA,WAAKI,OAAL,GAAwB,KAAKC,UAAL,CAAgBJ,MAAhB,CAAxB;AACA,WAAKK,aAAL,GAAwBnC,EAAEoC,SAAF,CAAYpC,EAClC,qCAAmC6B,QAAQQ,EAA3C,wDAC0CR,QAAQQ,EADlD,QADkC,CAAZ,CAAxB;;AAKA,WAAKC,OAAL,GAAe,KAAKL,OAAL,CAAatB,MAAb,GAAsB,KAAK4B,UAAL,EAAtB,GAA0C,IAAzD;;AAEA,UAAI,CAAC,KAAKN,OAAL,CAAatB,MAAlB,EAA0B;AACxB,aAAK6B,yBAAL,CAA+B,KAAKR,QAApC,EAA8C,KAAKG,aAAnD;AACD;;AAED,UAAI,KAAKF,OAAL,CAAavB,MAAjB,EAAyB;AACvB,aAAKA,MAAL;AACD;AACF;;AAGD;;AAWA;;AA7FqB,uBA+FrBA,MA/FqB,qBA+FZ;AACP,UAAIV,EAAE,KAAKgC,QAAP,EAAiBS,QAAjB,CAA0BtB,UAAUL,IAApC,CAAJ,EAA+C;AAC7C,aAAK4B,IAAL;AACD,OAFD,MAEO;AACL,aAAKC,IAAL;AACD;AACF,KArGoB;;AAAA,uBAuGrBA,IAvGqB,mBAuGd;AAAA;;AACL,UAAI,KAAKZ,gBAAT,EAA2B;AACzB,cAAM,IAAIa,KAAJ,CAAU,2BAAV,CAAN;AACD;;AAED,UAAI5C,EAAE,KAAKgC,QAAP,EAAiBS,QAAjB,CAA0BtB,UAAUL,IAApC,CAAJ,EAA+C;AAC7C;AACD;;AAED,UAAI+B,gBAAJ;AACA,UAAIC,oBAAJ;;AAEA,UAAI,KAAKR,OAAT,EAAkB;AAChBO,kBAAU7C,EAAEoC,SAAF,CAAYpC,EAAE,KAAKsC,OAAP,EAAgBS,IAAhB,CAAqBrB,SAASC,OAA9B,CAAZ,CAAV;AACA,YAAI,CAACkB,QAAQG,MAAb,EAAqB;AACnBH,oBAAU,IAAV;AACD;AACF;;AAED,UAAIA,OAAJ,EAAa;AACXC,sBAAc9C,EAAE6C,OAAF,EAAWI,IAAX,CAAgB9C,QAAhB,CAAd;AACA,YAAI2C,eAAeA,YAAYf,gBAA/B,EAAiD;AAC/C;AACD;AACF;;AAED,UAAMmB,aAAalD,EAAEa,KAAF,CAAQA,MAAMC,IAAd,CAAnB;AACAd,QAAE,KAAKgC,QAAP,EAAiBmB,OAAjB,CAAyBD,UAAzB;AACA,UAAIA,WAAWE,kBAAX,EAAJ,EAAqC;AACnC;AACD;;AAED,UAAIP,OAAJ,EAAa;AACX9C,iBAASsD,gBAAT,CAA0BC,IAA1B,CAA+BtD,EAAE6C,OAAF,CAA/B,EAA2C,MAA3C;AACA,YAAI,CAACC,WAAL,EAAkB;AAChB9C,YAAE6C,OAAF,EAAWI,IAAX,CAAgB9C,QAAhB,EAA0B,IAA1B;AACD;AACF;;AAED,UAAMoD,YAAY,KAAKC,aAAL,EAAlB;;AAEAxD,QAAE,KAAKgC,QAAP,EACGyB,WADH,CACetC,UAAUC,QADzB,EAEGsC,QAFH,CAEYvC,UAAUE,UAFtB;;AAIA,WAAKW,QAAL,CAAc2B,KAAd,CAAoBJ,SAApB,IAAiC,CAAjC;AACA,WAAKvB,QAAL,CAAc4B,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;AAEA,UAAI,KAAKzB,aAAL,CAAmBa,MAAvB,EAA+B;AAC7BhD,UAAE,KAAKmC,aAAP,EACGsB,WADH,CACetC,UAAUG,SADzB,EAEGuC,IAFH,CAEQ,eAFR,EAEyB,IAFzB;AAGD;;AAED,WAAKC,gBAAL,CAAsB,IAAtB;;AAEA,UAAMC,WAAW,SAAXA,QAAW,GAAM;AACrB/D,UAAE,MAAKgC,QAAP,EACGyB,WADH,CACetC,UAAUE,UADzB,EAEGqC,QAFH,CAEYvC,UAAUC,QAFtB,EAGGsC,QAHH,CAGYvC,UAAUL,IAHtB;;AAKA,cAAKkB,QAAL,CAAc2B,KAAd,CAAoBJ,SAApB,IAAiC,EAAjC;;AAEA,cAAKO,gBAAL,CAAsB,KAAtB;;AAEA9D,UAAE,MAAKgC,QAAP,EAAiBmB,OAAjB,CAAyBtC,MAAME,KAA/B;AACD,OAXD;;AAaA,UAAI,CAACiD,KAAKC,qBAAL,EAAL,EAAmC;AACjCF;AACA;AACD;;AAED,UAAMG,uBAAuBX,UAAU,CAAV,EAAaY,WAAb,KAA6BZ,UAAUa,KAAV,CAAgB,CAAhB,CAA1D;AACA,UAAMC,wBAAgCH,oBAAtC;;AAEAlE,QAAE,KAAKgC,QAAP,EACGsC,GADH,CACON,KAAKO,cADZ,EAC4BR,QAD5B,EAEGS,oBAFH,CAEwBhE,mBAFxB;;AAIA,WAAKwB,QAAL,CAAc2B,KAAd,CAAoBJ,SAApB,IAAoC,KAAKvB,QAAL,CAAcqC,UAAd,CAApC;AACD,KAzLoB;;AAAA,uBA2LrB3B,IA3LqB,mBA2Ld;AAAA;;AACL,UAAI,KAAKX,gBAAT,EAA2B;AACzB,cAAM,IAAIa,KAAJ,CAAU,2BAAV,CAAN;AACD;;AAED,UAAI,CAAC5C,EAAE,KAAKgC,QAAP,EAAiBS,QAAjB,CAA0BtB,UAAUL,IAApC,CAAL,EAAgD;AAC9C;AACD;;AAED,UAAMoC,aAAalD,EAAEa,KAAF,CAAQA,MAAMG,IAAd,CAAnB;AACAhB,QAAE,KAAKgC,QAAP,EAAiBmB,OAAjB,CAAyBD,UAAzB;AACA,UAAIA,WAAWE,kBAAX,EAAJ,EAAqC;AACnC;AACD;;AAED,UAAMG,YAAkB,KAAKC,aAAL,EAAxB;AACA,UAAMiB,kBAAkBlB,cAAchC,UAAUC,KAAxB,GACtB,aADsB,GACN,cADlB;;AAGA,WAAKQ,QAAL,CAAc2B,KAAd,CAAoBJ,SAApB,IAAoC,KAAKvB,QAAL,CAAcyC,eAAd,CAApC;;AAEAT,WAAKU,MAAL,CAAY,KAAK1C,QAAjB;;AAEAhC,QAAE,KAAKgC,QAAP,EACG0B,QADH,CACYvC,UAAUE,UADtB,EAEGoC,WAFH,CAEetC,UAAUC,QAFzB,EAGGqC,WAHH,CAGetC,UAAUL,IAHzB;;AAKA,WAAKkB,QAAL,CAAc4B,YAAd,CAA2B,eAA3B,EAA4C,KAA5C;;AAEA,UAAI,KAAKzB,aAAL,CAAmBa,MAAvB,EAA+B;AAC7BhD,UAAE,KAAKmC,aAAP,EACGuB,QADH,CACYvC,UAAUG,SADtB,EAEGuC,IAFH,CAEQ,eAFR,EAEyB,KAFzB;AAGD;;AAED,WAAKC,gBAAL,CAAsB,IAAtB;;AAEA,UAAMC,WAAW,SAAXA,QAAW,GAAM;AACrB,eAAKD,gBAAL,CAAsB,KAAtB;AACA9D,UAAE,OAAKgC,QAAP,EACGyB,WADH,CACetC,UAAUE,UADzB,EAEGqC,QAFH,CAEYvC,UAAUC,QAFtB,EAGG+B,OAHH,CAGWtC,MAAMI,MAHjB;AAID,OAND;;AAQA,WAAKe,QAAL,CAAc2B,KAAd,CAAoBJ,SAApB,IAAiC,EAAjC;;AAEA,UAAI,CAACS,KAAKC,qBAAL,EAAL,EAAmC;AACjCF;AACA;AACD;;AAED/D,QAAE,KAAKgC,QAAP,EACGsC,GADH,CACON,KAAKO,cADZ,EAC4BR,QAD5B,EAEGS,oBAFH,CAEwBhE,mBAFxB;AAGD,KAnPoB;;AAAA,uBAqPrBsD,gBArPqB,6BAqPJa,eArPI,EAqPa;AAChC,WAAK5C,gBAAL,GAAwB4C,eAAxB;AACD,KAvPoB;;AAAA,uBAyPrBC,OAzPqB,sBAyPX;AACR5E,QAAE6E,UAAF,CAAa,KAAK7C,QAAlB,EAA4B7B,QAA5B;;AAEA,WAAK8B,OAAL,GAAwB,IAAxB;AACA,WAAKK,OAAL,GAAwB,IAAxB;AACA,WAAKN,QAAL,GAAwB,IAAxB;AACA,WAAKG,aAAL,GAAwB,IAAxB;AACA,WAAKJ,gBAAL,GAAwB,IAAxB;AACD,KAjQoB;;AAoQrB;;AApQqB,uBAsQrBG,UAtQqB,uBAsQVJ,MAtQU,EAsQF;AACjBA,eAAS9B,EAAE8E,MAAF,CAAS,EAAT,EAAarE,OAAb,EAAsBqB,MAAtB,CAAT;AACAA,aAAOpB,MAAP,GAAgBqE,QAAQjD,OAAOpB,MAAf,CAAhB,CAFiB,CAEsB;AACvCsD,WAAKgB,eAAL,CAAqB/E,IAArB,EAA2B6B,MAA3B,EAAmClB,WAAnC;AACA,aAAOkB,MAAP;AACD,KA3QoB;;AAAA,uBA6QrB0B,aA7QqB,4BA6QL;AACd,UAAMyB,WAAWjF,EAAE,KAAKgC,QAAP,EAAiBS,QAAjB,CAA0BlB,UAAUC,KAApC,CAAjB;AACA,aAAOyD,WAAW1D,UAAUC,KAArB,GAA6BD,UAAUE,MAA9C;AACD,KAhRoB;;AAAA,uBAkRrBc,UAlRqB,yBAkRR;AAAA;;AACX,UAAM5B,SAAWX,EAAE,KAAKiC,OAAL,CAAatB,MAAf,EAAuB,CAAvB,CAAjB;AACA,UAAMuE,sDACqC,KAAKjD,OAAL,CAAatB,MADlD,OAAN;;AAGAX,QAAEW,MAAF,EAAUoC,IAAV,CAAemC,QAAf,EAAyBC,IAAzB,CAA8B,UAACC,CAAD,EAAIvD,OAAJ,EAAgB;AAC5C,eAAKW,yBAAL,CACEzC,SAASsF,qBAAT,CAA+BxD,OAA/B,CADF,EAEE,CAACA,OAAD,CAFF;AAID,OALD;;AAOA,aAAOlB,MAAP;AACD,KA/RoB;;AAAA,uBAiSrB6B,yBAjSqB,sCAiSKX,OAjSL,EAiScyD,YAjSd,EAiS4B;AAC/C,UAAIzD,OAAJ,EAAa;AACX,YAAM0D,SAASvF,EAAE6B,OAAF,EAAWY,QAAX,CAAoBtB,UAAUL,IAA9B,CAAf;AACAe,gBAAQ+B,YAAR,CAAqB,eAArB,EAAsC2B,MAAtC;;AAEA,YAAID,aAAatC,MAAjB,EAAyB;AACvBhD,YAAEsF,YAAF,EACGE,WADH,CACerE,UAAUG,SADzB,EACoC,CAACiE,MADrC,EAEG1B,IAFH,CAEQ,eAFR,EAEyB0B,MAFzB;AAGD;AACF;AACF,KA5SoB;;AA+SrB;;AA/SqB,aAiTdF,qBAjTc,kCAiTQxD,OAjTR,EAiTiB;AACpC,UAAMqD,WAAWlB,KAAKyB,sBAAL,CAA4B5D,OAA5B,CAAjB;AACA,aAAOqD,WAAWlF,EAAEkF,QAAF,EAAY,CAAZ,CAAX,GAA4B,IAAnC;AACD,KApToB;;AAAA,aAsTd7B,gBAtTc,6BAsTGvB,MAtTH,EAsTW;AAC9B,aAAO,KAAKqD,IAAL,CAAU,YAAY;AAC3B,YAAMO,QAAU1F,EAAE,IAAF,CAAhB;AACA,YAAIiD,OAAYyC,MAAMzC,IAAN,CAAW9C,QAAX,CAAhB;AACA,YAAM8B,UAAUjC,EAAE8E,MAAF,CACd,EADc,EAEdrE,OAFc,EAGdiF,MAAMzC,IAAN,EAHc,EAId,QAAOnB,MAAP,yCAAOA,MAAP,OAAkB,QAAlB,IAA8BA,MAJhB,CAAhB;;AAOA,YAAI,CAACmB,IAAD,IAAShB,QAAQvB,MAAjB,IAA2B,YAAYiF,IAAZ,CAAiB7D,MAAjB,CAA/B,EAAyD;AACvDG,kBAAQvB,MAAR,GAAiB,KAAjB;AACD;;AAED,YAAI,CAACuC,IAAL,EAAW;AACTA,iBAAO,IAAIlD,QAAJ,CAAa,IAAb,EAAmBkC,OAAnB,CAAP;AACAyD,gBAAMzC,IAAN,CAAW9C,QAAX,EAAqB8C,IAArB;AACD;;AAED,YAAI,OAAOnB,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,cAAImB,KAAKnB,MAAL,MAAiB8D,SAArB,EAAgC;AAC9B,kBAAM,IAAIhD,KAAJ,uBAA8Bd,MAA9B,OAAN;AACD;AACDmB,eAAKnB,MAAL;AACD;AACF,OAzBM,CAAP;AA0BD,KAjVoB;;AAAA;AAAA;AAAA,0BAoFA;AACnB,eAAO5B,OAAP;AACD;AAtFoB;AAAA;AAAA,0BAwFA;AACnB,eAAOO,OAAP;AACD;AA1FoB;;AAAA;AAAA;;AAsVvB;;;;;;AAMAT,IAAE6F,QAAF,EAAYC,EAAZ,CAAejF,MAAMK,cAArB,EAAqCQ,SAASE,WAA9C,EAA2D,UAAUmE,KAAV,EAAiB;AAC1EA,UAAMC,cAAN;;AAEA,QAAMC,SAASlG,SAASsF,qBAAT,CAA+B,IAA/B,CAAf;AACA,QAAMpC,OAASjD,EAAEiG,MAAF,EAAUhD,IAAV,CAAe9C,QAAf,CAAf;AACA,QAAM2B,SAASmB,OAAO,QAAP,GAAkBjD,EAAE,IAAF,EAAQiD,IAAR,EAAjC;;AAEAlD,aAASsD,gBAAT,CAA0BC,IAA1B,CAA+BtD,EAAEiG,MAAF,CAA/B,EAA0CnE,MAA1C;AACD,GARD;;AAWA;;;;;;AAMA9B,IAAEO,EAAF,CAAKN,IAAL,IAAyBF,SAASsD,gBAAlC;AACArD,IAAEO,EAAF,CAAKN,IAAL,EAAWiG,WAAX,GAAyBnG,QAAzB;AACAC,IAAEO,EAAF,CAAKN,IAAL,EAAWkG,UAAX,GAAyB,YAAY;AACnCnG,MAAEO,EAAF,CAAKN,IAAL,IAAaK,kBAAb;AACA,WAAOP,SAASsD,gBAAhB;AACD,GAHD;;AAKA,SAAOtD,QAAP;AAED,CAtXgB,CAsXdqG,MAtXc,CAAjB", "file": "collapse.js", "sourcesContent": ["import Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v4.0.0-alpha.6): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Collapse = (($) => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'collapse'\n  const VERSION             = '4.0.0-alpha.6'\n  const DATA_KEY            = 'bs.collapse'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 600\n\n  const Default = {\n    toggle : true,\n    parent : ''\n  }\n\n  const DefaultType = {\n    toggle : 'boolean',\n    parent : 'string'\n  }\n\n  const Event = {\n    SHOW           : `show${EVENT_KEY}`,\n    SHOWN          : `shown${EVENT_KEY}`,\n    HIDE           : `hide${EVENT_KEY}`,\n    HIDDEN         : `hidden${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    SHOW       : 'show',\n    COLLAPSE   : 'collapse',\n    COLLAPSING : 'collapsing',\n    COLLAPSED  : 'collapsed'\n  }\n\n  const Dimension = {\n    WIDTH  : 'width',\n    HEIGHT : 'height'\n  }\n\n  const Selector = {\n    ACTIVES     : '.card > .show, .card > .collapsing',\n    DATA_TOGGLE : '[data-toggle=\"collapse\"]'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Collapse {\n\n    constructor(element, config) {\n      this._isTransitioning = false\n      this._element         = element\n      this._config          = this._getConfig(config)\n      this._triggerArray    = $.makeArray($(\n        `[data-toggle=\"collapse\"][href=\"#${element.id}\"],` +\n        `[data-toggle=\"collapse\"][data-target=\"#${element.id}\"]`\n      ))\n\n      this._parent = this._config.parent ? this._getParent() : null\n\n      if (!this._config.parent) {\n        this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n      }\n\n      if (this._config.toggle) {\n        this.toggle()\n      }\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n\n    // public\n\n    toggle() {\n      if ($(this._element).hasClass(ClassName.SHOW)) {\n        this.hide()\n      } else {\n        this.show()\n      }\n    }\n\n    show() {\n      if (this._isTransitioning) {\n        throw new Error('Collapse is transitioning')\n      }\n\n      if ($(this._element).hasClass(ClassName.SHOW)) {\n        return\n      }\n\n      let actives\n      let activesData\n\n      if (this._parent) {\n        actives = $.makeArray($(this._parent).find(Selector.ACTIVES))\n        if (!actives.length) {\n          actives = null\n        }\n      }\n\n      if (actives) {\n        activesData = $(actives).data(DATA_KEY)\n        if (activesData && activesData._isTransitioning) {\n          return\n        }\n      }\n\n      const startEvent = $.Event(Event.SHOW)\n      $(this._element).trigger(startEvent)\n      if (startEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (actives) {\n        Collapse._jQueryInterface.call($(actives), 'hide')\n        if (!activesData) {\n          $(actives).data(DATA_KEY, null)\n        }\n      }\n\n      const dimension = this._getDimension()\n\n      $(this._element)\n        .removeClass(ClassName.COLLAPSE)\n        .addClass(ClassName.COLLAPSING)\n\n      this._element.style[dimension] = 0\n      this._element.setAttribute('aria-expanded', true)\n\n      if (this._triggerArray.length) {\n        $(this._triggerArray)\n          .removeClass(ClassName.COLLAPSED)\n          .attr('aria-expanded', true)\n      }\n\n      this.setTransitioning(true)\n\n      const complete = () => {\n        $(this._element)\n          .removeClass(ClassName.COLLAPSING)\n          .addClass(ClassName.COLLAPSE)\n          .addClass(ClassName.SHOW)\n\n        this._element.style[dimension] = ''\n\n        this.setTransitioning(false)\n\n        $(this._element).trigger(Event.SHOWN)\n      }\n\n      if (!Util.supportsTransitionEnd()) {\n        complete()\n        return\n      }\n\n      const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n      const scrollSize           = `scroll${capitalizedDimension}`\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(TRANSITION_DURATION)\n\n      this._element.style[dimension] = `${this._element[scrollSize]}px`\n    }\n\n    hide() {\n      if (this._isTransitioning) {\n        throw new Error('Collapse is transitioning')\n      }\n\n      if (!$(this._element).hasClass(ClassName.SHOW)) {\n        return\n      }\n\n      const startEvent = $.Event(Event.HIDE)\n      $(this._element).trigger(startEvent)\n      if (startEvent.isDefaultPrevented()) {\n        return\n      }\n\n      const dimension       = this._getDimension()\n      const offsetDimension = dimension === Dimension.WIDTH ?\n        'offsetWidth' : 'offsetHeight'\n\n      this._element.style[dimension] = `${this._element[offsetDimension]}px`\n\n      Util.reflow(this._element)\n\n      $(this._element)\n        .addClass(ClassName.COLLAPSING)\n        .removeClass(ClassName.COLLAPSE)\n        .removeClass(ClassName.SHOW)\n\n      this._element.setAttribute('aria-expanded', false)\n\n      if (this._triggerArray.length) {\n        $(this._triggerArray)\n          .addClass(ClassName.COLLAPSED)\n          .attr('aria-expanded', false)\n      }\n\n      this.setTransitioning(true)\n\n      const complete = () => {\n        this.setTransitioning(false)\n        $(this._element)\n          .removeClass(ClassName.COLLAPSING)\n          .addClass(ClassName.COLLAPSE)\n          .trigger(Event.HIDDEN)\n      }\n\n      this._element.style[dimension] = ''\n\n      if (!Util.supportsTransitionEnd()) {\n        complete()\n        return\n      }\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(TRANSITION_DURATION)\n    }\n\n    setTransitioning(isTransitioning) {\n      this._isTransitioning = isTransitioning\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n\n      this._config          = null\n      this._parent          = null\n      this._element         = null\n      this._triggerArray    = null\n      this._isTransitioning = null\n    }\n\n\n    // private\n\n    _getConfig(config) {\n      config = $.extend({}, Default, config)\n      config.toggle = Boolean(config.toggle) // coerce string values\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _getDimension() {\n      const hasWidth = $(this._element).hasClass(Dimension.WIDTH)\n      return hasWidth ? Dimension.WIDTH : Dimension.HEIGHT\n    }\n\n    _getParent() {\n      const parent   = $(this._config.parent)[0]\n      const selector =\n        `[data-toggle=\"collapse\"][data-parent=\"${this._config.parent}\"]`\n\n      $(parent).find(selector).each((i, element) => {\n        this._addAriaAndCollapsedClass(\n          Collapse._getTargetFromElement(element),\n          [element]\n        )\n      })\n\n      return parent\n    }\n\n    _addAriaAndCollapsedClass(element, triggerArray) {\n      if (element) {\n        const isOpen = $(element).hasClass(ClassName.SHOW)\n        element.setAttribute('aria-expanded', isOpen)\n\n        if (triggerArray.length) {\n          $(triggerArray)\n            .toggleClass(ClassName.COLLAPSED, !isOpen)\n            .attr('aria-expanded', isOpen)\n        }\n      }\n    }\n\n\n    // static\n\n    static _getTargetFromElement(element) {\n      const selector = Util.getSelectorFromElement(element)\n      return selector ? $(selector)[0] : null\n    }\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $this   = $(this)\n        let data      = $this.data(DATA_KEY)\n        const _config = $.extend(\n          {},\n          Default,\n          $this.data(),\n          typeof config === 'object' && config\n        )\n\n        if (!data && _config.toggle && /show|hide/.test(config)) {\n          _config.toggle = false\n        }\n\n        if (!data) {\n          data = new Collapse(this, _config)\n          $this.data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (data[config] === undefined) {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    event.preventDefault()\n\n    const target = Collapse._getTargetFromElement(this)\n    const data   = $(target).data(DATA_KEY)\n    const config = data ? 'toggle' : $(this).data()\n\n    Collapse._jQueryInterface.call($(target), config)\n  })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Collapse._jQueryInterface\n  $.fn[NAME].Constructor = Collapse\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Collapse._jQueryInterface\n  }\n\n  return Collapse\n\n})(jQuery)\n\nexport default Collapse\n"]}
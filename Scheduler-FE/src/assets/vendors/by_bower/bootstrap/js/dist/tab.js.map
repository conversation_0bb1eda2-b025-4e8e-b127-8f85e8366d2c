{"version": 3, "sources": ["../src/tab.js"], "names": ["Tab", "$", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "fn", "TRANSITION_DURATION", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "CLICK_DATA_API", "ClassName", "DROPDOWN_MENU", "ACTIVE", "DISABLED", "FADE", "Selector", "A", "LI", "DROPDOWN", "LIST", "FADE_CHILD", "ACTIVE_CHILD", "DATA_TOGGLE", "DROPDOWN_TOGGLE", "DROPDOWN_ACTIVE_CHILD", "element", "_element", "show", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "hasClass", "target", "previous", "listElement", "closest", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "makeArray", "find", "length", "hideEvent", "relatedTarget", "showEvent", "trigger", "isDefaultPrevented", "_activate", "complete", "hiddenEvent", "shownEvent", "dispose", "removeClass", "container", "callback", "active", "isTransitioning", "supportsTransitionEnd", "Boolean", "_transitionComplete", "one", "TRANSITION_END", "emulateTransitionEnd", "dropdown<PERSON><PERSON>d", "setAttribute", "addClass", "reflow", "dropdownElement", "_jQueryInterface", "config", "each", "$this", "data", "undefined", "Error", "document", "on", "event", "preventDefault", "call", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;AAGA;;;;;;;AAOA,IAAMA,MAAO,UAACC,CAAD,EAAO;;AAGlB;;;;;;AAMA,MAAMC,OAAsB,KAA5B;AACA,MAAMC,UAAsB,eAA5B;AACA,MAAMC,WAAsB,QAA5B;AACA,MAAMC,kBAA0BD,QAAhC;AACA,MAAME,eAAsB,WAA5B;AACA,MAAMC,qBAAsBN,EAAEO,EAAF,CAAKN,IAAL,CAA5B;AACA,MAAMO,sBAAsB,GAA5B;;AAEA,MAAMC,QAAQ;AACZC,mBAAwBN,SADZ;AAEZO,uBAA0BP,SAFd;AAGZQ,mBAAwBR,SAHZ;AAIZS,qBAAyBT,SAJb;AAKZU,8BAAyBV,SAAzB,GAAqCC;AALzB,GAAd;;AAQA,MAAMU,YAAY;AAChBC,mBAAgB,eADA;AAEhBC,YAAgB,QAFA;AAGhBC,cAAgB,UAHA;AAIhBC,UAAgB,MAJA;AAKhBP,UAAgB;AALA,GAAlB;;AAQA,MAAMQ,WAAW;AACfC,OAAwB,GADT;AAEfC,QAAwB,IAFT;AAGfC,cAAwB,WAHT;AAIfC,UAAwB,yEAJT;AAKfC,gBAAwB,4BALT;AAMfR,YAAwB,SANT;AAOfS,kBAAwB,kCAPT;AAQfC,iBAAwB,2CART;AASfC,qBAAwB,kBATT;AAUfC,2BAAwB;AAVT,GAAjB;;AAcA;;;;;;AA/CkB,MAqDZ9B,GArDY;AAuDhB,iBAAY+B,OAAZ,EAAqB;AAAA;;AACnB,WAAKC,QAAL,GAAgBD,OAAhB;AACD;;AAGD;;AAOA;;AAnEgB,kBAqEhBE,IArEgB,mBAqET;AAAA;;AACL,UAAI,KAAKD,QAAL,CAAcE,UAAd,IACA,KAAKF,QAAL,CAAcE,UAAd,CAAyBC,QAAzB,KAAsCC,KAAKC,YAD3C,IAEApC,EAAE,KAAK+B,QAAP,EAAiBM,QAAjB,CAA0BtB,UAAUE,MAApC,CAFA,IAGAjB,EAAE,KAAK+B,QAAP,EAAiBM,QAAjB,CAA0BtB,UAAUG,QAApC,CAHJ,EAGmD;AACjD;AACD;;AAED,UAAIoB,eAAJ;AACA,UAAIC,iBAAJ;AACA,UAAMC,cAAcxC,EAAE,KAAK+B,QAAP,EAAiBU,OAAjB,CAAyBrB,SAASI,IAAlC,EAAwC,CAAxC,CAApB;AACA,UAAMkB,WAAcC,KAAKC,sBAAL,CAA4B,KAAKb,QAAjC,CAApB;;AAEA,UAAIS,WAAJ,EAAiB;AACfD,mBAAWvC,EAAE6C,SAAF,CAAY7C,EAAEwC,WAAF,EAAeM,IAAf,CAAoB1B,SAASH,MAA7B,CAAZ,CAAX;AACAsB,mBAAWA,SAASA,SAASQ,MAAT,GAAkB,CAA3B,CAAX;AACD;;AAED,UAAMC,YAAYhD,EAAES,KAAF,CAAQA,MAAMC,IAAd,EAAoB;AACpCuC,uBAAe,KAAKlB;AADgB,OAApB,CAAlB;;AAIA,UAAMmB,YAAYlD,EAAES,KAAF,CAAQA,MAAMG,IAAd,EAAoB;AACpCqC,uBAAeV;AADqB,OAApB,CAAlB;;AAIA,UAAIA,QAAJ,EAAc;AACZvC,UAAEuC,QAAF,EAAYY,OAAZ,CAAoBH,SAApB;AACD;;AAEDhD,QAAE,KAAK+B,QAAP,EAAiBoB,OAAjB,CAAyBD,SAAzB;;AAEA,UAAIA,UAAUE,kBAAV,MACDJ,UAAUI,kBAAV,EADH,EACmC;AACjC;AACD;;AAED,UAAIV,QAAJ,EAAc;AACZJ,iBAAStC,EAAE0C,QAAF,EAAY,CAAZ,CAAT;AACD;;AAED,WAAKW,SAAL,CACE,KAAKtB,QADP,EAEES,WAFF;;AAKA,UAAMc,WAAW,SAAXA,QAAW,GAAM;AACrB,YAAMC,cAAcvD,EAAES,KAAF,CAAQA,MAAME,MAAd,EAAsB;AACxCsC,yBAAe,MAAKlB;AADoB,SAAtB,CAApB;;AAIA,YAAMyB,aAAaxD,EAAES,KAAF,CAAQA,MAAMI,KAAd,EAAqB;AACtCoC,yBAAeV;AADuB,SAArB,CAAnB;;AAIAvC,UAAEuC,QAAF,EAAYY,OAAZ,CAAoBI,WAApB;AACAvD,UAAE,MAAK+B,QAAP,EAAiBoB,OAAjB,CAAyBK,UAAzB;AACD,OAXD;;AAaA,UAAIlB,MAAJ,EAAY;AACV,aAAKe,SAAL,CAAef,MAAf,EAAuBA,OAAOL,UAA9B,EAA0CqB,QAA1C;AACD,OAFD,MAEO;AACLA;AACD;AACF,KArIe;;AAAA,kBAuIhBG,OAvIgB,sBAuIN;AACRzD,QAAE0D,WAAF,CAAc,KAAK3B,QAAnB,EAA6B5B,QAA7B;AACA,WAAK4B,QAAL,GAAgB,IAAhB;AACD,KA1Ie;;AA6IhB;;AA7IgB,kBA+IhBsB,SA/IgB,sBA+INvB,OA/IM,EA+IG6B,SA/IH,EA+IcC,QA/Id,EA+IwB;AAAA;;AACtC,UAAMC,SAAkB7D,EAAE2D,SAAF,EAAab,IAAb,CAAkB1B,SAASM,YAA3B,EAAyC,CAAzC,CAAxB;AACA,UAAMoC,kBAAkBF,YACnBjB,KAAKoB,qBAAL,EADmB,KAElBF,UAAU7D,EAAE6D,MAAF,EAAUxB,QAAV,CAAmBtB,UAAUI,IAA7B,CAAV,IACE6C,QAAQhE,EAAE2D,SAAF,EAAab,IAAb,CAAkB1B,SAASK,UAA3B,EAAuC,CAAvC,CAAR,CAHgB,CAAxB;;AAKA,UAAM6B,WAAW,SAAXA,QAAW;AAAA,eAAM,OAAKW,mBAAL,CACrBnC,OADqB,EAErB+B,MAFqB,EAGrBC,eAHqB,EAIrBF,QAJqB,CAAN;AAAA,OAAjB;;AAOA,UAAIC,UAAUC,eAAd,EAA+B;AAC7B9D,UAAE6D,MAAF,EACGK,GADH,CACOvB,KAAKwB,cADZ,EAC4Bb,QAD5B,EAEGc,oBAFH,CAEwB5D,mBAFxB;AAID,OALD,MAKO;AACL8C;AACD;;AAED,UAAIO,MAAJ,EAAY;AACV7D,UAAE6D,MAAF,EAAUH,WAAV,CAAsB3C,UAAUH,IAAhC;AACD;AACF,KAzKe;;AAAA,kBA2KhBqD,mBA3KgB,gCA2KInC,OA3KJ,EA2Ka+B,MA3Kb,EA2KqBC,eA3KrB,EA2KsCF,QA3KtC,EA2KgD;AAC9D,UAAIC,MAAJ,EAAY;AACV7D,UAAE6D,MAAF,EAAUH,WAAV,CAAsB3C,UAAUE,MAAhC;;AAEA,YAAMoD,gBAAgBrE,EAAE6D,OAAO5B,UAAT,EAAqBa,IAArB,CACpB1B,SAASS,qBADW,EAEpB,CAFoB,CAAtB;;AAIA,YAAIwC,aAAJ,EAAmB;AACjBrE,YAAEqE,aAAF,EAAiBX,WAAjB,CAA6B3C,UAAUE,MAAvC;AACD;;AAED4C,eAAOS,YAAP,CAAoB,eAApB,EAAqC,KAArC;AACD;;AAEDtE,QAAE8B,OAAF,EAAWyC,QAAX,CAAoBxD,UAAUE,MAA9B;AACAa,cAAQwC,YAAR,CAAqB,eAArB,EAAsC,IAAtC;;AAEA,UAAIR,eAAJ,EAAqB;AACnBnB,aAAK6B,MAAL,CAAY1C,OAAZ;AACA9B,UAAE8B,OAAF,EAAWyC,QAAX,CAAoBxD,UAAUH,IAA9B;AACD,OAHD,MAGO;AACLZ,UAAE8B,OAAF,EAAW4B,WAAX,CAAuB3C,UAAUI,IAAjC;AACD;;AAED,UAAIW,QAAQG,UAAR,IACAjC,EAAE8B,QAAQG,UAAV,EAAsBI,QAAtB,CAA+BtB,UAAUC,aAAzC,CADJ,EAC6D;;AAE3D,YAAMyD,kBAAkBzE,EAAE8B,OAAF,EAAWW,OAAX,CAAmBrB,SAASG,QAA5B,EAAsC,CAAtC,CAAxB;AACA,YAAIkD,eAAJ,EAAqB;AACnBzE,YAAEyE,eAAF,EAAmB3B,IAAnB,CAAwB1B,SAASQ,eAAjC,EAAkD2C,QAAlD,CAA2DxD,UAAUE,MAArE;AACD;;AAEDa,gBAAQwC,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD;;AAED,UAAIV,QAAJ,EAAc;AACZA;AACD;AACF,KAlNe;;AAqNhB;;AArNgB,QAuNTc,gBAvNS,6BAuNQC,MAvNR,EAuNgB;AAC9B,aAAO,KAAKC,IAAL,CAAU,YAAY;AAC3B,YAAMC,QAAQ7E,EAAE,IAAF,CAAd;AACA,YAAI8E,OAAUD,MAAMC,IAAN,CAAW3E,QAAX,CAAd;;AAEA,YAAI,CAAC2E,IAAL,EAAW;AACTA,iBAAO,IAAI/E,GAAJ,CAAQ,IAAR,CAAP;AACA8E,gBAAMC,IAAN,CAAW3E,QAAX,EAAqB2E,IAArB;AACD;;AAED,YAAI,OAAOH,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,cAAIG,KAAKH,MAAL,MAAiBI,SAArB,EAAgC;AAC9B,kBAAM,IAAIC,KAAJ,uBAA8BL,MAA9B,OAAN;AACD;AACDG,eAAKH,MAAL;AACD;AACF,OAfM,CAAP;AAgBD,KAxOe;;AAAA;AAAA;AAAA,0BA8DK;AACnB,eAAOzE,OAAP;AACD;AAhEe;;AAAA;AAAA;;AA6OlB;;;;;;AAMAF,IAAEiF,QAAF,EACGC,EADH,CACMzE,MAAMK,cADZ,EAC4BM,SAASO,WADrC,EACkD,UAAUwD,KAAV,EAAiB;AAC/DA,UAAMC,cAAN;AACArF,QAAI2E,gBAAJ,CAAqBW,IAArB,CAA0BrF,EAAE,IAAF,CAA1B,EAAmC,MAAnC;AACD,GAJH;;AAOA;;;;;;AAMAA,IAAEO,EAAF,CAAKN,IAAL,IAAyBF,IAAI2E,gBAA7B;AACA1E,IAAEO,EAAF,CAAKN,IAAL,EAAWqF,WAAX,GAAyBvF,GAAzB;AACAC,IAAEO,EAAF,CAAKN,IAAL,EAAWsF,UAAX,GAAyB,YAAY;AACnCvF,MAAEO,EAAF,CAAKN,IAAL,IAAaK,kBAAb;AACA,WAAOP,IAAI2E,gBAAX;AACD,GAHD;;AAKA,SAAO3E,GAAP;AAED,CAzQW,CAyQTyF,MAzQS,CAAZ", "file": "tab.js", "sourcesContent": ["import Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-alpha.6): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Tab = (($) => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'tab'\n  const VERSION             = '4.0.0-alpha.6'\n  const DATA_KEY            = 'bs.tab'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n\n  const Event = {\n    HIDE           : `hide${EVENT_KEY}`,\n    HIDDEN         : `hidden${EVENT_KEY}`,\n    SHOW           : `show${EVENT_KEY}`,\n    SHOWN          : `shown${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DROPDOWN_MENU : 'dropdown-menu',\n    ACTIVE        : 'active',\n    DISABLED      : 'disabled',\n    FADE          : 'fade',\n    SHOW          : 'show'\n  }\n\n  const Selector = {\n    A                     : 'a',\n    LI                    : 'li',\n    DROPDOWN              : '.dropdown',\n    LIST                  : 'ul:not(.dropdown-menu), ol:not(.dropdown-menu), nav:not(.dropdown-menu)',\n    FADE_CHILD            : '> .nav-item .fade, > .fade',\n    ACTIVE                : '.active',\n    ACTIVE_CHILD          : '> .nav-item > .active, > .active',\n    DATA_TOGGLE           : '[data-toggle=\"tab\"], [data-toggle=\"pill\"]',\n    DROPDOWN_TOGGLE       : '.dropdown-toggle',\n    DROPDOWN_ACTIVE_CHILD : '> .dropdown-menu .active'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Tab {\n\n    constructor(element) {\n      this._element = element\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n\n    // public\n\n    show() {\n      if (this._element.parentNode &&\n          this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n          $(this._element).hasClass(ClassName.ACTIVE) ||\n          $(this._element).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      let target\n      let previous\n      const listElement = $(this._element).closest(Selector.LIST)[0]\n      const selector    = Util.getSelectorFromElement(this._element)\n\n      if (listElement) {\n        previous = $.makeArray($(listElement).find(Selector.ACTIVE))\n        previous = previous[previous.length - 1]\n      }\n\n      const hideEvent = $.Event(Event.HIDE, {\n        relatedTarget: this._element\n      })\n\n      const showEvent = $.Event(Event.SHOW, {\n        relatedTarget: previous\n      })\n\n      if (previous) {\n        $(previous).trigger(hideEvent)\n      }\n\n      $(this._element).trigger(showEvent)\n\n      if (showEvent.isDefaultPrevented() ||\n         hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (selector) {\n        target = $(selector)[0]\n      }\n\n      this._activate(\n        this._element,\n        listElement\n      )\n\n      const complete = () => {\n        const hiddenEvent = $.Event(Event.HIDDEN, {\n          relatedTarget: this._element\n        })\n\n        const shownEvent = $.Event(Event.SHOWN, {\n          relatedTarget: previous\n        })\n\n        $(previous).trigger(hiddenEvent)\n        $(this._element).trigger(shownEvent)\n      }\n\n      if (target) {\n        this._activate(target, target.parentNode, complete)\n      } else {\n        complete()\n      }\n    }\n\n    dispose() {\n      $.removeClass(this._element, DATA_KEY)\n      this._element = null\n    }\n\n\n    // private\n\n    _activate(element, container, callback) {\n      const active          = $(container).find(Selector.ACTIVE_CHILD)[0]\n      const isTransitioning = callback\n        && Util.supportsTransitionEnd()\n        && (active && $(active).hasClass(ClassName.FADE)\n           || Boolean($(container).find(Selector.FADE_CHILD)[0]))\n\n      const complete = () => this._transitionComplete(\n        element,\n        active,\n        isTransitioning,\n        callback\n      )\n\n      if (active && isTransitioning) {\n        $(active)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n\n      } else {\n        complete()\n      }\n\n      if (active) {\n        $(active).removeClass(ClassName.SHOW)\n      }\n    }\n\n    _transitionComplete(element, active, isTransitioning, callback) {\n      if (active) {\n        $(active).removeClass(ClassName.ACTIVE)\n\n        const dropdownChild = $(active.parentNode).find(\n          Selector.DROPDOWN_ACTIVE_CHILD\n        )[0]\n\n        if (dropdownChild) {\n          $(dropdownChild).removeClass(ClassName.ACTIVE)\n        }\n\n        active.setAttribute('aria-expanded', false)\n      }\n\n      $(element).addClass(ClassName.ACTIVE)\n      element.setAttribute('aria-expanded', true)\n\n      if (isTransitioning) {\n        Util.reflow(element)\n        $(element).addClass(ClassName.SHOW)\n      } else {\n        $(element).removeClass(ClassName.FADE)\n      }\n\n      if (element.parentNode &&\n          $(element.parentNode).hasClass(ClassName.DROPDOWN_MENU)) {\n\n        const dropdownElement = $(element).closest(Selector.DROPDOWN)[0]\n        if (dropdownElement) {\n          $(dropdownElement).find(Selector.DROPDOWN_TOGGLE).addClass(ClassName.ACTIVE)\n        }\n\n        element.setAttribute('aria-expanded', true)\n      }\n\n      if (callback) {\n        callback()\n      }\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $this = $(this)\n        let data    = $this.data(DATA_KEY)\n\n        if (!data) {\n          data = new Tab(this)\n          $this.data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (data[config] === undefined) {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n      event.preventDefault()\n      Tab._jQueryInterface.call($(this), 'show')\n    })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Tab._jQueryInterface\n  $.fn[NAME].Constructor = Tab\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tab._jQueryInterface\n  }\n\n  return Tab\n\n})(jQuery)\n\nexport default Tab\n"]}
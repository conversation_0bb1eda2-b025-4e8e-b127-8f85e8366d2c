{"version": 3, "sources": ["../src/modal.js"], "names": ["Modal", "$", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "fn", "TRANSITION_DURATION", "BACKDROP_TRANSITION_DURATION", "ESCAPE_KEYCODE", "<PERSON><PERSON><PERSON>", "backdrop", "keyboard", "focus", "show", "DefaultType", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "FOCUSIN", "RESIZE", "CLICK_DISMISS", "KEYDOWN_DISMISS", "MOUSEUP_DISMISS", "MOUSEDOWN_DISMISS", "CLICK_DATA_API", "ClassName", "SCROLLBAR_MEASURER", "BACKDROP", "OPEN", "FADE", "Selector", "DIALOG", "DATA_TOGGLE", "DATA_DISMISS", "FIXED_CONTENT", "element", "config", "_config", "_getConfig", "_element", "_dialog", "find", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_isTransitioning", "_originalBodyPadding", "_scrollbarWidth", "toggle", "relatedTarget", "hide", "Error", "<PERSON><PERSON>", "supportsTransitionEnd", "hasClass", "showEvent", "trigger", "isDefaultPrevented", "_checkScrollbar", "_setScrollbar", "document", "body", "addClass", "_setEscapeEvent", "_setResizeEvent", "on", "event", "one", "target", "is", "_showBackdrop", "_showElement", "preventDefault", "transition", "hideEvent", "off", "removeClass", "TRANSITION_END", "_hideModal", "emulateTransitionEnd", "dispose", "removeData", "window", "extend", "typeCheckConfig", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "append<PERSON><PERSON><PERSON>", "style", "display", "removeAttribute", "scrollTop", "reflow", "_enforceFocus", "shownEvent", "transitionComplete", "has", "length", "which", "_handleUpdate", "setAttribute", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "remove", "callback", "animate", "doAnimate", "createElement", "className", "appendTo", "currentTarget", "callback<PERSON><PERSON><PERSON>", "_adjustDialog", "isModalOverflowing", "scrollHeight", "documentElement", "clientHeight", "paddingLeft", "paddingRight", "clientWidth", "innerWidth", "_getScrollbarWidth", "bodyPadding", "parseInt", "css", "scrollDiv", "scrollbarWidth", "offsetWidth", "<PERSON><PERSON><PERSON><PERSON>", "_jQueryInterface", "each", "data", "undefined", "selector", "getSelectorFromElement", "tagName", "$target", "call", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;AAGA;;;;;;;AAOA,IAAMA,QAAS,UAACC,CAAD,EAAO;;AAGpB;;;;;;AAMA,MAAMC,OAA+B,OAArC;AACA,MAAMC,UAA+B,eAArC;AACA,MAAMC,WAA+B,UAArC;AACA,MAAMC,kBAAmCD,QAAzC;AACA,MAAME,eAA+B,WAArC;AACA,MAAMC,qBAA+BN,EAAEO,EAAF,CAAKN,IAAL,CAArC;AACA,MAAMO,sBAA+B,GAArC;AACA,MAAMC,+BAA+B,GAArC;AACA,MAAMC,iBAA+B,EAArC,CAjBoB,CAiBoB;;AAExC,MAAMC,UAAU;AACdC,cAAW,IADG;AAEdC,cAAW,IAFG;AAGdC,WAAW,IAHG;AAIdC,UAAW;AAJG,GAAhB;;AAOA,MAAMC,cAAc;AAClBJ,cAAW,kBADO;AAElBC,cAAW,SAFO;AAGlBC,WAAW,SAHO;AAIlBC,UAAW;AAJO,GAApB;;AAOA,MAAME,QAAQ;AACZC,mBAA2Bd,SADf;AAEZe,uBAA6Bf,SAFjB;AAGZgB,mBAA2BhB,SAHf;AAIZiB,qBAA4BjB,SAJhB;AAKZkB,yBAA8BlB,SALlB;AAMZmB,uBAA6BnB,SANjB;AAOZoB,qCAAoCpB,SAPxB;AAQZqB,yCAAsCrB,SAR1B;AASZsB,yCAAsCtB,SAT1B;AAUZuB,6CAAwCvB,SAV5B;AAWZwB,8BAA4BxB,SAA5B,GAAwCC;AAX5B,GAAd;;AAcA,MAAMwB,YAAY;AAChBC,wBAAqB,yBADL;AAEhBC,cAAqB,gBAFL;AAGhBC,UAAqB,YAHL;AAIhBC,UAAqB,MAJL;AAKhBb,UAAqB;AALL,GAAlB;;AAQA,MAAMc,WAAW;AACfC,YAAqB,eADN;AAEfC,iBAAqB,uBAFN;AAGfC,kBAAqB,wBAHN;AAIfC,mBAAqB;AAJN,GAAjB;;AAQA;;;;;;AA/DoB,MAqEdvC,KArEc;AAuElB,mBAAYwC,OAAZ,EAAqBC,MAArB,EAA6B;AAAA;;AAC3B,WAAKC,OAAL,GAA4B,KAAKC,UAAL,CAAgBF,MAAhB,CAA5B;AACA,WAAKG,QAAL,GAA4BJ,OAA5B;AACA,WAAKK,OAAL,GAA4B5C,EAAEuC,OAAF,EAAWM,IAAX,CAAgBX,SAASC,MAAzB,EAAiC,CAAjC,CAA5B;AACA,WAAKW,SAAL,GAA4B,IAA5B;AACA,WAAKC,QAAL,GAA4B,KAA5B;AACA,WAAKC,kBAAL,GAA4B,KAA5B;AACA,WAAKC,oBAAL,GAA4B,KAA5B;AACA,WAAKC,gBAAL,GAA4B,KAA5B;AACA,WAAKC,oBAAL,GAA4B,CAA5B;AACA,WAAKC,eAAL,GAA4B,CAA5B;AACD;;AAGD;;AAWA;;AAhGkB,oBAkGlBC,MAlGkB,mBAkGXC,aAlGW,EAkGI;AACpB,aAAO,KAAKP,QAAL,GAAgB,KAAKQ,IAAL,EAAhB,GAA8B,KAAKxC,IAAL,CAAUuC,aAAV,CAArC;AACD,KApGiB;;AAAA,oBAsGlBvC,IAtGkB,iBAsGbuC,aAtGa,EAsGE;AAAA;;AAClB,UAAI,KAAKJ,gBAAT,EAA2B;AACzB,cAAM,IAAIM,KAAJ,CAAU,wBAAV,CAAN;AACD;;AAED,UAAIC,KAAKC,qBAAL,MACF1D,EAAE,KAAK2C,QAAP,EAAiBgB,QAAjB,CAA0B9B,UAAUI,IAApC,CADF,EAC6C;AAC3C,aAAKiB,gBAAL,GAAwB,IAAxB;AACD;AACD,UAAMU,YAAY5D,EAAEiB,KAAF,CAAQA,MAAMG,IAAd,EAAoB;AACpCkC;AADoC,OAApB,CAAlB;;AAIAtD,QAAE,KAAK2C,QAAP,EAAiBkB,OAAjB,CAAyBD,SAAzB;;AAEA,UAAI,KAAKb,QAAL,IAAiBa,UAAUE,kBAAV,EAArB,EAAqD;AACnD;AACD;;AAED,WAAKf,QAAL,GAAgB,IAAhB;;AAEA,WAAKgB,eAAL;AACA,WAAKC,aAAL;;AAEAhE,QAAEiE,SAASC,IAAX,EAAiBC,QAAjB,CAA0BtC,UAAUG,IAApC;;AAEA,WAAKoC,eAAL;AACA,WAAKC,eAAL;;AAEArE,QAAE,KAAK2C,QAAP,EAAiB2B,EAAjB,CACErD,MAAMO,aADR,EAEEU,SAASG,YAFX,EAGE,UAACkC,KAAD;AAAA,eAAW,MAAKhB,IAAL,CAAUgB,KAAV,CAAX;AAAA,OAHF;;AAMAvE,QAAE,KAAK4C,OAAP,EAAgB0B,EAAhB,CAAmBrD,MAAMU,iBAAzB,EAA4C,YAAM;AAChD3B,UAAE,MAAK2C,QAAP,EAAiB6B,GAAjB,CAAqBvD,MAAMS,eAA3B,EAA4C,UAAC6C,KAAD,EAAW;AACrD,cAAIvE,EAAEuE,MAAME,MAAR,EAAgBC,EAAhB,CAAmB,MAAK/B,QAAxB,CAAJ,EAAuC;AACrC,kBAAKM,oBAAL,GAA4B,IAA5B;AACD;AACF,SAJD;AAKD,OAND;;AAQA,WAAK0B,aAAL,CAAmB;AAAA,eAAM,MAAKC,YAAL,CAAkBtB,aAAlB,CAAN;AAAA,OAAnB;AACD,KAlJiB;;AAAA,oBAoJlBC,IApJkB,iBAoJbgB,KApJa,EAoJN;AAAA;;AACV,UAAIA,KAAJ,EAAW;AACTA,cAAMM,cAAN;AACD;;AAED,UAAI,KAAK3B,gBAAT,EAA2B;AACzB,cAAM,IAAIM,KAAJ,CAAU,wBAAV,CAAN;AACD;;AAED,UAAMsB,aAAarB,KAAKC,qBAAL,MACjB1D,EAAE,KAAK2C,QAAP,EAAiBgB,QAAjB,CAA0B9B,UAAUI,IAApC,CADF;AAEA,UAAI6C,UAAJ,EAAgB;AACd,aAAK5B,gBAAL,GAAwB,IAAxB;AACD;;AAED,UAAM6B,YAAY/E,EAAEiB,KAAF,CAAQA,MAAMC,IAAd,CAAlB;AACAlB,QAAE,KAAK2C,QAAP,EAAiBkB,OAAjB,CAAyBkB,SAAzB;;AAEA,UAAI,CAAC,KAAKhC,QAAN,IAAkBgC,UAAUjB,kBAAV,EAAtB,EAAsD;AACpD;AACD;;AAED,WAAKf,QAAL,GAAgB,KAAhB;;AAEA,WAAKqB,eAAL;AACA,WAAKC,eAAL;;AAEArE,QAAEiE,QAAF,EAAYe,GAAZ,CAAgB/D,MAAMK,OAAtB;;AAEAtB,QAAE,KAAK2C,QAAP,EAAiBsC,WAAjB,CAA6BpD,UAAUT,IAAvC;;AAEApB,QAAE,KAAK2C,QAAP,EAAiBqC,GAAjB,CAAqB/D,MAAMO,aAA3B;AACAxB,QAAE,KAAK4C,OAAP,EAAgBoC,GAAhB,CAAoB/D,MAAMU,iBAA1B;;AAEA,UAAImD,UAAJ,EAAgB;AACd9E,UAAE,KAAK2C,QAAP,EACG6B,GADH,CACOf,KAAKyB,cADZ,EAC4B,UAACX,KAAD;AAAA,iBAAW,OAAKY,UAAL,CAAgBZ,KAAhB,CAAX;AAAA,SAD5B,EAEGa,oBAFH,CAEwB5E,mBAFxB;AAGD,OAJD,MAIO;AACL,aAAK2E,UAAL;AACD;AACF,KA7LiB;;AAAA,oBA+LlBE,OA/LkB,sBA+LR;AACRrF,QAAEsF,UAAF,CAAa,KAAK3C,QAAlB,EAA4BxC,QAA5B;;AAEAH,QAAEuF,MAAF,EAAUtB,QAAV,EAAoB,KAAKtB,QAAzB,EAAmC,KAAKG,SAAxC,EAAmDkC,GAAnD,CAAuD5E,SAAvD;;AAEA,WAAKqC,OAAL,GAA4B,IAA5B;AACA,WAAKE,QAAL,GAA4B,IAA5B;AACA,WAAKC,OAAL,GAA4B,IAA5B;AACA,WAAKE,SAAL,GAA4B,IAA5B;AACA,WAAKC,QAAL,GAA4B,IAA5B;AACA,WAAKC,kBAAL,GAA4B,IAA5B;AACA,WAAKC,oBAAL,GAA4B,IAA5B;AACA,WAAKE,oBAAL,GAA4B,IAA5B;AACA,WAAKC,eAAL,GAA4B,IAA5B;AACD,KA7MiB;;AAgNlB;;AAhNkB,oBAkNlBV,UAlNkB,uBAkNPF,MAlNO,EAkNC;AACjBA,eAASxC,EAAEwF,MAAF,CAAS,EAAT,EAAa7E,OAAb,EAAsB6B,MAAtB,CAAT;AACAiB,WAAKgC,eAAL,CAAqBxF,IAArB,EAA2BuC,MAA3B,EAAmCxB,WAAnC;AACA,aAAOwB,MAAP;AACD,KAtNiB;;AAAA,oBAwNlBoC,YAxNkB,yBAwNLtB,aAxNK,EAwNU;AAAA;;AAC1B,UAAMwB,aAAarB,KAAKC,qBAAL,MACjB1D,EAAE,KAAK2C,QAAP,EAAiBgB,QAAjB,CAA0B9B,UAAUI,IAApC,CADF;;AAGA,UAAI,CAAC,KAAKU,QAAL,CAAc+C,UAAf,IACD,KAAK/C,QAAL,CAAc+C,UAAd,CAAyBC,QAAzB,KAAsCC,KAAKC,YAD9C,EAC4D;AAC1D;AACA5B,iBAASC,IAAT,CAAc4B,WAAd,CAA0B,KAAKnD,QAA/B;AACD;;AAED,WAAKA,QAAL,CAAcoD,KAAd,CAAoBC,OAApB,GAA8B,OAA9B;AACA,WAAKrD,QAAL,CAAcsD,eAAd,CAA8B,aAA9B;AACA,WAAKtD,QAAL,CAAcuD,SAAd,GAA0B,CAA1B;;AAEA,UAAIpB,UAAJ,EAAgB;AACdrB,aAAK0C,MAAL,CAAY,KAAKxD,QAAjB;AACD;;AAED3C,QAAE,KAAK2C,QAAP,EAAiBwB,QAAjB,CAA0BtC,UAAUT,IAApC;;AAEA,UAAI,KAAKqB,OAAL,CAAa3B,KAAjB,EAAwB;AACtB,aAAKsF,aAAL;AACD;;AAED,UAAMC,aAAarG,EAAEiB,KAAF,CAAQA,MAAMI,KAAd,EAAqB;AACtCiC;AADsC,OAArB,CAAnB;;AAIA,UAAMgD,qBAAqB,SAArBA,kBAAqB,GAAM;AAC/B,YAAI,OAAK7D,OAAL,CAAa3B,KAAjB,EAAwB;AACtB,iBAAK6B,QAAL,CAAc7B,KAAd;AACD;AACD,eAAKoC,gBAAL,GAAwB,KAAxB;AACAlD,UAAE,OAAK2C,QAAP,EAAiBkB,OAAjB,CAAyBwC,UAAzB;AACD,OAND;;AAQA,UAAIvB,UAAJ,EAAgB;AACd9E,UAAE,KAAK4C,OAAP,EACG4B,GADH,CACOf,KAAKyB,cADZ,EAC4BoB,kBAD5B,EAEGlB,oBAFH,CAEwB5E,mBAFxB;AAGD,OAJD,MAIO;AACL8F;AACD;AACF,KAnQiB;;AAAA,oBAqQlBF,aArQkB,4BAqQF;AAAA;;AACdpG,QAAEiE,QAAF,EACGe,GADH,CACO/D,MAAMK,OADb,EACsB;AADtB,OAEGgD,EAFH,CAEMrD,MAAMK,OAFZ,EAEqB,UAACiD,KAAD,EAAW;AAC5B,YAAIN,aAAaM,MAAME,MAAnB,IACA,OAAK9B,QAAL,KAAkB4B,MAAME,MADxB,IAEA,CAACzE,EAAE,OAAK2C,QAAP,EAAiB4D,GAAjB,CAAqBhC,MAAME,MAA3B,EAAmC+B,MAFxC,EAEgD;AAC9C,iBAAK7D,QAAL,CAAc7B,KAAd;AACD;AACF,OARH;AASD,KA/QiB;;AAAA,oBAiRlBsD,eAjRkB,8BAiRA;AAAA;;AAChB,UAAI,KAAKrB,QAAL,IAAiB,KAAKN,OAAL,CAAa5B,QAAlC,EAA4C;AAC1Cb,UAAE,KAAK2C,QAAP,EAAiB2B,EAAjB,CAAoBrD,MAAMQ,eAA1B,EAA2C,UAAC8C,KAAD,EAAW;AACpD,cAAIA,MAAMkC,KAAN,KAAgB/F,cAApB,EAAoC;AAClC,mBAAK6C,IAAL;AACD;AACF,SAJD;AAMD,OAPD,MAOO,IAAI,CAAC,KAAKR,QAAV,EAAoB;AACzB/C,UAAE,KAAK2C,QAAP,EAAiBqC,GAAjB,CAAqB/D,MAAMQ,eAA3B;AACD;AACF,KA5RiB;;AAAA,oBA8RlB4C,eA9RkB,8BA8RA;AAAA;;AAChB,UAAI,KAAKtB,QAAT,EAAmB;AACjB/C,UAAEuF,MAAF,EAAUjB,EAAV,CAAarD,MAAMM,MAAnB,EAA2B,UAACgD,KAAD;AAAA,iBAAW,OAAKmC,aAAL,CAAmBnC,KAAnB,CAAX;AAAA,SAA3B;AACD,OAFD,MAEO;AACLvE,UAAEuF,MAAF,EAAUP,GAAV,CAAc/D,MAAMM,MAApB;AACD;AACF,KApSiB;;AAAA,oBAsSlB4D,UAtSkB,yBAsSL;AAAA;;AACX,WAAKxC,QAAL,CAAcoD,KAAd,CAAoBC,OAApB,GAA8B,MAA9B;AACA,WAAKrD,QAAL,CAAcgE,YAAd,CAA2B,aAA3B,EAA0C,MAA1C;AACA,WAAKzD,gBAAL,GAAwB,KAAxB;AACA,WAAKyB,aAAL,CAAmB,YAAM;AACvB3E,UAAEiE,SAASC,IAAX,EAAiBe,WAAjB,CAA6BpD,UAAUG,IAAvC;AACA,eAAK4E,iBAAL;AACA,eAAKC,eAAL;AACA7G,UAAE,OAAK2C,QAAP,EAAiBkB,OAAjB,CAAyB5C,MAAME,MAA/B;AACD,OALD;AAMD,KAhTiB;;AAAA,oBAkTlB2F,eAlTkB,8BAkTA;AAChB,UAAI,KAAKhE,SAAT,EAAoB;AAClB9C,UAAE,KAAK8C,SAAP,EAAkBiE,MAAlB;AACA,aAAKjE,SAAL,GAAiB,IAAjB;AACD;AACF,KAvTiB;;AAAA,oBAyTlB6B,aAzTkB,0BAyTJqC,QAzTI,EAyTM;AAAA;;AACtB,UAAMC,UAAUjH,EAAE,KAAK2C,QAAP,EAAiBgB,QAAjB,CAA0B9B,UAAUI,IAApC,IACdJ,UAAUI,IADI,GACG,EADnB;;AAGA,UAAI,KAAKc,QAAL,IAAiB,KAAKN,OAAL,CAAa7B,QAAlC,EAA4C;AAC1C,YAAMsG,YAAYzD,KAAKC,qBAAL,MAAgCuD,OAAlD;;AAEA,aAAKnE,SAAL,GAAiBmB,SAASkD,aAAT,CAAuB,KAAvB,CAAjB;AACA,aAAKrE,SAAL,CAAesE,SAAf,GAA2BvF,UAAUE,QAArC;;AAEA,YAAIkF,OAAJ,EAAa;AACXjH,YAAE,KAAK8C,SAAP,EAAkBqB,QAAlB,CAA2B8C,OAA3B;AACD;;AAEDjH,UAAE,KAAK8C,SAAP,EAAkBuE,QAAlB,CAA2BpD,SAASC,IAApC;;AAEAlE,UAAE,KAAK2C,QAAP,EAAiB2B,EAAjB,CAAoBrD,MAAMO,aAA1B,EAAyC,UAAC+C,KAAD,EAAW;AAClD,cAAI,OAAKtB,oBAAT,EAA+B;AAC7B,mBAAKA,oBAAL,GAA4B,KAA5B;AACA;AACD;AACD,cAAIsB,MAAME,MAAN,KAAiBF,MAAM+C,aAA3B,EAA0C;AACxC;AACD;AACD,cAAI,OAAK7E,OAAL,CAAa7B,QAAb,KAA0B,QAA9B,EAAwC;AACtC,mBAAK+B,QAAL,CAAc7B,KAAd;AACD,WAFD,MAEO;AACL,mBAAKyC,IAAL;AACD;AACF,SAbD;;AAeA,YAAI2D,SAAJ,EAAe;AACbzD,eAAK0C,MAAL,CAAY,KAAKrD,SAAjB;AACD;;AAED9C,UAAE,KAAK8C,SAAP,EAAkBqB,QAAlB,CAA2BtC,UAAUT,IAArC;;AAEA,YAAI,CAAC4F,QAAL,EAAe;AACb;AACD;;AAED,YAAI,CAACE,SAAL,EAAgB;AACdF;AACA;AACD;;AAEDhH,UAAE,KAAK8C,SAAP,EACG0B,GADH,CACOf,KAAKyB,cADZ,EAC4B8B,QAD5B,EAEG5B,oBAFH,CAEwB3E,4BAFxB;AAID,OA9CD,MA8CO,IAAI,CAAC,KAAKsC,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;AAC3C9C,UAAE,KAAK8C,SAAP,EAAkBmC,WAAlB,CAA8BpD,UAAUT,IAAxC;;AAEA,YAAMmG,iBAAiB,SAAjBA,cAAiB,GAAM;AAC3B,iBAAKT,eAAL;AACA,cAAIE,QAAJ,EAAc;AACZA;AACD;AACF,SALD;;AAOA,YAAIvD,KAAKC,qBAAL,MACD1D,EAAE,KAAK2C,QAAP,EAAiBgB,QAAjB,CAA0B9B,UAAUI,IAApC,CADH,EAC8C;AAC5CjC,YAAE,KAAK8C,SAAP,EACG0B,GADH,CACOf,KAAKyB,cADZ,EAC4BqC,cAD5B,EAEGnC,oBAFH,CAEwB3E,4BAFxB;AAGD,SALD,MAKO;AACL8G;AACD;AAEF,OAnBM,MAmBA,IAAIP,QAAJ,EAAc;AACnBA;AACD;AACF,KAjYiB;;AAoYlB;AACA;AACA;AACA;;AAvYkB,oBAyYlBN,aAzYkB,4BAyYF;AACd,WAAKc,aAAL;AACD,KA3YiB;;AAAA,oBA6YlBA,aA7YkB,4BA6YF;AACd,UAAMC,qBACJ,KAAK9E,QAAL,CAAc+E,YAAd,GAA6BzD,SAAS0D,eAAT,CAAyBC,YADxD;;AAGA,UAAI,CAAC,KAAK5E,kBAAN,IAA4ByE,kBAAhC,EAAoD;AAClD,aAAK9E,QAAL,CAAcoD,KAAd,CAAoB8B,WAApB,GAAqC,KAAKzE,eAA1C;AACD;;AAED,UAAI,KAAKJ,kBAAL,IAA2B,CAACyE,kBAAhC,EAAoD;AAClD,aAAK9E,QAAL,CAAcoD,KAAd,CAAoB+B,YAApB,GAAsC,KAAK1E,eAA3C;AACD;AACF,KAxZiB;;AAAA,oBA0ZlBwD,iBA1ZkB,gCA0ZE;AAClB,WAAKjE,QAAL,CAAcoD,KAAd,CAAoB8B,WAApB,GAAkC,EAAlC;AACA,WAAKlF,QAAL,CAAcoD,KAAd,CAAoB+B,YAApB,GAAmC,EAAnC;AACD,KA7ZiB;;AAAA,oBA+ZlB/D,eA/ZkB,8BA+ZA;AAChB,WAAKf,kBAAL,GAA0BiB,SAASC,IAAT,CAAc6D,WAAd,GAA4BxC,OAAOyC,UAA7D;AACA,WAAK5E,eAAL,GAAuB,KAAK6E,kBAAL,EAAvB;AACD,KAlaiB;;AAAA,oBAoalBjE,aApakB,4BAoaF;AACd,UAAMkE,cAAcC,SAClBnI,EAAEkC,SAASI,aAAX,EAA0B8F,GAA1B,CAA8B,eAA9B,KAAkD,CADhC,EAElB,EAFkB,CAApB;;AAKA,WAAKjF,oBAAL,GAA4Bc,SAASC,IAAT,CAAc6B,KAAd,CAAoB+B,YAApB,IAAoC,EAAhE;;AAEA,UAAI,KAAK9E,kBAAT,EAA6B;AAC3BiB,iBAASC,IAAT,CAAc6B,KAAd,CAAoB+B,YAApB,GACKI,cAAc,KAAK9E,eADxB;AAED;AACF,KAhbiB;;AAAA,oBAkblByD,eAlbkB,8BAkbA;AAChB5C,eAASC,IAAT,CAAc6B,KAAd,CAAoB+B,YAApB,GAAmC,KAAK3E,oBAAxC;AACD,KApbiB;;AAAA,oBAsblB8E,kBAtbkB,iCAsbG;AAAE;AACrB,UAAMI,YAAYpE,SAASkD,aAAT,CAAuB,KAAvB,CAAlB;AACAkB,gBAAUjB,SAAV,GAAsBvF,UAAUC,kBAAhC;AACAmC,eAASC,IAAT,CAAc4B,WAAd,CAA0BuC,SAA1B;AACA,UAAMC,iBAAiBD,UAAUE,WAAV,GAAwBF,UAAUN,WAAzD;AACA9D,eAASC,IAAT,CAAcsE,WAAd,CAA0BH,SAA1B;AACA,aAAOC,cAAP;AACD,KA7biB;;AAgclB;;AAhckB,UAkcXG,gBAlcW,6BAkcMjG,MAlcN,EAkccc,aAlcd,EAkc6B;AAC7C,aAAO,KAAKoF,IAAL,CAAU,YAAY;AAC3B,YAAIC,OAAY3I,EAAE,IAAF,EAAQ2I,IAAR,CAAaxI,QAAb,CAAhB;AACA,YAAMsC,UAAUzC,EAAEwF,MAAF,CACd,EADc,EAEdzF,MAAMY,OAFQ,EAGdX,EAAE,IAAF,EAAQ2I,IAAR,EAHc,EAId,QAAOnG,MAAP,yCAAOA,MAAP,OAAkB,QAAlB,IAA8BA,MAJhB,CAAhB;;AAOA,YAAI,CAACmG,IAAL,EAAW;AACTA,iBAAO,IAAI5I,KAAJ,CAAU,IAAV,EAAgB0C,OAAhB,CAAP;AACAzC,YAAE,IAAF,EAAQ2I,IAAR,CAAaxI,QAAb,EAAuBwI,IAAvB;AACD;;AAED,YAAI,OAAOnG,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,cAAImG,KAAKnG,MAAL,MAAiBoG,SAArB,EAAgC;AAC9B,kBAAM,IAAIpF,KAAJ,uBAA8BhB,MAA9B,OAAN;AACD;AACDmG,eAAKnG,MAAL,EAAac,aAAb;AACD,SALD,MAKO,IAAIb,QAAQ1B,IAAZ,EAAkB;AACvB4H,eAAK5H,IAAL,CAAUuC,aAAV;AACD;AACF,OAtBM,CAAP;AAuBD,KA1diB;;AAAA;AAAA;AAAA,0BAuFG;AACnB,eAAOpD,OAAP;AACD;AAzFiB;AAAA;AAAA,0BA2FG;AACnB,eAAOS,OAAP;AACD;AA7FiB;;AAAA;AAAA;;AA+dpB;;;;;;AAMAX,IAAEiE,QAAF,EAAYK,EAAZ,CAAerD,MAAMW,cAArB,EAAqCM,SAASE,WAA9C,EAA2D,UAAUmC,KAAV,EAAiB;AAAA;;AAC1E,QAAIE,eAAJ;AACA,QAAMoE,WAAWpF,KAAKqF,sBAAL,CAA4B,IAA5B,CAAjB;;AAEA,QAAID,QAAJ,EAAc;AACZpE,eAASzE,EAAE6I,QAAF,EAAY,CAAZ,CAAT;AACD;;AAED,QAAMrG,SAASxC,EAAEyE,MAAF,EAAUkE,IAAV,CAAexI,QAAf,IACb,QADa,GACFH,EAAEwF,MAAF,CAAS,EAAT,EAAaxF,EAAEyE,MAAF,EAAUkE,IAAV,EAAb,EAA+B3I,EAAE,IAAF,EAAQ2I,IAAR,EAA/B,CADb;;AAGA,QAAI,KAAKI,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;AACnDxE,YAAMM,cAAN;AACD;;AAED,QAAMmE,UAAUhJ,EAAEyE,MAAF,EAAUD,GAAV,CAAcvD,MAAMG,IAApB,EAA0B,UAACwC,SAAD,EAAe;AACvD,UAAIA,UAAUE,kBAAV,EAAJ,EAAoC;AAClC;AACA;AACD;;AAEDkF,cAAQxE,GAAR,CAAYvD,MAAME,MAAlB,EAA0B,YAAM;AAC9B,YAAInB,UAAQ0E,EAAR,CAAW,UAAX,CAAJ,EAA4B;AAC1B,iBAAK5D,KAAL;AACD;AACF,OAJD;AAKD,KAXe,CAAhB;;AAaAf,UAAM0I,gBAAN,CAAuBQ,IAAvB,CAA4BjJ,EAAEyE,MAAF,CAA5B,EAAuCjC,MAAvC,EAA+C,IAA/C;AACD,GA7BD;;AAgCA;;;;;;AAMAxC,IAAEO,EAAF,CAAKN,IAAL,IAAyBF,MAAM0I,gBAA/B;AACAzI,IAAEO,EAAF,CAAKN,IAAL,EAAWiJ,WAAX,GAAyBnJ,KAAzB;AACAC,IAAEO,EAAF,CAAKN,IAAL,EAAWkJ,UAAX,GAAyB,YAAY;AACnCnJ,MAAEO,EAAF,CAAKN,IAAL,IAAaK,kBAAb;AACA,WAAOP,MAAM0I,gBAAb;AACD,GAHD;;AAKA,SAAO1I,KAAP;AAED,CAphBa,CAohBXqJ,MAphBW,CAAd", "file": "modal.js", "sourcesContent": ["import Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-alpha.6): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Modal = (($) => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                         = 'modal'\n  const VERSION                      = '4.0.0-alpha.6'\n  const DATA_KEY                     = 'bs.modal'\n  const EVENT_KEY                    = `.${DATA_KEY}`\n  const DATA_API_KEY                 = '.data-api'\n  const JQUERY_NO_CONFLICT           = $.fn[NAME]\n  const TRANSITION_DURATION          = 300\n  const BACKDROP_TRANSITION_DURATION = 150\n  const ESCAPE_KEYCODE               = 27 // KeyboardEvent.which value for Escape (Esc) key\n\n  const Default = {\n    backdrop : true,\n    keyboard : true,\n    focus    : true,\n    show     : true\n  }\n\n  const DefaultType = {\n    backdrop : '(boolean|string)',\n    keyboard : 'boolean',\n    focus    : 'boolean',\n    show     : 'boolean'\n  }\n\n  const Event = {\n    HIDE              : `hide${EVENT_KEY}`,\n    HIDDEN            : `hidden${EVENT_KEY}`,\n    SHOW              : `show${EVENT_KEY}`,\n    SHOWN             : `shown${EVENT_KEY}`,\n    FOCUSIN           : `focusin${EVENT_KEY}`,\n    RESIZE            : `resize${EVENT_KEY}`,\n    CLICK_DISMISS     : `click.dismiss${EVENT_KEY}`,\n    KEYDOWN_DISMISS   : `keydown.dismiss${EVENT_KEY}`,\n    MOUSEUP_DISMISS   : `mouseup.dismiss${EVENT_KEY}`,\n    MOUSEDOWN_DISMISS : `mousedown.dismiss${EVENT_KEY}`,\n    CLICK_DATA_API    : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    SCROLLBAR_MEASURER : 'modal-scrollbar-measure',\n    BACKDROP           : 'modal-backdrop',\n    OPEN               : 'modal-open',\n    FADE               : 'fade',\n    SHOW               : 'show'\n  }\n\n  const Selector = {\n    DIALOG             : '.modal-dialog',\n    DATA_TOGGLE        : '[data-toggle=\"modal\"]',\n    DATA_DISMISS       : '[data-dismiss=\"modal\"]',\n    FIXED_CONTENT      : '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Modal {\n\n    constructor(element, config) {\n      this._config              = this._getConfig(config)\n      this._element             = element\n      this._dialog              = $(element).find(Selector.DIALOG)[0]\n      this._backdrop            = null\n      this._isShown             = false\n      this._isBodyOverflowing   = false\n      this._ignoreBackdropClick = false\n      this._isTransitioning     = false\n      this._originalBodyPadding = 0\n      this._scrollbarWidth      = 0\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n\n    // public\n\n    toggle(relatedTarget) {\n      return this._isShown ? this.hide() : this.show(relatedTarget)\n    }\n\n    show(relatedTarget) {\n      if (this._isTransitioning) {\n        throw new Error('Modal is transitioning')\n      }\n\n      if (Util.supportsTransitionEnd() &&\n        $(this._element).hasClass(ClassName.FADE)) {\n        this._isTransitioning = true\n      }\n      const showEvent = $.Event(Event.SHOW, {\n        relatedTarget\n      })\n\n      $(this._element).trigger(showEvent)\n\n      if (this._isShown || showEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._isShown = true\n\n      this._checkScrollbar()\n      this._setScrollbar()\n\n      $(document.body).addClass(ClassName.OPEN)\n\n      this._setEscapeEvent()\n      this._setResizeEvent()\n\n      $(this._element).on(\n        Event.CLICK_DISMISS,\n        Selector.DATA_DISMISS,\n        (event) => this.hide(event)\n      )\n\n      $(this._dialog).on(Event.MOUSEDOWN_DISMISS, () => {\n        $(this._element).one(Event.MOUSEUP_DISMISS, (event) => {\n          if ($(event.target).is(this._element)) {\n            this._ignoreBackdropClick = true\n          }\n        })\n      })\n\n      this._showBackdrop(() => this._showElement(relatedTarget))\n    }\n\n    hide(event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      if (this._isTransitioning) {\n        throw new Error('Modal is transitioning')\n      }\n\n      const transition = Util.supportsTransitionEnd() &&\n        $(this._element).hasClass(ClassName.FADE)\n      if (transition) {\n        this._isTransitioning = true\n      }\n\n      const hideEvent = $.Event(Event.HIDE)\n      $(this._element).trigger(hideEvent)\n\n      if (!this._isShown || hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._isShown = false\n\n      this._setEscapeEvent()\n      this._setResizeEvent()\n\n      $(document).off(Event.FOCUSIN)\n\n      $(this._element).removeClass(ClassName.SHOW)\n\n      $(this._element).off(Event.CLICK_DISMISS)\n      $(this._dialog).off(Event.MOUSEDOWN_DISMISS)\n\n      if (transition) {\n        $(this._element)\n          .one(Util.TRANSITION_END, (event) => this._hideModal(event))\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        this._hideModal()\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n\n      $(window, document, this._element, this._backdrop).off(EVENT_KEY)\n\n      this._config              = null\n      this._element             = null\n      this._dialog              = null\n      this._backdrop            = null\n      this._isShown             = null\n      this._isBodyOverflowing   = null\n      this._ignoreBackdropClick = null\n      this._originalBodyPadding = null\n      this._scrollbarWidth      = null\n    }\n\n\n    // private\n\n    _getConfig(config) {\n      config = $.extend({}, Default, config)\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _showElement(relatedTarget) {\n      const transition = Util.supportsTransitionEnd() &&\n        $(this._element).hasClass(ClassName.FADE)\n\n      if (!this._element.parentNode ||\n         this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n        // don't move modals dom position\n        document.body.appendChild(this._element)\n      }\n\n      this._element.style.display = 'block'\n      this._element.removeAttribute('aria-hidden')\n      this._element.scrollTop = 0\n\n      if (transition) {\n        Util.reflow(this._element)\n      }\n\n      $(this._element).addClass(ClassName.SHOW)\n\n      if (this._config.focus) {\n        this._enforceFocus()\n      }\n\n      const shownEvent = $.Event(Event.SHOWN, {\n        relatedTarget\n      })\n\n      const transitionComplete = () => {\n        if (this._config.focus) {\n          this._element.focus()\n        }\n        this._isTransitioning = false\n        $(this._element).trigger(shownEvent)\n      }\n\n      if (transition) {\n        $(this._dialog)\n          .one(Util.TRANSITION_END, transitionComplete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        transitionComplete()\n      }\n    }\n\n    _enforceFocus() {\n      $(document)\n        .off(Event.FOCUSIN) // guard against infinite focus loop\n        .on(Event.FOCUSIN, (event) => {\n          if (document !== event.target &&\n              this._element !== event.target &&\n              !$(this._element).has(event.target).length) {\n            this._element.focus()\n          }\n        })\n    }\n\n    _setEscapeEvent() {\n      if (this._isShown && this._config.keyboard) {\n        $(this._element).on(Event.KEYDOWN_DISMISS, (event) => {\n          if (event.which === ESCAPE_KEYCODE) {\n            this.hide()\n          }\n        })\n\n      } else if (!this._isShown) {\n        $(this._element).off(Event.KEYDOWN_DISMISS)\n      }\n    }\n\n    _setResizeEvent() {\n      if (this._isShown) {\n        $(window).on(Event.RESIZE, (event) => this._handleUpdate(event))\n      } else {\n        $(window).off(Event.RESIZE)\n      }\n    }\n\n    _hideModal() {\n      this._element.style.display = 'none'\n      this._element.setAttribute('aria-hidden', 'true')\n      this._isTransitioning = false\n      this._showBackdrop(() => {\n        $(document.body).removeClass(ClassName.OPEN)\n        this._resetAdjustments()\n        this._resetScrollbar()\n        $(this._element).trigger(Event.HIDDEN)\n      })\n    }\n\n    _removeBackdrop() {\n      if (this._backdrop) {\n        $(this._backdrop).remove()\n        this._backdrop = null\n      }\n    }\n\n    _showBackdrop(callback) {\n      const animate = $(this._element).hasClass(ClassName.FADE) ?\n        ClassName.FADE : ''\n\n      if (this._isShown && this._config.backdrop) {\n        const doAnimate = Util.supportsTransitionEnd() && animate\n\n        this._backdrop = document.createElement('div')\n        this._backdrop.className = ClassName.BACKDROP\n\n        if (animate) {\n          $(this._backdrop).addClass(animate)\n        }\n\n        $(this._backdrop).appendTo(document.body)\n\n        $(this._element).on(Event.CLICK_DISMISS, (event) => {\n          if (this._ignoreBackdropClick) {\n            this._ignoreBackdropClick = false\n            return\n          }\n          if (event.target !== event.currentTarget) {\n            return\n          }\n          if (this._config.backdrop === 'static') {\n            this._element.focus()\n          } else {\n            this.hide()\n          }\n        })\n\n        if (doAnimate) {\n          Util.reflow(this._backdrop)\n        }\n\n        $(this._backdrop).addClass(ClassName.SHOW)\n\n        if (!callback) {\n          return\n        }\n\n        if (!doAnimate) {\n          callback()\n          return\n        }\n\n        $(this._backdrop)\n          .one(Util.TRANSITION_END, callback)\n          .emulateTransitionEnd(BACKDROP_TRANSITION_DURATION)\n\n      } else if (!this._isShown && this._backdrop) {\n        $(this._backdrop).removeClass(ClassName.SHOW)\n\n        const callbackRemove = () => {\n          this._removeBackdrop()\n          if (callback) {\n            callback()\n          }\n        }\n\n        if (Util.supportsTransitionEnd() &&\n           $(this._element).hasClass(ClassName.FADE)) {\n          $(this._backdrop)\n            .one(Util.TRANSITION_END, callbackRemove)\n            .emulateTransitionEnd(BACKDROP_TRANSITION_DURATION)\n        } else {\n          callbackRemove()\n        }\n\n      } else if (callback) {\n        callback()\n      }\n    }\n\n\n    // ----------------------------------------------------------------------\n    // the following methods are used to handle overflowing modals\n    // todo (fat): these should probably be refactored out of modal.js\n    // ----------------------------------------------------------------------\n\n    _handleUpdate() {\n      this._adjustDialog()\n    }\n\n    _adjustDialog() {\n      const isModalOverflowing =\n        this._element.scrollHeight > document.documentElement.clientHeight\n\n      if (!this._isBodyOverflowing && isModalOverflowing) {\n        this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n      }\n\n      if (this._isBodyOverflowing && !isModalOverflowing) {\n        this._element.style.paddingRight = `${this._scrollbarWidth}px`\n      }\n    }\n\n    _resetAdjustments() {\n      this._element.style.paddingLeft = ''\n      this._element.style.paddingRight = ''\n    }\n\n    _checkScrollbar() {\n      this._isBodyOverflowing = document.body.clientWidth < window.innerWidth\n      this._scrollbarWidth = this._getScrollbarWidth()\n    }\n\n    _setScrollbar() {\n      const bodyPadding = parseInt(\n        $(Selector.FIXED_CONTENT).css('padding-right') || 0,\n        10\n      )\n\n      this._originalBodyPadding = document.body.style.paddingRight || ''\n\n      if (this._isBodyOverflowing) {\n        document.body.style.paddingRight =\n          `${bodyPadding + this._scrollbarWidth}px`\n      }\n    }\n\n    _resetScrollbar() {\n      document.body.style.paddingRight = this._originalBodyPadding\n    }\n\n    _getScrollbarWidth() { // thx d.walsh\n      const scrollDiv = document.createElement('div')\n      scrollDiv.className = ClassName.SCROLLBAR_MEASURER\n      document.body.appendChild(scrollDiv)\n      const scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth\n      document.body.removeChild(scrollDiv)\n      return scrollbarWidth\n    }\n\n\n    // static\n\n    static _jQueryInterface(config, relatedTarget) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = $.extend(\n          {},\n          Modal.Default,\n          $(this).data(),\n          typeof config === 'object' && config\n        )\n\n        if (!data) {\n          data = new Modal(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (data[config] === undefined) {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config](relatedTarget)\n        } else if (_config.show) {\n          data.show(relatedTarget)\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    let target\n    const selector = Util.getSelectorFromElement(this)\n\n    if (selector) {\n      target = $(selector)[0]\n    }\n\n    const config = $(target).data(DATA_KEY) ?\n      'toggle' : $.extend({}, $(target).data(), $(this).data())\n\n    if (this.tagName === 'A' || this.tagName === 'AREA') {\n      event.preventDefault()\n    }\n\n    const $target = $(target).one(Event.SHOW, (showEvent) => {\n      if (showEvent.isDefaultPrevented()) {\n        // only register focus restorer if modal will actually get shown\n        return\n      }\n\n      $target.one(Event.HIDDEN, () => {\n        if ($(this).is(':visible')) {\n          this.focus()\n        }\n      })\n    })\n\n    Modal._jQueryInterface.call($(target), config, this)\n  })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Modal._jQueryInterface\n  $.fn[NAME].Constructor = Modal\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Modal._jQueryInterface\n  }\n\n  return Modal\n\n})(jQuery)\n\nexport default Modal\n"]}
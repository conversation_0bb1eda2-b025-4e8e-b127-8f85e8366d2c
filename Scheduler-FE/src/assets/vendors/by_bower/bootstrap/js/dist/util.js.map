{"version": 3, "sources": ["../src/util.js"], "names": ["<PERSON><PERSON>", "$", "transition", "MAX_UID", "TransitionEndEvent", "WebkitTransition", "MozTransition", "OTransition", "toType", "obj", "toString", "call", "match", "toLowerCase", "isElement", "nodeType", "getSpecialTransitionEndEvent", "bindType", "end", "delegateType", "handle", "event", "target", "is", "handleObj", "handler", "apply", "arguments", "undefined", "transitionEndTest", "window", "QUnit", "el", "document", "createElement", "name", "style", "transitionEndEmulator", "duration", "called", "one", "TRANSITION_END", "setTimeout", "triggerTransitionEnd", "setTransitionEndSupport", "fn", "emulateTransitionEnd", "supportsTransitionEnd", "special", "getUID", "prefix", "Math", "random", "getElementById", "getSelectorFromElement", "element", "selector", "getAttribute", "test", "reflow", "offsetHeight", "trigger", "Boolean", "typeCheckConfig", "componentName", "config", "configTypes", "property", "hasOwnProperty", "expectedTypes", "value", "valueType", "RegExp", "Error", "toUpperCase", "j<PERSON><PERSON><PERSON>"], "mappings": "AAAA;;;;;;;AAOA,IAAMA,OAAQ,UAACC,CAAD,EAAO;;AAGnB;;;;;;AAMA,MAAIC,aAAa,KAAjB;;AAEA,MAAMC,UAAU,OAAhB;;AAEA,MAAMC,qBAAqB;AACzBC,sBAAmB,qBADM;AAEzBC,mBAAmB,eAFM;AAGzBC,iBAAmB,+BAHM;AAIzBL,gBAAmB;AAJM,GAA3B;;AAOA;AACA,WAASM,MAAT,CAAgBC,GAAhB,EAAqB;AACnB,WAAO,GAAGC,QAAH,CAAYC,IAAZ,CAAiBF,GAAjB,EAAsBG,KAAtB,CAA4B,eAA5B,EAA6C,CAA7C,EAAgDC,WAAhD,EAAP;AACD;;AAED,WAASC,SAAT,CAAmBL,GAAnB,EAAwB;AACtB,WAAO,CAACA,IAAI,CAAJ,KAAUA,GAAX,EAAgBM,QAAvB;AACD;;AAED,WAASC,4BAAT,GAAwC;AACtC,WAAO;AACLC,gBAAUf,WAAWgB,GADhB;AAELC,oBAAcjB,WAAWgB,GAFpB;AAGLE,YAHK,kBAGEC,KAHF,EAGS;AACZ,YAAIpB,EAAEoB,MAAMC,MAAR,EAAgBC,EAAhB,CAAmB,IAAnB,CAAJ,EAA8B;AAC5B,iBAAOF,MAAMG,SAAN,CAAgBC,OAAhB,CAAwBC,KAAxB,CAA8B,IAA9B,EAAoCC,SAApC,CAAP,CAD4B,CAC0B;AACvD;AACD,eAAOC,SAAP;AACD;AARI,KAAP;AAUD;;AAED,WAASC,iBAAT,GAA6B;AAC3B,QAAIC,OAAOC,KAAX,EAAkB;AAChB,aAAO,KAAP;AACD;;AAED,QAAMC,KAAKC,SAASC,aAAT,CAAuB,WAAvB,CAAX;;AAEA,SAAK,IAAMC,IAAX,IAAmB/B,kBAAnB,EAAuC;AACrC,UAAI4B,GAAGI,KAAH,CAASD,IAAT,MAAmBP,SAAvB,EAAkC;AAChC,eAAO;AACLV,eAAKd,mBAAmB+B,IAAnB;AADA,SAAP;AAGD;AACF;;AAED,WAAO,KAAP;AACD;;AAED,WAASE,qBAAT,CAA+BC,QAA/B,EAAyC;AAAA;;AACvC,QAAIC,SAAS,KAAb;;AAEAtC,MAAE,IAAF,EAAQuC,GAAR,CAAYxC,KAAKyC,cAAjB,EAAiC,YAAM;AACrCF,eAAS,IAAT;AACD,KAFD;;AAIAG,eAAW,YAAM;AACf,UAAI,CAACH,MAAL,EAAa;AACXvC,aAAK2C,oBAAL;AACD;AACF,KAJD,EAIGL,QAJH;;AAMA,WAAO,IAAP;AACD;;AAED,WAASM,uBAAT,GAAmC;AACjC1C,iBAAa2B,mBAAb;;AAEA5B,MAAE4C,EAAF,CAAKC,oBAAL,GAA4BT,qBAA5B;;AAEA,QAAIrC,KAAK+C,qBAAL,EAAJ,EAAkC;AAChC9C,QAAEoB,KAAF,CAAQ2B,OAAR,CAAgBhD,KAAKyC,cAArB,IAAuCzB,8BAAvC;AACD;AACF;;AAGD;;;;;;AAMA,MAAMhB,OAAO;;AAEXyC,oBAAgB,iBAFL;;AAIXQ,UAJW,kBAIJC,MAJI,EAII;AACb,SAAG;AACD;AACAA,kBAAU,CAAC,EAAEC,KAAKC,MAAL,KAAgBjD,OAAlB,CAAX,CAFC,CAEqC;AACvC,OAHD,QAGS8B,SAASoB,cAAT,CAAwBH,MAAxB,CAHT;AAIA,aAAOA,MAAP;AACD,KAVU;AAYXI,0BAZW,kCAYYC,OAZZ,EAYqB;AAC9B,UAAIC,WAAWD,QAAQE,YAAR,CAAqB,aAArB,CAAf;;AAEA,UAAI,CAACD,QAAL,EAAe;AACbA,mBAAWD,QAAQE,YAAR,CAAqB,MAArB,KAAgC,EAA3C;AACAD,mBAAW,WAAWE,IAAX,CAAgBF,QAAhB,IAA4BA,QAA5B,GAAuC,IAAlD;AACD;;AAED,aAAOA,QAAP;AACD,KArBU;AAuBXG,UAvBW,kBAuBJJ,OAvBI,EAuBK;AACd,aAAOA,QAAQK,YAAf;AACD,KAzBU;AA2BXjB,wBA3BW,gCA2BUY,OA3BV,EA2BmB;AAC5BtD,QAAEsD,OAAF,EAAWM,OAAX,CAAmB3D,WAAWgB,GAA9B;AACD,KA7BU;AA+BX6B,yBA/BW,mCA+Ba;AACtB,aAAOe,QAAQ5D,UAAR,CAAP;AACD,KAjCU;AAmCX6D,mBAnCW,2BAmCKC,aAnCL,EAmCoBC,MAnCpB,EAmC4BC,WAnC5B,EAmCyC;AAClD,WAAK,IAAMC,QAAX,IAAuBD,WAAvB,EAAoC;AAClC,YAAIA,YAAYE,cAAZ,CAA2BD,QAA3B,CAAJ,EAA0C;AACxC,cAAME,gBAAgBH,YAAYC,QAAZ,CAAtB;AACA,cAAMG,QAAgBL,OAAOE,QAAP,CAAtB;AACA,cAAMI,YAAgBD,SAASxD,UAAUwD,KAAV,CAAT,GACA,SADA,GACY9D,OAAO8D,KAAP,CADlC;;AAGA,cAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BX,IAA1B,CAA+Ba,SAA/B,CAAL,EAAgD;AAC9C,kBAAM,IAAIE,KAAJ,CACDT,cAAcU,WAAd,EAAH,wBACWP,QADX,yBACuCI,SADvC,oCAEsBF,aAFtB,QADI,CAAN;AAID;AACF;AACF;AACF;AAnDU,GAAb;;AAsDAzB;;AAEA,SAAO5C,IAAP;AAED,CAvJY,CAuJV2E,MAvJU,CAAb", "file": "util.js", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-alpha.6): util.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Util = (($) => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Private TransitionEnd Helpers\n   * ------------------------------------------------------------------------\n   */\n\n  let transition = false\n\n  const MAX_UID = 1000000\n\n  const TransitionEndEvent = {\n    WebkitTransition : 'webkitTransitionEnd',\n    MozTransition    : 'transitionend',\n    OTransition      : 'oTransitionEnd otransitionend',\n    transition       : 'transitionend'\n  }\n\n  // shoutout AngusCroll (https://goo.gl/pxwQGp)\n  function toType(obj) {\n    return {}.toString.call(obj).match(/\\s([a-zA-Z]+)/)[1].toLowerCase()\n  }\n\n  function isElement(obj) {\n    return (obj[0] || obj).nodeType\n  }\n\n  function getSpecialTransitionEndEvent() {\n    return {\n      bindType: transition.end,\n      delegateType: transition.end,\n      handle(event) {\n        if ($(event.target).is(this)) {\n          return event.handleObj.handler.apply(this, arguments) // eslint-disable-line prefer-rest-params\n        }\n        return undefined\n      }\n    }\n  }\n\n  function transitionEndTest() {\n    if (window.QUnit) {\n      return false\n    }\n\n    const el = document.createElement('bootstrap')\n\n    for (const name in TransitionEndEvent) {\n      if (el.style[name] !== undefined) {\n        return {\n          end: TransitionEndEvent[name]\n        }\n      }\n    }\n\n    return false\n  }\n\n  function transitionEndEmulator(duration) {\n    let called = false\n\n    $(this).one(Util.TRANSITION_END, () => {\n      called = true\n    })\n\n    setTimeout(() => {\n      if (!called) {\n        Util.triggerTransitionEnd(this)\n      }\n    }, duration)\n\n    return this\n  }\n\n  function setTransitionEndSupport() {\n    transition = transitionEndTest()\n\n    $.fn.emulateTransitionEnd = transitionEndEmulator\n\n    if (Util.supportsTransitionEnd()) {\n      $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent()\n    }\n  }\n\n\n  /**\n   * --------------------------------------------------------------------------\n   * Public Util Api\n   * --------------------------------------------------------------------------\n   */\n\n  const Util = {\n\n    TRANSITION_END: 'bsTransitionEnd',\n\n    getUID(prefix) {\n      do {\n        // eslint-disable-next-line no-bitwise\n        prefix += ~~(Math.random() * MAX_UID) // \"~~\" acts like a faster Math.floor() here\n      } while (document.getElementById(prefix))\n      return prefix\n    },\n\n    getSelectorFromElement(element) {\n      let selector = element.getAttribute('data-target')\n\n      if (!selector) {\n        selector = element.getAttribute('href') || ''\n        selector = /^#[a-z]/i.test(selector) ? selector : null\n      }\n\n      return selector\n    },\n\n    reflow(element) {\n      return element.offsetHeight\n    },\n\n    triggerTransitionEnd(element) {\n      $(element).trigger(transition.end)\n    },\n\n    supportsTransitionEnd() {\n      return Boolean(transition)\n    },\n\n    typeCheckConfig(componentName, config, configTypes) {\n      for (const property in configTypes) {\n        if (configTypes.hasOwnProperty(property)) {\n          const expectedTypes = configTypes[property]\n          const value         = config[property]\n          const valueType     = value && isElement(value) ?\n                                'element' : toType(value)\n\n          if (!new RegExp(expectedTypes).test(valueType)) {\n            throw new Error(\n              `${componentName.toUpperCase()}: ` +\n              `Option \"${property}\" provided type \"${valueType}\" ` +\n              `but expected type \"${expectedTypes}\".`)\n          }\n        }\n      }\n    }\n  }\n\n  setTransitionEndSupport()\n\n  return Util\n\n})(jQuery)\n\nexport default Util\n"]}
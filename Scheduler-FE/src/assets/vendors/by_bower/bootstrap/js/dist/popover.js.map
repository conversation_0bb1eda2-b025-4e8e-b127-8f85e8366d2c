{"version": 3, "sources": ["../src/popover.js"], "names": ["Popover", "$", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "fn", "<PERSON><PERSON><PERSON>", "extend", "<PERSON><PERSON><PERSON>", "placement", "trigger", "content", "template", "DefaultType", "ClassName", "FADE", "SHOW", "Selector", "TITLE", "CONTENT", "Event", "HIDE", "HIDDEN", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "isWithContent", "getTitle", "_getContent", "getTipElement", "tip", "config", "<PERSON><PERSON><PERSON><PERSON>", "$tip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "removeClass", "cleanupTether", "element", "getAttribute", "call", "_jQueryInterface", "each", "data", "_config", "test", "undefined", "Error", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAGA;;;;;;;AAOA,IAAMA,UAAW,UAACC,CAAD,EAAO;;AAGtB;;;;;;AAMA,MAAMC,OAAsB,SAA5B;AACA,MAAMC,UAAsB,eAA5B;AACA,MAAMC,WAAsB,YAA5B;AACA,MAAMC,kBAA0BD,QAAhC;AACA,MAAME,qBAAsBL,EAAEM,EAAF,CAAKL,IAAL,CAA5B;;AAEA,MAAMM,UAAUP,EAAEQ,MAAF,CAAS,EAAT,EAAaC,QAAQF,OAArB,EAA8B;AAC5CG,eAAY,OADgC;AAE5CC,aAAY,OAFgC;AAG5CC,aAAY,EAHgC;AAI5CC,cAAY,yCACA,iCADA,GAEA;AANgC,GAA9B,CAAhB;;AASA,MAAMC,cAAcd,EAAEQ,MAAF,CAAS,EAAT,EAAaC,QAAQK,WAArB,EAAkC;AACpDF,aAAU;AAD0C,GAAlC,CAApB;;AAIA,MAAMG,YAAY;AAChBC,UAAO,MADS;AAEhBC,UAAO;AAFS,GAAlB;;AAKA,MAAMC,WAAW;AACfC,WAAU,gBADK;AAEfC,aAAU;AAFK,GAAjB;;AAKA,MAAMC,QAAQ;AACZC,mBAAoBlB,SADR;AAEZmB,uBAAsBnB,SAFV;AAGZa,mBAAoBb,SAHR;AAIZoB,qBAAqBpB,SAJT;AAKZqB,2BAAwBrB,SALZ;AAMZsB,qBAAqBtB,SANT;AAOZuB,yBAAuBvB,SAPX;AAQZwB,2BAAwBxB,SARZ;AASZyB,+BAA0BzB,SATd;AAUZ0B,+BAA0B1B;AAVd,GAAd;;AAcA;;;;;;AApDsB,MA0DhBL,OA1DgB;AAAA;;AAAA;AAAA;;AAAA;AAAA;;AA4FpB;;AA5FoB,sBA8FpBgC,aA9FoB,4BA8FJ;AACd,aAAO,KAAKC,QAAL,MAAmB,KAAKC,WAAL,EAA1B;AACD,KAhGmB;;AAAA,sBAkGpBC,aAlGoB,4BAkGJ;AACd,aAAO,KAAKC,GAAL,GAAW,KAAKA,GAAL,IAAYnC,EAAE,KAAKoC,MAAL,CAAYvB,QAAd,EAAwB,CAAxB,CAA9B;AACD,KApGmB;;AAAA,sBAsGpBwB,UAtGoB,yBAsGP;AACX,UAAMC,OAAOtC,EAAE,KAAKkC,aAAL,EAAF,CAAb;;AAEA;AACA,WAAKK,iBAAL,CAAuBD,KAAKE,IAAL,CAAUtB,SAASC,KAAnB,CAAvB,EAAkD,KAAKa,QAAL,EAAlD;AACA,WAAKO,iBAAL,CAAuBD,KAAKE,IAAL,CAAUtB,SAASE,OAAnB,CAAvB,EAAoD,KAAKa,WAAL,EAApD;;AAEAK,WAAKG,WAAL,CAAoB1B,UAAUC,IAA9B,SAAsCD,UAAUE,IAAhD;;AAEA,WAAKyB,aAAL;AACD,KAhHmB;;AAkHpB;;AAlHoB,sBAoHpBT,WApHoB,0BAoHN;AACZ,aAAO,KAAKU,OAAL,CAAaC,YAAb,CAA0B,cAA1B,MACD,OAAO,KAAKR,MAAL,CAAYxB,OAAnB,KAA+B,UAA/B,GACE,KAAKwB,MAAL,CAAYxB,OAAZ,CAAoBiC,IAApB,CAAyB,KAAKF,OAA9B,CADF,GAEE,KAAKP,MAAL,CAAYxB,OAHb,CAAP;AAID,KAzHmB;;AA4HpB;;AA5HoB,YA8HbkC,gBA9Ha,6BA8HIV,MA9HJ,EA8HY;AAC9B,aAAO,KAAKW,IAAL,CAAU,YAAY;AAC3B,YAAIC,OAAYhD,EAAE,IAAF,EAAQgD,IAAR,CAAa7C,QAAb,CAAhB;AACA,YAAM8C,UAAU,QAAOb,MAAP,yCAAOA,MAAP,OAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;AAEA,YAAI,CAACY,IAAD,IAAS,eAAeE,IAAf,CAAoBd,MAApB,CAAb,EAA0C;AACxC;AACD;;AAED,YAAI,CAACY,IAAL,EAAW;AACTA,iBAAO,IAAIjD,OAAJ,CAAY,IAAZ,EAAkBkD,OAAlB,CAAP;AACAjD,YAAE,IAAF,EAAQgD,IAAR,CAAa7C,QAAb,EAAuB6C,IAAvB;AACD;;AAED,YAAI,OAAOZ,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,cAAIY,KAAKZ,MAAL,MAAiBe,SAArB,EAAgC;AAC9B,kBAAM,IAAIC,KAAJ,uBAA8BhB,MAA9B,OAAN;AACD;AACDY,eAAKZ,MAAL;AACD;AACF,OAnBM,CAAP;AAoBD,KAnJmB;;AAAA;AAAA;;;AA6DpB;;AA7DoB,0BA+DC;AACnB,eAAOlC,OAAP;AACD;AAjEmB;AAAA;AAAA,0BAmEC;AACnB,eAAOK,OAAP;AACD;AArEmB;AAAA;AAAA,0BAuEF;AAChB,eAAON,IAAP;AACD;AAzEmB;AAAA;AAAA,0BA2EE;AACpB,eAAOE,QAAP;AACD;AA7EmB;AAAA;AAAA,0BA+ED;AACjB,eAAOkB,KAAP;AACD;AAjFmB;AAAA;AAAA,0BAmFG;AACrB,eAAOjB,SAAP;AACD;AArFmB;AAAA;AAAA,0BAuFK;AACvB,eAAOU,WAAP;AACD;AAzFmB;;AAAA;AAAA,IA0DAL,OA1DA;;AAuJtB;;;;;;AAMAT,IAAEM,EAAF,CAAKL,IAAL,IAAyBF,QAAQ+C,gBAAjC;AACA9C,IAAEM,EAAF,CAAKL,IAAL,EAAWoD,WAAX,GAAyBtD,OAAzB;AACAC,IAAEM,EAAF,CAAKL,IAAL,EAAWqD,UAAX,GAAyB,YAAY;AACnCtD,MAAEM,EAAF,CAAKL,IAAL,IAAaI,kBAAb;AACA,WAAON,QAAQ+C,gBAAf;AACD,GAHD;;AAKA,SAAO/C,OAAP;AAED,CAtKe,CAsKbwD,MAtKa,CAAhB", "file": "popover.js", "sourcesContent": ["import Tooltip from './tooltip'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-alpha.6): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Popover = (($) => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'popover'\n  const VERSION             = '4.0.0-alpha.6'\n  const DATA_KEY            = 'bs.popover'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n\n  const Default = $.extend({}, Tooltip.Default, {\n    placement : 'right',\n    trigger   : 'click',\n    content   : '',\n    template  : '<div class=\"popover\" role=\"tooltip\">'\n              + '<h3 class=\"popover-title\"></h3>'\n              + '<div class=\"popover-content\"></div></div>'\n  })\n\n  const DefaultType = $.extend({}, Tooltip.DefaultType, {\n    content : '(string|element|function)'\n  })\n\n  const ClassName = {\n    FADE : 'fade',\n    SHOW : 'show'\n  }\n\n  const Selector = {\n    TITLE   : '.popover-title',\n    CONTENT : '.popover-content'\n  }\n\n  const Event = {\n    HIDE       : `hide${EVENT_KEY}`,\n    HIDDEN     : `hidden${EVENT_KEY}`,\n    SHOW       : `show${EVENT_KEY}`,\n    SHOWN      : `shown${EVENT_KEY}`,\n    INSERTED   : `inserted${EVENT_KEY}`,\n    CLICK      : `click${EVENT_KEY}`,\n    FOCUSIN    : `focusin${EVENT_KEY}`,\n    FOCUSOUT   : `focusout${EVENT_KEY}`,\n    MOUSEENTER : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE : `mouseleave${EVENT_KEY}`\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Popover extends Tooltip {\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get NAME() {\n      return NAME\n    }\n\n    static get DATA_KEY() {\n      return DATA_KEY\n    }\n\n    static get Event() {\n      return Event\n    }\n\n    static get EVENT_KEY() {\n      return EVENT_KEY\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n\n    // overrides\n\n    isWithContent() {\n      return this.getTitle() || this._getContent()\n    }\n\n    getTipElement() {\n      return this.tip = this.tip || $(this.config.template)[0]\n    }\n\n    setContent() {\n      const $tip = $(this.getTipElement())\n\n      // we use append for html objects to maintain js events\n      this.setElementContent($tip.find(Selector.TITLE), this.getTitle())\n      this.setElementContent($tip.find(Selector.CONTENT), this._getContent())\n\n      $tip.removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n\n      this.cleanupTether()\n    }\n\n    // private\n\n    _getContent() {\n      return this.element.getAttribute('data-content')\n        || (typeof this.config.content === 'function' ?\n              this.config.content.call(this.element) :\n              this.config.content)\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' ? config : null\n\n        if (!data && /destroy|hide/.test(config)) {\n          return\n        }\n\n        if (!data) {\n          data = new Popover(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (data[config] === undefined) {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Popover._jQueryInterface\n  $.fn[NAME].Constructor = Popover\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Popover._jQueryInterface\n  }\n\n  return Popover\n\n})(jQuery)\n\nexport default Popover\n"]}
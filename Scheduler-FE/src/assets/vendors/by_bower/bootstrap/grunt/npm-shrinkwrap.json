{"name": "bootstrap", "version": "4.0.0-alpha.6", "dependencies": {"abbrev": {"version": "1.0.9", "from": "abbrev@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.9.tgz", "dev": true}, "accepts": {"version": "1.3.3", "from": "accepts@>=1.3.3 <1.4.0", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.3.tgz", "dev": true}, "acorn": {"version": "4.0.4", "from": "acorn@>=4.0.1 <5.0.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-4.0.4.tgz", "dev": true}, "acorn-jsx": {"version": "3.0.1", "from": "acorn-jsx@>=3.0.0 <4.0.0", "resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-3.0.1.tgz", "dev": true, "dependencies": {"acorn": {"version": "3.3.0", "from": "acorn@>=3.0.4 <4.0.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-3.3.0.tgz", "dev": true}}}, "agent-base": {"version": "2.0.1", "from": "agent-base@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-2.0.1.tgz", "dev": true, "dependencies": {"semver": {"version": "5.0.3", "from": "semver@>=5.0.1 <5.1.0", "resolved": "https://registry.npmjs.org/semver/-/semver-5.0.3.tgz", "dev": true}}}, "ajv": {"version": "4.10.3", "from": "ajv@>=4.7.0 <5.0.0", "resolved": "https://registry.npmjs.org/ajv/-/ajv-4.10.3.tgz", "dev": true}, "ajv-keywords": {"version": "1.5.0", "from": "ajv-keywords@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.5.0.tgz", "dev": true}, "align-text": {"version": "0.1.4", "from": "align-text@>=0.1.3 <0.2.0", "resolved": "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz", "dev": true}, "amdefine": {"version": "1.0.1", "from": "amdefine@>=0.0.4", "resolved": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz", "dev": true}, "ansi-escapes": {"version": "1.4.0", "from": "ansi-escapes@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-1.4.0.tgz", "dev": true}, "ansi-regex": {"version": "2.0.0", "from": "ansi-regex@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.0.0.tgz", "dev": true}, "ansi-styles": {"version": "2.2.1", "from": "ansi-styles@>=2.2.1 <3.0.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "dev": true}, "anymatch": {"version": "1.3.0", "from": "anymatch@>=1.3.0 <2.0.0", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-1.3.0.tgz", "dev": true, "optional": true}, "aproba": {"version": "1.0.4", "from": "aproba@>=1.0.3 <2.0.0", "resolved": "https://registry.npmjs.org/aproba/-/aproba-1.0.4.tgz", "dev": true}, "archiver": {"version": "1.3.0", "from": "archiver@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/archiver/-/archiver-1.3.0.tgz", "dev": true, "dependencies": {"async": {"version": "2.1.4", "from": "async@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/async/-/async-2.1.4.tgz", "dev": true}}}, "archiver-utils": {"version": "1.3.0", "from": "archiver-utils@>=1.3.0 <2.0.0", "resolved": "https://registry.npmjs.org/archiver-utils/-/archiver-utils-1.3.0.tgz", "dev": true}, "are-we-there-yet": {"version": "1.1.2", "from": "are-we-there-yet@>=1.1.2 <1.2.0", "resolved": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.2.tgz", "dev": true}, "argparse": {"version": "1.0.9", "from": "argparse@>=1.0.7 <2.0.0", "resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.9.tgz", "dev": true}, "arr-diff": {"version": "2.0.0", "from": "arr-diff@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "dev": true, "optional": true}, "arr-flatten": {"version": "1.0.1", "from": "arr-flatten@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.0.1.tgz", "dev": true, "optional": true}, "array-differ": {"version": "1.0.0", "from": "array-differ@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/array-differ/-/array-differ-1.0.0.tgz", "dev": true}, "array-find-index": {"version": "1.0.2", "from": "array-find-index@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/array-find-index/-/array-find-index-1.0.2.tgz", "dev": true}, "array-index": {"version": "1.0.0", "from": "array-index@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/array-index/-/array-index-1.0.0.tgz", "dev": true}, "array-union": {"version": "1.0.2", "from": "array-union@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/array-union/-/array-union-1.0.2.tgz", "dev": true}, "array-uniq": {"version": "1.0.3", "from": "array-uniq@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/array-uniq/-/array-uniq-1.0.3.tgz", "dev": true}, "array-unique": {"version": "0.2.1", "from": "array-unique@>=0.2.1 <0.3.0", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "dev": true, "optional": true}, "arrify": {"version": "1.0.1", "from": "arrify@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz", "dev": true}, "asn1": {"version": "0.2.3", "from": "asn1@>=0.2.3 <0.3.0", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.3.tgz", "dev": true}, "assert-plus": {"version": "0.2.0", "from": "assert-plus@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.2.0.tgz", "dev": true}, "async": {"version": "1.5.2", "from": "async@>=1.5.2 <1.6.0", "resolved": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "dev": true}, "async-each": {"version": "1.0.1", "from": "async-each@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/async-each/-/async-each-1.0.1.tgz", "dev": true, "optional": true}, "async-foreach": {"version": "0.1.3", "from": "async-foreach@>=0.1.3 <0.2.0", "resolved": "https://registry.npmjs.org/async-foreach/-/async-foreach-0.1.3.tgz", "dev": true}, "asynckit": {"version": "0.4.0", "from": "asynckit@>=0.4.0 <0.5.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "dev": true}, "autoprefixer": {"version": "6.6.1", "from": "autoprefixer@>=6.6.1 <7.0.0", "resolved": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.6.1.tgz", "dev": true}, "aws-sign2": {"version": "0.6.0", "from": "aws-sign2@>=0.6.0 <0.7.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.6.0.tgz", "dev": true}, "aws4": {"version": "1.5.0", "from": "aws4@>=1.2.1 <2.0.0", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.5.0.tgz", "dev": true}, "babel-code-frame": {"version": "6.20.0", "from": "babel-code-frame@>=6.16.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-code-frame/-/babel-code-frame-6.20.0.tgz", "dev": true}, "babel-core": {"version": "6.21.0", "from": "babel-core@>=6.0.12 <7.0.0", "resolved": "https://registry.npmjs.org/babel-core/-/babel-core-6.21.0.tgz", "dev": true}, "babel-eslint": {"version": "7.1.1", "from": "babel-eslint@>=7.1.1 <8.0.0", "resolved": "https://registry.npmjs.org/babel-eslint/-/babel-eslint-7.1.1.tgz", "dev": true}, "babel-generator": {"version": "6.21.0", "from": "babel-generator@>=6.21.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-generator/-/babel-generator-6.21.0.tgz", "dev": true, "dependencies": {"jsesc": {"version": "1.3.0", "from": "jsesc@>=1.3.0 <2.0.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-1.3.0.tgz", "dev": true}}}, "babel-helper-call-delegate": {"version": "6.18.0", "from": "babel-helper-call-delegate@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-helper-call-delegate/-/babel-helper-call-delegate-6.18.0.tgz", "dev": true}, "babel-helper-define-map": {"version": "6.18.0", "from": "babel-helper-define-map@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-helper-define-map/-/babel-helper-define-map-6.18.0.tgz", "dev": true}, "babel-helper-function-name": {"version": "6.18.0", "from": "babel-helper-function-name@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-helper-function-name/-/babel-helper-function-name-6.18.0.tgz", "dev": true}, "babel-helper-get-function-arity": {"version": "6.18.0", "from": "babel-helper-get-function-arity@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-helper-get-function-arity/-/babel-helper-get-function-arity-6.18.0.tgz", "dev": true}, "babel-helper-hoist-variables": {"version": "6.18.0", "from": "babel-helper-hoist-variables@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-helper-hoist-variables/-/babel-helper-hoist-variables-6.18.0.tgz", "dev": true}, "babel-helper-optimise-call-expression": {"version": "6.18.0", "from": "babel-helper-optimise-call-expression@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-helper-optimise-call-expression/-/babel-helper-optimise-call-expression-6.18.0.tgz", "dev": true}, "babel-helper-regex": {"version": "6.18.0", "from": "babel-helper-regex@>=6.8.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-helper-regex/-/babel-helper-regex-6.18.0.tgz", "dev": true}, "babel-helper-replace-supers": {"version": "6.18.0", "from": "babel-helper-replace-supers@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-helper-replace-supers/-/babel-helper-replace-supers-6.18.0.tgz", "dev": true}, "babel-helpers": {"version": "6.16.0", "from": "babel-helpers@>=6.16.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-helpers/-/babel-helpers-6.16.0.tgz", "dev": true}, "babel-messages": {"version": "6.8.0", "from": "babel-messages@>=6.8.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-messages/-/babel-messages-6.8.0.tgz", "dev": true}, "babel-plugin-check-es2015-constants": {"version": "6.8.0", "from": "babel-plugin-check-es2015-constants@>=6.3.13 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-check-es2015-constants/-/babel-plugin-check-es2015-constants-6.8.0.tgz", "dev": true}, "babel-plugin-transform-es2015-arrow-functions": {"version": "6.8.0", "from": "babel-plugin-transform-es2015-arrow-functions@>=6.3.13 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-arrow-functions/-/babel-plugin-transform-es2015-arrow-functions-6.8.0.tgz", "dev": true}, "babel-plugin-transform-es2015-block-scoped-functions": {"version": "6.8.0", "from": "babel-plugin-transform-es2015-block-scoped-functions@>=6.3.13 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-block-scoped-functions/-/babel-plugin-transform-es2015-block-scoped-functions-6.8.0.tgz", "dev": true}, "babel-plugin-transform-es2015-block-scoping": {"version": "6.21.0", "from": "babel-plugin-transform-es2015-block-scoping@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-block-scoping/-/babel-plugin-transform-es2015-block-scoping-6.21.0.tgz", "dev": true}, "babel-plugin-transform-es2015-classes": {"version": "6.18.0", "from": "babel-plugin-transform-es2015-classes@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-classes/-/babel-plugin-transform-es2015-classes-6.18.0.tgz", "dev": true}, "babel-plugin-transform-es2015-computed-properties": {"version": "6.8.0", "from": "babel-plugin-transform-es2015-computed-properties@>=6.3.13 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-computed-properties/-/babel-plugin-transform-es2015-computed-properties-6.8.0.tgz", "dev": true}, "babel-plugin-transform-es2015-destructuring": {"version": "6.19.0", "from": "babel-plugin-transform-es2015-destructuring@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-destructuring/-/babel-plugin-transform-es2015-destructuring-6.19.0.tgz", "dev": true}, "babel-plugin-transform-es2015-duplicate-keys": {"version": "6.8.0", "from": "babel-plugin-transform-es2015-duplicate-keys@>=6.6.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-duplicate-keys/-/babel-plugin-transform-es2015-duplicate-keys-6.8.0.tgz", "dev": true}, "babel-plugin-transform-es2015-for-of": {"version": "6.18.0", "from": "babel-plugin-transform-es2015-for-of@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-for-of/-/babel-plugin-transform-es2015-for-of-6.18.0.tgz", "dev": true}, "babel-plugin-transform-es2015-function-name": {"version": "6.9.0", "from": "babel-plugin-transform-es2015-function-name@>=6.9.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-function-name/-/babel-plugin-transform-es2015-function-name-6.9.0.tgz", "dev": true}, "babel-plugin-transform-es2015-literals": {"version": "6.8.0", "from": "babel-plugin-transform-es2015-literals@>=6.3.13 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-literals/-/babel-plugin-transform-es2015-literals-6.8.0.tgz", "dev": true}, "babel-plugin-transform-es2015-modules-amd": {"version": "6.18.0", "from": "babel-plugin-transform-es2015-modules-amd@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-amd/-/babel-plugin-transform-es2015-modules-amd-6.18.0.tgz", "dev": true}, "babel-plugin-transform-es2015-modules-commonjs": {"version": "6.18.0", "from": "babel-plugin-transform-es2015-modules-commonjs@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-commonjs/-/babel-plugin-transform-es2015-modules-commonjs-6.18.0.tgz", "dev": true}, "babel-plugin-transform-es2015-modules-strip": {"version": "0.1.0", "from": "babel-plugin-transform-es2015-modules-strip@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-strip/-/babel-plugin-transform-es2015-modules-strip-0.1.0.tgz", "dev": true}, "babel-plugin-transform-es2015-modules-systemjs": {"version": "6.19.0", "from": "babel-plugin-transform-es2015-modules-systemjs@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-systemjs/-/babel-plugin-transform-es2015-modules-systemjs-6.19.0.tgz", "dev": true}, "babel-plugin-transform-es2015-modules-umd": {"version": "6.18.0", "from": "babel-plugin-transform-es2015-modules-umd@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-modules-umd/-/babel-plugin-transform-es2015-modules-umd-6.18.0.tgz", "dev": true}, "babel-plugin-transform-es2015-object-super": {"version": "6.8.0", "from": "babel-plugin-transform-es2015-object-super@>=6.3.13 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-object-super/-/babel-plugin-transform-es2015-object-super-6.8.0.tgz", "dev": true}, "babel-plugin-transform-es2015-parameters": {"version": "6.21.0", "from": "babel-plugin-transform-es2015-parameters@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-parameters/-/babel-plugin-transform-es2015-parameters-6.21.0.tgz", "dev": true}, "babel-plugin-transform-es2015-shorthand-properties": {"version": "6.18.0", "from": "babel-plugin-transform-es2015-shorthand-properties@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-shorthand-properties/-/babel-plugin-transform-es2015-shorthand-properties-6.18.0.tgz", "dev": true}, "babel-plugin-transform-es2015-spread": {"version": "6.8.0", "from": "babel-plugin-transform-es2015-spread@>=6.3.13 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-spread/-/babel-plugin-transform-es2015-spread-6.8.0.tgz", "dev": true}, "babel-plugin-transform-es2015-sticky-regex": {"version": "6.8.0", "from": "babel-plugin-transform-es2015-sticky-regex@>=6.3.13 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-sticky-regex/-/babel-plugin-transform-es2015-sticky-regex-6.8.0.tgz", "dev": true}, "babel-plugin-transform-es2015-template-literals": {"version": "6.8.0", "from": "babel-plugin-transform-es2015-template-literals@>=6.6.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-template-literals/-/babel-plugin-transform-es2015-template-literals-6.8.0.tgz", "dev": true}, "babel-plugin-transform-es2015-typeof-symbol": {"version": "6.18.0", "from": "babel-plugin-transform-es2015-typeof-symbol@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-typeof-symbol/-/babel-plugin-transform-es2015-typeof-symbol-6.18.0.tgz", "dev": true}, "babel-plugin-transform-es2015-unicode-regex": {"version": "6.11.0", "from": "babel-plugin-transform-es2015-unicode-regex@>=6.3.13 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-es2015-unicode-regex/-/babel-plugin-transform-es2015-unicode-regex-6.11.0.tgz", "dev": true}, "babel-plugin-transform-regenerator": {"version": "6.21.0", "from": "babel-plugin-transform-regenerator@>=6.16.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-regenerator/-/babel-plugin-transform-regenerator-6.21.0.tgz", "dev": true}, "babel-plugin-transform-strict-mode": {"version": "6.18.0", "from": "babel-plugin-transform-strict-mode@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-plugin-transform-strict-mode/-/babel-plugin-transform-strict-mode-6.18.0.tgz", "dev": true}, "babel-preset-es2015": {"version": "6.18.0", "from": "babel-preset-es2015@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-preset-es2015/-/babel-preset-es2015-6.18.0.tgz", "dev": true}, "babel-register": {"version": "6.18.0", "from": "babel-register@>=6.18.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-register/-/babel-register-6.18.0.tgz", "dev": true}, "babel-runtime": {"version": "6.20.0", "from": "babel-runtime@>=6.20.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.20.0.tgz", "dev": true}, "babel-template": {"version": "6.16.0", "from": "babel-template@>=6.15.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-template/-/babel-template-6.16.0.tgz", "dev": true}, "babel-traverse": {"version": "6.21.0", "from": "babel-traverse@>=6.15.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-traverse/-/babel-traverse-6.21.0.tgz", "dev": true}, "babel-types": {"version": "6.21.0", "from": "babel-types@>=6.15.0 <7.0.0", "resolved": "https://registry.npmjs.org/babel-types/-/babel-types-6.21.0.tgz", "dev": true}, "babylon": {"version": "6.14.1", "from": "babylon@>=6.13.0 <7.0.0", "resolved": "https://registry.npmjs.org/babylon/-/babylon-6.14.1.tgz", "dev": true}, "balanced-match": {"version": "0.4.2", "from": "balanced-match@>=0.4.1 <0.5.0", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.4.2.tgz", "dev": true}, "basic-auth": {"version": "1.0.4", "from": "basic-auth@>=1.0.3 <1.1.0", "resolved": "https://registry.npmjs.org/basic-auth/-/basic-auth-1.0.4.tgz", "dev": true}, "batch": {"version": "0.5.3", "from": "batch@0.5.3", "resolved": "https://registry.npmjs.org/batch/-/batch-0.5.3.tgz", "dev": true}, "bcrypt-pbkdf": {"version": "1.0.0", "from": "bcrypt-pbkdf@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.0.tgz", "dev": true, "optional": true}, "binary-extensions": {"version": "1.8.0", "from": "binary-extensions@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.8.0.tgz", "dev": true, "optional": true}, "bl": {"version": "1.2.0", "from": "bl@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/bl/-/bl-1.2.0.tgz", "dev": true}, "block-stream": {"version": "0.0.9", "from": "block-stream@*", "resolved": "https://registry.npmjs.org/block-stream/-/block-stream-0.0.9.tgz", "dev": true}, "bluebird": {"version": "3.4.7", "from": "bluebird@>=3.0.6 <4.0.0", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-3.4.7.tgz", "dev": true}, "body-parser": {"version": "1.14.2", "from": "body-parser@>=1.14.0 <1.15.0", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.14.2.tgz", "dev": true, "dependencies": {"debug": {"version": "2.2.0", "from": "debug@>=2.2.0 <2.3.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "dev": true}, "http-errors": {"version": "1.3.1", "from": "http-errors@>=1.3.1 <1.4.0", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-1.3.1.tgz", "dev": true}, "iconv-lite": {"version": "0.4.13", "from": "iconv-lite@0.4.13", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.13.tgz", "dev": true}, "ms": {"version": "0.7.1", "from": "ms@0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "dev": true}, "qs": {"version": "5.2.0", "from": "qs@5.2.0", "resolved": "https://registry.npmjs.org/qs/-/qs-5.2.0.tgz", "dev": true}}}, "boom": {"version": "2.10.1", "from": "boom@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/boom/-/boom-2.10.1.tgz", "dev": true}, "brace-expansion": {"version": "1.1.6", "from": "brace-expansion@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.6.tgz", "dev": true}, "braces": {"version": "1.8.5", "from": "braces@>=1.8.2 <2.0.0", "resolved": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "dev": true, "optional": true}, "browserslist": {"version": "1.5.1", "from": "browserslist@>=1.5.1 <1.6.0", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-1.5.1.tgz", "dev": true}, "buffer-crc32": {"version": "0.2.13", "from": "buffer-crc32@>=0.2.1 <0.3.0", "resolved": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "dev": true}, "buffer-shims": {"version": "1.0.0", "from": "buffer-shims@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/buffer-shims/-/buffer-shims-1.0.0.tgz", "dev": true}, "builtin-modules": {"version": "1.1.1", "from": "builtin-modules@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/builtin-modules/-/builtin-modules-1.1.1.tgz", "dev": true}, "bytes": {"version": "2.2.0", "from": "bytes@2.2.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-2.2.0.tgz", "dev": true}, "caller-path": {"version": "0.1.0", "from": "caller-path@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/caller-path/-/caller-path-0.1.0.tgz", "dev": true}, "callsites": {"version": "0.2.0", "from": "callsites@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/callsites/-/callsites-0.2.0.tgz", "dev": true}, "camelcase": {"version": "2.1.1", "from": "camelcase@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-2.1.1.tgz", "dev": true}, "camelcase-keys": {"version": "2.1.0", "from": "camelcase-keys@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/camelcase-keys/-/camelcase-keys-2.1.0.tgz", "dev": true}, "caniuse-db": {"version": "1.0.30000604", "from": "caniuse-db@>=1.0.30000604 <2.0.0", "resolved": "https://registry.npmjs.org/caniuse-db/-/caniuse-db-1.0.30000604.tgz", "dev": true}, "caseless": {"version": "0.11.0", "from": "caseless@>=0.11.0 <0.12.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.11.0.tgz", "dev": true}, "center-align": {"version": "0.1.3", "from": "center-align@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz", "dev": true}, "chalk": {"version": "1.1.3", "from": "chalk@>=1.1.3 <2.0.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "dev": true, "dependencies": {"supports-color": {"version": "2.0.0", "from": "supports-color@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "dev": true}}}, "chokidar": {"version": "1.6.1", "from": "chokidar@>=1.5.1 <2.0.0", "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-1.6.1.tgz", "dev": true, "optional": true}, "circular-json": {"version": "0.3.1", "from": "circular-json@>=0.3.1 <0.4.0", "resolved": "https://registry.npmjs.org/circular-json/-/circular-json-0.3.1.tgz", "dev": true}, "clean-css": {"version": "3.4.23", "from": "clean-css@>=3.4.23 <4.0.0", "resolved": "https://registry.npmjs.org/clean-css/-/clean-css-3.4.23.tgz", "dev": true, "dependencies": {"source-map": {"version": "0.4.4", "from": "source-map@>=0.4.0 <0.5.0", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.4.4.tgz", "dev": true}}}, "cli": {"version": "0.6.6", "from": "cli@>=0.6.0 <0.7.0", "resolved": "https://registry.npmjs.org/cli/-/cli-0.6.6.tgz", "dev": true, "dependencies": {"glob": {"version": "3.2.11", "from": "glob@>=3.2.1 <3.3.0", "resolved": "https://registry.npmjs.org/glob/-/glob-3.2.11.tgz", "dev": true}, "minimatch": {"version": "0.3.0", "from": "minimatch@>=0.3.0 <0.4.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz", "dev": true}}}, "cli-cursor": {"version": "1.0.2", "from": "cli-cursor@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-1.0.2.tgz", "dev": true}, "cli-width": {"version": "2.1.0", "from": "cli-width@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/cli-width/-/cli-width-2.1.0.tgz", "dev": true}, "cliui": {"version": "3.2.0", "from": "cliui@>=3.2.0 <4.0.0", "resolved": "https://registry.npmjs.org/cliui/-/cliui-3.2.0.tgz", "dev": true}, "co": {"version": "4.6.0", "from": "co@>=4.6.0 <5.0.0", "resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "dev": true}, "code-point-at": {"version": "1.1.0", "from": "code-point-at@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/code-point-at/-/code-point-at-1.1.0.tgz", "dev": true}, "coffee-script": {"version": "1.10.0", "from": "coffee-script@>=1.10.0 <1.11.0", "resolved": "https://registry.npmjs.org/coffee-script/-/coffee-script-1.10.0.tgz", "dev": true}, "colors": {"version": "1.1.2", "from": "colors@>=1.1.2 <1.2.0", "resolved": "https://registry.npmjs.org/colors/-/colors-1.1.2.tgz", "dev": true}, "combined-stream": {"version": "1.0.5", "from": "combined-stream@>=1.0.5 <1.1.0", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.5.tgz", "dev": true}, "commander": {"version": "2.8.1", "from": "commander@>=2.8.0 <2.9.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.8.1.tgz", "dev": true}, "compress-commons": {"version": "1.1.0", "from": "compress-commons@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/compress-commons/-/compress-commons-1.1.0.tgz", "dev": true}, "concat-map": {"version": "0.0.1", "from": "concat-map@0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "dev": true}, "concat-stream": {"version": "1.6.0", "from": "concat-stream@>=1.4.6 <2.0.0", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.0.tgz", "dev": true}, "connect": {"version": "3.5.0", "from": "connect@>=3.4.0 <4.0.0", "resolved": "https://registry.npmjs.org/connect/-/connect-3.5.0.tgz", "dev": true, "dependencies": {"debug": {"version": "2.2.0", "from": "debug@>=2.2.0 <2.3.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "dev": true}, "ms": {"version": "0.7.1", "from": "ms@0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "dev": true}}}, "connect-livereload": {"version": "0.5.4", "from": "connect-livereload@>=0.5.0 <0.6.0", "resolved": "https://registry.npmjs.org/connect-livereload/-/connect-livereload-0.5.4.tgz", "dev": true}, "console-browserify": {"version": "1.1.0", "from": "console-browserify@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/console-browserify/-/console-browserify-1.1.0.tgz", "dev": true}, "console-control-strings": {"version": "1.1.0", "from": "console-control-strings@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz", "dev": true}, "content-type": {"version": "1.0.2", "from": "content-type@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.2.tgz", "dev": true}, "convert-source-map": {"version": "1.3.0", "from": "convert-source-map@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.3.0.tgz", "dev": true}, "core-js": {"version": "2.4.1", "from": "core-js@>=2.4.0 <3.0.0", "resolved": "https://registry.npmjs.org/core-js/-/core-js-2.4.1.tgz", "dev": true}, "core-util-is": {"version": "1.0.2", "from": "core-util-is@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "dev": true}, "crc32-stream": {"version": "1.0.0", "from": "crc32-stream@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/crc32-stream/-/crc32-stream-1.0.0.tgz", "dev": true}, "cross-spawn": {"version": "3.0.1", "from": "cross-spawn@>=3.0.0 <4.0.0", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-3.0.1.tgz", "dev": true, "dependencies": {"lru-cache": {"version": "4.0.2", "from": "lru-cache@>=4.0.1 <5.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.0.2.tgz", "dev": true}}}, "cryptiles": {"version": "2.0.5", "from": "cryptiles@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/cryptiles/-/cryptiles-2.0.5.tgz", "dev": true}, "csslint": {"version": "0.10.0", "from": "csslint@0.10.0", "resolved": "https://registry.npmjs.org/csslint/-/csslint-0.10.0.tgz", "dev": true}, "currently-unhandled": {"version": "0.4.1", "from": "currently-unhandled@>=0.4.1 <0.5.0", "resolved": "https://registry.npmjs.org/currently-unhandled/-/currently-unhandled-0.4.1.tgz", "dev": true}, "d": {"version": "0.1.1", "from": "d@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/d/-/d-0.1.1.tgz", "dev": true}, "dashdash": {"version": "1.14.1", "from": "dashdash@>=1.12.0 <2.0.0", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "dev": true, "dependencies": {"assert-plus": {"version": "1.0.0", "from": "assert-plus@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "dev": true}}}, "date-now": {"version": "0.1.4", "from": "date-now@>=0.1.4 <0.2.0", "resolved": "https://registry.npmjs.org/date-now/-/date-now-0.1.4.tgz", "dev": true}, "date-time": {"version": "1.1.0", "from": "date-time@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/date-time/-/date-time-1.1.0.tgz", "dev": true}, "dateformat": {"version": "1.0.12", "from": "dateformat@>=1.0.12 <1.1.0", "resolved": "https://registry.npmjs.org/dateformat/-/dateformat-1.0.12.tgz", "dev": true}, "debug": {"version": "2.6.0", "from": "debug@>=2.2.0 <3.0.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.0.tgz", "dev": true}, "decamelize": {"version": "1.2.0", "from": "decamelize@>=1.1.2 <2.0.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "dev": true}, "deep-is": {"version": "0.1.3", "from": "deep-is@>=0.1.3 <0.2.0", "resolved": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz", "dev": true}, "del": {"version": "2.2.2", "from": "del@>=2.0.2 <3.0.0", "resolved": "https://registry.npmjs.org/del/-/del-2.2.2.tgz", "dev": true}, "delayed-stream": {"version": "1.0.0", "from": "delayed-stream@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "dev": true}, "delegates": {"version": "1.0.0", "from": "delegates@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz", "dev": true}, "depd": {"version": "1.1.0", "from": "depd@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/depd/-/depd-1.1.0.tgz", "dev": true}, "destroy": {"version": "1.0.4", "from": "destroy@>=1.0.4 <1.1.0", "resolved": "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz", "dev": true}, "detect-indent": {"version": "4.0.0", "from": "detect-indent@>=4.0.0 <5.0.0", "resolved": "https://registry.npmjs.org/detect-indent/-/detect-indent-4.0.0.tgz", "dev": true}, "doctrine": {"version": "1.5.0", "from": "doctrine@>=1.2.2 <2.0.0", "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-1.5.0.tgz", "dev": true}, "dom-serializer": {"version": "0.1.0", "from": "dom-serializer@>=0.0.0 <1.0.0", "resolved": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.1.0.tgz", "dev": true, "dependencies": {"domelementtype": {"version": "1.1.3", "from": "domelementtype@>=1.1.1 <1.2.0", "resolved": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.1.3.tgz", "dev": true}, "entities": {"version": "1.1.1", "from": "entities@>=1.1.1 <1.2.0", "resolved": "https://registry.npmjs.org/entities/-/entities-1.1.1.tgz", "dev": true}}}, "domelementtype": {"version": "1.3.0", "from": "domelementtype@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.0.tgz", "dev": true}, "domhandler": {"version": "2.3.0", "from": "domhandler@>=2.3.0 <2.4.0", "resolved": "https://registry.npmjs.org/domhandler/-/domhandler-2.3.0.tgz", "dev": true}, "domutils": {"version": "1.5.1", "from": "domutils@>=1.5.0 <1.6.0", "resolved": "https://registry.npmjs.org/domutils/-/domutils-1.5.1.tgz", "dev": true}, "ecc-jsbn": {"version": "0.1.1", "from": "ecc-jsbn@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.1.tgz", "dev": true, "optional": true}, "ee-first": {"version": "1.1.1", "from": "ee-first@1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "dev": true}, "encodeurl": {"version": "1.0.1", "from": "encodeurl@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.1.tgz", "dev": true}, "end-of-stream": {"version": "1.1.0", "from": "end-of-stream@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.1.0.tgz", "dev": true, "dependencies": {"once": {"version": "1.3.3", "from": "once@>=1.3.0 <1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.3.3.tgz", "dev": true}}}, "entities": {"version": "1.0.0", "from": "entities@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/entities/-/entities-1.0.0.tgz", "dev": true}, "error-ex": {"version": "1.3.0", "from": "error-ex@>=1.2.0 <2.0.0", "resolved": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.0.tgz", "dev": true}, "es5-ext": {"version": "0.10.12", "from": "es5-ext@>=0.10.11 <0.11.0", "resolved": "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.12.tgz", "dev": true}, "es6-iterator": {"version": "2.0.0", "from": "es6-iterator@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.0.tgz", "dev": true}, "es6-map": {"version": "0.1.4", "from": "es6-map@>=0.1.3 <0.2.0", "resolved": "https://registry.npmjs.org/es6-map/-/es6-map-0.1.4.tgz", "dev": true}, "es6-object-assign": {"version": "1.0.3", "from": "es6-object-assign@>=1.0.3 <2.0.0", "resolved": "https://registry.npmjs.org/es6-object-assign/-/es6-object-assign-1.0.3.tgz", "dev": true}, "es6-promise": {"version": "4.0.5", "from": "es6-promise@>=4.0.3 <4.1.0", "resolved": "https://registry.npmjs.org/es6-promise/-/es6-promise-4.0.5.tgz", "dev": true}, "es6-set": {"version": "0.1.4", "from": "es6-set@>=0.1.3 <0.2.0", "resolved": "https://registry.npmjs.org/es6-set/-/es6-set-0.1.4.tgz", "dev": true}, "es6-symbol": {"version": "3.1.0", "from": "es6-symbol@>=3.1.0 <3.2.0", "resolved": "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.0.tgz", "dev": true}, "es6-weak-map": {"version": "2.0.1", "from": "es6-weak-map@>=2.0.1 <3.0.0", "resolved": "https://registry.npmjs.org/es6-weak-map/-/es6-weak-map-2.0.1.tgz", "dev": true}, "escape-html": {"version": "1.0.3", "from": "escape-html@>=1.0.3 <1.1.0", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "from": "escape-string-regexp@>=1.0.2 <2.0.0", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "dev": true}, "escope": {"version": "3.6.0", "from": "escope@>=3.6.0 <4.0.0", "resolved": "https://registry.npmjs.org/escope/-/escope-3.6.0.tgz", "dev": true}, "eslint": {"version": "3.12.2", "from": "eslint@>=3.12.2 <4.0.0", "resolved": "https://registry.npmjs.org/eslint/-/eslint-3.12.2.tgz", "dev": true}, "espree": {"version": "3.3.2", "from": "espree@>=3.3.1 <4.0.0", "resolved": "https://registry.npmjs.org/espree/-/espree-3.3.2.tgz", "dev": true}, "esprima": {"version": "2.7.3", "from": "esprima@>=2.6.0 <3.0.0", "resolved": "https://registry.npmjs.org/esprima/-/esprima-2.7.3.tgz", "dev": true}, "esrecurse": {"version": "4.1.0", "from": "esrecurse@>=4.1.0 <5.0.0", "resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.1.0.tgz", "dev": true, "dependencies": {"estraverse": {"version": "4.1.1", "from": "estraverse@>=4.1.0 <4.2.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-4.1.1.tgz", "dev": true}}}, "estraverse": {"version": "4.2.0", "from": "estraverse@>=4.2.0 <5.0.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-4.2.0.tgz", "dev": true}, "esutils": {"version": "2.0.2", "from": "esutils@>=2.0.2 <3.0.0", "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "dev": true}, "etag": {"version": "1.7.0", "from": "etag@>=1.7.0 <1.8.0", "resolved": "https://registry.npmjs.org/etag/-/etag-1.7.0.tgz", "dev": true}, "event-emitter": {"version": "0.3.4", "from": "event-emitter@>=0.3.4 <0.4.0", "resolved": "https://registry.npmjs.org/event-emitter/-/event-emitter-0.3.4.tgz", "dev": true}, "eventemitter2": {"version": "0.4.14", "from": "eventemitter2@>=0.4.13 <0.5.0", "resolved": "https://registry.npmjs.org/eventemitter2/-/eventemitter2-0.4.14.tgz", "dev": true}, "exit": {"version": "0.1.2", "from": "exit@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz", "dev": true}, "exit-hook": {"version": "1.1.1", "from": "exit-hook@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/exit-hook/-/exit-hook-1.1.1.tgz", "dev": true}, "expand-brackets": {"version": "0.1.5", "from": "expand-brackets@>=0.1.4 <0.2.0", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz", "dev": true, "optional": true}, "expand-range": {"version": "1.8.2", "from": "expand-range@>=1.8.1 <2.0.0", "resolved": "https://registry.npmjs.org/expand-range/-/expand-range-1.8.2.tgz", "dev": true, "optional": true}, "extend": {"version": "3.0.0", "from": "extend@>=3.0.0 <3.1.0", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.0.tgz", "dev": true}, "extglob": {"version": "0.3.2", "from": "extglob@>=0.3.1 <0.4.0", "resolved": "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz", "dev": true, "optional": true}, "extract-zip": {"version": "1.5.0", "from": "extract-zip@>=1.5.0 <1.6.0", "resolved": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.5.0.tgz", "dev": true, "dependencies": {"concat-stream": {"version": "1.5.0", "from": "concat-stream@1.5.0", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.5.0.tgz", "dev": true}, "debug": {"version": "0.7.4", "from": "debug@0.7.4", "resolved": "https://registry.npmjs.org/debug/-/debug-0.7.4.tgz", "dev": true}, "mkdirp": {"version": "0.5.0", "from": "mkdirp@0.5.0", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.0.tgz", "dev": true}, "readable-stream": {"version": "2.0.6", "from": "readable-stream@>=2.0.0 <2.1.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz", "dev": true}}}, "extsprintf": {"version": "1.0.2", "from": "extsprintf@1.0.2", "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.0.2.tgz", "dev": true}, "fast-levenshtein": {"version": "2.0.6", "from": "fast-leven<PERSON>tein@>=2.0.4 <2.1.0", "resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "dev": true}, "faye-websocket": {"version": "0.10.0", "from": "faye-websocket@>=0.10.0 <0.11.0", "resolved": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.10.0.tgz", "dev": true}, "fd-slicer": {"version": "1.0.1", "from": "fd-slicer@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.0.1.tgz", "dev": true}, "fg-lodash": {"version": "0.0.2", "from": "fg-lodash@0.0.2", "resolved": "https://registry.npmjs.org/fg-lodash/-/fg-lodash-0.0.2.tgz", "dev": true, "dependencies": {"lodash": {"version": "2.4.2", "from": "lodash@>=2.4.1 <3.0.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "dev": true}, "underscore.string": {"version": "2.3.3", "from": "underscore.string@>=2.3.3 <2.4.0", "resolved": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.3.3.tgz", "dev": true}}}, "figures": {"version": "1.7.0", "from": "figures@>=1.3.5 <2.0.0", "resolved": "https://registry.npmjs.org/figures/-/figures-1.7.0.tgz", "dev": true}, "file-entry-cache": {"version": "2.0.0", "from": "file-entry-cache@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-2.0.0.tgz", "dev": true}, "file-sync-cmp": {"version": "0.1.1", "from": "file-sync-cmp@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/file-sync-cmp/-/file-sync-cmp-0.1.1.tgz", "dev": true}, "filename-regex": {"version": "2.0.0", "from": "filename-regex@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/filename-regex/-/filename-regex-2.0.0.tgz", "dev": true, "optional": true}, "fill-range": {"version": "2.2.3", "from": "fill-range@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-2.2.3.tgz", "dev": true, "optional": true}, "finalhandler": {"version": "0.5.0", "from": "finalhandler@0.5.0", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.5.0.tgz", "dev": true, "dependencies": {"debug": {"version": "2.2.0", "from": "debug@>=2.2.0 <2.3.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "dev": true}, "ms": {"version": "0.7.1", "from": "ms@0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "dev": true}}}, "find-up": {"version": "1.1.2", "from": "find-up@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-1.1.2.tgz", "dev": true}, "findup-sync": {"version": "0.3.0", "from": "findup-sync@>=0.3.0 <0.4.0", "resolved": "https://registry.npmjs.org/findup-sync/-/findup-sync-0.3.0.tgz", "dev": true, "dependencies": {"glob": {"version": "5.0.15", "from": "glob@>=5.0.0 <5.1.0", "resolved": "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz", "dev": true}}}, "flat-cache": {"version": "1.2.2", "from": "flat-cache@>=1.2.1 <2.0.0", "resolved": "https://registry.npmjs.org/flat-cache/-/flat-cache-1.2.2.tgz", "dev": true}, "for-in": {"version": "0.1.6", "from": "for-in@>=0.1.5 <0.2.0", "resolved": "https://registry.npmjs.org/for-in/-/for-in-0.1.6.tgz", "dev": true, "optional": true}, "for-own": {"version": "0.1.4", "from": "for-own@>=0.1.4 <0.2.0", "resolved": "https://registry.npmjs.org/for-own/-/for-own-0.1.4.tgz", "dev": true, "optional": true}, "forever-agent": {"version": "0.6.1", "from": "forever-agent@>=0.6.1 <0.7.0", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz", "dev": true}, "form-data": {"version": "2.1.2", "from": "form-data@>=2.1.1 <2.2.0", "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.1.2.tgz", "dev": true}, "fresh": {"version": "0.3.0", "from": "fresh@0.3.0", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.3.0.tgz", "dev": true}, "fs-extra": {"version": "1.0.0", "from": "fs-extra@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-1.0.0.tgz", "dev": true}, "fs.realpath": {"version": "1.0.0", "from": "fs.realpath@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "dev": true}, "fstream": {"version": "1.0.10", "from": "fstream@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/fstream/-/fstream-1.0.10.tgz", "dev": true}, "gather-stream": {"version": "1.0.0", "from": "gather-stream@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/gather-stream/-/gather-stream-1.0.0.tgz", "dev": true}, "gauge": {"version": "2.6.0", "from": "gauge@>=2.6.0 <2.7.0", "resolved": "https://registry.npmjs.org/gauge/-/gauge-2.6.0.tgz", "dev": true}, "gaze": {"version": "1.1.2", "from": "gaze@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/gaze/-/gaze-1.1.2.tgz", "dev": true}, "generate-function": {"version": "2.0.0", "from": "generate-function@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/generate-function/-/generate-function-2.0.0.tgz", "dev": true}, "generate-object-property": {"version": "1.2.0", "from": "generate-object-property@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/generate-object-property/-/generate-object-property-1.2.0.tgz", "dev": true}, "get-caller-file": {"version": "1.0.2", "from": "get-caller-file@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-1.0.2.tgz", "dev": true}, "get-stdin": {"version": "4.0.1", "from": "get-stdin@>=4.0.1 <5.0.0", "resolved": "https://registry.npmjs.org/get-stdin/-/get-stdin-4.0.1.tgz", "dev": true}, "getobject": {"version": "0.1.0", "from": "getobject@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/getobject/-/getobject-0.1.0.tgz", "dev": true}, "getpass": {"version": "0.1.6", "from": "getpass@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.6.tgz", "dev": true, "dependencies": {"assert-plus": {"version": "1.0.0", "from": "assert-plus@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "dev": true}}}, "glob": {"version": "7.1.1", "from": "glob@>=7.0.3 <8.0.0", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.1.tgz", "dev": true}, "glob-base": {"version": "0.3.0", "from": "glob-base@>=0.3.0 <0.4.0", "resolved": "https://registry.npmjs.org/glob-base/-/glob-base-0.3.0.tgz", "dev": true}, "glob-parent": {"version": "2.0.0", "from": "glob-parent@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-2.0.0.tgz", "dev": true}, "globals": {"version": "9.14.0", "from": "globals@>=9.0.0 <10.0.0", "resolved": "https://registry.npmjs.org/globals/-/globals-9.14.0.tgz", "dev": true}, "globby": {"version": "5.0.0", "from": "globby@>=5.0.0 <6.0.0", "resolved": "https://registry.npmjs.org/globby/-/globby-5.0.0.tgz", "dev": true}, "globule": {"version": "1.1.0", "from": "globule@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/globule/-/globule-1.1.0.tgz", "dev": true, "dependencies": {"lodash": {"version": "4.16.6", "from": "lodash@>=4.16.4 <4.17.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.16.6.tgz", "dev": true}}}, "graceful-fs": {"version": "4.1.11", "from": "graceful-fs@>=4.1.2 <5.0.0", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "dev": true}, "graceful-readlink": {"version": "1.0.1", "from": "graceful-readlink@>=1.0.0", "resolved": "https://registry.npmjs.org/graceful-readlink/-/graceful-readlink-1.0.1.tgz", "dev": true}, "grunt": {"version": "1.0.1", "from": "grunt@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/grunt/-/grunt-1.0.1.tgz", "dev": true, "dependencies": {"glob": {"version": "7.0.6", "from": "glob@>=7.0.0 <7.1.0", "resolved": "https://registry.npmjs.org/glob/-/glob-7.0.6.tgz", "dev": true}, "grunt-cli": {"version": "1.2.0", "from": "grunt-cli@>=1.2.0 <1.3.0", "resolved": "https://registry.npmjs.org/grunt-cli/-/grunt-cli-1.2.0.tgz", "dev": true}, "js-yaml": {"version": "3.5.5", "from": "js-yaml@>=3.5.2 <3.6.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.5.5.tgz", "dev": true}, "resolve": {"version": "1.1.7", "from": "resolve@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.1.7.tgz", "dev": true}, "rimraf": {"version": "2.2.8", "from": "rimraf@>=2.2.8 <2.3.0", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.8.tgz", "dev": true}}}, "grunt-babel": {"version": "6.0.0", "from": "grunt-babel@>=6.0.0 <7.0.0", "resolved": "https://registry.npmjs.org/grunt-babel/-/grunt-babel-6.0.0.tgz", "dev": true}, "grunt-build-control": {"version": "0.7.1", "from": "grunt-build-control@>=0.7.1 <0.8.0", "resolved": "https://registry.npmjs.org/grunt-build-control/-/grunt-build-control-0.7.1.tgz", "dev": true, "dependencies": {"semver": {"version": "4.3.6", "from": "semver@>=4.3.3 <4.4.0", "resolved": "https://registry.npmjs.org/semver/-/semver-4.3.6.tgz", "dev": true}, "shelljs": {"version": "0.2.6", "from": "shelljs@>=0.2.6 <0.3.0", "resolved": "https://registry.npmjs.org/shelljs/-/shelljs-0.2.6.tgz", "dev": true}}}, "grunt-contrib-clean": {"version": "1.0.0", "from": "grunt-contrib-clean@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/grunt-contrib-clean/-/grunt-contrib-clean-1.0.0.tgz", "dev": true}, "grunt-contrib-compress": {"version": "1.3.0", "from": "grunt-contrib-compress@>=1.3.0 <2.0.0", "resolved": "https://registry.npmjs.org/grunt-contrib-compress/-/grunt-contrib-compress-1.3.0.tgz", "dev": true}, "grunt-contrib-concat": {"version": "1.0.1", "from": "grunt-contrib-concat@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/grunt-contrib-concat/-/grunt-contrib-concat-1.0.1.tgz", "dev": true}, "grunt-contrib-connect": {"version": "1.0.2", "from": "grunt-contrib-connect@>=1.0.2 <2.0.0", "resolved": "https://registry.npmjs.org/grunt-contrib-connect/-/grunt-contrib-connect-1.0.2.tgz", "dev": true}, "grunt-contrib-copy": {"version": "1.0.0", "from": "grunt-contrib-copy@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/grunt-contrib-copy/-/grunt-contrib-copy-1.0.0.tgz", "dev": true}, "grunt-contrib-qunit": {"version": "1.2.0", "from": "grunt-contrib-qunit@>=1.2.0 <2.0.0", "resolved": "https://registry.npmjs.org/grunt-contrib-qunit/-/grunt-contrib-qunit-1.2.0.tgz", "dev": true}, "grunt-contrib-watch": {"version": "1.0.0", "from": "grunt-contrib-watch@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/grunt-contrib-watch/-/grunt-contrib-watch-1.0.0.tgz", "dev": true, "dependencies": {"lodash": {"version": "3.10.1", "from": "lodash@>=3.10.1 <4.0.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "dev": true}}}, "grunt-exec": {"version": "1.0.1", "from": "grunt-exec@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/grunt-exec/-/grunt-exec-1.0.1.tgz", "dev": true}, "grunt-html": {"version": "8.1.0", "from": "grunt-html@>=8.1.0 <9.0.0", "resolved": "https://registry.npmjs.org/grunt-html/-/grunt-html-8.1.0.tgz", "dev": true, "dependencies": {"async": {"version": "2.1.2", "from": "async@2.1.2", "resolved": "https://registry.npmjs.org/async/-/async-2.1.2.tgz", "dev": true}}}, "grunt-jekyll": {"version": "0.4.4", "from": "grunt-jekyll@>=0.4.4 <0.5.0", "resolved": "https://registry.npmjs.org/grunt-jekyll/-/grunt-jekyll-0.4.4.tgz", "dev": true}, "grunt-known-options": {"version": "1.1.0", "from": "grunt-known-options@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/grunt-known-options/-/grunt-known-options-1.1.0.tgz", "dev": true}, "grunt-legacy-log": {"version": "1.0.0", "from": "grunt-legacy-log@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-legacy-log/-/grunt-legacy-log-1.0.0.tgz", "dev": true, "dependencies": {"lodash": {"version": "3.10.1", "from": "lodash@>=3.10.1 <3.11.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "dev": true}}}, "grunt-legacy-log-utils": {"version": "1.0.0", "from": "grunt-legacy-log-utils@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-legacy-log-utils/-/grunt-legacy-log-utils-1.0.0.tgz", "dev": true, "dependencies": {"lodash": {"version": "4.3.0", "from": "lodash@>=4.3.0 <4.4.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.3.0.tgz", "dev": true}}}, "grunt-legacy-util": {"version": "1.0.0", "from": "grunt-legacy-util@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-legacy-util/-/grunt-legacy-util-1.0.0.tgz", "dev": true, "dependencies": {"lodash": {"version": "4.3.0", "from": "lodash@>=4.3.0 <4.4.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.3.0.tgz", "dev": true}}}, "grunt-lib-phantomjs": {"version": "1.1.0", "from": "grunt-lib-phantomjs@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/grunt-lib-phantomjs/-/grunt-lib-phantomjs-1.1.0.tgz", "dev": true}, "grunt-saucelabs": {"version": "9.0.0", "from": "grunt-saucelabs@>=9.0.0 <10.0.0", "resolved": "https://registry.npmjs.org/grunt-saucelabs/-/grunt-saucelabs-9.0.0.tgz", "dev": true, "dependencies": {"lodash": {"version": "4.13.1", "from": "lodash@>=4.13.1 <4.14.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.13.1.tgz", "dev": true}}}, "grunt-stamp": {"version": "0.3.0", "from": "grunt-stamp@>=0.3.0 <0.4.0", "resolved": "https://registry.npmjs.org/grunt-stamp/-/grunt-stamp-0.3.0.tgz", "dev": true}, "har-validator": {"version": "2.0.6", "from": "har-validator@>=2.0.6 <2.1.0", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.6.tgz", "dev": true, "dependencies": {"commander": {"version": "2.9.0", "from": "commander@>=2.9.0 <3.0.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.9.0.tgz", "dev": true}}}, "has-ansi": {"version": "2.0.0", "from": "has-ansi@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz", "dev": true}, "has-color": {"version": "0.1.7", "from": "has-color@>=0.1.7 <0.2.0", "resolved": "https://registry.npmjs.org/has-color/-/has-color-0.1.7.tgz", "dev": true}, "has-flag": {"version": "1.0.0", "from": "has-flag@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-1.0.0.tgz", "dev": true}, "has-unicode": {"version": "2.0.1", "from": "has-unicode@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz", "dev": true}, "hasha": {"version": "2.2.0", "from": "hasha@>=2.2.0 <2.3.0", "resolved": "https://registry.npmjs.org/hasha/-/hasha-2.2.0.tgz", "dev": true}, "hawk": {"version": "3.1.3", "from": "hawk@>=3.1.3 <3.2.0", "resolved": "https://registry.npmjs.org/hawk/-/hawk-3.1.3.tgz", "dev": true}, "hoek": {"version": "2.16.3", "from": "hoek@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/hoek/-/hoek-2.16.3.tgz", "dev": true}, "home-or-tmp": {"version": "2.0.0", "from": "home-or-tmp@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/home-or-tmp/-/home-or-tmp-2.0.0.tgz", "dev": true}, "hooker": {"version": "0.2.3", "from": "hooker@>=0.2.3 <0.3.0", "resolved": "https://registry.npmjs.org/hooker/-/hooker-0.2.3.tgz", "dev": true}, "hosted-git-info": {"version": "2.1.5", "from": "hosted-git-info@>=2.1.4 <3.0.0", "resolved": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.1.5.tgz", "dev": true}, "htmlhint": {"version": "0.9.13", "from": "htmlhint@>=0.9.13 <0.10.0", "resolved": "https://registry.npmjs.org/htmlhint/-/htmlhint-0.9.13.tgz", "dev": true, "dependencies": {"async": {"version": "1.4.2", "from": "async@1.4.2", "resolved": "https://registry.npmjs.org/async/-/async-1.4.2.tgz", "dev": true}, "colors": {"version": "1.0.3", "from": "colors@1.0.3", "resolved": "https://registry.npmjs.org/colors/-/colors-1.0.3.tgz", "dev": true}, "commander": {"version": "2.6.0", "from": "commander@2.6.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.6.0.tgz", "dev": true}, "glob": {"version": "5.0.15", "from": "glob@5.0.15", "resolved": "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz", "dev": true}}}, "htmlparser2": {"version": "3.8.3", "from": "htmlparser2@>=3.8.0 <3.9.0", "resolved": "https://registry.npmjs.org/htmlparser2/-/htmlparser2-3.8.3.tgz", "dev": true, "dependencies": {"isarray": {"version": "0.0.1", "from": "isarray@0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "dev": true}, "readable-stream": {"version": "1.1.14", "from": "readable-stream@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "dev": true}}}, "http-errors": {"version": "1.5.1", "from": "http-errors@>=1.5.0 <1.6.0", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-1.5.1.tgz", "dev": true}, "http-signature": {"version": "1.1.1", "from": "http-signature@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-1.1.1.tgz", "dev": true}, "http2": {"version": "3.3.6", "from": "http2@>=3.3.4 <4.0.0", "resolved": "https://registry.npmjs.org/http2/-/http2-3.3.6.tgz", "dev": true}, "https-proxy-agent": {"version": "1.0.0", "from": "https-proxy-agent@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-1.0.0.tgz", "dev": true}, "iconv-lite": {"version": "0.4.15", "from": "iconv-lite@>=0.4.13 <0.5.0", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.15.tgz", "dev": true}, "ignore": {"version": "3.2.0", "from": "ignore@>=3.2.0 <4.0.0", "resolved": "https://registry.npmjs.org/ignore/-/ignore-3.2.0.tgz", "dev": true}, "imurmurhash": {"version": "0.1.4", "from": "imurmurhash@>=0.1.4 <0.2.0", "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "dev": true}, "in-publish": {"version": "2.0.0", "from": "in-publish@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/in-publish/-/in-publish-2.0.0.tgz", "dev": true}, "indent-string": {"version": "2.1.0", "from": "indent-string@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/indent-string/-/indent-string-2.1.0.tgz", "dev": true}, "inflight": {"version": "1.0.6", "from": "inflight@>=1.0.4 <2.0.0", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "dev": true}, "inherits": {"version": "2.0.3", "from": "inherits@>=2.0.3 <3.0.0", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "dev": true}, "inquirer": {"version": "0.12.0", "from": "inquirer@>=0.12.0 <0.13.0", "resolved": "https://registry.npmjs.org/inquirer/-/inquirer-0.12.0.tgz", "dev": true}, "interpret": {"version": "1.0.1", "from": "interpret@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/interpret/-/interpret-1.0.1.tgz", "dev": true}, "invariant": {"version": "2.2.2", "from": "invariant@>=2.2.0 <3.0.0", "resolved": "https://registry.npmjs.org/invariant/-/invariant-2.2.2.tgz", "dev": true}, "invert-kv": {"version": "1.0.0", "from": "invert-kv@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/invert-kv/-/invert-kv-1.0.0.tgz", "dev": true}, "is-arrayish": {"version": "0.2.1", "from": "is-arrayish@>=0.2.1 <0.3.0", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "dev": true}, "is-binary-path": {"version": "1.0.1", "from": "is-binary-path@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz", "dev": true, "optional": true}, "is-buffer": {"version": "1.1.4", "from": "is-buffer@>=1.0.2 <2.0.0", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.4.tgz", "dev": true}, "is-builtin-module": {"version": "1.0.0", "from": "is-builtin-module@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-builtin-module/-/is-builtin-module-1.0.0.tgz", "dev": true}, "is-dotfile": {"version": "1.0.2", "from": "is-dotfile@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-dotfile/-/is-dotfile-1.0.2.tgz", "dev": true}, "is-equal-shallow": {"version": "0.1.3", "from": "is-equal-shallow@>=0.1.3 <0.2.0", "resolved": "https://registry.npmjs.org/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz", "dev": true, "optional": true}, "is-extendable": {"version": "0.1.1", "from": "is-extendable@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz", "dev": true, "optional": true}, "is-extglob": {"version": "1.0.0", "from": "is-extglob@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "dev": true}, "is-finite": {"version": "1.0.2", "from": "is-finite@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-finite/-/is-finite-1.0.2.tgz", "dev": true}, "is-fullwidth-code-point": {"version": "1.0.0", "from": "is-fullwidth-code-point@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "dev": true}, "is-glob": {"version": "2.0.1", "from": "is-glob@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "dev": true}, "is-my-json-valid": {"version": "2.15.0", "from": "is-my-json-valid@>=2.10.0 <3.0.0", "resolved": "https://registry.npmjs.org/is-my-json-valid/-/is-my-json-valid-2.15.0.tgz", "dev": true}, "is-number": {"version": "2.1.0", "from": "is-number@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz", "dev": true}, "is-path-cwd": {"version": "1.0.0", "from": "is-path-cwd@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-1.0.0.tgz", "dev": true}, "is-path-in-cwd": {"version": "1.0.0", "from": "is-path-in-cwd@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-path-in-cwd/-/is-path-in-cwd-1.0.0.tgz", "dev": true}, "is-path-inside": {"version": "1.0.0", "from": "is-path-inside@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-1.0.0.tgz", "dev": true}, "is-posix-bracket": {"version": "0.1.1", "from": "is-posix-bracket@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz", "dev": true, "optional": true}, "is-primitive": {"version": "2.0.0", "from": "is-primitive@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/is-primitive/-/is-primitive-2.0.0.tgz", "dev": true}, "is-property": {"version": "1.0.2", "from": "is-property@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-property/-/is-property-1.0.2.tgz", "dev": true}, "is-resolvable": {"version": "1.0.0", "from": "is-resolvable@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-resolvable/-/is-resolvable-1.0.0.tgz", "dev": true}, "is-stream": {"version": "1.1.0", "from": "is-stream@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "dev": true}, "is-travis": {"version": "1.0.0", "from": "is-travis@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-travis/-/is-travis-1.0.0.tgz", "dev": true}, "is-typedarray": {"version": "1.0.0", "from": "is-typedarray@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "dev": true}, "is-utf8": {"version": "0.2.1", "from": "is-utf8@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz", "dev": true}, "isarray": {"version": "1.0.0", "from": "isarray@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "dev": true}, "isexe": {"version": "1.1.2", "from": "isexe@>=1.1.1 <2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-1.1.2.tgz", "dev": true}, "isobject": {"version": "2.1.0", "from": "isobject@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "dev": true, "optional": true}, "isstream": {"version": "0.1.2", "from": "isstream@>=0.1.2 <0.2.0", "resolved": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "dev": true}, "jodid25519": {"version": "1.0.2", "from": "jodid25519@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/jodid25519/-/jodid25519-1.0.2.tgz", "dev": true, "optional": true}, "jquery": {"version": "3.1.1", "from": "jquery@>=1.9.1", "resolved": "https://registry.npmjs.org/jquery/-/jquery-3.1.1.tgz"}, "js-base64": {"version": "2.1.9", "from": "js-base64@>=2.1.9 <3.0.0", "resolved": "https://registry.npmjs.org/js-base64/-/js-base64-2.1.9.tgz", "dev": true}, "js-tokens": {"version": "2.0.0", "from": "js-tokens@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-2.0.0.tgz", "dev": true}, "js-yaml": {"version": "3.7.0", "from": "js-yaml@>=3.5.1 <4.0.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.7.0.tgz", "dev": true}, "jsbn": {"version": "0.1.0", "from": "jsbn@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.0.tgz", "dev": true, "optional": true}, "jsesc": {"version": "0.5.0", "from": "jsesc@>=0.5.0 <0.6.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz", "dev": true}, "jshint": {"version": "2.8.0", "from": "jshint@2.8.0", "resolved": "https://registry.npmjs.org/jshint/-/jshint-2.8.0.tgz", "dev": true, "dependencies": {"lodash": {"version": "3.7.0", "from": "lodash@>=3.7.0 <3.8.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.7.0.tgz", "dev": true}, "minimatch": {"version": "2.0.10", "from": "minimatch@>=2.0.0 <2.1.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.10.tgz", "dev": true}, "shelljs": {"version": "0.3.0", "from": "shelljs@>=0.3.0 <0.4.0", "resolved": "https://registry.npmjs.org/shelljs/-/shelljs-0.3.0.tgz", "dev": true}}}, "json-schema": {"version": "0.2.3", "from": "json-schema@0.2.3", "resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz", "dev": true}, "json-stable-stringify": {"version": "1.0.1", "from": "json-stable-stringify@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz", "dev": true}, "json-stringify-safe": {"version": "5.0.1", "from": "json-stringify-safe@>=5.0.1 <5.1.0", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "dev": true}, "json5": {"version": "0.5.1", "from": "json5@>=0.5.0 <0.6.0", "resolved": "https://registry.npmjs.org/json5/-/json5-0.5.1.tgz", "dev": true}, "jsonfile": {"version": "2.4.0", "from": "jsonfile@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-2.4.0.tgz", "dev": true}, "jsonify": {"version": "0.0.0", "from": "jsonify@>=0.0.0 <0.1.0", "resolved": "https://registry.npmjs.org/jsonify/-/jsonify-0.0.0.tgz", "dev": true}, "jsonpointer": {"version": "4.0.1", "from": "jsonpointer@>=4.0.0 <5.0.0", "resolved": "https://registry.npmjs.org/jsonpointer/-/jsonpointer-4.0.1.tgz", "dev": true}, "jsprim": {"version": "1.3.1", "from": "jsprim@>=1.2.2 <2.0.0", "resolved": "https://registry.npmjs.org/jsprim/-/jsprim-1.3.1.tgz", "dev": true}, "kew": {"version": "0.7.0", "from": "kew@>=0.7.0 <0.8.0", "resolved": "https://registry.npmjs.org/kew/-/kew-0.7.0.tgz", "dev": true}, "kind-of": {"version": "3.1.0", "from": "kind-of@>=3.0.2 <4.0.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.1.0.tgz", "dev": true}, "klaw": {"version": "1.3.1", "from": "klaw@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/klaw/-/klaw-1.3.1.tgz", "dev": true}, "lazy-cache": {"version": "1.0.4", "from": "lazy-cache@>=1.0.3 <2.0.0", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "dev": true}, "lazystream": {"version": "1.0.0", "from": "lazystream@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/lazystream/-/lazystream-1.0.0.tgz", "dev": true}, "lcid": {"version": "1.0.0", "from": "lcid@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/lcid/-/lcid-1.0.0.tgz", "dev": true}, "levn": {"version": "0.3.0", "from": "levn@>=0.3.0 <0.4.0", "resolved": "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz", "dev": true}, "livereload-js": {"version": "2.2.2", "from": "livereload-js@>=2.2.0 <3.0.0", "resolved": "https://registry.npmjs.org/livereload-js/-/livereload-js-2.2.2.tgz", "dev": true}, "load-grunt-tasks": {"version": "3.5.2", "from": "load-grunt-tasks@>=3.5.2 <4.0.0", "resolved": "https://registry.npmjs.org/load-grunt-tasks/-/load-grunt-tasks-3.5.2.tgz", "dev": true}, "load-json-file": {"version": "1.1.0", "from": "load-json-file@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/load-json-file/-/load-json-file-1.1.0.tgz", "dev": true, "dependencies": {"strip-bom": {"version": "2.0.0", "from": "strip-bom@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz", "dev": true}}}, "lodash": {"version": "4.17.4", "from": "lodash@>=4.2.0 <5.0.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.4.tgz", "dev": true}, "lodash.assign": {"version": "4.2.0", "from": "lodash.assign@>=4.2.0 <5.0.0", "resolved": "https://registry.npmjs.org/lodash.assign/-/lodash.assign-4.2.0.tgz", "dev": true}, "lodash.clonedeep": {"version": "4.5.0", "from": "lodash.clonedeep@>=4.3.2 <5.0.0", "resolved": "https://registry.npmjs.org/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz", "dev": true}, "lodash.mergewith": {"version": "4.6.0", "from": "lodash.mergewith@>=4.6.0 <5.0.0", "resolved": "https://registry.npmjs.org/lodash.mergewith/-/lodash.mergewith-4.6.0.tgz", "dev": true}, "lodash.pickby": {"version": "4.6.0", "from": "lodash.pickby@>=4.6.0 <5.0.0", "resolved": "https://registry.npmjs.org/lodash.pickby/-/lodash.pickby-4.6.0.tgz", "dev": true}, "longest": {"version": "1.0.1", "from": "longest@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz", "dev": true}, "loose-envify": {"version": "1.3.0", "from": "loose-envify@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.3.0.tgz", "dev": true}, "loud-rejection": {"version": "1.6.0", "from": "loud-rejection@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/loud-rejection/-/loud-rejection-1.6.0.tgz", "dev": true}, "lru-cache": {"version": "2.7.3", "from": "lru-cache@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.7.3.tgz", "dev": true}, "map-obj": {"version": "1.0.1", "from": "map-obj@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/map-obj/-/map-obj-1.0.1.tgz", "dev": true}, "media-typer": {"version": "0.3.0", "from": "media-typer@0.3.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "dev": true}, "meow": {"version": "3.7.0", "from": "meow@>=3.3.0 <4.0.0", "resolved": "https://registry.npmjs.org/meow/-/meow-3.7.0.tgz", "dev": true, "dependencies": {"minimist": {"version": "1.2.0", "from": "minimist@>=1.1.3 <2.0.0", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz", "dev": true}}}, "micromatch": {"version": "2.3.11", "from": "micromatch@>=2.1.5 <3.0.0", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "dev": true, "optional": true}, "mime": {"version": "1.3.4", "from": "mime@1.3.4", "resolved": "https://registry.npmjs.org/mime/-/mime-1.3.4.tgz", "dev": true}, "mime-db": {"version": "1.25.0", "from": "mime-db@>=1.25.0 <1.26.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.25.0.tgz", "dev": true}, "mime-types": {"version": "2.1.13", "from": "mime-types@>=2.1.11 <2.2.0", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.13.tgz", "dev": true}, "minimatch": {"version": "3.0.3", "from": "minimatch@>=3.0.2 <4.0.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.3.tgz", "dev": true}, "minimist": {"version": "0.0.8", "from": "minimist@0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "dev": true}, "mkdirp": {"version": "0.5.1", "from": "mkdirp@>=0.5.0 <0.6.0", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "dev": true}, "morgan": {"version": "1.7.0", "from": "morgan@>=1.6.1 <2.0.0", "resolved": "https://registry.npmjs.org/morgan/-/morgan-1.7.0.tgz", "dev": true, "dependencies": {"debug": {"version": "2.2.0", "from": "debug@>=2.2.0 <2.3.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "dev": true}, "ms": {"version": "0.7.1", "from": "ms@0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "dev": true}}}, "ms": {"version": "0.7.2", "from": "ms@0.7.2", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.2.tgz", "dev": true}, "multimatch": {"version": "2.1.0", "from": "multimatch@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/multimatch/-/multimatch-2.1.0.tgz", "dev": true}, "mute-stream": {"version": "0.0.5", "from": "mute-stream@0.0.5", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.5.tgz", "dev": true}, "nan": {"version": "2.5.0", "from": "nan@>=2.3.2 <3.0.0", "resolved": "https://registry.npmjs.org/nan/-/nan-2.5.0.tgz", "dev": true}, "natural-compare": {"version": "1.4.0", "from": "natural-compare@>=1.4.0 <2.0.0", "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "dev": true}, "negotiator": {"version": "0.6.1", "from": "negotiator@0.6.1", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.1.tgz", "dev": true}, "neo-async": {"version": "1.8.2", "from": "neo-async@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/neo-async/-/neo-async-1.8.2.tgz", "dev": true}, "node-gyp": {"version": "3.4.0", "from": "node-gyp@>=3.3.1 <4.0.0", "resolved": "https://registry.npmjs.org/node-gyp/-/node-gyp-3.4.0.tgz", "dev": true, "dependencies": {"npmlog": {"version": "3.1.2", "from": "npmlog@>=0.0.0 <1.0.0||>=1.0.0 <2.0.0||>=2.0.0 <3.0.0||>=3.0.0 <4.0.0", "resolved": "https://registry.npmjs.org/npmlog/-/npmlog-3.1.2.tgz", "dev": true}}}, "node-sass": {"version": "4.1.1", "from": "node-sass@>=4.1.1 <5.0.0", "resolved": "https://registry.npmjs.org/node-sass/-/node-sass-4.1.1.tgz", "dev": true}, "nopt": {"version": "3.0.6", "from": "nopt@>=3.0.6 <3.1.0", "resolved": "https://registry.npmjs.org/nopt/-/nopt-3.0.6.tgz", "dev": true}, "normalize-package-data": {"version": "2.3.5", "from": "normalize-package-data@>=2.3.4 <3.0.0", "resolved": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.3.5.tgz", "dev": true}, "normalize-path": {"version": "2.0.1", "from": "normalize-path@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.0.1.tgz", "dev": true}, "normalize-range": {"version": "0.1.2", "from": "normalize-range@>=0.1.2 <0.2.0", "resolved": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz", "dev": true}, "npmlog": {"version": "4.0.2", "from": "npmlog@>=4.0.0 <5.0.0", "resolved": "https://registry.npmjs.org/npmlog/-/npmlog-4.0.2.tgz", "dev": true, "dependencies": {"gauge": {"version": "2.7.2", "from": "gauge@>=2.7.1 <2.8.0", "resolved": "https://registry.npmjs.org/gauge/-/gauge-2.7.2.tgz", "dev": true}, "supports-color": {"version": "0.2.0", "from": "supports-color@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-0.2.0.tgz", "dev": true}}}, "num2fraction": {"version": "1.2.2", "from": "num2fraction@>=1.2.2 <2.0.0", "resolved": "https://registry.npmjs.org/num2fraction/-/num2fraction-1.2.2.tgz", "dev": true}, "number-is-nan": {"version": "1.0.1", "from": "number-is-nan@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz", "dev": true}, "oauth-sign": {"version": "0.8.2", "from": "oauth-sign@>=0.8.1 <0.9.0", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.2.tgz", "dev": true}, "object-assign": {"version": "4.1.0", "from": "object-assign@>=4.0.1 <5.0.0", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.0.tgz", "dev": true}, "object.omit": {"version": "2.0.1", "from": "object.omit@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/object.omit/-/object.omit-2.0.1.tgz", "dev": true, "optional": true}, "on-finished": {"version": "2.3.0", "from": "on-finished@>=2.3.0 <2.4.0", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz", "dev": true}, "on-headers": {"version": "1.0.1", "from": "on-headers@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.1.tgz", "dev": true}, "once": {"version": "1.4.0", "from": "once@>=1.3.0 <2.0.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "dev": true}, "onetime": {"version": "1.1.0", "from": "onetime@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/onetime/-/onetime-1.1.0.tgz", "dev": true}, "opn": {"version": "4.0.2", "from": "opn@>=4.0.0 <5.0.0", "resolved": "https://registry.npmjs.org/opn/-/opn-4.0.2.tgz", "dev": true}, "optionator": {"version": "0.8.2", "from": "optionator@>=0.8.2 <0.9.0", "resolved": "https://registry.npmjs.org/optionator/-/optionator-0.8.2.tgz", "dev": true}, "os-homedir": {"version": "1.0.2", "from": "os-homedir@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz", "dev": true}, "os-locale": {"version": "1.4.0", "from": "os-locale@>=1.4.0 <2.0.0", "resolved": "https://registry.npmjs.org/os-locale/-/os-locale-1.4.0.tgz", "dev": true}, "os-tmpdir": {"version": "1.0.2", "from": "os-tmpdir@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "dev": true}, "osenv": {"version": "0.1.4", "from": "osenv@>=0.0.0 <1.0.0", "resolved": "https://registry.npmjs.org/osenv/-/osenv-0.1.4.tgz", "dev": true}, "package": {"version": "1.0.1", "from": "package@>=1.0.0 <1.2.0", "resolved": "https://registry.npmjs.org/package/-/package-1.0.1.tgz", "dev": true}, "parse-glob": {"version": "3.0.4", "from": "parse-glob@3.0.4", "resolved": "https://registry.npmjs.org/parse-glob/-/parse-glob-3.0.4.tgz", "dev": true}, "parse-json": {"version": "2.2.0", "from": "parse-json@>=2.2.0 <3.0.0", "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-2.2.0.tgz", "dev": true}, "parse-ms": {"version": "1.0.1", "from": "parse-ms@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/parse-ms/-/parse-ms-1.0.1.tgz", "dev": true}, "parserlib": {"version": "0.2.5", "from": "parserlib@>=0.2.2 <0.3.0", "resolved": "https://registry.npmjs.org/parserlib/-/parserlib-0.2.5.tgz", "dev": true}, "parseurl": {"version": "1.3.1", "from": "parseurl@>=1.3.1 <1.4.0", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.1.tgz", "dev": true}, "path-array": {"version": "1.0.1", "from": "path-array@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/path-array/-/path-array-1.0.1.tgz", "dev": true}, "path-exists": {"version": "2.1.0", "from": "path-exists@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-2.1.0.tgz", "dev": true}, "path-is-absolute": {"version": "1.0.1", "from": "path-is-absolute@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "dev": true}, "path-is-inside": {"version": "1.0.2", "from": "path-is-inside@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz", "dev": true}, "path-type": {"version": "1.1.0", "from": "path-type@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-1.1.0.tgz", "dev": true}, "pend": {"version": "1.2.0", "from": "pend@>=1.2.0 <1.3.0", "resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz", "dev": true}, "phantomjs-prebuilt": {"version": "2.1.14", "from": "phantomjs-prebuilt@>=2.1.3 <3.0.0", "resolved": "https://registry.npmjs.org/phantomjs-prebuilt/-/phantomjs-prebuilt-2.1.14.tgz", "dev": true}, "pify": {"version": "2.3.0", "from": "pify@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz", "dev": true}, "pinkie": {"version": "2.0.4", "from": "pinkie@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz", "dev": true}, "pinkie-promise": {"version": "2.0.1", "from": "pinkie-promise@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "dev": true}, "pkg-up": {"version": "1.0.0", "from": "pkg-up@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/pkg-up/-/pkg-up-1.0.0.tgz", "dev": true}, "plur": {"version": "1.0.0", "from": "plur@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/plur/-/plur-1.0.0.tgz", "dev": true}, "pluralize": {"version": "1.2.1", "from": "pluralize@>=1.2.1 <2.0.0", "resolved": "https://registry.npmjs.org/pluralize/-/pluralize-1.2.1.tgz", "dev": true}, "portscanner": {"version": "1.2.0", "from": "portscanner@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/portscanner/-/portscanner-1.2.0.tgz", "dev": true}, "postcss": {"version": "5.2.8", "from": "postcss@>=5.2.8 <6.0.0", "resolved": "https://registry.npmjs.org/postcss/-/postcss-5.2.8.tgz", "dev": true}, "postcss-cli": {"version": "2.6.0", "from": "postcss-cli@>=2.6.0 <3.0.0", "resolved": "https://registry.npmjs.org/postcss-cli/-/postcss-cli-2.6.0.tgz", "dev": true, "dependencies": {"glob": {"version": "6.0.4", "from": "glob@>=6.0.1 <7.0.0", "resolved": "https://registry.npmjs.org/glob/-/glob-6.0.4.tgz", "dev": true}, "globby": {"version": "4.1.0", "from": "globby@>=4.1.0 <5.0.0", "resolved": "https://registry.npmjs.org/globby/-/globby-4.1.0.tgz", "dev": true}}}, "postcss-flexbugs-fixes": {"version": "2.1.0", "from": "postcss-flexbugs-fixes@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/postcss-flexbugs-fixes/-/postcss-flexbugs-fixes-2.1.0.tgz", "dev": true}, "postcss-value-parser": {"version": "3.3.0", "from": "postcss-value-parser@>=3.2.3 <4.0.0", "resolved": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.3.0.tgz", "dev": true}, "prelude-ls": {"version": "1.1.2", "from": "prelude-ls@>=1.1.2 <1.2.0", "resolved": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz", "dev": true}, "preserve": {"version": "0.2.0", "from": "preserve@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/preserve/-/preserve-0.2.0.tgz", "dev": true, "optional": true}, "pretty-bytes": {"version": "3.0.1", "from": "pretty-bytes@>=3.0.1 <4.0.0", "resolved": "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-3.0.1.tgz", "dev": true}, "pretty-ms": {"version": "2.1.0", "from": "pretty-ms@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/pretty-ms/-/pretty-ms-2.1.0.tgz", "dev": true}, "private": {"version": "0.1.6", "from": "private@>=0.1.6 <0.2.0", "resolved": "https://registry.npmjs.org/private/-/private-0.1.6.tgz", "dev": true}, "process-nextick-args": {"version": "1.0.7", "from": "process-nextick-args@>=1.0.6 <1.1.0", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.7.tgz", "dev": true}, "progress": {"version": "1.1.8", "from": "progress@>=1.1.8 <2.0.0", "resolved": "https://registry.npmjs.org/progress/-/progress-1.1.8.tgz", "dev": true}, "pseudomap": {"version": "1.0.2", "from": "pseudomap@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz", "dev": true}, "punycode": {"version": "1.4.1", "from": "punycode@>=1.4.1 <2.0.0", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "dev": true}, "q": {"version": "1.4.1", "from": "q@>=1.4.1 <1.5.0", "resolved": "https://registry.npmjs.org/q/-/q-1.4.1.tgz", "dev": true}, "qs": {"version": "6.3.0", "from": "qs@>=6.3.0 <6.4.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.3.0.tgz", "dev": true}, "randomatic": {"version": "1.1.6", "from": "randomatic@>=1.1.3 <2.0.0", "resolved": "https://registry.npmjs.org/randomatic/-/randomatic-1.1.6.tgz", "dev": true, "optional": true}, "range-parser": {"version": "1.2.0", "from": "range-parser@>=1.2.0 <1.3.0", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.0.tgz", "dev": true}, "raw-body": {"version": "2.1.7", "from": "raw-body@>=2.1.5 <2.2.0", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.1.7.tgz", "dev": true, "dependencies": {"bytes": {"version": "2.4.0", "from": "bytes@2.4.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-2.4.0.tgz", "dev": true}, "iconv-lite": {"version": "0.4.13", "from": "iconv-lite@0.4.13", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.13.tgz", "dev": true}}}, "read-file-stdin": {"version": "0.2.1", "from": "read-file-stdin@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/read-file-stdin/-/read-file-stdin-0.2.1.tgz", "dev": true}, "read-pkg": {"version": "1.1.0", "from": "read-pkg@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/read-pkg/-/read-pkg-1.1.0.tgz", "dev": true}, "read-pkg-up": {"version": "1.0.1", "from": "read-pkg-up@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-1.0.1.tgz", "dev": true}, "readable-stream": {"version": "2.2.2", "from": "readable-stream@>=2.2.2 <3.0.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.2.tgz", "dev": true}, "readdirp": {"version": "2.1.0", "from": "readdirp@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-2.1.0.tgz", "dev": true, "optional": true}, "readline2": {"version": "1.0.1", "from": "readline2@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/readline2/-/readline2-1.0.1.tgz", "dev": true}, "rechoir": {"version": "0.6.2", "from": "rechoir@>=0.6.2 <0.7.0", "resolved": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz", "dev": true}, "redent": {"version": "1.0.0", "from": "redent@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/redent/-/redent-1.0.0.tgz", "dev": true}, "regenerate": {"version": "1.3.2", "from": "regenerate@>=1.2.1 <2.0.0", "resolved": "https://registry.npmjs.org/regenerate/-/regenerate-1.3.2.tgz", "dev": true}, "regenerator-runtime": {"version": "0.10.1", "from": "regenerator-runtime@>=0.10.0 <0.11.0", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.10.1.tgz", "dev": true}, "regenerator-transform": {"version": "0.9.8", "from": "regenerator-transform@0.9.8", "resolved": "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.9.8.tgz", "dev": true}, "regex-cache": {"version": "0.4.3", "from": "regex-cache@>=0.4.2 <0.5.0", "resolved": "https://registry.npmjs.org/regex-cache/-/regex-cache-0.4.3.tgz", "dev": true, "optional": true}, "regexpu-core": {"version": "2.0.0", "from": "regexpu-core@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-2.0.0.tgz", "dev": true}, "regjsgen": {"version": "0.2.0", "from": "regjsgen@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.2.0.tgz", "dev": true}, "regjsparser": {"version": "0.1.5", "from": "regj<PERSON><PERSON>er@>=0.1.4 <0.2.0", "resolved": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.1.5.tgz", "dev": true}, "repeat-element": {"version": "1.1.2", "from": "repeat-element@>=1.1.2 <2.0.0", "resolved": "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.2.tgz", "dev": true}, "repeat-string": {"version": "1.6.1", "from": "repeat-string@>=1.5.2 <2.0.0", "resolved": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "dev": true}, "repeating": {"version": "2.0.1", "from": "repeating@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/repeating/-/repeating-2.0.1.tgz", "dev": true}, "request": {"version": "2.79.0", "from": "request@>=2.79.0 <2.80.0", "resolved": "https://registry.npmjs.org/request/-/request-2.79.0.tgz", "dev": true}, "request-progress": {"version": "2.0.1", "from": "request-progress@>=2.0.1 <2.1.0", "resolved": "https://registry.npmjs.org/request-progress/-/request-progress-2.0.1.tgz", "dev": true}, "requestretry": {"version": "1.9.1", "from": "requestretry@>=1.9.0 <1.10.0", "resolved": "https://registry.npmjs.org/requestretry/-/requestretry-1.9.1.tgz", "dev": true}, "require-directory": {"version": "2.1.1", "from": "require-directory@>=2.1.1 <3.0.0", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "dev": true}, "require-main-filename": {"version": "1.0.1", "from": "require-main-filename@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/require-main-filename/-/require-main-filename-1.0.1.tgz", "dev": true}, "require-uncached": {"version": "1.0.3", "from": "require-uncached@>=1.0.2 <2.0.0", "resolved": "https://registry.npmjs.org/require-uncached/-/require-uncached-1.0.3.tgz", "dev": true}, "resolve": {"version": "1.2.0", "from": "resolve@>=1.1.6 <2.0.0", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.2.0.tgz", "dev": true}, "resolve-from": {"version": "1.0.1", "from": "resolve-from@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-1.0.1.tgz", "dev": true}, "resolve-pkg": {"version": "0.1.0", "from": "resolve-pkg@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/resolve-pkg/-/resolve-pkg-0.1.0.tgz", "dev": true, "dependencies": {"resolve-from": {"version": "2.0.0", "from": "resolve-from@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-2.0.0.tgz", "dev": true}}}, "restore-cursor": {"version": "1.0.1", "from": "restore-cursor@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-1.0.1.tgz", "dev": true}, "right-align": {"version": "0.1.3", "from": "right-align@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz", "dev": true}, "rimraf": {"version": "2.5.4", "from": "rimraf@>=2.2.8 <3.0.0", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.5.4.tgz", "dev": true}, "run-async": {"version": "0.1.0", "from": "run-async@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/run-async/-/run-async-0.1.0.tgz", "dev": true}, "rx-lite": {"version": "3.1.2", "from": "rx-lite@>=3.1.2 <4.0.0", "resolved": "https://registry.npmjs.org/rx-lite/-/rx-lite-3.1.2.tgz", "dev": true}, "sass-graph": {"version": "2.1.2", "from": "sass-graph@>=2.1.1 <3.0.0", "resolved": "https://registry.npmjs.org/sass-graph/-/sass-graph-2.1.2.tgz", "dev": true}, "sauce-tunnel": {"version": "2.5.0", "from": "sauce-tunnel@>=2.5.0 <2.6.0", "resolved": "https://registry.npmjs.org/sauce-tunnel/-/sauce-tunnel-2.5.0.tgz", "dev": true}, "saucelabs": {"version": "1.2.0", "from": "saucelabs@>=1.2.0 <1.3.0", "resolved": "https://registry.npmjs.org/saucelabs/-/saucelabs-1.2.0.tgz", "dev": true}, "semver": {"version": "5.3.0", "from": "semver@>=2.0.0 <3.0.0||>=3.0.0 <4.0.0||>=4.0.0 <5.0.0||>=5.0.0 <6.0.0", "resolved": "https://registry.npmjs.org/semver/-/semver-5.3.0.tgz", "dev": true}, "send": {"version": "0.14.1", "from": "send@0.14.1", "resolved": "https://registry.npmjs.org/send/-/send-0.14.1.tgz", "dev": true, "dependencies": {"debug": {"version": "2.2.0", "from": "debug@>=2.2.0 <2.3.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "dev": true}, "ms": {"version": "0.7.1", "from": "ms@0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "dev": true}}}, "serve-index": {"version": "1.8.0", "from": "serve-index@>=1.7.1 <2.0.0", "resolved": "https://registry.npmjs.org/serve-index/-/serve-index-1.8.0.tgz", "dev": true, "dependencies": {"debug": {"version": "2.2.0", "from": "debug@>=2.2.0 <2.3.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "dev": true}, "ms": {"version": "0.7.1", "from": "ms@0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "dev": true}}}, "serve-static": {"version": "1.11.1", "from": "serve-static@>=1.10.0 <2.0.0", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.11.1.tgz", "dev": true}, "set-blocking": {"version": "2.0.0", "from": "set-blocking@>=2.0.0 <2.1.0", "resolved": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "dev": true}, "set-immediate-shim": {"version": "1.0.1", "from": "set-immediate-shim@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/set-immediate-shim/-/set-immediate-shim-1.0.1.tgz", "dev": true, "optional": true}, "setprototypeof": {"version": "1.0.2", "from": "setprototypeof@1.0.2", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.0.2.tgz", "dev": true}, "shelljs": {"version": "0.7.5", "from": "shelljs@>=0.7.5 <0.8.0", "resolved": "https://registry.npmjs.org/shelljs/-/shelljs-0.7.5.tgz", "dev": true}, "shx": {"version": "0.2.1", "from": "shx@>=0.2.1 <0.3.0", "resolved": "https://registry.npmjs.org/shx/-/shx-0.2.1.tgz", "dev": true, "dependencies": {"minimist": {"version": "1.2.0", "from": "minimist@>=1.2.0 <2.0.0", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz", "dev": true}}}, "sigmund": {"version": "1.0.1", "from": "sigmund@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/sigmund/-/sigmund-1.0.1.tgz", "dev": true}, "signal-exit": {"version": "3.0.2", "from": "signal-exit@>=3.0.0 <4.0.0", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.2.tgz", "dev": true}, "slash": {"version": "1.0.0", "from": "slash@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/slash/-/slash-1.0.0.tgz", "dev": true}, "slice-ansi": {"version": "0.0.4", "from": "slice-ansi@0.0.4", "resolved": "https://registry.npmjs.org/slice-ansi/-/slice-ansi-0.0.4.tgz", "dev": true}, "sntp": {"version": "1.0.9", "from": "sntp@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/sntp/-/sntp-1.0.9.tgz", "dev": true}, "source-map": {"version": "0.5.6", "from": "source-map@>=0.5.6 <0.6.0", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.6.tgz", "dev": true}, "source-map-support": {"version": "0.4.8", "from": "source-map-support@>=0.4.2 <0.5.0", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.4.8.tgz", "dev": true}, "spdx-correct": {"version": "1.0.2", "from": "spdx-correct@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/spdx-correct/-/spdx-correct-1.0.2.tgz", "dev": true}, "spdx-expression-parse": {"version": "1.0.4", "from": "spdx-expression-parse@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-1.0.4.tgz", "dev": true}, "spdx-license-ids": {"version": "1.2.2", "from": "spdx-license-ids@>=1.0.2 <2.0.0", "resolved": "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-1.2.2.tgz", "dev": true}, "split": {"version": "1.0.0", "from": "split@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/split/-/split-1.0.0.tgz", "dev": true}, "sprintf-js": {"version": "1.0.3", "from": "sprintf-js@>=1.0.2 <1.1.0", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "dev": true}, "sshpk": {"version": "1.10.1", "from": "sshpk@>=1.7.0 <2.0.0", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.10.1.tgz", "dev": true, "dependencies": {"assert-plus": {"version": "1.0.0", "from": "assert-plus@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "dev": true}}}, "statuses": {"version": "1.3.1", "from": "statuses@>=1.3.0 <1.4.0", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.3.1.tgz", "dev": true}, "stdout-stream": {"version": "1.4.0", "from": "stdout-stream@>=1.4.0 <2.0.0", "resolved": "https://registry.npmjs.org/stdout-stream/-/stdout-stream-1.4.0.tgz", "dev": true}, "stream-buffers": {"version": "2.2.0", "from": "stream-buffers@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/stream-buffers/-/stream-buffers-2.2.0.tgz", "dev": true}, "string_decoder": {"version": "0.10.31", "from": "string_decoder@>=0.10.0 <0.11.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "dev": true}, "string-width": {"version": "1.0.2", "from": "string-width@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz", "dev": true}, "stringstream": {"version": "0.0.5", "from": "stringstream@>=0.0.4 <0.1.0", "resolved": "https://registry.npmjs.org/stringstream/-/stringstream-0.0.5.tgz", "dev": true}, "strip-ansi": {"version": "3.0.1", "from": "strip-ansi@>=3.0.0 <4.0.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "dev": true}, "strip-bom": {"version": "3.0.0", "from": "strip-bom@>=3.0.0 <4.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz", "dev": true}, "strip-indent": {"version": "1.0.1", "from": "strip-indent@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/strip-indent/-/strip-indent-1.0.1.tgz", "dev": true}, "strip-json-comments": {"version": "1.0.4", "from": "strip-json-comments@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.4.tgz", "dev": true}, "supports-color": {"version": "3.1.2", "from": "supports-color@>=3.1.2 <4.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-3.1.2.tgz", "dev": true}, "table": {"version": "3.8.3", "from": "table@>=3.7.8 <4.0.0", "resolved": "https://registry.npmjs.org/table/-/table-3.8.3.tgz", "dev": true, "dependencies": {"is-fullwidth-code-point": {"version": "2.0.0", "from": "is-fullwidth-code-point@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "dev": true}, "string-width": {"version": "2.0.0", "from": "string-width@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/string-width/-/string-width-2.0.0.tgz", "dev": true}}}, "tar": {"version": "2.2.1", "from": "tar@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/tar/-/tar-2.2.1.tgz", "dev": true}, "tar-stream": {"version": "1.5.2", "from": "tar-stream@>=1.5.0 <2.0.0", "resolved": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.5.2.tgz", "dev": true}, "temporary": {"version": "0.0.8", "from": "temporary@>=0.0.8 <0.0.9", "resolved": "https://registry.npmjs.org/temporary/-/temporary-0.0.8.tgz", "dev": true}, "tether": {"version": "1.4.0", "from": "tether@>=1.4.0 <2.0.0", "resolved": "https://registry.npmjs.org/tether/-/tether-1.4.0.tgz"}, "text-table": {"version": "0.2.0", "from": "text-table@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "dev": true}, "throttleit": {"version": "1.0.0", "from": "throttleit@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/throttleit/-/throttleit-1.0.0.tgz", "dev": true}, "through": {"version": "2.3.8", "from": "through@>=2.3.6 <3.0.0", "resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "dev": true}, "time-grunt": {"version": "1.4.0", "from": "time-grunt@>=1.4.0 <2.0.0", "resolved": "https://registry.npmjs.org/time-grunt/-/time-grunt-1.4.0.tgz", "dev": true}, "time-zone": {"version": "0.1.0", "from": "time-zone@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/time-zone/-/time-zone-0.1.0.tgz", "dev": true}, "tiny-lr": {"version": "0.2.1", "from": "tiny-lr@>=0.2.1 <0.3.0", "resolved": "https://registry.npmjs.org/tiny-lr/-/tiny-lr-0.2.1.tgz", "dev": true, "dependencies": {"debug": {"version": "2.2.0", "from": "debug@>=2.2.0 <2.3.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "dev": true}, "ms": {"version": "0.7.1", "from": "ms@0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "dev": true}, "qs": {"version": "5.1.0", "from": "qs@>=5.1.0 <5.2.0", "resolved": "https://registry.npmjs.org/qs/-/qs-5.1.0.tgz", "dev": true}}}, "tmp": {"version": "0.0.28", "from": "tmp@>=0.0.28 <0.0.29", "resolved": "https://registry.npmjs.org/tmp/-/tmp-0.0.28.tgz", "dev": true}, "to-fast-properties": {"version": "1.0.2", "from": "to-fast-properties@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-1.0.2.tgz", "dev": true}, "tough-cookie": {"version": "2.3.2", "from": "tough-cookie@>=2.3.0 <2.4.0", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.2.tgz", "dev": true}, "trim-newlines": {"version": "1.0.0", "from": "trim-newlines@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/trim-newlines/-/trim-newlines-1.0.0.tgz", "dev": true}, "tryit": {"version": "1.0.3", "from": "tryit@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/tryit/-/tryit-1.0.3.tgz", "dev": true}, "tunnel-agent": {"version": "0.4.3", "from": "tunnel-agent@>=0.4.1 <0.5.0", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.3.tgz", "dev": true}, "tweetnacl": {"version": "0.14.5", "from": "tweetnacl@>=0.14.0 <0.15.0", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "dev": true, "optional": true}, "type-check": {"version": "0.3.2", "from": "type-check@>=0.3.2 <0.4.0", "resolved": "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz", "dev": true}, "type-is": {"version": "1.6.14", "from": "type-is@>=1.6.10 <1.7.0", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.14.tgz", "dev": true}, "typedarray": {"version": "0.0.6", "from": "typedarray@>=0.0.6 <0.0.7", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "dev": true}, "uglify-js": {"version": "2.7.5", "from": "uglify-js@>=2.7.5 <3.0.0", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.7.5.tgz", "dev": true, "dependencies": {"async": {"version": "0.2.10", "from": "async@>=0.2.6 <0.3.0", "resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz", "dev": true}, "camelcase": {"version": "1.2.1", "from": "camelcase@>=1.0.2 <2.0.0", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz", "dev": true}, "cliui": {"version": "2.1.0", "from": "cliui@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz", "dev": true}, "window-size": {"version": "0.1.0", "from": "window-size@0.1.0", "resolved": "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz", "dev": true}, "wordwrap": {"version": "0.0.2", "from": "wordwrap@0.0.2", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz", "dev": true}, "yargs": {"version": "3.10.0", "from": "yargs@>=3.10.0 <3.11.0", "resolved": "https://registry.npmjs.org/yargs/-/yargs-3.10.0.tgz", "dev": true}}}, "uglify-to-browserify": {"version": "1.0.2", "from": "uglify-to-browserify@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz", "dev": true}, "underscore.string": {"version": "3.2.3", "from": "underscore.string@>=3.2.3 <3.3.0", "resolved": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.2.3.tgz", "dev": true}, "unpipe": {"version": "1.0.0", "from": "unpipe@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "dev": true}, "user-home": {"version": "2.0.0", "from": "user-home@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/user-home/-/user-home-2.0.0.tgz", "dev": true}, "util-deprecate": {"version": "1.0.2", "from": "util-deprecate@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "dev": true}, "utils-merge": {"version": "1.0.0", "from": "utils-merge@1.0.0", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.0.tgz", "dev": true}, "uuid": {"version": "3.0.1", "from": "uuid@>=3.0.0 <4.0.0", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.0.1.tgz", "dev": true}, "validate-npm-package-license": {"version": "3.0.1", "from": "validate-npm-package-license@>=3.0.1 <4.0.0", "resolved": "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.1.tgz", "dev": true}, "verror": {"version": "1.3.6", "from": "verror@1.3.6", "resolved": "https://registry.npmjs.org/verror/-/verror-1.3.6.tgz", "dev": true}, "vnu-jar": {"version": "16.6.29", "from": "vnu-jar@16.6.29", "resolved": "https://registry.npmjs.org/vnu-jar/-/vnu-jar-16.6.29.tgz", "dev": true}, "walkdir": {"version": "0.0.11", "from": "walkdir@>=0.0.11 <0.0.12", "resolved": "https://registry.npmjs.org/walkdir/-/walkdir-0.0.11.tgz", "dev": true}, "websocket-driver": {"version": "0.6.5", "from": "websocket-driver@>=0.5.1", "resolved": "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.6.5.tgz", "dev": true}, "websocket-extensions": {"version": "0.1.1", "from": "websocket-extensions@>=0.1.1", "resolved": "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.1.tgz", "dev": true}, "when": {"version": "3.7.7", "from": "when@>=3.7.5 <3.8.0", "resolved": "https://registry.npmjs.org/when/-/when-3.7.7.tgz", "dev": true}, "which": {"version": "1.2.12", "from": "which@>=1.2.1 <1.3.0", "resolved": "https://registry.npmjs.org/which/-/which-1.2.12.tgz", "dev": true}, "which-module": {"version": "1.0.0", "from": "which-module@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/which-module/-/which-module-1.0.0.tgz", "dev": true}, "wide-align": {"version": "1.1.0", "from": "wide-align@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.0.tgz", "dev": true}, "window-size": {"version": "0.2.0", "from": "window-size@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/window-size/-/window-size-0.2.0.tgz", "dev": true}, "wordwrap": {"version": "1.0.0", "from": "wordwrap@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz", "dev": true}, "wrap-ansi": {"version": "2.1.0", "from": "wrap-ansi@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-2.1.0.tgz", "dev": true}, "wrappy": {"version": "1.0.2", "from": "wrappy@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "dev": true}, "write": {"version": "0.2.1", "from": "write@>=0.2.1 <0.3.0", "resolved": "https://registry.npmjs.org/write/-/write-0.2.1.tgz", "dev": true}, "xml": {"version": "1.0.0", "from": "xml@1.0.0", "resolved": "https://registry.npmjs.org/xml/-/xml-1.0.0.tgz", "dev": true}, "xtend": {"version": "4.0.1", "from": "xtend@>=4.0.0 <5.0.0", "resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.1.tgz", "dev": true}, "y18n": {"version": "3.2.1", "from": "y18n@>=3.2.1 <4.0.0", "resolved": "https://registry.npmjs.org/y18n/-/y18n-3.2.1.tgz", "dev": true}, "yallist": {"version": "2.0.0", "from": "yallist@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/yallist/-/yallist-2.0.0.tgz", "dev": true}, "yargs": {"version": "4.8.1", "from": "yargs@>=4.7.1 <5.0.0", "resolved": "https://registry.npmjs.org/yargs/-/yargs-4.8.1.tgz", "dev": true}, "yargs-parser": {"version": "2.4.1", "from": "yargs-parser@>=2.4.1 <3.0.0", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-2.4.1.tgz", "dev": true, "dependencies": {"camelcase": {"version": "3.0.0", "from": "camelcase@>=3.0.0 <4.0.0", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-3.0.0.tgz", "dev": true}}}, "yauzl": {"version": "2.4.1", "from": "yauzl@2.4.1", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-2.4.1.tgz", "dev": true}, "zip-stream": {"version": "1.1.0", "from": "zip-stream@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/zip-stream/-/zip-stream-1.1.0.tgz", "dev": true}}}
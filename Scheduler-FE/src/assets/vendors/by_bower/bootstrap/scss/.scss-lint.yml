# Default application configuration that all configurations inherit from.
scss_files:
  - "**/*.scss"
  - "docs/assets/scss/**/*.scss"

exclude:
  - "scss/_normalize.scss"

plugin_directories: ['.scss-linters']

# List of gem names to load custom linters from (make sure they are already
# installed)
plugin_gems: []

# Default severity of all linters.
severity: warning

linters:
  BangFormat:
    enabled: true
    space_before_bang: true
    space_after_bang: false

  BemDepth:
    enabled: false
    max_elements: 1

  BorderZero:
    enabled: true
    convention: zero # or `none`
    exclude:
      - _normalize.scss

  ChainedClasses:
    enabled: false

  ColorKeyword:
    enabled: true

  ColorVariable:
    enabled: false

  Comment:
    enabled: true
    exclude:
      - _normalize.scss
      - bootstrap.scss
    style: silent

  DebugStatement:
    enabled: true

  DeclarationOrder:
    enabled: false

  DisableLinterReason:
    enabled: false

  DuplicateProperty:
    enabled: true

  ElsePlacement:
    enabled: true
    style: same_line # or 'new_line'

  EmptyLineBetweenBlocks:
    enabled: false
    ignore_single_line_blocks: true

  EmptyRule:
    enabled: true

  ExtendDirective:
    enabled: false

  FinalNewline:
    enabled: true
    present: true

  HexLength:
    enabled: true
    style: short # or 'long'

  HexNotation:
    enabled: true
    style: lowercase # or 'uppercase'

  HexValidation:
    enabled: true

  IdSelector:
    enabled: true

  ImportantRule:
    enabled: false

  ImportPath:
    enabled: true
    leading_underscore: false
    filename_extension: false

  Indentation:
    enabled: true
    allow_non_nested_indentation: false
    character: space # or 'tab'
    width: 2

  LeadingZero:
    enabled: true
    style: exclude_zero # or 'include_zero'
    exclude:
      - _normalize.scss

  MergeableSelector:
    enabled: false
    force_nesting: true

  NameFormat:
    enabled: true
    allow_leading_underscore: true
    convention: hyphenated_lowercase # or 'camel_case', or 'snake_case', or a regex pattern

  NestingDepth:
    enabled: true
    max_depth: 4
    ignore_parent_selectors: false

  PlaceholderInExtend:
    enabled: false

  PropertyCount:
    enabled: false
    include_nested: false
    max_properties: 10

  PropertySortOrder:
    enabled: true
    ignore_unspecified: false
    min_properties: 2
    separate_groups: false
    exclude:
      - _normalize.scss
    order:
      - position
      - top
      - right
      - bottom
      - left
      - z-index
      - -webkit-box-sizing
      - -moz-box-sizing
      - box-sizing
      - display
      - flex
      - flex-align
      - flex-basis
      - flex-direction
      - flex-wrap
      - flex-flow
      - flex-grow
      - flex-order
      - flex-pack
      - align-items
      - align-self
      - justify-content
      - float
      - width
      - min-width
      - max-width
      - height
      - min-height
      - max-height
      - padding
      - padding-top
      - padding-right
      - padding-bottom
      - padding-left
      - margin
      - margin-top
      - margin-right
      - margin-bottom
      - margin-left
      - overflow
      - overflow-x
      - overflow-y
      - -webkit-overflow-scrolling
      - -ms-overflow-x
      - -ms-overflow-y
      - -ms-overflow-style
      - clip
      - clear
      - font
      - font-family
      - font-size
      - font-style
      - font-weight
      - font-variant
      - font-size-adjust
      - font-stretch
      - font-effect
      - font-emphasize
      - font-emphasize-position
      - font-emphasize-style
      - font-smooth
      - -webkit-hyphens
      - -moz-hyphens
      - hyphens
      - line-height
      - color
      - text-align
      - -webkit-text-align-last
      - -moz-text-align-last
      - -ms-text-align-last
      - text-align-last
      - text-emphasis
      - text-emphasis-color
      - text-emphasis-style
      - text-emphasis-position
      - text-decoration
      - text-indent
      - text-justify
      - text-outline
      - -ms-text-overflow
      - text-overflow
      - text-overflow-ellipsis
      - text-overflow-mode
      - text-shadow
      - text-transform
      - text-wrap
      - -webkit-text-size-adjust
      - -ms-text-size-adjust
      - letter-spacing
      - -ms-word-break
      - word-break
      - word-spacing
      - -ms-word-wrap
      - word-wrap
      - overflow-wrap
      - -moz-tab-size
      - -o-tab-size
      - tab-size
      - white-space
      - vertical-align
      - list-style
      - list-style-position
      - list-style-type
      - list-style-image
      - pointer-events
      - -ms-touch-action
      - touch-action
      - cursor
      - visibility
      - zoom
      - table-layout
      - empty-cells
      - caption-side
      - border-spacing
      - border-collapse
      - content
      - quotes
      - counter-reset
      - counter-increment
      - resize
      - -webkit-user-select
      - -moz-user-select
      - -ms-user-select
      - -o-user-select
      - user-select
      - nav-index
      - nav-up
      - nav-right
      - nav-down
      - nav-left
      - background
      - background-color
      - background-image
      - -ms-filter:\\'progid:DXImageTransform.Microsoft.gradient
      - filter:progid:DXImageTransform.Microsoft.gradient
      - filter:progid:DXImageTransform.Microsoft.AlphaImageLoader
      - filter
      - background-repeat
      - background-attachment
      - background-position
      - background-position-x
      - background-position-y
      - -webkit-background-clip
      - -moz-background-clip
      - background-clip
      - background-origin
      - -webkit-background-size
      - -moz-background-size
      - -o-background-size
      - background-size
      - border
      - border-color
      - border-style
      - border-width
      - border-top
      - border-top-color
      - border-top-style
      - border-top-width
      - border-right
      - border-right-color
      - border-right-style
      - border-right-width
      - border-bottom
      - border-bottom-color
      - border-bottom-style
      - border-bottom-width
      - border-left
      - border-left-color
      - border-left-style
      - border-left-width
      - border-radius
      - border-top-left-radius
      - border-top-right-radius
      - border-bottom-right-radius
      - border-bottom-left-radius
      - -webkit-border-image
      - -moz-border-image
      - -o-border-image
      - border-image
      - -webkit-border-image-source
      - -moz-border-image-source
      - -o-border-image-source
      - border-image-source
      - -webkit-border-image-slice
      - -moz-border-image-slice
      - -o-border-image-slice
      - border-image-slice
      - -webkit-border-image-width
      - -moz-border-image-width
      - -o-border-image-width
      - border-image-width
      - -webkit-border-image-outset
      - -moz-border-image-outset
      - -o-border-image-outset
      - border-image-outset
      - -webkit-border-image-repeat
      - -moz-border-image-repeat
      - -o-border-image-repeat
      - border-image-repeat
      - outline
      - outline-width
      - outline-style
      - outline-color
      - outline-offset
      - -webkit-box-shadow
      - -moz-box-shadow
      - box-shadow
      - filter:progid:DXImageTransform.Microsoft.Alpha(Opacity
      - -ms-filter:\\'progid:DXImageTransform.Microsoft.Alpha
      - opacity
      - -ms-interpolation-mode
      - -webkit-transition
      - -moz-transition
      - -ms-transition
      - -o-transition
      - transition
      - -webkit-transition-delay
      - -moz-transition-delay
      - -ms-transition-delay
      - -o-transition-delay
      - transition-delay
      - -webkit-transition-timing-function
      - -moz-transition-timing-function
      - -ms-transition-timing-function
      - -o-transition-timing-function
      - transition-timing-function
      - -webkit-transition-duration
      - -moz-transition-duration
      - -ms-transition-duration
      - -o-transition-duration
      - transition-duration
      - -webkit-transition-property
      - -moz-transition-property
      - -ms-transition-property
      - -o-transition-property
      - transition-property
      - -webkit-transform
      - -moz-transform
      - -ms-transform
      - -o-transform
      - transform
      - -webkit-transform-origin
      - -moz-transform-origin
      - -ms-transform-origin
      - -o-transform-origin
      - transform-origin
      - -webkit-animation
      - -moz-animation
      - -ms-animation
      - -o-animation
      - animation
      - -webkit-animation-name
      - -moz-animation-name
      - -ms-animation-name
      - -o-animation-name
      - animation-name
      - -webkit-animation-duration
      - -moz-animation-duration
      - -ms-animation-duration
      - -o-animation-duration
      - animation-duration
      - -webkit-animation-play-state
      - -moz-animation-play-state
      - -ms-animation-play-state
      - -o-animation-play-state
      - animation-play-state
      - -webkit-animation-timing-function
      - -moz-animation-timing-function
      - -ms-animation-timing-function
      - -o-animation-timing-function
      - animation-timing-function
      - -webkit-animation-delay
      - -moz-animation-delay
      - -ms-animation-delay
      - -o-animation-delay
      - animation-delay
      - -webkit-animation-iteration-count
      - -moz-animation-iteration-count
      - -ms-animation-iteration-count
      - -o-animation-iteration-count
      - animation-iteration-count
      - -webkit-animation-direction
      - -moz-animation-direction
      - -ms-animation-direction
      - -o-animation-direction


  PropertySpelling:
    enabled: true
    extra_properties: []
    disabled_properties: []

  PropertyUnits:
    enabled: true
    global: [
      'ch', 'em', 'ex', 'rem',                 # Font-relative lengths
      'cm', 'in', 'mm', 'pc', 'pt', 'px', 'q', # Absolute lengths
      'vh', 'vw', 'vmin', 'vmax',              # Viewport-percentage lengths
      'deg', 'grad', 'rad', 'turn',            # Angle
      'ms', 's',                               # Duration
      'Hz', 'kHz',                             # Frequency
      'dpi', 'dpcm', 'dppx',                   # Resolution
      '%']                                     # Other
    properties: {}

  PseudoElement:
    enabled: true

  QualifyingElement:
    enabled: true
    allow_element_with_attribute: false
    allow_element_with_class: false
    allow_element_with_id: false

  SelectorDepth:
    enabled: true
    max_depth: 4

  SelectorFormat:
    enabled: false
    convention: hyphenated_lowercase # or 'strict_BEM', or 'hyphenated_BEM', or 'snake_case', or 'camel_case', or a regex pattern

  Shorthand:
    enabled: true
    allowed_shorthands: [1, 2, 3, 4]

  SingleLinePerProperty:
    enabled: false
    allow_single_line_rule_sets: true

  SingleLinePerSelector:
    enabled: false

  SpaceAfterComma:
    enabled: false
    style: one_space # or 'no_space', or 'at_least_one_space'

  SpaceAfterPropertyColon:
    enabled: true
    style: at_least_one_space # or 'no_space', or 'at_least_one_space', or 'aligned'

  SpaceAfterPropertyName:
    enabled: true

  SpaceAfterVariableName:
    enabled: true

  SpaceAroundOperator:
    enabled: true
    style: one_space # or 'at_least_one_space', or 'no_space'

  SpaceBeforeBrace:
    enabled: true
    style: space # or 'new_line'
    allow_single_line_padding: true

  SpaceBetweenParens:
    enabled: true
    spaces: 0

  StringQuotes:
    enabled: true
    style: double_quotes # or double_quotes

  TrailingSemicolon:
    enabled: true

  TrailingWhitespace:
    enabled: true

  TrailingZero:
    enabled: false

  TransitionAll:
    enabled: false

  UnnecessaryMantissa:
    enabled: true

  UnnecessaryParentReference:
    enabled: true

  UrlFormat:
    enabled: true

  UrlQuotes:
    enabled: true

  VariableForProperty:
    enabled: false
    properties: []

  VendorPrefix:
    enabled: true
    identifier_list: base
    additional_identifiers: []
    excluded_identifiers: []
    exclude:
      - _normalize.scss

  ZeroUnit:
    enabled: true

  Compass::*:
    enabled: false

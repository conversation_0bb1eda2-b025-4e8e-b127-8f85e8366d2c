// Bootstrap Grid only
//
// Includes relevant variables and mixins for the flexbox grid
// system, as well as the generated predefined classes (e.g., `.col-sm-4`).

//
// Box sizing, responsive, and more
//

@at-root {
  @-ms-viewport { width: device-width; }
}

html {
  box-sizing: border-box;
  -ms-overflow-style: scrollbar;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}


//
// Variables
//

@import "variables";

//
// Grid mixins
//

@import "mixins/clearfix";
@import "mixins/breakpoints";
@import "mixins/grid-framework";
@import "mixins/grid";

@import "custom";

@import "grid";

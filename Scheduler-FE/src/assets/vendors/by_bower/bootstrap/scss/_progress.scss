// Progress animations
@keyframes progress-bar-stripes {
  from { background-position: $progress-height 0; }
  to { background-position: 0 0; }
}

// Basic progress bar
.progress {
  display: flex;
  overflow: hidden; // force rounded corners by cropping it
  font-size: $progress-font-size;
  line-height: $progress-height;
  text-align: center;
  background-color: $progress-bg;
  @include border-radius($progress-border-radius);
}
.progress-bar {
  height: $progress-height;
  color: $progress-bar-color;
  background-color: $progress-bar-bg;
}

// Striped
.progress-bar-striped {
  @include gradient-striped();
  background-size: $progress-height $progress-height;
}

// Animated
.progress-bar-animated {
  animation: progress-bar-stripes $progress-bar-animation-timing;
}

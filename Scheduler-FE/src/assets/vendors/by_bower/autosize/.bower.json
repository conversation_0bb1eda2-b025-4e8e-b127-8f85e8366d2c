{"name": "autosize", "description": "Autosize is a small, stand-alone script to automatically adjust textarea height to fit text.", "dependencies": {}, "keywords": ["textarea", "form", "ui"], "authors": [{"name": "<PERSON>", "url": "http://www.jacklmoore.com", "email": "<EMAIL>"}], "license": "MIT", "homepage": "http://www.jacklmoore.com/autosize", "ignore": [], "repository": {"type": "git", "url": "http://github.com/jackmoore/autosize.git"}, "main": "dist/autosize.js", "moduleType": ["amd", "node"], "version": "3.0.20", "_release": "3.0.20", "_resolution": {"type": "version", "tag": "3.0.20", "commit": "2277ca66ae8e466c5159faf81069b94575db87aa"}, "_source": "https://github.com/jackmoore/autosize.git", "_target": "3.0.20", "_originalSource": "autosize"}
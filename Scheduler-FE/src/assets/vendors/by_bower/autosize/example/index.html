<!DOCTYPE html>
<html>
	<head>
		<meta charset='utf-8'/>
		<title>Simple Autosize for textareas</title>
		<style>
			textarea {
				padding: 10px;
				vertical-align: top;
				width: 200px;
			}
			textarea:focus {
				outline-style: solid;
				outline-width: 2px;
			}
		</style>
	</head>
	<body>
		<h3>max-height 300px</h3>
		<textarea style='max-height: 300px'>The coconut palm (also, cocoanut), Cocos nucifera, is a member of the family Arecaceae (palm family). It is the only accepted species in the genus Cocos.[2] The term coconut can refer to the entire coconut palm, the seed, or the fruit, which, botanically, is a drupe, not a nut. The spelling cocoanut is an archaic form of the word.[3] The term is derived from 16th-century Portuguese and Spanish coco, meaning "head" or "skull",[4] from the three small holes on the coconut shell that resemble human facial features.</textarea>

		<h3>no max-height</h3>
		<textarea>The coconut palm (also, cocoanut), Cocos nucifera, is a member of the family Arecaceae (palm family). It is the only accepted species in the genus Cocos.[2] The term coconut can refer to the entire coconut palm, the seed, or the fruit, which, botanically, is a drupe, not a nut. The spelling cocoanut is an archaic form of the word.[3] The term is derived from 16th-century Portuguese and Spanish coco, meaning "head" or "skull",[4] from the three small holes on the coconut shell that resemble human facial features.</textarea>
	</body>
	<script src='../dist/autosize.js'></script>
	<script>
		autosize(document.querySelectorAll('textarea'));
	</script>
</html>

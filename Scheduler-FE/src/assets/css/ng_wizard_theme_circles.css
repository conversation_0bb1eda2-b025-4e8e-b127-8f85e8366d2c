.ng-wizard-theme-circles {}

.ng-wizard-theme-circles .ng-wizard-container {
    min-height: 300px;
}

.ng-wizard-theme-circles .step-content {
    padding: 10px 0;
    background-color: #FFF;
    text-align: left;
}

.ng-wizard-theme-circles .ng-wizard-toolbar {
    background: #fff;
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 0 !important;
}

.ng-wizard-theme-circles .ng-wizard-toolbar-top {}

.ng-wizard-theme-circles .ng-wizard-toolbar-bottom {
    border-top-color: #ddd !important;
    border-bottom-color: #ddd !important;
}

.ng-wizard-theme-circles>ul.step-anchor {
    position: relative;
    background: #fff;
    border: none;
    list-style: none;
    margin-bottom: 40px;
}

.ng-wizard-theme-circles>ul.step-anchor:before {
    content: " ";
    position: absolute;
    top: 50%;
    bottom: 0;
    width: 100%;
    height: 5px;
    background-color: #f5f5f5;
    border-radius: 3px;
    z-index: 0;
}

.ng-wizard-theme-circles>ul.step-anchor>li {
    border: none;
    margin-left: 40px;
    z-index: 98;
}

.ng-wizard-theme-circles>ul.step-anchor>li>a {
    border: 2px solid #f5f5f5;
    background: #f5f5f5;
    width: 75px;
    height: 75px;
    text-align: center;
    padding: 25px 0;
    border-radius: 50%;
    -webkit-box-shadow: inset 0px 0px 0px 3px #fff !important;
    box-shadow: inset 0px 0px 0px 3px #fff !important;
    text-decoration: none;
    outline-style: none;
    z-index: 99;
    color: #bbb;
    background: #f5f5f5;
    line-height: 1;
}

.ng-wizard-theme-circles>ul.step-anchor>li>a:hover {
    color: #bbb;
    background: #f5f5f5;
    border-width: 2px;
}

.ng-wizard-theme-circles>ul.step-anchor>li>a>small {
    position: relative;
    bottom: -40px;
    color: #ccc;
}

.ng-wizard-theme-circles>ul.step-anchor>li.clickable>a:hover {
    color: #4285F4 !important;
}

.ng-wizard-theme-circles>ul.step-anchor>li.active>a {
    border-color: #5bc0de;
    color: #fff;
    background: #5bc0de;
}

.ng-wizard-theme-circles>ul.step-anchor>li.active>a>small {
    color: #5bc0de;
}

.ng-wizard-theme-circles>ul.step-anchor>li.done>a {
    border-color: #5cb85c;
    color: #fff;
    background: #5cb85c;
}

.ng-wizard-theme-circles>ul.step-anchor>li.done>a>small {
    color: #5cb85c;
}

.ng-wizard-theme-circles>ul.step-anchor>li.danger>a {
    border-color: #d9534f;
    color: #d9534f;
    background: #fff;
}

.ng-wizard-theme-circles>ul.step-anchor>li.danger>a>small {
    color: #d9534f;
}

.ng-wizard-theme-circles>ul.step-anchor>li.disabled>a,
.ng-wizard-theme-circles>ul.step-anchor>li.disabled>a:hover {
    color: #eee !important;
}
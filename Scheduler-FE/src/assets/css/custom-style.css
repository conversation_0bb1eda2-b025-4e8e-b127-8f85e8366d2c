.loading-style {
    border: 1px solid #ddd;
    padding: 6px 10px 6px 30px;
    display: block;
    border-radius: 4px;
    width: 120px;
    background: url("../images/loader-img.gif") no-repeat 5px center;
}
.horizontal-scroll-container  .ag-body-horizontal-scroll-viewport {
    overflow-x: hidden; 
}
.ag-ltr .ag-cell[col-id="priority"] {
    width: 50px !important;
  }
.margin-auto {
    margin: 0px auto;
}

.heightClass {
    height: 500px;
}

.cat__menu-left__logo img {
    max-height: 3.5rem !important;
    max-width: 90% !important;
    vertical-align: bottom !important;
}

breadcrumb .breadcrumb {
    background: #f8f8f8 !important;
    margin-top: 10px !important;
    border: 1px solid #e4e9f0;
    margin-left: 20px;
}

.pointer {
    cursor: pointer;
}

.sequenceCount {
    min-height: 82px !important;
}

.pagination {
    margin-right: 20px !important;
}

.pagination .page-item.disabled .page-link {
    color: #74708d !important;
}

.swap-btn {
    margin-left: 10px;
    cursor: pointer;
}

.loading {
    position: relative;
    text-align: center;
    z-index: 1000;
    top: 100px;
}

.count-bold {
    font-size: xx-large;
    font-weight: 600;
}

.scroll {
    border: 0;
    border-collapse: collapse;
}

.scroll tr {
    display: flex;
}

.scroll td {
    padding: 3px;
    flex: 1 auto;
    width: 100%;
    word-wrap: break-word;
    overflow: hidden;
    display: block;
}

.scroll thead tr:after {
    content: "";
    /* overflow-y: scroll; */
    visibility: hidden;
    height: 0;
}

.scroll thead th {
    flex: 1 auto;
    display: block;
    width: 100%;
    word-wrap: break-word;
    overflow: hidden;
}

.scroll tbody {
    display: block;
    width: 100%;
    /* overflow-y: scroll; */
    max-height: 300px;
}

@keyframes flashfade {
    0% {
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    80% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

.flashfade {
    animation-name: flashfade;
    animation-duration: 6s;
    display: block;
    text-align: center;
    padding-bottom: 0 !important;
}

.alert.alert-success {
    background: #46be8a;
    position: fixed;
    right: 0;
    bottom: 15px;
    z-index: 99;
    padding: 10px 25px !important;
    line-height: 25px;
}

.alert.alert-danger {
    background: red;
    position: fixed;
    right: 0;
    bottom: 15px;
    z-index: 99;
    padding: 10px 25px !important;
    line-height: 25px;
}

.editClass,
#deleteRoItem {
    background: none;
    border: none;
    color: #6c6c6d;
    margin: -5px;
    cursor: pointer;
}

.filter-accordion .card-header {
    padding: 0.75rem 1.25rem 0 1.25rem;
}

.filter-accordion .card-block {
    padding: 0 1.25rem 1.25rem 1.25rem;
}

.filter-form {
    position: absolute;
    background: #0190fe;
    left: -8px;
    top: -11px;
    bottom: 0px;
    width: 45px;
    text-align: center;
    padding-top: 12px !important;
}

.filter-form .minus,
.filter-form .plus {
    color: #fff;
}

.action-selectbtn {
    background: #6a7a84 !important;
    margin-right: 5px;
    color: #fff !important;
    padding: 7px;
    border-radius: 5px;
    font-size: 11px !important;
}

.editClass span {
    font-family: "PT Sans", sans-serif !important;
    font-size: 13px !important;
}

.form-control {
    border-color: #e4e9f0 !important;
}

.card-header.ui-sortable-handle {
    border: none !important;
}

.under-construction img {
    display: block;
    margin: 0 auto;
}

.cat__menu-left__item a {
    cursor: pointer;
}

.info {
    position: absolute;
    right: 20px;
}

.info i {
    font-size: 22px;
}

#showInfoIcon {
    cursor: pointer;
}

#showInfoDiv {
    position: fixed;
    right: 32px;
    margin-top: 25px;
    z-index: 9999;
}

.required {
    color: red;
}

#showInfoDiv .card-block {
    padding: 0px;
}

#showInfoDiv .card {
    padding: 10px;
}

#showInfoDiv .close {
    position: absolute;
    right: 10px;
    top: 10px;
}

@media (min-width: 992px) and (max-width: 1380px) {
    .cat_core_wrap .cat__core__widget .cat__core__step {
        min-height: 110px;
    }
}

body.cat__config--vertical.cat__config--compact .cat__menu-left__item {
    width: 100%;
    float: left;
}

body.cat__config--vertical.cat__config--compact .cat__menu-left__item>a,
body.cat__config--vertical.cat__config--compact .cat__menu-left__item>a:hover {
    cursor: pointer !important;
    float: left;
    width: 100%;
}

.typeahead__query .input-group .form-control:active,
.typeahead__query .input-group .form-control:focus,
.typeahead__query .input-group .form-control:hover {
    background: none !important;
    flex-direction: row;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    /* display: none; <- Crashes Chrome on hover */
    -webkit-appearance: none;
    margin: 0;
    /* <-- Apparently some margin are still there even though it's hidden */
}

input[type="number"] {
    -moz-appearance: textfield;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

body.cat__theme--light .cat__menu-left__item:active>a,
body.cat__theme--light .cat__menu-left__item:focus>a {
    background: none !important;
}

ul#opertionsSubMenu li.active a {
    color: #0a0a0a !important;
}

.prd-snapshot {
    background-color: #e4e9f0 !important;
}

.textinput::-ms-clear {
    display: none;
}

.cat__menu-left__logo {
    background: none !important;
}

.cat__menu-left__lock {
    background: none !important;
}

.breadcrumb_resize {
    margin-left: 220px !important;
}

.modal-footer>button {
    cursor: pointer;
}

.modal-body {
    font-family: Courier New, Courier, monospace;
}

.header_scrolled2 {
    position: fixed !important;
    top: 60px;
    z-index: 950;
}

.header_scrolled3 {
    position: fixed !important;
    top: 115px;
    z-index: 950;
}

.infoIcon {
    cursor: pointer;
}

.infoSummary {
    font-weight: bold;
}

#dueDateReportTable_filter {
    display: none;
}

.dropdown-list {
    position: absolute;
    padding-top: 14px;
    width: 100%;
    z-index: 90 !important;
}

.daterangepicker.dropdown-menu {
    max-width: none;
    z-index: 90 !important;
}

body {
    color: #0f1010 !important;
    font-size: 1rem !important;
}

.duedateReport.fixedHeader-floating {
    display: none;
}

.clear-search {
    border-radius: 50% !important;
    width: 20px;
    height: 20px;
    border: none !important;
    position: absolute;
    right: 38px;
    z-index: 100;
    top: 10px;
    box-shadow: none !important;
    text-align: center;
    color: #333;
}

body.cat__theme--light .cat__menu-left__item>a {
    color: #4b4a4e !important;
}

.cat__top-bar {
    color: #4b4a4e !important;
}

.single-selection .pure-checkbox[_ngcontent-c2] input[type="checkbox"][_ngcontent-c2]+label[_ngcontent-c2]::before,
.single-selection .pure-checkbox[_ngcontent-c1] input[type="checkbox"][_ngcontent-c1]+label[_ngcontent-c1]::before,
.single-selection .pure-checkbox[_ngcontent-c0] input[type="checkbox"][_ngcontent-c0]+label[_ngcontent-c0]::before {
    border-radius: 50%;
    border: 1px solid #0079fe !important;
    background: #fff !important;
}

.single-selection .pure-checkbox[_ngcontent-c2] input[type="checkbox"][_ngcontent-c2]:checked+label[_ngcontent-c2]:before,
.single-selection .pure-checkbox[_ngcontent-c1] input[type="checkbox"][_ngcontent-c1]:checked+label[_ngcontent-c1]:before,
.single-selection .pure-checkbox[_ngcontent-c0] input[type="checkbox"][_ngcontent-c0]:checked+label[_ngcontent-c0]:before {
    background: #fff !important;
}

.single-selection .pure-checkbox[_ngcontent-c2] input[type="checkbox"][_ngcontent-c2]+label[_ngcontent-c2]::after,
.single-selection .pure-checkbox[_ngcontent-c1] input[type="checkbox"][_ngcontent-c1]+label[_ngcontent-c1]::after,
.single-selection .pure-checkbox[_ngcontent-c0] input[type="checkbox"][_ngcontent-c0]+label[_ngcontent-c0]::after {
    top: 45.5% !important;
    left: 2.8px !important;
    width: 10px !important;
    height: 10px !important;
    margin-top: -5px !important;
    border-style: solid !important;
    border-color: #ffffff !important;
    border-width: 0 !important;
    border-image: none !important;
    transform: rotate(-45deg) scale(0);
    background: #0079fe !important;
    border-radius: 50px !important;
}

#parent-select .c-list {
    max-height: 100px;
    overflow-y: auto;
    max-width: 95%;
    min-width: 95%;
}

#year-month-select .c-list {
    max-height: 100px;
    overflow-y: auto;
    max-width: 95%;
    min-width: 95%;
}

.dt-head-center {
    vertical-align: middle !important;
}

.cat__menu-left:hover {
    transition: 0.2s ease-out 1s;
    transform: translate3d(0, 0, 0);
}

.selectedReport .cat__core__step {
    border-left: 10px solid rgba(0, 0, 0, 0.3) !important;
}

#reportSection .cat__core__step {
    border-radius: 0 !important;
}

.selected-list .c-btn {
    box-shadow: none !important;
}

.tag-checkbox {
    line-height: 10px;
}

.tag-checkbox input {
    float: left;
    margin-right: 10px;
}

.note-popover {
    display: none;
}

.scenario-key-search .card-block {
    padding-bottom: 0 !important;
}

.scenario-key-search .form-group {
    margin-bottom: 0 !important;
}

.sequence-section {
    float: left;
    margin-right: 20px;
}

.sequence-master {
    border: 1px solid #ddd;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 3px;
}

.sequence-master .btn {
    margin-bottom: 0 !important;
}

.sequence-action-btns {
    margin-top: 25px;
}

.filter-btn {
    color: #1790fe !important;
}

.scenario-dropdown li {
    border-bottom: 1px solid #e4e3e3;
}

.cat__core__step__digit {
    float: right !important;
}

.pagination {
    display: inline-flex !important;
}

.range-slider {
    padding-left: 20px;
}

.action-btn {
    background: #1790fe !important;
    margin-right: 5px;
    color: #fff !important;
    padding: 7px;
    border-radius: 5px;
    font-size: 11px !important;
}

.action-btn-disable {
    background: #1790fe !important;
    margin-right: 5px;
    color: #fff !important;
    padding: 7px;
    border-radius: 5px;
    font-size: 11px !important;
    cursor: not-allowed !important;
    opacity: 0.65;
}

div.dataTables_info {
    font-weight: bold;
    white-space: normal !important;
}

.tooltip-inner {
    max-width: 400px !important;
    white-space: pre-line;
    text-align: left !important;
    font-size: 12px !important;
    line-height: 16px !important;
}

.inner-data-cnt td,
.inner-data-cnt th {
    padding: 5px 5px !important;
}

.scenario-key-data .card-header {
    padding-left: 0 !important;
}

.break-word {
    word-wrap: break-word;
}

div.dataTables_paginate {
    text-align: left !important;
}

.plus {
    color: #0190fe;
}

.minus {
    color: #0190fe;
}

.selected-list[_ngcontent-c1] .c-list[_ngcontent-c1] .c-token[_ngcontent-c1] {
    margin-bottom: 4px;
}

.InnerDiv {
    position: relative;
}

.innerContent {
    height: 0px;
    width: 0px;
}

input[type="email"],
input[type="password"],
input[type="text"],
input[type="number"] {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    height: 42px;
    padding: 0 5px;
}

table {
    border-collapse: separate !important;
}

.ps-scrollbar-x {
    width: 0px !important;
}

.details-footer .card-footer {
    background: transparent !important;
}

table.dataTable.dtr-inline.collapsed>tbody>tr>td:first-child:before,
table.dataTable.dtr-inline.collapsed>tbody>tr>th:first-child:before {
    top: initial !important;
    vertical-align: middle;
}

.access-denied img {
    width: 100%;
}

.cat__pages__login {
    background-position: center center !important;
}

.cursor_default {
    cursor: default !important;
}

.select_row {
    background-color: lightgrey;
}

.custom-modal-dialog {
    max-width: none !important;
    margin: 30px auto;
    width: 50% !important;
}

.modal-lg {
    max-width: none !important;
    margin: 30px auto;
    width: 60% !important;
}

.warranty_basis {
    width: 100%;
    display: table;
}

.souce_data {
    width: 100%;
    display: table;
}

.uplift_target {
    width: 100%;
    display: table;
}

.data-master-tbody {
    max-height: 186px !important;
    overflow: auto;
    display: inline-block;
    width: 100%;
}

.store-tbody {
    max-height: 250px !important;
    overflow: auto;
    display: inline-block;
    width: 100%;
}

.scroll-tr {
    width: 100%;
    display: table;
}

.dt-checkboxes-select-all input {
    margin-left: -30px !important;
}

.link {
    color: #0190fe;
}

.unlink {
    color: #ef2b25;
}

.storeDetailsTable.fixedHeader-floating {
    display: none;
}

.duedateTitle {
    color: #0275d8 !important;
}

.duedateTitle:hover {
    color: #74a3c7 !important;
}

#brand-select .c-list {
    max-height: 100px;
    overflow-y: auto;
    max-width: 95%;
    min-width: 95%;
}

#state-select .c-list {
    max-height: 100px;
    overflow-y: auto;
    max-width: 95%;
    min-width: 95%;
}

.small-font {
    font-size: 0.85rem !important;
}

.sourceName:hover {
    color: #74a3c7 !important;
}

.basisName:hover {
    color: #74a3c7 !important;
}

.targetName:hover {
    color: #74a3c7 !important;
}

.sweet-alert {
    border: solid 1px;
}

.controlPanelStoreTable.fixedHeader-floating {
    display: none;
}

.dmTable.fixedHeader-floating {
    display: none;
}

.projectNameElipsis {
    width: 150px;
    overflow: hidden;
    display: inline-block;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 10px;
}

.sizeLarge {
    font-size: large;
    word-wrap: break-word;
}

.getProjectStageSummarytable {
    width: 99% !important;
}

.getProjectStageSummarytable table tr th {
    vertical-align: top !important;
}

.dt-head-right {
    text-align: right !important;
    vertical-align: middle !important;
}

.dt-head-left {
    text-align: left !important;
    vertical-align: middle !important;
}

.row-eq-height {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.primary-bundle-data .cat__core__step {
    background-color: transparent !important;
    color: #fff;
    padding-bottom: 35px;
}

.primary-bundle-data .col-lg-4 {
    position: relative;
}

.primaryData {
    position: absolute;
    bottom: 25px;
    right: 35px;
    margin-right: 17px;
}

.proj-table-border {
    border-right: 2px solid #000;
}

.single-selection.single-disabled .c-btn.disabled[_ngcontent-c2],
.single-selection.single-disabled .c-btn.disabled[_ngcontent-c1],
.single-selection.single-disabled .c-btn.disabled[_ngcontent-c0] {
    background: #fff !important;
}

.spinner-subscription {
    right: -144px;
    position: relative;
    top: 41px;
    z-index: 1000;
}

#cover-spin {
    position: fixed;
    width: 100%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: rgba(87, 86, 86, 0.9);
    z-index: 9999;
    text-align: center;
}

@-webkit-keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

#cover-spin::after {
    content: "";
    display: block;
    position: absolute;
    left: 48%;
    top: 40%;
    width: 40px;
    height: 40px;
    border-style: solid;
    border-color: #fff;
    border-top-color: transparent;
    border-width: 4px;
    border-radius: 50%;
    -webkit-animation: spin 0.8s linear infinite;
    animation: spin 0.8s linear infinite;
}

.spinnerText {
    text-align: center;
    position: relative;
    top: 53%;
    font-size: 16px;
    font-weight: normal;
    color: #fff;
}

.create-group-s360 {
    width: 85px;
    padding: 0px !important;
    float: right;
}

.solve360-store-link {
    cursor: pointer;
    float: right;
    position: relative;
    top: 9px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 30px;
    height: 17px;
    top: 6px;
}

.switch input {
    display: none;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: 0.4s;
    transition: 0.4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 13px;
    width: 13px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: 0.4s;
    transition: 0.4s;
}

input:checked+.slider {
    background-color: #2196f3;
}

input:focus+.slider {
    box-shadow: 0 0 1px #2196f3;
}

input:checked+.slider:before {
    -webkit-transform: translateX(13px);
    -ms-transform: translateX(13px);
    transform: translateX(13px);
}


/* Rounded sliders */

.slider.round {
    border-radius: 17px;
}

.slider.round:before {
    border-radius: 50%;
}

.dropdown-span .ng-invalid span:first-child {
    color: #bababc;
}

.red-background {
    background-color: #f7c1be;
}

#controlPanelStoreTable_wrapper {
    width: 100%;
}

.create_project_listall_span {
    position: relative;
    top: -13px;
    left: 47px;
}

.create_store_listall_span {
    position: relative;
    top: -15px;
    left: 42px;
}

.create_project_listall_container {
    float: left;
    position: absolute;
}

.create_store_listall_container {
    float: left;
    position: absolute;
    top: 10px;
    left: 102px;
}

.cat__menu-left__logo {
    width: 18.19rem !important;
    height: 4.64rem;
    display: inline-block;
    padding: 1rem 1.28rem;
}

.set_widget_border {
    border: 3px solid #000 !important;
}

.no_widget_border {
    border: none;
}

.revenue-bar {
    min-height: 122px !important;
}

.revenue-bar .cat__core__step1 {
    background-color: transparent !important;
    padding-bottom: 35px;
    padding: 1.42rem;
    border-radius: 3px;
    display: block;
}

.comment-text {
    display: block;
    width: 91%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    top: 22px;
    position: relative;
}

.comment-data-icon {
    margin-top: 20px;
    position: relative;
    float: right;
    top: -16px;
    right: -16px;
}

.comment-action-button {
    position: relative;
    left: -10px;
}

.sync-wrapper {
    float: right;
    font-size: 18px;
    color: #e20f0f;
    top: 11px;
    left: 92%;
    cursor: pointer;
    padding-right: 10px;
}

.sync-span {
    float: right;
    font-size: 18px;
    color: #099c0f;
    top: 11px;
    left: 92%;
    padding-right: 10px;
}

.form-group.has-danger .form-control,
.has-danger .form-control {
    border-color: #fb434a !important;
}

.red-background-pre-audit-report {
    background-color: #a20f0a;
    color: #ffffff;
}

.addTos360Link {
    float: left;
    color: #0190fe !important;
    font-size: 20px !important;
    position: absolute;
    margin-left: 15px;
    margin-right: 11px;
}

.s360Link {
    float: left;
    color: #f0ad4e !important;
    font-size: 19px !important;
    position: absolute;
    margin-left: 11px;
    margin-right: 11px;
    margin-top: -5px;
}

.action-btn-container {
    position: absolute;
    margin-left: 45px;
    top: -3px;
}

.action-btn-group {
    background: #1790fe !important;
    margin-right: 5px;
    color: #fff !important;
    padding: 4px;
    border-radius: 4px;
    font-size: 10px !important;
}

.editStores360 {
    float: right;
    font-size: 15px !important;
    background: #1790fe !important;
    margin-right: 5px;
    color: #fff !important;
    padding: 2px;
    border-radius: 5px;
}

.editStore {
    float: right;
    font-size: 15px !important;
    background: #1790fe !important;
    margin-right: 5px;
    color: #fff !important;
    padding: 2px;
    border-radius: 5px;
}

.editStoreContainer {
    float: left;
    position: absolute;
    margin-left: 43px;
}

.form-group.has-danger .ng-invalid .selected-list .c-btn {
    border-color: #fb434a !important;
}

.form-group.has-danger .create_store_listall_span {
    color: #000 !important;
}

.red-background-projection {
    background-color: #a20f0a;
}

.partsPssReportHeadV2 {
    border: 1px solid #cac7c7;
}

.partsPssReportSubListV2 {
    border: 1px solid #cac7c7;
}

.factBehaviorClassWhite {
    background-color: #ffffff !important;
}

.factBehaviorClassGreen {
    background-color: #d9ead3 !important;
}

.factBehaviorClassRed {
    background-color: #a20f0a !important;
    color: #ffffff !important;
}

.audit-final-result {
    padding-left: 0px !important;
    padding-right: 0px !important;
    padding-bottom: 0px !important;
}

.audit-final-result-sub1 {
    width: 43%;
    vertical-align: inherit !important;
    text-align: center;
    border: 1px solid #000;
    border-left: none;
    border-bottom: none;
}

.audit-final-result-sub2 {
    width: 57%;
    vertical-align: inherit !important;
    text-align: center;
    border: 1px solid #000;
    border-right: none;
    border-bottom: none;
}

.anual-fact-adj-sub1 {
    width: 32%;
    vertical-align: inherit !important;
    text-align: center;
    border: none !important;
    padding: 0rem !important;
}

.anual-fact-adj-sub2 {
    width: 57%;
    vertical-align: inherit !important;
    text-align: center;
    border: none !important;
    padding: 0rem !important;
}

#partsStage-select .c-list {
    max-height: 50px;
    overflow-y: auto;
    max-width: 95%;
    min-width: 95%;
}

#laborStage-select .c-list {
    max-height: 50px;
    overflow-y: auto;
    max-width: 95%;
    min-width: 95%;
}

.edit-icon-deadline-note {
    margin-left: 10px;
    position: relative;
    top: 2px;
    cursor: pointer;
}

.project-resolve-status {
    font-weight: bold;
    color: #f5a751;
    position: relative;
    top: 2px;
}

.create_store_listall_container2 {
    float: left;
    position: absolute;
    top: 10px;
    right: 0 !important;
}

.deleteStoreContainer {
    float: left;
    position: absolute;
    margin-left: 73px;
}

.deleteOrphanStoreContainer {
    float: left;
    margin-left: 30px;
}

.deleteStore,
.deleteStores360,
.deleteOrphanStores360 {
    float: right;
    font-size: 15px !important;
    background: #1790fe !important;
    margin-right: 5px;
    color: #fff !important;
    padding: 2px;
    border-radius: 3px;
}

.action-btn-group-ss {
    background: #1790fe !important;
    margin-right: 5px;
    color: #fff !important;
    padding: 4px;
    border-radius: 4px;
    font-size: 13px !important;
}

.updateParentGroup {
    float: left;
    position: absolute;
    margin-left: 100px;
}

.updateGroups360,
.updateGroup {
    float: right;
    font-size: 19px !important;
    color: #1790fe;
}

.sizeLarger {
    font-size: larger;
    word-wrap: break-word;
}

.btn1 {
    display: inline-block;
    margin-bottom: 0px;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    background-image: none;
    white-space: nowrap;
    font-size: 15px;
    line-height: 1.42857;
    user-select: none;
    border-width: 1px;
    border-style: solid;
    border-color: transparent;
    border-image: initial;
    padding: 10px 18px;
    border-radius: 0px;
}

.label {
    display: inline;
    padding: 0.2em 0.6em 0.3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25em;
}

.label-success {
    background-color: #5cb85c;
}

.label-scheduled {
    background-color: #5bc0de;
}

.label-running {
    background-color: #f0ad4e;
}

.label-repeating {
    background-color: #5bc0de;
}

.label-failed {
    background-color: #d9534f;
}

.label-upload-halt {
    background-color: #5b6b4d;
    margin-left: 10px;
}

.label-queued {
    background-color: #337ab7;
}

.label-locked {
    background-color: #f0ad4e;
}

.label-halt {
    background-color: #80047d;
}

.label-dead {
    background-color: rgb(106, 108, 109);
}

.label-resumed {
    background-color: #48663c;
}

.refresh-data-table-process-queue,
.refresh-data-table-process-list {
    font-size: 19px !important;
    position: relative;
    left: 5px;
}

#scheduleProcessCompleted_wrapper .row:nth-child(3) {
    margin-top: 10px;
}

#processXmlList_wrapper .row:nth-child(3) {
    margin-top: 10px;
}

#scheduleProcessQueue_wrapper .row:nth-child(3) {
    margin-top: 10px;
}

.accordion .card .card-header {
    cursor: default !important;
}

.accordion .card .card-header .accordion-indicator {
    cursor: pointer;
}

.dataTables_scrollHeadInner,
.dataTables_scrollHeadInner table {
    width: 100% !important;
}

.clear-padding {
    padding-top: 1px !important;
    padding-bottom: 0px !important;
}

.extraction-dashboard-ui angular4-multiselect .cuppa-dropdown .selected-list .c-btn {
    padding: 3px !important;
    position: relative;
    top: 6px;
}

.re-run-proxy-data {
    background-color: #d5e0d2;
}

.pay-type-filter-title {
    background-color: rgba(0, 0, 0, 0.05);
    height: 40px;
    padding: 1%;
}

.pay-type-filter-tbl-width {
    max-width: 800px;
}

#inv-select .c-btn span span {
    word-break: break-all !important;
}

.related-store-container .c-btn {
    height: 45px !important;
    overflow: hidden !important;
}


/* Added for dropdown settings*/

.searchicon-dropdown .filter-textbox {
    position: relative;
}

.searchicon-dropdown .filter-textbox:after {
    width: 30px;
    height: 30px;
    top: 24px;
    left: 14px;
    position: absolute;
    display: inline-block;
    content: "\f002";
    font: normal normal normal 15px/1 FontAwesome;
    color: #888;
}

.select-sp-report .true .multiselect-dropdown {
    border: 1px solid red;
}

.iel-checkbox {
    display: inline-block;
    margin-right: 30px;
}

.select-sp-report .multiselect-item-checkbox input[type="checkbox"]:checked+div:before,
.select-sp-report .multiselect-item-checkbox input[type="checkbox"]:checked+div::before {
    background: none !important;
    border: 0 !important;
}

.multiselect-item-checkbox input[type="checkbox"]+div:after {
    border-color: #337ab7 !important;
}

.multiselect-item-checkbox input[type="checkbox"]+div:before,
.multiselect-item-checkbox input[type="checkbox"]+div::before {
    border: 0 !important;
    box-shadow: none !important;
}

.rounded-selection .multiselect-item-checkbox input[type="checkbox"]+div:before {
    background: none !important;
    border: 1px solid #337ab7 !important;
    border-radius: 50% !important;
}

.rounded-selection .multiselect-item-checkbox input[type="checkbox"]+div:after {
    background: #337ab7 !important;
    width: 7px !important;
    height: 7px !important;
    border-radius: 50%;
    top: 9px !important;
    left: 3px !important;
}

.rounded-selection .selected-item {
    background: none !important;
    border: 0 !important;
}

.rounded-selection .selected-item span {
    color: #333;
}

.rounded-selection .selected-item a {
    display: none;
}

.dropdown-list .filter-textbox {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.dropdown-list {
    padding-top: 0 !important;
}

.dropdown-list .filter-textbox input {
    height: 35px;
}

.rounded-selection .searchicon-dropdown .filter-textbox:after {
    top: 10px !important;
}

.rounded-selection .item1 {
    overflow: hidden !important;
}

.searchicon-dropdown .dropdown-list {
    position: relative;
    margin-top: 13px !important;
}

.searchicon-dropdown .dropdown-list:before {
    width: 0;
    height: 0;
    border-left: 13px solid transparent;
    border-right: 13px solid transparent;
    border-bottom: 15px solid #fff;
    margin-left: 15px;
    position: absolute !important;
    top: -14px;
    content: "";
    z-index: 9;
}

.searchicon-dropdown .dropdown-list:after {
    width: 0;
    height: 0;
    border-left: 13px solid transparent;
    border-right: 13px solid transparent;
    border-bottom: 15px solid #ccc;
    margin-left: 15px;
    position: absolute !important;
    top: -15px;
    content: "";
}

.multi-search .filter-textbox:after {
    top: 10px !important;
}

.searchicon-dropdown ul.item1 {
    overflow: hidden !important;
}

.rouded-sel-label .dropdown-btn .selected-item {
    color: #000 !important;
    box-shadow: none !important;
}

.selected-item {
    font-family: "PT Sans", sans-serif !important;
    max-width: 100% !important;
}


/* .disabled a {
  display: none !important;
} */

.single-check-label .dropdown-btn .selected-item {
    background: none !important;
    border: 0 !important;
    color: #000 !important;
    box-shadow: none !important;
}

.searchicon-dropdown-dis .multiselect-dropdown .dropdown-btn .selected-item {
    background: #ccc !important;
    color: black !important;
    border: #ccc !important;
    box-shadow: none !important;
    text-shadow: none !important;
    pointer-events: none!important;
}

.searchicon-dropdown-dis span.dropdown-btn {
    background: #ccc !important;
    border: 1px solid #ccc !important;
}

.searchicon-dropdown-dis .selected-item a {
    display: none;
}

.searchicon-dropdown-dis-selected span.dropdown-btn {
    background: #ccc !important;
    border: 1px solid #ccc !important;
    pointer-events: none;
}

.filter-type-status .dropdown-list {
    position: absolute !important;
}

.searchicon-dropdown .dropdown-btn {
    background: #fff !important;
    border: 1px solid #ccc !important;
    border-radius: 3px !important;
    font-size: 14px !important;
    color: #333 !important;
    padding: 10px 5px !important;
}

.multiselect-dropdown .dropdown-btn .dropdown-multiselect__caret:before {
    top: 70% !important;
    color: #999;
    border-width: 8px 6px 0px !important;
}

.dm-select .multiselect-item-checkbox input[type="checkbox"]+div:before {
    top: 11px;
}

.ag-grid-custom-search {
    position: absolute;
    z-index: 9;
    width: 200px !important;
    height: 25px !important;
    border-radius: 0 !important;
    margin-top: 4px;
    margin-left: 11px;
}

.import-grid .ag-header-cell-text {
    font-size: 13px !important;
    color: black !important;
}

.ag-header-cell-text {
    overflow: visible !important;
    text-overflow: unset !important;
    white-space: normal !important;
}

.halt-class {
    background-color: purple !important;
    color: white !important;
}

.halt-import {
    background-color: #0190fe !important;
    color: white !important;
}

.halt-import1 {
    background-color: #0190fe !important;
    color: white !important;
}

.halt-import2 {
    background-color: #0190fe !important;
    color: white !important;
}

.halt-paytype {
    background-color: #0190fe !important;
    color: white !important;
}

.halt-dept {
    background-color: #0190fe !important;
    color: white !important;
}


/* .selectcustom {
  height: 201px !important;
} */

.icon-tooltip {
    cursor: pointer;
    /* Show pointer cursor on hover */
    margin-right: 5px;
    /* Add spacing between icons */
}


/* Adjust icon size and color */

.icon-tooltip i {
    font-size: 16px;
    /* Change the size of the icon */
    color: #000;
    /* Change the color of the icon */
}


/* Style tooltip */

.icon-tooltip:hover::after {
    content: attr(title);
    /* Set tooltip text */
    position: absolute;
    background-color: #333;
    color: #fff;
    padding: 4px;
    border-radius: 4px;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 999;
}

.halt-class {
    background: purple !important;
    color: white !important;
}

.badge-custom {
    font-weight: normal !important;
    font-style: normal !important;
    display: inline-block !important;
    padding: 0.25em 0.4em !important;
    font-size: 75% !important;
    font-weight: 700 !important;
    line-height: 1 !important;
    color: #fff !important;
    text-align: center !important;
    white-space: nowrap !important;
    vertical-align: baseline !important;
    border-radius: 0.25rem !important;
}

.badge-font {
    font-style: normal !important;
}

.disabled-dropdown.ng-multiselect-dropdown {
    opacity: 0.5;
    /* Adjust the opacity as needed */
    pointer-events: none;
}

.disabled-dropdown.ng-dropdown-panel {
    background-color: #f2f2f2;
    /* Change to the desired grey color */
    /* Additional styling for the dropdown options */
}

.disabled-dropdown .ng-option {
    color: #808080;
    /* Change to the desired grey color */
    /* Additional styling for the dropdown options */
}

.container-box {
    background-color: #f0f0f0;
    /* Background color */
    border: 1px solid #ccc;
    /* Border */
    border-radius: 5px;
    /* Rounded corners */
    padding: 20px;
    /* Padding */
    display: flex;
    /* Use flexbox */
    flex-direction: column;
    /* Arrange children vertically */
    align-items: center;
    /* Center horizontally */
    justify-content: center;
    /* Center vertically */
    text-align: center;
    /* Center align text */
    margin-top: 76px;
}

.container-box p.content {
    margin: 0;
    /* Remove default margin */
}

.container-box button {
    margin-top: 10px;
    /* Adds space between button and paragraph */
}

.hidden {
    display: none;
}

.ng-wizard-btn-prev {
    margin-right: 5px !important;
}

.ng-wizard-btn-next disabled {
    display: none;
}

.error-message {
    color: red;
    font-weight: bold;
    /* Add any other styles you want for error messages */
}

.store-loader {
    position: absolute;
    top: 58% !important;
    right: 12px;
}

.ng-wizard-btn-prev {
    cursor: pointer !important;
}

.ng-wizard-btn-next {
    cursor: pointer !important;
}

.btn btn-primary {
    cursor: pointer !important;
}

.btn-secondary.disabled,
.btn-secondary:disabled {
    display: none;
}

#tabImportContainer {
    width: 1140px;
    max-width: 100%;
    margin-left: 22px;
    margin-top: 30px;
}

.searchicon-dropdown-dis .dropdown-list {
    display: none !important;
}


/* .btn.btn-primary,
.show>.btn.btn-primary {
  cursor: pointer !important;
} */

.badge-custom.halt-class {
    margin-right: 5px;
    cursor: pointer !important;
}

.disabled-pointer {
    cursor: not-allowed !important;
}

.searchicon-dropdown-dms-dis-selected span.dropdown-btn {
    background: #ccc !important;
    border: 1px solid #ccc !important;
    pointer-events: none;
}

.searchicon-dropdown-dms-dis-selected .selected-item a {
    display: none !important;
}

.grid-highlight-yellow {
    background-color: #f8e681 !important;
}

.no-rows-overlay {
    padding: 10px;
    border: 2px solid #444;
    background: lightgoldenrodyellow;
    text-align: center;
    font-weight: bold;
    border-radius: 2px;
    display: flex;
    margin-top: 27px !important;
}

.selectcustom {
    overflow: hidden;
    width: 100%;
    min-height: 80px;
    /* Ensure the select box expands to fit its container */
}

.selectcustom option {
    padding: 2px;
    /* Adjust padding as needed */
}

.no-rows-overlay-seq {
    padding: 10px;
    border: 2px solid #444;
    background: lightgoldenrodyellow;
    text-align: center;
    font-weight: bold;
    border-radius: 2px;
    display: flex;
    margin-top: 27px !important;
}

.no-rows-import-overlay {
    padding: 10px;
    border: 2px solid #444;
    background: lightgoldenrodyellow;
    text-align: center;
    font-weight: bold;
    border-radius: 2px;
    display: flex;
    margin-top: 110px !important;
}

.no-rows-dept-overlay {
    padding: 10px;
    border: 2px solid #444;
    background: lightgoldenrodyellow;
    text-align: center;
    font-weight: bold;
    border-radius: 2px;
    display: flex;
    margin-top: 3px !important;
}

.dd-select .multiselect-item-checkbox input[type="checkbox"]+div:before {
    top: 11px !important;
}

.first-area {
    padding-left: 27px!important;
}

.extraction-type {
    font-weight: 700;
    position: relative;
    top: -2px!important;
    left: -5px!important;
}

.reynolds-mock {
    right: 21px!important;
}

.reynolds-save-file {
    right: 143px!important;
}

.autosoft-save-file {
    right: 4px!important;
}

.no-rows-seq-overlay {
    padding: 10px;
    border: 2px solid #444;
    background: lightgoldenrodyellow;
    text-align: center;
    font-weight: bold;
    border-radius: 2px;
    display: flex;
    margin-top: 60px !important;
}

.no-rows-roseq-overlay {
    padding: 10px;
    border: 2px solid #444;
    background: lightgoldenrodyellow;
    text-align: center;
    font-weight: bold;
    border-radius: 2px;
    display: flex;
    margin-top: 62px !important;
}

#archive-grid .ag-root-wrapper {
    overflow: visible !important;
}

.makecustom ul.item2 {
    position: relative;
}

.finishbutton {
    cursor: pointer !important;
}


/* wizard.component.css */

.full-screen-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* semi-transparent background */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    /* high z-index to ensure it appears on top */
}

.loader {
    border: 8px solid #f3f3f3;
    /* Light grey */
    border-top: 8px solid #3498db;
    /* Blue */
    border-radius: 50%;
    width: 60px;
    /* Smaller width */
    height: 60px;
    /* Smaller height */
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.ag-grid-custom-search-invoice {
    position: absolute;
    z-index: 9;
    width: 200px !important;
    height: 25px !important;
    border-radius: 0 !important;
    margin-top: 12px;
    margin-left: 168px;
}

.haltmessage {
    color: red;
    background-color: white;
    border: 1px solid red;
    padding: 4px;
    position: absolute;
    right: 22px;
}

.haltpaymessage {
    color: red;
    background-color: white;
    border: 1px solid red;
    padding: 4px;
    position: absolute;
    right: 2px;
}

.haltdeptmessage {
    color: red;
    background-color: white;
    border: 1px solid red;
    padding: 4px;
    position: absolute;
    right: 300px;
}

.haltdivmessage {
    position: relative;
}

.error-display-padding {
    padding-left: 50px;
    display: flex;
}


/* MS Tab Demo */

.ms-tab-demo .breadcrumb {
    padding: 0px;
    background: #d4d4d4;
    list-style: none;
    overflow: hidden;
    margin-top: 20px;
    width: 100%;
}

.ms-tab-demo .breadcrumb>li+li:before {
    padding: 0;
}

.ms-tab-demo .breadcrumb li {
    float: left;
}

.ms-tab-demo .breadcrumb li.completed a {
    background: brown;
    /* fallback color */
    background: #b1dfbb;
}

.ms-tab-demo .breadcrumb li.active a {
    background: brown;
    /* fallback color */
    background: #5cb85c;
}

.ms-tab-demo .breadcrumb li.error a {
    background: brown;
    /* fallback color */
    background: #d9534f;
}

.ms-tab-demo .breadcrumb li.completed a:after {
    border-left: 30px solid #b1dfbb;
}

.ms-tab-demo .breadcrumb li.active a:after {
    border-left: 30px solid #5cb85c;
}

.ms-tab-demo .breadcrumb li.error a:after {
    border-left: 30px solid #d9534f;
}

.ms-tab-demo .breadcrumb li.active a,
.ms-tab-demo .breadcrumb li.error a,
.ms-tab-demo .breadcrumb li.completed {
    color: #fff;
}

.ms-tab-demo .breadcrumb li a {
    color: #727272;
    text-decoration: none;
    padding: 10px 0 10px 45px;
    position: relative;
    display: block;
    float: left;
}

.ms-tab-demo .breadcrumb li a:after {
    content: " ";
    display: block;
    width: 0;
    height: 0;
    border-top: 50px solid transparent;
    /* Go big on the size, and let overflow hide */
    border-bottom: 50px solid transparent;
    border-left: 30px solid hsla(0, 0%, 83%, 1);
    position: absolute;
    top: 50%;
    margin-top: -50px;
    left: 100%;
    z-index: 2;
}

.ms-tab-demo .breadcrumb li a:before {
    content: " ";
    display: block;
    width: 0;
    height: 0;
    border-top: 50px solid transparent;
    /* Go big on the size, and let overflow hide */
    border-bottom: 50px solid transparent;
    border-left: 30px solid white;
    position: absolute;
    top: 50%;
    margin-top: -50px;
    margin-left: 1px;
    left: 100%;
    z-index: 1;
}

.ms-tab-demo .breadcrumb li:first-child a {
    padding-left: 15px;
}

.ms-tab-demo .breadcrumb li a:hover {
    background: #b1dfbb;
}

.ms-tab-demo .breadcrumb li a:hover:after {
    border-left-color: #b1dfbb !important;
}

.button-container {
    display: flex;
    justify-content: flex-end;
    /* / Aligns items to the right / */
    align-items: flex-end;
    /* / Aligns items at the bottom / */
    position: absolute;
    /* / or fixed, depending on your layout / */
    right: 20px;
    /* / Distance from the right edge / */
    bottom: 100px;
    /* / Distance from the bottom edge / */
    width: 100%;
    /* / Ensure the container takes full width / */
}

button.btn.btn-primary.import-previous {
    background-color: #6a7a84 !important;
    border-color: #6a7a84 !important;
    color: white !important;
    margin-right: 10px;
    /* / Adds space between buttons / */
}

button.btn.btn-primary.import-next {
    background-color: #6a7a84 !important;
    border-color: #6a7a84 !important;
    color: white !important;
}

button.btn.btn-primary.import-previous:hover,
button.btn.btn-primary.import-next:hover {
    background-color: #5a6a74 !important;
    border-color: #5a6a74 !important;
}

.disabled {
    pointer-events: none;
    opacity: 0.6;
    /* /optional: make disabled tabs visually distinct /; */
}

.ms-tab-container {
    min-width: 100% !important;
    padding: 0 !important;
}

.ms-tab-demo .breadcrumb {
    border-radius: 0;
    margin-top: 0;
}

.ms-tab-main {
    border: 1px solid #ccc;
    padding-bottom: 15px;
}

.next-prev-button-sec {
    padding: 10px 10px 0 10px;
    text-align: right;
}

.next-prev-button-sec button {
    cursor: pointer;
}

.make-container {
    padding: 10px;
}


/* Styles for both inv-select and store-select */

#inv-select .selected-item,
#store-select .selected-item {
    overflow: auto;
    word-wrap: break-word;
    white-space: normal;
    display: block !important;
}

#inv-select .selected-item-container,
#store-select .selected-item-container {
    float: none !important;
    width: 95%;
}

.de-form-control {
    position: relative;
}

.de-icon {
    background-color: #acb7bf;
    position: absolute;
    right: 10px;
    bottom: 0;
    padding: 10px 15px;
    border-radius: 0 5px 5px 00;
    top: 39px;
    height: 42px;
}

span.de-icon i {
    color: #fff;
}

.multiselect-item-checkbox input[type=checkbox]+div {
    word-break: break-all;
}

.btn.finishbuttons {
    cursor: pointer;
}

.expand-collapse-btn-make {
    background: #2196f3;
    border: #2196f3;
    font-size: 20px;
    cursor: pointer;
    position: relative;
    float: right;
    margin-bottom: -30px;
    bottom: 0px;
    z-index: 9;
    color: #fff;
    outline: 0px !important;
    width: 26px;
    line-height: 18px;
}

.expand-collapse-btn-invoice {
    background: #2196f3;
    border: #2196f3;
    font-size: 20px;
    cursor: pointer;
    position: relative;
    float: right;
    margin-bottom: -30px;
    bottom: -20px;
    z-index: 9;
    color: #fff;
    outline: 0px !important;
    width: 26px;
    line-height: 18px;
}

.expand-collapse-btn-invoice-2 {
    background: #2196f3;
    border: #2196f3;
    font-size: 20px;
    cursor: pointer;
    position: relative;
    float: right;
    margin-bottom: -30px;
    bottom: 0px;
    z-index: 9;
    color: #fff;
    outline: 0px !important;
    width: 26px;
    line-height: 18px;
}

.spacing-right {
    margin-right: -8px;
    margin-bottom: 2px !important;
    /* Adds space between Parts and Labor */
}

.spacing-left {
    margin-left: 1rem;
    margin-bottom: 2px !important;
    /* Adds space between Parts and Labor */
}


/* .show>.dropdown-menu.parts {
    animation-name: dropdown-slide-bottom;
    animation-duration: 0.15s;
    animation-fill-mode: both;
    animation-delay: 0.05s;
    display: block;
} */

.typeFilter {
    padding: 3px;
    margin-bottom: 10px;
    cursor: pointer;
    padding-left: 10px;
    border-bottom: 1px solid #bbb;
    border-color: #e6e6e6;
}

.margin-left-halt {
    margin-left: 340px !important;
    cursor: pointer;
}

.margin-left-default {
    margin-left: 504px !important;
    cursor: pointer;
}

.button-parts-default {
    margin-left: 37rem !important;
    padding: 2px 5px !important;
    font-size: 10px !important;
    font-weight: bold !important;
    cursor: pointer;
}

.button-partstype {
    margin-left: 26rem !important;
    padding: 2px 5px !important;
    font-size: 10px !important;
    font-weight: bold !important;
    cursor: pointer;
}

.button-labor-default {
    padding: 2px 5px !important;
    font-size: 10px !important;
    font-weight: bold !important;
    cursor: pointer;
}

.button-labortype {
    padding: 2px 5px !important;
    font-size: 10px !important;
    font-weight: bold !important;
    cursor: pointer;
}

.show .dropdown-menu.parts,
.dropdown-menu.labor {
    position: absolute;
    z-index: 1050;
    animation-name: dropdown-slide-bottom;
    animation-duration: 0.15s;
    animation-fill-mode: both;
}

.priority-ordering .ag-layout-auto-height .ag-center-cols-clipper, 
.priority-ordering .ag-layout-auto-height .ag-center-cols-container, 
.priority-ordering .ag-layout-print .ag-center-cols-clipper, 
.priority-ordering .ag-layout-print .ag-center-cols-container { 
    min-height: 150px !important;
}
.priority-ordering .ag-body-horizontal-scroll{
    display: none;
}
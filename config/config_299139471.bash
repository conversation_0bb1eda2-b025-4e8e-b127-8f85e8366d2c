# Bash Source Test Config File

export DMS_BASE='ReynoldsRCI'
source "$DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/ReynoldsRCI.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_ReynoldsRCI}"
PROMPT_FOR_FILE='true'

#ETL  DMS: ReynoldsRCI
#Base DMS: ReynoldsRCI (optional)

DMS="ReynoldsRCI"
DATE_PART='202507'

GROUP_CODE="QAREY521"
GROUPNAME="QAREY52"
STORENAME="QASREY522"
STORE_PART="QASREY523"
MFG="GM"
STATE='AR'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY522"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="rc20250714100526003117"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714100526.zip"
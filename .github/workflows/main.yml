on: 
  push:
    branches: master
name: Main Workflow
jobs:
  sonarQubeTriggerFE:
    name: SonarQube Trigger FE
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@master
    - name: SonarQube Scan
      uses: kitabisa/sonarqube-action@master
      with:
        host: ${{ secrets.SONARQUBE_HOST }}
        login: ${{ secrets.SONARQUBE_TOKEN }}
        projectKey: Scheduler-FE
        projectBaseDir: Scheduler-FE

  sonarQubeTriggerBE:
    name: SonarQube Trigger BE
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@master
    - name: SonarQube Scan
      uses: kitabisa/sonarqube-action@master
      with:
        host: ${{ secrets.SONARQUBE_HOST }}
        login: ${{ secrets.SONARQUBE_TOKEN }}        
        projectKey: Scheduler-BE
        projectBaseDir:    Scheduler-BE
#!/usr/bin/env bash
function initalizeConfiguration(){
   cd $WORK_DIRECTORY 
#    rm -rf  $WORK_DIRECTORY/*
} 

function unzipReynoldsWebhookFile() {
    gunzip -c "$WEBHOOK_INPUT_FILE_PATH" > $WORK_DIRECTORY/reynolds.xml
}

function unzipInvoiceMasterFile() {
    unzip -j -q "$INVOICE_MASTER_FILE_PATH" -d "$WORK_DIRECTORY"
}

function unifiedTogether(){
    zip -qr "$WORK_DIRECTORY"/"$TARGET_FILE_NAME" ./*
    mv "$WORK_DIRECTORY"/"$TARGET_FILE_NAME" "${TARGET_DIRECTORY_PATH}"/"${TARGET_FILE_NAME}"
    rm -rf  $WORK_DIRECTORY/*
}

WORK_DIRECTORY=$1
WEBHOOK_INPUT_FILE_PATH=$2
echo "WEBHOOK_INPUT_FILE_PATH":$WEBHOOK_INPUT_FILE_PATH 
INVOICE_MASTER_FILE_PATH=$3
echo "INVOICE_MASTER_FILE_PATH":$INVOICE_MASTER_FILE_PATH 
TARGET_DIRECTORY_PATH=$4
echo "TARGET_DIRECTORY_PATH":$TARGET_DIRECTORY_PATH 
TARGET_FILE_NAME=$5
echo "TARGET_FILE_NAME":$TARGET_FILE_NAME 
initalizeConfiguration
# unzipReynoldsWebhookFile
if [[ -z "$INVOICE_MASTER_FILE_PATH" ]]; then
    echo "No raw data zip file found!"
else
file_extension="${INVOICE_MASTER_FILE_PATH##*.}"
echo "file_extension:$file_extension"
if [[ "$file_extension" == "csv" ]]; then
  echo "The file is a CSV file."
  cp  "$INVOICE_MASTER_FILE_PATH" $WORK_DIRECTORY
else
  echo "The file is not a CSV file."
  unzipInvoiceMasterFile
fi

fi
unifiedTogether

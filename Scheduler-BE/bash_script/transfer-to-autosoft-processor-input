#!/usr/bin/env bash
function initalizeConfiguration(){
   cd $WORK_DIRECTORY 
   echo "WORK_DIRECTORY:$WORK_DIRECTORY"
#    rm -rf  $WORK_DIRECTORY/*
} 

function unzipReynoldsWebhookFile() {
    gunzip -c "$AUTOSOFT_INPUT_FILE_PATH" > $WORK_DIRECTORY/autosoft.txt
}

function unzipInvoiceMasterFile() {
    unzip -j -q "$INVOICE_MASTER_FILE_PATH" -d "$WORK_DIRECTORY"
}

function unifiedTogether(){
    zip -qr "$WORK_DIRECTORY"/"$TARGET_FILE_NAME" ./*
    mv "$WORK_DIRECTORY"/"$TARGET_FILE_NAME" "${TARGET_DIRECTORY_PATH}"/"${TARGET_FILE_NAME}"
    rm -rf  $WORK_DIRECTORY/*
}
echo "Inside transfer input  BASH script"

WORK_DIRECTORY=$1
AUTOSOFT_INPUT_FILE_PATH=$2
echo "AUTOSOFT_INPUT_FILE_PATH":$AUTOSOFT_INPUT_FILE_PATH 
INVOICE_MASTER_FILE_PATH=$3
echo "INVOICE_MASTER_FILE_PATH":$INVOICE_MASTER_FILE_PATH 
TARGET_DIRECTORY_PATH=$4
echo "TARGET_DIRECTORY_PATH":$TARGET_DIRECTORY_PATH 
TARGET_FILE_NAME=$5
echo "TARGET_FILE_NAME":$TARGET_FILE_NAME 
initalizeConfiguration
# unzipReynoldsWebhookFile
# if [[ -z "$INVOICE_MASTER_FILE_PATH" ]]; then
#     echo "No raw data zip file found!"
# else
#     unzipInvoiceMasterFile
# fi
unifiedTogether

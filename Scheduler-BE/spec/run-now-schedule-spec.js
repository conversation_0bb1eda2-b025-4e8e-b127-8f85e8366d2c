"use strict";

const { MongoClient } = require("mongodb");
const delay = require("delay");
const Agenda = require("agenda");
const CDKJobManager = require("../src/controllers/CDK/CDKJobManager");
const constants = require("../src/controllers/CDK/constants");
const util = require("../src/controllers/CDK/util");
var moment = require("moment-timezone");

// Create agenda instances
var agenda = null;
var mongoDb = null;
var mongoClient = null;

var today = new Date();
var schedule = moment().utc();

const clearJobs = () => {
    return mongoDb.collection("agendaJobs").deleteMany({});
};

describe("CDK extract Job schedule:", () => {

    beforeEach(done => {
        agenda = new Agenda({
            db: {
                address: constants.MONGO_DB_CONN,
                options: { useNewUrlParser: true }
            },
        }, err => {
            if (err) {
                done(err);
            }
            MongoClient.connect(constants.MONGO_DB_CONN, { useNewUrlParser: true },
                async (err, client) => {
                    if (err) {

                        done(err);
                    }
                    mongoClient = client;
                    mongoDb = client.db(process.env.MONGO_DB);
                    await delay(50);
                    done();
                });
        });
        agenda.define("CDK-EXTRACT", (job, done) => {
            console.log("CDK-EXTRACT");
        });

    });

    afterEach(async () => {
        await clearJobs();
        if (agenda != null) {
            await agenda.stop();
        }
        if (mongoClient != null) {
            await mongoClient.close();
        }
        if (agenda._db != null) {
            await agenda._db.close();
        }

    });
    describe("Create a schedule to run now", () => {
        it("with an already existing schedule for the same store", async () => {
            var jobInput = {
                jobName: "CDK_EXTRACT",
                jobSchedule: schedule,
                jobData: {
                    groupName: "G1",
                    storeDataArray: [
                        {
                            dealerId: "s1",
                            startDate: "09/25/2018",
                            endDate: "09/25/2018",
                            CloseROOption: "ALL"
                        },
                        {
                            dealerId: "s2",
                            startDate: "09/25/2018",
                            endDate: "09/25/2018",
                            CloseROOption: "ALL"
                        }
                    ]
                }
            };
            var response = await CDKJobManager.scheduleCDKExtractJob(jobInput);
            expect(response.status).toEqual(true);
            expect(response.job.data.groupName).toEqual("G1");
            expect(response.job.data.storeDataArray.length).toEqual(2);

            var runNowInput = {
                jobName: "CDK_EXTRACT",
                jobSchedule: schedule,
                jobData: {
                    groupName: "G1",
                    storeData: {
                        dealerId: "s1",
                        startDate: "09/25/2018",
                        endDate: "09/25/2018",
                        CloseROOption: "ALL"
                    }
                }
            };
            var response2 = await CDKJobManager.runNowCDKExtractJobByStore(runNowInput);
            expect(response2.status).toEqual(true);
            expect(response2.job.data.groupName).toEqual("G1");
            expect(response2.job.data.storeDataArray.length).toEqual(1);
            expect(response2.job.data.runNow).toEqual(true);
            var scheduleDateZ = util.toDate(jobInput.jobSchedule);
            var jobs = await agenda.jobs({
                $and: [
                    { "data.groupName": "G1" },
                    { nextRunAt: new Date(scheduleDateZ) }
                ]
            });
            expect(jobs[0].attrs.data.storeDataArray.includes(runNowInput.jobData.storeData)).toEqual(false);  //   s1 removed from Schedule
        });

        it("with an already existing store which removes the previous schedule and run now", async () => {


            var jobInput = {
                jobName: "CDK_EXTRACT",
                jobSchedule: schedule,
                jobData: {
                    groupName: "G1",
                    storeDataArray: [
                        {
                            dealerId: "s1",
                            startDate: "09/25/2018",
                            endDate: "09/25/2018",
                            CloseROOption: "ALL"
                        }
                    ]
                }
            };
            var response = await CDKJobManager.scheduleCDKExtractJob(jobInput);
            expect(response.status).toEqual(true);
            expect(response.job.data.groupName).toEqual("G1");
            expect(response.job.data.storeDataArray.length).toEqual(1);
            var runNowInput = {
                jobName: "CDK_EXTRACT",
                jobSchedule: schedule,
                jobData: {
                    groupName: "G1",
                    storeData: {
                        dealerId: "s1",
                        startDate: "09/25/2018",
                        endDate: "09/25/2018",
                        CloseROOption: "ALL"
                    }
                }
            };
            var response2 = await CDKJobManager.runNowCDKExtractJobByStore(runNowInput);
            expect(response2.status).toEqual(true);
            expect(response2.job.data.groupName).toEqual("G1");
            expect(response2.job.data.storeDataArray.length).toEqual(1);
            expect(response2.job.data.runNow).toEqual(true);

            var scheduleDateZ = util.toDate(runNowInput.jobSchedule);
            var jobs = await agenda.jobs({
                $and: [
                    { "data.groupName": "G1" },
                    { nextRunAt: new Date(scheduleDateZ) }
                ]
            });
            expect(jobs[0]).toBeDefined;  // Old job removed
        });
    });
});

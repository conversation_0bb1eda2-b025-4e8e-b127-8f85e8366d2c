"use strict";

const { MongoClient } = require("mongodb");
const Agenda = require("agenda");
const delay = require("delay");
const CDKJobManager = require("../src/controllers/CDK/CDKJobManager");
const constants = require("../src/controllers/CDK/constants");
const util = require("../src/controllers/CDK/util");

var moment = require("moment-timezone");

var agenda = null;
var mongoDb = null;
var mongoClient = null;

var today = new Date();
var schedule = moment().utc();

const clearJobs = () => {
    return mongoDb.collection("agendaJobs").deleteMany({});
};

describe("CDK extract Job schedule:", () => {
    beforeEach(done => {
        agenda = new Agenda({
            db: {
                address: constants.MONGO_DB_CONN,
                options: { useNewUrlParser: true }
            },
        }, err => {
            if (err) {
                done(err);
            }
            MongoClient.connect(constants.MONGO_DB_CONN, { useNewUrlParser: true },
                async (err, client) => {
                    if (err) {
                        done(err);
                    }
                    mongoClient = client;
                    mongoDb = client.db(process.env.MONGO_DB);
                    await delay(50);
                    done();
                });
        });
        agenda.define("CDK-EXTRACT", (job, done) => {
            console.log("CDK-EXTRACT");
        });
    });

    afterEach(async () => {
        await clearJobs();
        if (agenda != null) {
            await agenda.stop();
        }
        if (mongoClient != null) {
            await mongoClient.close();
        }
        if (agenda._db != null) {
            await agenda._db.close();
        }

    });
    describe("Cancel a schedule", () => {

        it("from an existing schedule with group of stores and removes only that store", async () => {
            var jobInput = {
                jobName: "CDK_EXTRACT",
                jobSchedule: schedule,
                jobData: {
                    groupName: "G1",
                    storeDataArray: [
                        {
                            dealerId: "s1",
                            startDate: "09/28/2018",
                            endDate: "09/28/2018",
                            CloseROOption: "ALL"
                        },
                        {
                            dealerId: "s2",
                            startDate: "09/28/2018",
                            endDate: "09/28/2018",
                            CloseROOption: "ALL"
                        }
                    ]
                }
            };

            var response = await CDKJobManager.scheduleCDKExtractJob(jobInput);
            expect(response.status).toEqual(true);
            expect(response.job.data.groupName).toEqual("G1");
            expect(response.job.data.storeDataArray.length).toEqual(2);
            var cancelInput = {
                jobSchedule: schedule,
                jobData: {
                    groupName: "G1",
                    storeData:
                    {
                        dealerId: "s2",
                        startDate: "09/28/2018",
                        endDate: "09/28/2018",
                        CloseROOption: "ALL"
                    }

                }
            };
            var response2 = await CDKJobManager.cancelCDKExtractJobByStore(cancelInput);
            expect(response2.status).toEqual(true);
            expect(response2).toBeDefined // s2 removed from schedule
        });
        it("from an existing schedule with a group having one store and cancels the whole group schedule", async () => {
            var jobInput = {
                jobName: "CDK_EXTRACT",
                jobSchedule: schedule,
                jobData: {
                    groupName: "G1",
                    storeDataArray: [
                        {
                            dealerId: "s1",
                            startDate: "09/28/2018",
                            endDate: "09/28/2018",
                            CloseROOption: "ALL"
                        }
                    ]
                }
            };
            var response = await CDKJobManager.scheduleCDKExtractJob(jobInput);
            expect(response.status).toEqual(true);
            expect(response.job.data.groupName).toEqual("G1");
            expect(response.job.data.storeDataArray.length).toEqual(1);
            var cancelInput = {
                jobSchedule: schedule,
                jobData: {
                    groupName: "G1",
                    storeData:
                    {
                        dealerId: "s1",
                        startDate: "09/28/2018",
                        endDate: "09/28/2018",
                        CloseROOption: "ALL"
                    }

                }
            };
            var response2 = await CDKJobManager.cancelCDKExtractJobByStore(cancelInput);
            expect(response2.status).toEqual(true);
            expect(response2.job).toEqual(null); // Whole schedule under that Group removed
        });
    });
});

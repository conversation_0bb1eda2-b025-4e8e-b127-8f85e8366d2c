"use strict";

const { MongoClient } = require("mongodb");
const delay = require("delay");
const Agenda = require("agenda");
const CDKJobManager = require("../src/controllers/CDK/CDKJobManager");
const constants = require("../src/controllers/CDK/constants");
const util = require("../src/controllers/CDK/util");
var moment = require("moment-timezone");

// Create agenda instances
var agenda = require("../src/controllers/agenda");
var mongoDb = null;
var mongoClient = null;

/**
 * Function to find the unique items in an array
 */
Array.prototype.unique = function () {
    var a = this.concat();
    for (var i = 0; i < a.length; ++i) {
        for (var j = i + 1; j < a.length; ++j) {
            if (a[i].dealerId === a[j].dealerId)
                a.splice(j--, 1);
        }
    }
    return a;
};

const clearJobs = () => {
    return mongoDb.collection("agendaJobs").deleteMany({});
};

describe("CDK extract Job schedule:", () => {

    beforeEach(done => {
        agenda = new Agenda({
            db: {
                address: constants.MONGO_DB_CONN,
                options: { useNewUrlParser: true }
            },
        }, err => {
            if (err) {
                done(err);
            }
            MongoClient.connect(constants.MONGO_DB_CONN, { useNewUrlParser: true },
                async (err, client) => {
                    if (err) {

                        done(err);
                    }
                    mongoClient = client;
                    mongoDb = client.db(process.env.MONGO_DB);
                    await delay(50);
                    done();
                });
        });
        agenda.define("CDK-EXTRACT", (job, done) => {
            console.log("CDK-EXTRACT Job running");
        });
    });

    afterEach(async () => {
        await clearJobs();
        if (agenda != null) {
            await agenda.stop();
        }
        if (mongoClient != null) {
            await mongoClient.close();
        }
        if (agenda._db != null) {
            await agenda._db.close();
        }

    });
    describe("Create a schedule", () => {
        var jobInput = {
            jobName: "CDK_EXTRACT",
            jobSchedule: moment().utc(),
            jobData: {
                groupName: "G1",
                storeDataArray: [
                    {
                        dealerId: "s1",
                        startDate: "09/25/2018",
                        endDate: "09/25/2018",
                        CloseROOption: "ALL"
                    },
                    {
                        dealerId: "s2",
                        startDate: "09/25/2018",
                        endDate: "09/25/2018",
                        CloseROOption: "ALL"
                    }
                ]
            }
        };
        it("with multiple store in same group", async () => {
            var response = await CDKJobManager.scheduleCDKExtractJob(jobInput);
            expect(response.status).toEqual(true);
            expect(response.job.data.groupName).toEqual("G1");
            expect(response.job.data.storeDataArray[0].dealerId).toEqual("s1");
            expect(response.job.data.storeDataArray[0].startDate).toEqual("09/25/2018");
            expect(response.job.data.storeDataArray[0].endDate).toEqual("09/25/2018");
            expect(response.job.data.storeDataArray[1].dealerId).toEqual("s2");
            expect(response.job.data.storeDataArray[1].startDate).toEqual("09/25/2018");
            expect(response.job.data.storeDataArray[1].endDate).toEqual("09/25/2018");
            expect(response.job.data.storeDataArray.length).toEqual(2);
        });
    });
    describe("Update an existing Schedule", () => {
        it("with a new store", async () => {
            var jobInput = {
                jobName: "CDK_EXTRACT",
                jobSchedule: moment().utc(),
                jobData: {
                    groupName: "G2",
                    storeDataArray: [
                        {
                            dealerId: "s1",
                            startDate: "10/25/2019",
                            endDate: "09/25/2018",
                            CloseROOption: "ALL"
                        },
                        {
                            dealerId: "s2",
                            startDate: "09/25/2018",
                            endDate: "09/25/2018",
                            CloseROOption: "ALL"
                        }
                    ]
                }
            };
            var jobInputToUpdate = {
                jobName: "CDK_EXTRACT",
                jobSchedule: moment().utc(),
                jobData: {
                    groupName: "G2",
                    storeDataArray: [
                        {
                            dealerId: "s3",
                            startDate: "25-09-2015",
                            endDate: "09/25/2018",
                            CloseROOption: "ALL"
                        }
                    ]
                }
            };

            var response = await CDKJobManager.scheduleCDKExtractJob(jobInput);
            expect(response.job.data.groupName).toEqual("G2");
            expect(response.job.data.storeDataArray.length).toEqual(2);
            expect(new Date(response.job.nextRunAt)).toEqual(new Date(util.toDate(moment().utc())));
            var response2 = await CDKJobManager.scheduleCDKExtractJob(jobInputToUpdate);
            expect(response2.job.data.groupName).toEqual("G2");
            expect(response2.job.data.storeDataArray.length).toEqual(3);
            var newStoreArray = jobInputToUpdate.jobData.storeDataArray;
            expect(response2.job.data.storeDataArray.includes(newStoreArray[0])).toEqual(true); // Added to already existing schedule
        });
        it("with a store that already exist", async () => {
            var jobInput = {
                jobName: "CDK_EXTRACT",
                jobSchedule: moment().utc(),
                jobData: {
                    groupName: "G3",
                    storeDataArray: [
                        {
                            dealerId: "s1",
                            startDate: "09/25/2018",
                            endDate: "09/25/2018",
                            CloseROOption: "ALL"
                        }
                    ]
                }
            };
            var jobInputToUpdate = {
                jobName: "CDK_EXTRACT",
                jobSchedule: moment().utc(),
                jobData: {
                    groupName: "G3",
                    storeDataArray: [
                        {
                            dealerId: "s1",
                            startDate: "09/25/2010",
                            endDate: "10/25/2010",
                            CloseROOption: "ALL"
                        }
                    ]
                }
            };
            var response = await CDKJobManager.scheduleCDKExtractJob(jobInput);
            expect(response.job.data.groupName).toEqual("G3");
            expect(response.job.data.storeDataArray.length).toEqual(1);
            expect(new Date(response.job.nextRunAt)).toEqual(new Date(util.toDate(moment().utc())));
            var response2 = await CDKJobManager.scheduleCDKExtractJob(jobInputToUpdate);
            expect(response2.job.data.groupName).toEqual("G3");
            expect(response2.job.data.storeDataArray.length).toEqual(1);
            var newStoreArray = jobInputToUpdate.jobData.storeDataArray;
            expect(response.job.data.storeDataArray.includes(newStoreArray[0])).toEqual(false); // Added to already existing schedule but updated the store details
        });
    });
});

BEGIN;

SELECT set_config('client_min_messages', 'WARNING', FALS<PERSON>);
DROP SCHEMA IF EXISTS process_data CASCADE;
CREATE SCHEMA process_data;

CREATE TABLE process_data.schedule_detail
(
    scheduler_id    text PRIMARY KEY,
    total_ros       bigint,
    created_at      timestamptz DEFAULT current_timestamp(3),
    created_by      text
);

CREATE TABLE process_data.schedule_exceptions
(
    exception_id    bigserial NOT NULL PRIMARY KEY,
    scheduler_id    text,
    exception_key   text,
    ro_number       text,
    additional_info json NULL,
    created_at      timestamptz DEFAULT current_timestamp(3),
    created_by      text
);
CREATE INDEX idx_scheduler_id ON process_data.schedule_exceptions (scheduler_id);
CREATE INDEX idx_exception_key ON process_data.schedule_exceptions (exception_key);

CREATE TABLE process_data.exception_review
(
    project_id      bigint NOT NULL,
    scheduler_id    text NOT NULL,
    exception_key   text NOT NULL,
    is_reviewed     bool NULL DEFAULT FALSE,
    reviewed_by     text,
    reviewed_on     timestamptz DEFAULT current_timestamp(3),
    PRIMARY KEY (project_id, scheduler_id, exception_key)
);

CREATE OR REPLACE FUNCTION process_data.get_scheduler_exception_details
    (
        in_scheduler_id text,
        in_project_id   bigint
    )
RETURNS json
LANGUAGE sql
STABLE SECURITY DEFINER
AS $function$
    SELECT json_agg(row_to_json(data))
    FROM(
        SELECT
            se.exception_key,
            em.exception_name                                                   AS schedule_exceptions,
            em.exception_description                                            AS schedule_description,
            sd.total_ros                                                        AS total_ros,
            COUNT(se.ro_number)                                                 AS exception_ro_count,
            array_agg(se.ro_number)                                             AS exception_ro_list,
            json_agg((se.additional_info::jsonb ||
                jsonb_build_object('exception_ro_number', se.ro_number))::json) AS exception_ro_info,
            COALESCE(er.is_reviewed, FALSE)                                     AS is_reviewed
        FROM process_data.schedule_exceptions se
            LEFT JOIN process_data.schedule_detail sd USING(scheduler_id)
            LEFT JOIN core.exception_master em
                ON em.exception_key = se.exception_key
            LEFT JOIN process_data.exception_review er
                ON se.scheduler_id = er.scheduler_id
                    AND er.project_id  = in_project_id
                    AND se.exception_key = er.exception_key
        WHERE se.scheduler_id = in_scheduler_id
        GROUP BY se.exception_key, em.exception_name, em.exception_description, sd.total_ros,er.is_reviewed) AS data;
$function$;

CREATE OR REPLACE FUNCTION process_data.update_schedule_exception_review_status
    (
        in_scheduler_id    text,
        in_project_id      bigint,
        in_exception_data  json,
        in_is_reviewed_by  text
    )
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
    status json;
BEGIN
    WITH exception_data AS (
        SELECT
            exception_list.*,
            er.exception_key IS NOT NULL AS already_exist
        FROM json_to_recordset(in_exception_data)
                  AS exception_list (exception_key text,
                                     is_reviewed    boolean)
            LEFT JOIN process_data.exception_review er
                ON er.project_id = in_project_id
                    AND er.scheduler_id = in_scheduler_id
                    AND er.exception_key = exception_list.exception_key
    )
    , insert_exception AS (
        INSERT INTO process_data.exception_review (project_id, scheduler_id, exception_key, is_reviewed, reviewed_by, reviewed_on)
        SELECT in_project_id, in_scheduler_id, ed.exception_key, ed.is_reviewed, in_is_reviewed_by, current_timestamp
        FROM exception_data ed
        WHERE NOT ed.already_exist
    )
    UPDATE process_data.exception_review er
    SET is_reviewed = ed.is_reviewed,
        reviewed_by = in_is_reviewed_by,
        reviewed_on = current_timestamp
    FROM exception_data ed
    WHERE ed.already_exist
        AND er.scheduler_id = in_scheduler_id
        AND er.project_id = in_project_id
        AND er.exception_key = ed.exception_key;

    RETURN json_build_object('status', 'success');
END;
$function$;

COMMIT;



GRANT USAGE ON 	SCHEMA analytics TO adudigitalservice;
<PERSON>RA<PERSON> SELECT, INSERT ON analytics.activity_log TO adudigitalservice;
GRANT USAGE, SELECT ON SEQUENCE analytics.activity_log_id_seq TO adudigitalservice;

GRANT USAGE ON SCHEMA process_data TO adudigitalservice;
GRANT USAGE ON SCHEMA core TO adudigitalservice;

REVOKE EXECUTE ON FUNCTION process_data.get_scheduler_exception_details(text, bigint) FROM PUBLIC;
GRANT EXECUTE ON FUNCTION process_data.get_scheduler_exception_details(text, bigint) TO adudigitalservice;

REVOKE EXECUTE ON FUNCTION process_data.update_schedule_exception_review_status(text, bigint, json, text) FROM PUBLIC;
GRANT EXECUTE ON FUNCTION process_data.update_schedule_exception_review_status(text, bigint, json, text) TO adudigitalservice;

#!/usr/bin/env bash
SCRIPT_DIR="$(cd $(dirname $0) && pwd)"
cd "$SCRIPT_DIR"

ACTIVITY_MODEL_DB_SCRIPT='./handlers/scheduler_activity_log.psql'
CORE_SCRIPT='./handlers/core_model.psql'
PROCESS_SCRIPT='./handlers/process_data_model.psql'
PERMISSION_SCRIPT='./handlers/role-adudigitalservice.psql'

function die() {
    txt_red='\e[1;31m'
    txt_reset='\e[0m'
    echo -e "${txt_red}$@${txt_reset}"
    exit 1
}

function psql_schedulerdb() {
    psql --quiet "service=du_scheduler" --set=ON_ERROR_STOP=1 "$@"
} > /dev/null

psql_schedulerdb --quiet --file "${ACTIVITY_MODEL_DB_SCRIPT}"  > /dev/null || die "Activity model Failed"
psql_schedulerdb --quiet --file "${CORE_FUNCTION_SCRIPT}"  > /dev/null || die "Core module Failed"
psql_schedulerdb --quiet --file "${PROCESS_SCRIPT}"  > /dev/null || die "Process data module Failed"
psql_schedulerdb --quiet --file "${PERMISSION_SCRIPT}"  > /dev/null || die "Permission Failed"

echo "DU Scheduler Database Installation Completed"

exit 0

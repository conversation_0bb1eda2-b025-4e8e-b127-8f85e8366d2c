const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeUcsProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "ucs-process-json") {
      initializeUcsProcessJSON = true
    }
    if (type.split('-')[0] == 'ucs') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the ucs-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeUcsProcessJSON) {
    try {
      // Initialize Ucs Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("Ucs :  Process JSON schedule started");
      segment.saveSegment("Ucs :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("Ucs Extraction Processing Not Enabled - Pass Job Type ucs-process-json To Enable");
    segment.saveSegment("Ucs Extraction Processing Not Enabled - Pass Job Type ucs-process-json To Enable");
  }
  return true;
}

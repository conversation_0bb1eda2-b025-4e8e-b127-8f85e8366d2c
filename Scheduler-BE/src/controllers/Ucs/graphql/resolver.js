const UcsJobManager = require("../UcsJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all Ucs-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllUcsExtractJobs(_, args) {
      return UcsJobManager.getAllUcsExtractJobs();
    },

    /**
     * Query to get all Automate Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllUcsProcessJSONJobs(_, args) {
      return UcsJobManager.getAllUcsProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule Ucs-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleUcsExtractJob(_, args) {
      return UcsJobManager.scheduleUcsExtractJob(args.input);
    },

    /**
     * Mutation to run a Ucs-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowUcsExtractJobByStore(_, args) {
      return UcsJobManager.runNowUcsExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a Ucs-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelUcsExtractJobByStore(_, args) {
      return UcsJobManager.cancelUcsExtractJobByStore(args.input);
    },
  },
  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;

var express = require("express");
var api = express.Router();
var bodyParser = require("body-parser");
var { graphqlExpress, graphiqlExpress } = require("graphql-server-express");


const CDKSchema = require("./CDK/graphql/schema");
const CDKFLEXSchema = require("../controllers/CDKFLEX/graphql/schema");
const AutoMateSchema = require("./AutoMate/graphql/schema");
const DealerBuiltSchema = require("./DealerBuilt/graphql/schema");
const DealerTrackSchema = require("./DealerTrack/graphql/schema");
const AdamSchema = require("./Adam/graphql/schema");
const ReynoldsSchema =  require("./Reynolds/graphql/schema"); 
const DominionSchema = require("./Dominion/graphql/schema");
const AutosoftSchema =  require("./AutoSoft/graphql/schema"); 
const PbsSchema =  require("./Pbs/graphql/schema"); 
const QuorumSchema =  require("./Quorum/graphql/schema"); 
const UcsSchema =  require("./Ucs/graphql/schema"); 
const MpkSchema =  require("./Mpk/graphql/schema"); 
const TekionSchema =  require("./Tekion/graphql/schema"); 
const TekionApiSchema = require("./TekionApi/graphql/schema");
const FortellisSchema = require("./Fortellis/graphql/schema");
const scheduleFieldSchema =  require("./scheduleFields/graphql/schema"); 


//For CDK3PA
api.use("/cdk3pa/graphql", bodyParser.json(), graphqlExpress({ schema: CDKSchema }));
api.use("/cdk3pa/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/cdk3pa/graphql" }));

//For CDKFLEX
api.use("/cdkflex/graphql", bodyParser.json(), graphqlExpress({ schema: CDKFLEXSchema }));
api.use("/cdkflex/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/cdkflex/graphql" }));

//For Automate
api.use("/automate/graphql", bodyParser.json(), graphqlExpress({ schema: AutoMateSchema }));
api.use("/automate/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/automate/graphql" }));

//For Dealerbuilt
api.use("/dealerbuilt/graphql", bodyParser.json(), graphqlExpress({ schema: DealerBuiltSchema }));
api.use("/dealerbuilt/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/dealerbuilt/graphql" }));

//For DealerTrack
api.use("/dealertrack/graphql", bodyParser.json(), graphqlExpress({ schema: DealerTrackSchema }));
api.use("/dealertrack/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/dealertrack/graphql" }));
module.exports = api;

//For Adam
api.use("/adam/graphql", bodyParser.json(), graphqlExpress({ schema: AdamSchema }));
api.use("/adam/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/adam/graphql" }));


//For Reynolds
api.use("/reynolds/graphql", bodyParser.json(), graphqlExpress({ schema: ReynoldsSchema }));
api.use("/reynolds/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/reynolds/graphql" }));

//For Dominion
api.use("/dominion/graphql", bodyParser.json(), graphqlExpress({ schema: DominionSchema }));
api.use("/dominion/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/dominion/graphql" }));

//For Autosoft
api.use("/autosoft/graphql", bodyParser.json(), graphqlExpress({ schema: AutosoftSchema }));
api.use("/autosoft/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/autosoft/graphql" }));

//For Pbs
api.use("/pbs/graphql", bodyParser.json(), graphqlExpress({ schema: PbsSchema }));
api.use("/pbs/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/pbs/graphql" }));


//For Quorum
api.use("/quorum/graphql", bodyParser.json(), graphqlExpress({ schema: QuorumSchema }));
api.use("/quorum/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/quorum/graphql" }));


//For Mpk
api.use("/mpk/graphql", bodyParser.json(), graphqlExpress({ schema: MpkSchema }));
api.use("/mpk/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/mpk/graphql" }));

//For Tekion
api.use("/tekion/graphql", bodyParser.json(), graphqlExpress({ schema: TekionSchema }));
api.use("/tekion/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/tekion/graphql" }));

//For TekionApi
api.use("/tekionapi/graphql", bodyParser.json(), graphqlExpress({ schema: TekionApiSchema }));
api.use("/tekionapi/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/tekionapi/graphql" }));

//For Fortellis
api.use("/fortellis/graphql", bodyParser.json(), graphqlExpress({ schema: FortellisSchema }));
api.use("/fortellis/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/fortellis/graphql" }));

//For Ucs
api.use("/ucs/graphql", bodyParser.json(), graphqlExpress({ schema: UcsSchema }));
api.use("/ucs/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/ucs/graphql" }));

api.use("/scheduleFields/graphql", bodyParser.json(), graphqlExpress({ schema: scheduleFieldSchema }));
api.use("/scheduleFields/graphiql", graphiqlExpress({ endpointURL: "/scheduler/api/scheduleFields/graphql" }));
module.exports = api;

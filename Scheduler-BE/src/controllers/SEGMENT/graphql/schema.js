var gt = require("graphql-tools");
var resolvers = require("./resolver.js");

var typeDefs = `
# Represents the Status of each Mutations
type Status {
  status: Boolean!
  message: String!
}

input createActivityLogInput{
  content: String!
}

# This type specifies the entry points into our API

type Query {
    dummy: String
}

# Mutations
type Mutation {
  # Mutation to save Segment details
  createActivityLog (input: createActivityLogInput!): Status,
}

schema {
  query: Query,
  mutation: Mutation
}
`;

var schema = gt.makeExecutableSchema({ typeDefs, resolvers });
module.exports = schema;

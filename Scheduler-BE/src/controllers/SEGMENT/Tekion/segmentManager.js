var fs = require('fs');
const moment = require("moment-timezone");
/**
 * Function to Save segment details to file
 * @param {*} segmentData
 */
async function saveSegment(segmentData) {
    try {
        var currentPath = process.cwd() + '/logs/Tekion/segment-tekion-' + moment().format('YYYY-MM-DD') + '.log';
        var infoStream = fs.createWriteStream(currentPath, { flags: 'a+' });
        var message = moment().toString() + ' : ' + segmentData + '\n';
        var respMessage = 'Success';
        try {
            infoStream.write(message);
        } catch (error) {
            respMessage = 'Failure';
        }
        return { status: true, message: respMessage };
    } catch (error) {
        respMessage = 'Failure';
        return { status: false, message: respMessage };
    }
}

async function saveSegmentFailure(segmentData,storeCode) {

    let failureDirectory = process.cwd() + '/logs/Tekion/failure/';
    try {
        if (!fs.existsSync(failureDirectory)){
          fs.mkdirSync(failureDirectory)
        }
      } catch (err) {
        console.error(err)
      }
    try {
        var currentPath = failureDirectory + storeCode + '.log';
       
        var infoStream = fs.createWriteStream(currentPath, { flags: 'a+' });
        var message = moment().toString() + ' : ' + segmentData + '\n';
        var respMessage = 'Success';
        try {
            infoStream.write(message);
        } catch (error) {
            respMessage = 'Failure';
        }
        return { status: true, message: respMessage };
    } catch (error) {
        respMessage = 'Failure';
        return { status: false, message: respMessage };
    }  
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

module.exports = {
    saveSegment,
    saveSegmentFailure,
    sleep
};

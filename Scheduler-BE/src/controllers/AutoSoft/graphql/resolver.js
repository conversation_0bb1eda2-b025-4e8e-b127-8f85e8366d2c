const AutosoftJobManager = require("../AutosoftJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all Autosoft-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllAutosoftExtractJobs(_, args) {
      return AutosoftJobManager.getAllAutosoftExtractJobs();
    },

    /**
     * Query to get all Automate Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllAutosoftProcessJSONJobs(_, args) {
      return AutosoftJobManager.getAllAutosoftProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule Autosoft-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleAutosoftExtractJob(_, args) {
      return AutosoftJobManager.scheduleAutosoftExtractJob(args.input);
    },

    /**
     * Mutation to run a Autosoft-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowAutosoftExtractJobByStore(_, args) {
      return AutosoftJobManager.runNowAutosoftExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a Autosoft-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelAutosoftExtractJobByStore(_, args) {
      return AutosoftJobManager.cancelAutosoftExtractJobByStore(args.input);
    },
  },
  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;

const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeAutosoftProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "autosoft-process-json") {
      initializeAutosoftProcessJSON = true
    }
    if (type.split('-')[0] == 'autosoft') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the autosoft-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeAutosoftProcessJSON) {
    try {
      // Initialize Autosoft Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("Autosoft :  Process JSON schedule started");
      segment.saveSegment("Autosoft :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("Autosoft Extraction Processing Not Enabled - Pass Job Type autosoft-process-json To Enable");
    segment.saveSegment("Autosoft Extraction Processing Not Enabled - Pass Job Type autosoft-process-json To Enable");
  }
  return true;
}

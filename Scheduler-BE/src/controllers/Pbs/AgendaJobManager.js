const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializePbsProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "pbs-process-json") {
      initializePbsProcessJSON = true
    }
    if (type.split('-')[0] == 'pbs') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the pbs-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializePbsProcessJSON) {
    try {
      // Initialize Pbs Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("Pbs :  Process JSON schedule started");
      segment.saveSegment("Pbs :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("Pbs Extraction Processing Not Enabled - Pass Job Type pbs-process-json To Enable");
    segment.saveSegment("Pbs Extraction Processing Not Enabled - Pass Job Type pbs-process-json To Enable");
  }
  return true;
}

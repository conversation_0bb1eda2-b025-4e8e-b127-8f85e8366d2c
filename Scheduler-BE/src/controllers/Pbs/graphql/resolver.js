const PbsJobManager = require("../PbsJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all Pbs-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllPbsExtractJobs(_, args) {
      return PbsJobManager.getAllPbsExtractJobs();
    },

    /**
     * Query to get all Automate Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllPbsProcessJSONJobs(_, args) {
      return PbsJobManager.getAllPbsProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule Pbs-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    schedulePbsExtractJob(_, args) {
      return PbsJobManager.schedulePbsExtractJob(args.input);
    },

    /**
     * Mutation to run a Pbs-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowPbsExtractJobByStore(_, args) {
      return PbsJobManager.runNowPbsExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a Pbs-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelPbsExtractJobByStore(_, args) {
      return PbsJobManager.cancelPbsExtractJobByStore(args.input);
    },
  },
  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;

const constantsCommon = require("../../common/constants");
const segment = require("../SEGMENT/segmentManager");
const { logger } = require("../../logger/schedulerLogger");
const rp = require("request-promise");

async function getScheduleFieldDataByStoreId(companyId, res) {
  console.log(companyId)
  if (!companyId || companyId == 0) {
    console.error("Company ID is missing or empty:", companyId);
    segment.saveSegment(`getScheduleFieldDataByDealerId: ${companyId}`);
    return {
      status: 400,
      message: "Invalid companyId: It must be a non-zero, non-null value.",
    };
  }
  try {
    const responseData = await getScheduleFieldDataByCompanyId(companyId);
    if (responseData.status === 200) {
      return responseData;
    } else {
      return {
        status: responseData.status,
        message: responseData.message,
        data: null,
      };
    }
  } catch (error) {
    console.error(
      "Error retrieving schedule fields for companyId:",
      companyId,
      error
    );
    return { status: 500, message: "Error retrieving schedule fields" };
  }
}
async function getScheduleFieldDataByCompanyId(companyId) {
  segment.saveSegment(`getScheduleFieldDataByDealerId: ${companyId}`);
  const query = `
  query ($companyId: BigInt!) {
    allStoreCredentials(condition: { companyId: $companyId }) {
      edges {
        node {
          companyId
          thirdPartyUserName
          enterpriseSourceId
          serverStore
          branch
          cancelledDate
          mageGroupName
          mageGroupCode
          mageStoreName
          billingCode
          mageStoreCode
          isFopcStore 
        }
      }
    }
  }
`;
const options = {
  method: "POST",
  uri: constantsCommon.conf.GRAPHQL_SCHEDULER_URI,
  body: {
    query: query,
    variables: {
      companyId: companyId, // this should be a Number or BigInt-compatible string
    },
  },
  headers: {
    "Content-Type": "application/json",
  },
  json: true,
};
  try {
    const response = await rp(options);
    const data = response.data.allStoreCredentials.edges.map(
      (edge) => edge.node
    );
    segment.saveSegment(`Manage DMS Fields : Response From allStoreCredentials ${JSON.stringify(data)}`);

    console.log(data);
    return {
      status: 200,
      message: "success",
      storeCredentials: data,
    };
  } catch (error) {
    console.error("Error retrieving schedule fields:", error);
    return {
      status: 500,
      message: "Error retrieving schedule fields",
    };
  }
}
async function getGroups() {
  const GRAPHQL_DUPORTAL_URI = constantsCommon.conf.GRAPHQL_DUPORTAL_URI
  segment.saveSegment(`getGroups`);  
  // Update the query to use GraphQL variables
  const query = `
  query allSolve360StoreGroups {
  allSolve360StoreGroups(
    condition: { isInPortal: true, tagGroupRegion: false }
  ) {
    edges {
      node {
        companyId
        companyName
        brandList
        state
        sgId
        isInPortal
        tagStoreGroup
        tagGroupRegion
        tagSoloStore
        tagCustomer
        sp
        salesPerson
        spEmail
        spPhone
        createdBy
        createdAt
        updatedBy
        updatedAt
      }
    }
  }
}`;

  // Pass the companyId as a variable to the query
  const options = {
    method: "POST",
    uri: GRAPHQL_DUPORTAL_URI,
    body: {
      query: query      
    },
    headers: { "Content-Type": "application/json" },
    json: true, // Automatically stringifies the body to JSON
  };

  try {
    const response = await rp(options);
    console.log(response);
    const data = response.data.allSolve360StoreGroups.edges.map(edge => edge.node);

    console.log(data);
    return {
      status: 200,
      message: 'success',
      data: data, // Directly return the data array
    };
  } catch (error) {
    console.error("Error retrieving schedule fields:", error);
    return {
      status: 500,
      message: "Error retrieving schedule fields",
    };
  }
}
async function getStoreById(companyId) {
  const GRAPHQL_DUPORTAL_URI = constantsCommon.conf.GRAPHQL_DUPORTAL_URI
  segment.saveSegment(`getScheduleFieldDataByDealerId: ${companyId}`);
  
  // Update the query to use GraphQL variables
  const query = `
  query getStoresForStoreGroup($companyId: String!) {
    getStoresForStoreGroup(inSgId: $companyId) {
      edges {
        node {
          stId
          sgId
          storeName
          manufacturer
          state
          companyId
          isInPortal
          isInSolve
          mgIdList
          sp
          contact
          salesPerson
          dms
          brandCode
          stateName
          createdBy
          createdAt
          updatedBy
          updatedAt
          dealerCode
          marketingSource
          tagCustomer
        }
      }
    }
  }`;

  // Pass the companyId as a variable to the query
  const options = {
    method: "POST",
    uri: GRAPHQL_DUPORTAL_URI,
    body: {
      query: query,
      variables: {
        companyId: companyId, // Pass the companyId here
      },
    },
    headers: { "Content-Type": "application/json" },
    json: true, // Automatically stringifies the body to JSON
  };

  try {
    const response = await rp(options);
    const data = response.data.getStoresForStoreGroup.edges.map(
      (edge) => edge.node
    );
    console.log(data);
    return {
      status: 200,
      message: "success",
      storeCredentials: data,
    };
  } catch (error) {
    console.error("Error retrieving schedule fields:", error);
    return {
      status: 500,
      message: "Error retrieving schedule fields",
    };
  }
}
async function addScheduleFields(UserInput) {
 return new Promise(async (resolve, reject) => {
  const {
   inCompanyId,
   inThirdPartyUserName,
   inEnterpriseSourceId,
   inServerStore,
   inBranch,
   inCancelledDate,
   inMageStoreCode,
   inBillingCode,
   inMageStoreName,
   inMageGroupCode,
   inMageGroupName,
   inUpdatedBy,
  } = UserInput;
  const cancelledDateTimestamp = new Date(inCancelledDate).toISOString(); // Ensure the date is in the correct format
  const insertStoreCredentialMutation = buildInsertStoreCredentialMutation(
   inCompanyId,
   inThirdPartyUserName,
   inEnterpriseSourceId,
   inServerStore,
   inBranch,
   cancelledDateTimestamp,
   inMageStoreCode,
   inBillingCode,
   inMageStoreName,
   inMageGroupCode,
   inMageGroupName,
   inUpdatedBy // Assuming you have inUpdatedBy available
  );
  // Call the GraphQL mutation
  const schedulerImportOptions = getOptions(
   constantsCommon.conf.GRAPHQL_SCHEDULER_URI,
   insertStoreCredentialMutation
  );

  console.log("Scheduler Import Options:", schedulerImportOptions);
  rp(schedulerImportOptions)
   .then(async (response) => {
    console.log(response);
    if (!response) {
     logger.error(`No response data from GraphQL query`);
     resolve({
      status: "failed",
      message: `No response from GraphQL query: ${response}`,
     });
    }
    console.log("Response from GraphQL:", JSON.parse(response));

    try {
     const responseData = JSON.parse(response);
     // Handle responseData as needed
     resolve(responseData);
    } catch (error) {
     logger.error("Error parsing response:", error);
     resolve({
      status: "error",
      message: "Error processing the response",
     });
    }
   })
   .catch((error) => {
    logger.error("Error executing GraphQL mutation:", error);
    resolve({
     status: "error",
     message: "Error executing mutation",
    });
   });
 });
}
function getOptions(URI, query) {
 return {
  method: "POST",
  uri: URI,
  body: JSON.stringify({ query: query }),
  headers: {
   "Content-Type": "application/json",
  },
 };
}
// Function to build the GraphQL mutation
function buildInsertStoreCredentialMutation(
 companyId,
 thirdPartyUserName,
 enterpriseSourceId,
 serverStore,
 branch,
 cancelledDate,
 mageStoreCode,
 billingCode,
 mageStoreName,
 mageGroupCode,
 mageGroupName,
 updatedBy
) {
 return `mutation {
    insertStoreCredentialDetails(input: {
      inCompanyId: "${companyId}",
      inThirdPartyUserName: "${thirdPartyUserName}",
      inEnterpriseSourceId: "${enterpriseSourceId}",
      inServerStore: "${serverStore}",
      inBranch: "${branch}",
      inCancelledDate: "${cancelledDate}",
      inMageStoreCode: "${mageStoreCode}",
      inBillingCode: "${billingCode}",
      inMageStoreName: "${mageStoreName}",
      inMageGroupCode: "${mageGroupCode}",
      inMageGroupName: "${mageGroupName}",
      inUpdatedBy: "${updatedBy}"
    }) {
      json
    }
  }`;
}
module.exports = {
 getScheduleFieldDataByStoreId,
 addScheduleFields,
 getGroups,
 getStoreById
};

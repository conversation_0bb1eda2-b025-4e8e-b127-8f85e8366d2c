const ScheduleManager = require("../scheduleManager");
const validator = require("validator");

var resolvers = {
 Query: {
  /**
   * Query to get schedule field data by dealerId.
   * @param {object} _ GraphQL root object
   * @param {object} args User arguments, including dealerId
   * @returns {Promise<ScheduleFields>} The schedule field data for the specified dealerId
   */
  async getScheduleFieldDataByStoreId(_, args) {
   return ScheduleManager.getScheduleFieldDataByStoreId(args.companyId);
  },
  async getStoresForStoreGroup(_, args) {
    return await ScheduleManager.getStoreById(args.companyId);  // Call the function from ScheduleManager with companyId as argument
  },
  
  // Resolver for getGroups
  async allSolve360StoreGroups(_, args) {
    return await ScheduleManager.getGroups();  // Call the function from ScheduleManager
  },
 },
 Mutation: {
  /**
   * Mutation to add a new schedule field.
   * @param {object} _ GraphQL root object
   * @param {object} args User arguments, including input data for the new schedule field
   * @returns {Promise<Status>} The status of the add operation
   */
  addScheduleFields(_, args) {
   console.log("heeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee");
   return ScheduleManager.addScheduleFields(args.input);
  }
 },
 Date: {
  __serialize(value) {
   return value;
  },

  __parseValue(value) {
   var parts = value.split("-");
   return [parts[0], parts[1], parts[2]].join("/");
  },
  __parseLiteral(ast) {
   var dateStr = JSON.parse(JSON.stringify(ast)).value;
   var parts = dateStr.split("-");
   return [parts[1], parts[0], parts[2]].join("/");
  },
 },

 DateTime: {
  __serialize(value) {
   if (validator.isISO8601(value)) {
    return value;
   }
   throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
  },
  __parseValue(value) {
   if (validator.isISO8601(value)) {
    return value;
   }
   throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
  },
  __parseLiteral(ast) {
   if (validator.isISO8601(ast.value)) {
    return ast.value;
   }
   throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
  },
 },
};

module.exports = resolvers;

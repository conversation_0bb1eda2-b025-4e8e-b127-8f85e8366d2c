const gt = require("graphql-tools");
const resolvers = require("./resolver");

const typeDefs = `
# Scalar types
scalar Date
scalar DateTime
scalar _bsontype
scalar BigInt

# Input type for store items
input StoreItemInput {
  id: String
  itemName: String
}

# Input type for schedule fields
input ScheduleFieldsInput {
  inCompanyId: BigInt
  inThirdPartyUserName: String
  inEnterpriseSourceId: String
  inServerStore: String
  inBranch: String
  inCancelledDate: String
  inMageStoreCode: String
  inBillingCode: String
  inMageStoreName: String
  inMageGroupCode: String
  inMageGroupName: String
  inUpdatedBy: String
}

# Type for schedule fields
type ScheduleFields {
  companyId: BigInt
  enterpriseSourceId: String
  serverStore: String
  branch: String
  cancelledDate: String
  mageGroupName: String
  mageGroupCode: String
  mageStoreName: String
  mageStoreCode: String
  billingCode: String
  isFopcStore: Boolean
  thirdPartyUserName: String
}

# Response type for schedule fields query
type ScheduleFieldsResponse {
  status: String
  message: String
  storeCredentials: [ScheduleFields]
}

# Represents the status of each mutation
type Status {
  status: Boolean!
  message: String!
}

# Query type
type Query {
  # Query to get schedule field data by store ID
  getScheduleFieldDataByStoreId(companyId: BigInt!): ScheduleFieldsResponse 
  getStoresForStoreGroup(inSgId: String!): StoreConnection
  allSolve360StoreGroups(condition: StoreGroupCondition): StoreGroupConnection
}

# Mutation type
type Mutation {
  # Mutation to add a schedule field
  addScheduleFields(input: ScheduleFieldsInput!): Status 
}

# Store type definition
type Store {
  stId: String
  sgId: String
  storeName: String
  manufacturer: String
  state: String
  companyId: String
  isInPortal: Boolean
  isInSolve: Boolean
  mgIdList: [String]
  sp: String
  contact: String
  salesPerson: String
  dms: String
  brandCode: String
  stateName: String
  createdBy: String
  createdAt: String
  updatedBy: String
  updatedAt: String
  dealerCode: String
  marketingSource: String
  tagCustomer: String
}

# Edge type for Store
type StoreEdge {
  node: Store
}

# Connection type for Store
type StoreConnection {
  edges: [StoreEdge]
}

# StoreGroup type definition
type StoreGroup {
  companyId: String
  companyName: String
  brandList: [String]
  state: String
  sgId: String
  isInPortal: Boolean
  tagStoreGroup: Boolean
  tagGroupRegion: Boolean
  tagSoloStore: Boolean
  tagCustomer: Boolean
  sp: String
  salesPerson: String
  spEmail: String
  spPhone: String
  createdBy: String
  createdAt: String
  updatedBy: String
  updatedAt: String
}

# Edge type for StoreGroup
type StoreGroupEdge {
  node: StoreGroup
}

# Connection type for StoreGroup
type StoreGroupConnection {
  edges: [StoreGroupEdge]
}

# Input type for store group condition
input StoreGroupCondition {
  isInPortal: Boolean
  tagGroupRegion: Boolean
}

# Root schema definition
schema {
  query: Query
  mutation: Mutation
}
`;

const schema = gt.makeExecutableSchema({ typeDefs, resolvers });
module.exports = schema;

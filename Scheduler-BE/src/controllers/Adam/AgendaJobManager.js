const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeAdamProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "adam-process-json") {
      initializeAdamProcessJSON = true
    }
    if (type.split('-')[0] == 'adam') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the adam-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeAdamProcessJSON) {
    try {
      // Initialize Adam Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("Adam :  Process JSON schedule started");
      segment.saveSegment("Adam :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("Adam Extraction Processing Not Enabled - Pass Job Type adam-process-json To Enable");
    segment.saveSegment("Adam Extraction Processing Not Enabled - Pass Job Type adam-process-json To Enable");
  }
  return true;
}

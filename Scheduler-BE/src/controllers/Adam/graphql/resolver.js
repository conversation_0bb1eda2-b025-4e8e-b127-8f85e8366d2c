const AdamJobManager = require("../AdamJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all Adam-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllAdamExtractJobs(_, args) {
      return AdamJobManager.getAllAdamExtractJobs();
    },

    /**
     * Query to get all Adam Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllAdamProcessJSONJobs(_, args) {
      return AdamJobManager.getAllAdamProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule Adam-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleAdamExtractJob(_, args) {
      return AdamJobManager.scheduleAdamExtractJob(args.input);
    },

    /**
     * Mutation to run a Adam-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowAdamExtractJobByStore(_, args) {
      return AdamJobManager.runNowAdamExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a Adam-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelAdamExtractJobByStore(_, args) {
      return AdamJobManager.cancelAdamExtractJobByStore(args.input);
    },
  },
  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;
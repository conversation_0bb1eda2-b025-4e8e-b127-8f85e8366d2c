var gt = require("graphql-tools");
var resolvers = require("./resolver");

var typeDefs = `

# This type specifies the entry points into our API

#Scalar types
scalar Date
scalar DateTime
scalar _bsontype

# Type of Adam extract Job
enum JobType {
  initial
  refresh
  ondemand
}

# close RO options
enum ClosedROOption {
  all
  monthly
  weekly
  current
}

input SingleStoreJobData{
  groupName: String!
  storeData: ExtractData!
}

type Data{
  groupName: String # Optional
  storeDataArray: [StoreData]
}

type QueueData{
  storeID: String,
  fileToProcess: String
  priority:String
}

type JSONProcessData {
  operation: String # Avoid operation = recheck or operation = start
  inputFile: String
  storeID: String
  outputFile: String
  status: Boolean
  message: String
  createdAt: String
}

type StoreData {
  storeCode:String!
  projectId: String
  secondProjectId: String
  mageManufacturer: String
  solve360Update: Boolean
  buildProxies: Boolean
  userName: String
  startDate: Date!
  endDate: Date!
  closedROOption: ClosedROOption
  startTime: String
  endTime:String
  status: Boolean
  message: String
  jobType: JobType
  mageGroupCode: String
  mageStoreCode: String
  stateCode: String
  dealerAddress: String
  projectIds:String
  secondProjectIdList:String
  companyIds:String
  testData:Boolean
  companyObj:String
  processFileName:String
}

type ExtractJob{
  timeFrameZone: String
  timeFrameStartTime: String
  timeFrameEndTime:String
  poolTime:Int
  jobArray: [Job]
}

type Job {
  name: String
  data:Data
  type: String
  priority: String
  nextRunAt: String
  _id: _bsontype
  lastModifiedBy: String
  lockedAt: String
  lastRunAt: String
  lastFinishedAt: String
  running: Boolean
  scheduled: Boolean
  queued: Boolean
  completed: Boolean
  failed: Boolean
  repeating: Boolean
  failReason: String
}

type ProcessJSONJob {
  name: String
  data:JSONProcessData
  type: String
  priority: String
  nextRunAt: String
  _id: _bsontype
  lastModifiedBy: String
  lockedAt: String
  lastRunAt: String
  lastFinishedAt: String
  running: Boolean
  scheduled: Boolean
  queued: Boolean
  completed: Boolean
  failed: Boolean
  repeating: Boolean
  failReason: String
  uploadStatus: Boolean
}

# Input to pass custom JSON data to a schedule
input JobData{
  groupName: String!
  storeDataArray: [ExtractData]!
}

input ExtractData {
  storeCode:String!
  projectId: String
  secondProjectId: String
  mageManufacturer: String
  solve360Update: Boolean
  buildProxies: Boolean
  userName: String
  startDate: Date!
  endDate: Date!
  closedROOption: ClosedROOption
  jobType: JobType
  mageGroupCode: String!
  mageStoreCode: String!
  stateCode: String!
  dealerAddress: String
  projectIds:String
  secondProjectIdList:String
  companyIds:String
  testData:Boolean
  companyObj:String
  projectType:String
  secondaryProjectType:String
  groupCode:String
  mageStoreName:String
}

input AdamScheduleInput{
  jobSchedule: DateTime!
  jobData: JobData!
}

input CancelAdamScheduleInput{
  jobSchedule: DateTime!
  jobData: SingleStoreJobData!
}

#Input to run a particular Adam Store's  extraction
input RunNowAdamScheduleInput {
  jobSchedule: DateTime!
  jobData: SingleStoreJobData!
}

# Represents the Status of each Mutations
type Status {
  status: Boolean!
  message: String!
  job: Job
}

type ProcessJSONInfo{
  processJSONJobsQueue: [QueueData],
  processJSONJobs: [ProcessJSONJob]
}

type Query {
  getAllAdamProcessJSONJobs: ProcessJSONInfo
  getAllAdamExtractJobs: ExtractJob
}

# Mutations
type Mutation {
  # Mutation to schedule a Adam extraction job
  scheduleAdamExtractJob (input: AdamScheduleInput!): Status,
  cancelAdamExtractJobByStore (input: CancelAdamScheduleInput!): Status,
  runNowAdamExtractJobByStore (input: RunNowAdamScheduleInput!): Status,
}

schema {
  query: Query,
  mutation: Mutation
}
`;
var schema = gt.makeExecutableSchema({ typeDefs, resolvers });
module.exports = schema;

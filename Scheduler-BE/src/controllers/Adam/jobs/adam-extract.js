"use strict";

const constants = require("../constants");
const util = require("../util");
const AdamJobManager = require("../AdamJobManager");
const { spawn } = require("child_process");
const moment = require("moment-timezone");
const fs = require("fs");
const segment = require("../../SEGMENT/Adam/segmentManager");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");
const { v4: uuidv4 } = require('uuid');
const SetProcessJobStatus = require('../../../model/setProcessJobStatus');
const manageScheduleField = require('../../../../src/common/util');
/**
 * Function to find the unique stores in an array of stores
 */
Array.prototype.unique = function () {
    var a = this.concat();
    for (var i = 0; i < a.length; ++i) {
        for (var j = i + 1; j < a.length; ++j) {
            if (a[i].enterpriseCode === a[j].enterpriseCode)
                a.splice(j--, 1);
        }
    }
    return a;
};

/**
 * Function to perform Adam-Extract
 */
module.exports = function AdamExtractJOB(agenda) {
    var storeCode = '';
    console.log(
        `Adam-Extract job started: JobName: ${constants.ADAM.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.ADAM.CONCURRENCY}`
    );
    segment.saveSegment(`Adam-Extract job started: JobName: ${constants.ADAM.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.ADAM.CONCURRENCY}`);
    agenda.define(constants.ADAM.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.ADAM.CONCURRENCY },
        async (job, done) => {
            const att = job.attrs.data;
            const storeDataArray = att.storeDataArray.reverse();
            var i = 0;
            var projectId, solve360Update, buildProxies, secondaryProjectId, projectType, secondaryProjectType, inLaborProjectType, inLaborProjectId, inPartsProjectType, inPartsProjectId;
            let userName;
            var warningObj = {};
            let processFileName;
            async function extract(att, job) {
                var errorWarnningMessage = '';
                storeCode = att.mageStoreCode;
                userName = att.userName;
                projectId = att.projectId;
                projectType = att.projectType || null;
                secondaryProjectType = att.secondaryProjectType || null;
                secondaryProjectId = att.secondProjectId;
                if (projectType && projectType.toLowerCase().startsWith("labor")) {
                    inLaborProjectType = projectType;
                    inLaborProjectId = projectId;
                } else if (secondaryProjectType && secondaryProjectType.toLowerCase().startsWith("labor")) {
                    inLaborProjectType = secondaryProjectType;
                    inLaborProjectId = secondaryProjectId;
                }

                // Check if projectType or secondaryProjectType starts with "parts"
                if (projectType && projectType.toLowerCase().startsWith("parts")) {
                    inPartsProjectType = projectType;
                    inPartsProjectId = projectId;
                } else if (secondaryProjectType && secondaryProjectType.toLowerCase().startsWith("parts")) {
                    inPartsProjectType = secondaryProjectType;
                    inPartsProjectId = secondaryProjectId;
                }
                warningObj.scheduled_by = userName;
                segment.saveSegment(`Adam: Extraction Job Started: ${JSON.stringify(att)}`);
                segment.saveSegmentFailure(`Adam: Extraction Job Started: ${JSON.stringify(att)}`, storeCode);
                if (att.storeCode && att.startDate && att.endDate) {
                    if (!fs.existsSync(constants.ADAM_DEADLETTER_DIR_PREFIX + '-extracted')) {
                        fs.mkdirSync(constants.ADAM_DEADLETTER_DIR_PREFIX + '-extracted');
                    }
                    var options = [
                        constants.ADAM.PULL_OP,
                        constants.ADAM.OPT_STORE_ID, att.storeCode,
                        constants.ADAM.OPT_START_DATE, att.startDate,
                        constants.ADAM.OPT_END_DATE, att.endDate,
                        constants.ADAM.OPT_DEADLETTER_DIR_PREFIX,
                        constants.ADAM_DEADLETTER_DIR_PREFIX + '-extracted',
                        constants.ADAM.OPT_MAGE_GROUP_CODE, att.mageGroupCode,
                        constants.ADAM.OPT_MAGE_STORE_CODE, att.mageStoreCode,
                        // constants.ADAM.OPT_DEALER_ADDRESS, att.dealerAddress,
                        '--stateCode', att.stateCode
                    ];

                    options.push(constants.ADAM.OPT_ZIP_PATH, att.zipPath ? att.zipPath : constants.ADAM_SCHEDULER_ETI_DIR);
                    options.push(constants.ADAM.OPT_ZAP_AFTER_ZIP);
                    options.push(constants.ADAM.OPT_BUNDLE);
                    options.push(att.jobType);
                    segment.saveSegment(`Adam: Extraction Job Started: ${JSON.stringify(att)}`);
                    segment.saveSegmentFailure(`Adam: Extraction Job Started: ${JSON.stringify(att)}`, storeCode);
                    //Mock Server
                    let isMockServer = constants.ADAM.ENV_MOCKSERVER;
                    if (isMockServer == "true") {
                        let groupCode = att.mageGroupCode;
                        let sourceFolder = constants.ADAM.MOCKSERVER_SOURCE_FOLDER_PATH + 'adam/';
                        let destinationFolder = constants.ADAM.MOCKSERVER_DESTINATION_FOLDER_PATH_ADAM;
                        //PremierAutoGroupCT-SUB_WAT-CT-INITIAL-12708-20220218062800.zip
                        const mageGroupCodeDIR = groupCode.replace(/ +/g, "");
                        const mageStorecodeDIR = storeCode.replace(/ +/g, "");
                        const stateCodeDIR = att.stateCode;
                        const jobTypeDIR = att.jobType;
                        const enterpriseCodeDir = att.storeCode;
                        const zipFileName = mageGroupCodeDIR + '-' + mageStorecodeDIR + '-' + stateCodeDIR + '-' + jobTypeDIR.toUpperCase(); +'-' + enterpriseCodeDir + '-';
                        console.log('-------zipFileName-------', zipFileName);

                        fs.readdir(sourceFolder, function (err, files) {
                            if (err) {
                                console.log('ADAM : Unable to scan directory');
                                segment.saveSegment(`ADAM : Unable to scan directory`, err);
                            }
                            const matchedResults = [];
                            let searchResult;
                            files.forEach(function (file) {
                                if (file.includes(zipFileName)) {
                                    matchedResults.push(file);
                                }
                            });
                            console.log('ADAM : files', matchedResults);
                            if (matchedResults.length > 0) {
                                searchResult = (matchedResults.sort().reverse())[0];
                                att.startTime = new Date(moment().utc());
                                fs.copyFile(sourceFolder + searchResult, destinationFolder + '/' + searchResult, (err) => {
                                    if (err) {
                                        console.log('ADAM : Unable to copy file');
                                        segment.saveSegment(`ADAM : Unable to copy file`, err);
                                    } else {
                                        att.endTime = new Date(moment().utc());
                                        att.uniqueId = util.generateUniqueId();
                                        att.status = true;
                                        att.mockServer = true;
                                        att.message = "Success";
                                        let oldStoreArray = job.attrs.data.storeDataArray;
                                        let newStoreArray = [att];
                                        oldStoreArray.map(data => {
                                            if (data.enterpriseCode === newStoreArray[0].enterpriseCode) {
                                                data = newStoreArray;
                                            }
                                        });
                                        var _storeArray = oldStoreArray;
                                        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                                        job.attrs.data.storeDataArray = _storeArray;
                                        job.save();
                                        done();
                                    }
                                });
                            } else {
                                let warningObj = {};
                                segment.saveSegment(`ADAM : Test job failed`);
                                console.log('ADAM : Test job failed');
                                att.startTime = new Date(moment().utc());
                                att.endTime = new Date(moment().utc());
                                att.uniqueId = util.generateUniqueId();
                                att.status = false;
                                att.mockServer = true;
                                att.message = "Failed";
                                job.fail(new Error(`ADAM :Test job failed`));
                                job.save()

                                let failureDirectory = process.cwd() + '/logs/Adam/failure/';
                                let failurelogFile = failureDirectory + storeCode + '.log';
                                let mailTemplateReplacementValues = {
                                    dmsType: constants.JOB_TYPE,
                                    processTypes: constants.PROCESS_JSON.JOB_NAME,
                                    subject: `Test Extraction Job for ${groupCode} - ${storeCode} Failed`,
                                    warningObj: warningObj,
                                    thirdPartyUsername: att.projectId,
                                    storeCode: storeCode,
                                    groupCode: groupCode
                                };
                                let mailBody = {
                                    fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                                    toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                                    ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                                    attachedfailurelogFile: failurelogFile
                                }
                                var displayMessage = `Test Failed ${constants.JOB_TYPE} ${constants.ADAM.JOB_NAME} job for group ${groupCode} and store ${storeCode}`;
                                mailTemplateReplacementValues.message = displayMessage;
                                mailTemplateReplacementValues.status = 'Failed';
                                mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                                segment.saveSegment(`Send notification: ${displayMessage}`);
                                segment.saveSegmentFailure('Extraction status: Extraction Failed', storeCode);
                                // Send notification after  cdk extraction job completed
                                mailSender.sendMail(mailBody, constants.ADAM.JOB_NAME);
                            }
                        });
                    } else {
                        const child = spawn(constants.ADAM.EXTRACT_CMD, options);
                        var startTime = new Date(moment().utc());
                        att.startTime = startTime;
                        var oldStoreArray = job.attrs.data.storeDataArray;
                        var newStoreArray = [att];
                        oldStoreArray.map(data => {
                            if (data.storeCode === newStoreArray[0].storeCode) {
                                data = newStoreArray;
                            }
                        });
                        var _storeArray = oldStoreArray;
                        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                        job.attrs.data.storeDataArray = _storeArray;
                        segment.saveSegment(`Extraction Job Data: ${JSON.stringify(_storeArray)}`);
                        segment.saveSegmentFailure(`Extraction Job Data: ${JSON.stringify(_storeArray)}`, storeCode);
                        att.uniqueId = util.generateUniqueId();
                        await job.save();
                        process.stdin.pipe(child.stdin)
                        var compareString = "";
                        var status = true;
                        var message = "n/a";

                        child.stdout.on("data", async (data) => {
                            await job.touch();
                            compareString = data.toString('utf8');
                            if (compareString.search("error:") != -1) {
                                message = data.toString('utf8')
                            }
                            console.log(`stdout: ${data}`);
                            data = data.toString('utf8');
                            if (data.includes('Output zip file successfully generated @path')) {
                                const pathMatch = data.match(/@path: (\/.*?\.zip)/);                               
                                processFileName = data.split(':')[2].split('/')[7];                                
                                segment.saveSegment('OUTPUT FILE  GENERATED', processFileName);
                            } else {
                                segment.saveSegment('FAILED TO GENERATE FILE');
                            }
                            segment.saveSegment(`stdout: ${data}`);
                            segment.saveSegmentFailure(`stdout: ${data}`, storeCode);
                        });

                        child.stderr.on("data", async (data) => {
                            await job.touch();
                            compareString = data.toString('utf8');
                            if (compareString.search("error:") != -1) {
                                message = data.toString('utf8')
                            }
                            console.log(`stderr: ${data}`);
                            segment.saveSegment(`stderr: ${data}`);
                            segment.saveSegmentFailure(`stderr: ${data}`, storeCode);
                        });

                        child.on("close", async (code) => {
                            if (code == constants.STATUS_CODE.SUCCESS) {
                                status = true;
                                message = "Success";
                            } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                                status = false;
                                message = "Extraction failed, general death";
                            } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                                status = false;
                                message = "Extraction failed, moved to dead-letter path";
                            }
                            segment.saveSegment(`Job close: ${message}`);
                            segment.saveSegmentFailure(`Job close: ${message}`, storeCode);

                            let failureDirectory = process.cwd() + '/logs/Adam/failure/';
                            let failurelogFile = failureDirectory + storeCode + '.log';

                            var groupName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageGroupCode : '';
                            var storeName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageStoreCode : '';

                            var mailTemplateReplacementValues = {
                                dmsType: constants.JOB_TYPE,
                                processTypes: constants.PROCESS_JSON.JOB_NAME,
                                subject: `Extraction Job for ${groupName} - ${storeName} Completed`,
                                warningObj: warningObj
                            };
                            var mailBody = {
                                fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                                toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                                ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                                attachedfailurelogFile: failurelogFile
                            }
                            var groupName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageGroupCode : '';
                            var storeName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageStoreCode : '';
                            if (status) {
                                // Send notification
                                var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.ADAM.JOB_EXTRACT_NAME} job for group ${groupName} and store ${storeName}`;
                                mailTemplateReplacementValues.message = displayMessage;
                                mailTemplateReplacementValues.status = 'Success';
                                mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                                segment.saveSegment(`Send notification: ${displayMessage}`);
                                segment.saveSegmentFailure('Extraction status: Extraction completed', storeCode);
                                // Send notification after  Adam extraction job completed
                                mailSender.sendMail(mailBody, constants.ADAM.JOB_NAME);
                            } else {
                                // Send notification
                                mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                                mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                                var displayMessage = `Failed ${constants.JOB_TYPE} ${constants.ADAM.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                mailTemplateReplacementValues.message = displayMessage;
                                mailTemplateReplacementValues.subject = 'Extraction Job Failed';
                                mailTemplateReplacementValues.status = `Extraction Job for ${groupName} - ${storeName} Failed`;
                                mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                                const UserInput = {
                                    inLaborProjectId: inLaborProjectId,
                                    inLaborProjectType: inLaborProjectType,
                                    inPartsProjectType: inPartsProjectType,
                                    inPartsProjectId: inPartsProjectId,
                                    inIsInSales: true,
                                    inSalesComment: appConstants.SALES_TAG_COMMENT,
                                    inUpdatedBy: userName
                                }
                                segment.saveSegment(`Send notification - sales tag creation: ${JSON.stringify(UserInput)}`);
                                try {
                                    const SendSalesTagDetails = await manageScheduleField.sendNotificationCall(UserInput, constants.ADAM.JOB_NAME);
                                    segment.saveSegment(`ADAM Extraction - sales tag creation: ${JSON.stringify(SendSalesTagDetails)}`);
                                } catch (salesError) {
                                    segment.saveSegment(`Send notification - sales tag creation Error: ${salesError}`);
                                }

                                segment.saveSegment(`Send notification: ${displayMessage}`);
                                segment.saveSegmentFailure('Extraction status: Extraction failed', storeCode);

                                // Portal update for extraction failed
                                let todayDate;
                                let attPayload = {};
                                let projectID;
                                let secondProjectID;
                                let inpObjProject;
                                let inpObjSecondProject;
                                let secondProjectIdList;
                                let projectIdList;
                                try {
                                    todayDate = new Date().toISOString().slice(0, 10);
                                    attPayload = att;
                                    projectID = attPayload.hasOwnProperty('projectId') ? attPayload.projectId : "";
                                    projectIdList = attPayload.hasOwnProperty('projectIds') ? attPayload.projectIds.split("*") : "";
                                    secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ? attPayload.secondProjectIdList.split("*") : "";
                                    attPayload['inProjectId'] = projectID;

                                    secondProjectID = attPayload.hasOwnProperty('secondProjectId') ? attPayload.secondProjectId : "";
                                    attPayload.in_is_update_retrieve_ro = attPayload.hasOwnProperty('solve360Update') ? attPayload.solve360Update : "";

                                    attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                                    attPayload.in_retrive_ro_request_on = todayDate;
                                    inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectID, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                    console.log(inpObjProject, "******** INP OBJJJJJ ***********");
                                    if (secondProjectIdList.length > 0) {
                                        inpObjSecondProject = commonUtil.getinpObjFordoPayloadAction(secondProjectID, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                        console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********");
                                    }
                                } catch (err) {
                                    console.log(JSON.stringify(err));
                                    segment.saveSegment(`ADAM : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                                }
                                segment.saveSegment(`ADAM : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                                segment.saveSegment(`ADAM : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                                try {
                                    if (secondProjectIdList.length > 0) {
                                        for (let i = 0; i < secondProjectIdList.length; i++) {
                                            if (secondProjectIdList[i] != undefined && secondProjectIdList[i] != '') {
                                                inpObjSecondProject = commonUtil.getinpObjFordoPayloadAction(secondProjectIdList[i], attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                                console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********");
                                                segment.saveSegment(`ADAM : doPayloadAction - ${JSON.stringify(inpObjSecondProject)}`);
                                                portalUpdate.doPayloadAction(inpObjSecondProject);
                                            }

                                        }

                                    }

                                    if (projectIdList.length > 0) {
                                        for (let i = 0; i < projectIdList.length; i++) {
                                            if (projectIdList[i] != undefined && projectIdList[i] != '') {

                                                inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIdList[i], attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                                segment.saveSegment(`ADAM : doPayloadAction - ${JSON.stringify(inpObjProject)}`);
                                                portalUpdate.doPayloadAction(inpObjProject);
                                            }

                                        }

                                    }
                                } catch (error) {
                                    console.log("Error:", error);
                                    segment.saveSegment(`ADAM : doPayloadAction Error - ${JSON.stringify(error)}`);
                                }
                                //code end for portal update for extraction failed

                                // Send notification for failed Adam extraction
                                await segment.sleep(2000);
                                mailSender.sendMail(mailBody, constants.ADAM.JOB_NAME);
                                // segment.sleep(2000).then(() => { 
                                //     mailSender.sendMail(mailBody, constants.ADAM.JOB_NAME);
                                // });
                            }

                            att.endTime = new Date(moment().utc());
                            att.uniqueId = util.generateUniqueId();
                            att.status = status;
                            att.message = message;
                            var oldStoreArray = job.attrs.data.storeDataArray;
                            var newStoreArray = [att];
                            oldStoreArray.map(data => {
                                if (data.storeCode === newStoreArray[0].storeCode) {
                                    data = newStoreArray;
                                }
                            });
                            var _storeArray = oldStoreArray;
                            // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                            job.attrs.data.storeDataArray = _storeArray;
                            if (processFileName) {
                                processFileName = processFileName.trim();
                                if (pathMatch && pathMatch[1]) {
                                    const filePath = pathMatch[1];
                                    await manageScheduleField.processCompanyData(job, userName, processFileName, filePath, constants.JOB_TYPE);                                    
                                }
                                att.processFileName = processFileName;
                            } else {
                                processFileName = null;
                            }
                            await job.save();
                            console.log(`Adam : Extraction process for store ${att.mageStoreCode} exited code ${code}`);
                            segment.saveSegment(`Adam : Extraction process for store ${att.mageStoreCode} exited code ${code}`);
                            i++;
                            if (i < storeDataArray.length) {
                                // Check time frame
                                if (att.runNow) {
                                    segment.saveSegment(`Adam : runNow`);
                                    await extract(storeDataArray[i], job);
                                } else if (util.checkExtractTimeFrame()) {
                                    segment.saveSegment(`Adam : Check time frame and start extraction ${JSON.stringify(storeDataArray[i])}`);
                                    await extract(storeDataArray[i], job);
                                } else {
                                    const newDataArray = storeDataArray;
                                    try {
                                        AdamJobManager.scheduleAdamExtractJob(AdamJobManager.createScheduleObject(job, newDataArray.slice(i)), true);
                                        job.attrs.data.storeDataArray = storeDataArray.slice(0, i);
                                        job.fail(new Error(`Adam : Time exceeded, remaining stores scheduled to next day.`));
                                        segment.saveSegment(`Adam : Time exceeded, remaining stores scheduled to next day.`);
                                        await job.save();
                                        //done();
                                    } catch (error) {
                                        console.error(error);
                                        segment.saveSegment(`Error : ${error.toString()}`);
                                    }
                                }
                            } else {
                                done();
                            }
                        });
                    }
                } else {
                    console.error("Adam : Store data Extraction attributes not defined");
                    segment.saveSegment("Adam : Store data Extraction attributes not defined");
                }
            }
            if (att.runNow) { // Check whether it is for run now or not, if yes, no need to check time frame
                segment.saveSegment(`Adam : runNow : Check whether it is for run now or not, if yes, no need to check time frame ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else if (util.checkExtractTimeFrame()) {
                segment.saveSegment(`Adam : Check time frame and start extraction ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else { // Auto schedule full Group wise schedule for tomorrow
                segment.saveSegment(`Adam : Auto schedule full Group wise schedule for tomorrow`);
                AdamJobManager.scheduleAdamExtractJob(AdamJobManager.createScheduleObject(job), true);
                job.remove();
            }
        });

    agenda.on("start", job => {
        console.log(`Adam : Job ${job.attrs.name}_${job.attrs._id} starting`);
    });

    agenda.on("complete", job => {
        console.log(`Adam : Job ${job.attrs.name}_${job.attrs._id} finished`);
    });

    agenda.on("fail", (err, job) => {
        console.log(`Adam : Job ${job.attrs.name}_${job.attrs._id} failed with error: ${err.message} `);
    });
    return agenda;
}

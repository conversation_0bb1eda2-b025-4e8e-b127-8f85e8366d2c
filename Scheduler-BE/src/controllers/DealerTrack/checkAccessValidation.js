var errorResultObj = [];
const soap = require("soap");
const path = require("path");
const ENV_PATH = path.join(process.env.HOME + "/.dealertrack/.env");
require("dotenv").config({
  path: ENV_PATH,
});
var wsSecurity = new soap.WSSecurity(
  process.env.DEALERTRACK_VENDOR_ID,
  process.env.DEALERTRACK_VENDOR_PASSWORD
);
var nonstopCheckFlag = true;

const validateServiceAPI = (payload) => {
  return new Promise((resolve) => {
    try {
      let url = process.env.DEALERTRACK_SERVICE_URL_PIP_BASE;
      let wsdlOptions = {
        overrideRootElement: {
          namespace: "open",
          xmlnsAttributes: [
            {
              name: "xmlns:open",
              value: "opentrack.dealertrack.com",
            },
          ],
        },
      };

      let args = {
        "open:dealer": {
          "open:CompanyNumber": payload.companyNumber,
          "open:EnterpriseCode": payload.enterpriseCode,
          "open:ServerName": payload.serverName,
        },
        "open:request": {
          "open:RepairOrderNumber": "6074638",
        },
      };

      soap.createClient(url, wsdlOptions, function (err, client) {
        client.setSecurity(wsSecurity);
        client.GetClosedRepairOrderDetails(args, function (err, result) {
          if (err) {
            console.log(JSON.stringify(err));
            errorResultObj.push({
              errorMessage: "Something went wrong!",
              apiType: "Service API",
            });
            resolve(err);
          } else {
            if (
              result.GetClosedRepairOrderDetailsResult.hasOwnProperty("Errors")
            ) {
              let errorResObj;
              let errorMessage;
              let errorCode;
              errorResObj =
                result.GetClosedRepairOrderDetailsResult.Errors.Error;
              errorMessage = errorResObj[0].Message;
              if (errorResObj[0].hasOwnProperty("Code")) {
                errorCode = errorResObj[0].Code;
              }
              console.log("errorMessage:", errorMessage);
              console.log("errorCode:", errorCode);
              if (
                errorCode == "309" ||
                errorCode == "311" ||
                errorMessage == "Enterprise not found." ||
                errorMessage == "Company not found."
              ) {
                errorResultObj.push({
                  errorMessage: errorMessage,
                  apiType: "Service API",
                });
              } else {
                errorResultObj.push({
                  errorMessage: "Success",
                  apiType: "Service API",
                });
              }
              if (
                errorMessage == "Enterprise not found." ||
                errorMessage == "Company not found."
              ) {
                nonstopCheckFlag = false;
              }
            } else {
              errorResultObj.push({
                errorMessage: "Success",
                apiType: "Service API",
              });
            }
            resolve("completed");
          }
        });
      });
    } catch (err) {
      errorResultObj.push({
        errorMessage: "Something went wrong!",
        apiType: "Service API",
      });
      resolve(err);
    }
  });
};

const validateVehicleAPI = (payload) => {
  return new Promise((resolve) => {
    try {
      let url = process.env.DEALERTRACK_VEHICLE_LOOKUP_URL;
      let wsdlOptions = {
        overrideRootElement: {
          namespace: "open",
          xmlnsAttributes: [
            {
              name: "xmlns:open",
              value: "opentrack.dealertrack.com",
            },
          ],
        },
      };

      let args = {
        "open:Dealer": {
          "open:CompanyNumber": payload.companyNumber,
          "open:EnterpriseCode": payload.enterpriseCode,
          "open:ServerName": payload.serverName,
        },
        "open:LookupParms": {
          "open:VIN": "1FTFX1ET9CFB19097",
        },
      };

      soap.createClient(url, wsdlOptions, function (err, client) {
        client.setSecurity(wsSecurity);
        client.VehicleLookup(args, function (err, result) {
          if (err) {
            console.log("error", err);
            errorResultObj.push({
              errorMessage: "Something went wrong!",
              apiType: "Vehicle API",
            });
            resolve(err);
          } else {
            if (result.hasOwnProperty("Errors")) {
              console.log("Error:", JSON.stringify(result));
              let errorResObj;
              let errorMessage;
              let errorCode;
              errorResObj = result.Errors.Error;
              console.log(JSON.stringify(errorResObj));
              errorMessage = errorResObj[0].Message;
              if (errorResObj[0].hasOwnProperty("Code")) {
                errorCode = errorResObj[0].Code;
              }
              console.log("Error in Vehicle Lookup");
              console.log("errorMessage:", errorMessage);
              console.log("errorCode:", errorCode);
              if (errorCode == "309" || errorCode == "311") {
                errorResultObj.push({
                  errorMessage: errorMessage,
                  apiType: "Vehicle API",
                });
              } else {
                errorResultObj.push({
                  errorMessage: "Success",
                  apiType: "Vehicle API",
                });
              }
            } else {
              errorResultObj.push({
                errorMessage: "Success",
                apiType: "Vehicle API",
              });
            }
            resolve("completed");
          }
        });
      });
    } catch (err) {
      errorResultObj.push({
        errorMessage: "Something went wrong!",
        apiType: "Vehicle API",
      });
      resolve(err);
    }
  });
};

const validateCustomerAPI = (payload) => {
  return new Promise((resolve) => {
    try {
      let url = process.env.DEALERTRACK_CUSTOMER_LOOKUP_URL;
      let wsdlOptions = {
        overrideRootElement: {
          namespace: "open",
          xmlnsAttributes: [
            {
              name: "xmlns:open",
              value: "opentrack.dealertrack.com",
            },
          ],
        },
      };

      let args = {
        "open:Dealer": {
          "open:CompanyNumber": payload.companyNumber,
          "open:EnterpriseCode": payload.enterpriseCode,
          "open:ServerName": payload.serverName,
        },
        "open:LookupParms": {
          "open:CustomerNumber": "1039974",
        },
      };

      soap.createClient(url, wsdlOptions, function (err, client) {
        client.setSecurity(wsSecurity);
        client.CustomerLookup(args, function (err, result) {
          if (err) {
            console.log("error", err);
            errorResultObj.push({
              errorMessage: "Something went wrong!",
              apiType: "Customer API",
            });
            resolve(err);
          } else {
            let errorResObj;
            let errorMessage;
            let errorCode;
            // console.log(JSON.stringify(result));
            if (result.hasOwnProperty("Errors")) {
              errorResObj = result.Errors.Error;
              errorMessage = errorResObj[0].Message;
              if (errorResObj[0].hasOwnProperty("Code")) {
                errorCode = errorResObj[0].Code;
              }
              console.log("Error in Customer Lookup");
              console.log("errorMessage:", errorMessage);
              console.log("errorCode:", errorCode);
              if (errorCode == "309" || errorCode == "311") {
                errorResultObj.push({
                  errorMessage: errorMessage,
                  apiType: "Customer API",
                });
              } else {
                errorResultObj.push({
                  errorMessage: "Success",
                  apiType: "Customer API",
                });
              }
            } else {
              errorResultObj.push({
                errorMessage: "Success",
                apiType: "Customer API",
              });
            }
            resolve("completed");
          }
        });
      });
    } catch (err) {
      errorResultObj.push({
        errorMessage: "Something went wrong!",
        apiType: "Customer API",
      });
      resolve(err);
    }
  });
};

const validateGLAccountAPI = (payload) => {
  return new Promise((resolve) => {
    try {
      let url = process.env.DEALERTRACK_GLACCOUNT_LOOKUP_URL;
      let wsdlOptions = {
        overrideRootElement: {
          namespace: "open",
          xmlnsAttributes: [
            {
              name: "xmlns:open",
              value: "opentrack.dealertrack.com",
            },
          ],
        },
      };

      let args = {
        "open:Dealer": {
          "open:CompanyNumber": payload.companyNumber,
          "open:EnterpriseCode": payload.enterpriseCode,
          "open:ServerName": payload.serverName,
        },
        "open:LookupParms": {
          "open:DocumentNumber": "6000001",
        },
      };

      soap.createClient(url, wsdlOptions, function (err, client) {
        client.setSecurity(wsSecurity);
        client.GLDetailLookup(args, function (err, result) {
          if (err) {
            console.log("error", err);
            errorResultObj.push({
              errorMessage: "Something went wrong!",
              apiType: "Accounting API",
            });
            resolve(err);
          } else {
            if (result.hasOwnProperty("Errors")) {
              let errorResObj;
              let errorMessage;
              let errorCode;
              errorResObj = result.Errors.Error;
              errorMessage = errorResObj[0].Message;
              if (errorResObj[0].hasOwnProperty("Code")) {
                errorCode = errorResObj[0].Code;
              }
              console.log("Error in GLDetail Lookup");
              console.log("errorMessage:", errorMessage);
              console.log("errorCode:", errorCode);
              if (errorCode == "309" || errorCode == "311") {
                errorResultObj.push({
                  errorMessage: errorMessage,
                  apiType: "Accounting API",
                });
              } else {
                errorResultObj.push({
                  errorMessage: "Success",
                  apiType: "Accounting API",
                });
              }
            } else {
              errorResultObj.push({
                errorMessage: "Success",
                apiType: "Accounting API",
              });
            }
            resolve("completed");
          }
        });
      });
    } catch (err) {
      errorResultObj.push({
        errorMessage: "Something went wrong!",
        apiType: "Accounting API",
      });
      resolve(err);
    }
  });
};

const validatePartKitLookUpAPI = (payload) => {
  return new Promise((resolve) => {
    try {
      let url = process.env.DEALERTRACK_PARTKIT_URL_PIP_BASE;
      let wsdlOptions = {
        overrideRootElement: {
          namespace: "open",
          xmlnsAttributes: [
            {
              name: "xmlns:open",
              value: "opentrack.dealertrack.com",
            },
          ],
        },
      };

      let args = {
        "open:Dealer": {
        "open:CompanyNumber": payload.companyNumber,
        "open:EnterpriseCode": payload.enterpriseCode,
        "open:ServerName": payload.serverName,
        },
        "open:LookupParms": {
          "open:KitName": "A",
        },
      };

      soap.createClient(url, wsdlOptions, function (err, client) {
        client.setSecurity(wsSecurity);
        client.PartKitLookup(args, function (err, result) {
          if (err) {
            console.log("error", err);
            errorResultObj.push({
              errorMessage: "Something went wrong!",
              apiType: "Parts Kit Lookup API",
            });
            resolve(err);
          } else {
            let errorResObj;
            let errorMessage;
            let errorCode;
            if (result.PartKitLookupResult.Errors != null) {
              errorResObj = result.PartKitLookupResult.Errors.Error;
              errorMessage = errorResObj[0].Message;
              if (errorResObj[0].hasOwnProperty("Code")) {
                errorCode = errorResObj[0].Code;
              }
              console.log("Error in PartsKit lookup");
              console.log("errorMessage:", errorMessage);
              console.log("errorCode:", errorCode);
              if (errorCode == "309" || errorCode == "311" || errorResObj[0].Message.trim() == 'Company not found.' || errorResObj[0].Message.trim() == 'Enterprise not found.') {
                errorResultObj.push({
                  errorMessage: errorMessage,
                  apiType: "Parts Kit Lookup API",
                });
              } else {
                errorResultObj.push({
                  errorMessage: "Success",
                  apiType: "PartsKit Lookup API",
                });
              }
            } else {
              errorResultObj.push({
                errorMessage: "Success",
                apiType: "PartsKit Lookup API",
              });
            }
            resolve("completed");
          }
        });
      });
    } catch (err) {
      errorResultObj.push({
        errorMessage: "Something went wrong!",
        apiType: "Parts Kit Lookup API",
      });
      resolve(err);
    }
  });
};

exports.checkAccessValidation = async (payload, cb) => {
  console.log("payload@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",payload);
  errorResultObj = [];
  nonstopCheckFlag = true;
  console.log(payload);
  console.log("ENV_PATH:", ENV_PATH);
  await validateServiceAPI(payload);
  if (nonstopCheckFlag) {
    await validateVehicleAPI(payload);
    await validateCustomerAPI(payload);
    await validateGLAccountAPI(payload);
    await validatePartKitLookUpAPI(payload);
  }
  console.log(JSON.stringify(errorResultObj));
  cb(errorResultObj);
};

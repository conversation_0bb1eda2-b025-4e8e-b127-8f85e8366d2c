const DealerTrackJobManager = require("../DealerTrackJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all DealerTrack-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllDealerTrackExtractJobs(_, args) {
      return DealerTrackJobManager.getAllDealerTrackExtractJobs();
    },

    /**
     * Query to get all Automate Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllDealerTrackProcessJSONJobs(_, args) {
      return DealerTrackJobManager.getAllDealerTrackProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule DealerTrack-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleDealerTrackExtractJob(_, args) {
      return DealerTrackJobManager.scheduleDealerTrackExtractJob(args.input);
    },

    /**
     * Mutation to run a DealerTrack-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowDealerTrackExtractJobByStore(_, args) {
      return DealerTrackJobManager.runNowDealerTrackExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a DealerTrack-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelDealerTrackExtractJobByStore(_, args) {
      return DealerTrackJobManager.cancelDealerTrackExtractJobByStore(args.input);
    },

   /**  * Mutation to create a Process XML job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    createProxyWithSqlDump(_, args) {
    return DealerTrackJobManager.createProxyWithSqlDump(args.input);

    }
 },
  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;
var gt = require("graphql-tools");
var resolvers = require("./resolver");

var typeDefs = `

# This type specifies the entry points into our API

#Scalar types
scalar Date
scalar DateTime
scalar _bsontype

# Type of DealerTrack extract Job
enum JobType {
  initial
  refresh
  ondemand
}

# close RO options
enum ClosedROOption {
  all
  monthly
  weekly
  current
}

input SingleStoreJobData{
  groupName: String!
  storeData: ExtractData!
}

type Data{
  groupName: String # Optional
  storeDataArray: [StoreData]
}

type QueueData{
  storeID: String,
  fileToProcess: String,
  priority:String
}

type JSONProcessData {
  operation: String # Avoid operation = recheck or operation = start
  inputFile: String
  storeID: String
  outputFile: String
  status: Boolean
  message: String
  createdAt: String
  storeName: String
  warningMessage: errorData
  roAccountDescExceptionCount: Int
  company_no_not_matching_count:Int
  grouped_team_work_count:Int
  address:String
  isResumed:String
  isAlreadyResumed:Boolean
  suffixedInvoicesCsvData:String
  new_line_type_count:Int
  negative_coupon_count:Int
  labor_with_zero_sale_nonzero_cost_count:Int
  gl_missing_ros_count:Int
  coupon_discount_basis_amount_mismatch_exception_count:Int
  labor_with_no_paytype_exception_count:Int
  parts_excluded_from_history_exception_count:Int
  lost_sale_parts_exception_count:Int
  processorUniqueId:String
  chart_of_accounts_file_path:String
}

type errorData {
  skipErrorCount: Int,
  errorwarningMessage: [String],
  closedRODetailErrorwarningMessage: [String],
  vehicleErrorwarningMessage: [String]
  customerErrorwarningMessage: [String]
  glDeatilErrorwarningMessage: [String]
  couponAndDiscountWarningMessage: String
  roAccountDescriptionWarningMessage: Int
  couponAndDiscountFileNotUploadedWarningMessage: String
  coaExceptionWarningMessage: String
  customerExceptionWarningMessage: String
}

type StoreData {
  enterpriseCode: String!
  projectId: String
  secondProjectId: String
  mageManufacturer: String
  solve360Update: Boolean
  buildProxies: Boolean
  userName: String
  startDate: Date!
  endDate: Date!
  zipPath: String
  closedROOption: ClosedROOption
  startTime: String
  endTime:String
  status: Boolean
  message: String
  jobType: JobType
  mageGroupCode: String
  mageStoreCode: String
  stateCode: String
  companyNumber: String
  serverName: String
  dealerAddress: String
  skipErrorCount: Int
  coupon_and_discountCSVFilePath: String 
  haltOverRide: Boolean
  chart_of_accounts_file_path: String
  projectIds:String
  secondProjectIdList:String
  parentName:String
  testData:Boolean
  companyIds:String
  companyObj:String
  processFileName:String
  brands:String
 
}

type ExtractJob{
  timeFrameZone: String
  timeFrameStartTime: String
  timeFrameEndTime:String
  poolTime:Int
  jobArray: [Job]
}

type Job {
  name: String
  data:Data
  type: String
  priority: String
  nextRunAt: String
  _id: _bsontype
  lastModifiedBy: String
  lockedAt: String
  lastRunAt: String
  lastFinishedAt: String
  running: Boolean
  scheduled: Boolean
  queued: Boolean
  completed: Boolean
  failed: Boolean
  repeating: Boolean
  failReason: String
}

type ProcessJSONJob {
  name: String
  data:JSONProcessData
  type: String
  priority: String
  nextRunAt: String
  _id: _bsontype
  lastModifiedBy: String
  lockedAt: String
  lastRunAt: String
  lastFinishedAt: String
  running: Boolean
  scheduled: Boolean
  queued: Boolean
  completed: Boolean
  failed: Boolean
  repeating: Boolean
  failReason: String
  uploadStatus: Boolean
}

# Input to pass custom JSON data to a schedule
input JobData{
  groupName: String!
  storeDataArray: [ExtractData]!
}

input ExtractData {
  enterpriseCode: String!
  projectId: String
  secondProjectId: String
  mageManufacturer: String
  solve360Update: Boolean
  buildProxies: Boolean
  userName: String
  startDate: Date!
  endDate: Date!
  zipPath: String
  closedROOption: ClosedROOption
  jobType: JobType
  mageGroupCode: String!
  mageStoreCode: String!
  stateCode: String!
  companyNumber: String!
  serverName: String!
  dealerAddress: String
  skipErrorCount: Int
  coupon_and_discountCSVFilePath: String
  haltOverRide: Boolean
  chart_of_accounts_file_path: String
  projectIds:String
  secondProjectIdList:String
  testData:Boolean
  companyIds:String
  futureDate:String
  parentName:String
  companyObj:String
  projectType:String
  secondaryProjectType:String
  groupCode:String
  mageStoreName:String
  errors: String
  thirdPartyUsername: String
  assignedtoCn: String
  brands:String
}

input DealerTrackScheduleInput{
  jobSchedule: DateTime!
  jobData: JobData!
}

input CancelDealerTrackScheduleInput{
  jobSchedule: DateTime!
  jobData: SingleStoreJobData!
}

#Input to run a particular DealerTrack Store's  extraction
input RunNowDealerTrackScheduleInput {
  jobSchedule: DateTime!
  jobData: SingleStoreJobData!
}

input ProxyGenerationUsingPgDump{
  zipPath: String!

}

input CreateProxyInput{
  proxyJobData: ProxyGenerationUsingPgDump!
}

# Represents the Status of each Mutations
type Status {
  status: Boolean!
  message: String!
  job: Job
}

type ProcessJSONInfo{
  processJSONJobsQueue: [QueueData],
  processJSONJobs: [ProcessJSONJob]
}

type Query {
  getAllDealerTrackProcessJSONJobs: ProcessJSONInfo
  getAllDealerTrackExtractJobs: ExtractJob
}

# Mutations
type Mutation {
  # Mutation to schedule a DealerTrack extraction job
  scheduleDealerTrackExtractJob (input: DealerTrackScheduleInput!): Status,
  cancelDealerTrackExtractJobByStore (input: CancelDealerTrackScheduleInput!): Status,
  runNowDealerTrackExtractJobByStore (input: RunNowDealerTrackScheduleInput!): Status,
  createProxyWithSqlDump (input: CreateProxyInput!): Status
}

schema {
  query: Query,
  mutation: Mutation
}
`;
var schema = gt.makeExecutableSchema({ typeDefs, resolvers });
module.exports = schema;

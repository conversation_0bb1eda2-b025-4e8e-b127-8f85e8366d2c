const constants = require("./constants");
const segment = require("../SEGMENT/DealerTrack/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeDealerTrackProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "dealertrack-process-json") {
      initializeDealerTrackProcessJSON = true
    }
    if (type.split('-')[0] == 'dealertrack') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the dealerTrack-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeDealerTrackProcessJSON) {
    try {
      // Initialize DealerTrack Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("DealerTrack :  Process JSON schedule started");
      segment.saveSegment("DealerTrack :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("DealerTrack Extraction Processing Not Enabled - Pass Job Type dealerTrack-process-json To Enable");
    segment.saveSegment("DealerTrack Extraction Processing Not Enabled - Pass Job Type dealerTrack-process-json To Enable");
  }
  return true;
}

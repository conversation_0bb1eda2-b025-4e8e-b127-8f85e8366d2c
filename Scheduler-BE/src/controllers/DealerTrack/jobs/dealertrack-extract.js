"use strict";

const constants = require("../constants");
const util = require("../util");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const DealertrackJobManager = require("../DealerTrackJobManager");
const { spawn } = require("child_process");
const moment = require("moment-timezone");
const fs = require("fs");
const segment = require("../../SEGMENT/DealerTrack/segmentManager");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const { v4: uuidv4 } = require('uuid');
const manageScheduleField = require('../../../../src/common/util');


/**
 * Function to find the unique stores in an array of stores
 */
Array.prototype.unique = function () {
    var a = this.concat();
    for (var i = 0; i < a.length; ++i) {
        for (var j = i + 1; j < a.length; ++j) {
            if (a[i].enterpriseCode === a[j].enterpriseCode)
                a.splice(j--, 1);
        }
    }
    return a;
};

/**
 * Function to perform DealerTrack-Extract
 */
module.exports = function DealerTrackExtractJOB(agenda) {
    var storeCode = '';
    let extractionId;
    var enterpriseCode;
    var groupCode; 
    var uniqueFailLogName;
    console.log(
        `DealerTrack-Extract job started: JobName: ${constants.DEALERTRACK.DMS}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.DEALERTRACK.CONCURRENCY}`
    );
    segment.saveSegment(`DealerTrack-Extract job started: JobName: ${constants.DEALERTRACK.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.DEALERTRACK.CONCURRENCY}`);
    agenda.define(constants.DEALERTRACK.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.DEALERTRACK.CONCURRENCY },
        async (job, done) => {
            extractionId = job.attrs._id;
            const att = job.attrs.data;
            const storeDataArray = att.storeDataArray.reverse();
            var i = 0;
            var projectId,projectType,secondaryProjectType,inLaborProjectType,inLaborProjectId,inPartsProjectType,inPartsProjectId,secondaryProjectId;
            var solve360Update, buildProxies;
            var warningObj = {};
            let processFileName;
            let userName;

            async function extract(att, job) {
                enterpriseCode = att.enterpriseCode;
                groupCode = att.mageGroupCode;
                storeCode = att.mageStoreCode;
                projectId = att.projectId;
                projectType = att.projectType || null;              
                secondaryProjectType = att.secondaryProjectType || null;
                secondaryProjectId = att.secondProjectId;
                if (projectType && projectType.toLowerCase().startsWith("labor")) {
                    inLaborProjectType = projectType;
                    inLaborProjectId = projectId;
                } else if (secondaryProjectType && secondaryProjectType.toLowerCase().startsWith("labor")) {
                    inLaborProjectType = secondaryProjectType;
                    inLaborProjectId = secondaryProjectId;
                }
                  
                  // Check if projectType or secondaryProjectType starts with "parts"
                  if (projectType && projectType.toLowerCase().startsWith("parts")) {
                    inPartsProjectType = projectType;
                    inPartsProjectId = projectId;
                  } else if (secondaryProjectType && secondaryProjectType.toLowerCase().startsWith("parts")) {
                    inPartsProjectType = secondaryProjectType;
                    inPartsProjectId = secondaryProjectId;
                  }
                solve360Update = att.solve360Update;
                buildProxies = att.buildProxies;
                uniqueFailLogName = enterpriseCode + '-' + storeCode;
                userName = att.userName;
                warningObj.scheduled_by = userName;
                console.log('projectId', projectId);
                console.log('solve360Update', solve360Update);
                console.log('extractionId:', extractionId)
                segment.saveSegment(`Dealertrack: Extraction Job Started: ${JSON.stringify(att)}`);
                segment.saveSegmentFailure(`Dealertrack: Extraction Job Started: ${JSON.stringify(att)}`, uniqueFailLogName);
                if (att.enterpriseCode && att.startDate && att.endDate) {
                    if (!fs.existsSync(constants.DEALERTRACK_DEADLETTER_DIR_PREFIX + '-extracted')) {
                        fs.mkdirSync(constants.DEALERTRACK_DEADLETTER_DIR_PREFIX + '-extracted');
                    }
                    var options = [
                        constants.DEALERTRACK.PULL_OP,
                        constants.DEALERTRACK.OPT_ENTERPRISE_CODE, att.enterpriseCode,
                        constants.DEALERTRACK.OPT_COMPANY_NUMBER, att.companyNumber,
                        constants.DEALERTRACK.OPT_SERVER_NAME, att.serverName,
                        constants.DEALERTRACK.OPT_START_DATE, att.startDate,
                        constants.DEALERTRACK.OPT_END_DATE, att.endDate,
                        constants.DEALERTRACK.OPT_DEADLETTER_DIR_PREFIX,
                        constants.DEALERTRACK_DEADLETTER_DIR_PREFIX + '-extracted',
                        constants.DEALERTRACK.OPT_MAGE_GROUP_CODE, att.mageGroupCode,
                        constants.DEALERTRACK.OPT_MAGE_STORE_CODE, att.mageStoreCode,
                        constants.DEALERTRACK.OPT_DEALER_ADDRESS, att.dealerAddress,
                        '--stateCode', att.stateCode,
                        '--extractionID',extractionId,
                        '--skipErrorCount',att.skipErrorCount
                    ];
                    
                    options.push(constants.DEALERTRACK.OPT_ZIP_PATH, att.zipPath ? att.zipPath : constants.DEALERTRACK_SCHEDULER_ETI_DIR);
                    options.push(constants.DEALERTRACK.OPT_ZAP_AFTER_ZIP);
                    
                    
                    console.log("att.closedROOption################################################",att.closedROOption);
                    // initial
                    (att.jobType == constants.DEALERTRACK.JOB_TYPE_INITIAL) ? options.push(constants.DEALERTRACK.OPT_CLOSED) : null;

                    ((att.jobType == constants.DEALERTRACK.JOB_TYPE_INITIAL) ?
                        options.push(att.closedROOption ? att.closedROOption : constants.DEALERTRACK.OPT_CLOSED_CRITERIA_ALL) : null);

                    // refresh
                    ((att.jobType == constants.DEALERTRACK.JOB_TYPE_REFRESH) ? options.push(constants.DEALERTRACK.OPT_DELTA_DATE) : null);
                    ((att.jobType == constants.DEALERTRACK.JOB_TYPE_REFRESH) ?
                        options.push(moment().tz(constants.DEALERTRACK.TIME_ZONE).format("MM/DD/YYYY")) : null);

                    // ondemand
                    (att.jobType == constants.DEALERTRACK.JOB_TYPE_ON_DEMAND) ? options.push(constants.DEALERTRACK.OPT_CLOSED) : null;
                    (att.jobType == constants.DEALERTRACK.JOB_TYPE_ON_DEMAND) ? options.push(constants.DEALERTRACK.OPT_CLOSED_CRITERIA_CURRENT) : null; 




                    options.push(constants.DEALERTRACK.OPT_BUNDLE);
                    options.push(att.jobType);
                    segment.saveSegment(`DEALERTRACK: Extraction Job Started: ${JSON.stringify(att)}`);
                    segment.saveSegmentFailure(`DEALERTRACK: Extraction Job Started: ${JSON.stringify(att)}`, uniqueFailLogName);
                    //Mock Server
                    let isMockServer = constants.DEALERTRACK.ENV_MOCKSERVER; 
                    if(isMockServer == "true"){
                        let groupCode = att.mageGroupCode;
                        let sourceFolder = constants.DEALERTRACK.MOCKSERVER_SOURCE_FOLDER_PATH +'dealertrack/'; 
                        let destinationFolder = constants.DEALERTRACK.MOCKSERVER_DESTINATION_FOLDER_PATH_DEALERTRACK;
                        //HertrichFamilyDlrshp-POCOMO_GM-MD-INITIAL-E6-20231120085220.zip
                        const mageGroupCodeDIR =  groupCode.replace(/ +/g, "");
                        const mageStorecodeDIR =  storeCode.replace(/ +/g, "");
                        const stateCodeDIR = att.stateCode;
                        const jobTypeDIR = att.jobType ;
                        const enterpriseCodeDir = att.enterpriseCode;
                        const zipFileName = mageGroupCodeDIR +'-'+ mageStorecodeDIR +'-'+ stateCodeDIR +'-'+ jobTypeDIR.toUpperCase(); +'-'+ enterpriseCodeDir +'-';
                        console.log('-------zipFileName-------',zipFileName);
                        
                        fs.readdir(sourceFolder, function (err, files) {
                            if (err) {
                                console.log('DEALERTRACK : Unable to scan directory');
                                segment.saveSegment(`DEALERTRACK : Unable to scan directory`,err);
                            } 
                            const matchedResults = [];
                            let searchResult;
                            files.forEach(function (file) {
                                if(file.includes(zipFileName)) {
                                    matchedResults.push(file);
                                }
                            });
                            console.log('DEALERTRACK : files',matchedResults);
                            if(matchedResults.length > 0) {
                                searchResult = (matchedResults.sort().reverse())[0];
                                att.startTime = new Date(moment().utc());
                                fs.copyFile(sourceFolder+searchResult, destinationFolder+'/'+searchResult, (err) => {
                                    if (err) {
                                        console.log('DEALERTRACK : Unable to copy file');
                                        segment.saveSegment(`DEALERTRACK : Unable to copy file`,err);
                                    } else {
                                        att.endTime = new Date(moment().utc());
                                        att.uniqueId = util.generateUniqueId();
                                        att.status = true;
                                        att.mockServer = true;
                                        att.message = "Success";
                                        let oldStoreArray = job.attrs.data.storeDataArray;
                                        let newStoreArray = [att];
                                        oldStoreArray.map(data => {
                                            if (data.enterpriseCode === newStoreArray[0].enterpriseCode) {
                                                data = newStoreArray;
                                            }
                                        });
                                        var _storeArray = oldStoreArray;
                                        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                                        job.attrs.data.storeDataArray = _storeArray;
                                        job.save();
                                        done();
                                    }
                                });
                            }else{
                            let warningObj={};
                            segment.saveSegment(`DEALERTRACK : Test job failed`);
                            console.log('DEALERTRACK : Test job failed');
                            att.startTime = new Date(moment().utc());
                            att.endTime = new Date(moment().utc());
                            att.uniqueId = util.generateUniqueId();
                            att.status = false;
                            att.mockServer = true;
                            att.message = "Failed";
                            job.fail(new Error(`DEALERTRACK :Test job failed`));
                            job.save()
                            
                            let failureDirectory = process.cwd() + '/logs/DealerTrack/failure/';
                            let failurelogFile = failureDirectory + storeCode + '.log';
                            let mailTemplateReplacementValues = {
                                dmsType: constants.JOB_TYPE,
                                processTypes: constants.PROCESS_JSON.JOB_NAME,
                                subject: `Test Extraction Job for ${groupCode} - ${storeCode} Failed`,
                                warningObj: warningObj,
                                thirdPartyUsername: att.projectId,
                                storeCode: storeCode,
                                groupCode: groupCode
                            };
                            let mailBody = {
                                fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                                toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                                ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                                attachedfailurelogFile:failurelogFile
                            }
                            var displayMessage = `Test Failed ${constants.JOB_TYPE} ${constants.DEALERTRACK.JOB_NAME} job for group ${groupCode} and store ${storeCode}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Failed';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure('Extraction status: Extraction Failed', storeCode);
                            // Send notification after  cdk extraction job completed
                            mailSender.sendMail(mailBody, constants.DEALERTRACK.JOB_NAME);
                        }
                    });
                    } else {
                    const child = spawn(constants.DEALERTRACK.EXTRACT_CMD, options);
                    var startTime = new Date(moment().utc());
                    att.startTime = startTime;
                    var oldStoreArray = job.attrs.data.storeDataArray;
                    var newStoreArray = [att];
                    oldStoreArray.map(data => {
                        if (data.enterpriseCode === newStoreArray[0].enterpriseCode) {
                            data = newStoreArray;
                        }
                    });
                    var _storeArray = oldStoreArray;
                    // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                    job.attrs.data.storeDataArray = _storeArray;
                    segment.saveSegment(`Extraction Job Data: ${JSON.stringify(_storeArray)}`);
                    segment.saveSegmentFailure(`Extraction Job Data: ${JSON.stringify(_storeArray)}`, uniqueFailLogName);
                    att.uniqueId = util.generateUniqueId();
                    await job.save();
                    process.stdin.pipe(child.stdin)
                    var compareString = "";
                    var status = true;
                    var message = "n/a";
                    let unsubscribedDms = false;
                    child.stdout.on("data", async (data) => {
                        await job.touch();
                        compareString = data.toString('utf8');
                        if (compareString.search("error:") != -1) {
                            message = data.toString('utf8')
                        }
                        data = data.toString('utf8');
                        if (data.includes(`Access validation error: Dealer has not granted access to Vendor`)) {
                            unsubscribedDms = true;
                        }
                        if (data.includes("Output zip file successfully generated @path")) {
                            // Extract the file path portion
                            console.log("data",data);
                            const pathMatch = data.match(/@path: (\/.*?\.zip)/);
                            console.log("pathMatch",pathMatch);
                            segment.saveSegment(`pathMatch: ${pathMatch}`);
                            if (pathMatch && pathMatch[1]) {
                              const filePath = pathMatch[1];
                              // Extract the filename from the path
                              const match = filePath.match(/[^/]+\.zip$/);
                              if (match) {
                                processFileName = match[0];
                                console.log("Extracted filename:", processFileName);
                                if(processFileName){
                                    await manageScheduleField.processCompanyData(job, userName, processFileName, filePath, constants.DMS);
                                    att.processFileName = processFileName.trim();
                                    await job.save();
                                }else{
                                    processFileName = null;
                                }
                             
                              } else {
                                console.log("Failed to extract filename from path:", filePath);
                              }
                            } else {
                              console.log("Failed to find the file path in the log data");
                            }
                          } else {
                            console.log("Failed to generate file");
                          }
                        console.log(`stdout: ${data}`);
                        segment.saveSegment(`stdout: ${data}`);
                        segment.saveSegmentFailure(`stdout: ${data}`, uniqueFailLogName);
                    });

                    child.stderr.on("data", async (data) => {
                        await job.touch();
                        compareString = data.toString('utf8');
                        if (compareString.search("error:") != -1) {
                            message = data.toString('utf8')
                        }
                        console.log(`stderr: ${data}`);
                        segment.saveSegment(`stderr: ${data}`);
                        segment.saveSegmentFailure(`stderr: ${data}`, uniqueFailLogName);
                    });

                    child.on("close", async (code) => {
                        if (code == constants.STATUS_CODE.SUCCESS) {
                            status = true;
                            message = "Success";
                        } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                            status = false;
                            message = "Extraction failed, general death";
                        } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                            status = false;
                            message = "Extraction failed, moved to dead-letter path";
                        }
                        segment.saveSegment(`Job close: ${message}`);
                        segment.saveSegmentFailure(`Job close: ${message}`, uniqueFailLogName);

                        let failureDirectory = process.cwd() + '/logs/DealerTrack/failure/';
                        let failurelogFile = failureDirectory + uniqueFailLogName + '.log';

                        var groupName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageGroupCode : '';
                        var storeName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageStoreCode : '';

                        var mailTemplateReplacementValues = {
                            dmsType: constants.JOB_TYPE,
                            processTypes: constants.PROCESS_JSON.JOB_NAME,
                            subject: `Extraction Job for ${groupName} - ${storeName} Completed`,
                            warningObj: warningObj,
                            thirdPartyUsername: enterpriseCode,
                            storeCode: storeCode,
                            groupCode: groupCode
                        };
                        var mailBody = {
                            fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                            toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                            ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                            attachedfailurelogFile:failurelogFile
                        }
                        
                        if (status) {
                            // Send notification
                            var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.DEALERTRACK.JOB_EXTRACT_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Success';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure('Extraction status: Extraction completed', uniqueFailLogName);
                            // Send notification after  Dealertrack extraction job completed
                            mailSender.sendMail(mailBody, constants.DEALERTRACK.JOB_NAME);
                        } else {
                            // Send notification
                            mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                            mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                            var displayMessage = `Failed ${constants.JOB_TYPE} ${constants.DEALERTRACK.JOB_EXTRACT_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.subject = `Extraction Job for ${groupName} - ${storeName} Failed`;
                            mailTemplateReplacementValues.status = 'Failed';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            if(unsubscribedDms) {
                                const UserInput = {
                                inLaborProjectId :inLaborProjectId,
                                inLaborProjectType :inLaborProjectType,
                                inPartsProjectType:inPartsProjectType,
                                inPartsProjectId:inPartsProjectId,                               
                                inIsInSales :true,
                                inSalesComment : appConstants.SALES_TAG_COMMENT,                               
                                inUpdatedBy :userName
                              }
                              segment.saveSegment(`DEALERTRACK Extraction - sales tag creation: ${JSON.stringify(UserInput)}`);
                            try {
                                const SendSalesTagDetails = await manageScheduleField.sendNotificationCall(UserInput,constants.DEALERTRACK.JOB_EXTRACT_NAME);
                                segment.saveSegment(`DEALERTRACK Extraction - sales tag creation: ${JSON.stringify(SendSalesTagDetails)}`);
                              } catch (salesError) {                                
                                segment.saveSegment(`DEALERTRACK Extraction - sales tag creation Error: ${salesError}`);
                              }
                            } 

                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure('Extraction status: Extraction failed', uniqueFailLogName);
                            // Send notification for failed Dealertrack extraction
                            await segment.sleep(2000);
                            mailSender.sendMail(mailBody, constants.DEALERTRACK.JOB_NAME);

                            // Portal update for extraction failed
                            let todayDate;
                            let attPayload = {};
                            let projectID;
                            let secondProjectID;
                            let inpObjProject;
                            let inpObjSecondProject;
                            let secondProjectIdList;
                            let projectIdList;
                            try{
                            todayDate = new Date().toISOString().slice(0, 10);
                            attPayload = att;
                            projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                            projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                            secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                            segment.saveSegment(`DealerTrack : projectIdList - ${projectIdList}`);
                            segment.saveSegment(`DealerTrack : secondProjectIdList - ${secondProjectIdList}`);
                            attPayload['inProjectId'] =  projectID;

                            secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                            attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                            attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                            attPayload.in_retrive_ro_request_on = todayDate;
                           
                            console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                            console.log("projectId List!!!!!!!!!!!!!!!!!!!",projectIdList);
                            console.log("second List!!!!!!!!!!!!!!!!!!!",secondProjectIdList);
                            if(secondProjectIdList.length>0){
                                for(let i=0;i<secondProjectIdList.length;i++){
                                    if(secondProjectIdList[i]!=undefined && secondProjectIdList[i]!= ''){
                                        inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList[i], attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                        console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                                        portalUpdate.doPayloadAction(inpObjSecondProject);
                                    }
                                   
                                }
                               
                            }

                            if(projectIdList.length>0){
                                for(let i=0;i<projectIdList.length;i++){
                                    if(projectIdList[i]!=undefined && projectIdList[i]!=''){
                                              
                                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIdList[i], attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                        segment.saveSegment(`DealerTrack : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                                        portalUpdate.doPayloadAction(inpObjProject);
                                    }
                             
                                }
                               
                            }


                            } catch(err){
                            console.log(JSON.stringify(err));
                            segment.saveSegment(`DealerTrack : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                            }
                            segment.saveSegment(`DealerTrack : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                            segment.saveSegment(`DealerTrack : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                    
                            //code end for portal update for extraction failed
                        }

                        att.endTime = new Date(moment().utc());
                        att.uniqueId ? att.uniqueId : util.generateUniqueId();
                        att.status = status;
                        att.message = message;
                        var oldStoreArray = job.attrs.data.storeDataArray;
                        var newStoreArray = [att];
                        oldStoreArray.map(data => {
                            if (data.enterpriseCode === newStoreArray[0].enterpriseCode) {
                                data = newStoreArray;
                            }
                        });
                        var _storeArray = oldStoreArray;
                        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                        job.attrs.data.storeDataArray = _storeArray;
                        //     if(processFileName){
                        //     att.processFileName = processFileName.trim();
                        // }else{
                        //     processFileName = null;
                        // }
                        await job.save();
                        console.log(`Dealertrack : Extraction process for store ${att.mageStoreCode} exited code ${code}`);
                        segment.saveSegment(`Dealertrack : Extraction process for store ${att.mageStoreCode} exited code ${code}`);
                        i++;
                        if (i < storeDataArray.length) {
                            // Check time frame
                            if (att.runNow) {
                                segment.saveSegment(`Dealertrack : runNow`);
                                await extract(storeDataArray[i], job);
                            } else if (util.checkExtractTimeFrame()) {
                                segment.saveSegment(`Dealertrack : Check time frame and start extraction ${JSON.stringify(storeDataArray[i])}`);
                                await extract(storeDataArray[i], job);
                            } else {
                                const newDataArray = storeDataArray;
                                try {
                                    DealertrackJobManager.scheduleDealertrackExtractJob(DealertrackJobManager.createScheduleObject(job, newDataArray.slice(i)), true);
                                    job.attrs.data.storeDataArray = storeDataArray.slice(0, i);
                                    job.fail(new Error(`Dealertrack : Time exceeded, remaining stores scheduled to next day.`));
                                    segment.saveSegment(`Dealertrack : Time exceeded, remaining stores scheduled to next day.`);
                                    await job.save();
                                    //done();
                                } catch (error) {
                                    console.error(error);
                                    segment.saveSegment(`Error : ${error.toString()}`);
                                }
                            }
                        } else {
                            done();
                        }
                    });
                  }
                } else {
                    console.error("Dealertrack : Store data Extraction attributes not defined");
                    segment.saveSegment("Dealertrack : Store data Extraction attributes not defined");
                }
            }
            if (att.runNow) { // Check whether it is for run now or not, if yes, no need to check time frame
                segment.saveSegment(`Dealertrack : runNow : Check whether it is for run now or not, if yes, no need to check time frame ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else if (util.checkExtractTimeFrame()) {
                segment.saveSegment(`Dealertrack : Check time frame and start extraction ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else { // Auto schedule full Group wise schedule for tomorrow
                segment.saveSegment(`Dealertrack : Auto schedule full Group wise schedule for tomorrow`);
                DealertrackJobManager.scheduleDealertrackExtractJob(DealertrackJobManager.createScheduleObject(job), true);
                job.remove();
            }
        });

    agenda.on("start", job => {
        console.log(`Dealertrack : Job ${job.attrs.name}_${job.attrs._id} starting`);
    });

    agenda.on("complete", job => {
        console.log(`Dealertrack : Job ${job.attrs.name}_${job.attrs._id} finished`);
    });

    agenda.on("fail", (err, job) => {
        console.log(`Dealertrack : Job ${job.attrs.name}_${job.attrs._id} failed with error: ${err.message} `);
    });
    return agenda;
}

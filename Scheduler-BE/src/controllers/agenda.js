const Agenda = require("agenda");
const path = require("path");
const fs = require("fs");

/**
 * Configuration file (.env) path
 */
const ENV_PATH = path.join(process.env.HOME + "/.scheduler/.env");
if (!fs.existsSync(ENV_PATH)) {
 console.error(
  `Configuration file ${ENV_PATH} not accessible or exist; Please run  ../install script`
 );
 process.exit(1);
}
const result = require("dotenv").config({ path: ENV_PATH });
if (result.error) {
 throw result.error;
}
const env = process.env;
console.log("Mongo USER", env.MONGO_USER);
console.log("MONGO PASS", env.MONGO_PASS);
console.log("env.MONGO_HOST}", env.MONGO_HOST);
const MONGO_HOST='localhost';
const MONGO_PORT='27017';
const MONGO_DB='agenda';
const MONGO_USER='agenda';
const MONGO_PASS='agenda123';
// env.MONGO_HOST = "***********";
//const MONGO_DB_CONN = `mongodb://${env.MONGO_USER}:${env.MONGO_PASS}@${env.MONGO_HOST}:/${env.MONGO_DB}`;
const MONGO_DB_CONN = `mongodb://${MONGO_USER}:${MONGO_PASS}@${MONGO_HOST}:/${MONGO_DB}`;

//const MONGO_DB_CONN = `mongodb://localhost:27017`;

const agenda = new Agenda({
 db: { address: MONGO_DB_CONN, options: { useNewUrlParser: true , useUnifiedTopology: true } },
});

module.exports = agenda;

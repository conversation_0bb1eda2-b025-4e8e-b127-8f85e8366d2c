const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeFortellisProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "fortellis-process-json") {
      initializeFortellisProcessJSON = true
    }
    if (type.split('-')[0] == 'fortellis') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the fortellis-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeFortellisProcessJSON) {
    try {
      // Initialize Fortellis Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("Fortellis :  Process JSON schedule started");
      segment.saveSegment("Fortellis :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("Fortellis Extraction Processing Not Enabled - Pass Job Type fortellis-process-json To Enable");
    segment.saveSegment("Fortellis Extraction Processing Not Enabled - Pass Job Type fortellis-process-json To Enable");
  }
  return true;
}

const FortellisJobManager = require("../FortellisJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all Fortellis-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllFortellisExtractJobs(_, args) {
      return FortellisJobManager.getAllFortellisExtractJobs();
    },

    /**
     * Query to get all Fortellis Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllFortellisProcessJSONJobs(_, args) {
      return FortellisJobManager.getAllFortellisProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule Fortellis-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleFortellisExtractJob(_, args) {
      return FortellisJobManager.scheduleFortellisExtractJob(args.input);
    },

    /**
     * Mutation to run a Fortellis-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowFortellisExtractJobByStore(_, args) {
      return FortellisJobManager.runNowFortellisExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a Fortellis-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelFortellisExtractJobByStore(_, args) {
      return FortellisJobManager.cancelFortellisExtractJobByStore(args.input);
    },
    
        /**
     * Mutation to create a Process XML job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */


    createProxyWithSqlDump(_, args) {
      return FortellisJobManager.createProxyWithSqlDump(args.input);
    },
  },


  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;

var gt = require("graphql-tools");
var resolvers = require("./resolver");

var typeDefs = `

# This type specifies the entry points into our API

#Scalar types
scalar Date
scalar DateTime
scalar _bsontype

# Type of Fortellis extract Job
enum JobType {
  initial
  refresh
  ondemand
}

# close RO options
enum ClosedROOption {
  all
  monthly
  weekly
  current
}

input SingleStoreJobData{
  groupName: String!
  storeData: ExtractData!
}

type Data{
  groupName: String # Optional
  storeDataArray: [StoreData]
}

type QueueData{
  storeID: String,
  fileToProcess: String,
  priority:String
}

type JSONProcessData {
  operation: String # Avoid operation = recheck or operation = start
  inputFile: String
  storeID: String
  outputFile: String
  status: Boolean
  message: String
  createdAt: String
  miscExceptionCount: Int
  address:String
  processorUniqueId:String
}

type StoreData {
  dealerId: String!
  projectId: String
  secondProjectId: String
  glAccountCompanyID: String
  mageManufacturer: String
  solve360Update: Boolean
  buildProxies: Boolean
  userName: String
  startDate: Date!
  endDate: Date!
  zipPath: String
  closedROOption: ClosedROOption
  startTime: String
  endTime:String
  status: Boolean
  message: String
  jobType: JobType
  mageGroupCode: String
  mageStoreCode: String
  stateCode: String
  dealerAddress: String
  projectIds:String
  secondProjectIdList:String
  companyIds:String
  testData:Boolean
  companyObj:String
  processFileName:String
  brands:String
}

type ExtractJob{
  timeFrameZone: String
  timeFrameStartTime: String
  timeFrameEndTime:String
  poolTime:Int
  jobArray: [Job]
}

type Job {
  name: String
  data:Data
  type: String
  priority: String
  nextRunAt: String
  _id: _bsontype
  lastModifiedBy: String
  lockedAt: String
  lastRunAt: String
  lastFinishedAt: String
  running: Boolean
  scheduled: Boolean
  queued: Boolean
  completed: Boolean
  failed: Boolean
  repeating: Boolean
  failReason: String
}

type ProcessJSONJob {
  name: String
  data:JSONProcessData
  type: String
  priority: String
  nextRunAt: String
  _id: _bsontype
  lastModifiedBy: String
  lockedAt: String
  lastRunAt: String
  lastFinishedAt: String
  running: Boolean
  scheduled: Boolean
  queued: Boolean
  completed: Boolean
  failed: Boolean
  repeating: Boolean
  failReason: String
  uploadStatus: Boolean
}

# Input to pass custom JSON data to a schedule
input JobData{
  groupName: String!
  storeDataArray: [ExtractData]!
}

input ExtractData {
  dealerId: String!
  projectId: String
  secondProjectId: String
  glAccountCompanyID: String
  mageManufacturer: String
  solve360Update: Boolean
  buildProxies: Boolean
  userName: String
  startDate: Date!
  endDate: Date!
  zipPath: String
  closedROOption: ClosedROOption
  jobType: JobType
  mageGroupCode: String!
  mageStoreCode: String!
  stateCode: String!
  dealerAddress: String
  projectIds:String
  secondProjectIdList:String
  companyIds:String
  testData:Boolean
  companyObj:String
  projectType:String
  secondaryProjectType:String
  groupCode:String
  mageStoreName:String
  errors: String
  thirdPartyUsername: String
  assignedtoCn: String
  brands:String
}

input FortellisScheduleInput{
  jobSchedule: DateTime!
  jobData: JobData!
}

input CancelFortellisScheduleInput{
  jobSchedule: DateTime!
  jobData: SingleStoreJobData!
}

#Input to run a particular Fortellis Store's  extraction
input RunNowFortellisScheduleInput {
  jobSchedule: DateTime!
  jobData: SingleStoreJobData!
}
input ProxyGenerationUsingPgDump{
  zipPath: String!

}

input CreateProxyInput{
  proxyJobData: ProxyGenerationUsingPgDump!
}

# Represents the Status of each Mutations
type Status {
  status: Boolean!
  message: String!
  job: Job
}

type FortellisProcessJSONInfo{
  processJSONJobsQueue: [QueueData],
  processJSONJobs: [ProcessJSONJob]
}

type Query {
  getAllFortellisProcessJSONJobs: FortellisProcessJSONInfo
  getAllFortellisExtractJobs: ExtractJob
}

# Mutations
type Mutation {
  # Mutation to schedule a Fortellis extraction job
  scheduleFortellisExtractJob (input: FortellisScheduleInput!): Status,
  cancelFortellisExtractJobByStore (input: CancelFortellisScheduleInput!): Status,
  runNowFortellisExtractJobByStore (input: RunNowFortellisScheduleInput!): Status,
  createProxyWithSqlDump (input: CreateProxyInput!): Status
}

schema {
  query: Query,
  mutation: Mutation
}
`;
var schema = gt.makeExecutableSchema({ typeDefs, resolvers });
module.exports = schema;

var gt = require("graphql-tools");
var resolvers = require("./resolver");

var typeDefs = `

# This type specifies the entry points into our API

#Scalar types
scalar Date
scalar DateTime
scalar _bsontype

# Type of Reynolds extract Job
enum JobType {
  initial
  refresh
  ondemand
}

# close RO options
enum ClosedROOption {
  all
  monthly
  weekly
  current
}

input SingleStoreJobData{
  groupName: String!
  storeData: ExtractData!
}

type Data{
  groupName: String # Optional
  storeDataArray: [StoreData]
}

type QueueData{
  storeID: String,
  fileToProcess: String
  priority:String
}

type JSONProcessData {
  operation: String # Avoid operation = recheck or operation = start
  inputFile: String
  inputFilePath: String
  storeID: String
  outputFile: String
  status: Boolean
  message: String
  createdAt: String
  invalidmiscpaytypeCount:Int
  estimateCount:Int
  punchTimeMissingCount:String
  inputFilePath1:String
  suffixedInvoicesCount:Int
  suffixedInvoicesCsvData:String
  processorUniqueId:String

  
 
}

type StoreData {
  locationId: String!
  sourceId: String!
  activityStoreId: String
  projectId: String
  secondProjectId: String
  mageManufacturer: String
  solve360Update: Boolean
  buildProxies: Boolean
  userName: String
  inputFilePath: String
  fileDate:String
  invoiceMasterCSVFilePath: String
  switchBranch: Boolean
  customBranchName: String
  etlDMSType: String 
  startDate: Date!
  endDate: Date!
  zipPath: String
  closedROOption: ClosedROOption
  startTime: String
  endTime:String
  status: Boolean
  message: String
  jobType: JobType
  mageGroupCode: String
  mageStoreCode: String
  stateCode: String
  projectIds:String
  secondProjectIdList:String
  companyIds:String
  parentName:String
  testData:String
  companyObj:String
  isCombinedAll:Boolean
  brands:String
}

type ExtractJob{
  timeFrameZone: String
  timeFrameStartTime: String
  timeFrameEndTime:String
  poolTime:Int
  jobArray: [Job]
}

type Job {
  name: String
  data:Data
  type: String
  priority: String
  nextRunAt: String
  _id: _bsontype
  lastModifiedBy: String
  lockedAt: String
  lastRunAt: String
  lastFinishedAt: String
  running: Boolean
  scheduled: Boolean
  queued: Boolean
  completed: Boolean
  failed: Boolean
  repeating: Boolean
  failReason: String
}

type ProcessJSONJob {
  name: String
  data:JSONProcessData
  type: String
  priority: String
  nextRunAt: String
  _id: _bsontype
  lastModifiedBy: String
  lockedAt: String
  lastRunAt: String
  lastFinishedAt: String
  running: Boolean
  scheduled: Boolean
  queued: Boolean
  completed: Boolean
  failed: Boolean
  repeating: Boolean
  failReason: String
  uploadStatus: Boolean
  processorUniqueId:String
}

# Input to pass custom JSON data to a schedule
input JobData{
  groupName: String!
  storeDataArray: [ExtractData]!
}

input ExtractData {
  locationId: String!
  sourceId: String!
  activityStoreId: String
  projectId: String
  secondProjectId: String
  mageManufacturer: String
  solve360Update: Boolean
  buildProxies: Boolean
  userName: String
  inputFilePath: String!
  fileDate:String
  invoiceMasterCSVFilePath: String
  switchBranch: Boolean
  customBranchName: String
  etlDMSType: String
  startDate: Date!
  endDate: Date!
  zipPath: String
  closedROOption: ClosedROOption
  jobType: JobType
  mageGroupCode: String!
  mageStoreCode: String!
  stateCode: String!
  projectIds:String
  secondProjectIdList:String
  companyIds:String
  parentName:String
  testData:Boolean
  companyObj:String
  projectType:String
  secondaryProjectType:String
  groupCode:String
  mageStoreName:String
  errors: String
  thirdPartyUsername: String
  assignedtoCn: String
  isCombinedAll:Boolean
  brands:String
}

input ReynoldsScheduleInput{
  jobSchedule: DateTime!
  jobData: JobData!
}

input CancelReynoldsScheduleInput{
  jobSchedule: DateTime!
  jobData: SingleStoreJobData!
}

#Input to run a particular Reynolds Store's  extraction
input RunNowReynoldsScheduleInput {
  jobSchedule: DateTime!
  jobData: SingleStoreJobData!
}

# Represents the Status of each Mutations
type Status {
  status: Boolean!
  message: String!
  job: Job
}

type ProcessJSONInfo{
  processJSONJobsQueue: [QueueData],
  processJSONJobs: [ProcessJSONJob]
}

type Query {
  getAllReynoldsProcessJSONJobs: ProcessJSONInfo
  getAllReynoldsExtractJobs: ExtractJob
}

# Mutations
type Mutation {
  # Mutation to schedule a Reynolds extraction job
  scheduleReynoldsExtractJob (input: ReynoldsScheduleInput!): Status,
  cancelReynoldsExtractJobByStore (input: CancelReynoldsScheduleInput!): Status,
  runNowReynoldsExtractJobByStore (input: RunNowReynoldsScheduleInput!): Status,
}

schema {
  query: Query,
  mutation: Mutation
}
`;
var schema = gt.makeExecutableSchema({ typeDefs, resolvers });
module.exports = schema;

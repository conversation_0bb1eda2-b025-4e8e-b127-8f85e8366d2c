const ReynoldsJobManager = require("../ReynoldsJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all Reynolds-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllReynoldsExtractJobs(_, args) {
      return ReynoldsJobManager.getAllReynoldsExtractJobs();
    },

    /**
     * Query to get all Automate Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllReynoldsProcessJSONJobs(_, args) {
      return ReynoldsJobManager.getAllReynoldsProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule Reynolds-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleReynoldsExtractJob(_, args) {
      return ReynoldsJobManager.scheduleReynoldsExtractJob(args.input);
    },

    /**
     * Mutation to run a Reynolds-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowReynoldsExtractJobByStore(_, args) {
      return ReynoldsJobManager.runNowReynoldsExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a Reynolds-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelReynoldsExtractJobByStore(_, args) {
      return ReynoldsJobManager.cancelReynoldsExtractJobByStore(args.input);
    },
  },
  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;

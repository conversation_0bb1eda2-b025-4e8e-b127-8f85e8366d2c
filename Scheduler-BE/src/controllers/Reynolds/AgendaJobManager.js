const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeReynoldsProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "reynolds-process-json") {
      initializeReynoldsProcessJSON = true
    }
    if (type.split('-')[0] == 'reynolds') {
      console.log('Reynolds extract job call');
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the reynolds-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeReynoldsProcessJSON) {
    try {
      // Initialize Reynolds Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("Reynolds :  Process JSON schedule started");
      segment.saveSegment("Reynolds :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("Reynolds Extraction Processing Not Enabled - Pass Job Type reynolds-process-json To Enable");
    segment.saveSegment("Reynolds Extraction Processing Not Enabled - Pass Job Type reynolds-process-json To Enable");
  }
  return true;
}

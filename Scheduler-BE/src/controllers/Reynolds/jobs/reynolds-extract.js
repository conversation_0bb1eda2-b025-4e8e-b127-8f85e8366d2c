"use strict";

const constants = require("../constants");
const util = require("../util");
const ReynoldsJobManager = require("../ReynoldsJobManager");
const { spawn } = require("child_process");
const moment = require("moment-timezone");
const gunzip = require("gunzip-file");
const commonUtil = require('../../../../src/common/util');
const fs = require("fs");
const segment = require("../../SEGMENT/Reynolds/segmentManager");
const XLSX = require('xlsx');
const { v4: uuidv4 } = require('uuid');
/**
 * Function to find the unique stores in an array of stores
 *
 *
 */

var store,
  filePathArray,
  filePath,
  storeIdentification,
  fileList = [];

const unZipWorkDirectory = constants.UN_ZIP_WORK_DIRECTORY;
const fileName = constants.FILE_NAME;
const fileExtension = constants.FILE_EXTENSION;

Array.prototype.unique = function () {
  var a = this.concat();
  for (var i = 0; i < a.length; ++i) {
    for (var j = i + 1; j < a.length; ++j) {
      if (a[i].enterpriseCode === a[j].enterpriseCode) a.splice(j--, 1);
    }
  }
  return a;
};

function unzipFiles(index, cb) {
  if (index < fileList.length) {
    unzipFile(fileList[index], index, cb);
  } else {
    segment.saveSegment(`Reynolds:Unzip Completed done!`);
    console.log("Completed");
    cb({ status: true, response: "unzip completed" });
  }
}

function unzipFile(data, index, cb) {
  console.log("file name:", data.filename);
  segment.saveSegment(`Reynolds:file name: : ${data.filename}`);
  let unZipFilepath;
  unZipFilepath = filePath + "/" + data.filename;
  try {
    if (fs.existsSync(unZipFilepath)) {
      // Do something
      gunzip(
        unZipFilepath,
        `${unZipWorkDirectory}/${fileName}${index}.${fileExtension}`,
        () => {
          segment.saveSegment(`Reynolds:gunzip done! : ${unZipFilepath}`);
          console.log("gunzip done!");
          index++;
          unzipFiles(index, cb);
        }
      );
    } else {
      segment.saveSegment(`Reynolds:${unZipFilepath} not exist`);
    }
  } catch (err) {
    segment.saveSegment(
      `Reynolds:gunzip have error : ${unZipFilepath} : ${JSON.stringify(err)}`
    );
    console.log(err);
  }
}

function unGunzipWebhookInputFiles(inputFilePath,fileDate,isCombinedAll) {
  return new Promise((resolve, reject) => {
    fileList = [];
    store = inputFilePath;
    filePathArray = store.split("/");
    console.log(filePathArray);
    storeIdentification = store.split("/").reverse()[0];
    console.log("storeIdentification:", storeIdentification);
    segment.saveSegment(
      `Reynolds:storeIdentification : ${storeIdentification}`
    );
    let branchName = storeIdentification.split('_')[3]
    filePathArray.shift();
    filePathArray.pop();
    filePath = "/" + filePathArray.join("/");
    console.log(filePath);
    segment.saveSegment(`Reynolds:filePath : ${filePath}`);

    if (fs.existsSync(filePath)) {
      // console.log("Directory exists.");
      fs.readdirSync(filePath).forEach((file) => {
        let test1 = file.split('-')

        let branchOfFile = test1[0].split('_')[7];
        
        let dat, date, dateLabel, len,dateOfFile;

        dat = file.split('_');
                 // date = dat.reverse()[0].substring(0, 8);
     
                 if (dat) {
                   len = dat.length - 1;
                 }
                 if (len) {
                   date = dat[len].substring(0, 8);
                 }
                 if (date) {
                   dateLabel =
                     date.substring(4, 6) +
                     '/' +
                     date.substring(6, 8) +
                     '/' +
                     date.substring(0, 4);
                 }
     
                 dateOfFile =
                   dat[4] +
                   '__' +
                   dat[6] +
                   '_' +
                   dat[7] +
                   '_' +
                   dat[8] +
                   '-' +
                   dateLabel;

          dateOfFile = dateOfFile.split('-')[1];
          
        //   console.log(file);
        if(isCombinedAll){
          if (file.includes(storeIdentification)) {
            if(branchOfFile == branchName){
                fileList.push({ filename: file });     
            }
           
          }
          console.log("Combined all filename$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",fileList);
          
        }else{
          fileDate = fileDate.split('[')[0]
          if (file.includes(storeIdentification)) {
            if(branchOfFile == branchName){
              if(dateOfFile == fileDate){
                fileList.push({ filename: file });
              }
             
            }
           
          }
          console.log("Non Combined all filename$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",fileList);
        }
     



      });
      // console.log(fileList);
    } else {
      segment.saveSegment(`Reynolds:${filePath} Directory not exist`);
      console.log("Directory not exist");
      resolve({ status: false, response: "Directory not exist" });
    }
    let index = 0;
    unzipFiles(index, (response) => {
      if (response.status) {
        segment.saveSegment(`Reynolds:unzip webhook input files completed`);
        resolve({
          status: true,
          response: "unzip webhook input files completed",
        });
      } else {
        segment.saveSegment(`Reynolds:Error in unzip webhook input files`);
        resolve({
          status: false,
          response: "Error in unzip webhook input files",
        });
      }
    });
  });
}

/**
 * Function to perform Reynolds-Extract
 */
module.exports = function ReynoldsExtractJOB(agenda) {
  var storeCode = "";
  store = "";
  filePathArray = "";
  filePath = "";
  storeIdentification = "";
  fileList = [];
  var isCombinedAll = false;
  var fileDate;
  console.log(
    `Reynolds-Extract job started: JobName: ${constants.REYNOLDS.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.REYNOLDS.CONCURRENCY}`
  );
  segment.saveSegment(
    `Reynolds-Extract job started: JobName: ${constants.REYNOLDS.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.REYNOLDS.CONCURRENCY}`
  );
  agenda.define(
    constants.REYNOLDS.JOB_NAME,
    {
      priority: constants.JOB_PRIORITY.HIGHEST,
      concurrency: constants.REYNOLDS.CONCURRENCY,
    },
    async (job, done) => {
      const att = job.attrs.data;
      const storeDataArray = att.storeDataArray.reverse();
      var i = 0;
      async function extract(att, job) {
        let inputFilePath, invoiceMasterFilePath,fileDate;
        inputFilePath = att.inputFilePath;
        fileDate = att.fileDate;
        let userName = att.userName;
        isCombinedAll = att.isCombinedAll;
        console.log("inputFilePath:", inputFilePath);
        if (att.hasOwnProperty("invoiceMasterCSVFilePath")) {
          invoiceMasterFilePath = att.invoiceMasterCSVFilePath
            ? att.invoiceMasterCSVFilePath
            : "";
        } else {
          invoiceMasterFilePath = "";
        }
        let fileExtention = invoiceMasterFilePath.split('.')[1]
      if(fileExtention == 'xlsx'){
          let fileName = invoiceMasterFilePath.split('/')[5].split('.')[0]
          console.log('filename',fileName);
          let filePath ='/etl/etl-vagrant/etl-reynolds/reynolds-inv/'
           const workbook = XLSX.readFile(invoiceMasterFilePath);
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];
          const csvData = XLSX.utils.sheet_to_csv(worksheet);
          fs.writeFileSync(`${filePath}${fileName}.csv`, csvData, 'utf-8');
          invoiceMasterFilePath = `${filePath}${fileName}.csv`;

        }
        storeCode = att.mageStoreCode;
        att.invoiceMasterCSVFilePath = invoiceMasterFilePath;
        segment.saveSegment(
          `Reynolds: Extraction Job Started: ${JSON.stringify(att)}`
        );
        segment.saveSegmentFailure(
          `Reynolds: Extraction Job Started: ${JSON.stringify(att)}`,
          storeCode
        );
        if (att.locationId && att.startDate && att.endDate) {
          if (
            !fs.existsSync(
              constants.REYNOLDS_DEADLETTER_DIR_PREFIX + "-extracted"
            )
          ) {
            fs.mkdirSync(
              constants.REYNOLDS_DEADLETTER_DIR_PREFIX + "-extracted"
            );
          }
          segment.saveSegment(
            `Reynolds: Extraction Job Started: ${JSON.stringify(att)}`
          );
          segment.saveSegmentFailure(
            `Reynolds: Extraction Job Started: ${JSON.stringify(att)}`,
            storeCode
          );

          let actualFileName;
          actualFileName = inputFilePath.split("/").reverse()[0];
          console.log("actualFileName:", actualFileName);
          segment.saveSegmentFailure(
            `Reynolds: ActualFileName: ${actualFileName}`,
            storeCode
          );

          var startTime = new Date(moment().utc());
          att.startTime = startTime;
          var oldStoreArray = job.attrs.data.storeDataArray;
          var newStoreArray = [att];
          oldStoreArray.map((data) => {
            if (
              data.locationId === newStoreArray[0].locationId &&
              data.mageStoreCode === newStoreArray[0].mageStoreCode
            ) {
              data = newStoreArray;
            }
          });
          var _storeArray = oldStoreArray;
          // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
          job.attrs.data.storeDataArray = _storeArray;
          segment.saveSegment(
            `Extraction Job Data: ${JSON.stringify(_storeArray)}`
          );
          segment.saveSegmentFailure(
            `Extraction Job Data: ${JSON.stringify(_storeArray)}`,
            storeCode
          );
          att.uniqueId = util.generateUniqueId();
          await job.save();
          // process.stdin.pipe(child.stdin)
          let branchNo = inputFilePath.split('_')[2];
          let storeNo = inputFilePath.split('_')[3];
          var compareString = "";
          var status = true;
          var message = "n/a";

          let modifiedFileName, replacedFileName;
          replacedFileName =
            att.mageGroupCode.trim().replace(/ /g, "_") +
            "-" +
            att.mageStoreCode +
            "-" +
            att.stateCode +
            "-" +
            "WEBHOOK" +
            "-" +
            att.locationId +
            "-" +
            "_" +
            storeNo +
            "_" +
            branchNo +
            "_" +
            moment().format("YYYYMMDDhhmmss") +
            ".zip";

          await unGunzipWebhookInputFiles(inputFilePath, fileDate,isCombinedAll);
          //  await unzipRawDataFiles();
          //  await zipAndCombineInputFiles();
          //  await transferFileToProcessorApplication();

          modifiedFileName =
            constants.REYNOLDS_SCHEDULER_ETI_DIR + "/" + actualFileName;

          console.log("modifiedFileName:", modifiedFileName);
          segment.saveSegmentFailure(
            `Modified File Name: ${modifiedFileName}`,
            storeCode
          );
          console.log("replacedFileName:", replacedFileName);
          att.processFileName = replacedFileName;
          await job.save();
          
          segment.saveSegmentFailure(
            `Replaced File Name: ${replacedFileName}`,
            storeCode
          );

          //To transfer combined input to processor directory
          try {
            var stdErrorArray;
            let processFilePath = process.cwd() + "/bash_script";
            console.log("processFilePath:", processFilePath);
            const transferInputToProcessor = spawn(
              "bash",
              [
                "transfer-to-processor-input",
                unZipWorkDirectory,
                inputFilePath,
                invoiceMasterFilePath,
                constants.REYNOLDS_SCHEDULER_ETI_DIR,
                replacedFileName,
              ],
              {
                cwd: processFilePath,
                env: Object.assign({}, process.env, {
                  PATH: process.env.PATH + ":/usr/local/bin",
                }),
              }
            ).on("error", function (err) {
              console.log("error :", err);
              segment.saveSegment(`error: ${err}`);
            });

            process.stdin.pipe(transferInputToProcessor.stdin);

            transferInputToProcessor.stdout.on("data", async (data) => {
              console.log(`stdout: ${data}`);
            });

            transferInputToProcessor.stderr.on("data", async (data) => {
              console.log(`stderr: ${data}`);
              stdErrorArray += data.toString() + " ";
              segment.saveSegment(`stderr: ${data}`);
            });

            transferInputToProcessor.on("close", async (code) => {
              console.log("code:", code);
            let processFilePath = `/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/${att.processFileName}`;
            segment.saveSegment(`processCompanyData ...999 job:${job}userName:${userName}processFileName:${att.processFileName}destinationFile : ${processFilePath}`);            
            await commonUtil.processCompanyData(job, userName, att.processFileName, processFilePath, constants.DMS);
              console.log(`child process exited with code ${code}`);
              segment.saveSegment(`Job close: ${message}`);
              att.endTime = new Date(moment().utc());
              att.uniqueId = att.uniqueId ? att.uniqueId : util.generateUniqueId();
              att.status = status;
              att.message = message;

              var oldStoreArray = job.attrs.data.storeDataArray;
              var newStoreArray = [att];
              oldStoreArray.map((data) => {
                if (data.dealerId === newStoreArray[0].dealerId) {
                  data = newStoreArray;
                }
              });
              var _storeArray = oldStoreArray;
              job.attrs.data.storeDataArray = _storeArray;
              await job.save();
              console.log(
                `Reynolds : Extraction process for store ${att.mageStoreCode} exited code ${code}`
              );
              segment.saveSegment(
                `Reynolds : Extraction process for store ${att.mageStoreCode} exited code ${code}`
              );
              i++;
              if (i < storeDataArray.length) {
                // Check time frame
                if (att.runNow) {
                  segment.saveSegment(`Reynolds : runNow`);
                  await extract(storeDataArray[i], job);
                } else if (util.checkExtractTimeFrame()) {
                  segment.saveSegment(
                    `Reynolds : Check time frame and start extraction ${JSON.stringify(
                      storeDataArray[i]
                    )}`
                  );
                  await extract(storeDataArray[i], job);
                } else {
                  const newDataArray = storeDataArray;
                  try {
                    ReynoldsJobManager.scheduleReynoldsExtractJob(
                      ReynoldsJobManager.createScheduleObject(
                        job,
                        newDataArray.slice(i)
                      ),
                      true
                    );
                    job.attrs.data.storeDataArray = storeDataArray.slice(0, i);
                    job.fail(
                      new Error(
                        `Reynolds : Time exceeded, remaining stores scheduled to next day.`
                      )
                    );
                    segment.saveSegment(
                      `Reynolds : Time exceeded, remaining stores scheduled to next day.`
                    );
                    await job.save();
                    //done();
                  } catch (error) {
                    console.error(error);
                    segment.saveSegment(`Error : ${error.toString()}`);
                  }
                }
              } else {
                done();
              }
            });
          } catch (err) {
            segment.saveSegment(`error: ${JSON.stringify(err)}`);
          }
        } else {
          console.error(
            "Reynolds : Store data Extraction attributes not defined"
          );
          segment.saveSegment(
            "Reynolds : Store data Extraction attributes not defined"
          );
        }
      }
      console.log("att.runNow@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",att.runNow);
      if (att.runNow) {
        // Check whether it is for run now or not, if yes, no need to check time frame
        segment.saveSegment(
          `Reynolds : runNow : Check whether it is for run now or not, if yes, no need to check time frame ${JSON.stringify(
            storeDataArray[0]
          )}`
        );
        await extract(storeDataArray[0], job);
      } else if (true) {
        segment.saveSegment(
          `Reynolds : Check time frame and start extraction ${JSON.stringify(
            storeDataArray[0]
          )}`
        );
        await extract(storeDataArray[0], job);
      } else {
        console.log("Auto schedule full Group wise schedule for tomorrow@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
        // Auto schedule full Group wise schedule for tomorrow
        segment.saveSegment(
          `Reynolds : Auto schedule full Group wise schedule for tomorrow`
        );
        ReynoldsJobManager.scheduleReynoldsExtractJob(
          ReynoldsJobManager.createScheduleObject(job),
          true
        );
        job.remove();
      }
    }
  );

  agenda.on("start", (job) => {
    console.log(`Reynolds : Job ${job.attrs.name}_${job.attrs._id} starting`);
  });

  agenda.on("complete", (job) => {
    console.log(`Reynolds : Job ${job.attrs.name}_${job.attrs._id} finished`);
  });

  agenda.on("fail", (err, job) => {
    console.log(
      `Reynolds : Job ${job.attrs.name}_${job.attrs._id} failed with error: ${err.message} `
    );
  });
  return agenda;
};

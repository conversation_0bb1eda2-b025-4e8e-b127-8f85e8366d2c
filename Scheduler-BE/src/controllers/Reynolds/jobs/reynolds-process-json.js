"use strict";

const constants = require("../constants");
const util = require("../util");
const moment = require("moment-timezone");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const { spawn } = require("child_process");
const path = require('path');
const fs = require("fs");
const segment = require("../../SEGMENT/Reynolds/segmentManager");
const sharePoint = require("../../../routes/sharePoint");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const stripAnsi = require('strip-ansi');
var Agenda = require("../../agenda");
const extractionError = require('../../../../src/common/extractionError');
const { CHART_OF_ACCOUNTS_FILE_UPLOAD_DIRECTORY } = require("../../DealerTrack/constants");
const { Console } = require("console");
var accounting_csv_directory = '/etl/accounting_in/';
const csv=require('csvtojson');
const unZipper = require("unzipper");
const csvParser = require('csv-parser');
const SetProcessJobStatus = require('../../../model/setProcessJobStatus')
const csv1 = require('csv-parser');
/**
 * Function to perform processing of XML file downloaded through Reynolds-Extract job
 */
module.exports = async function ProcessXmlJOB(agenda) {

    var distributeFile = async function (fileName, rerunFlag , updateSolve360Data, warningObj, etlDMSType, jobId) {
        var stdErrorArray;
        var distDir = path.join(process.env.REYNOLDS_DIST_DIR, fileName);
        var etlDir = path.join(process.env.REYNOLDS_ETL_DIR, fileName);
        etlDir = etlDir.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
        var filePath = path.join(process.env.REYNOLDS_BUNDLE_DIR, fileName);
        var DMSType;
        var ETLDIR;
        
        if(etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase()){
            DMSType = constants.JOB_TYPE; 
            ETLDIR = process.env.REYNOLDS_ETL_DIR;  
        } else{
            DMSType = 'UCS'; 
            ETLDIR = process.env.UCS_ETL_DIR;
        }
        const distributeFile = spawn("bash",
            [
                'send-bundle-live-hpdog', filePath , ETLDIR
            ], {
                cwd: constants.PROCESS_JSON.REYNOLDS_DISTRIBUTE_CMD_PATH,
                env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
            }).on('error', function (err) {
                console.log("error :", err);
                segment.saveSegment(`error: ${err}`);
            });
        console.log(`Reynolds: Start processing of distribution`);
        segment.saveSegment(`Reynolds:  processing distribution`);
        process.stdin.pipe(distributeFile.stdin);
        distributeFile.stdout.on("data", async (data) => {
            console.log(`stdout: ${data}`);
            segment.saveSegment(`stdout: ${data}`);
        });

        distributeFile.stderr.on("data", async (data) => {
            console.log(`stderr: ${data}`);
            stdErrorArray += data.toString() + ' ';
            segment.saveSegment(`stderr: ${data}`);
        });

        distributeFile.on("close", async (code) => {
            var message = "n/a";
            var status = false;
            if (code == constants.STATUS_CODE.SUCCESS) {
                status = true;
                message = "Success";
                // await SetProcessJobStatus.setProcessJobStatusForReynolds(locationId,mageStoreCode,message);
            } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                message = "Distribution failed, general death";
                // await SetProcessJobStatus.setProcessJobStatusForReynolds(locationId,mageStoreCode,'Failed');
            } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                message = "Distribution failed";
                // await SetProcessJobStatus.setProcessJobStatusForReynolds(locationId,mageStoreCode,'Failed');
            }
            segment.saveSegment(`close: ${message}`);
            if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_EXIST_CHECK)) {
                status = false;
                message = "Distribution failed. Zip File Must Exist";
                // await SetProcessJobStatus.setProcessJobStatusForReynolds(locationId,mageStoreCode,'Failed');
                segment.saveSegment(message);
            }
            /**
              * Upload files to SharePoint
              */
            if (status) {
                sharePoint.initSharePoint(distDir, DMSType, rerunFlag , updateSolve360Data, warningObj, 0, jobId);//Upload dist directory zip file to sharepoint
            }
            await doNextProcess();
        });
    }

    var doNextProcess = async function () {
        const extractZip = await util.findOldestZipFile(constants.REYNOLDS_SCHEDULER_ETI_DIR);
        if (fs.existsSync(`/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/${extractZip}`)) {
            console.log(`file Name ${extractZip} exists!`);
        } else {
            console.log(`file Name ${extractZip} does not exists`);
        }
        if (extractZip && fs.existsSync(`/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/${extractZip}`)) {
            console.log(`Reynolds : Found one Store extraction > ${extractZip} to process now`);
            segment.saveSegment(`Reynolds : Found one Store extraction > ${extractZip} to process now`);
            try {
                var createdAt = extractZip.slice(0, extractZip.length - 4).split("-").reverse()[0];
                await agenda.now(constants.PROCESS_JSON.JOB_NAME, {
                    inputFile: extractZip,
                    createdAt: createdAt,
                    // accountingCsvFile: accountingCsvFilepath,
                    operation: "json-processing"
                });
                console.log(`Reynolds : Process JSON schedule started with file > ${extractZip}`);
                segment.saveSegment(`Reynolds : Process JSON schedule started with file > ${extractZip}`);
            } catch (error) {
                console.error(error);
            }

        } else {
            console.log(`Reynolds : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            //segment.saveSegment(`Reynolds : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            try {
                await agenda.schedule(`${constants.PROCESS_JSON.TIME_GAP}`, constants.PROCESS_JSON.JOB_NAME, { operation: "recheck" });
                console.log(`Reynolds : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
                //segment.saveSegment(`Reynolds : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
            } catch (error) {
                console.error(error);
            }
        }
    }

    console.log(
        `Reynolds : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`
    );
    segment.saveSegment(`Reynolds : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`);
    agenda.define(constants.PROCESS_JSON.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.PROCESS_JSON.CONCURRENCY,lockLifetime: 3 * 60 * 60 * 1000 },
        async (job, done) => {
            const touch = setInterval(() => job.touch(), 1 * 60 * 1000);
            const att = job.attrs.data;
            var extractZip = null;
            var accountingCsvFile = null;
            var stdErrorArray = [];
            var stdOutArray = [];
            var uniqueFailLogName;

            var inpFile = att.inputFile ? path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, att.inputFile) : '';
            if (att.inputFile && fs.existsSync(inpFile)) {
                if (!fs.existsSync(constants.REYNOLDS_DEADLETTER_DIR_PREFIX + '-processed')) {
                    fs.mkdirSync(constants.REYNOLDS_DEADLETTER_DIR_PREFIX + '-processed');
                }
                extractZip = att.inputFile;
                accountingCsvFile = att.accountingCsvFile;
                let basename = path.basename(extractZip);
                job.attrs.data.storeID = basename.split("-").reverse()[1];
                let storeName = job.attrs.data.storeID;
                var storeCode = storeName;
                let locationId = basename.split("-").reverse()[1];
                let mageGroupCode = basename.split("-")[0];
                let mageStoreCode =  basename.split("-")[1];

                uniqueFailLogName = locationId + '-' + mageStoreCode;

                console.log('Groupname:', mageGroupCode);
                segment.saveSegment(`Groupname : ${mageGroupCode}`);
                console.log('Location Id:', locationId);
                segment.saveSegment(`Location Id: : ${locationId}`);
                console.log('storeName:',mageStoreCode);
                segment.saveSegment(`storeName : ${mageStoreCode}`);

                var jobsTmp = await Agenda.jobs( {
                    $and: [
                        { "data.storeDataArray.locationId": locationId },
                        { "name": constants.REYNOLDS.JOB_NAME} ,
                        {"data.storeDataArray.mageStoreCode":mageStoreCode
                        }
                    ]
                });
                
                var projectId = '';
                var secondProjectId = '';
                var userName = '';
                var solve360Update = '';
                var updateSolve360Data; 
                var buildProxies;
                var extractionId;
                var invoiceMasterCSVFilePath;
                var inputStoreName;
                var etlDMSType; 
                 
                let agendaObject;
                let extractedFileTimeStamp;
                let extractedFileCreationDate;
                let extractedObjectIndex;

                let switchBranch;
                let customBranchName;

                let mageManufacturer = '';  // Initialize with empty string
                let isPorscheStore = false;  // Initialize as false

                let haltIdentifier = false;
                let haltOverRide = false;
                let resumeUser;
                let projectIds;
                let secondProjectIdList;
                let uniqueId;
                let companyIds;
                let companyObj;
                let testData;
                var processorStatus;
                let totalRoCount;
                let brands = [];  // Initialize as empty array
                let fopcStore = false;
                let exceptionTypeCounts = {};
                
                try{
                    extractedFileTimeStamp = basename.split("-").reverse()[0].replace(".zip", "");
                    segment.saveSegment(`extractedFileTimeStamp : ${extractedFileTimeStamp}`);
                    extractedFileCreationDate =  moment(extractedFileTimeStamp, "YYYYMMDDhhmmss").format("YYYY-MM-DD");
                    segment.saveSegment(`extractedFileCreationDate : ${extractedFileCreationDate}`);
                } catch(err){
                    console.log(err);
                    segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                }
                
                segment.saveSegment(`jobsTmp : ${JSON.stringify(jobsTmp)}`);
                segment.saveSegment(`jobsTmp[jobsTmp.length-1] : ${JSON.stringify(jobsTmp[jobsTmp.length-1])}`);
                segment.saveSegment(`Location Id : ${locationId}`);

                if(jobsTmp[jobsTmp.length-1]){
                    if(jobsTmp[jobsTmp.length-1].hasOwnProperty("attrs")){
                        extractionId = jobsTmp[jobsTmp.length-1].attrs._id;
                        try{
                            segment.saveSegment(`jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : ${JSON.stringify(jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray)}`);
                            agendaObject = jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray;
                            agendaObject = agendaObject.filter(function (el) {
                                return el.locationId == locationId && el.mageStoreCode == mageStoreCode;
                            });
                            segment.saveSegment(`agendaObject : ${JSON.stringify(agendaObject)}`);
                            extractedObjectIndex = 0;
                            if(agendaObject.length > 0){ 
                                agendaObject =  agendaObject.sort((a,b) => b.endTime > a.endTime);
                                extractedObjectIndex = agendaObject.findIndex(
                                    obj => moment(obj.endTime, "YYYY-MM-DDTHH:mm:ss.SSS[Z]").format("YYYY-MM-DD") == extractedFileCreationDate
                                );
                            }

                            if(extractedObjectIndex < 0){
                                extractedObjectIndex = 0;
                            }
                            
                            segment.saveSegment(`Sorted agenda object : ${JSON.stringify(agendaObject)}`);
                            segment.saveSegment(`extractedObjectIndex : ${extractedObjectIndex}`);
                            segment.saveSegment(`Extracted agenda object : ${JSON.stringify(agendaObject[extractedObjectIndex])}`);        
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("projectId")){
                                projectId = agendaObject[extractedObjectIndex].projectId;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectId")){
                                secondProjectId = agendaObject[extractedObjectIndex].secondProjectId;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("solve360Update")){
                                solve360Update = agendaObject[extractedObjectIndex].solve360Update;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("buildProxies")){
                                buildProxies = agendaObject[extractedObjectIndex].buildProxies;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("isCombinedAll")){
                                fopcStore = agendaObject[extractedObjectIndex].isCombinedAll;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("includeMetaData")){
                                includeMetaData = agendaObject[extractedObjectIndex].includeMetaData;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("userName")){
                                userName = agendaObject[extractedObjectIndex].userName;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("invoiceMasterCSVFilePath")){
                               invoiceMasterCSVFilePath = agendaObject[extractedObjectIndex].invoiceMasterCSVFilePath;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("inputFilePath")){
                                inputStoreName = agendaObject[extractedObjectIndex].inputFilePath;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("etlDMSType")){
                                etlDMSType = agendaObject[extractedObjectIndex].etlDMSType;
                            } 
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("switchBranch")){
                                switchBranch = agendaObject[extractedObjectIndex].switchBranch;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("customBranchName")){
                                customBranchName = agendaObject[extractedObjectIndex].customBranchName;
                            } 

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("mageManufacturer")){
                                mageManufacturer = agendaObject[extractedObjectIndex].mageManufacturer;
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("haltOverRide")){
                                haltOverRide = agendaObject[extractedObjectIndex].haltOverRide;
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("projectIds")){
                                projectIds = agendaObject[extractedObjectIndex].projectIds;
                                projectIds =  projectIds.split("*");

                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectIdList")){
                                secondProjectIdList = agendaObject[extractedObjectIndex].secondProjectIdList;
                                secondProjectIdList = secondProjectIdList.split("*");
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("uniqueId")){
                                uniqueId = agendaObject[extractedObjectIndex].uniqueId;
                                uniqueId+='-'+Date.now();
                           
                            }


                            if(agendaObject[extractedObjectIndex].hasOwnProperty("companyIds")){
                                console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                                companyIds = agendaObject[extractedObjectIndex].companyIds;
                                companyIds =  companyIds.replace(new RegExp('\\*', 'g'), ',')
                                console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",companyIds);
                                // companyIds = companyIds.replace(/,\s*$/, "");
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("companyObj")){
                                companyObj = JSON.parse(agendaObject[extractedObjectIndex].companyObj);
                                console.log("companyObj?????????????????????????????????????????????????????????",companyObj);
                            }

                               if(agendaObject[extractedObjectIndex].hasOwnProperty("brands")){
                                    console.log("Brands exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                                    brands = agendaObject[extractedObjectIndex].brands.split("*");

                                    console.log("brands exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",brands);
                                }

                                if(brands && brands.length > 1){
                                     const hasPorche = brands.some(item => item.toLowerCase() === 'porche');
                                    if (hasPorche && brands.length > 1) {
                                         mageManufacturer = brands.find(item => item.toLowerCase() !== 'porche');
                                         isPorscheStore = true;
                                       }
                                } else {
                                    if(mageManufacturer == constants.REYNOLDS.PORSCHE_STORE_LABEL){
                                         isPorscheStore = true;
                                   } else{
                                    isPorscheStore = false;
                                 }
                                }

                         if(agendaObject[extractedObjectIndex].hasOwnProperty("testData")){
                                testData = agendaObject[extractedObjectIndex].testData;
                            }
                            
                    
                        } catch(err){
                            console.log(err);
                            segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                        }
                    }
                }

                updateSolve360Data = {projectId:projectId, secondProjectId:secondProjectId, userName:userName, solve360Update:solve360Update, thirdPartyUsername:locationId, storeCode: mageStoreCode, dmsType: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.JOB_TYPE : 'UCS', groupCode:mageGroupCode,projectIds:projectIds,secondProjectIdList:secondProjectIdList,uniqueId:uniqueId,testData:testData,companyObj:companyObj};
                console.log(updateSolve360Data);
                segment.saveSegment(`updateSolve360Data : ${updateSolve360Data}`);

                console.log('projectId:', projectId);
                segment.saveSegment(`projectId : ${projectId}`);

                console.log('secondProjectId:', secondProjectId);
                segment.saveSegment(`secondProjectId : ${secondProjectId}`);

                console.log('userName:', userName);
                segment.saveSegment(`userName : ${userName}`);

                console.log('solve360Update:', solve360Update);
                segment.saveSegment(`solve360Update : ${solve360Update}`);

                console.log('buildProxies:', buildProxies);
                segment.saveSegment(`buildProxies : ${buildProxies}`);

                console.log('fopcStore:', fopcStore);
                segment.saveSegment(`fopcStore : ${fopcStore}`);
                
                console.log('extractionId:', extractionId);
                segment.saveSegment(`extractionId : ${extractionId}`);

                console.log('invoiceMasterCSVFilePath:', invoiceMasterCSVFilePath);
                segment.saveSegment(`invoiceMasterCSVFilePath : ${invoiceMasterCSVFilePath}`);

                console.log('inputStoreName:', inputStoreName);
                segment.saveSegment(`inputStoreName : ${inputStoreName}`);

                console.log('etlDMSType:', etlDMSType);
                segment.saveSegment(`etlDMSType : ${etlDMSType}`);


                console.log('switchBranch:', switchBranch);
                segment.saveSegment(`switchBranch : ${switchBranch}`);

                console.log('customBranchName:', customBranchName);
                segment.saveSegment(`customBranchName : ${customBranchName}`);

                console.log('haltOverRide:', haltOverRide);
                segment.saveSegment(`haltOverRide : ${haltOverRide}`);


                console.log('uniqueId:', uniqueId);
                segment.saveSegment(`uniqueId : ${uniqueId}`);

                console.log('companyIds:', companyIds);
                segment.saveSegment(`companyIds : ${companyIds}`);

                console.log('companyObj:', companyObj);
                segment.saveSegment(`companyObj : ${companyObj}`);

 		console.log('testData:', testData);
                segment.saveSegment(`testData : ${testData}`);
                  

                if(haltOverRide){
                    resumeUser = `${userName}`;
                    console.log('resumeUser:', resumeUser);
                    segment.saveSegment(`resumeUser : ${resumeUser}`);
                }

                // if(mageManufacturer == constants.REYNOLDS.PORSCHE_STORE_LABEL){
                //     isPorscheStore = true;
                // } else{
                //     isPorscheStore = false; 
                // }

                // Ensure mageManufacturer has a default value if still empty
                if (!mageManufacturer || mageManufacturer.trim() === '') {
                    mageManufacturer = 'UNKNOWN';  // Default brand
                    console.log('mageManufacturer was empty, setting default:', mageManufacturer);
                    segment.saveSegment(`mageManufacturer was empty, setting default: ${mageManufacturer}`);
                }

                console.log('mageManufacturer:', mageManufacturer);
                segment.saveSegment(`mageManufacturer : ${mageManufacturer}`);

                console.log('isPorscheStore:', isPorscheStore);
                segment.saveSegment(`isPorscheStore : ${isPorscheStore}`);


                let buildProxiesDecider;
                if(buildProxies){
                    buildProxiesDecider = constants.PROCESS_JSON.OPT_BUILD_PROXY_RO; 
                } else{
                    buildProxiesDecider = constants.PROCESS_JSON.OPT_NO_BUILD_PROXY_RO;
                }
                job.attrs.data.inputFilePath1 = inputStoreName;
                await job.save();

                if(haltOverRide){
                    // Portal update for process xml Resume
                    let todayDate;
                    let attPayload = {};    
                    let projectID;
                    let secondProjectID;
                    let inpObjProject;
                    let inpObjSecondProject;
                    let projectIdList;
                    let secondProjectIdList;
                    try{
                    todayDate = new Date().toISOString().slice(0, 10);
                    attPayload = agendaObject[extractedObjectIndex];
                    projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                    // projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds : ""; 
                    // secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList : ""; 
                    projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                    secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                    attPayload['inProjectId'] =  projectID;

                    secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                    attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                    attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                    attPayload.in_retrive_ro_request_on = todayDate;
                    inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                    console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                    if(secondProjectIdList.length>0){
                        inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                        console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                    }
                    } catch(err){
                    console.log(JSON.stringify(err));
                    segment.saveSegment(`AUTOMATE : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                    }
                    segment.saveSegment(`AUTOMATE : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                    segment.saveSegment(`AUTOMATE : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                    try {
                        segment.saveSegment(`AUTOMATE : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                        // let parsedData = JSON.parse(inpObjProject.inData);
                        // let  projectIdList =  parsedData.projectIds.split("*");
                            if(projectIdList){
                                 for(const id of projectIdList){
                                    if(id!=undefined && id!=''){
                                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                                        portalUpdate.doPayloadAction(inpObjProject);
                                        console.log(`Delertrack Schedule portal call with Project Id RESUME${id}`);
                                        segment.saveSegment(`Dealertrack Schedule portal call with Project Id RESUME${id}`);
                
                                    }
                                 }
                            } 
                       
                        if(secondProjectIdList.length>0){
                        segment.saveSegment(`CDK : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                        // let parsedData = JSON.parse(inpObjSecondProject.inData);
                        // let  secondProjectIdList =  parsedData.secondProjectIdList.split("*");
                        if(secondProjectIdList){
                            for(const id of secondProjectIdList){
                               if(id!=undefined && id!=''){
                                inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                                   portalUpdate.doPayloadAction(inpObjSecondProject);
                                   console.log(`Dealertrack Schedule portal call with Second Project Id Resume${id}`);
                                   segment.saveSegment(`Dealertrack Schedule portal call with Second Project Id RESUME${id}`);
           
                               }
                            }
                       } 
                        
                        }
                    } catch(error) {
                        console.log("Error:", error);
                        segment.saveSegment(`DEALERTRACK  : doPayloadAction Error - ${JSON.stringify(error)}`); 
                    }
                    //code end for portal update for process xml Resume
                }

                const processJson = spawn("bash",
                    [
                        constants.PROCESS_JSON.PROCESS_CMD,
                        constants.PROCESS_JSON.OPT_BUNDLE_DIR, constants.PROCESS_JSON.REYNOLDS_BUNDLE_DIR,
                        constants.PROCESS_JSON.OPT_INPUT_ZIP, path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip),
                        constants.PROCESS_JSON.OPT_ZAP_INPUT,
                        constants.PROCESS_JSON.OPT_PERFORM_ZIP,
                        // constants.PROCESS_JSON.OPT_NO_BUILD_PROXY_RO,
                        buildProxiesDecider,
                        isPorscheStore ? constants.PROCESS_JSON.OPT_PORSCHE_STORE : '',
                        fopcStore ? constants.PROCESS_JSON.OPT_FOPC_STORE : '',
                        constants.PROCESS_JSON.OPT_DEADLETTER_DIR_PREFIX,
                        constants.REYNOLDS_DEADLETTER_DIR_PREFIX + '-processed',
                        constants.PROCESS_JSON.OUTPUT_PREFIX,
                        constants.PROCESS_JSON.OUTPUT_PREFIX_VAL,
                        constants.PROCESS_JSON.HALT_OVER_RIDE,
                        haltOverRide,
                        constants.PROCESS_JSON.INPUT_STORE_NAME,
                        inputStoreName,
                        constants.PROCESS_JSON.SINGLE_STORE_FLAG,
                        switchBranch,
                        constants.PROCESS_JSON.CUSTOM_BRANCH_NAME,
                        customBranchName,
                        constants.PROCESS_JSON.BRAND_NAME,mageManufacturer,
                        "--uuid",uniqueId,
                        "--performed-by",userName,
                        "--exception-report",true,
                        "--company_ids",companyIds                        
                    ], {
                        cwd: constants.PROCESS_JSON.PROCESS_CMD_PATH,
                        env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
                    }).on('error', function (err) {
                        console.log("error ::", err);
                        segment.saveSegment(`error: ${err}`);
                    });
                console.log(`Reynolds : Start processing of extractihaltOverRideon > ${basename}`);
                segment.saveSegment(`Reynolds : Start processing of extraction > ${basename}`);
                segment.saveSegmentFailure(`Reynolds : Start processing of extraction > ${basename}`, uniqueFailLogName);
                process.stdin.pipe(processJson.stdin);
                processJson.stdout.on("data", async (data) => {
                    console.log(`stdout: ${data}`);
                    stdOutArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                     
                    
                    data = data.toString('utf8');

                      if(data.includes('Total Ros Count:-')){
                                                segment.saveSegment(`Total Ros Count55555555555,${data}`);
                                                console.log('file generated',data.split(':')[1]);
                                                segment.saveSegment(`Total Ro90999999999,${data.split(':')[1]}`);
                                                totalRoCount = data.split(':-')[1];
                                                segment.saveSegment(`totalRoCount666666,${totalRoCount}`);
                                         
                                           }else{
                                                console.log("failed to generate1212 file")
                       } 

                    if(data.includes('Processor status')){
                         segment.saveSegment('Processor statu for UI',data);
                         console.log('file generated',data.split(':')[1]);
                         processorStatus = data.split(':')[1];
                         segment.saveSegment('processorStatus',processorStatus);
                         await SetProcessJobStatus.setProcessJobStatusForRunningJob(basename,processorStatus);
                       }else{
                       console.log("failed to generate file")
                      }


                     await job.touch();
                    segment.saveSegment(`stdout: ${data}`);
                    segment.saveSegmentFailure(`stdout: ${data}`, uniqueFailLogName);
                });

                processJson.stderr.on("data", async (data) => {
                    console.log(`stderr: ${data}`);
                    stdErrorArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                    await job.touch();
                    segment.saveSegment(`stderr: ${data}`);
                    segment.saveSegmentFailure(`stderr: ${data}`, uniqueFailLogName);

                    try{
                        if(!data.includes('Beginning zip processing in') && data){
                            if (fs.existsSync(path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip))) {
                                var deadLetterPath = `${process.env.REYNOLDS_WORK_DIR}/dead-letter-processed`;
                                fs.copyFile(path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip), deadLetterPath+ "/" + basename, (err) => {
                                    if (err) {
                                        console.log(err);
                                        segment.saveSegment(`Error in input file to dead letter: ${err}`);
                                        segment.saveSegmentFailure(`Error in input file to dead letter: ${err}`, storeCode);
                                    }
                                    console.log(`${ path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`);
                                    segment.saveSegment(`${ path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`);
                                    segment.saveSegmentFailure(`${ path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`, storeCode);
                                });
                        
                                // fs.unlink(path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip), function (err) {
                                //     if (err){
                                //         console.log(err);
                                //         segment.saveSegment(`Error in delete input file : ${err}`);
                                //         segment.saveSegmentFailure(`Error in delete input file : ${err}`, storeCode);
                                //     } 
                                // });
                            }
                        }
                    } catch(err){
                        console.log(err)
                    }
                    
                });

                processJson.on("close", async (code) => {
                const filePath = '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception_tag/All_exception_details.csv';
                    if (fs.existsSync(filePath)) {
                      fs.createReadStream(filePath)
                      .pipe(csv1())
                      .on('data', (row) => {
                      const type = row['Type'];
                       if (type) {
                          exceptionTypeCounts[type] = (exceptionTypeCounts[type] || 0) + 1;
                        }
                    })
                     .on('end', () => {
                     console.log("exceptionTypeCounts", exceptionTypeCounts);
                     segment.saveSegment(`exceptionTypeCounts: ${JSON.stringify(exceptionTypeCounts)}`);
                  });
               } else {
                   console.error(`File not found: ${filePath}`);
                   segment.saveSegment(`Error: File not found at path ${filePath}`);
                }

                    var message = "n/a";
                    let status = false;
                    if (code == constants.STATUS_CODE.SUCCESS) {
                        status = true;
                        message = "Success";
                    } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                        message = "Extraction failed, general death";
                        job.fail(new Error(`Error: ${message}`));
                    } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                        message = "Extraction failed, moved to dead-letter path";
                        job.fail(new Error(`Error: ${message}`));
                    }
                    if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_PROCESSING_FAILED)) {
                        message = "Extraction failed, Zip File Processing Failed,  ";
                        status = false;
                    }
                    var deadLetterPath = `${process.env.REYNOLDS_WORK_DIR}/dead-letter-processed`;
                    var errResp = `Moving input to dead-letter bin: ${deadLetterPath}`;
                    if (stdOutArray && stdOutArray.includes(errResp)) {
                        message += errResp
                        status = false;
                    }
                    stdErrorArray.forEach((v,i) =>{
                        if(v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT)){
                        }
                    })
                    job.attrs.data.processorUniqueId = '';
                    if(uniqueId && uniqueId!=undefined){
                        job.attrs.data.processorUniqueId = uniqueId;
                    }
                   
                    /////////////////////////////Code for Halt and Resume Processor/////////////////////////////////////////////////////////////////////////////
                    var invalidmiscpaytypeFilePath = '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv';
                    var  invalidmiscpaytypeCount;
                    var invalidmiscpaytypeArray;
                   
                    var estimateCount;
                    var estimateArray;
                    var estimateFilePath = "/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/estimate.csv";
                    
                    var punchTimeMissingCount;
                    var punchTimeMissingArray;
                    var punchTimeMissingFilePath= "/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing_percentage.txt";
                    
                    var suffixedInvoicesCount;
                    var suffixedInvoicesArray;
                    var suffixedInvoicesFilePath =
                    "/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/suffixed-invoices.csv";
                    
                    let PUNCH_TIME_MISSING_FILEPATH='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv';

                    var exceptionClosedInvoicesFilePath ='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/exception-closed-invoices.csv';
                    var exceptionClosedInvoicesCount;
                    var exceptionClosedInvoicesArray;
                     
                    var extraRoInXmlExceptionFilePath ='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv';
                    var extraRoInXmlExceptionCount;
                    var extraRoInXmlExceptionArray;
                    
                    var imOpenedClosedRciRosFilePath= '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/im_opened_closed_rci_ros.csv';
                    var imOpenedClosedRciRosCount;
                    var imOpenedClosedRciRosArray;

                    var deletedRosFilepath='/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/removed_ros.csv'
                    var deletedRosArray;
                    var deletedRoscount;

                    

                    var warningObj = {};
                    warningObj.scheduled_by = userName;
                    if(testData){
                        warningObj.testData = true;
                        warningObj.userName = userName;
                    }
                    if (fs.existsSync(invalidmiscpaytypeFilePath)) {
                                
                        console.log(`The invalid Core Cost Sale Mismatch File Csv File exists: ${invalidmiscpaytypeFilePath}`);
                        segment.saveSegment(`The invalid Core Cost Sale Mismatch File Csv File exists: ${invalidmiscpaytypeFilePath}`);
                        segment.saveSegmentFailure(`The invalid Core Cost Sale Mismatch File Csv File exists: ${invalidmiscpaytypeFilePath}`, storeCode);
                        
                        invalidmiscpaytypeArray = await csv().fromFile(invalidmiscpaytypeFilePath);

                        if(invalidmiscpaytypeArray){
                            if(invalidmiscpaytypeArray.length){

                            console.log(`invalidmiscpaytypeArray.length: ${invalidmiscpaytypeArray.length}`);
                            segment.saveSegment(`invalidmiscpaytypeArray.length: ${invalidmiscpaytypeArray.length}`);
                            segment.saveSegmentFailure(`invalidmiscpaytypeArray.length: ${invalidmiscpaytypeArray.length}`, storeCode);
                            
                            invalidmiscpaytypeCount = invalidmiscpaytypeArray.length;
                            console.log(invalidmiscpaytypeCount);
                            warningObj.invalidmiscpaytypeCount = invalidmiscpaytypeCount;
                            if(invalidmiscpaytypeCount) warningObj.invalidmiscpaytypeFilePath = invalidmiscpaytypeFilePath;
                            // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                        }
                    }

                        console.log(`invalidmiscpaytypeCount: ${invalidmiscpaytypeCount}`);
                        segment.saveSegment(`invalidmiscpaytypeCount: ${invalidmiscpaytypeCount}`);
                        segment.saveSegmentFailure(`invalidmiscpaytypeCount: ${invalidmiscpaytypeCount}`, storeCode);
                    }
                    if (fs.existsSync(estimateFilePath)) {
                        console.log(
                          `The estimate Csv File exists: ${estimateFilePath}`
                        );
                        segment.saveSegment(
                          `The estimate Csv File exists: ${estimateFilePath}`
                        );
                        segment.saveSegmentFailure(
                          `The estimate Csv File exists: ${estimateFilePath}`,
                          storeCode
                        );
            
                        estimateArray = await csv().fromFile(
                          estimateFilePath
                        );
            
                        if (estimateArray) {
                          if (estimateArray.length) {
                            console.log(
                              `estimateArray.length: ${estimateArray.length}`
                            );
                            segment.saveSegment(
                              `estimateArray.length: ${estimateArray.length}`
                            );
                            segment.saveSegmentFailure(
                              `estimateArray.length: ${estimateArray.length}`,
                              storeCode
                            );
            
                            estimateCount = estimateArray.length;
                            console.log('estimateCount',estimateCount);
                            warningObj.estimateCount = estimateCount;
                            // if (estimateCount)
                            //   warningObj.invalidmiscpaytypeFilePath =
                            //     invalidmiscpaytypeFilePath;
                            // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                          }
                        }
            
                        console.log(`invalidmiscpaytypeCount: ${invalidmiscpaytypeCount}`);
                        segment.saveSegment(
                          `invalidmiscpaytypeCount: ${invalidmiscpaytypeCount}`
                        );
                        segment.saveSegmentFailure(
                          `invalidmiscpaytypeCount: ${invalidmiscpaytypeCount}`,
                          storeCode
                        );
                      }

              

              
           
                      if (fs.existsSync(punchTimeMissingFilePath)) {
                        console.log(
                          `The pubnh time missing Csv File exists: ${punchTimeMissingFilePath}`
                        );
                        segment.saveSegment(
                          `The Punch Time Missing Csv File exists: ${punchTimeMissingFilePath}`
                        );
                        segment.saveSegmentFailure(
                          `The Punch Time Missing Csv File exists: ${punchTimeMissingFilePath}`,
                          storeCode
                        );
            
                         fs.readFile(punchTimeMissingFilePath, 'utf8', (err, data) => {
                            if (err) {
                              console.error('Error reading the file:', err);
                              return;
                            }
                          
                            punchTimeMissingCount = data;
                            if(punchTimeMissingCount){

                                warningObj.punchTimeMissingCount = punchTimeMissingCount;
                                if (fs.existsSync(PUNCH_TIME_MISSING_FILEPATH)) {
                                    // The file or directory at the specified path exists
                                    console.log('PUNCH_TIME_MISSING csv file Exists');
                                    warningObj.PUNCH_TIME_MISSING_FILEPATH = PUNCH_TIME_MISSING_FILEPATH;
                                  } else {
                                    // The file or directory does not exist
                                    console.log('PUNCH_TIME_MISSING csv file Does not exist');
                                  }

                            }
                          });
                          

                         console.log(`punchTimeMissingCount: ${punchTimeMissingCount}`);
                         segment.saveSegment(
                          `punchTimeMissingCount: ${punchTimeMissingCount}`
                         );
                        segment.saveSegmentFailure(
                          `punchTimeMissingCount: ${punchTimeMissingCount}`,
                          storeCode
                        );
                      }


                      if (fs.existsSync(suffixedInvoicesFilePath)) {
                        console.log(
                          `The suffixedInvoices Csv File exists: ${suffixedInvoicesFilePath}`
                        );
                        segment.saveSegment(
                          `The suffixedInvoices Csv File exists: ${suffixedInvoicesFilePath}`
                        );
                        segment.saveSegmentFailure(
                          `The suffixedInvoices Csv File exists: ${suffixedInvoicesFilePath}`,
                          storeCode
                        );
            
                        suffixedInvoicesArray = await csv().fromFile(
                          suffixedInvoicesFilePath
                        );
            
                        if (suffixedInvoicesArray) {
                          if (suffixedInvoicesArray.length) {
                            console.log(
                              `suffixedInvoicesArray.length: ${suffixedInvoicesArray.length}`
                            );
                            segment.saveSegment(
                              `suffixedInvoicesArray.length: ${suffixedInvoicesArray.length}`
                            );
                            segment.saveSegmentFailure(
                              `suffixedInvoicesArray.length: ${suffixedInvoicesArray.length}`,
                              storeCode
                            );
            
                            suffixedInvoicesCount = suffixedInvoicesArray.length;
                            console.log('suffixedInvoicesCount',suffixedInvoicesCount);
                            warningObj.suffixedInvoicesCount = suffixedInvoicesCount;
                            if (suffixedInvoicesCount){
                                warningObj.suffixedInvoicesFilePath =
                                suffixedInvoicesFilePath;

                            }
                              
                            // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                          }
                        }
                      
                        console.log(`suffixedInvoicesCount: ${suffixedInvoicesCount}`);
                        segment.saveSegment(
                          `suffixedInvoicesCount: ${suffixedInvoicesCount}`
                        );
                        segment.saveSegmentFailure(
                          `suffixed invoice count: ${suffixedInvoicesCount}`,
                          storeCode
                        );

                      }
                      
                    var groupedSuffixInvoiceFilePath = '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/grouped_suffixinvoice.csv';

                    var suffixedInvoicesCsvData='';
                    if (fs.existsSync(groupedSuffixInvoiceFilePath)) {
                          fs.createReadStream(groupedSuffixInvoiceFilePath)
                          .pipe(csvParser())
                          .on('data', (row) => {
                          suffixedInvoicesCsvData+=`${row.source}:${row.date}:${row.ROCount}*`
                          })
                          .on('end', () => {
                            // CSV parsing is complete
                            console.log('CSV parsing finished.');
                            console.log("suffixedInvoicesCsvData",suffixedInvoicesCsvData);
                          })
                          .on('error', (error) => {
                            // Handle any error that occurs during parsing
                            console.error('Error occurred while parsing CSV:', error);
                          });
                    }


             
                    if (fs.existsSync(exceptionClosedInvoicesFilePath)) {
                                
                        console.log(`The Exception Closed Invoices File exists: ${exceptionClosedInvoicesFilePath}`);
                        segment.saveSegment(`The Exception Closed Invoices File exists: ${exceptionClosedInvoicesFilePath}`);
                        segment.saveSegmentFailure(`The Exception Closed Invoices File  exists: ${exceptionClosedInvoicesFilePath}`, storeCode);
                        
                        exceptionClosedInvoicesArray = await csv().fromFile(exceptionClosedInvoicesFilePath);

                        if(exceptionClosedInvoicesArray){
                            if(exceptionClosedInvoicesArray.length){

                            console.log(`exceptionClosedInvoicesArray.length: ${exceptionClosedInvoicesArray.length}`);
                            segment.saveSegment(`exceptionClosedInvoicesArray.length: ${exceptionClosedInvoicesArray.length}`);
                            segment.saveSegmentFailure(`exceptionClosedInvoicesArray.length: ${exceptionClosedInvoicesArray.length}`, storeCode);
                            
                            exceptionClosedInvoicesCount = exceptionClosedInvoicesArray.length;
                            console.log(exceptionClosedInvoicesCount);
                            warningObj.exceptionClosedInvoicesCount = exceptionClosedInvoicesCount;
                            if(exceptionClosedInvoicesCount) warningObj.exceptionClosedInvoicesFilePath = exceptionClosedInvoicesFilePath;
                            
                        }
                    }

                        console.log(`exceptionClosedInvoicesCount: ${exceptionClosedInvoicesCount}`);
                        segment.saveSegment(`exceptionClosedInvoicesCount: ${exceptionClosedInvoicesCount}`);
                        segment.saveSegmentFailure(`exceptionClosedInvoicesCount: ${exceptionClosedInvoicesCount}`, storeCode);
                    }

                



                    if (fs.existsSync(extraRoInXmlExceptionFilePath)) {
                                
                        console.log(`The Exception Closed Invoices File exists: ${extraRoInXmlExceptionFilePath}`);
                        segment.saveSegment(`The Exception Closed Invoices File exists: ${extraRoInXmlExceptionFilePath}`);
                        segment.saveSegmentFailure(`The Exception Closed Invoices File  exists: ${extraRoInXmlExceptionFilePath}`, storeCode);
                        
                        exceptionClosedInvoicesArray = await csv().fromFile(extraRoInXmlExceptionFilePath);

                        if(extraRoInXmlExceptionArray){
                            if(extraRoInXmlExceptionArray.length){

                            console.log(`exceptionClosedInvoicesArray.length: ${extraRoInXmlExceptionArray.length}`);
                            segment.saveSegment(`exceptionClosedInvoicesArray.length: ${extraRoInXmlExceptionArray.length}`);
                            segment.saveSegmentFailure(`exceptionClosedInvoicesArray.length: ${extraRoInXmlExceptionArray.length}`, storeCode);
                            
                            extraRoInXmlExceptionCount = extraRoInXmlExceptionArray.length;
                            console.log(extraRoInXmlExceptionCount);
                            warningObj.extraRoInXmlExceptionCount = extraRoInXmlExceptionCount;
                            if(extraRoInXmlExceptionCount) warningObj.extraRoInXmlExceptionFilePath = extraRoInXmlExceptionFilePath;
                            
                        }
                    }

                        console.log(`extraRoInXmlExceptionCount: ${extraRoInXmlExceptionCount}`);
                        segment.saveSegment(`extraRoInXmlExceptionCount: ${extraRoInXmlExceptionCount}`);
                        segment.saveSegmentFailure(`extraRoInXmlExceptionCount: ${extraRoInXmlExceptionCount}`, storeCode);
                    }




                    if (fs.existsSync(extraRoInXmlExceptionFilePath)) {
                                
                        console.log(`The Extra Ro in Xml Exception File exists: ${extraRoInXmlExceptionFilePath}`);
                        segment.saveSegment(`The Extra Ro in Xml Exception File exists: ${extraRoInXmlExceptionFilePath}`);
                        segment.saveSegmentFailure(`The Extra Ro in Xml Exception File exists: ${extraRoInXmlExceptionFilePath}`, storeCode);
                        
                        extraRoInXmlExceptionArray = await csv().fromFile(extraRoInXmlExceptionFilePath);

                        if(extraRoInXmlExceptionArray){
                            if(extraRoInXmlExceptionArray.length){

                            console.log(`exceptionClosedInvoicesArray.length: ${extraRoInXmlExceptionArray.length}`);
                            segment.saveSegment(`exceptionClosedInvoicesArray.length: ${extraRoInXmlExceptionArray.length}`);
                            segment.saveSegmentFailure(`exceptionClosedInvoicesArray.length: ${extraRoInXmlExceptionArray.length}`, storeCode);
                            
                            extraRoInXmlExceptionCount = extraRoInXmlExceptionArray.length;
                            console.log(extraRoInXmlExceptionCount);
                            warningObj.extraRoInXmlExceptionCount = extraRoInXmlExceptionCount;
                            if(extraRoInXmlExceptionCount) warningObj.extraRoInXmlExceptionFilePath = extraRoInXmlExceptionFilePath;
                            
                        }
                    }

                        console.log(`extraRoInXmlExceptionCount: ${extraRoInXmlExceptionCount}`);
                        segment.saveSegment(`extraRoInXmlExceptionCount: ${extraRoInXmlExceptionCount}`);
                        segment.saveSegmentFailure(`extraRoInXmlExceptionCount: ${extraRoInXmlExceptionCount}`, storeCode);
                    }

                    
                      
            
                    
                    if (fs.existsSync(imOpenedClosedRciRosFilePath)) {
                                
                        console.log(`The imOpendedClosedRciRos exists: ${imOpenedClosedRciRosFilePath}`);
                        segment.saveSegment(`The imOpendedClosedRciRos File exists: ${imOpenedClosedRciRosFilePath}`);
                        segment.saveSegmentFailure(`The imOpendedClosedRciRosFile  exists: ${imOpenedClosedRciRosFilePath}`, storeCode);
                        
                        imOpenedClosedRciRosArray = await csv().fromFile(imOpenedClosedRciRosFilePath);

                        if(imOpenedClosedRciRosArray){
                            if(imOpenedClosedRciRosArray.length){

                            console.log(`imOpenedClosedRciRosArray.length: ${imOpenedClosedRciRosArray.length}`);
                            segment.saveSegment(`imOpenedClosedRciRosArray.length: ${imOpenedClosedRciRosArray.length}`);
                            segment.saveSegmentFailure(`imOpenedClosedRciRosArray.length: ${imOpenedClosedRciRosArray.length}`, storeCode);
                            
                            imOpenedClosedRciRosCount = imOpenedClosedRciRosArray.length;
                            console.log(imOpenedClosedRciRosCount);
                            warningObj.imOpenedClosedRciRosCount = imOpenedClosedRciRosCount;
                            if(imOpenedClosedRciRosCount) warningObj.imOpenedClosedRciRosFilePath = imOpenedClosedRciRosFilePath;
                            
                        }
                    }

                        console.log(`imOpenedClosedRciRosCount: ${imOpenedClosedRciRosCount}`);
                        segment.saveSegment(`imOpenedClosedRciRosCount: ${imOpenedClosedRciRosCount}`);
                        segment.saveSegmentFailure(`imOpenedClosedRciRosCount: ${imOpenedClosedRciRosCount}`, storeCode);
                    }


                     
                    if (fs.existsSync(deletedRosFilepath)) {
                                
                        console.log(`The deletedRosFilepath exists: ${deletedRosFilepath}`);
                        segment.saveSegment(`The deletedRosFilepath File exists: ${deletedRosFilepath}`);
                        segment.saveSegmentFailure(`The deletedRosFilepath  exists: ${deletedRosFilepath}`, storeCode);
                        
                        deletedRosArray = await csv().fromFile(deletedRosFilepath);

                        if(deletedRosArray){
                            if(deletedRosArray.length){

                            console.log(`deletedRosArray.length: ${deletedRosArray.length}`);
                            segment.saveSegment(`deletedRosArray.length: ${deletedRosArray.length}`);
                            segment.saveSegmentFailure(`deletedRosArray.length: ${deletedRosArray.length}`, storeCode);
                            
                            deletedRoscount = deletedRosArray.length;
                            console.log(deletedRoscount);
                            warningObj.deletedRoscount = deletedRoscount;
                            if(deletedRoscount) warningObj.deletedRosFilepath = deletedRosFilepath;
                            
                        }
                    }

                        console.log(`deletedRoscount: ${deletedRoscount}`);
                        segment.saveSegment(`deletedRoscount: ${deletedRoscount}`);
                        segment.saveSegmentFailure(`deletedRoscount: ${deletedRoscount}`, storeCode);
                    }
                      
                      

                    console.log(stdErrorArray);
                    if (stdErrorArray && !haltOverRide) {

                        console.log(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT);
                        console.log(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_DEAD);

                        stdErrorArray.forEach(async(v, i) => {
                            if (
                                v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT)
                            ) {
                                message = "Halt";
                                haltIdentifier = true;
                                status = false;
                                await SetProcessJobStatus.setProcessJobStatusForReynolds(locationId,mageStoreCode,message);
                            }

                            if (
                                v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_DEAD)
                            ) {
                                message = "Dead";
                                await SetProcessJobStatus.setProcessJobStatusForReynolds(locationId,mageStoreCode,message);
                                haltIdentifier = true;
                                status = false;
                            }


                        });

                        try {
                            if (
                                stdOutArray &&
                                stdOutArray.includes(errResp) &&
                                message == "Halt" &&
                                !haltOverRide
                            ) {
                                let deadLetterFilePath = deadLetterPath + "/" + basename;
                                let haltFilePath =
                                    "/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/halt/" +
                                    basename;
                                if (fs.existsSync(deadLetterFilePath)) {
                                    fs.copyFile(deadLetterFilePath, haltFilePath, (err) => {
                                        if (err)
                                        {
                                            console.log('err',err);
                                        }
                                        else{
                                        console.log(`${deadLetterFilePath} was copied to ${haltFilePath}`)
                                        }
                                    });
                                } else {
                                    console.log(`${deadLetterFilePath} not exist!`);
                                }
                            } else{
                                console.log('Not a Halt process')
                            }
                        } catch (err) {
                            console.log(err);
                        }
                    }
                    /////////////////////////////Code for Halt and Resume Processor/////////////////////////////////////////////////////////////////////////////


                    console.log(`Reynolds : JSON processing job for Store ${storeName} exited with code ${code}`);
                    segment.saveSegment(`Reynolds : JSON processing job for Store ${storeName} exited with code ${code}`);
                    segment.saveSegmentFailure(`Reynolds : JSON processing job for Store ${storeName} exited with code ${code}`, uniqueFailLogName)

                    var extractionErrorResponse;
                    var errorWarnningMessage;
                    

                    if(extractionId){
                        extractionErrorResponse = await extractionError.displayErrorLogWithSpecific(extractionId, 'Reynolds');
                        console.log('extractionErrorResponse:',extractionErrorResponse);
                        if(extractionErrorResponse.status){
                            let resp = JSON.parse(JSON.stringify(extractionErrorResponse.response))
                            let tmpDescritionArray = [];
                            resp.forEach(e => {
                                //tmpDescritionArray.push(e.ro_pull_type+" : "+e.description);
                                tmpDescritionArray.push(e.description);
                                });
                            errorWarnningMessage = tmpDescritionArray.join(", ");
                        }
                    } 
                    console.log('errorWarnningMessage:',errorWarnningMessage);

                    if(errorWarnningMessage){
                        if(errorWarnningMessage.length > 0){
                            warningObj.errorwarningMessage = errorWarnningMessage;
                        }
                    }

                    let failureDirectory = process.cwd() + '/logs/Reynolds/failure/';
                    let failurelogFile = failureDirectory + uniqueFailLogName + '.log';

                    var fetchGroupAndStoreName = (job.attrs.data.inputFile).split('-');
                    var groupName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[0] : '';
                    var storeName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[1] : '';
                    console.log(warningObj);
                    var mailTemplateReplacementValues = {
                        dmsType: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.JOB_TYPE : 'REYNOLDSRCI',
                        processTypes: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.PROCESS_JSON.JOB_NAME : 'REYNOLDSRCI-PROCESS-JSON',
                        subject: `Process JSON for ${groupName} - ${storeName} Completed`,
                        warningObj: warningObj,
                        thirdPartyUsername: locationId,
                        storeCode: storeName,
                        groupCode: groupName
                    };
                    var mailBody = {
                        fromAddress: appConstants.NOTIFICATION.FROMADDRESS,
                        toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                        ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                        attachedfailurelogFile:failurelogFile
                    }

                       job.attrs.data.processorUniqueId = '';
                     
                       if(uniqueId && uniqueId!=undefined){
                         job.attrs.data.processorUniqueId = uniqueId;
                        }
                   
                   
                    if (status) {
                        clearInterval(touch);
                        var opDataFileEtl = path.join(constants.PROCESS_JSON.REYNOLDS_ETL_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                        var opDataFileDist = path.join(constants.PROCESS_JSON.REYNOLDS_DIST_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                        opDataFileEtl = opDataFileEtl.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
                        var outputFile = opDataFileDist + ' & ' + opDataFileEtl;
                        job.attrs.data.outputFile = outputFile;
                        job.attrs.data.status = status;
                        job.attrs.data.message = message;
                        job.attrs.data.warningMessage = warningObj;
                        job.attrs.data.invalidmiscpaytypeCount =  invalidmiscpaytypeCount;
                        job.attrs.data.estimateCount = estimateCount;
                        job.attrs.data.punchTimeMissingCount = punchTimeMissingCount;
                        job.attrs.data.suffixedInvoicesCount = suffixedInvoicesCount;
                        job.attrs.data.suffixedInvoicesCsvData = suffixedInvoicesCsvData;
                        segment.saveSegment(`Job saved to DB ${JSON.stringify(job)}`);
                        segment.saveSegmentFailure(`Job saved to DB ${JSON.stringify(job)}`, uniqueFailLogName);
                        await job.save();
                        done();
                      
                        // Send notification after process json job completed
                        var basenameCheck1 = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                        var zipPath = path.join(constants.PROCESS_JSON.REYNOLDS_BUNDLE_DIR, basenameCheck1);
                        var outPutTemp = constants.PROCESS_JSON.REYNOLDS_BUNDLE_DIR+'/temp1';
                        var invoice_missing_count='';
                            
                            fs.createReadStream(zipPath)
                            .pipe(unZipper .Extract({ path: outPutTemp }))
                            .on ('close',async (res)=>{
                            if(fs.existsSync(`${outPutTemp}/processing-result/missing-invoices.csv`)){
                                
                            let  missing_ro_countArray = await csv().fromFile(`${outPutTemp}/processing-result/missing-invoices.csv`);
                            invoice_missing_count = missing_ro_countArray.length;
                                
                    }else{
                             console.log('false');
                         }

                            })
                       
                            await segment.sleep(8000); 

                            warningObj.invoice_missing_count = invoice_missing_count;
                            warningObj.missingInvoiceFilePath = `${outPutTemp}/processing-result/missing-invoices.csv`;

                            console.log('****************************************WARNING OBJ***************************************',warningObj);

                            var mailTemplateReplacementValues = {
                                dmsType: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.JOB_TYPE : 'UCS',
                                processTypes: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.PROCESS_JSON.JOB_NAME : 'UCS-PROCESS-JSON',
                                subject: `Process JSON for ${groupName} - ${storeName} Completed`,
                                warningObj: warningObj,
                                thirdPartyUsername: locationId,
                                storeCode: storeName,
                                groupCode: groupName
                            };

                            var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Success';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);

                        mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);
                        
                        await segment.sleep(15000); 
                                  if(fs.existsSync(outPutTemp)){
                        
                                removeDirForce(outPutTemp);
                            
                                function removeDirForce(dirPath) {
                                try { var files = fs.readdirSync(dirPath); }
                                catch (e) { return; }
                                if (files.length > 0)
                                    for (var i = 0; i < files.length; i++) {
                                        let filePath = dirPath + '/' + files[i];
                                        if (fs.statSync(filePath).isFile())
                                            fs.unlinkSync(filePath);
                                           
                                        else
                                            removeDirForce(filePath);
                                    }
                                fs.rmdirSync(dirPath);
                               
                            }
                          }
                             else{
                                console.log("file not exit remove");
                             }




                    } else {
                        clearInterval(touch);
                        // Portal update for process json failed

                        const directoryPath = '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/'; 
                        const fileName = basename;                                 
                        const filePath = path.join(directoryPath, fileName);
                        segment.saveSegment(`Autosoft : filePath inpObj Error - ${fileName}`);
                         
                        if (fs.existsSync(filePath)) {
                             fs.unlink(filePath, (err) => {
                               if (err) {
                                segment.saveSegment(`Autosoft : Error deleting file - ${err}`);
                                console.error('Error deleting file:', err);
                               } else {
                                segment.saveSegment(`Autosoft : File deleted successfully - ${filePath}`);
                                console.log('File deleted successfully:', filePath);
                                }
                            });
                        } else {
                           console.log('File does not exist:', filePath);
                     }
                        

                        let todayDate;
                        let attPayload = {};
                        let projectID;
                        let secondProjectID;
                        let inpObjProject;
                        let inpObjSecondProject;
                        let projectIdList;
                        let secondProjectIdList;
                        try{
                        todayDate = new Date().toISOString().slice(0, 10);
                        attPayload = agendaObject[extractedObjectIndex];
                        projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                        projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                        secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                        attPayload['inProjectId'] =  projectID;

                        secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                        attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                        attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                        attPayload.in_retrive_ro_request_on = todayDate;
                        job.attrs.data.invalidmiscpaytypeCount =  invalidmiscpaytypeCount;
                        job.attrs.data.estimateCount = estimateCount;
                        job.attrs.data.punchTimeMissingCount = punchTimeMissingCount;
                        job.attrs.data.suffixedInvoicesCount = suffixedInvoicesCount;
                        job.attrs.data.suffixedInvoicesCsvData = suffixedInvoicesCsvData;
                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate,  haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                        console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                        if(secondProjectIdList.length>0){
                            inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate,  haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                            console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                        }
                        } catch(err){
                        console.log(JSON.stringify(err));
                        segment.saveSegment(`Reynolds : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                        }
                        segment.saveSegment(`Reynolds : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                        segment.saveSegment(`Reynolds : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                        try {
                            segment.saveSegment(`Reynolds : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                            // let  projectIdList =  inpObjProject.inProjectId;

                            // let parsedData = JSON.parse(inpObjProject.inData);
                            //  console.log(parsedData.inpObjProject);
                            // let  projectIdList =  parsedData.projectIds.split("*");
                            if(projectIdList){
                                 for(const id of projectIdList){
                                    if(id!=undefined && id!=''){
                                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate,  haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                        portalUpdate.doPayloadAction(inpObjProject);
                                        console.log(`Reynolds Schedule portal call with Project Id Failure${id}`);
                                        segment.saveSegment(`REYNOLDS Schedule portal call with Project Id FAILURE${id}`);
                
                                    }
                                 }
                            } 
                            // portalUpdate.doPayloadAction(inpObjProject);
                            if(secondProjectId){
                            //     let parsedData = JSON.parse(inpObjSecondProject.inData);
                            //  console.log(parsedData.inpObjProject);
                            // let  secondProjectIdList =  parsedData.secondProjectIdList.split("*");
                            if(secondProjectIdList){
                                for(const id of secondProjectIdList){
                                   if(id!=undefined && id!=''){
                                      inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate,  haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                       portalUpdate.doPayloadAction(inpObjSecondProject);
                                       console.log(`Reynolds Schedule portal call with Second Project Id Failure${id}`);
                                       segment.saveSegment(`REYNOLDS Schedule portal call with Second Project Id FAILURE${id}`);
               
                                   }
                                }
                           } 
                            }
                           
                        } catch(error) {
                            console.log("Error:", error);
                            segment.saveSegment(`Reynolds : doPayloadAction Error - ${JSON.stringify(error)}`); 
                        }
                        //code end for portal update for process json failed


                        await job.fail(new Error(`Error: ${message}`));
                        done();
                        if(haltIdentifier){
                            
                            if(message == 'Halt'){
                                displayMessage = `Halted ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                mailTemplateReplacementValues.message = displayMessage;
                                mailTemplateReplacementValues.status = 'Halted';
                                mailTemplateReplacementValues.subject = `Process JSON for ${groupName} - ${storeName} Halted`;
                            }
                        } else{
                               if(haltOverRide){
                                    mailTemplateReplacementValues.resumeUser = resumeUser ? resumeUser : '';
                               } 
                            
                        var displayMessage = `Failed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                        mailTemplateReplacementValues.message = displayMessage;
                        mailTemplateReplacementValues.status = 'Failed';
                        mailTemplateReplacementValues.subject = `Process JSON for ${groupName} - ${storeName} Failed`;
                            }
                        mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                        mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                        mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                        segment.saveSegmentFailure(displayMessage, uniqueFailLogName);
                        // Send notification for failed process json job
                        await segment.sleep(2000);
                        // Send notification for failed process json job
                        mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);
                    }
                    console.log(`Call for next job selection`);
                    segment.saveSegment(`Call method for SharePoint data upload`);
                    segment.saveSegment(`Call for next job selection`);
                    var basenameCheck = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                    var distFile = path.join(constants.PROCESS_JSON.REYNOLDS_BUNDLE_DIR, basenameCheck)

                    let exceptions = "";

                    // if (invalidmiscpaytypeCount !== undefined && invalidmiscpaytypeCount !== null) {
                    //   exceptions += `invalidmiscpaytypeCount:${invalidmiscpaytypeCount}, `;
                    // }

                    // if (estimateCount !== undefined && estimateCount !== null) {
                    //     exceptions += `estimateCount: ${estimateCount}, `;
                    //   }
                      if(punchTimeMissingCount){
                        if (punchTimeMissingCount !== undefined && punchTimeMissingCount >40) {
                            exceptions += `Punch Time Missing percentage: ${punchTimeMissingCount}, `;
                          }
                      }
                   
                      if(suffixedInvoicesCount){
                        if (suffixedInvoicesCount !== undefined && suffixedInvoicesCount >40) {
                            exceptions += `Suffixed Invoices Count: ${suffixedInvoicesCount}, `;
                          }
      
                      }

                      if(extraRoInXmlExceptionCount){
                        if (extraRoInXmlExceptionCount !== undefined){
                            exceptions += `Extra ROs in Raw Data: ${extraRoInXmlExceptionCount}, `;
                          }
      
                      }

                      
                
                     console.log('exceptions Reynolds::::::::::::::::::::',exceptions);


                    //  exceptions = `invalidmiscpaytypeCount:${invalidmiscpaytypeCount}, estimateCount:${estimateCount}, punchTimeMissingCount:${punchTimeMissingCount}, suffixedInvoicesCount:${suffixedInvoicesCount}`;
                    updateSolve360Data = {projectId:projectId, secondProjectId:secondProjectId, userName:userName, solve360Update:solve360Update, thirdPartyUsername:locationId, storeCode: mageStoreCode, dmsType: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.JOB_TYPE : 'UCS', groupCode:mageGroupCode,exceptions:exceptions,projectIds:projectIds,secondProjectIdList:secondProjectIdList,uniqueId:uniqueId,companyObj:companyObj};
                    
                    if (status && fs.existsSync(distFile)) {
                        basename = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                        // await distributeFile(basename, null, updateSolve360Data, errorWarnningMessage);
                        if(totalRoCount && totalRoCount!=undefined){
                                updateSolve360Data.totalRoCount = totalRoCount;
                               }else{
                                 updateSolve360Data.totalRoCount = 0;
                          }
                           if(exceptionTypeCounts){
                                updateSolve360Data.exceptionTypeCounts = exceptionTypeCounts;
                             }else{
                                 updateSolve360Data.exceptionTypeCounts = null;
                         }   
                        await distributeFile(basename, null, updateSolve360Data, warningObj, etlDMSType, job.attrs._id);

                    } else {
                        // Process JSON Job Fail ....
                        segment.saveSegment(`Call for next job selection`);
                        await doNextProcess();
                    }
                });
            } else {                
                /**
                * Remove the Initial/recheck schedules
                */
                if(job.attrs.data.operation=="recheck"){
                job.remove(err => {
                    if (!err) {
                        console.log("Initial/recheck schedule for Reynolds Process JSON job successfully removed");
                    }
                });
              }
                done();
                await doNextProcess();
            }
        });

    return agenda;
}

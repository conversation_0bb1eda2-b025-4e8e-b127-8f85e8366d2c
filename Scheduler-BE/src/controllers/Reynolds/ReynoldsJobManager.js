const constants = require("./constants");
const ObjectID = require("mongodb").ObjectID;
const constantsCommon = require('../../common/constants');
const fs = require("fs");
var AgendaJobManager = require("../agenda");
var util = require("./util");
const segment = require("../SEGMENT/segmentManager");
const solve360Update = require("../../routes/solve360Update");
const agendaDbModel = require("../../model/agendaDb");
/**
 * Function to find the unique stores in an array of stores
 */
Array.prototype.unique = function () {
    var a = this.concat();
    for (var i = 0; i < a.length; ++i) {
        for (var j = i + 1; j < a.length; ++j) {
            if (a[i].locationId === a[j].locationId)
                a.splice(j--, 1);
        }
    }
    return a;
};

function getReturnObject(status, message, object, error) {
    if (status) {
        console.log(message, object ? JSON.stringify(object) : "");
    } else {
        console.error(error);
    }
    return {
        status: status,
        message: message,
        job: object
    }
}

/**
 * Returns all the Reynolds-Extract schedules
 */
async function getAllReynoldsExtractJobs() {
    return (await getAgendaJobs(AgendaJobManager, constants.REYNOLDS.JOB_NAME));
}

/**
 * Returns all the Reynolds Process-JSON schedules
 */
async function getAllReynoldsProcessJSONJobs() {
    return (await getAgendaJobs(AgendaJobManager, constants.PROCESS_JSON.JOB_NAME));
}


/**
 * Function to schedule Reynolds-Extract job of different stores under a Group
 * @param {*} UserInput
 */
async function scheduleReynoldsExtractJob(UserInput, innerCall) {
    segment.saveSegment(`Reynolds : Schedule Reynolds Extract Job ${JSON.stringify(UserInput)}`);

    //Portal update on Schedule a job configuration
    console.log(JSON.stringify(UserInput));
    console.log('Schedule reynolds extract input datra:-------------------------------------',UserInput);
    let todayDate;
    let attPayload = {};
    let projectID;
    let secondProjectID;
    let testData;
    let inpObjProject;
    let inpObjSecondProject;
    let projectIds;
    let secondProjectIdList;
    let parentName;
    let companyObj;
    let companyIds;
    try{
        todayDate = new Date().toISOString().slice(0, 10);
        attPayload = UserInput.jobData.storeDataArray[0];
        projectID = UserInput.jobData.storeDataArray[0].hasOwnProperty('projectId') ?  UserInput.jobData.storeDataArray[0].projectId : ""; 
        projectIds = UserInput.jobData.storeDataArray[0].hasOwnProperty('projectIds') ?  UserInput.jobData.storeDataArray[0].projectIds : ""; 
        attPayload['inProjectId'] =  projectID;
        testData = UserInput.jobData.storeDataArray[0].hasOwnProperty('testData') ?  UserInput.jobData.storeDataArray[0].testData : ""; 
        secondProjectID = UserInput.jobData.storeDataArray[0].hasOwnProperty('secondProjectId') ?  UserInput.jobData.storeDataArray[0].secondProjectId : ""; 
        secondProjectIdList = UserInput.jobData.storeDataArray[0].hasOwnProperty('secondProjectIdList') ?  UserInput.jobData.storeDataArray[0].secondProjectIdList : ""; 
        companyIds = UserInput.jobData.storeDataArray[0].hasOwnProperty('companyIds') ?  UserInput.jobData.storeDataArray[0].companyIds : ""; 
        attPayload.in_is_update_retrieve_ro = UserInput.jobData.storeDataArray[0].hasOwnProperty('solve360Update') ?  UserInput.jobData.storeDataArray[0].solve360Update : "";
        parentName = UserInput.jobData.storeDataArray[0].hasOwnProperty('parentName') ?  UserInput.jobData.storeDataArray[0].parentName : ""; 
        attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
        attPayload.in_retrive_ro_request_on = todayDate;
	attPayload.testData = testData;
        inpObjProject = getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.ASSIGN);
        console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
        companyObj = UserInput.jobData.storeDataArray[0].hasOwnProperty('companyObj') ?  UserInput.jobData.storeDataArray[0].companyObj : "";
        attPayload.companyObj = companyObj;
        if(secondProjectIdList.length>0){
        inpObjSecondProject =  getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.ASSIGN);
        console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
        }
    } catch(err){
        console.log(JSON.stringify(err));
        segment.saveSegment(`Reynolds : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
    }
    segment.saveSegment(`Reynolds : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
    segment.saveSegment(`Reynolds : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);


    var scheduleDateZ = innerCall ? UserInput.jobSchedule : util.toDate(UserInput.jobSchedule);
    /**
     * Find jobs with same Group name and date from Agenda
     * matching with user supplied Group name and schedule date
     */
    var jobs = null;
    jobs = await AgendaJobManager.jobs(
        {
            $and: [
                { "data.groupName": UserInput.jobData.groupName },
                { lastFinishedAt: null } // Jobs that not finished; Only scheduled
            ]
        });
    if (jobs[0]) { // Same group and same date job found
        var oldStoreArray = jobs[0].attrs.data.storeDataArray;
        var newStoreArray = UserInput.jobData.storeDataArray;
        // Merges both arrays and gets unique items
        var concatArrayFlag = false;
        oldStoreArray.map(data => {
            if (data.locationId === newStoreArray[0].locationId && data.mageStoreCode === newStoreArray[0].mageStoreCode) {
                concatArrayFlag = true;
                data = newStoreArray;
            }
        });
        var _storeArray = oldStoreArray;
        if (!concatArrayFlag) {
            var _storeArray = newStoreArray.concat(oldStoreArray);
        }
        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
        jobs[0].attrs.data.storeDataArray = _storeArray;

        try {
            segment.saveSegment(`Reynolds : doPayloadAction - ${JSON.stringify(inpObjProject)}`);  
            console.log(`Reynolds : doPayloadAction **********************************************************J ${JSON.stringify(inpObjProject)}`)
           let  projectIdList =  inpObjProject.inProjectId.split("*");
           console.log('ProjectId List-------------------------------------------------------',projectIdList);
           console.log("projectIdList  IF$$$$$$$$$$$$$$$$$$$$$$$$$",projectIdList);
           console.log(`projectIdList IF${projectIdList}`);
           segment.saveSegment(`projectIdList IF${projectIdList}`);
           segment.saveSegment(`testData IF${testData}`);
            if(projectIdList && testData==false ){
                 for(const id of projectIdList){
                    if(id){
                        inpObjProject.inProjectId = id;
                        solve360Update.doPayloadAction(inpObjProject);
                        console.log(`Reynolds Schedule portal call with Project Id${id}`);
                        segment.saveSegment(`reynolds : doPayloadAction(While Scheduling) Project Id -${id} `);

                    }
                 }
            }
           
            
            if(secondProjectIdList.length>0 && testData==false ){
              segment.saveSegment(`Reynolds : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
              
              let  secondProjectIdList =  inpObjSecondProject.inProjectId.split("*");
              if(secondProjectIdList){
                  for(const id of secondProjectIdList){
                     if(id!=undefined){
                         inpObjSecondProject.inProjectId = id;
                         solve360Update.doPayloadAction(inpObjSecondProject);
                         console.log(`Reynolds Schedule portal call with Second Project Id ${id}`);
                         segment.saveSegment(`REYNOLDS : doPayloadAction(While Scheduling) Second Project Id -${id} `);   
    
                     }
                  }
             } 

           
            }
        } catch(error) {
            console.log("Error:", error);
            segment.saveSegment(`Reynolds : doPayloadAction Error - ${JSON.stringify(error)}`); 
        } 

        jobs[0].save();
        return getReturnObject(true, constants.SCHEDULE.OVERRIDE_SUCCESS, jobs[0].attrs, null);
    } else {
        try {

            try {
                segment.saveSegment(`Reynolds : doPayloadAction for project 1 - ${JSON.stringify(inpObjProject)}`);   
                console.log(`Reynolds : doPayloadAction ***********************ELSE***********************************J ${JSON.stringify(inpObjProject)}`)
                let  projectIdList =  inpObjProject.inProjectId.split("*");
                console.log("projectIdList$$$$$$$$$$$$$$$$$$$$$$$$$",projectIdList);
                console.log(`projectIdList ELSE${projectIdList}`);
                segment.saveSegment(`projectIdList ELSE${projectIdList}`);
                segment.saveSegment(`testData ELSE${testData}`);
                 if(projectIdList && testData == false ){
                    segment.saveSegment(`testData ELSe test1`);
                      for(const id of projectIdList){
                         console.log("id!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!",id);
                         segment.saveSegment(`id=>${id}`);
                         if(id){
                         segment.saveSegment(`id test2=>${id}`);
                             inpObjProject.inProjectId = id;
                             solve360Update.doPayloadAction(inpObjProject);
                             console.log(`Reynolds Schedule portal call with Project Id ELSE${id}`);
                             segment.saveSegment(`Reynolds : doPayloadAction(While Scheduling) Project Id -${id} `);
     
                         }
                      }
                 } 
                // solve360Update.doPayloadAction(inpObjProject);
                if(secondProjectIdList.length>0 && testData==false ){
                  segment.saveSegment(`Reynolds : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                  let  secondProjectIdList =  inpObjSecondProject.inProjectId.split("*");
                  if(secondProjectIdList){
                      for(const id of secondProjectIdList){
                         if(id!=undefined){
                             inpObjSecondProject.inProjectId = id;
                             solve360Update.doPayloadAction(inpObjSecondProject);
                             console.log(`Reynolds Schedule portal call with Second Project Id ${id}`);
                             segment.saveSegment(`REYNOLDS : doPayloadAction(While Scheduling) Second Project Id -${id} `);   
        
                         }
                      }
                 } 

                }
            } catch(error) {
                console.log("Error:", error);
                segment.saveSegment(`Reynolds : doPayloadAction Error - ${JSON.stringify(error)}`);
            }

            var job = await AgendaJobManager.schedule(
                new Date(scheduleDateZ), constants.REYNOLDS.JOB_NAME, UserInput.jobData
            );
            return getReturnObject(true, constants.SCHEDULE.SUCCESS, job.attrs, null);
        } catch (error) {
            return getReturnObject(false, error.message, null, error);
        }
    }
}

function  getinpObjFordoPayloadAction(projectID, attPayload, todayDate,  actionLabel){
    let inpObj;
    try{
      inpObj = {
        inProjectId : projectID,
        inSource : constantsCommon.PULLED_VIA,
        inAction : actionLabel,
        inAssignee  : attPayload.userName,
        inPerformedOn : todayDate,
        inPerformedBy : attPayload.userName,
        inData  : JSON.stringify(attPayload),
        inCreatedBy : attPayload.userName
      };
    } catch (err){
      console.log(err);
    }
    return inpObj;
}

/**
 * Function to run a Reynolds-Extract job job of a store under a Group
 * @param {*} UserInput
 */
async function runNowReynoldsExtractJobByStore(UserInput) {
    segment.saveSegment(`Reynolds : runNow Reynolds Extract Job By Store ${JSON.stringify(UserInput)}`);
    var isCancel = false;
    var scheduleDateZ = UserInput.jobSchedule;
    /**
     * Find jobs with same Group name and same date from Agenda
     * matching with user supplied Group name and schedule date (here today)
     */
    var jobs = await AgendaJobManager.jobs({
        $and: [
            { "data.groupName": UserInput.jobData.groupName },
            { nextRunAt: new Date(scheduleDateZ) }
        ]
    });
    var runNowStoreData = UserInput.jobData.storeData;

    if (jobs[0]) { // Same group and same date job found

        var oldStoreArray = jobs[0].attrs.data.storeDataArray;
        /**
         * Check and remove the store if it exist in any of the schedule
         * with same Group name and same date
         */
        var storesToUpdate = oldStoreArray.filter((store) => { // Remove the store from existing schedule
            return store.locationId != runNowStoreData.locationId;
        });
        if (storesToUpdate.length > 0) { // Check whether any store remains in the existing schedule and save if yes
            jobs[0].attrs.data.storeDataArray = storesToUpdate;
            jobs[0].save();
        } else { // cancel the job with it's id if the storeDataArray is empty
            await AgendaJobManager.cancel({ _id: ObjectID(jobs[0].attrs._id) })
                .then((job) => {
                    isCancel = true;
                }).catch((error) => {
                    return getReturnObject(false, error.message, null, error);
                });
        }
    }
    /**
     * We need to pass store array to REYNOLDS Job, so
     * construct it and pass to the job
     */
    var newStoreArray = [runNowStoreData]
    UserInput.jobData.runNow = true; // To override the time frame control
    UserInput.jobData.storeDataArray = newStoreArray;
    delete UserInput.jobData.storeData;
    try {
        var job = await AgendaJobManager.now(constants.REYNOLDS.JOB_NAME, UserInput.jobData);
        return getReturnObject(
            true,
            isCancel ? constants.SCHEDULE.CANCEL_EXISTING + constants.SCHEDULE.STARTED : constants.SCHEDULE.STARTED,
            job.attrs, null
        );
    } catch (error) {
        return getReturnObject(false, error.message, null, error);
    }
}

/**
 * Function to cancel a CDK-Extract job job of a store under a Group
 * @param {*} UserInput
 */
async function cancelReynoldsExtractJobByStore(UserInput) {
    segment.saveSegment(`Reynolds : Cancel Dealer Built Extract Job By Store ${JSON.stringify(UserInput)}`);
    var scheduleDateZ = UserInput.jobSchedule;

    //Portal update on Schedule a job configuration
    let todayDate;
    let attPayload = {};
    let projectID;
    let secondProjectID;
    let inpObjProject;
    let inpObjSecondProject;
    try{
        todayDate = new Date().toISOString().slice(0, 10);
        attPayload = UserInput.jobData.storeData;
        projectID = UserInput.jobData.storeData.hasOwnProperty('projectId') ?  UserInput.jobData.storeData.projectId : ""; 
        attPayload['inProjectId'] =  projectID;

        secondProjectID = UserInput.jobData.storeData.hasOwnProperty('secondProjectId') ?  UserInput.jobData.storeData.secondProjectId : ""; 
        attPayload.in_is_update_retrieve_ro = UserInput.jobData.storeData.hasOwnProperty('solve360Update') ?  UserInput.jobData.storeData.solve360Update : "";

        attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
        attPayload.in_retrive_ro_request_on = todayDate;
        inpObjProject = getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.CANCEL);
        console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
        if(secondProjectID){
        inpObjSecondProject =  getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.CANCEL);
        console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
        }
    } catch(err){
        console.log(JSON.stringify(err));
        segment.saveSegment(`Reynolds : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
    }
    segment.saveSegment(`Reynolds : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
    segment.saveSegment(`Reynolds : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

    /**
     * Find jobs with same Group name and date from Agenda
     * matching with user supplied Group name and schedule date
     */
    var jobs = await AgendaJobManager.jobs({
        $and: [
            { "data.groupName": UserInput.jobData.groupName },
            { nextRunAt: new Date(scheduleDateZ) }
        ]
    });
    var toCancelStoreData = UserInput.jobData.storeData;
    if (jobs[0]) { // Same group and same date job found

        var oldStoreArray = jobs[0].attrs.data.storeDataArray;
        /**
         * Check and remove the store if it exist in any of the schedule
         * with same Group name and date
         */
        var storesToUpdate = oldStoreArray.filter((store) => {
            return store.locationId != toCancelStoreData.locationId;
        });
        if (storesToUpdate.length > 0) { // Check whether any store remains in the existing schedule and save if yes
            jobs[0].attrs.data.storeDataArray = storesToUpdate;
            jobs[0].save();
            return getReturnObject(true, constants.SCHEDULE.CANCELED, jobs[0].attrs, null);
        } else { // cancel the job with it's id if the storeDataArray is empty
            try {
               
                try {
                    segment.saveSegment(`Reynolds : doPayloadAction for project 1 - ${JSON.stringify(inpObjProject)}`);    
                    solve360Update.doPayloadAction(inpObjProject);
                    if(secondProjectID){
                      segment.saveSegment(`Reynolds : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                      solve360Update.doPayloadAction(inpObjSecondProject);
                    }
                } catch(error) {
                    console.log("Error:", error);
                    segment.saveSegment(`Reynolds : doPayloadAction Error - ${JSON.stringify(error)}`);
                }

                var status = await AgendaJobManager.cancel({ _id: ObjectID(jobs[0].attrs._id) });
                if (status)
                    return getReturnObject(true, constants.SCHEDULE.CANCELED, null, null);
            } catch (error) {
                return getReturnObject(false, error.message, null, error);
            }
        }
    } else {
        return getReturnObject(true, constants.SCHEDULE.NO_MATCHING, null, null);
    }
}

/**
 * Function to return the status of each Agenda job
 * @param {Agenda} Agenda Agenda object
 * @param {string} jobName Job name
 */
async function getAgendaJobs(Agenda, jobName) {
    return new Promise(async (resolve, reject) => {
        const collection = Agenda._collection.collection || Agenda._collection;//all data
        collection.aggregate([
            { $match: jobName ? { "name": jobName } : {} },
            {
                $project: {
                    _id: 0,
                    job: "$$ROOT",
                    nextRunAt: { $ifNull: ["$nextRunAt", 0] },
                    lockedAt: { $ifNull: ["$lockedAt", 0] },
                    lastRunAt: { $ifNull: ["$lastRunAt", 0] },
                    lastFinishedAt: { $ifNull: ["$lastFinishedAt", 0] },
                    failedAt: { $ifNull: ["$failedAt", 0] },
                    repeatInterval: { $ifNull: ["$repeatInterval", 0] },
                    uploadStatus: { $ifNull: ["$uploadStatus", null] } 
                }
            },
            {
                $project: {
                    job: "$job",
                    _id: "$job._id",
                    name: "$job.name",
                    type: "$job.type",
                    priority: "$job.priority",
                    nextRunAt: "$job.nextRunAt",
                    lastModifiedBy: "$job.lastModifiedBy",
                    lockedAt: "$job.lockedAt",
                    lastRunAt: "$job.lastRunAt",
                    lastFinishedAt: "$job.lastFinishedAt",
                    uploadStatus: "$job.uploadStatus",
                    data: "$job.data",
                    failReason: "$job.failReason",
                    running: {
                        $and: [
                            "$lastRunAt",
                            { $gt: ["$lastRunAt", "$lastFinishedAt"] }
                        ]
                    },
                    scheduled: {
                        $and: [
                            "$nextRunAt",
                            { $gte: ["$nextRunAt", new Date()] }
                        ]
                    },
                    queued: {
                        $and: [
                            "$nextRunAt",
                            { $gte: [new Date(), "$nextRunAt"] },
                            { $gte: ["$nextRunAt", "$lastFinishedAt"] }
                        ]
                    },
                    completed: {
                        $and: [
                            "$lastFinishedAt",
                            { $gt: ["$lastFinishedAt", "$failedAt"] }
                        ]
                    },
                    failed: {
                        $and: [
                            "$lastFinishedAt",
                            "$failedAt",
                            { $eq: ["$lastFinishedAt", "$failedAt"] }
                        ]
                    },
                    repeating: {
                        $and: [
                            "$repeatInterval",
                            { $ne: ["$repeatInterval", null] }
                        ]
                    }
                }
            }
        ]).toArray(async (err, results) => {
            let currRunningFile;
            for(let i=0;i<results.length;i++){
                if(results[i].running){
                  console.log("running",results[i].data.inputFile)
                  currRunningFile = results[i].data.inputFile;
                }
              }
             

            results.forEach(obj => {
                if (obj.job && obj.job.lastRunAt instanceof Date) {
                    obj.job.lastRunAt = obj.job.lastRunAt.getTime();
                }
                if (obj.job && obj.job.lastFinishedAt instanceof Date) {
                    obj.job.lastFinishedAt = obj.job.lastFinishedAt.getTime();
                }
                if (obj.job && obj.job.nextRunAt instanceof Date) {
                    obj.job.nextRunAt = obj.job.nextRunAt.getTime();
                }
                if (obj.job && obj.job.startTime instanceof Date) {
                    obj.job.startTime = obj.job.startTime.getTime();
                }
                if (obj.job && obj.job.endTime instanceof Date) {
                    obj.job.endTime = obj.job.endTime.getTime();
                }
                if (obj.job && obj.job.lockedAt instanceof Date) {
                    obj.job.lockedAt = obj.job.lockedAt.getTime();
                }
                if (obj.lastRunAt instanceof Date) {
                    obj.lastRunAt = obj.lastRunAt.getTime();
                }
                if (obj.lastFinishedAt instanceof Date) {
                    obj.lastFinishedAt = obj.lastFinishedAt.getTime();
                }
                if (obj.nextRunAt instanceof Date) {
                    obj.nextRunAt = obj.nextRunAt.getTime();
                }
                if (obj.startTime instanceof Date) {
                    obj.startTime = obj.startTime.getTime();
                }
                if (obj.endTime instanceof Date) {
                    obj.endTime = obj.endTime.getTime();
                }
                if (obj.lockedAt instanceof Date) {
                    obj.lockedAt = obj.lockedAt.getTime();
                }
              });
            if (err) {
                reject(err);
            }
            if (jobName === constants.REYNOLDS.JOB_NAME) {
                var timeFrame = util.getTimeFrame();
                var ret = {
                    timeFrameZone: constants.REYNOLDS.TIME_ZONE,
                    timeFrameStartTime: timeFrame.startTime.utc(),
                    timeFrameEndTime: timeFrame.endTime.utc(),
                    poolTime: constants.REYNOLDS.POOL_ADD,
                    jobArray: results
                };
                resolve(ret);
            } else if (jobName === constants.PROCESS_JSON.JOB_NAME) {

                let queueUpdationStatus = await util.updateProcessorQueue(constants.REYNOLDS_SCHEDULER_ETI_DIR,currRunningFile);
                console.log('Queue updation status============================',queueUpdationStatus);
                // var processorQueueUpdationStatus = await util.updateProcessorQueue(constants.REYNOLDS_SCHEDULER_ETI_DIR);
                // console.log('processorQueueUpdationStatus__________________________________________',processorQueueUpdationStatus);
                var  storesToProcess = [];
                let queueList = await agendaDbModel.fetchJobFromProcessorQueue("reynolds_queue");
                let queueResponse =queueList.response;



                for (let i = 0; i < queueResponse.length; i++) {
                   let currentItem = queueResponse[i];
                   let storeId = currentItem.fileName.split('-')[1];
                   let filePath =`/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/${currentItem.fileName}`;
                    if (fs.existsSync(filePath)) {
                        let obj = { storeID: storeId, fileToProcess: filePath, priority: currentItem.priority }
                        storesToProcess.push(obj)
                    } else {
                        console.log(`File not found: ${filePath}`);
                    }
                 }
                 storesToProcess = storesToProcess.reverse();
                // let queueList = await agendaDbModel.fetchJobFromProcessorQueue();
                results.forEach((result) => {
                    // storesToProcess = storesToProcess.filter(store => !store.fileToProcess.includes(result.data.inputFile));
                    storesToProcess = storesToProcess.filter(store => {
                        const storeFileName = store.fileToProcess.split('/').pop();
                        console.log(`Store file anme: ${storeFileName} currRunningFile = ${currRunningFile}`);
                        return storeFileName !== currRunningFile;
                   });
                });
                var ret = {
                    processJSONJobsQueue: storesToProcess,
                    processJSONJobs: results
                };
                resolve(ret);
            } else {
                resolve(results);
            }

        })
    });
}

/**
 * Function to create object for reschedule
 * @param {object} job Agenda job object
 * @param {*} storeDataArray Optional Array of store objects
 */
function createScheduleObject(job, storeDataArray) {
    var retObj = {};
    var jobData = {}
    jobData.groupName = job.attrs.data.groupName;
    jobData.storeDataArray = storeDataArray ? storeDataArray : job.attrs.data.storeDataArray;
    retObj.jobData = jobData;
    retObj.jobSchedule = util.getScheduleForTomorrow();
    return retObj
}

module.exports = {
    getAllReynoldsExtractJobs,
    getAllReynoldsProcessJSONJobs,
    scheduleReynoldsExtractJob,
    runNowReynoldsExtractJobByStore,
    cancelReynoldsExtractJobByStore,
    createScheduleObject
};

"use strict";

const constants = require("../constants");
const util = require("../util");
const moment = require("moment-timezone");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const { spawn } = require("child_process");
const path = require('path');
const fs = require("fs");
const segment = require("../../SEGMENT/Mpk/segmentManager");
const sharePoint = require("../../../routes/sharePoint");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const stripAnsi = require('strip-ansi');
var Agenda = require("../../agenda");
const extractionError = require('../../../../src/common/extractionError');
const { CHART_OF_ACCOUNTS_FILE_UPLOAD_DIRECTORY } = require("../../DealerTrack/constants");
const { Console } = require("console");
var accounting_csv_directory = '/etl/accounting_in/';
const csv=require('csvtojson');
const unZipper = require("unzipper");
const csvParser = require('csv-parser');
/**
 * Function to perform processing of XML file downloaded through Mpk-Extract job
 */
module.exports = async function ProcessXmlJOB(agenda) {
    var distributeFile = async function (fileName, rerunFlag , updateSolve360Data, warningObj, etlDMSType) {
        var stdErrorArray;
        var distDir = path.join(process.env.MPK_DIST_DIR, fileName);
        var etlDir = path.join(process.env.MPK_ETL_DIR, fileName);
        etlDir = etlDir.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
        var filePath = path.join(process.env.MPK_BUNDLE_DIR, fileName);
        var DMSType;
        var ETLDIR;
          
        if(etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase()){
            DMSType = constants.JOB_TYPE; 
            ETLDIR = process.env.MPK_ETL_DIR;  
        } else{
            DMSType = 'UCS'; 
            ETLDIR = process.env.UCS_ETL_DIR;
        }
        const distributeFile = spawn("bash",
            [
                'send-bundle-live-hpdog', filePath , ETLDIR
            ], {
                cwd: constants.PROCESS_JSON.MPK_DISTRIBUTE_CMD_PATH,
                env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
            }).on('error', function (err) {
                console.log("error :", err);
                segment.saveSegment(`error: ${err}`);
            });
        console.log(`Mpk: Start processing of distribution`);
        segment.saveSegment(`Mpk:  processing distribution`);
        process.stdin.pipe(distributeFile.stdin);
        distributeFile.stdout.on("data", async (data) => {
            console.log(`stdout: ${data}`);
            segment.saveSegment(`stdout: ${data}`);
        });

        distributeFile.stderr.on("data", async (data) => {
            console.log(`stderr: ${data}`);
            stdErrorArray += data.toString() + ' ';
            segment.saveSegment(`stderr: ${data}`);
        });

        distributeFile.on("close", async (code) => {
            var message = "n/a";
            var status = false;
            if (code == constants.STATUS_CODE.SUCCESS) {
                status = true;
                message = "Success";
            } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                message = "Distribution failed, general death";
            } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                message = "Distribution failed";
            }
            segment.saveSegment(`close: ${message}`);
            if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_EXIST_CHECK)) {
                status = false;
                message = "Distribution failed. Zip File Must Exist";
                segment.saveSegment(message);
            }
            /**
              * Upload files to SharePoint
              */
            if (status) {
                sharePoint.initSharePoint(distDir, DMSType, rerunFlag , updateSolve360Data, warningObj, 0);//Upload dist directory zip file to sharepoint
            }
            await doNextProcess();
        });
    }

    var doNextProcess = async function () {
        const extractZip = await util.findOldestZipFile(constants.MPK_SCHEDULER_ETI_DIR);
        if (extractZip) {
            console.log('exist');
            console.log(`Mpk : Found one Store extraction > ${extractZip} to process now`);
            segment.saveSegment(`Mpk : Found one Store extraction > ${extractZip} to process now`);
            try {
                var createdAt = extractZip.slice(0, extractZip.length - 4).split("-").reverse()[0];
                await agenda.now(constants.PROCESS_JSON.JOB_NAME, {
                    inputFile: extractZip,
                    createdAt: createdAt,
                    // accountingCsvFile: accountingCsvFilepath,
                    operation: "json-processing"
                });
                // process.exit(1);
                console.log(`Mpk : Process JSON schedule started with file > ${extractZip}`);
                segment.saveSegment(`Mpk : Process JSON schedule started with file > ${extractZip}`);
            } catch (error) {
                console.error(error);
            }

        } else {
            console.log(`Mpk : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            //segment.saveSegment(`Mpk : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            try {
                await agenda.schedule(`${constants.PROCESS_JSON.TIME_GAP}`, constants.PROCESS_JSON.JOB_NAME, { operation: "recheck" });
                console.log(`Mpk : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
                //segment.saveSegment(`Mpk : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
            } catch (error) {
                console.error(error);
            }
        }
    }

    console.log(
        `Mpk : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`
    );
    segment.saveSegment(`Mpk : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`);
    agenda.define(constants.PROCESS_JSON.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.PROCESS_JSON.CONCURRENCY },
        async (job, done) => {
            const att = job.attrs.data;
            var extractZip = null;
            var accountingCsvFile = null;
            var stdErrorArray = [];
            var stdOutArray = [];
            var uniqueFailLogName;

            var inpFile = att.inputFile ? path.join(constants.MPK_SCHEDULER_ETI_DIR, att.inputFile) : '';
            if (att.inputFile && fs.existsSync(inpFile)) {
                if (!fs.existsSync(constants.MPK_DEADLETTER_DIR_PREFIX + '-processed')) {
                    fs.mkdirSync(constants.MPK_DEADLETTER_DIR_PREFIX + '-processed');
                }
                extractZip = att.inputFile;
                accountingCsvFile = att.accountingCsvFile;
                let basename = path.basename(extractZip);

                let tempPath =`/etl/etl-vagrant/etl-mpk/mpk-rawzip/${basename}`
                 var jobsTmp = await Agenda.jobs( {
                    $and: [
                        // { "data.storeDataArray.locationId": locationId },
                        { "name": constants.MPK.JOB_NAME} ,
                        {"data.storeDataArray.inputFilePath":tempPath
                        }
                    ]
                });

                var projectId = '';
                var secondProjectId = '';
                var userName = '';
                var solve360Update = '';
                var updateSolve360Data; 
                var buildProxies;
                var extractionId;
                var invoiceMasterCSVFilePath;
                var inputStoreName;
                var etlDMSType; 
                 
                let agendaObject;
                let extractedFileTimeStamp;
                let extractedFileCreationDate;
                let extractedObjectIndex;

                let switchBranch;
                let customBranchName;

                let mageManufacturer;
                let isPorscheStore;

                let haltIdentifier = false;
                let haltOverRide = false;
                let resumeUser;
                let mageStoreCode ='';
                let mageGroupCode = '';
                let projectIds;
                let secondProjectIdList;
                let uniqueId;
                let companyIds;
                try{
                    extractedFileTimeStamp = basename.split("-").reverse()[0].replace(".zip", "");
                    segment.saveSegment(`extractedFileTimeStamp : ${extractedFileTimeStamp}`);
                    extractedFileCreationDate =  moment(extractedFileTimeStamp, "YYYYMMDDhhmmss").format("YYYY-MM-DD");
                    segment.saveSegment(`extractedFileCreationDate : ${extractedFileCreationDate}`);
                } catch(err){
                    console.log(err);
                    segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                }
                
                segment.saveSegment(`jobsTmp : ${JSON.stringify(jobsTmp)}`);
                segment.saveSegment(`jobsTmp[0] : ${JSON.stringify(jobsTmp[0])}`);
                // segment.saveSegment(`Location Id : ${locationId}`);

                if(jobsTmp[0]){
                    if(jobsTmp[0].hasOwnProperty("attrs")){
                        extractionId = jobsTmp[0].attrs._id;
                        try{
                            segment.saveSegment(`jobsTmp[0].attrs.data.storeDataArray : ${JSON.stringify(jobsTmp[0].attrs.data.storeDataArray)}`);
                            agendaObject = jobsTmp[0].attrs.data.storeDataArray;
                            agendaObject = agendaObject.filter(function (el) {
                                return el.inputFilePath == tempPath;
                            });
                            segment.saveSegment(`agendaObject : ${JSON.stringify(agendaObject)}`);
                            extractedObjectIndex = 0;
                            if(agendaObject.length > 0){ 
                                agendaObject =  agendaObject.sort((a,b) => b.endTime > a.endTime);
                                extractedObjectIndex = agendaObject.findIndex(
                                    obj => moment(obj.endTime, "YYYY-MM-DDTHH:mm:ss.SSS[Z]").format("YYYY-MM-DD") == extractedFileCreationDate
                                );
                            }

                            if(extractedObjectIndex < 0){
                                extractedObjectIndex = 0;
                            }
                            
                            segment.saveSegment(`Sorted agenda object : ${JSON.stringify(agendaObject)}`);
                            segment.saveSegment(`extractedObjectIndex : ${extractedObjectIndex}`);
                            segment.saveSegment(`Extracted agenda object : ${JSON.stringify(agendaObject[extractedObjectIndex])}`);      
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("projectId")){
                                projectId = agendaObject[extractedObjectIndex].projectId;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectId")){
                                secondProjectId = agendaObject[extractedObjectIndex].secondProjectId;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("solve360Update")){
                                solve360Update = agendaObject[extractedObjectIndex].solve360Update;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("buildProxies")){
                                buildProxies = agendaObject[extractedObjectIndex].buildProxies;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("includeMetaData")){
                                includeMetaData = agendaObject[extractedObjectIndex].includeMetaData;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("userName")){
                                userName = agendaObject[extractedObjectIndex].userName;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("invoiceMasterCSVFilePath")){
                               invoiceMasterCSVFilePath = agendaObject[extractedObjectIndex].invoiceMasterCSVFilePath;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("inputFilePath")){
                                inputStoreName = agendaObject[extractedObjectIndex].inputFilePath;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("etlDMSType")){
                                etlDMSType = agendaObject[extractedObjectIndex].etlDMSType;
                            } 
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("switchBranch")){
                                switchBranch = agendaObject[extractedObjectIndex].switchBranch;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("customBranchName")){
                                customBranchName = agendaObject[extractedObjectIndex].customBranchName;
                            } 

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("mageManufacturer")){
                                mageManufacturer = agendaObject[extractedObjectIndex].mageManufacturer;
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("haltOverRide")){
                                haltOverRide = agendaObject[extractedObjectIndex].haltOverRide;
                            }
             
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("mageStoreCode")){
                                mageStoreCode = agendaObject[extractedObjectIndex].mageStoreCode;
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("mageGroupCode")){
                                mageGroupCode = agendaObject[extractedObjectIndex].mageGroupCode;
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("projectIds")){
                                projectIds = agendaObject[extractedObjectIndex].projectIds;
                                projectIds =  projectIds.split("*");
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectIdList")){
                                secondProjectIdList = agendaObject[extractedObjectIndex].secondProjectIdList;
                                secondProjectIdList = secondProjectIdList.split("*");
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("uniqueId")){
                                uniqueId = agendaObject[extractedObjectIndex].uniqueId;
                                uniqueId+='_'+Date.now();
                           
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("companyIds")){
                                console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                                companyIds = agendaObject[extractedObjectIndex].companyIds;
                                companyIds =  companyIds.replace(new RegExp('\\*', 'g'), ',')
                                console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",companyIds);
                                // companyIds = companyIds.replace(/,\s*$/, "");
                            }
                            

                    
                        } catch(err){
                            console.log(err);
                            segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                        }
                    }
                }
                uniqueFailLogName = mageStoreCode;

                updateSolve360Data = {projectId:projectId, secondProjectId:secondProjectId, userName:userName, solve360Update:solve360Update, thirdPartyUsername:'test', storeCode: mageStoreCode, dmsType: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.JOB_TYPE : 'Mpk', groupCode:mageGroupCode,projectIds:projectIds,secondProjectIdList:secondProjectIdList,uniqueId:uniqueId};
                console.log(updateSolve360Data);
                segment.saveSegment(`updateSolve360Data : ${updateSolve360Data}`);

                console.log('projectId:', projectId);
                segment.saveSegment(`projectId : ${projectId}`);

                console.log('secondProjectId:', secondProjectId);
                segment.saveSegment(`secondProjectId : ${secondProjectId}`);

                console.log('userName:', userName);
                segment.saveSegment(`userName : ${userName}`);

                console.log('solve360Update:', solve360Update);
                segment.saveSegment(`solve360Update : ${solve360Update}`);

                console.log('buildProxies:', buildProxies);
                segment.saveSegment(`buildProxies : ${buildProxies}`);
                
                console.log('extractionId:', extractionId);
                segment.saveSegment(`extractionId : ${extractionId}`);

                console.log('invoiceMasterCSVFilePath:', invoiceMasterCSVFilePath);
                segment.saveSegment(`invoiceMasterCSVFilePath : ${invoiceMasterCSVFilePath}`);

                console.log('inputStoreName:', inputStoreName);
                segment.saveSegment(`inputStoreName : ${inputStoreName}`);

                console.log('etlDMSType:', etlDMSType);
                segment.saveSegment(`etlDMSType : ${etlDMSType}`);


                console.log('switchBranch:', switchBranch);
                segment.saveSegment(`switchBranch : ${switchBranch}`);

                console.log('customBranchName:', customBranchName);
                segment.saveSegment(`customBranchName : ${customBranchName}`);

                console.log('haltOverRide:', haltOverRide);
                segment.saveSegment(`haltOverRide : ${haltOverRide}`);
                console.log('mageManufacturer:', mageManufacturer);
                segment.saveSegment(`mageManufacturer : ${mageManufacturer}`);

                console.log('isPorscheStore:', isPorscheStore);
                segment.saveSegment(`isPorscheStore : ${isPorscheStore}`);

                console.log('projectIds:', projectIds);
                segment.saveSegment(`projectIds : ${projectIds}`);

                console.log('secondProjectIdList:', secondProjectIdList);
                segment.saveSegment(`secondProjectIdList : ${secondProjectIdList}`);

                console.log('uniqueId:', uniqueId);
                segment.saveSegment(`uniqueId : ${uniqueId}`);

                console.log('companyIds:', companyIds);
                segment.saveSegment(`companyIds : ${companyIds}`);



                job.attrs.data.inputFilePath1 = inputStoreName;
                await job.save();
                let buildProxiesDecider
                if(buildProxies){
                    buildProxiesDecider = constants.PROCESS_JSON.OPT_BUILD_PROXY_RO; 
                } else{
                    buildProxiesDecider = constants.PROCESS_JSON.OPT_NO_BUILD_PROXY_RO;
                }
                const processJson = spawn("bash",
                    [
                        constants.PROCESS_JSON.PROCESS_CMD,
                        constants.PROCESS_JSON.OPT_BUNDLE_DIR, constants.PROCESS_JSON.MPK_BUNDLE_DIR,
                        constants.PROCESS_JSON.OPT_INPUT_ZIP, path.join(constants.MPK_SCHEDULER_ETI_DIR, extractZip),
                        buildProxiesDecider,
                        constants.PROCESS_JSON.OPT_DEADLETTER_DIR_PREFIX,
                        constants.MPK_DEADLETTER_DIR_PREFIX + '-processed',
                        constants.PROCESS_JSON.OUTPUT_PREFIX,
                        constants.PROCESS_JSON.OUTPUT_PREFIX_VAL,
                        constants.PROCESS_JSON.HALT_OVER_RIDE,
                        haltOverRide,
                        "--uuid",uniqueId,
                        "--performed-by",userName,
                        "--exception-report",true,
                        "--company_ids",companyIds

                     ], {
                        cwd: constants.PROCESS_JSON.PROCESS_CMD_PATH,
                        env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
                    }).on('error', function (err) {
                        console.log("error ::", err);
                        segment.saveSegment(`error: ${err}`);
                    });
                 
                console.log(`Mpk : Start processing of extraction > ${basename}`);
                segment.saveSegment(`Mpk : Start processing of extraction > ${basename}`);
                segment.saveSegmentFailure(`Mpk : Start processing of extraction > ${basename}`, uniqueFailLogName);
                process.stdin.pipe(processJson.stdin);

                processJson.stdout.on("data", async (data) => {
                    console.log(`stdout: ${data}`);
                    stdOutArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                    await job.touch();
                    segment.saveSegment(`stdout: ${data}`);
                    segment.saveSegmentFailure(`stdout: ${data}`, uniqueFailLogName);
                });

                processJson.stderr.on("data", async (data) => {
                    console.log(`stderr: ${data}`);
                    stdErrorArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                    await job.touch();
                    segment.saveSegment(`stderr: ${data}`);
                    segment.saveSegmentFailure(`stderr: ${data}`, uniqueFailLogName);     

                    try{
                        var deadLetterPath = `${process.env.MPK_WORK_DIR}/dead-letter-processed`;
                        if(!data.includes('Beginning zip processing in') && data){
                            if (fs.existsSync(path.join(constants.MPK_SCHEDULER_ETI_DIR, extractZip))) {
                                fs.copyFile(path.join(constants.MPK_SCHEDULER_ETI_DIR, extractZip), deadLetterPath+ "/" + basename, (err) => {
                                    if (err) {
                                        console.log(err);
                                        segment.saveSegment(`Error in input file to dead letter: ${err}`);
                                        segment.saveSegmentFailure(`Error in input file to dead letter: ${err}`, storeCode);
                                    }
                                    console.log(`${ path.join(constants.MPK_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`);
                                    segment.saveSegment(`${ path.join(constants.MPK_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`);
                                    // segment.saveSegmentFailure(`${ path.join(constants.MPK_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`);
                                });
                        
                            }
                        }
                    } catch(err){
                        console.log(err)
                    }
                    
                });

                processJson.on("close", async (code) => {
                    var message = "n/a";
                    var status = false;
                    if (code == constants.STATUS_CODE.SUCCESS) {
                        status = true;
                        message = "Success";
                    } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                        message = "Extraction failed, general death";
                        job.fail(new Error(`Error: ${message}`));
                    } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                        message = "Extraction failed, moved to dead-letter path";
                        job.fail(new Error(`Error: ${message}`));
                    }
                    if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_PROCESSING_FAILED)) {
                        message = "Extraction failed, Zip File Processing Failed,  ";
                        status = false;
                    }
                    var deadLetterPath = `${process.env.MPK_WORK_DIR}/dead-letter-processed`;
                    var errResp = `Moving input to dead-letter bin: ${deadLetterPath}`;
                    if (stdOutArray && stdOutArray.includes(errResp)) {
                        message += errResp
                        status = false;
                    }
                    stdErrorArray.forEach((v,i) =>{
                        if(v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT)){

                        }
                    })

                    /////////////////////////////Code for Halt and Resume Processor/////////////////////////////////////////////////////////////////////////////
                    var warningObj = {};
                    var splitJobExceptionFilepath = '/home/<USER>/tmp/du-etl-dms-mpk-extractor-work/exception/split_job_exception.csv';
                    var splitJobExceptionCount;
                    var splitJobExceptionArray;

                    var lessSpecialDiscountFilepath = '/home/<USER>/tmp/du-etl-dms-mpk-extractor-work/exception/less_special_discount_data.csv';
                    var lessSpecialDiscountCount;
                    var lessSpecialDiscountExceptionArray;

                    if (fs.existsSync(splitJobExceptionFilepath)) {
                                
                        console.log(`The invalid Core Cost Sale Mismatch File Csv File exists: ${splitJobExceptionFilepath}`);
                        segment.saveSegment(`The invalid Core Cost Sale Mismatch File Csv File exists: ${splitJobExceptionFilepath}`);
                        segment.saveSegmentFailure(`The invalid Core Cost Sale Mismatch File Csv File exists: ${splitJobExceptionFilepath}`);
                        
                        splitJobExceptionArray = await csv().fromFile(splitJobExceptionFilepath);
                        console.log('splitJobExceptionArray:',splitJobExceptionArray);
                        if(splitJobExceptionArray){
                            if(splitJobExceptionArray.length){

                            console.log(`invalidmiscpaytypeArray.length: ${splitJobExceptionArray.length}`);
                            segment.saveSegment(`invalidmiscpaytypeArray.length: ${splitJobExceptionArray.length}`);
                            segment.saveSegmentFailure(`invalidmiscpaytypeArray.length: ${splitJobExceptionArray.length}`);
                            
                            splitJobExceptionCount = splitJobExceptionArray.length;
                            console.log('splitJobExceptionCount',splitJobExceptionCount);
                            warningObj.splitJobExceptionCount = splitJobExceptionCount;
                            if(splitJobExceptionCount){
                                warningObj.splitJobExceptionFilepath = splitJobExceptionFilepath;
                            } 
                           
                        }
                    }

                        console.log(`splitJobExceptionCount: ${splitJobExceptionCount}`);
                        segment.saveSegment(`splitJobExceptionCount: ${splitJobExceptionCount}`);
                        segment.saveSegmentFailure(`splitJobExceptionCount: ${splitJobExceptionCount}`);
                    }




                    if (fs.existsSync(lessSpecialDiscountFilepath)) {
                                
                        console.log(`The invalid Core Cost Sale Mismatch File Csv File exists: ${lessSpecialDiscountFilepath}`);
                        segment.saveSegment(`The invalid Core Cost Sale Mismatch File Csv File exists: ${lessSpecialDiscountFilepath}`);
                        segment.saveSegmentFailure(`The invalid Core Cost Sale Mismatch File Csv File exists: ${lessSpecialDiscountFilepath}`);
                        
                        lessSpecialDiscountExceptionArray = await csv().fromFile(lessSpecialDiscountFilepath);
                        console.log('lessSpecialDiscountExceptionArray:',lessSpecialDiscountExceptionArray);
                        if(lessSpecialDiscountExceptionArray){
                            if(lessSpecialDiscountExceptionArray.length){

                            console.log(`lessSpecialDiscountExceptionArray.length: ${lessSpecialDiscountExceptionArray.length}`);
                            segment.saveSegment(`lessSpecialDiscountExceptionArray.length: ${lessSpecialDiscountExceptionArray.length}`);
                            segment.saveSegmentFailure(`lessSpecialDiscountExceptionArray.length: ${lessSpecialDiscountExceptionArray.length}`);
                            
                            lessSpecialDiscountCount = lessSpecialDiscountExceptionArray.length;
                            console.log('lessSpecialDiscountCount',lessSpecialDiscountCount);
                            warningObj.lessSpecialDiscountCount = lessSpecialDiscountCount;
                            if(lessSpecialDiscountCount){
                                warningObj.lessSpecialDiscountFilepath = lessSpecialDiscountFilepath;
                                
                            } 
                           
                        }
                    }

                        console.log(`lessSpecialDiscountCount: ${lessSpecialDiscountCount}`);
                        segment.saveSegment(`lessSpecialDiscountCount: ${lessSpecialDiscountCount}`);
                        segment.saveSegmentFailure(`lessSpecialDiscountCount: ${lessSpecialDiscountCount}`);
                    }
                
                
               if (stdErrorArray && !haltOverRide) {

                        console.log(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT);
                        console.log(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_DEAD);

                        stdErrorArray.forEach((v, i) => {
                            if (
                                v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT)
                            ) {
                                message = "Halt";
                                haltIdentifier = true;
                                status = false;
                            }

                            if (
                                v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_DEAD)
                            ) {
                                message = "Dead";
                                haltIdentifier = true;
                                status = false;
                            }


                        });
           
                        try {
                            if (
                                stdOutArray &&
                                // stdOutArray.includes(errResp) &&
                                message == "Halt" &&
                                !haltOverRide
                            ) {
                                let deadLetterFilePath = deadLetterPath + "/" + basename;
                                let haltFilePath =
                                    "/home/<USER>/tmp/du-etl-dms-mpk-extractor-work/scheduler-temp/halt/" +
                                    basename;
                                if (fs.existsSync(deadLetterFilePath)) {
                                    fs.copyFile(deadLetterFilePath, haltFilePath, (err) => {
                                        if (err)
                                        {
                                            console.log('err',err);
                                        }
                                        else{
                                        console.log(`${deadLetterFilePath} was copied to ${haltFilePath}`)
                                        }
                                    });
                                } else {
                                    console.log(`${deadLetterFilePath} not exist!`);
                                }
                            } else{
                                console.log('Not a Halt process')
                            }
                        } catch (err) {
                            console.log(err);
                        }
                    }
                    /////////////////////////////Code for Halt and Resume Processor/////////////////////////////////////////////////////////////////////////////


                    console.log(`Mpk : JSON processing job for Store ${storeName} exited with code ${code}`);
                    segment.saveSegment(`Mpk : JSON processing job for Store ${storeName} exited with code ${code}`);
                    segment.saveSegmentFailure(`Mpk : JSON processing job for Store ${storeName} exited with code ${code}`, uniqueFailLogName)

                    var extractionErrorResponse;
                    var errorWarnningMessage;
                    

                    if(extractionId){
                        extractionErrorResponse = await extractionError.displayErrorLogWithSpecific(extractionId, 'Mpk');
                        console.log('extractionErrorResponse:',extractionErrorResponse);
                        if(extractionErrorResponse.status){
                            let resp = JSON.parse(JSON.stringify(extractionErrorResponse.response))
                            let tmpDescritionArray = [];
                            resp.forEach(e => {
                                //tmpDescritionArray.push(e.ro_pull_type+" : "+e.description);
                                tmpDescritionArray.push(e.description);
                                });
                            errorWarnningMessage = tmpDescritionArray.join(", ");
                        }
                    } 
                    console.log('errorWarnningMessage:',errorWarnningMessage);

                    if(errorWarnningMessage){
                        if(errorWarnningMessage.length > 0){
                            warningObj.errorwarningMessage = errorWarnningMessage;
                        }
                    }

                    let failureDirectory = process.cwd() + '/logs/Mpk/failure/';
                    let failurelogFile = failureDirectory + uniqueFailLogName + '.log';

                    var fetchGroupAndStoreName = (job.attrs.data.inputFile).split('-');
                    var groupName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[0] : '';
                    var storeName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[1] : '';
                    console.log(warningObj);
                    var mailTemplateReplacementValues = {
                        dmsType: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.JOB_TYPE : 'UCS',
                        processTypes: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.PROCESS_JSON.JOB_NAME : 'UCS-PROCESS-JSON',
                        subject: `Process JSON for ${mageGroupCode} Completed`,
                        warningObj: warningObj,
                        // thirdPartyUsername: locationId,
                        storeCode: mageStoreCode,
                        groupCode: mageGroupCode
                    };
                    var mailBody = {
                        fromAddress: appConstants.NOTIFICATION.FROMADDRESS,
                        toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                        ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                        attachedfailurelogFile:failurelogFile
                    }
                   
                    if (status) {
                        var opDataFileEtl = path.join(constants.PROCESS_JSON.MPK_ETL_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                        var opDataFileDist = path.join(constants.PROCESS_JSON.MPK_DIST_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                        opDataFileEtl = opDataFileEtl.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
                        var outputFile = opDataFileDist + ' & ' + opDataFileEtl;
                        job.attrs.data.outputFile = outputFile;
                        job.attrs.data.status = status;
                        job.attrs.data.message = message;
                        job.attrs.data.warningMessage = warningObj;
                        job.attrs.data.splitJobExceptionCount =  splitJobExceptionCount;
                        job.attrs.data.lessSpecialDiscountCount = lessSpecialDiscountCount;
                        segment.saveSegment(`Job saved to DB ${JSON.stringify(job)}`);
                        segment.saveSegmentFailure(`Job saved to DB ${JSON.stringify(job)}`, uniqueFailLogName);
                        await job.save();
                        done();
                      
                        // Send notification after process json job completed
                        var basenameCheck1 = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                        var zipPath = path.join(constants.PROCESS_JSON.MPK_BUNDLE_DIR, basenameCheck1);
                        var outPutTemp = constants.PROCESS_JSON.MPK_BUNDLE_DIR+'/temp1';
                        var invoice_missing_count='';
                            
                            fs.createReadStream(zipPath)
                            .pipe(unZipper .Extract({ path: outPutTemp }))
                            .on ('close',async (res)=>{
                            if(fs.existsSync(`${outPutTemp}/processing-result/missing-invoices.csv`)){
                                
                            let  missing_ro_countArray = await csv().fromFile(`${outPutTemp}/processing-result/missing-invoices.csv`);
                            invoice_missing_count = missing_ro_countArray.length;
                                
                    }else{
                             console.log('false');
                         }

                            })
                       
                            await segment.sleep(8000); 

                            warningObj.invoice_missing_count = invoice_missing_count;
                            warningObj.missingInvoiceFilePath = `${outPutTemp}/processing-result/missing-invoices.csv`;

                            var mailTemplateReplacementValues = {
                                dmsType: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.JOB_TYPE : 'UCS',
                                processTypes: etlDMSType.toLowerCase() == constants.JOB_TYPE.toLowerCase() ? constants.PROCESS_JSON.JOB_NAME : 'UCS-PROCESS-JSON',
                                subject: `Process JSON for ${mageGroupCode}  Completed`,
                                warningObj: warningObj,
                                // thirdPartyUsername: locationId,
                                storeCode: mageStoreCode,
                                groupCode: mageGroupCode
                            };

                            var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${mageGroupCode}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Success';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);

                        mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);
                        
                        await segment.sleep(15000); 
                                  if(fs.existsSync(outPutTemp)){
                        
                                removeDirForce(outPutTemp);
                            
                                function removeDirForce(dirPath) {
                                try { var files = fs.readdirSync(dirPath); }
                                catch (e) { return; }
                                if (files.length > 0)
                                    for (var i = 0; i < files.length; i++) {
                                        let filePath = dirPath + '/' + files[i];
                                        if (fs.statSync(filePath).isFile())
                                            fs.unlinkSync(filePath);
                                           
                                        else
                                            removeDirForce(filePath);
                                    }
                                fs.rmdirSync(dirPath);
                               
                            }
                          }
                             else{
                                console.log("file not exit remove");
                             }




                    } else {

                        // Portal update for process json failed
                        let todayDate;
                        let attPayload = {};
                        let projectID;
                        let secondProjectID;
                        let inpObjProject;
                        let inpObjSecondProject;
                        try{
                        todayDate = new Date().toISOString().slice(0, 10);
                        attPayload = agendaObject[extractedObjectIndex];
                        projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                        attPayload['inProjectId'] =  projectID;

                        secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                        attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                        attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                        attPayload.in_retrive_ro_request_on = todayDate;
                        job.attrs.data.splitJobExceptionCount =  splitJobExceptionCount;
                        job.attrs.data.lessSpecialDiscountCount = lessSpecialDiscountCount;
                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                        console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                        if(secondProjectID){
                            inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                            console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                        }
                        } catch(err){
                        console.log(JSON.stringify(err));
                        segment.saveSegment(`Mpk : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                        }
                        segment.saveSegment(`Mpk : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                        segment.saveSegment(`Mpk : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                        try {
                            segment.saveSegment(`Mpk : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                            

                            let parsedData = JSON.parse(inpObjProject.inData);
                             console.log(parsedData.inpObjProject);
                            let  projectIdList =  parsedData.projectIds.split("*");
                            if(projectIdList){
                                 for(const id of projectIdList){
                                    if(id){
                                        inpObjProject.inProjectId = id;
                                        portalUpdate.doPayloadAction(inpObjProject);
                                        console.log(`Mpk Schedule portal call with Project Id FAILURE${id}`);
                
                                    }
                                 }
                            } 



                           segment.saveSegment(`Mpk : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                            if(secondProjectId){
                                let parsedData = JSON.parse(inpObjSecondProject.inData);
                                let  secondProjectIdList =  parsedData.secondProjectIdList.split("*");
                                if(secondProjectIdList){
                                    for(const id of secondProjectIdList){
                                       if(id!=undefined){
                                           inpObjSecondProject.inProjectId = id;
                                           portalUpdate.doPayloadAction(inpObjSecondProject);
                                           console.log(`Mpk Schedule portal call with Second Project Id FAILURE${id}`);
                   
                                       }
                                    }
                               } 
                            }
                         
                        
                        } catch(error) {
                            console.log("Error:", error);
                            segment.saveSegment(`Mpk : doPayloadAction Error - ${JSON.stringify(error)}`); 
                        }
                        //code end for portal update for process json failed


                        await job.fail(new Error(`Error: ${message}`));
                        done();
                        if(haltIdentifier){
                            
                            if(message == 'Halt'){
                                displayMessage = `Halted ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${mageGroupCode} and store ${mageStoreCode}`;
                                mailTemplateReplacementValues.message = displayMessage;
                                mailTemplateReplacementValues.status = 'Halted';
                                mailTemplateReplacementValues.subject = `Process JSON for ${mageGroupCode} - ${mageStoreCode} Halted`;
                            }
                        } else{
                               if(haltOverRide){
                                    mailTemplateReplacementValues.resumeUser = resumeUser ? resumeUser : '';
                               } 
                            
                        var displayMessage = `Failed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${mageGroupCode} and store ${mageStoreCode}`;
                        mailTemplateReplacementValues.message = displayMessage;
                        mailTemplateReplacementValues.status = 'Failed';
                        mailTemplateReplacementValues.subject = `Process JSON for ${mageGroupCode} - ${mageStoreCode} Failed`;
                            }
                        mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                        mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                        mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                        segment.saveSegmentFailure(displayMessage, uniqueFailLogName);
                        // Send notification for failed process json job
                        await segment.sleep(2000);
                        // Send notification for failed process json job
                        mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);
                    }
                    console.log(`Call for next job selection`);
                    segment.saveSegment(`Call method for SharePoint data upload`);
                    segment.saveSegment(`Call for next job selection`);
                    var basenameCheck = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                    var distFile = path.join(constants.PROCESS_JSON.MPK_BUNDLE_DIR, basenameCheck)
                    if (status && fs.existsSync(distFile)) {
                        basename = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                        // await distributeFile(basename, null, updateSolve360Data, errorWarnningMessage);
                        await distributeFile(basename, null, updateSolve360Data, warningObj, etlDMSType);

                    } else {
                        // Process JSON Job Fail ....
                        segment.saveSegment(`Call for next job selection`);
                        await doNextProcess();
                    }
                });
            } else {
                done();
                /**
                * Remove the Initial/recheck schedules
                */
                if(job.attrs.data.operation=="recheck"){
                job.remove(err => {
                    if (!err) {
                        console.log("Initial/recheck schedule for Mpk Process JSON job successfully removed");
                    }
                });
              }
                await doNextProcess();
            }
        });

    return agenda;
}

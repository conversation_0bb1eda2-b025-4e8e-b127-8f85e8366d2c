"use strict";

const constants = require("../constants");
const util = require("../util");
const MpkJobManager = require("../MpkJobManager");
const { spawn } = require("child_process");
const moment = require("moment-timezone");
const gunzip = require("gunzip-file");

const fs = require("fs");
const segment = require("../../SEGMENT/Mpk/segmentManager");
const { v4: uuidv4 } = require('uuid');
/**
 * Function to find the unique stores in an array of stores
 *
 *
 */

var store,
  filePathArray,
  filePath,
  storeIdentification,
  modifiedFileName,
  fileList = [];

const unZipWorkDirectory = constants.UN_ZIP_WORK_DIRECTORY;
// const fileName = constants.FILE_NAME;
const fileName = 'mpk';
// const fileExtension = constants.FILE_EXTENSION;
const fileExtension = 'txt';
Array.prototype.unique = function () {
  var a = this.concat();
  for (var i = 0; i < a.length; ++i) {
    for (var j = i + 1; j < a.length; ++j) {
      if (a[i].enterpriseCode === a[j].enterpriseCode) a.splice(j--, 1);
    }
  }
  return a;
};

function getTimestamp() {
  const now = new Date();
  const year = now.getFullYear().toString().padStart(4, '0');
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  
  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

function unGunzipWebhookInputFiles(inputFilePath,fileDate) {
  return new Promise((resolve, reject) => {
    fileList = [];
    store = inputFilePath;
    filePathArray = store.split("/");
    console.log(filePathArray);
    storeIdentification = store.split("/").reverse()[0];
    console.log("storeIdentification:", storeIdentification);
    segment.saveSegment(
      `Mpk:storeIdentification : ${storeIdentification}`
    );
    // let branchName = storeIdentification.split('_')[3]
    filePathArray.shift();
    filePathArray.pop();
    filePath = "/" + filePathArray.join("/");
    console.log("Mpk filepath",filePath);
    segment.saveSegment(`Mpk:filePath : ${filePath}`);

    if (fs.existsSync(filePath)) {
      // console.log("Directory exists.");
      fs.readdirSync(filePath).forEach((file) => {
      if (file == storeIdentification) {
              fileList.push({ filename: file });
         
        }


   });


      modifiedFileName = fileList[0].filename.replace(".zip",`_${getTimestamp()}.zip`)
      const sourceFile = `${filePath}/${fileList[0].filename}`;
      const destinationFile = `/home/<USER>/tmp/du-etl-dms-mpk-extractor-work/scheduler-temp/mpk-zip-eti/${modifiedFileName}`;

  fs.copyFile(sourceFile, destinationFile, (err) => {
     if (err) {
         console.error('Error copying file:', err);
         resolve({
         status:false,
         response: "Failed to move",
         message:"failed",
       });
     } else {
     console.log('File copied successfully!');
     resolve({
     status: true,
     response: "unzip webhook input files completed",
     message:"success",
     });
   }
 });

    } else {
      segment.saveSegment(`Mpk:${filePath} Directory not exist`);
      console.log("Directory not exist");
      resolve({ status: false, response: "Directory not exist" });
    }
  });
}

/**
 * Function to perform Mpk-Extract
 */
module.exports = function MpkExtractJOB(agenda) {
  var storeCode = "";
  store = "";
  filePathArray = "";
  filePath = "";
  storeIdentification = "";
  fileList = [];
  console.log(
    `Mpk-Extract job started: JobName: ${constants.MPK.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.MPK.CONCURRENCY}`
  );
  segment.saveSegment(
    `Mpk-Extract job started: JobName: ${constants.MPK.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.MPK.CONCURRENCY}`
  );
  agenda.define(
    constants.MPK.JOB_NAME,
    {
      priority: constants.JOB_PRIORITY.HIGHEST,
      concurrency: constants.MPK.CONCURRENCY,
    },
    async (job, done) => {
      const att = job.attrs.data;
      const storeDataArray = att.storeDataArray.reverse();
      var i = 0;
      async function extract(att, job) {
        let inputFilePath, invoiceMasterFilePath,fileDate;
        inputFilePath = att.inputFilePath;
        fileDate = att.fileDate;
        console.log("inputFilePath:", inputFilePath);
        if (att.hasOwnProperty("invoiceMasterCSVFilePath")) {
          invoiceMasterFilePath = att.invoiceMasterCSVFilePath
            ? att.invoiceMasterCSVFilePath
            : "";
        } else {
          invoiceMasterFilePath = "";
        }

        console.log("invoiceMasterFilePath:", invoiceMasterFilePath);

        storeCode = att.mageStoreCode;
        segment.saveSegment(
          `Mpk: Extraction Job Started: ${JSON.stringify(att)}`
        );
        segment.saveSegmentFailure(
          `Mpk: Extraction Job Started: ${JSON.stringify(att)}`,
          storeCode
        );
        if (att.locationId && att.startDate && att.endDate) {
          if (
            !fs.existsSync(
              constants.MPK_DEADLETTER_DIR_PREFIX + "-extracted"
            )
          ) {
            fs.mkdirSync(
              constants.MPK_DEADLETTER_DIR_PREFIX + "-extracted"
            );
          }
          segment.saveSegment(
            `Mpk: Extraction Job Started: ${JSON.stringify(att)}`
          );
          segment.saveSegmentFailure(
            `Mpk: Extraction Job Started: ${JSON.stringify(att)}`,
            storeCode
          );

          let actualFileName;
          actualFileName = inputFilePath.split("/").reverse()[0];
          console.log("actualFileName:", actualFileName);
          segment.saveSegmentFailure(
            `Mpk: ActualFileName: ${actualFileName}`,
            storeCode
          );

          var startTime = new Date(moment().utc());
          att.startTime = startTime;
          var oldStoreArray = job.attrs.data.storeDataArray;
          var newStoreArray = [att];
          oldStoreArray.map((data) => {
            if (
              data.locationId === newStoreArray[0].locationId &&
              data.mageStoreCode === newStoreArray[0].mageStoreCode
            ) {
              data = newStoreArray;
            }
          });
          var _storeArray = oldStoreArray;
          // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
          job.attrs.data.storeDataArray = _storeArray;
          segment.saveSegment(
            `Extraction Job Data: ${JSON.stringify(_storeArray)}`
          );
          segment.saveSegmentFailure(
            `Extraction Job Data: ${JSON.stringify(_storeArray)}`,
            storeCode
          );
         
          await job.save();
         let response = await unGunzipWebhookInputFiles(inputFilePath, fileDate);
         if(response){
          att.endTime = new Date(moment().utc());
          att.uniqueId = uuidv4();
          att.status = response.status;
          att.message =response.message;
          att.inputFilePath =`/etl/etl-vagrant/etl-mpk/mpk-rawzip/${modifiedFileName}`;

         }
         await job.save();
         done();
       
        
        } else {
          console.error(
            "Mpk : Store data Extraction attributes not defined"
          );
          segment.saveSegment(
            "Mpk : Store data Extraction attributes not defined"
          );
        }
      }
      if (att.runNow) {
        // Check whether it is for run now or not, if yes, no need to check time frame
        segment.saveSegment(
          `Mpk : runNow : Check whether it is for run now or not, if yes, no need to check time frame ${JSON.stringify(
            storeDataArray[0]
          )}`
        );
        await extract(storeDataArray[0], job);
      } else if (util.checkExtractTimeFrame()) {
        segment.saveSegment(
          `Mpk : Check time frame and start extraction ${JSON.stringify(
            storeDataArray[0]
          )}`
        );
        await extract(storeDataArray[0], job);
      } else {
        // Auto schedule full Group wise schedule for tomorrow
        segment.saveSegment(
          `Mpk : Auto schedule full Group wise schedule for tomorrow`
        );
        MpkJobManager.scheduleMpkExtractJob(
          MpkJobManager.createScheduleObject(job),
          true
        );
        job.remove();
      }
    }
  );

  agenda.on("start", (job) => {
    console.log(`Mpk : Job ${job.attrs.name}_${job.attrs._id} starting`);
  });

  agenda.on("complete", (job) => {
    console.log(`Mpk : Job ${job.attrs.name}_${job.attrs._id} finished`);
  });

  agenda.on("fail", (err, job) => {
    console.log(
      `Mpk : Job ${job.attrs.name}_${job.attrs._id} failed with error: ${err.message} `
    );
  });
  return agenda;
};

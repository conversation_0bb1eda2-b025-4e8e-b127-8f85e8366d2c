const MpkJobManager = require("../MpkJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all Mpk-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllMpkExtractJobs(_, args) {
      return MpkJobManager.getAllMpkExtractJobs();
    },

    /**
     * Query to get all Automate Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllMpkProcessJSONJobs(_, args) {
      return MpkJobManager.getAllMpkProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule Mpk-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleMpkExtractJob(_, args) {
      return MpkJobManager.scheduleMpkExtractJob(args.input);
    },

    /**
     * Mutation to run a Mpk-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowMpkExtractJobByStore(_, args) {
      return MpkJobManager.runNowMpkExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a Mpk-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelMpkExtractJobByStore(_, args) {
      return MpkJobManager.cancelMpkExtractJobByStore(args.input);
    },
  },
  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;

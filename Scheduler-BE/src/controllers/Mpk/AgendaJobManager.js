const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeMpkProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "mpk-process-json") {
      initializeMpkProcessJSON = true
    }
    if (type.split('-')[0] == 'mpk') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the mpk-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeMpkProcessJSON) {
    try {
      // Initialize Mpk Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("Mpk :  Process JSON schedule started");
      segment.saveSegment("Mpk :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("Mpk Extraction Processing Not Enabled - Pass Job Type mpk-process-json To Enable");
    segment.saveSegment("Mpk Extraction Processing Not Enabled - Pass Job Type mpk-process-json To Enable");
  }
  return true;
}

const TekionJobManager = require("../TekionJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all Tekion-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllTekionExtractJobs(_, args) {
      return TekionJobManager.getAllTekionExtractJobs();
    },

    /**
     * Query to get all Automate Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllTekionProcessJSONJobs(_, args) {
      return TekionJobManager.getAllTekionProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule Tekion-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleTekionExtractJob(_, args) {
      return TekionJobManager.scheduleTekionExtractJob(args.input);
    },

    /**
     * Mutation to run a Tekion-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowTekionExtractJobByStore(_, args) {
      return TekionJobManager.runNowTekionExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a Tekion-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelTekionExtractJobByStore(_, args) {
      return TekionJobManager.cancelTekionExtractJobByStore(args.input);
    },
  },
  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;

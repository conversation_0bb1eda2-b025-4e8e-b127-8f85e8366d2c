const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeTekionProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "tekion-process-json") {
      initializeTekionProcessJSON = true
    }
    if (type.split('-')[0] == 'tekion') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the tekion-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeTekionProcessJSON) {
    try {
      // Initialize Tekion Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("Tekion :  Process JSON schedule started");
      segment.saveSegment("Tekion :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("Tekion Extraction Processing Not Enabled - Pass Job Type tekion-process-json To Enable");
    segment.saveSegment("Tekion Extraction Processing Not Enabled - Pass Job Type tekion-process-json To Enable");
  }
  return true;
}

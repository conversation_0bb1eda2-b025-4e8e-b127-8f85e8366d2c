"use strict";

const constants = require("../constants");
const util = require("../util");
const TekionJobManager = require("../TekionJobManager");
const { spawn } = require("child_process");
const moment = require("moment-timezone");
const gunzip = require("gunzip-file");

const fs = require("fs");
const segment = require("../../SEGMENT/Tekion/segmentManager");
const { v4: uuidv4 } = require('uuid');
/**
 * Function to find the unique stores in an array of stores
 *
 *
 */

var store,
  filePathArray,
  filePath,
  storeIdentification,
  modifiedFileName,
  fileList = [],
  mageGroupCode,
  storeCode;

const unZipWorkDirectory = constants.UN_ZIP_WORK_DIRECTORY;
// const fileName = constants.FILE_NAME;
const fileName = 'tekion';
// const fileExtension = constants.FILE_EXTENSION;
const fileExtension = 'txt';
Array.prototype.unique = function () {
  var a = this.concat();
  for (var i = 0; i < a.length; ++i) {
    for (var j = i + 1; j < a.length; ++j) {
      if (a[i].enterpriseCode === a[j].enterpriseCode) a.splice(j--, 1);
    }
  }
  return a;
};

function getTimestamp() {
  const now = new Date();
  const year = now.getFullYear().toString().padStart(4, '0');
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  
  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

function unGunzipWebhookInputFiles(inputFilePath,fileDate) {
  return new Promise((resolve, reject) => {
    segment.saveSegment(`inputFilePath : ${inputFilePath}`);
    fileList = [];
    store = inputFilePath;
    filePathArray = store.split("/");
    segment.saveSegment(`inputFilePath : ${filePathArray}`);    
    console.log(filePathArray);
    storeIdentification = store.split("/").reverse()[0];    
    console.log("storeIdentification:", storeIdentification);
    segment.saveSegment(
      `Tekion:storeIdentification : ${storeIdentification}`
    );
    // let branchName = storeIdentification.split('_')[3]
    filePathArray.shift();
    filePathArray.pop();
    segment.saveSegment(`filePathArray pop : ${filePathArray}`);    

    filePath = "/" + filePathArray.join("/");
    console.log("Tekion filepath",filePath);
    segment.saveSegment(`Tekion:filePath : ${filePath}`);

    if (fs.existsSync(filePath)) {
      segment.saveSegment(`Directory exists. : ${filePath}`);
      // console.log("Directory exists.");
      fs.readdirSync(filePath).forEach((file) => {
        segment.saveSegment(`Tekion:file : ${file}`);
      if (file == storeIdentification) {
              fileList.push({ filename: file });
         
        }
        segment.saveSegment(`Tekion:fileList : ${JSON.stringify(fileList)}`);

   });


      console.log("mageGroupcode*******************************",mageGroupCode);
      console.log("mageStorecode*******************************",storeCode);
         const mageGroupCodeWithNoSpace = mageGroupCode.replace(/\s+/g, '');
         console.log(mageGroupCodeWithNoSpace);
         console.log("fileList[0].filename@@@@@@@@@@@@@@@@@@@@@@@",fileList[0].filename);
        //  let oldPath = `/home/<USER>/tmp/du-etl-dms-tekion-extractor-work/scheduler-temp/tekion-zip-eti/${fileList[0].filename}`;

      // fileList[0].filename = `${mageGroupCodeWithNoSpace}_${storeCode}_${getTimestamp()}.zip`
      console.log("fileList[0].filename After modification@@@@@@@@@@@@@@@@@@@",fileList[0].filename);

      // modifiedFileName = fileList[0].filename.replace(".zip",`_${getTimestamp()}.zip`)
      modifiedFileName = `${mageGroupCodeWithNoSpace}-${storeCode}-${getTimestamp()}.zip` ;
      console.log("modifiedFileName%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%",modifiedFileName);
      // let newPath = `/home/<USER>/tmp/du-etl-dms-tekion-extractor-work/scheduler-temp/tekion-zip-eti/${fileList[0].filename}`

      

      //    fs.rename(oldPath, newPath, (err) => {
      //     if (err) {
      //         console.error('Error renaming file:', err);
      //     } else {
      //         console.log('File renamed successfully!');
      //     }
      // });


      const sourceFile = `${filePath}/${fileList[0].filename}`;
      const destinationFile = `/home/<USER>/tmp/du-etl-dms-tekion-extractor-work/scheduler-temp/tekion-zip-eti/${modifiedFileName}`;

  fs.copyFile(sourceFile, destinationFile, (err) => {
     if (err) {
         console.error('Error copying file:', err);
         resolve({
         status:false,
         response: "Failed to move",
         message:"failed",
       });
     } else {
     console.log('File copied successfully!');
     resolve({
     status: true,
     response: "unzip webhook input files completed",
     message:"success",
     });
   }
 });

    } else {
      segment.saveSegment(`Tekion:${filePath} Directory not exist`);
      console.log("Directory not exist");
      resolve({ status: false, response: "Directory not exist" });
    }
  });
}

/**
 * Function to perform Tekion-Extract
 */
module.exports = function TekionExtractJOB(agenda) {
  store = "";
  filePathArray = "";
  filePath = "";
  storeIdentification = "";
  fileList = [];
  console.log(
    `Tekion-Extract job started: JobName: ${constants.TEKION.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.TEKION.CONCURRENCY}`
  );
  segment.saveSegment(
    `Tekion-Extract job started: JobName: ${constants.TEKION.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.TEKION.CONCURRENCY}`
  );
  agenda.define(
    constants.TEKION.JOB_NAME,
    {
      priority: constants.JOB_PRIORITY.HIGHEST,
      concurrency: constants.TEKION.CONCURRENCY,
    },
    async (job, done) => {
      const att = job.attrs.data;
      const storeDataArray = att.storeDataArray.reverse();
      var i = 0;
      async function extract(att, job) {
        let inputFilePath, invoiceMasterFilePath,fileDate;
        inputFilePath = att.inputFilePath;
        fileDate = att.fileDate;
        console.log("inputFilePath:", inputFilePath);
        if (att.hasOwnProperty("invoiceMasterCSVFilePath")) {
          invoiceMasterFilePath = att.invoiceMasterCSVFilePath
            ? att.invoiceMasterCSVFilePath
            : "";
        } else {
          invoiceMasterFilePath = "";
        }

        console.log("invoiceMasterFilePath:", invoiceMasterFilePath);

        storeCode = att.mageStoreCode;
        mageGroupCode = att.mageGroupCode;
        segment.saveSegment(
          `Tekion: Extraction Job Started: ${JSON.stringify(att)}`
        );
        segment.saveSegmentFailure(
          `Tekion: Extraction Job Started: ${JSON.stringify(att)}`,
          storeCode
        );
        if (att.locationId && att.startDate && att.endDate) {
          if (
            !fs.existsSync(
              constants.TEKION_DEADLETTER_DIR_PREFIX + "-extracted"
            )
          ) {
            fs.mkdirSync(
              constants.TEKION_DEADLETTER_DIR_PREFIX + "-extracted"
            );
          }
          segment.saveSegment(
            `Tekion: Extraction Job Started: ${JSON.stringify(att)}`
          );
          segment.saveSegmentFailure(
            `Tekion: Extraction Job Started: ${JSON.stringify(att)}`,
            storeCode
          );

          let actualFileName;
          actualFileName = inputFilePath.split("/").reverse()[0];
          console.log("actualFileName:", actualFileName);
          segment.saveSegmentFailure(
            `Tekion: ActualFileName: ${actualFileName}`,
            storeCode
          );

          var startTime = new Date(moment().utc());
          att.startTime = startTime;
          var oldStoreArray = job.attrs.data.storeDataArray;
          var newStoreArray = [att];
          oldStoreArray.map((data) => {
            if (
              data.locationId === newStoreArray[0].locationId &&
              data.mageStoreCode === newStoreArray[0].mageStoreCode
            ) {
              data = newStoreArray;
            }
          });
          var _storeArray = oldStoreArray;
          // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
          job.attrs.data.storeDataArray = _storeArray;
          segment.saveSegment(
            `Extraction Job Data: ${JSON.stringify(_storeArray)}`
          );
          segment.saveSegmentFailure(
            `Extraction Job Data: ${JSON.stringify(_storeArray)}`,
            storeCode
          );
         
          await job.save();
         let response = await unGunzipWebhookInputFiles(inputFilePath, fileDate);
         if(response){
          att.endTime = new Date(moment().utc());
          att.uniqueId = util.generateUniqueId();
          att.status = response.status;
          att.message =response.message;
          att.inputFilePath =`/etl/etl-vagrant/etl-tekion/tekion-rawzip/${modifiedFileName}`;

         }
         await job.save();
         done();
       
        
        } else {
          console.error(
            "Tekion : Store data Extraction attributes not defined"
          );
          segment.saveSegment(
            "Tekion : Store data Extraction attributes not defined"
          );
        }
      }
      if (att.runNow) {
        // Check whether it is for run now or not, if yes, no need to check time frame
        segment.saveSegment(
          `Tekion : runNow : Check whether it is for run now or not, if yes, no need to check time frame ${JSON.stringify(
            storeDataArray[0]
          )}`
        );
        await extract(storeDataArray[0], job);
      } else if (util.checkExtractTimeFrame()) {
        segment.saveSegment(
          `Tekion : Check time frame and start extraction ${JSON.stringify(
            storeDataArray[0]
          )}`
        );
        await extract(storeDataArray[0], job);
      } else {
        // Auto schedule full Group wise schedule for tomorrow
        segment.saveSegment(
          `Tekion : Auto schedule full Group wise schedule for tomorrow`
        );
        TekionJobManager.scheduleTekionExtractJob(
          TekionJobManager.createScheduleObject(job),
          true
        );
        job.remove();
      }
    }
  );

  agenda.on("start", (job) => {
    console.log(`Tekion : Job ${job.attrs.name}_${job.attrs._id} starting`);
  });

  agenda.on("complete", (job) => {
    console.log(`Tekion : Job ${job.attrs.name}_${job.attrs._id} finished`);
  });

  agenda.on("fail", (err, job) => {
    console.log(
      `Tekion : Job ${job.attrs.name}_${job.attrs._id} failed with error: ${err.message} `
    );
  });
  return agenda;
};

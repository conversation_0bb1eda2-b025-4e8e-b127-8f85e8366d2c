const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeQuorumProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "quorum-process-json") {
      initializeQuorumProcessJSON = true
    }
    if (type.split('-')[0] == 'quorum') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the quorum-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeQuorumProcessJSON) {
    try {
      // Initialize Quorum Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("Quorum :  Process JSON schedule started");
      segment.saveSegment("Quorum :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("Quorum Extraction Processing Not Enabled - Pass Job Type quorum-process-json To Enable");
    segment.saveSegment("Quorum Extraction Processing Not Enabled - Pass Job Type quorum-process-json To Enable");
  }
  return true;
}

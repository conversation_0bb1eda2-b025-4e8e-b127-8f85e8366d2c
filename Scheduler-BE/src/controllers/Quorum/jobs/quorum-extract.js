"use strict";

const constants = require("../constants");
const util = require("../util");
const QuorumJobManager = require("../QuorumJobManager");
const { spawn } = require("child_process");
const moment = require("moment-timezone");
const gunzip = require("gunzip-file");
const commonUtil = require('../../../../src/common/util');
const fs = require("fs");
const segment = require("../../SEGMENT/Quorum/segmentManager");
const { v4: uuidv4 } = require('uuid');
/**
 * Function to find the unique stores in an array of stores
 *
 *
 */

var store,
  filePathArray,
  filePath,
  storeIdentification,
  modifiedFileName,
  fileList = [],
  mageGroupCode,
  storeCode;


const unZipWorkDirectory = constants.UN_ZIP_WORK_DIRECTORY;
// const fileName = constants.FILE_NAME;
const fileName = 'quorum';
// const fileExtension = constants.FILE_EXTENSION;
const fileExtension = 'txt';
Array.prototype.unique = function () {
  var a = this.concat();
  for (var i = 0; i < a.length; ++i) {
    for (var j = i + 1; j < a.length; ++j) {
      if (a[i].enterpriseCode === a[j].enterpriseCode) a.splice(j--, 1);
    }
  }
  return a;
};

function getTimestamp() {
  const now = new Date();
  const year = now.getFullYear().toString().padStart(4, '0');
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  
  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

function unGunzipWebhookInputFiles(inputFilePath,fileDate,job,userName) {
  return new Promise((resolve, reject) => {
    fileList = [];
    store = inputFilePath;
    filePathArray = store.split("/");
    console.log(filePathArray);
    storeIdentification = store.split("/").reverse()[0];
    console.log("storeIdentification:", storeIdentification);
    segment.saveSegment(
      `Quorum:storeIdentification : ${storeIdentification}`
    );
    // let branchName = storeIdentification.split('_')[3]
    filePathArray.shift();
    filePathArray.pop();
    filePath = "/" + filePathArray.join("/");
    console.log("Quorum filepath",filePath);
    segment.saveSegment(`Quorum:filePath : ${filePath}`);
   console.log("filePath$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",filePath);
    if (fs.existsSync(filePath)) {
      // console.log("Directory exists.");
      fs.readdirSync(filePath).forEach((file) => {
        console.log("File@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",file);
      if (file == storeIdentification) {
              fileList.push({ filename: file });
         
        }


   });

   console.log("mageGroupcode*******************************",mageGroupCode);
   console.log("mageStorecode*******************************",storeCode);
      const mageGroupCodeWithNoSpace = mageGroupCode.replace(/\s+/g, '');
      console.log(mageGroupCodeWithNoSpace);
      console.log("FlieList@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",fileList);
      if(fileList[0].filename){
        console.log("fileList[0].filename@@@@@@@@@@@@@@@@@@@@@@@",fileList[0].filename);
      }
      
  

     modifiedFileName = `${mageGroupCodeWithNoSpace}-${storeCode}-${getTimestamp()}.zip` ;
     console.log("modifiedFileName%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%",modifiedFileName);
   

  


      // modifiedFileName = fileList[0].filename.replace(".zip",`_${getTimestamp()}.zip`)
      const sourceFile = `${filePath}/${fileList[0].filename}`;
      const destinationFile = `/home/<USER>/tmp/du-etl-dms-quorum-extractor-work/scheduler-temp/quorum-zip-eti/${modifiedFileName}`;

      try {
        fs.copyFile(sourceFile, destinationFile, async (err) => {
          try {
            if (err) {
              segment.saveSegment(`job:${job}userName:${userName}error:Error copying file: ${err}`);
              resolve({
                status: false,
                response: "Failed to move",
                message: "failed",
              });
            } else {
              await commonUtil.processCompanyData(job, userName, modifiedFileName, destinationFile, constants.DMS);      
              resolve({
                status: true,
                response: "unzip webhook input files completed",
                message: "success",
              });
            }
          } catch (innerError) {
            segment.saveSegment(`job:${job}userName:${userName}error:Error during processing: ${innerError}`);
            resolve({
              status: false,
              response: "Error during processing",
              message: "failed",
            });
          }
        });
      } catch (outerError) {
        segment.saveSegment(`job:${job}userName:${userName}error:Unexpected error: ${outerError}`);
        resolve({
          status: false,
          response: "Unexpected failure",
          message: "failed",
        });
      }
    } else {
      segment.saveSegment(`Quorum:${filePath} Directory not exist`);
      console.log("Directory not exist");
      resolve({ status: false, response: "Directory not exist" });
    }
  });
}

/**
 * Function to perform Quorum-Extract
 */
module.exports = function QuorumExtractJOB(agenda) {
  store = "";
  filePathArray = "";
  filePath = "";
  storeIdentification = "";
  fileList = [];
  console.log(
    `Quorum-Extract job started: JobName: ${constants.QUORUM.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.QUORUM.CONCURRENCY}`
  );
  segment.saveSegment(
    `Quorum-Extract job started: JobName: ${constants.QUORUM.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.QUORUM.CONCURRENCY}`
  );
  agenda.define(
    constants.QUORUM.JOB_NAME,
    {
      priority: constants.JOB_PRIORITY.HIGHEST,
      concurrency: constants.QUORUM.CONCURRENCY,
    },
    async (job, done) => {
      const att = job.attrs.data;
      const storeDataArray = att.storeDataArray.reverse();
      var i = 0;
      async function extract(att, job) {
        let inputFilePath, invoiceMasterFilePath,fileDate;
        inputFilePath = att.inputFilePath;
        let userName = att.userName ? att.userName : "bilbi_john@up";
        fileDate = att.fileDate;
        console.log("inputFilePath:", inputFilePath);
        if (att.hasOwnProperty("invoiceMasterCSVFilePath")) {
          invoiceMasterFilePath = att.invoiceMasterCSVFilePath
            ? att.invoiceMasterCSVFilePath
            : "";
        } else {
          invoiceMasterFilePath = "";
        }

        console.log("invoiceMasterFilePath:", invoiceMasterFilePath);

        storeCode = att.mageStoreCode;
        mageGroupCode = att.mageGroupCode;
        segment.saveSegment(
          `Quorum: Extraction Job Started: ${JSON.stringify(att)}`
        );
        segment.saveSegmentFailure(
          `Quorum: Extraction Job Started: ${JSON.stringify(att)}`,
          storeCode
        );
        if (att.locationId && att.startDate && att.endDate) {
          if (
            !fs.existsSync(
              constants.QUORUM_DEADLETTER_DIR_PREFIX + "-extracted"
            )
          ) {
            fs.mkdirSync(
              constants.QUORUM_DEADLETTER_DIR_PREFIX + "-extracted"
            );
          }
          segment.saveSegment(
            `Quorum: Extraction Job Started: ${JSON.stringify(att)}`
          );
          segment.saveSegmentFailure(
            `Quorum: Extraction Job Started: ${JSON.stringify(att)}`,
            storeCode
          );

          let actualFileName;
          actualFileName = inputFilePath.split("/").reverse()[0];
          console.log("actualFileName:", actualFileName);
          segment.saveSegmentFailure(
            `Quorum: ActualFileName: ${actualFileName}`,
            storeCode
          );

          var startTime = new Date(moment().utc());
          att.startTime = startTime;
          var oldStoreArray = job.attrs.data.storeDataArray;
          var newStoreArray = [att];
          oldStoreArray.map((data) => {
            if (
              data.locationId === newStoreArray[0].locationId &&
              data.mageStoreCode === newStoreArray[0].mageStoreCode
            ) {
              data = newStoreArray;
            }
          });
          var _storeArray = oldStoreArray;
          // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
          job.attrs.data.storeDataArray = _storeArray;
          segment.saveSegment(
            `Extraction Job Data: ${JSON.stringify(_storeArray)}`
          );
          segment.saveSegmentFailure(
            `Extraction Job Data: ${JSON.stringify(_storeArray)}`,
            storeCode
          );
          att.uniqueId =  util.generateUniqueId();
          await job.save();
          segment.saveSegment(
            `Extraction Job Data: ${JSON.stringify(job)}userName:${userName}`
          );
         let response = await unGunzipWebhookInputFiles(inputFilePath, fileDate, job, userName);
         if(response){
          att.endTime = new Date(moment().utc());
          att.uniqueId = att.uniqueId ? att.uniqueId : util.generateUniqueId();
          att.status = response.status;
          att.message =response.message;
          att.inputFilePath =`/etl/etl-vagrant/etl-quorum/quorum-rawzip/${modifiedFileName}`;

         }
         await job.save();
         done();
       
        
        } else {
          console.error(
            "Quorum : Store data Extraction attributes not defined"
          );
          segment.saveSegment(
            "Quorum : Store data Extraction attributes not defined"
          );
        }
      }
      if (att.runNow) {
        // Check whether it is for run now or not, if yes, no need to check time frame
        segment.saveSegment(
          `Quorum : runNow : Check whether it is for run now or not, if yes, no need to check time frame ${JSON.stringify(
            storeDataArray[0]
          )}`
        );
        await extract(storeDataArray[0], job);
      } else if (util.checkExtractTimeFrame()) {
        segment.saveSegment(
          `Quorum : Check time frame and start extraction ${JSON.stringify(
            storeDataArray[0]
          )}`
        );
        await extract(storeDataArray[0], job);
      } else {
        // Auto schedule full Group wise schedule for tomorrow
        segment.saveSegment(
          `Quorum : Auto schedule full Group wise schedule for tomorrow`
        );
        QuorumJobManager.scheduleQuorumExtractJob(
          QuorumJobManager.createScheduleObject(job),
          true
        );
        job.remove();
      }
    }
  );

  agenda.on("start", (job) => {
    console.log(`Quorum : Job ${job.attrs.name}_${job.attrs._id} starting`);
  });

  agenda.on("complete", (job) => {
    console.log(`Quorum : Job ${job.attrs.name}_${job.attrs._id} finished`);
  });

  agenda.on("fail", (err, job) => {
    console.log(
      `Quorum : Job ${job.attrs.name}_${job.attrs._id} failed with error: ${err.message} `
    );
  });
  return agenda;
};

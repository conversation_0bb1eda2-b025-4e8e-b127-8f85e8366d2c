const QuorumJobManager = require("../QuorumJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all Quorum-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllQuorumExtractJobs(_, args) {
      return QuorumJobManager.getAllQuorumExtractJobs();
    },

    /**
     * Query to get all Automate Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllQuorumProcessJSONJobs(_, args) {
      return QuorumJobManager.getAllQuorumProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule Quorum-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleQuorumExtractJob(_, args) {
      return QuorumJobManager.scheduleQuorumExtractJob(args.input);
    },

    /**
     * Mutation to run a Quorum-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowQuorumExtractJobByStore(_, args) {
      return QuorumJobManager.runNowQuorumExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a Quorum-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelQuorumExtractJobByStore(_, args) {
      return QuorumJobManager.cancelQuorumExtractJobByStore(args.input);
    },
  },
  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;

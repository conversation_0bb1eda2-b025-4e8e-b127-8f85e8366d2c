var resolvers = {
  Query: {

    /**
     * Query to get all CDK-Extract schedules
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllTest(_, args) {
      return [
        { id: "1", test: "test1" },
        { id: "2", test: "test2" },
        { id: "3", test: "test3" },
      ];
    },

  },
  Mutation: {
    setTest(_, args) {
      return "Success";
    }
  },
};
module.exports = resolvers;

var gt = require("graphql-tools");
var resolvers = require("./resolvers.js");

var typeDefs = `

# This type specifies the entry points into our API

type Test {
  id: String!
  test: String!
}

type Query {
  getAllTest: [Test]
}

# Mutations
type Mutation {
  # Mutation to schedule a CDK extraction job
  setTest (input: String): String,
}

schema {
  query: Query,
  mutation: Mutation
}
`;
var schema = gt.makeExecutableSchema({ typeDefs, resolvers });
module.exports = schema;

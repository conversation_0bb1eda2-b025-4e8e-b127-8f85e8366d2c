const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");
module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeProcessXML = false;

  jobTypes.forEach(type => {
    if (type == "cdk-process-xml") {
      initializeProcessXML = true
    }
    if (type.split('-')[0] == 'cdk') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the process-xml job
  // and the rest of the operations will be done by the Job itself.
  if (initializeProcessXML) {
    try {
      // Initialize Process XML job
      await agenda.now(constants.PROCESS_XML.JOB_NAME, { operation: "start" });
      console.log("CDK : Process XML schedule started");
      segment.saveSegment("CDK : Process XML schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("CDK Extraction Processing Not Enabled - Pass Job Type cdk-process-xml To Enable");
    segment.saveSegment("CDK Extraction Processing Not Enabled - Pass Job Type cdk-process-xml To Enable");
  }
  return true;
}

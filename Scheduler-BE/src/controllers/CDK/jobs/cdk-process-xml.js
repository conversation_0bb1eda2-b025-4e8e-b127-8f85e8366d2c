"use strict";

const constants = require("../constants");
const util = require("../util");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const { spawn } = require("child_process");
const path = require('path');
const fs = require("fs");
const moment = require("moment-timezone");
const segment = require("../../SEGMENT/CDK3PA/segmentManager");
const sharePoint = require("../../../routes/sharePoint");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const stripAnsi = require('strip-ansi');
const extractionError = require('../../../../src/common/extractionError');
const unZipper = require("unzipper");
const SetProcessJobStatus = require('../../../model/setProcessJobStatus');

var Agenda = require("../../agenda");
const csv=require('csvtojson');
const csv1 = require('csv-parser');

/**
 * Function to perform processing of XML file downloaded through CDK-Extract job
 */
module.exports = async function ProcessXmlJOB(agenda) {

    var distributeFile = async function (fileName, rerunFlag, updateSolve360Data, warningObj, jobId) {
        var stdErrorArray;
        var distDir = path.join(process.env.DIST_DIR, fileName);
        var etlDir = path.join(process.env.ETL_DIR, fileName);
        etlDir = etlDir.replace(constants.PROCESS_XML.REPLACE_STRING.FROM, constants.PROCESS_XML.REPLACE_STRING.TO);
        var filePath = path.join(process.env.BUNDLE_DIR, fileName);
        const distributeFile = spawn("bash",
            [
                'send-bundle-live-hpdog', filePath, rerunFlag
            ], {
            cwd: constants.PROCESS_XML.DISTRIBUTE_CMD_PATH,
            env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
        }).on('error', function (err) {
            console.log("error :", err);
            segment.saveSegment(`error: ${err}`);
        });
        console.log(`Start processing of distribution`);
        segment.saveSegment(`Start processing distribution`);
        process.stdin.pipe(distributeFile.stdin);
        distributeFile.stdout.on("data", async (data) => {
            console.log(`stdout: ${data}`);
            segment.saveSegment(`stdout: ${data}`);
        });

        distributeFile.stderr.on("data", async (data) => {
            console.log(`stderr: ${data}`);
            stdErrorArray += data.toString() + ' ';
            segment.saveSegment(`stderr: ${data}`);
        });

        distributeFile.on("close", async (code) => {
            var message = "n/a";
            var status = false;
            if (code == constants.STATUS_CODE.SUCCESS) {
                status = true;
                message = "Success";
            } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                message = "Distribution failed, general death";
            } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                message = "Distribution failed";
            }
            segment.saveSegment(`close: ${message}`);
            if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_XML.ERROR_CHECKING_LABELS.ZIP_FILE_EXIST_CHECK)) {
                status = false;
                message = "Distribution failed. Zip File Must Exist";
                segment.saveSegment(message);
            }
            /**
              * Upload files to SharePoint
              */
            if (status) {
                sharePoint.initSharePoint(distDir, constants.JOB_TYPE, rerunFlag, updateSolve360Data, warningObj, 0, jobId);//Upload dist directory zip file to sharepoint
            }
            await doNextProcess();
        });
    }

    var doNextProcess = async function () {
        segment.saveSegment(`Call for findOldestZipFile method to check if any stores remaining to process`);
        await segment.sleep(5000);
        const extractZip = await util.findOldestZipFile(constants.CDK_SCHEDULER_ETI_DIR);
        if (fs.existsSync(`/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/${extractZip}`)) {
            console.log(`file Name ${extractZip} exists!`);
          } else {
            console.log(`file Name ${extractZip} does not exists`);
        }
        if (extractZip && fs.existsSync(`/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/${extractZip}`) ) {
            console.log(`Found one Store extraction > ${extractZip} to process now`);
            segment.saveSegment(`Found one Store extraction > ${extractZip} to process now`);
            try {
                var createdAt = extractZip.slice(0, extractZip.length - 4).split("-").reverse()[0];
                var storeIdArrayPos = 0;
                if (extractZip.includes(constants.PROCESS_XML.REPLACE_STRING.DO_PROXY_FROM)) {
                    storeIdArrayPos = 1;
                }
                var createdAt = extractZip.slice(0, extractZip.length - 4).split("-").reverse()[storeIdArrayPos];
                await agenda.now(constants.PROCESS_XML.JOB_NAME, {
                    inputFile: extractZip,
                    createdAt: createdAt,
                    operation: "xml-processing"
                });
                console.log(`Process XML schedule started with file > ${extractZip}`);
                segment.saveSegment(`Process XML schedule started with file > ${extractZip}`);
            } catch (error) {
                console.error(error);
            }

        } else {
            console.log(`CDK3PA: No Store's zip file to process now, will check ${constants.PROCESS_XML.TIME_GAP}`);
            //segment.saveSegment(`CDK3PA: No Store's zip file to process now, will check ${constants.PROCESS_XML.TIME_GAP}`);
            try {
                await agenda.schedule(`${constants.PROCESS_XML.TIME_GAP}`, constants.PROCESS_XML.JOB_NAME, { operation: "recheck" });
                console.log(`CDK3PA: Process XML schedule will run ${constants.PROCESS_XML.TIME_GAP}`);
                //segment.saveSegment(`CDK3PA: Process XML schedule will run ${constants.PROCESS_XML.TIME_GAP}`);
            } catch (error) {
                console.error(error);
            }
        }
    }

    console.log(
        `Process XML job started: JobName: ${constants.PROCESS_XML.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_XML.CONCURRENCY}`
    );
    segment.saveSegment(`Process XML job started: JobName: ${constants.PROCESS_XML.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_XML.CONCURRENCY}`);

    agenda.define(constants.PROCESS_XML.JOB_NAME,
        { 
            priority: constants.JOB_PRIORITY.HIGHEST, 
            concurrency: constants.PROCESS_XML.CONCURRENCY,
            lockLifetime: 3 * 60 * 60 * 1000 // 3 hours in milliseconds
       }, 
        async (job, done) => {
            console.log("TEST Processor***********************************************************************");
            const touch = setInterval(() => job.touch(), 1 * 60 * 1000);
            const att = job.attrs.data;
            var extractZip = null;
            var stdErrorArray = [];
            var stdOutArray = [];
            var inpFile = att.inputFile ? path.join(constants.CDK_SCHEDULER_ETI_DIR, att.inputFile) : '';
            var processorStatus;
            if (att.inputFile) {
                if (fs.existsSync(inpFile)) {
                    if (!fs.existsSync(constants.DEADLETTER_DIR_PREFIX + '-processed')) {
                        fs.mkdirSync(constants.DEADLETTER_DIR_PREFIX + '-processed');
                    }
                    extractZip = att.inputFile;
                    let basename = path.basename(extractZip);
                    var doProxyFromPgDump = false;
                    let dealerId;
                    // if (basename.includes(constants.PROCESS_XML.REPLACE_STRING.DO_PROXY_TO)) {
                    if (basename.includes(constants.PROCESS_XML.REPLACE_STRING.DO_PROXY_FROM)) {
                        doProxyFromPgDump = true;
                        dealerId = basename.split("-").reverse()[2];
                    } else {
                        dealerId = basename.split("-").reverse()[1];
                        doProxyFromPgDump = false;
                    }
                    job.attrs.data.storeID = !doProxyFromPgDump ? basename.split("-").reverse()[1] : basename.split("-").reverse()[2];
                    let storeName = job.attrs.data.storeID;
                   
                    let mageGroupCode = basename.split("-")[0];
                    let mageStoreCode =  basename.split("-")[1];
                    let storeCode = dealerId + '-' + mageStoreCode;

                    console.log('DealerId:', dealerId);
                    segment.saveSegment(`DealerId : ${dealerId}`);
                    console.log('Groupname:', mageGroupCode);
                    segment.saveSegment(`Groupname : ${mageGroupCode}`);
                    console.log('storeName:',mageStoreCode);
                    segment.saveSegment(`storeName : ${mageStoreCode}`);

                    // let jobsTmp = await Agenda.jobs( {
                    //     $and: [
                    //         { "data.storeDataArray.dealerId": dealerId },
                    //         { "name": constants.CDK_EXTRACT.JOB_NAME } ,
                    //         {"data.storeDataArray.mageStoreCode":mageStoreCode}
                    //     ]
                    // });

                    let jobsTmp = await Agenda.jobs({
                        $and: [
                            { "name": constants.CDK_EXTRACT.JOB_NAME },
                            { "data.storeDataArray": { 
                                $elemMatch: { 
                                    "dealerId": dealerId, 
                                    "mageStoreCode": mageStoreCode 
                                } 
                            }}
                        ]
                    });
                    
                    let projectId = '';
                    let secondProjectId = '';
                    let userName = '';
                    let solve360Update = '';
                    let updateSolve360Data; 
                    let buildProxies;
                    let includeMetaData;
                    let dualProxy;
                    let extractionId;
                    let showAccountingInProxy;
                    
                    let haltIdentifier = false;
                    let haltOverRide = false;

                    let resumeUser;
                    let mageManufacturer;
                    let isPorscheStore;
                   

                    let agendaObject;
                    let extractedFileTimeStamp;
                    let extractedFileCreationDate;
                    let extractedObjectIndex;
                    let projectIds;
                    let secondProjectIdList;
                    let uniqueId;
                    let testData;
                    let companyIds;
                    let companyObj;
                    let processFileName;
                    let brands;
                    let totalRoCount;
                    let exceptionTypeCounts = {};

                    try{
                        extractedFileTimeStamp = basename.split("-").reverse()[0].replace(".zip", "");
                        segment.saveSegment(`extractedFileTimeStamp : ${extractedFileTimeStamp}`);
                        extractedFileCreationDate =  moment(extractedFileTimeStamp, "YYYYMMDDhhmmss").format("YYYY-MM-DD");
                        segment.saveSegment(`extractedFileCreationDate : ${extractedFileCreationDate}`);
                    } catch(err){
                      console.log(err);
                      segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                    }
                    
                    segment.saveSegment(`jobsTmp : ${JSON.stringify(jobsTmp)}`);
                    segment.saveSegment(`jobsTmp[jobsTmp.length-1] : ${JSON.stringify(jobsTmp[jobsTmp.length-1])}`);
                    segment.saveSegment(`Dealer ID : ${dealerId}`);

                    if(jobsTmp[jobsTmp.length-1]){
                        console.log("jobsTmp::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::;",jobsTmp[jobsTmp.length-1]);
                        if(jobsTmp[jobsTmp.length-1].hasOwnProperty("attrs")){
                            extractionId = jobsTmp[jobsTmp.length-1].attrs._id;
                            try{
                                segment.saveSegment(`jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : ${JSON.stringify(jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray)}`);
                                agendaObject = jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray;
                                agendaObject = agendaObject.filter(function (el) {
                                    return el.dealerId == dealerId && el.mageStoreCode == mageStoreCode;
                                });
                                segment.saveSegment(`agendaObject : ${JSON.stringify(agendaObject)}`);

                                extractedObjectIndex = 0;
                                if(agendaObject.length > 0){ 
                                    agendaObject =  agendaObject.sort((a,b) => b.endTime > a.endTime);
                                    extractedObjectIndex = agendaObject.findIndex(
                                        obj => moment(obj.endTime, "YYYY-MM-DDTHH:mm:ss.SSS[Z]").format("YYYY-MM-DD") == extractedFileCreationDate
                                    );
                                }

                                if(extractedObjectIndex < 0){
                                    extractedObjectIndex = 0;
                                }
                                
                                segment.saveSegment(`Sorted agenda object : ${JSON.stringify(agendaObject)}`);
                                segment.saveSegment(`extractedObjectIndex : ${extractedObjectIndex}`);
                                segment.saveSegment(`Extracted agenda object : ${JSON.stringify(agendaObject[extractedObjectIndex])}`);


                                if(agendaObject[extractedObjectIndex].hasOwnProperty("projectId")){
                                    projectId = agendaObject[extractedObjectIndex].projectId;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectId")){
                                    secondProjectId = agendaObject[extractedObjectIndex].secondProjectId;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("solve360Update")){
                                    solve360Update = agendaObject[extractedObjectIndex].solve360Update;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("buildProxies")){
                                    buildProxies = agendaObject[extractedObjectIndex].buildProxies;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("includeMetaData")){
                                    includeMetaData = agendaObject[extractedObjectIndex].includeMetaData;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("dualProxy")){
                                    dualProxy = agendaObject[extractedObjectIndex].dualProxy;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("userName")){
                                    userName = agendaObject[extractedObjectIndex].userName;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("extractAccountingData")){
                                    showAccountingInProxy = agendaObject[extractedObjectIndex].extractAccountingData;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("mageManufacturer")){
                                    mageManufacturer = agendaObject[extractedObjectIndex].mageManufacturer;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("projectIds")){
                                    projectIds = agendaObject[extractedObjectIndex].projectIds;
                                    projectIds =  projectIds.split("*");
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectIdList")){
                                    secondProjectIdList = agendaObject[extractedObjectIndex].secondProjectIdList;
                                    secondProjectIdList = secondProjectIdList.split("*");
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("uniqueId")){
                                    uniqueId = agendaObject[extractedObjectIndex].uniqueId;
                                    
                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("testData")){
                                    testData = agendaObject[extractedObjectIndex].testData;
                                    
                                }
                                 if(agendaObject[extractedObjectIndex].hasOwnProperty("processFileName")){
                                    processFileName =agendaObject[extractedObjectIndex].processFileName;
                                }
                                console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>TEst>>>>>>");
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("companyIds")){
                                    console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                                    companyIds = agendaObject[extractedObjectIndex].companyIds;
                                    companyIds =  companyIds.replace(new RegExp('\\*', 'g'), ',')
                                    console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",companyIds);
                                    // companyIds = companyIds.replace(/,\s*$/, "");
                                }
                               if(agendaObject[extractedObjectIndex].hasOwnProperty("brands")){
                                    console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                                    brands = agendaObject[extractedObjectIndex].brands.split("*");

                                    console.log("brands exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",brands);
                                    // companyIds = companyIds.replace(/,\s*$/, "");
                                }

                                if(brands.length>1){
                                     const hasPorche = brands.some(item => item.toLowerCase() === 'porche');
                                    if (hasPorche && brands.length > 1) {
                                         mageManufacturer = brands.find(item => item.toLowerCase() !== 'porche');
                                         isPorscheStore = true;
                                       }
                                }else{
                                    if(mageManufacturer == constants.PORSCHE_STORE_LABEL){
                                         isPorscheStore = true;
                                   } else{
                                    isPorscheStore = false; 
                                 }
                                }

                            
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("haltOverRide")){
                                    haltOverRide = agendaObject[extractedObjectIndex].haltOverRide;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("companyObj")){
                                    companyObj =JSON.parse(agendaObject[extractedObjectIndex].companyObj);
                                    console.log("companyObj?????????????????????????????????????????????????????????",companyObj);
                                }

                            } catch(err){
                                console.log(err);
                                segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                            }
                        }
                    }

                    uniqueId+='-'+Date.now();

                    console.log('projectId:', projectId);
                    segment.saveSegment(`projectId : ${projectId}`);
                    
                    console.log('secondProjectId:', secondProjectId);
                    segment.saveSegment(`secondProjectId : ${secondProjectId}`);

                    console.log('userName:', userName);
                    segment.saveSegment(`userName : ${userName}`);

                    console.log('solve360Update:', solve360Update);
                    segment.saveSegment(`solve360Update : ${solve360Update}`);

                    console.log('buildProxies:', buildProxies);
                    segment.saveSegment(`buildProxies : ${buildProxies}`);

                    console.log('includeMetaData:', includeMetaData);
                    segment.saveSegment(`includeMetaData : ${includeMetaData}`);


                    console.log('dualProxy:', dualProxy);
                    segment.saveSegment(`dualProxy : ${dualProxy}`);

                    console.log('showAccountingInProxy:', showAccountingInProxy);
                    segment.saveSegment(`showAccountingInProxy : ${showAccountingInProxy}`);

                    console.log('extractionId:', extractionId);
                    segment.saveSegment(`extractionId : ${extractionId}`);

                    console.log('haltOverRide:', haltOverRide);
                    segment.saveSegment(`haltOverRide : ${haltOverRide}`);

                    console.log('projectIds:', projectIds);
                    segment.saveSegment(`projectIds : ${projectIds}`);

                    console.log('secondProjectIdList:', secondProjectIdList);
                    segment.saveSegment(`secondProjectIdList : ${secondProjectIdList}`);

                    console.log('uniqueId:', uniqueId);
                    segment.saveSegment(`uniqueId : ${uniqueId}`);

                    console.log('testData:', testData);
                    segment.saveSegment(`testData : ${testData}`);
                    
                    console.log('companyIds:', companyIds);
                    segment.saveSegment(`companyIds : ${companyIds}`);

                    console.log('companyObj:', companyObj);
                    segment.saveSegment(`companyObj : ${companyObj}`);

 
                    if(haltOverRide && !doProxyFromPgDump){
                        resumeUser = `${userName}`;
                        console.log('resumeUser:', resumeUser);
                        segment.saveSegment(`resumeUser : ${resumeUser}`);
                    }

                    updateSolve360Data = {projectId:projectId, secondProjectId:secondProjectId, userName:userName, solve360Update:solve360Update, thirdPartyUsername:dealerId, storeCode: mageStoreCode, dmsType: constants.JOB_TYPE, groupCode:mageGroupCode, resumeUser: resumeUser ? resumeUser : '',projectIds:projectIds,secondProjectIdList:secondProjectIdList,uniqueId:uniqueId,testData:testData,companyObj:companyObj,processFileName:processFileName};

                    segment.saveSegment(`updateSolve360Data : ${JSON.stringify(updateSolve360Data)}`);
                    console.log("update Solve 360 data",updateSolve360Data);
                    

                    let buildProxiesDecider;
                    if(buildProxies || doProxyFromPgDump){
                        buildProxiesDecider = constants.PROCESS_XML.OPT_BUILD_PROXY_RO; 
                    } else{
                        buildProxiesDecider = constants.PROCESS_XML.OPT_NO_BUILD_PROXY_RO;
                    }

                    let dualProxyDecider;
                    if(dualProxy){
                        dualProxyDecider = constants.PROCESS_XML.OPT_DUAL_PROXY; 
                    } 

                    let accountingProxyDecider;
                    if(showAccountingInProxy){
                        accountingProxyDecider = constants.PROCESS_XML.SHOW_ACCOUNTING_IN_PROXY; 
                    } else{
                        accountingProxyDecider = constants.PROCESS_XML.HIDE_ACCOUNTING_IN_PROXY;
                    }
                    segment.saveSegment(`accountingProxyDecider : ${accountingProxyDecider}`);


                    // if(mageManufacturer == constants.PROCESS_XML.PORSCHE_STORE_LABEL){
                    //     isPorscheStore = true;
                    // } else{
                    //     isPorscheStore = false; 
                    // }

                        if (brands.length > 0) {
                         
                            const lowerCaseBrands = brands.map(item => item.toLowerCase());
                            const allPorche = lowerCaseBrands.every(item => item === 'porche');
                            const hasPorche = lowerCaseBrands.includes('porche');

                                if (allPorche) {
                                
                                    mageManufacturer = 'porche';
                                    isPorscheStore = true;
                                 } else if (hasPorche && brands.length > 1) {
                                   mageManufacturer = brands.find(item => item.toLowerCase() !== 'porche');
                                   isPorscheStore = true;
                                 } else {
                                  if (mageManufacturer === constants.PORSCHE_STORE_LABEL) {
                                    isPorscheStore = true;
                                 } else {
                                   isPorscheStore = false;
                                      }
                                 }
                        }



                    console.log('mageManufacturer:', mageManufacturer);
                    segment.saveSegment(`mageManufacturer : ${mageManufacturer}`);

                    console.log('isPorscheStore:', isPorscheStore);
                    segment.saveSegment(`isPorscheStore : ${isPorscheStore}`);
                    // att.processFileName = basename;
                    await job.save();
                    await SetProcessJobStatus.setProcessJobStatusForCdk3pa(dealerId,mageStoreCode,'Running');
                    await SetProcessJobStatus.setProcessFileName(dealerId,mageStoreCode,basename,'CDK_EXTRACT');
                    if (doProxyFromPgDump) {
                        var payTypeFilterFileName = basename.replace(constants.PROCESS_XML.REPLACE_STRING.FROM, constants.PROCESS_XML.REPLACE_STRING.CSV);
                        var searchFile = '';
                        var inpStr = payTypeFilterFileName.split('/').reverse();
                        var inpStrSplit = inpStr[0].split(storeName);
                        searchFile = `${inpStrSplit[0]}${storeName}`;
                        const payTypeFileNameBase = await util.findPayTypeCsvFile(constants.PROCESS_XML.DIST_DIR, searchFile);
                        let payTypeFileName = extractZip.replace(constants.PROCESS_XML.REPLACE_STRING.DIR_PREFIX, '');
                        payTypeFileName = extractZip.replace(constants.PROCESS_XML.REPLACE_STRING.FROM, constants.PROCESS_XML.REPLACE_STRING.CSV);

                        if (fs.existsSync(payTypeFileName)) {
                            console.log("File exist");
                            payTypeFileName = payTypeFileName;
                        } else {
                            console.log("File not exist");
                            payTypeFileName = payTypeFileNameBase;
                        }
                        console.log("payTypeFileNameBase", payTypeFileNameBase);
                        console.log("payTypeFileName", payTypeFileName);
                        //basename = basename.replace(constants.PROCESS_XML.REPLACE_STRING.DO_PROXY_FROM, '');
                        spawnInputArray = [
                            constants.PROCESS_XML.PROCESS_CMD,
                            constants.PROCESS_XML.OPT_BUNDLE_DIR, constants.PROCESS_XML.BUNDLE_DIR,
                            constants.PROCESS_XML.OPT_BUILD_PROXY_USING, path.join(constants.CDK_SCHEDULER_ETI_DIR, extractZip),
                            constants.PROCESS_XML.OPT_PAY_TYPE_FILTER, path.join(process.env.DIST_DIR, payTypeFileName),
                            constants.PROCESS_XML.OPT_ZAP_INPUT,
                            constants.PROCESS_XML.OPT_DEADLETTER_DIR_PREFIX, constants.DEADLETTER_DIR_PREFIX + '-processed',
                            constants.PROCESS_XML.OUTPUT_PREFIX, constants.PROCESS_XML.OUTPUT_PREFIX_VAL,
                            accountingProxyDecider,
                            "--project-id",projectId,
                            "--secondary-project-id",secondProjectId,
                            constants.PROCESS_XML.BRAND_NAME,mageManufacturer,
                            "--uuid",uniqueId,
                            "--performed-by",userName,
                            "--exception-report",true,
                            "--pre-import-halt",false,
                            "--solve-db",true,
                            "--scheduler-db",true,
                            "--company_ids",companyIds
                        ];
                    } else {
                        if(haltOverRide){
                            resumeUser = `Resume by ${userName}`;
                            // Portal update for process xml Resume
                            let todayDate;
                            let attPayload = {};
                            let projectID;
                            let secondProjectID;
                            let inpObjProject;
                            let inpObjSecondProject;
                            let projectIdList;
                            let secondProjectIdList;
                            try{
                            todayDate = new Date().toISOString().slice(0, 10);
                            attPayload = agendaObject[extractedObjectIndex];
                            projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                            projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                            secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                            attPayload['inProjectId'] =  projectID;

                            secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                            attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                            attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                            attPayload.in_retrive_ro_request_on = todayDate;
                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                            console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                            if(secondProjectIdList.length>0){
                                inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                                console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                            }
                            } catch(err){
                            console.log(JSON.stringify(err));
                            segment.saveSegment(`CDK : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                            }
                            segment.saveSegment(`CDK : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                            segment.saveSegment(`CDK : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                            try {
                                // segment.saveSegment(`CDK : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                                
                                // let parsedData = JSON.parse(inpObjProject.inData);
                                // let projectIdList =  parsedData.projectIds.split("*");
                                if(projectIdList  && testData==false ){
                                     for(const id of projectIdList){
                                        if(id!=undefined && id!=''){
                                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                                            portalUpdate.doPayloadAction(inpObjProject);
                                            console.log(`CDK3PA Schedule portal call with Project Id RESUME${id}`);
                                            segment.saveSegment(`CDK3PA Schedule portal call with Project Id RESUME${id}`);
                    
                                        }
                                     }
                                } 
                                if(secondProjectIdList.length>0  && testData==false ){
                                  
                                    // let parsedData = JSON.parse(inpObjSecondProject.inData);
                                    // let secondProjectIdList =  parsedData.secondProjectIdList.split("*");

                                    //  segment.saveSegment(`CDK : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                   
                                   if(secondProjectIdList){
                                       for(const id of secondProjectIdList){
                                          if(id!=undefined && id!=''){
                                            inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                                             portalUpdate.doPayloadAction(inpObjSecondProject);
                                             console.log(`CDK3PA Schedule portal call with Second Project Id Resume${id}`);
                                             segment.saveSegment(`CDK3PA Schedule portal call with Second Project Id RESUME${id}`);
                   
                                          }
                                      }
                                    } 
                                }
                            } catch(error) {
                                console.log("Error:", error);
                                segment.saveSegment(`CDK : doPayloadAction Error - ${JSON.stringify(error)}`); 
                            }
                            //code end for portal update for process xml Resume
                        }

                        var spawnInputArray = [
                            constants.PROCESS_XML.PROCESS_CMD,
                            constants.PROCESS_XML.OPT_BUNDLE_DIR, constants.PROCESS_XML.BUNDLE_DIR,
                            constants.PROCESS_XML.OPT_INPUT_ZIP, path.join(constants.CDK_SCHEDULER_ETI_DIR, extractZip),
                            constants.PROCESS_XML.OPT_ZAP_INPUT,
                            constants.PROCESS_XML.OPT_PERFORM_ZIP,
                            buildProxiesDecider,
                            // constants.PROCESS_XML.OPT_BUILD_PROXY_RO,
                            dualProxyDecider,
                            constants.PROCESS_XML.OPT_DEADLETTER_DIR_PREFIX,
                            constants.DEADLETTER_DIR_PREFIX + '-processed',
                            constants.PROCESS_XML.OUTPUT_PREFIX,
                            constants.PROCESS_XML.OUTPUT_PREFIX_VAL,
                            accountingProxyDecider,
                            constants.PROCESS_XML.HALT_OVER_RIDE,
                            haltOverRide,
                            "--project-id",projectId,
                            "--secondary-project-id",secondProjectId,
                            constants.PROCESS_XML.BRAND_NAME,mageManufacturer,
                            "--uuid",uniqueId,
                            "--performed-by",userName,
                            "--exception-report",true,
                            "--pre-import-halt",false,
                            "--solve-db",true,
                            "--scheduler-db",true,    
                            "--company_ids",companyIds
                        ];
                    }

                    if (isPorscheStore){
                        spawnInputArray.push(constants.PROCESS_XML.OPT_PORSCHE_STORE)
                    }

                    console.log("constants.PROCESS_XML.PROCESS_CMD_PATH>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",constants.PROCESS_XML.PROCESS_CMD_PATH);
                    console.log("process.env.PATH>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",process.env.PATH);

                    const processXml = spawn("bash",
                        spawnInputArray, {
                        cwd: constants.PROCESS_XML.PROCESS_CMD_PATH,
                        env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
                    }).on('error', function (err) {
                        console.log("error ::", err);
                        segment.saveSegment(`error: ${err}`);
                    });
                    console.log(`Start processing of extraction > ${basename}`);
                    segment.saveSegment(`Start processing of extraction > ${basename}`);
                    segment.saveSegmentFailure(`Start processing of extraction > ${basename}`, storeCode);
                    process.stdin.pipe(processXml.stdin);
                    await SetProcessJobStatus.setProcessJobStatusForCdk3pa(dealerId,mageStoreCode,'Running');
                    processXml.stdout.on("data", async (data) => {
                        console.log(`stdout: ${data}`);
                        stdOutArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                        await job.touch();
                        segment.saveSegment(`stdout123: ${data}`);
                        segment.saveSegmentFailure(`stdout: ${data}`, storeCode);         
                        data = data.toString('utf8');
                           segment.saveSegment(`stdout11: ${data}`);


                           if(data.includes('Total Ros Count:-')){
                            segment.saveSegment(`Total Ros Count55555555555,${data}`);
                            console.log('file generated',data.split(':')[1]);
                            segment.saveSegment(`Total Ro90999999999,${data.split(':')[1]}`);
                            totalRoCount = data.split(':-')[1];
                            segment.saveSegment(`totalRoCount666666,${totalRoCount}`);
                            
                          }else{
                          console.log("failed to generate1212 file")
                         } 




                       if(data.includes('Processor status')){
                            segment.saveSegment('Processor statu for UI',data);
                            console.log('file generated',data.split(':')[1]);
                            processorStatus = data.split(':')[1];
                            segment.saveSegment('processorStatus',processorStatus);
                            await SetProcessJobStatus.setProcessJobStatusForRunningJob(basename,processorStatus);
                          }else{
                          console.log("failed to generate file")
                         } 
console.log("data$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",data)
  segment.saveSegment('data@@@@@@@@@@@@@@@@@@data@@@@@@@@@@@@@@@@@@@@@@@@@@@@@',data);
const match = data.match(/Total Ros Count:-\s*(\d+)/);
console.log("match@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",match);




if (match) {
     segment.saveSegment('Pmatch@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@',match);
  const totalRoCount = match[1];
  console.log("totalRoCount>>>>>>>>>>>>>>>>>>>>>>>>>>>",totalRoCount)
  segment.saveSegment('Total Ros Count:-', data);
  console.log('Total Ros Count:', totalRoCount);
  segment.saveSegment('Total Ros Count', totalRoCount);
} else {
  console.log("Failed to get total ros count");
}

                           
                           


                    });
                    processXml.stderr.on("data", async (data) => {
                        console.log(`stderr: ${data}`);
                        stdErrorArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                        await job.touch();
                        segment.saveSegment(`stderr: ${data}`);
                        segment.saveSegmentFailure(`stderr: ${data}`, storeCode);
                    });

                    processXml.on("close", async (code) => {
                        var message = "n/a";
                        var status = false;

const filePath = '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tag/All_exception_details.csv';

if (fs.existsSync(filePath)) {
  

  fs.createReadStream(filePath)
    .pipe(csv1())
    .on('data', (row) => {
      const type = row['Type'];
      if (type) {
        exceptionTypeCounts[type] = (exceptionTypeCounts[type] || 0) + 1;
      }
    })
    .on('end', () => {
      console.log("exceptionTypeCounts", exceptionTypeCounts);
      segment.saveSegment(`exceptionTypeCounts: ${JSON.stringify(exceptionTypeCounts)}`);
    });
} else {
  console.error(`File not found: ${filePath}`);
  segment.saveSegment(`Error: File not found at path ${filePath}`);
}

                        

                        if (code == constants.STATUS_CODE.SUCCESS) {
                            status = true;
                            message = "Success";
                            await SetProcessJobStatus.setProcessJobStatusForCdk3pa(dealerId,mageStoreCode,message);
                        } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                            message = "Extraction failed, general death";
                            await SetProcessJobStatus.setProcessJobStatusForCdk3pa(dealerId,mageStoreCode,'Failed');
                            await job.fail(new Error(`Error: ${message}`));
                        } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                            message = "Extraction failed, moved to dead-letter path";
                            await SetProcessJobStatus.setProcessJobStatusForCdk3pa(dealerId,mageStoreCode,'Failed');
                            await job.fail(new Error(`Error: ${message}`));
                        }
                        if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_XML.ERROR_CHECKING_LABELS.ZIP_FILE_PROCESSING_FAILED)) {
                            await SetProcessJobStatus.setProcessJobStatusForCdk3pa(dealerId,mageStoreCode,'Failed');
                            message = "Extraction failed, Zip File Processing Failed,  ";
                            status = false;
                        }
                        var deadLetterPath = `${process.env.CDK3PA_WORK_DIR}/dead-letter-processed`;
                        var errResp = `Moving input to dead-letter bin: ${deadLetterPath}`;
                        if (stdOutArray && stdOutArray.includes(errResp)) {
                            message += errResp
                            status = false;
                        }

/////////////////////////////Code for Halt and Resume Processor/////////////////////////////////////////////////////////////////////////////

                        console.log(stdErrorArray);
                        if (stdErrorArray && !haltOverRide) {

                            console.log(constants.PROCESS_XML.ERROR_CHECKING_LABELS.PROCESSOR_HALT);
                            console.log(constants.PROCESS_XML.ERROR_CHECKING_LABELS.PROCESSOR_DEAD);

                            stdErrorArray.forEach(async(v, i) => {
                                if (
                                    v.includes(constants.PROCESS_XML.ERROR_CHECKING_LABELS.PROCESSOR_HALT)
                                ) {
                                    message = "Halt";
                                    haltIdentifier = true;
                                    status = false;
                                    await SetProcessJobStatus.setProcessJobStatusForCdk3pa(dealerId,mageStoreCode,message);
                                }

                                if (
                                    v.includes(constants.PROCESS_XML.ERROR_CHECKING_LABELS.PROCESSOR_DEAD)
                                ) {
                                    message = "Dead";
                                    haltIdentifier = true;
                                    status = false;
                                    await SetProcessJobStatus.setProcessJobStatusForCdk3pa(dealerId,mageStoreCode,'Failed');
                                }


                            });

                            try {
                                if (
                                    stdOutArray &&
                                    stdOutArray.includes(errResp) &&
                                    message == "Halt" &&
                                    !haltOverRide
                                ) {
                                    let deadLetterFilePath = deadLetterPath + "/" + basename;
                                    let haltFilePath =
                                        "/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/halt/" +
                                        basename;
                                    if (fs.existsSync(deadLetterFilePath)) {
                                        fs.copyFile(deadLetterFilePath, haltFilePath, (err) => {
                                            if (err) throw err;
                                            console.log(`${deadLetterFilePath} was copied to ${haltFilePath}`);
                                        });
                                    } else {
                                        console.log(`${deadLetterFilePath} not exist!`);
                                    }
                                } else{
                                    console.log('Not a Halt process')
                                }
                            } catch (err) {
                                console.log(err);
                            }
                        }

/////////////////////////////Code for Halt and Resume Processor/////////////////////////////////////////////////////////////////////////////

                        console.log(`XML processing job: ${message}`);
                        segment.saveSegment(`XML processing job: ${message}`);
                        segment.saveSegmentFailure(`XML processing job: ${message}`, storeCode);
                        console.log(`XML processing job for Store ${storeName} exited with code ${code}`);
                        segment.saveSegment(`XML processing job for Store ${storeName} exited with code ${code}`);
                        segment.saveSegmentFailure(`XML processing job for Store ${storeName} exited with code ${code}`, storeCode);
                        // job.attrs.data.outputFile = path.join(constants.PROCESS_XML.BUNDLE_DIR, basename);
                        
                        let failureDirectory = process.cwd() + '/logs/CDK3PA/failure/';
                        let failurelogFile = failureDirectory + storeCode + '.log'; 
                        console.log("failureDirectory?????????????????????????????????/",failureDirectory);
                        console.log("storecode?????????????????????????????????/",storeCode);
                        console.log("failurelogFile?????????????????????????????????????????",failurelogFile)
                        var extractionErrorResponse;
                        var errorWarnningMessage;
                        var warningObj = {};

                        warningObj.scheduled_by = userName;
                            
                    if (testData) {
                        warningObj.testData = true;
                        // warningObj.userName = userName;
                    }
                        
                        //Code for fetch extraction error from mongodb
                        try{
                            if(extractionId){
                                let uniqueExtractionId = extractionId+dealerId;
    
                                segment.saveSegment(`uniqueExtractionId: ${uniqueExtractionId}`);
                                segment.saveSegmentFailure(`uniqueExtractionId: ${uniqueExtractionId}`, storeCode);

                                extractionErrorResponse = await extractionError.displayGlMissingErrors(uniqueExtractionId, 'CDK3PA');
                                
                                if(extractionErrorResponse.status){
                                    let resp = JSON.parse(JSON.stringify(extractionErrorResponse.response))
                                    let tmpDescritionArray = [];
                                    resp.forEach(e => {
                                        tmpDescritionArray.push(e.description);
                                        // tmpDescritionArray.push(e.ro_pull_type+" : "+e.description);
                                    });
                                    // errorWarnningMessage = tmpDescritionArray.join(", ");
                                    errorWarnningMessage = tmpDescritionArray;
                                }
                            
                            }
                            segment.saveSegment(`errorWarnningMessage: ${errorWarnningMessage}`);
                            segment.saveSegmentFailure(`errorWarnningMessage: ${errorWarnningMessage}`, storeCode); 
                            console.log('errorWarnningMessage:',errorWarnningMessage);
                        } catch(error){
                            segment.saveSegment(`Extraction error fetch error: ${JSON.stringify(error)}`);
                            segment.saveSegmentFailure(`Extraction error fetch error: ${JSON.stringify(error)}`, storeCode); 
                        }


                        if(errorWarnningMessage){
                            if(errorWarnningMessage.length > 0){
                                warningObj.errorwarningMessage = errorWarnningMessage;
                            }
                        }

                       
                        let coreReturnExceptionCsvFilePath = constants.CORE_RETURN_EXCEPTION_CSV_FILE_PATH;
                        let coreChargeExceptionCsvFilePath = constants.CORE_CHARGE_EXCEPTION_CSV_FILE_PATH;
                        let coreReturnNotEqualCoreChargeExceptionFilePath = constants.CORE_RETURN_NOT_EQUAL_TO_CORE_CHARGE_EXCEPTION_CSV_FILE_PATH;
                        let coreChargeWithNoSaleCsvFilePath = constants.CORE_CHARGE_WITH_NO_SALE;

                        let invalidCoreCostSaleMismatchFilePath = constants.INVALID_CORE_COST_SALE_MISMATCH;
                        let invalidCoreAmountMismatchFilePath = constants.INVALID_CORE_AMOUNT_MISMATCH;
                        let glRoMissingFilePath ='/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception/gl_missing_ro_list.csv'


                        let jsonCoreReturnArray, jsonCoreReturnMessage, coreReturnExceptionCount = 0;
                        let jsonCoreChargeArray, jsonCoreChargeMessage, coreChargeExceptionCount = 0;
                        let jsonCoreReturnNotEqualCoreChargeArray, jsonCoreReturnNotEqualCoreChargeMessage, coreReturnNotEqualCoreChargeExceptionCount = 0;
                        let coreChargeWithNoSaleArray, coreChargeWithNoSaleCount = 0;
                        let gl_missing_ro_count = 0;

                        let invalidCoreCostSaleMismatchArray, invalidCoreCostSaleMismatchCount = 0; 
                        let invalidCoreAmountMismatchArray, invalidCoreAmountMismatchCount = 0;

                        let partDetailsNullExceptionFilepath = '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception/part_details_null.csv';

                        let partDetailsNullExceptionArray;
                        let partDetailsNullExceptionCount;

                        try{
                            if (fs.existsSync(coreReturnExceptionCsvFilePath)) {

                                console.log(`The core Return Exception Csv File exists: ${coreReturnExceptionCsvFilePath}`);
                                segment.saveSegment(`The core Return Exception Csv File exists:${coreReturnExceptionCsvFilePath}`);
                                segment.saveSegmentFailure(`The core Return Exception Csv File exists:${coreReturnExceptionCsvFilePath}`, storeCode);

                                jsonCoreReturnArray = await csv().fromFile(coreReturnExceptionCsvFilePath);
                          
                                if(jsonCoreReturnArray){
                                  if(jsonCoreReturnArray.length){

                                    console.log(`jsonCoreReturnArray.length:${jsonCoreReturnArray.length}`);
                                    segment.saveSegment(`jsonCoreReturnArray.length:${jsonCoreReturnArray.length}`);
                                    segment.saveSegmentFailure(`jsonCoreReturnArray.length:${jsonCoreReturnArray.length}`, storeCode);

                                    jsonCoreReturnMessage = jsonCoreReturnArray[jsonCoreReturnArray.length-1]['RO Number'];
                                    if(jsonCoreReturnMessage){
                                        coreReturnExceptionCount = parseInt(jsonCoreReturnMessage.split(":").reverse()[0].trim());
                                    }
                                    console.log(`jsonCoreReturnMessage: ${jsonCoreReturnMessage}`);
                                    segment.saveSegment(`jsonCoreReturnMessage: ${jsonCoreReturnMessage}`);
                                    segment.saveSegmentFailure(`jsonCoreReturnMessage: ${jsonCoreReturnMessage}`, storeCode);

                                    console.log(`coreReturnExceptionCount: ${coreReturnExceptionCount}`);
                                    segment.saveSegment(`coreReturnExceptionCount: ${coreReturnExceptionCount}`);
                                    segment.saveSegmentFailure(`coreReturnExceptionCount: ${coreReturnExceptionCount}`, storeCode);
               
                                  }
                                }
                            }

                            

                            if (fs.existsSync(glRoMissingFilePath)) {

                                console.log(`The core Return Exception Csv File exists: ${glRoMissingFilePath}`);
                                segment.saveSegment(`The core Return Exception Csv File exists:${glRoMissingFilePath}`);
                                segment.saveSegmentFailure(`The core Return Exception Csv File exists:${glRoMissingFilePath}`, storeCode);

                              let  glMissingArray = await csv().fromFile(glRoMissingFilePath);
                          
                                if(jsonCoreReturnArray){
                                    gl_missing_ro_count = glMissingArray.length;
                                  
                                }
                                
                            }
                    

                            if (fs.existsSync(coreChargeExceptionCsvFilePath)) {
                                
                                console.log(`The core Charge Exception Csv File exists: ${coreChargeExceptionCsvFilePath}`);
                                segment.saveSegment(`The core Charge Exception Csv File exists: ${coreChargeExceptionCsvFilePath}`);
                                segment.saveSegmentFailure(`The core Charge Exception Csv File exists: ${coreChargeExceptionCsvFilePath}`, storeCode);

                                jsonCoreChargeArray = await csv().fromFile(coreChargeExceptionCsvFilePath);
                                if(jsonCoreChargeArray){
                                  if(jsonCoreChargeArray.length){

                                    console.log(`jsonCoreChargeArray.length: ${jsonCoreChargeArray.length}`);
                                    segment.saveSegment(`jsonCoreChargeArray.length: ${jsonCoreChargeArray.length}`);
                                    segment.saveSegmentFailure(`jsonCoreChargeArray.length: ${jsonCoreChargeArray.length}`, storeCode);

                                    jsonCoreChargeMessage = jsonCoreChargeArray[jsonCoreChargeArray.length-1]['RO Number'];
                                  
                                    if(jsonCoreChargeMessage){                                   
                                        coreChargeExceptionCount = parseInt(jsonCoreChargeMessage.split(":").reverse()[0].trim());
                                    }

                                    console.log(`jsonCoreChargeMessage: ${jsonCoreChargeMessage}`);
                                    segment.saveSegment(`jsonCoreChargeMessage: ${jsonCoreChargeMessage}`);
                                    segment.saveSegmentFailure(`jsonCoreChargeMessage: ${jsonCoreChargeMessage}`, storeCode);

                                    console.log(`coreChargeExceptionCount: ${coreChargeExceptionCount}`);
                                    segment.saveSegment(`coreChargeExceptionCount: ${coreChargeExceptionCount}`);
                                    segment.saveSegmentFailure(`coreChargeExceptionCount: ${coreChargeExceptionCount}`, storeCode);
                       
                                  }
                                }
                            }

                            if (fs.existsSync(coreReturnNotEqualCoreChargeExceptionFilePath)) {

                                console.log(`The core Return not equal to core Charge Exception Csv File exists:${coreReturnNotEqualCoreChargeExceptionFilePath}`);
                                segment.saveSegment(`The core Return not equal to core Charge Exception Csv File exists:${coreReturnNotEqualCoreChargeExceptionFilePath}`);
                                segment.saveSegmentFailure(`The core Return not equal to core Charge Exception Csv File exists:${coreReturnNotEqualCoreChargeExceptionFilePath}`, storeCode);

                                jsonCoreReturnNotEqualCoreChargeArray = await csv().fromFile(coreReturnNotEqualCoreChargeExceptionFilePath);
                                
                                if(jsonCoreReturnNotEqualCoreChargeArray){
                                  console.log(jsonCoreReturnNotEqualCoreChargeArray.length);
                                  if(jsonCoreReturnNotEqualCoreChargeArray.length){

                                    console.log(`jsonCoreReturnNotEqualCoreChargeArray.length: ${jsonCoreReturnNotEqualCoreChargeArray.length}`)
                                    segment.saveSegment(`jsonCoreReturnNotEqualCoreChargeArray.length: ${jsonCoreReturnNotEqualCoreChargeArray.length}`);
                                    segment.saveSegmentFailure(`jsonCoreReturnNotEqualCoreChargeArray.length: ${jsonCoreReturnNotEqualCoreChargeArray.length}`, storeCode);
                                    
                                    jsonCoreReturnNotEqualCoreChargeMessage = jsonCoreReturnNotEqualCoreChargeArray[jsonCoreReturnNotEqualCoreChargeArray.length-1]['RO Number'];
                                   
                                    if(jsonCoreReturnNotEqualCoreChargeMessage){
                                        coreReturnNotEqualCoreChargeExceptionCount = parseInt(jsonCoreReturnNotEqualCoreChargeMessage.split(":").reverse()[0].trim());
                                    }

                                    console.log(`jsonCoreReturnNotEqualCoreChargeMessage: ${jsonCoreReturnNotEqualCoreChargeMessage}`);
                                    segment.saveSegment(`jsonCoreReturnNotEqualCoreChargeMessage: ${jsonCoreReturnNotEqualCoreChargeMessage}`);
                                    segment.saveSegmentFailure(`jsonCoreReturnNotEqualCoreChargeMessage: ${jsonCoreReturnNotEqualCoreChargeMessage}`, storeCode);

                                    console.log(`coreReturnNotEqualCoreChargeExceptionCount: ${coreReturnNotEqualCoreChargeExceptionCount}`);
                                    segment.saveSegment(`coreReturnNotEqualCoreChargeExceptionCount: ${coreReturnNotEqualCoreChargeExceptionCount}`);
                                    segment.saveSegmentFailure(`coreReturnNotEqualCoreChargeExceptionCount: ${coreReturnNotEqualCoreChargeExceptionCount}`, storeCode);
                                 
                                  }
                                }
                            }

                            if (fs.existsSync(coreChargeWithNoSaleCsvFilePath)) {
                                
                                console.log(`The core charge with no sale Exception Csv File exists: ${coreChargeWithNoSaleCsvFilePath}`);
                                segment.saveSegment(`The core charge with no sale Exception Csv File exists: ${coreChargeWithNoSaleCsvFilePath}`);
                                segment.saveSegmentFailure(`The core charge with no sale Exception Csv File exists: ${coreChargeWithNoSaleCsvFilePath}`, storeCode);
                                
                                coreChargeWithNoSaleArray = await csv().fromFile(coreChargeWithNoSaleCsvFilePath);

                                if(coreChargeWithNoSaleArray){

                                    console.log(`coreChargeWithNoSaleArray.length: ${coreChargeWithNoSaleArray.length}`);
                                    segment.saveSegment(`coreChargeWithNoSaleArray.length: ${coreChargeWithNoSaleArray.length}`);
                                    segment.saveSegmentFailure(`coreChargeWithNoSaleArray.length: ${coreChargeWithNoSaleArray.length}`, storeCode);
                                    
                                    coreChargeWithNoSaleCount = coreChargeWithNoSaleArray.length;
                                }

                                console.log(`coreChargeWithNoSaleCount: ${coreChargeWithNoSaleCount}`);
                                segment.saveSegment(`coreChargeWithNoSaleCount: ${coreChargeWithNoSaleCount}`);
                                segment.saveSegmentFailure(`coreChargeWithNoSaleCount: ${coreChargeWithNoSaleCount}`, storeCode);
                            }

                            if (fs.existsSync(invalidCoreCostSaleMismatchFilePath)) {
                                
                                console.log(`The invalid Core Cost Sale Mismatch File Csv File exists: ${invalidCoreCostSaleMismatchFilePath}`);
                                segment.saveSegment(`The invalid Core Cost Sale Mismatch File Csv File exists: ${invalidCoreCostSaleMismatchFilePath}`);
                                segment.saveSegmentFailure(`The invalid Core Cost Sale Mismatch File Csv File exists: ${invalidCoreCostSaleMismatchFilePath}`, storeCode);
                                
                                invalidCoreCostSaleMismatchArray = await csv().fromFile(invalidCoreCostSaleMismatchFilePath);

                                if(invalidCoreCostSaleMismatchArray){

                                    console.log(`invalidCoreCostSaleMismatchArray.length: ${invalidCoreCostSaleMismatchArray.length}`);
                                    segment.saveSegment(`invalidCoreCostSaleMismatchArray.length: ${invalidCoreCostSaleMismatchArray.length}`);
                                    segment.saveSegmentFailure(`invalidCoreCostSaleMismatchArray.length: ${invalidCoreCostSaleMismatchArray.length}`, storeCode);
                                    
                                    invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                                }

                                console.log(`invalidCoreCostSaleMismatchCount: ${invalidCoreCostSaleMismatchCount}`);
                                segment.saveSegment(`invalidCoreCostSaleMismatchCount: ${invalidCoreCostSaleMismatchCount}`);
                                segment.saveSegmentFailure(`invalidCoreCostSaleMismatchCount: ${invalidCoreCostSaleMismatchCount}`, storeCode);
                            }


                            if (fs.existsSync(invalidCoreAmountMismatchFilePath)) {
                                
                                console.log(`The invalid Core Amount Mismatch File Csv File exists: ${invalidCoreAmountMismatchFilePath}`);
                                segment.saveSegment(`The invalid Core Amount Mismatch File Csv File exists: ${invalidCoreAmountMismatchFilePath}`);
                                segment.saveSegmentFailure(`The invalid Core Amount Mismatch File Csv File exists: ${invalidCoreAmountMismatchFilePath}`, storeCode);
                                
                                invalidCoreAmountMismatchArray = await csv().fromFile(invalidCoreAmountMismatchFilePath);

                                if(invalidCoreAmountMismatchArray){

                                    console.log(`invalidCoreAmountMismatchArray.length: ${invalidCoreAmountMismatchArray.length}`);
                                    segment.saveSegment(`invalidCoreAmountMismatchArray.length: ${invalidCoreAmountMismatchArray.length}`);
                                    segment.saveSegmentFailure(`invalidCoreAmountMismatchArray.length: ${invalidCoreAmountMismatchArray.length}`, storeCode);
                                    
                                    invalidCoreAmountMismatchCount = invalidCoreAmountMismatchArray.length;
                                }

                                console.log(`invalidCoreAmountMismatchCount: ${invalidCoreAmountMismatchCount}`);
                                segment.saveSegment(`invalidCoreAmountMismatchCount: ${invalidCoreAmountMismatchCount}`);
                                segment.saveSegmentFailure(`invalidCoreAmountMismatchCount: ${invalidCoreAmountMismatchCount}`, storeCode);
                            }

                      
 
                            if (fs.existsSync(partDetailsNullExceptionFilepath)) {
                                 
                             console.log(`The Part Details Null Exception CSV  File exists: ${partDetailsNullExceptionFilepath}`);
                             segment.saveSegment(`The Part Details Null Exception CSV  File exists: ${partDetailsNullExceptionFilepath}`);
                             segment.saveSegmentFailure(`The Part Details Null Exception CSV  File exists: ${partDetailsNullExceptionFilepath}`, storeCode);
                             
                             partDetailsNullExceptionArray = await csv().fromFile(partDetailsNullExceptionFilepath);
 
                             if(partDetailsNullExceptionArray){
 
                                 console.log(`partDetailsNullExceptionArray.length: ${partDetailsNullExceptionArray.length}`);
                                 segment.saveSegment(`partDetailsNullExceptionArray.length: ${partDetailsNullExceptionArray.length}`);
                                 segment.saveSegmentFailure(`partDetailsNullExceptionArray.length: ${partDetailsNullExceptionArray.length}`, storeCode);
                                 
                                 partDetailsNullExceptionCount = partDetailsNullExceptionArray.length;
                             }
 
                             console.log(`partDetailsNullExceptionCount: ${partDetailsNullExceptionCount}`);
                             segment.saveSegment(`partDetailsNullExceptionCount: ${partDetailsNullExceptionCount}`);
                             segment.saveSegmentFailure(`partDetailsNullExceptionCount: ${partDetailsNullExceptionCount}`, storeCode);
                             warningObj.partDetailsNullExceptionCount = partDetailsNullExceptionCount;
                             if(partDetailsNullExceptionCount) warningObj.partDetailsNullExceptionFilepath = partDetailsNullExceptionFilepath;
                            }

                        } catch(err){
                            console.log(err);
                            segment.saveSegment(`CDK3PA : Read operation  of exception.csv have error ${err}`);
                            segment.saveSegmentFailure(`CDK3PA : Read operation  of exception.csv have error ${err}`, storeCode);
                        }

                        var fetchGroupAndStoreName = (job.attrs.data.inputFile).split('-');
                        var groupName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[0] : '';
                        var storeName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[1] : '';

                        // coreReturnExceptionCount = 10;
                        // coreChargeExceptionCount = 20;
                        // coreReturnNotEqualCoreChargeExceptionCount = 40;
                        // coreChargeWithNoSaleCount = 30;

                        console.log('coreReturnExceptionCount:', coreReturnExceptionCount);
                        segment.saveSegment(`coreReturnExceptionCount : ${coreReturnExceptionCount}`);

                        console.log('coreChargeExceptionCount:', coreChargeExceptionCount);
                        segment.saveSegment(`coreChargeExceptionCount : ${coreChargeExceptionCount}`);

                        console.log('coreReturnNotEqualCoreChargeExceptionCount:', coreReturnNotEqualCoreChargeExceptionCount);
                        segment.saveSegment(`coreReturnNotEqualCoreChargeExceptionCount : ${coreReturnNotEqualCoreChargeExceptionCount}`);

                        console.log('coreChargeWithNoSaleCount:', coreChargeWithNoSaleCount);
                        segment.saveSegment(`coreChargeWithNoSaleCount : ${coreChargeWithNoSaleCount}`);

                        warningObj.coreReturnExceptionCount = coreReturnExceptionCount;
                        if(coreReturnExceptionCount) warningObj.coreReturnExceptionCsvFilePath = coreReturnExceptionCsvFilePath;

                        warningObj.coreChargeExceptionCount = coreChargeExceptionCount;
                        if(coreChargeExceptionCount) warningObj.coreChargeExceptionCsvFilePath = coreChargeExceptionCsvFilePath;

                        warningObj.coreReturnNotEqualCoreChargeExceptionCount = coreReturnNotEqualCoreChargeExceptionCount;
                        if(coreReturnNotEqualCoreChargeExceptionCount) warningObj.coreReturnNotEqualCoreChargeExceptionFilePath = coreReturnNotEqualCoreChargeExceptionFilePath;

                        warningObj.coreChargeWithNoSaleCount = coreChargeWithNoSaleCount;
                        if(coreChargeWithNoSaleCount) warningObj.coreChargeWithNoSaleCsvFilePath = coreChargeWithNoSaleCsvFilePath;


                        warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchCount;
                        if(invalidCoreCostSaleMismatchCount) warningObj.invalidCoreCostSaleMismatchFilePath = invalidCoreCostSaleMismatchFilePath;


                        warningObj.invalidCoreAmountMismatchCount = invalidCoreAmountMismatchCount;
                        if(invalidCoreAmountMismatchCount) warningObj.invalidCoreAmountMismatchFilePath = invalidCoreAmountMismatchFilePath;

                        warningObj.gl_missing_ro_count = gl_missing_ro_count
                         if(gl_missing_ro_count) warningObj.glRoMissingFilePath = glRoMissingFilePath;
                        var mailTemplateReplacementValues = {
                            dmsType: constants.JOB_TYPE,
                            processTypes: constants.PROCESS_XML.JOB_NAME,
                            subject: `Process XML for ${groupName} - ${storeName} Completed`,
                            warningObj: warningObj,
                            thirdPartyUsername: dealerId,
                            storeCode: storeName,
                            groupCode: groupName
                        };

                        var mailBody = {
                            fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                            toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                            ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                            attachedfailurelogFile:failurelogFile
                        }

                        job.attrs.data.processorUniqueId = '';
                     
                        if(uniqueId && uniqueId!=undefined){
                          job.attrs.data.processorUniqueId = uniqueId;
                         }
                    
                        
                        if (status) {
                            clearInterval(touch);
                            var dmsType = constants.JOB_TYPE.toLowerCase();
                            var opDataFileEtl = path.join(`/etl/etl-vagrant/etl-${dmsType}/${dmsType}-zip/`, `${constants.PROCESS_XML.OUTPUT_PREFIX_VAL}${basename}`);
                            var opDataFileDist = path.join(constants.PROCESS_XML.DIST_DIR, constants.PROCESS_XML.OUTPUT_PREFIX_VAL + basename)
                            opDataFileEtl = opDataFileEtl.replace(constants.PROCESS_XML.REPLACE_STRING.FROM, constants.PROCESS_XML.REPLACE_STRING.TO);
                            var outputFile = opDataFileDist + ' & ' + opDataFileEtl;
                            job.attrs.data.outputFile = outputFile;
                            job.attrs.data.status = status;
                            job.attrs.data.message = message;
                            job.attrs.data.warningMessage = warningObj;

                            job.attrs.data.coreReturnExceptionCount =  coreReturnExceptionCount;
                            job.attrs.data.coreChargeExceptionCount =  coreChargeExceptionCount;
                            job.attrs.data.coreReturnNotEqualCoreChargeExceptionCount =  coreReturnNotEqualCoreChargeExceptionCount;
                            job.attrs.data.coreChargeWithNoSaleCount =  coreChargeWithNoSaleCount;

                            job.attrs.data.invalidCoreCostSaleMismatchCount =  invalidCoreCostSaleMismatchCount;
                            job.attrs.data.invalidCoreAmountMismatchCount =  invalidCoreAmountMismatchCount;
                            job.attrs.data.partDetailsNullExceptionCount = partDetailsNullExceptionCount;




                            segment.saveSegment(`Job saved to DB ${JSON.stringify(job)}`);
                            segment.saveSegmentFailure(`Job saved to DB ${JSON.stringify(job)}`, storeCode);
                            await job.save();
                            done();
                         
                          
                            // var basenameCheck1 = constants.PROCESS_XML.OUTPUT_PREFIX_VAL + basename;
                            // var zipPath = path.join(constants.PROCESS_XML.BUNDLE_DIR, basenameCheck1);
                            // var outPutTemp = constants.PROCESS_XML.BUNDLE_DIR+'/temp1';
                            // var glRoNotFoundCount='';
                            
                        //     fs.createReadStream(zipPath)
                        //     .pipe(unZipper .Extract({ path: outPutTemp }))
                        //     .on ('close',async (res)=>{
                        //     if(fs.existsSync(`${outPutTemp}/processing-result/GL_ro_not_found.csv`)){
                                
                        //     let  gl_missing_countArray = await csv().fromFile(`${outPutTemp}/processing-result/GL_ro_not_found.csv`);
                        //     glRoNotFoundCount = gl_missing_countArray.length;
                        //         console.log("gl missing count", glRoNotFoundCount);
                        //     if(fs.existsSync(outPutTemp)){
                        //     removeDirForce(outPutTemp);
                            
                        //         function removeDirForce(dirPath) {
                        //         try { var files = fs.readdirSync(dirPath); }
                        //         catch (e) { return; }
                        //         if (files.length > 0)
                        //             for (var i = 0; i < files.length; i++) {
                        //                 let filePath = dirPath + '/' + files[i];
                        //                 if (fs.statSync(filePath).isFile())
                        //                     fs.unlinkSync(filePath);
                        //                 else
                        //                     removeDirForce(filePath);
                        //             }
                        //         fs.rmdirSync(dirPath);
                               
                        //     }
                        //   }
                        //      else{
                        //         console.log("file not exit remove");
                        //      }
                        //     }else{
                        //      console.log('false');
                        //      }

                        //     })
                       


                          
                            // CDK Process XML Completed - send notification mail       
                            // await segment.sleep(5000);          
                            // warningObj.glRoNotFoundCount = glRoNotFoundCount;
                            //  mailTemplateReplacementValues = {
                            //     dmsType: constants.JOB_TYPE,
                            //     processTypes: constants.PROCESS_XML.JOB_NAME,
                            //     subject: `Process XML for ${groupName} - ${storeName} Completed`,
                            //     warningObj: warningObj,
                            //     thirdPartyUsername: dealerId,
                            //     storeCode: storeName,
                            //     groupCode: groupName
                            // };     
                                  
                            var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.PROCESS_XML.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Success';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            // Send notification after process xml job completed
                            
                            mailSender.sendMail(mailBody, constants.PROCESS_XML.JOB_NAME);
                        } else {
                            const directoryPath = '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/'; 
                            const fileName = basename;                                 
                            const filePath = path.join(directoryPath, fileName);
                            segment.saveSegment(`CDK3PA : filePath inpObj Error - ${fileName}`);
                            if (fs.existsSync(filePath)) {
                                fs.unlink(filePath, (err) => {
                                   if(err) {
                                        segment.saveSegment(`CDK3PA : Error deleting file - ${err}`);
                                        console.error('Error deleting file:', err);
                                    } else {
                                      segment.saveSegment(`Autosoft : File deleted successfully - ${filePath}`);
                                      console.log('File deleted successfully:', filePath);
                                    }
                                 });
                            } else {
                               console.log('File does not exist:', filePath);
                          }

                            clearInterval(touch);

                            // Portal update for process xml failed or halt or dead
                            let todayDate;
                            let attPayload = {};
                            let projectID;
                            let secondProjectID;
                            let inpObjProject;
                            let inpObjSecondProject;
                            let projectIdList;
                            let secondProjectIdList;
                            try{
                            todayDate = new Date().toISOString().slice(0, 10);
                            attPayload = agendaObject[extractedObjectIndex];
                            projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                            projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                            secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                            attPayload['inProjectId'] =  projectID;

                            secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                            attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                            attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                            attPayload.in_retrive_ro_request_on = todayDate;
                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                            console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                            if(secondProjectIdList.length>0){
                                inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                            }
                            } catch(err){
                            console.log(JSON.stringify(err));
                            segment.saveSegment(`CDK : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                            }
                            segment.saveSegment(`CDK : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                            segment.saveSegment(`CDK : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                            try {
                                segment.saveSegment(`CDK : doPayloadAction - ${JSON.stringify(inpObjProject)}`);  
                                // let parsedData = JSON.parse(inpObjProject.inData);
                                // let projectIdList =  parsedData.projectIds.split("*"); 
                                if(projectIdList && testData==false ){
                                     for(const id of projectIdList){
                                        if(id!=undefined && id!=''){
                                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                            portalUpdate.doPayloadAction(inpObjProject);
                                            console.log(`CDK3PA Schedule portal call with Project Id Failure${id}`);
                                            segment.saveSegment(`CDK3PA Schedule portal call with Project Id Failure${id}`); 
                    
                                        }
                                     }
                                } 
                                if(secondProjectIdList.length>0){
                                segment.saveSegment(`CDK : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                                // let parsedData = JSON.parse(inpObjSecondProject.inData);
                                // let secondProjectIdList =  parsedData.secondProjectIdList.split("*");
                                if(secondProjectIdList){
                                    for(const id of secondProjectIdList){
                                       if(id!=undefined && id!=''){
                                        //    inpObjSecondProject.inProjectId = id;
                                        inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                           portalUpdate.doPayloadAction(inpObjSecondProject);
                                           console.log(`CDK3PA Schedule portal call with Second Project Id Failure${id}`);
                                           segment.saveSegment(`CDK3PA Schedule portal call with Second Project Id Failure${id}`); 
                   
                                       }
                                    }
                               } 
                                }
                            } catch(error) {
                                console.log("Error:", error);
                                segment.saveSegment(`CDK : doPayloadAction Error - ${JSON.stringify(error)}`); 
                            }
                            //code end for portal update for process xml failed
                            job.attrs.data.warningMessage = warningObj;

                            job.attrs.data.coreReturnExceptionCount =  coreReturnExceptionCount;
                            job.attrs.data.coreChargeExceptionCount =  coreChargeExceptionCount;
                            job.attrs.data.coreReturnNotEqualCoreChargeExceptionCount =  coreReturnNotEqualCoreChargeExceptionCount;
                            job.attrs.data.coreChargeWithNoSaleCount =  coreChargeWithNoSaleCount;

                            job.attrs.data.invalidCoreCostSaleMismatchCount =  invalidCoreCostSaleMismatchCount;
                            job.attrs.data.invalidCoreAmountMismatchCount =  invalidCoreAmountMismatchCount;
                            job.attrs.data.partDetailsNullExceptionCount = partDetailsNullExceptionCount;

                            await job.fail(new Error(`Error: ${message}`));
                            done();
                            
                            // CDK Process XML Failed - send notification mail
                            let displayMessage ;
                            if(haltIdentifier){
                                if(message == 'Halt'){
                                    displayMessage = `Halted ${constants.JOB_TYPE} ${constants.PROCESS_XML.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                    mailTemplateReplacementValues.message = displayMessage;
                                    mailTemplateReplacementValues.status = 'Halted';
                                    mailTemplateReplacementValues.subject = `Process XML  for ${groupName} - ${storeName} Halted`;
                                } else if(message == 'Dead'){
                                    displayMessage = `Held ${constants.JOB_TYPE} ${constants.PROCESS_XML.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                    mailTemplateReplacementValues.message = displayMessage;
                                    mailTemplateReplacementValues.status = 'Held';
                                    mailTemplateReplacementValues.subject = `Process XML  for ${groupName} - ${storeName} Held`;
                                }
                            } else{
                                if(haltOverRide){
                                    mailTemplateReplacementValues.resumeUser = resumeUser ? resumeUser : '';
                                }
                                displayMessage = `Failed ${constants.JOB_TYPE} ${constants.PROCESS_XML.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                mailTemplateReplacementValues.message = displayMessage;
                                mailTemplateReplacementValues.status = 'Failed';
                                mailTemplateReplacementValues.subject = `Process XML for for ${groupName} - ${storeName} Failed`;   
                            }
                           
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            // Send notification for failed process xml job
                            mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                            mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                            segment.saveSegmentFailure(displayMessage, storeCode);
                            await segment.sleep(2000);
                            mailSender.sendMail(mailBody, constants.PROCESS_XML.JOB_NAME);
                        }
                        console.log(`Call for next job selection`);
                        segment.saveSegment(`Call method for SharePoint data upload`);
                        segment.saveSegment(`Call for next job selection`);
                        var basenameCheck = constants.PROCESS_XML.OUTPUT_PREFIX_VAL + basename;
                        var distFile = path.join(constants.PROCESS_XML.BUNDLE_DIR, basenameCheck)
                        if (status && fs.existsSync(distFile)) {
                            console.log('Just start the distribute function');
                            console.log('distFile', distFile);
                            basename = constants.PROCESS_XML.OUTPUT_PREFIX_VAL + basename;
                            console.log('doProxyFromPgDump:', doProxyFromPgDump);
                            let rerunFlag;
                            if(doProxyFromPgDump){
                                rerunFlag='RERUN'; 
                            }
                            console.log('Job saved with ID:', job.attrs._id);
                            if(totalRoCount && totalRoCount!=undefined){
                                updateSolve360Data.totalRoCount = totalRoCount;
                            }else{
                                 updateSolve360Data.totalRoCount = 0;
                            }
                             if(exceptionTypeCounts){
                                updateSolve360Data.exceptionTypeCounts = exceptionTypeCounts;
                            }else{
                                 updateSolve360Data.exceptionTypeCounts = null;
                            }
                            
                            await distributeFile(basename, rerunFlag, updateSolve360Data, warningObj, job.attrs._id);
                        } else {
                            // Process XML Job Fail ....
                            segment.saveSegment(`Call for next job selection`);
                            await doNextProcess();
                        }
                    });
                }
            } else {
                if(job.attrs.data.operation=="recheck") {
                    job.remove(err => {
                    segment.saveSegment(`Inside Job remove function`);
                    segment.saveSegment(`job: ${JSON.stringify(job)}`);
                    if (!err) {
                        segment.saveSegment(`Job removed successfully`);
                        console.log("Initial/recheck schedule for CDK3PA Process JSON job successfully removed");
                    } else{
                        segment.saveSegment(`Job removal process have error : ${JSON.stringify(err)}`);
                    }
                  });
              }
                done();
                await doNextProcess();
            }
        });

    return agenda;
}

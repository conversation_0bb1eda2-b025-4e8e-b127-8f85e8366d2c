"use strict";

const constants = require("../constants");
const util = require("../util");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const CDKJobManager = require("../CDKJobManager");
const { spawn } = require("child_process");
const moment = require("moment-timezone");
const fs = require("fs");
const segment = require("../../SEGMENT/CDK3PA/segmentManager");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const extractionError = require('../../../../src/common/extractionError');
const SetProcessJobStatus = require('../../../model/setProcessJobStatus');
const manageScheduleField = require('../../../../src/common/util');
const { v4: uuidv4 } = require('uuid');
var path = require("path");
/**
 * Function to find the unique stores in an array of stores
 */
Array.prototype.unique = function () {
    var a = this.concat();
    for (var i = 0; i < a.length; ++i) {
        for (var j = i + 1; j < a.length; ++j) {
            if (a[i].dealerId === a[j].dealerId)
                a.splice(j--, 1);
        }
    }
    return a;
};

/**
 * Function to perform CDK-Extract
 */
module.exports = function CDKExtractJOB(agenda) {
    console.log(
        `CDK-Extract job started: JobName: ${constants.CDK_EXTRACT.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.CDK_EXTRACT.CONCURRENCY}`
    );
    segment.saveSegment(`CDK-Extract job started: JobName: ${constants.CDK_EXTRACT.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.CDK_EXTRACT.CONCURRENCY}`);
    agenda.define(constants.CDK_EXTRACT.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.CDK_EXTRACT.CONCURRENCY },
        async (job, done) => {
            let storeCode , extractionId, dealerId, glAccountCompanyID;
            extractionId = job.attrs._id;
            segment.saveSegment(`Extraction Object: ${JSON.stringify(job)}`);
            console.log('Extraction Object:',JSON.stringify(job));
            const att = job.attrs.data;
            const storeDataArray = att.storeDataArray.reverse();
            let i = 0;
            let projectId;
            let solve360Update, buildProxies, includeMetaData,dualProxy, metaData,userName,projectType,secondaryProjectType,inLaborProjectType,inLaborProjectId,inPartsProjectType,inPartsProjectId;
            let errorWarnningMessage = '';
            let warningObj = {};           
            let secondaryProjectId;

            let processFileName;
            let uniqueExtractionId, dealerNotSubscribedErrorResponse, dealerNotSubscribedMessage, tmpDealerNotSubscribeArray;

            async function extract(att, job) {
                dealerId = att.dealerId;
                storeCode = att.dealerId + '-' + att.mageStoreCode;
                projectId = att.projectId;
                projectType = att.projectType || null;              
                secondaryProjectType = att.secondaryProjectType || null;
                secondaryProjectId = att.secondProjectId;
                solve360Update = att.solve360Update;
                buildProxies = att.buildProxies;
                includeMetaData = att.includeMetaData;
                dualProxy = att.dualProxy;
                metaData = att.metaData;
                userName = att.userName;
                glAccountCompanyID = att.glAccountCompanyID;
           
                let mageGroupCode = (att.mageGroupCode) ? att.mageGroupCode : '';
                let mageStoreCode = (att.mageStoreCode) ? att.mageStoreCode : '';
                if (projectType && projectType.toLowerCase().startsWith("labor")) {
                    inLaborProjectType = projectType;
                    inLaborProjectId = projectId;
                  } else if (secondaryProjectType && secondaryProjectType.toLowerCase().startsWith("labor")) {
                    inLaborProjectType = secondaryProjectType;
                    inLaborProjectId = secondaryProjectId;
                  }
                  
                  // Check if projectType or secondaryProjectType starts with "parts"
                  if (projectType && projectType.toLowerCase().startsWith("parts")) {
                    inPartsProjectType = projectType;
                    inPartsProjectId = projectId;
                  } else if (secondaryProjectType && secondaryProjectType.toLowerCase().startsWith("parts")) {
                    inPartsProjectType = secondaryProjectType;
                    inPartsProjectId = secondaryProjectId;
                  }
                uniqueExtractionId = extractionId+att.dealerId;

                console.log('dealerId', dealerId);
                segment.saveSegment(`dealerId: ${dealerId}`);
                
                console.log('projectId', projectId);
                segment.saveSegment(`projectId: ${projectId}`);

                console.log('solve360Update', solve360Update);
                segment.saveSegment(`solve360Update: ${solve360Update}`);

                console.log('buildProxies:', buildProxies);
                segment.saveSegment(`buildProxies: ${buildProxies}`);

                console.log('includeMetaData:', includeMetaData);
                segment.saveSegment(`includeMetaData: ${includeMetaData}`);

                console.log('dualProxy:', dualProxy);
                segment.saveSegment(`dualProxy: ${dualProxy}`);

                console.log('metaData:', metaData);
                segment.saveSegment(`metaData: ${metaData}`);

                console.log('extractionId:', extractionId);
                segment.saveSegment(`extractionId: ${extractionId}`);

                console.log('Mage group code:', mageGroupCode);
                segment.saveSegment(`Mage group code: ${mageGroupCode}`);

                console.log('Mage store code:', mageStoreCode);
                segment.saveSegment(`Mage store code: ${mageStoreCode}`);

                console.log('GL Account Company ID:', glAccountCompanyID);
                segment.saveSegment(`GL Account Company ID: ${glAccountCompanyID}`);

                console.log('uniqueExtractionId:', uniqueExtractionId);
                segment.saveSegment(`uniqueExtractionId: ${uniqueExtractionId}`);

                console.log('userName:', userName);
                segment.saveSegment(`userName: ${userName}`);
                warningObj.scheduled_by = userName;

                segment.saveSegment(`Extraction Job Started: ${JSON.stringify(att)}`);
                segment.saveSegmentFailure(`CDK: Extraction Job Started: ${JSON.stringify(att)}`, storeCode);
                if (att.dealerId && att.startDate && att.endDate) {
                    if (!fs.existsSync(constants.DEADLETTER_DIR_PREFIX + '-extracted')) {
                        fs.mkdirSync(constants.DEADLETTER_DIR_PREFIX + '-extracted');
                    }
                    var options = [
                        constants.CDK_EXTRACT.PULL_OP,
                        constants.CDK_EXTRACT.OPT_DEALER_ID, att.dealerId,
                        constants.CDK_EXTRACT.OPT_START_DATE, att.startDate,
                        constants.CDK_EXTRACT.OPT_END_DATE, att.endDate,
                        constants.CDK_EXTRACT.OPT_DEADLETTER_DIR_PREFIX,
                        constants.DEADLETTER_DIR_PREFIX + '-extracted',
                        constants.CDK_EXTRACT.OPT_MAGE_GROUP_CODE, att.mageGroupCode,
                        constants.CDK_EXTRACT.OPT_MAGE_STORE_CODE, att.mageStoreCode,
                        '--stateCode', att.stateCode,
                        '--extractionID',extractionId,
                        '--includeMetaData',includeMetaData,
                        '--metaData',metaData,
                        '--glAccountCompanyID',glAccountCompanyID
                    ];
                    att.message = "Running";
                    job.save();
                    // The use of --stateCode here is intentional; redirecting via a constant named arguments
                    // to external functions is more annoying than helpful; mainly due to verbosity
                    // repating "constants" just is and having to namespace the variable ends up being
                    // redundant since the command invocation itself provides the necessary namespacing; once

                    options.push(constants.CDK_EXTRACT.OPT_ZIP_PATH, att.zipPath ? att.zipPath : constants.CDK_SCHEDULER_ETI_DIR);
                    options.push(constants.CDK_EXTRACT.OPT_ZAP_AFTER_ZIP);

                    // initial
                    (att.jobType == constants.CDK_EXTRACT.JOB_TYPE_INITIAL) ? options.push(constants.CDK_EXTRACT.OPT_CLOSED) : null;
                    ((att.jobType == constants.CDK_EXTRACT.JOB_TYPE_INITIAL) ?
                        options.push(att.closedROOption ? att.closedROOption : constants.CDK_EXTRACT.OPT_CLOSED_CRITERIA_ALL) : null);

                    // refresh
                    ((att.jobType == constants.CDK_EXTRACT.JOB_TYPE_REFRESH) ? options.push(constants.CDK_EXTRACT.OPT_DELTA_DATE) : null);
                    ((att.jobType == constants.CDK_EXTRACT.JOB_TYPE_REFRESH) ?
                        options.push(moment().tz(constants.CDK_EXTRACT.TIME_ZONE).format("MM/DD/YYYY")) : null);

                    // ondemand
                    (att.jobType == constants.CDK_EXTRACT.JOB_TYPE_ON_DEMAND) ? options.push(constants.CDK_EXTRACT.OPT_CLOSED) : null;
                    (att.jobType == constants.CDK_EXTRACT.JOB_TYPE_ON_DEMAND) ? options.push(constants.CDK_EXTRACT.OPT_CLOSED_CRITERIA_CURRENT) : null; // For now there is not other option

                    options.push(constants.CDK_EXTRACT.OPT_BUNDLE);
                    options.push(att.jobType);
                    segment.saveSegment(`Extraction Job Started: ${JSON.stringify(att)}`);
                    segment.saveSegmentFailure(`Extraction Job Started: ${JSON.stringify(att)}`, storeCode);
                    //Mock Server
                    let isMockServer = constants.CDK_EXTRACT.ENV_MOCKSERVER; 
                    console.log("is mockserver &&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&",isMockServer);
                    if(isMockServer == "true"){
                        console.log("Inside Mock servew*********************************************************");
                        let sourceFolder = constants.CDK_EXTRACT.MOCKSERVER_SOURCE_FOLDER_PATH +'cdk3pa/'; 
                        let destinationFolder = constants.CDK_EXTRACT.MOCKSERVER_DESTINATION_FOLDER_PATH_CDK3PA;
                        
                        const mageGroupCodeDIR =  mageGroupCode.replace(/ +/g, "");
                        const mageStorecodeDIR =  mageStoreCode.replace(/ +/g, "");
                        const stateCodeDIR = att.stateCode;
                        const jobTypeDIR = att.jobType ;
                        const dealerIDDIR = dealerId;
                        const zipFileName = mageGroupCodeDIR +'-'+ mageStorecodeDIR +'-'+ stateCodeDIR +'-'+ jobTypeDIR.toUpperCase(); +'-'+ dealerIDDIR +'-';
                        
                        fs.readdir(sourceFolder, function (err, files) {
                            if (err) {
                                segment.saveSegment(`CDK : Unable to scan directory`,err);
                            } 
                            const matchedResults = [];
                            let searchResult;
                            files.forEach(function (file) {
                                if(file.includes(zipFileName)) {
                                    matchedResults.push(file);
                                }
                            });
                            if(matchedResults.length > 0) {
                                searchResult = (matchedResults.sort().reverse())[0];
                                att.startTime = new Date(moment().utc());
                                fs.copyFile(sourceFolder+searchResult, destinationFolder+'/'+searchResult, (err) => {
                                    if (err) {
                                        segment.saveSegment(`CDK : Unable to copy file`,err);
                                    } else {
                                        att.endTime = new Date(moment().utc());
                                        att.uniqueId =util.generateUniqueId(); 
                                        segment.saveSegment(`uniqueId 1111${att.uniqueId}`);
                                        att.status = true;
                                        att.mockServer = true;
                                        att.uniqueId = util.generateUniqueId();
                                        segment.saveSegment(`uniqueId 2222${att.uniqueId}`);
                                        att.message = "Success";
                                        let oldStoreArray = job.attrs.data.storeDataArray;
                                        let newStoreArray = [att];
                                        oldStoreArray.map(data => {
                                            if (data.dealerId === newStoreArray[0].dealerId) {
                                                data = newStoreArray;
                                            }
                                        });
                                        let _storeArray = oldStoreArray;
                                        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                                        job.attrs.data.storeDataArray = _storeArray;
                                        job.save();
                                        done();
                                        
                                    }
                                });
                            }else{
                            let warningObj={};
                            segment.saveSegment(`CDK :Test job failed`);
                            att.startTime = new Date(moment().utc());
                            att.endTime = new Date(moment().utc());
                            att.status = false;
                            att.mockServer = true;
                            att.uniqueId = util.generateUniqueId();
                            segment.saveSegment(`uniqueId 3333${att.uniqueId}`);
                            att.message = "Failed";
                            job.fail(new Error(`CDK :Test job failed`));
                            job.save()
                            let failureDirectory = process.cwd() + '/logs/CDK3PA/failure/';
                            let failurelogFile = failureDirectory + mageStoreCode + '.log';
                            let mailTemplateReplacementValues = {
                                dmsType: constants.JOB_TYPE,
                                processTypes: constants.PROCESS_XML.JOB_NAME,
                                subject: `Test Extraction Job for ${mageGroupCode} - ${mageStoreCode} Failed`,
                                warningObj: warningObj,
                                thirdPartyUsername: dealerId,
                                storeCode: mageStoreCode,
                                groupCode: mageGroupCode
                            };
                            let mailBody = {
                                fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                                toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                                ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                                attachedfailurelogFile:failurelogFile
                            }
                            var displayMessage = `Test Failed ${constants.JOB_TYPE} ${constants.CDK_EXTRACT.JOB_NAME} job for group ${mageGroupCode} and store ${mageStoreCode}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Failed';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure('Extraction status: Extraction Failed', mageStoreCode);
                            // Send notification after  cdk extraction job completed
                            mailSender.sendMail(mailBody, constants.CDK_EXTRACT.JOB_NAME);
                        }
                    });
                    }else{
                    const child = spawn(constants.CDK_EXTRACT.EXTRACT_CMD, options);
                    let startTime = new Date(moment().utc());
                    att.startTime = startTime;
                    let oldStoreArray = job.attrs.data.storeDataArray;
                    let newStoreArray = [att];
                    oldStoreArray.map(data => {
                        if (data.dealerId === newStoreArray[0].dealerId) {
                            data = newStoreArray;
                        }
                    });
                    let _storeArray = oldStoreArray;
                    // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                    job.attrs.data.storeDataArray = _storeArray;
                    segment.saveSegment(`Extraction Job Data: ${JSON.stringify(_storeArray)}`);
                    segment.saveSegmentFailure(`Extraction Job Data: ${JSON.stringify(_storeArray)}`, storeCode);
                    att.uniqueId = util.generateUniqueId();
                    await job.save();
                    process.stdin.pipe(child.stdin)
                    let compareString = "";
                    let status = true;
                    let message = "n/a";
                    let unsubscribedArray = [];
                    let inSalesComment = appConstants.SALES_TAG_COMMENT;
                    child.stdout.on("data", async (data) => {
                        await job.touch();
                        compareString = data.toString('utf8');
                        if (compareString.search("error:") != -1) {
                            message = data.toString('utf8')
                            //status = false;
                        }
                        console.log(`stdout: ${data}`);
                        data = data.toString('utf8');

                        const endpoints = [
                            { endpoint: 'adp3pa.pip.help-employee-extract.standard.endpoint', label: 'employee' },
                            { endpoint: 'cdk3pa.dsapi.labor.type.extract.endpoint', label: 'labor' },
                            { endpoint: 'cdk3pa.pip.help-customer-extract.endpoint', label: 'customer' }
                        ];
                        endpoints.forEach(({ endpoint, label }) => {
                            if (data.includes(`Dealer ${att.dealerId} is not subscribed to ${endpoint}`)) {
                                unsubscribedArray.push(label);
                            }
                        });
                        if ((data.includes(`Dealer ${att.dealerId} is not subscribed`)) && (unsubscribedArray.length <= 0)) {
                            unsubscribedArray.push('ro bulk')
                        }
                    if (data.includes("Output zip file successfully generated @path")) {
                      console.log("data",data);
                         const pathMatch = data.match(/@path: (\/.*?\.zip)/);
                         console.log("pathMatch",pathMatch);
                         segment.saveSegment(`pathMatch: ${pathMatch}`);
                        if (pathMatch && pathMatch[1]) {
                            const filePath = pathMatch[1];
                            const match = filePath.match(/[^/]+\.zip$/);
                            if (match) {
                                segment.saveSegment(`match: ${match}`);
                                processFileName = match[0];
                                segment.saveSegment(`filePath: ${filePath}`);
                                console.log("Extracted filename:", processFileName);
                                segment.saveSegment(`jobs: ${JSON.stringify(job.attrs)}`);
                                segment.saveSegment(`job: ${job.attrs}`); 
                                if (processFileName) {
                                    await manageScheduleField.processCompanyData(job, userName, processFileName, filePath, constants.JOB_TYPE);
                                    att.processFileName = processFileName.trim();
                                    await job.save();
                                } else {
                                    processFileName = null;
                                }
                            } else {
                                console.log("Failed to extract filename from path:", filePath);
                            }
                        } else {
                            console.log("Failed to find the file path in the log data");
                        }
                         } else {
                         console.log("Failed to generate file");
                       }
                        segment.saveSegment(`stdout: ${data}`);
                        segment.saveSegmentFailure(`stdout: ${data}`, storeCode);
                    });
                    child.stderr.on("data", async (data) => {
                        await job.touch();
                        compareString = data.toString('utf8');
                        if (compareString.search("error:") != -1) {
                            message = data.toString('utf8')
                            //status = false;
                        }
                        console.log(`stderr: ${data}`);
                        segment.saveSegment(`stderr: ${data}`);
                        segment.saveSegmentFailure(`stderr: ${data}`, storeCode);
                    });

                    child.on("close", async (code) => {
                        if (code == constants.STATUS_CODE.SUCCESS) {
                            status = true;
                            message = "Success";
                        } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                            status = false;
                            message = "Extraction failed, general death";
                        } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                            status = false;
                            message = "Extraction failed, moved to dead-letter path";
                        }
                        segment.saveSegment(`Job close: ${message}`);
                        segment.saveSegmentFailure(`Job close: ${message}`, storeCode);

                        let failureDirectory = process.cwd() + '/logs/CDK3PA/failure/';
                        let failurelogFile = failureDirectory + storeCode + '.log';

                        // let groupName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageGroupCode : '';
                        // let storeName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageStoreCode : '';

                        let groupName = mageGroupCode;
                        let storeName = mageStoreCode;

                        dealerNotSubscribedErrorResponse = await extractionError.displayErrorDealerNotSubscribedCDK3PA(uniqueExtractionId, 'CDK3PA');
                        if(dealerNotSubscribedErrorResponse.status){
                          tmpDealerNotSubscribeArray = JSON.parse(JSON.stringify(dealerNotSubscribedErrorResponse.response));
                          if(tmpDealerNotSubscribeArray){
                              if(tmpDealerNotSubscribeArray.length > 0){
                                dealerNotSubscribedMessage = tmpDealerNotSubscribeArray[0]['description'];
                              }
                          }
                        }

                        console.log('************************************************')
                        console.log(dealerNotSubscribedErrorResponse);
                        console.log(dealerNotSubscribedMessage);
                        console.log('************************************************')                               

                        if(dealerNotSubscribedMessage){
                            if(dealerNotSubscribedMessage.length > 0){
                                warningObj.dealerNotSubscribedMessage = dealerNotSubscribedMessage.split("to")[0].trim();
                            }
                        }

                        let mailTemplateReplacementValues = {
                            dmsType: constants.JOB_TYPE,
                            processTypes: constants.PROCESS_XML.JOB_NAME,
                            subject: `Extraction Job for ${groupName} - ${storeName} Completed`,
                            warningObj: warningObj,
                            thirdPartyUsername: dealerId,
                            storeCode: storeName,
                            groupCode: groupName
                        };
                        let mailBody = {
                            fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                            toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                            ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                            attachedfailurelogFile:failurelogFile
                        }
                        
                        if (status) {
                            // Send notification
                            var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.CDK_EXTRACT.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Success';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure('Extraction status: Extraction completed', storeCode);
                            // Send notification after  cdk extraction job completed
                            mailSender.sendMail(mailBody, constants.CDK_EXTRACT.JOB_NAME);
                        } else {
                           

                             //Code for fetch extraction error from mongodb
                        try{
                            if(extractionId){
                                 uniqueExtractionId = extractionId+dealerId;
    
                                segment.saveSegment(`uniqueExtractionId: ${uniqueExtractionId}`);
                                segment.saveSegmentFailure(`uniqueExtractionId: ${uniqueExtractionId}`, storeCode);

                              var extractionErrorResponse = await extractionError.displayErrorLogWithSpecific(uniqueExtractionId, 'CDK3PA');
                                
                                if(extractionErrorResponse.status){
                                    let resp = JSON.parse(JSON.stringify(extractionErrorResponse.response))
                                    let tmpDescritionArray = [];
                                    resp.forEach(e => {
                                        tmpDescritionArray.push(e.description);
                                        // tmpDescritionArray.push(e.ro_pull_type+" : "+e.description);
                                    });
                                    // errorWarnningMessage = tmpDescritionArray.join(", ");
                                    errorWarnningMessage = tmpDescritionArray;
                                }
                            
                            }
                            segment.saveSegment(`errorWarnningMessage: ${errorWarnningMessage}`);
                            segment.saveSegmentFailure(`errorWarnningMessage: ${errorWarnningMessage}`, storeCode); 
                            console.log('errorWarnningMessage:',errorWarnningMessage);
                        } catch(error){
                            segment.saveSegment(`Extraction error fetch error: ${JSON.stringify(error)}`);
                            segment.saveSegmentFailure(`Extraction error fetch error: ${JSON.stringify(error)}`, storeCode); 
                        }


                        if(errorWarnningMessage){
                            if(errorWarnningMessage.length > 0){
                                warningObj.errorwarningMessage = errorWarnningMessage;
                            }
                        }
                           
                            mailTemplateReplacementValues.warningObj = warningObj;
                            mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                            mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                            // Send notification
                            var displayMessage = `Failed ${constants.JOB_TYPE} ${constants.CDK_EXTRACT.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.subject = `Extraction Job for ${groupName} - ${storeName} Failed`;
                            mailTemplateReplacementValues.warnningMessage = errorWarnningMessage;
                            mailTemplateReplacementValues.status = 'Failed';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            if(unsubscribedArray.length > 0){
                            const UserInput = {
                                inLaborProjectId :inLaborProjectId,
                                inLaborProjectType :inLaborProjectType,
                                inPartsProjectType: inPartsProjectType,
                                inPartsProjectId:inPartsProjectId,                               
                                inIsInSales :true,
                                inSalesComment : inSalesComment,                               
                                inUpdatedBy :userName
                              }
                              segment.saveSegment(`CDK Extraction - sales tag creation: ${UserInput}`);
                              let  unsubscribedDms= "Need DMS Access Granted to " + unsubscribedArray.join(', ');
                              segment.saveSegment(`CDK Extraction - sales tag creation : ${unsubscribedDms}`);
                            try {
                                const SendSalesTagDetails = await manageScheduleField.sendNotificationCall(UserInput,constants.CDK_EXTRACT.JOB_NAME);
                                segment.saveSegment(`CDK Extraction - sales tag creation: ${JSON.stringify(SendSalesTagDetails)}`);
                              } catch (salesError) {                                
                                segment.saveSegment(`CDK Extraction - sales tag creation Error: ${salesError}`);
                              }
                            }

                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure('Extraction status: Extraction failed', storeCode);
                            // Send notification for failed cdk extraction
                            await segment.sleep(2000);
                            mailSender.sendMail(mailBody, constants.CDK_EXTRACT.JOB_NAME);

                            // Portal update for extraction failed
                            let todayDate;
                            let attPayload = {};
                            let projectID;
                            let secondProjectID;
                            let inpObjProject;
                            let inpObjSecondProject;
                            let secondProjectIdList;
                            let projectIdList;
                            try{
                               todayDate = new Date().toISOString().slice(0, 10);
                               attPayload = att;
                               projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                               projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                               secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                               segment.saveSegment(`CDK EXTRACT : projectIdList - ${projectIdList}`);
                               segment.saveSegment(`CDK EXTRACT : secondProjectIdList - ${secondProjectIdList}`);
                               attPayload['inProjectId'] =  projectID;
                          
                               secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                               attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";
                          
                               attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                               attPayload.in_retrive_ro_request_on = todayDate;
                               inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectID, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                               console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 

                            //    if(secondProjectID){
                            //     inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectID, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                            //     console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                            //    }

                            if(secondProjectIdList.length>0){
                                for(let i=0;i<secondProjectIdList.length;i++){
                                    if(secondProjectIdList[i]!=undefined && secondProjectIdList[i]!= ''){
                                        inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList[i], attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                        console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                                        portalUpdate.doPayloadAction(inpObjSecondProject);
                                    }
                                   
                                }
                               
                            }

                            if(projectIdList.length>0){
                                for(let i=0;i<projectIdList.length;i++){
                                    if(projectIdList[i]!=undefined && projectIdList[i]!=''){
                                              
                                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIdList[i], attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                        segment.saveSegment(`CDK3PA : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                                        portalUpdate.doPayloadAction(inpObjProject);
                                    }
                             
                                }
                               
                            }



                            } catch(err){
                              console.log(JSON.stringify(err));
                              segment.saveSegment(`CDK : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                            }
                            segment.saveSegment(`CDK : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                            segment.saveSegment(`CDK : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                            // try {
                            //     segment.saveSegment(`CDK : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                            //     portalUpdate.doPayloadAction(inpObjProject);
                            //     if(secondProjectID){
                            //       segment.saveSegment(`CDK : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                            //       portalUpdate.doPayloadAction(inpObjSecondProject);
                            //     }
                            // } catch(error) {
                            //     console.log("Error:", error);
                            //     segment.saveSegment(`CDK : doPayloadAction Error - ${JSON.stringify(error)}`); 
                            // }
                            //code end for portal update for extraction failed
                        }
                        att.endTime = new Date(moment().utc());
                        att.uniqueId = att.uniqueId ? att.uniqueId : util.generateUniqueId();
                        segment.saveSegment(`uniqueId 4444${att.uniqueId}`);
                        att.status = status;
                        att.message = message;
                        let oldStoreArray = job.attrs.data.storeDataArray;
                        let newStoreArray = [att];
                        oldStoreArray.map(data => {
                            if (data.dealerId === newStoreArray[0].dealerId) {
                                data = newStoreArray;
                            }
                        });
                        let _storeArray = oldStoreArray;
                        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                        job.attrs.data.storeDataArray = _storeArray;
                        // if(processFileName){
                        //     att.processFileName = processFileName.trim();
                        // }else{
                        //     processFileName = null;
                        // }
                        await job.save();
                        console.log(`CDK : Extraction process for store ${att.dealerId} exited code ${code}`);
                        segment.saveSegment(`CDK : Extraction process for store ${att.dealerId} exited code ${code}`);

                        i++;
                        if (i < storeDataArray.length) {
                            // Check time frame
                            if (att.runNow) {
                                segment.saveSegment(`CDK : runNow`);
                                await extract(storeDataArray[i], job);
                            } else if (util.checkExtractTimeFrame()) {
                                segment.saveSegment(`CDK : Check time frame and start extraction ${JSON.stringify(storeDataArray[i])}`);
                                await extract(storeDataArray[i], job);
                            } else {
                                const newDataArray = storeDataArray;
                                try {
                                    CDKJobManager.scheduleCDKExtractJob(CDKJobManager.createScheduleObject(job, newDataArray.slice(i)), true);
                                    job.attrs.data.storeDataArray = storeDataArray.slice(0, i);
                                    job.fail(new Error(`CDK : Time exceeded, remaining stores scheduled to next day.`));
                                    segment.saveSegment(`CDK : Time exceeded, remaining stores scheduled to next day.`);
                                    await job.save();
                                } catch (error) {
                                    console.error(error);
                                    segment.saveSegment(`Error : ${error.toString()}`);
                                }
                            }
                        } else {
                            done();
                        }
                    });
                  }
                } else {
                    console.error("CDK : Store data Extraction attributes not defined");
                    segment.saveSegment("CDK : Store data Extraction attributes not defined");
                }
            }
            if (att.runNow) { // Check whether it is for run now or not, if yes, no need to check time frame
                segment.saveSegment(`CDK : runNow : Check whether it is for run now or not, if yes, no need to check time frame ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else if (util.checkExtractTimeFrame()) {
                segment.saveSegment(`CDK : checkExtractTimeFrame ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else { // Auto schedule full Group wise schedule for tomorrowinpObjProject
                segment.saveSegment(`CDK : Auto schedule full Group wise schedule for tomorrow`);
                CDKJobManager.scheduleCDKExtractJob(CDKJobManager.createScheduleObject(job), true);
                job.remove();
            }
        });

    agenda.on("start", job => {
        console.log(`CDK : Job ${job.attrs.name}_${job.attrs._id} starting`);
    });

    agenda.on("complete", job => {
        console.log(`CDK : Job ${job.attrs.name}_${job.attrs._id} finished`);
    });

    agenda.on("fail", (err, job) => {
        console.log(`CDK : Job ${job.attrs.name}_${job.attrs._id} failed with error: ${err.message} `);
    });
    return agenda;
}

var gt = require("graphql-tools");
var resolvers = require("./resolver");

var typeDefs = `

# This type specifies the entry points into our API

#Scalar types
scalar Date
scalar DateTime
scalar _bsontype

# Type of CDK extract Job
enum JobType {
  # Back-end Job to run the CDK extraction
  initial
  refresh
  ondemand
}

# close RO options
enum ClosedROOption{
  # Extract options for Closed ROs
  all
  monthly
  weekly
  current
}

# Input to pass custom JSON data to a schedule
input JobData{
  groupName: String!
  storeDataArray: [ExtractData]!
}

input SingleStoreJobData{
  groupName: String!
  storeData: ExtractData!
}

input ExtractData{
  dealerId: String!
  projectId: String
  secondProjectId: String
  glAccountCompanyID: String
  mageManufacturer: String
  solve360Update: Boolean
  buildProxies: Boolean
  includeMetaData: Boolean
  extractAccountingData: Boolean
  dualProxy: Boolean
  userName: String
  startDate: Date!
  endDate: Date!
  zipPath: String
  closedROOption: ClosedROOption
  status: Boolean
  message: String
  jobType:  JobType
  mageGroupCode: String!
  mageStoreCode: String!
  stateCode: String!
  metaData: String
  projectIds:String
  secondProjectIdList:String
  testData:Boolean
  companyIds:String
  futureDate:String
  parentName:String
  companyObj:String
  jobType:  JobType
  projectType:String
  secondaryProjectType:String
  groupCode:String
  mageStoreName:String
  errors:String
  thirdPartyUsername:String
  assignedtoCn:String
  brands:String
}

type Data{
  groupName: String # Optional
  storeDataArray: [StoreData]
}

type QueueData{
  storeID: String,
  fileToProcess: String,
  priority:String
}

type XMLProcessData{
  operation: String # Avoid operation = recheck or operation = start
  inputFile: String
  storeID: String
  outputFile: String
  status: Boolean
  message: String
  createdAt: String
  coreReturnExceptionCount: Int
  coreChargeExceptionCount: Int
  coreReturnNotEqualCoreChargeExceptionCount: Int
  coreChargeWithNoSaleCount: Int
  invalidCoreCostSaleMismatchCount: Int
  invalidCoreAmountMismatchCount: Int
  partDetailsNullExceptionCount:Int
  processorUniqueId:String
  processorRunningStatus:String
}

type StoreData{
  dealerId: String!
  projectId: String
  secondProjectId: String
  glAccountCompanyID: String
  mageManufacturer: String
  solve360Update: Boolean
  buildProxies: Boolean
  includeMetaData: Boolean
  extractAccountingData: Boolean
  dualProxy: Boolean
  userName: String
  startDate: Date!
  endDate: Date!
  zipPath: String
  closedROOption: ClosedROOption
  startTime: String
  endTime:String
  status: Boolean
  message: String
  jobType:  JobType
  mageGroupCode: String
  mageStoreCode: String
  stateCode: String
  metaData: String
  projectIds:String
  secondProjectIdList:String
  testData:String
  companyIds:String
  companyObj:String
  parentName:String
  processFileName:String
  projectType:String
  secondaryProjectType:String
  groupCode:String
  mageStoreName:String
  brands:String
}

type ExtractJob{
  timeFrameZone: String
  timeFrameStartTime: String
  timeFrameEndTime:String
  poolTime:Int
  jobArray: [Job]
}

type Job {
  name: String
  data:Data
  type: String
  priority: String
  nextRunAt: String
  _id: _bsontype
  lastModifiedBy: String
  lockedAt: String
  lastRunAt: String
  lastFinishedAt: String
  running: Boolean
  scheduled: Boolean
  queued: Boolean
  completed: Boolean
  failed: Boolean
  repeating: Boolean
  failReason: String
}

type ProcessXMLJob {
  name: String
  data:XMLProcessData
  type: String
  priority: String
  nextRunAt: String
  _id: _bsontype
  lastModifiedBy: String
  lockedAt: String
  lastRunAt: String
  lastFinishedAt: String
  running: Boolean
  scheduled: Boolean
  queued: Boolean
  completed: Boolean
  failed: Boolean
  repeating: Boolean
  failReason: String
  uploadStatus: Boolean
}

input CDKScheduleInput{
  jobSchedule: DateTime!
  jobData: JobData!
}

input CancelCDKScheduleInput{
  jobSchedule: DateTime!
  jobData: SingleStoreJobData!
}

#Input to run a particular Store's CDK extraction
input RunNowCDKScheduleInput{
  jobSchedule: DateTime!
  jobData: SingleStoreJobData!
}

input ProxyGenerationUsingPgDump{
  zipPath: String!
  payType: String!
}

input CreateProxyInput{
  proxyJobData: ProxyGenerationUsingPgDump!
}

# Represents the Status of each Mutations
type Status {
  status: Boolean!
  message: String!
  job: Job
}

type ProcessXMLInfo{
  processXMLJobsQueue: [QueueData],
  processXMLJobs: [ProcessXMLJob]
}

type Query {
  getAllProcessXMLJobs: ProcessXMLInfo # Need to move to generic
  getAllCDKExtractJobs: ExtractJob
}

# Mutations
type Mutation {
  # Mutation to schedule a CDK extraction job
  scheduleCDKExtractJob (input: CDKScheduleInput!): Status,
  cancelCDKExtractJobByStore (input: CancelCDKScheduleInput!): Status,
  runNowCDKExtractJobByStore (input: RunNowCDKScheduleInput!): Status,
  createProxyWithSqlDump (input: CreateProxyInput!): Status
}

schema {
  query: Query,
  mutation: Mutation
}
`;
var schema = gt.makeExecutableSchema({ typeDefs, resolvers });
module.exports = schema;

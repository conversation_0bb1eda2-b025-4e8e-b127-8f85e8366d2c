const CDKJobManager = require("../CDKJobManager");
const validator = require("validator");
const constants = require("../constants");

var resolvers = {
  Query: {

    /**
    * Query to get all CDK-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllCDKExtractJobs(_, args) {
      return CDKJobManager.getAllCDKExtractJobs();
    },

    /**
     * Query to get all Process-XML schedules
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllProcessXMLJobs(_, args) {
      return CDKJobManager.getAllProcessXMLJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule CDK-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleCDKExtractJob(_, args) {
      return CDKJobManager.scheduleCDKExtractJob(args.input);
    },

    /**
     * Mutation to run a CDK-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowCDKExtractJobByStore(_, args) {
      return CDKJobManager.runNowCDKExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a CDK-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelCDKExtractJobByStore(_, args) {
      return CDKJobManager.cancelCDKExtractJobByStore(args.input);
    },

    /**
     * Mutation to create a Process XML job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */

    createProxyWithSqlDump(_, args) {
      console.log('#######################################inside  createProxyWithSqlDump ##################################################################### ');
      console.log(args);
      return CDKJobManager.createProxyWithSqlDump(args.input);
    },
  },
  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;

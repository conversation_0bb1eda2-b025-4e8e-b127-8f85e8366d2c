const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeAutomateProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "automate-process-json") {
      initializeAutomateProcessJSON = true
    }
    if (type.split('-')[0] == 'automate') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the automate-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeAutomateProcessJSON) {
    try {
      // Initialize Automate Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("Automate :  Process JSON schedule started");
      segment.saveSegment("Automate :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("AutoMate Extraction Processing Not Enabled - Pass Job Type automate-process-json To Enable");
    segment.saveSegment("AutoMate Extraction Processing Not Enabled - Pass Job Type automate-process-json To Enable");
  }
  return true;
}

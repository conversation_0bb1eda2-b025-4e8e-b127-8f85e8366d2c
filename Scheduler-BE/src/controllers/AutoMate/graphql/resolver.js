const AutoMateJobManager = require("../AutoMateJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all AutoMate-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllAutoMateExtractJobs(_, args) {
      return AutoMateJobManager.getAllAutoMateExtractJobs();
    },

    /**
     * Query to get all Automate Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllAutoMateProcessJSONJobs(_, args) {
      return AutoMateJobManager.getAllAutoMateProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule AutoMate-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleAutoMateExtractJob(_, args) {
      return AutoMateJobManager.scheduleAutoMateExtractJob(args.input);
    },

    /**
     * Mutation to run a AutoMate-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowAutoMateExtractJobByStore(_, args) {
      return AutoMateJobManager.runNowAutoMateExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a AutoMate-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelAutoMateExtractJobByStore(_, args) {
      return AutoMateJobManager.cancelAutoMateExtractJobByStore(args.input);
    },
    
        /**
     * Mutation to create a Process XML job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */


    createProxyWithSqlDump(_, args) {
      return AutoMateJobManager.createProxyWithSqlDump(args.input);
    },
  },


  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;

"use strict";

const constants = require("../constants");
const util = require("../util");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const { spawn } = require("child_process");
const path = require('path');
const fs = require("fs");
const moment = require("moment-timezone");
const segment = require("../../SEGMENT/AutoMate/segmentManager");
const sharePoint = require("../../../routes/sharePoint");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const stripAnsi = require('strip-ansi');
const extractionError = require('../../../../src/common/extractionError');
const SetProcessJobStatus = require('../../../model/setProcessJobStatus');
var Agenda = require("../../agenda");
const csv1 = require('csv-parser');
const csv = require('csvtojson');
/**
 * Function to perform processing of JSON file downloaded through Automate-Extract job
 */
module.exports = async function ProcessXmlJOB(agenda) {

    var distributeFile = async function (fileName, rerunFlag, updateSolve360Data, warningObj, jobId) {
        var stdErrorArray;
        var distDir = path.join(process.env.AUTOMATE_DIST_DIR, fileName);
        var etlDir = path.join(process.env.AUTOMATE_ETL_DIR, fileName);
        etlDir = etlDir.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
        var filePath = path.join(process.env.AUTOMATE_BUNDLE_DIR, fileName);
        const distributeFile = spawn("bash",
            [
                'send-bundle-live-hpdog', filePath, rerunFlag
            ], {
            cwd: constants.PROCESS_JSON.AUTOMATE_DISTRIBUTE_CMD_PATH,
            env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
        }).on('error', function (err) {
            console.log("error :", err);
            segment.saveSegment(`error: ${err}`);
        });
        console.log(`Automate: Start processing of distribution`);
        segment.saveSegment(`Automate:  processing distribution`);
        process.stdin.pipe(distributeFile.stdin);
        distributeFile.stdout.on("data", async (data) => {
            console.log(`stdout: ${data}`);
            segment.saveSegment(`stdout: ${data}`);
        });

        distributeFile.stderr.on("data", async (data) => {
            console.log(`stderr: ${data}`);
            stdErrorArray += data.toString() + ' ';
            segment.saveSegment(`stderr: ${data}`);
        });

        distributeFile.on("close", async (code) => {
            var message = "n/a";
            var status = false;
            if (code == constants.STATUS_CODE.SUCCESS) {
                status = true;
                message = "Success";
            } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                message = "Distribution failed, general death";
            } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                message = "Distribution failed";
            }
            segment.saveSegment(`close: ${message}`);
            if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_EXIST_CHECK)) {
                status = false;
                message = "Distribution failed. Zip File Must Exist";
                segment.saveSegment(message);
            }
            /**
              * Upload files to SharePoint
              */
            if (status) {
                sharePoint.initSharePoint(distDir, constants.JOB_TYPE, rerunFlag, updateSolve360Data, warningObj, 0, jobId);//Upload dist directory zip file to sharepoint
            }

            await doNextProcess();
        });
    }

    var doNextProcess = async function () {
        await segment.sleep(5000);
        const extractZip = await util.findOldestZipFile(constants.AUTOMATE_SCHEDULER_ETI_DIR);
        if (fs.existsSync(`/home/<USER>/tmp/du-etl-dms-automate-extractor-work/scheduler-temp/automate-zip-eti/${extractZip}`)) {
            console.log(`file Name ${extractZip} exists!`);
        } else {
            console.log(`file Name ${extractZip} does not exists`);
        }
        if (extractZip && fs.existsSync(`/home/<USER>/tmp/du-etl-dms-automate-extractor-work/scheduler-temp/automate-zip-eti/${extractZip}`)) {
            console.log(`AutoMate : Found one Store extraction > ${extractZip} to process now`);
            segment.saveSegment(`AutoMate : Found one Store extraction > ${extractZip} to process now`);
            try {
                var createdAt = extractZip.slice(0, extractZip.length - 4).split("-").reverse()[0];
                await agenda.now(constants.PROCESS_JSON.JOB_NAME, {
                    inputFile: extractZip,
                    createdAt: createdAt,
                    operation: "json-processing"
                });
                console.log(`AutoMate : Process JSON schedule started with file > ${extractZip}`);
                segment.saveSegment(`AutoMate : Process JSON schedule started with file > ${extractZip}`);
            } catch (error) {
                console.error(error);
            }

        } else {
            console.log(`AutoMate : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            //segment.saveSegment(`AutoMate : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            try {
                await agenda.schedule(`${constants.PROCESS_JSON.TIME_GAP}`, constants.PROCESS_JSON.JOB_NAME, { operation: "recheck" });
                console.log(`AutoMate : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
                //segment.saveSegment(`AutoMate : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
            } catch (error) {
                console.error(error);
            }
        }


    }

    console.log(
        `AutoMate : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`
    );
    segment.saveSegment(`AutoMate : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`);
    agenda.define(constants.PROCESS_JSON.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.PROCESS_JSON.CONCURRENCY, lockLifetime: 3 * 60 * 60 * 1000 },
        async (job, done) => {
            const touch = setInterval(() => job.touch(), 1 * 60 * 1000);

            const att = job.attrs.data;
            var extractZip = null;
            var stdErrorArray = [];
            var stdOutArray = [];

            var inpFile = att.inputFile ? path.join(constants.AUTOMATE_SCHEDULER_ETI_DIR, att.inputFile) : '';

            if (att.inputFile && fs.existsSync(inpFile)) {
                if (!fs.existsSync(constants.AUTOMATE_DEADLETTER_DIR_PREFIX + '-processed')) {
                    fs.mkdirSync(constants.AUTOMATE_DEADLETTER_DIR_PREFIX + '-processed');
                }
                extractZip = att.inputFile;
                let basename = path.basename(extractZip);
                var doProxyFromPgDump = false;
                job.attrs.data.storeID = basename.split("-").reverse()[1];
                let storeName = job.attrs.data.storeID;
                var storeCode = storeName;


                let dealerId = basename.split("-").reverse()[1];
                let mageGroupCode = basename.split("-")[0];
                let mageStoreCode = basename.split("-")[1];

                if (basename.includes(constants.PROCESS_JSON.REPLACE_STRING.DO_PROXY_FROM)) {
                    doProxyFromPgDump = true;
                    dealerId = basename.split("-").reverse()[2];
                } else {
                    dealerId = basename.split("-").reverse()[1];
                    doProxyFromPgDump = false;
                }
                job.attrs.data.storeID = !doProxyFromPgDump ? basename.split("-").reverse()[1] : basename.split("-").reverse()[2];

                console.log('Groupname:', mageGroupCode);
                segment.saveSegment(`Groupname : ${mageGroupCode}`);

                console.log('DealerId:', dealerId);
                segment.saveSegment(`DealerId : ${dealerId}`);

                console.log('storeName:', mageStoreCode);
                segment.saveSegment(`storeName : ${mageStoreCode}`);

                var jobsTmp = await Agenda.jobs({
                    $and: [
                        { "data.storeDataArray.dealerId": dealerId },
                        { "name": constants.AUTOMATE.JOB_NAME },
                        { "data.storeDataArray.mageStoreCode": mageStoreCode }

                    ]
                });

                var projectId = '';
                var secondProjectId = '';
                var solve360Update = '';
                var userName = '';
                var updateSolve360Data;
                var buildProxies;
                var extractionId;

                let agendaObject;
                let extractedFileTimeStamp;
                let extractedFileCreationDate;
                let extractedObjectIndex;
                let dealerAddress;

                let mageManufacturer;
                let isPorscheStore;

                let haltIdentifier = false;
                let haltOverRide = false;
                let resumeUser;
                let projectIds;
                let secondProjectIdList;
                let uniqueId;
                let companyIds;
                let companyObj;
                let testData;
                var processorStatus;
                let totalRoCount;
                let brands;
                let exceptionTypeCounts = {};

                try {
                    extractedFileTimeStamp = basename.split("-").reverse()[0].replace(".zip", "");
                    segment.saveSegment(`extractedFileTimeStamp : ${extractedFileTimeStamp}`);
                    extractedFileCreationDate = moment(extractedFileTimeStamp, "YYYYMMDDhhmmss").format("YYYY-MM-DD");
                    segment.saveSegment(`extractedFileCreationDate : ${extractedFileCreationDate}`);
                } catch (err) {
                    console.log(err);
                    segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                }

                segment.saveSegment(`jobsTmp : ${JSON.stringify(jobsTmp)}`);
                segment.saveSegment(`jobsTmp[jobsTmp.length-1] : ${JSON.stringify(jobsTmp[jobsTmp.length - 1])}`);
                segment.saveSegment(`Dealer ID : ${dealerId}`);

                if (jobsTmp[jobsTmp.length - 1]) {
                    if (jobsTmp[jobsTmp.length - 1].hasOwnProperty("attrs")) {
                        extractionId = jobsTmp[jobsTmp.length - 1].attrs._id;
                        try {
                            segment.saveSegment(`jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : ${JSON.stringify(jobsTmp[jobsTmp.length - 1].attrs.data.storeDataArray)}`);
                            agendaObject = jobsTmp[jobsTmp.length - 1].attrs.data.storeDataArray;
                            // && el.mageGroupCode == mageGroupCode
                            agendaObject = agendaObject.filter(function (el) {
                                return el.dealerId == dealerId && el.mageStoreCode == mageStoreCode;
                            });
                            segment.saveSegment(`agendaObject : ${JSON.stringify(agendaObject)}`);
                            extractedObjectIndex = 0;
                            if (agendaObject.length > 0) {
                                agendaObject = agendaObject.sort((a, b) => b.endTime > a.endTime);
                                extractedObjectIndex = agendaObject.findIndex(
                                    obj => moment(obj.endTime, "YYYY-MM-DDTHH:mm:ss.SSS[Z]").format("YYYY-MM-DD") == extractedFileCreationDate
                                );
                            }

                            if (extractedObjectIndex < 0) {
                                extractedObjectIndex = 0;
                            }

                            segment.saveSegment(`Sorted agenda object : ${JSON.stringify(agendaObject)}`);
                            segment.saveSegment(`extractedObjectIndex : ${extractedObjectIndex}`);
                            segment.saveSegment(`Extracted agenda object : ${JSON.stringify(agendaObject[extractedObjectIndex])}`);


                            if (agendaObject[extractedObjectIndex].hasOwnProperty("projectId")) {
                                projectId = agendaObject[extractedObjectIndex].projectId;
                            }
                            if (agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectId")) {
                                secondProjectId = agendaObject[extractedObjectIndex].secondProjectId;
                            }
                            if (agendaObject[extractedObjectIndex].hasOwnProperty("solve360Update")) {
                                solve360Update = agendaObject[extractedObjectIndex].solve360Update;
                            }
                            if (agendaObject[extractedObjectIndex].hasOwnProperty("buildProxies")) {
                                buildProxies = agendaObject[extractedObjectIndex].buildProxies;
                            }
                            if (agendaObject[extractedObjectIndex].hasOwnProperty("includeMetaData")) {
                                includeMetaData = agendaObject[extractedObjectIndex].includeMetaData;
                            }
                            if (agendaObject[extractedObjectIndex].hasOwnProperty("userName")) {
                                userName = agendaObject[extractedObjectIndex].userName;
                            }
                            if (agendaObject[extractedObjectIndex].hasOwnProperty("dealerAddress")) {
                                dealerAddress = agendaObject[extractedObjectIndex].dealerAddress;
                                dealerAddress = dealerAddress ? dealerAddress.replace(/\n/g, "~") : '';
                            }

                            if (agendaObject[extractedObjectIndex].hasOwnProperty("mageManufacturer")) {
                                mageManufacturer = agendaObject[extractedObjectIndex].mageManufacturer;
                            }

                            if (agendaObject[extractedObjectIndex].hasOwnProperty("haltOverRide")) {
                                haltOverRide = agendaObject[extractedObjectIndex].haltOverRide;
                            }

                            if (agendaObject[extractedObjectIndex].hasOwnProperty("projectIds")) {
                                projectIds = agendaObject[extractedObjectIndex].projectIds;
                                projectIds = projectIds.split("*");

                            }

                            if (agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectIdList")) {
                                secondProjectIdList = agendaObject[extractedObjectIndex].secondProjectIdList;
                                secondProjectIdList = secondProjectIdList.split("*");
                            }

                            if (agendaObject[extractedObjectIndex].hasOwnProperty("companyIds")) {
                                companyIds = agendaObject[extractedObjectIndex].companyIds;
                                companyIds = companyIds.replace(new RegExp('\\*', 'g'), ',')
                            }


                            if (agendaObject[extractedObjectIndex].hasOwnProperty("uniqueId")) {
                                uniqueId = agendaObject[extractedObjectIndex].uniqueId;
                                uniqueId += '-' + Date.now();

                            }
                            if (agendaObject[extractedObjectIndex].hasOwnProperty("companyObj")) {
                                companyObj = JSON.parse(agendaObject[extractedObjectIndex].companyObj);
                            }
                           
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("brands")){
                                    console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                                    brands = agendaObject[extractedObjectIndex].brands.split("*");

                                    console.log("brands exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",brands);
                                }

                                if(brands.length>1){
                                     const hasPorche = brands.some(item => item.toLowerCase() === 'porche');
                                    if (hasPorche && brands.length > 1) {
                                         mageManufacturer = brands.find(item => item.toLowerCase() !== 'porche');
                                         isPorscheStore = true;
                                       }
                                }else{
                                    if(mageManufacturer == constants.AUTOMATE.PORSCHE_STORE_LABEL){
                                         isPorscheStore = true;
                                   } else{
                                    isPorscheStore = false; 
                                 }
                                }
                           
                           if (agendaObject[extractedObjectIndex].hasOwnProperty("testData")) {
                                testData = agendaObject[extractedObjectIndex].testData;
                            }

                        } catch (err) {
                            console.log(err);
                            segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                        }
                    }
                }



                console.log('projectId:', projectId);
                segment.saveSegment(`projectId : ${projectId}`);

                console.log('secondProjectId:', secondProjectId);
                segment.saveSegment(`secondProjectId : ${secondProjectId}`);

                console.log('userName:', userName);
                segment.saveSegment(`userName : ${userName}`);

                console.log('solve360Update:', solve360Update);
                segment.saveSegment(`solve360Update : ${solve360Update}`);

                console.log('buildProxies:', buildProxies);
                segment.saveSegment(`buildProxies : ${buildProxies}`);

                console.log('extractionId:', extractionId);
                segment.saveSegment(`extractionId : ${extractionId}`);

                console.log('dealerAddress:', dealerAddress);
                segment.saveSegment(`dealerAddress : ${dealerAddress}`);

                console.log('haltOverRide:', haltOverRide);
                segment.saveSegment(`haltOverRide : ${haltOverRide}`);

                console.log('uniqueId:', uniqueId);
                segment.saveSegment(`uniqueId : ${uniqueId}`);

                console.log('testData:', testData);
                segment.saveSegment(`testData : ${testData}`);

                console.log('companyIds:', companyIds);
                segment.saveSegment(`companyIds : ${companyIds}`);

                if (haltOverRide) {
                    resumeUser = `${userName}`;
                    console.log('resumeUser:', resumeUser);
                    segment.saveSegment(`resumeUser : ${resumeUser}`);


                }
                if (haltOverRide) {
                    let todayDate;
                    let attPayload = {};
                    let projectID;
                    let secondProjectID;
                    let inpObjProject;
                    let inpObjSecondProject;
                    let secondProjectIdList;
                    let projectIdList;
                    try {
                        todayDate = new Date().toISOString().slice(0, 10);
                        attPayload = agendaObject[extractedObjectIndex];
                        projectID = attPayload.hasOwnProperty('projectId') ? attPayload.projectId : "";
                        attPayload['inProjectId'] = projectID;

                        secondProjectID = attPayload.hasOwnProperty('secondProjectId') ? attPayload.secondProjectId : "";
                        attPayload.in_is_update_retrieve_ro = attPayload.hasOwnProperty('solve360Update') ? attPayload.solve360Update : "";
                        projectIdList = attPayload.hasOwnProperty('projectIds') ? attPayload.projectIds.split("*") : "";
                        secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ? attPayload.secondProjectIdList.split("*") : "";
                        attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                        attPayload.in_retrive_ro_request_on = todayDate;
                        console.log(inpObjProject, "******** INP OBJJJJJ ***********");
                    } catch (err) {
                        console.log(JSON.stringify(err));
                        segment.saveSegment(`AUTOMATE : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                    }
                    segment.saveSegment(`AUTOMATE : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                    segment.saveSegment(`AUTOMATE : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                    try {
                        segment.saveSegment(`AUTOMATE : doPayloadAction - ${JSON.stringify(inpObjProject)}`);
                        if (projectIdList) {
                            for (const id of projectIdList) {
                                if (id != undefined && id != '') {
                                    inpObjProject = commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                                    portalUpdate.doPayloadAction(inpObjProject);
                                    console.log(`Delertrack Schedule portal call with Project Id RESUME${id}`);
                                    segment.saveSegment(`Dealertrack Schedule portal call with Project Id RESUME${id}`);

                                }
                            }
                        }

                        if (secondProjectIdList.length > 0) {
                            segment.saveSegment(`CDK : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                            if (secondProjectIdList) {
                                for (const id of secondProjectIdList) {
                                    if (id != undefined && id != '') {
                                        inpObjSecondProject = commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                                        portalUpdate.doPayloadAction(inpObjSecondProject);
                                        console.log(`Dealertrack Schedule portal call with Second Project Id Resume${id}`);
                                        segment.saveSegment(`Dealertrack Schedule portal call with Second Project Id RESUME${id}`);

                                    }
                                }
                            }

                        }
                    } catch (error) {
                        console.log("Error:", error);
                        segment.saveSegment(`DEALERTRACK  : doPayloadAction Error - ${JSON.stringify(error)}`);
                    }
                }


                // if (mageManufacturer == constants.AUTOMATE.PORSCHE_STORE_LABEL) {
                //     isPorscheStore = true;
                // } else {
                //     isPorscheStore = false;
                // }

                segment.saveSegment(`mageManufacturer : ${mageManufacturer}`);
                segment.saveSegment(`isPorscheStore : ${isPorscheStore}`);

                updateSolve360Data = { projectId: projectId, secondProjectId: secondProjectId, userName: userName, solve360Update: solve360Update, thirdPartyUsername: dealerId, storeCode: mageStoreCode, dmsType: constants.JOB_TYPE, groupCode: mageGroupCode, resumeUser: resumeUser ? resumeUser : '', projectIds: projectIds, secondProjectIdList: secondProjectIdList, uniqueId: uniqueId, testData: testData, companyObj: companyObj };

                console.log(updateSolve360Data);
                segment.saveSegment(`updateSolve360Data : ${updateSolve360Data}`);


                let buildProxiesDecider;
                if (buildProxies) {
                    buildProxiesDecider = constants.PROCESS_JSON.OPT_BUILD_PROXY_RO;
                } else {
                    buildProxiesDecider = constants.PROCESS_JSON.OPT_NO_BUILD_PROXY_RO;
                }

                await job.save();
                if (doProxyFromPgDump) {
                    spawnInputArray = [
                        constants.PROCESS_JSON.PROCESS_CMD,
                        constants.PROCESS_JSON.OPT_BUNDLE_DIR, constants.PROCESS_JSON.AUTOMATE_BUNDLE_DIR,
                        constants.PROCESS_JSON.OPT_BUILD_PROXY_USING, path.join(constants.AUTOMATE_SCHEDULER_ETI_DIR, extractZip),
                        constants.PROCESS_JSON.OPT_ZAP_INPUT,
                        constants.PROCESS_JSON.OPT_DEADLETTER_DIR_PREFIX, constants.AUTOMATE_DEADLETTER_DIR_PREFIX + '-processed',
                        constants.PROCESS_JSON.OUTPUT_PREFIX, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL,
                        constants.PROCESS_JSON.DEALER_ADDRESS, dealerAddress,
                        "--uuid", uniqueId,
                        "--company_ids", companyIds


                    ];
                } else {
                    var spawnInputArray = [
                        constants.PROCESS_JSON.PROCESS_CMD,
                        constants.PROCESS_JSON.OPT_BUNDLE_DIR, constants.PROCESS_JSON.AUTOMATE_BUNDLE_DIR,
                        constants.PROCESS_JSON.OPT_INPUT_ZIP, path.join(constants.AUTOMATE_SCHEDULER_ETI_DIR, extractZip),
                        constants.PROCESS_JSON.OPT_ZAP_INPUT,
                        constants.PROCESS_JSON.OPT_PERFORM_ZIP,
                        buildProxiesDecider,
                        isPorscheStore ? constants.PROCESS_JSON.OPT_PORSCHE_STORE : '',
                        constants.PROCESS_JSON.OPT_DEADLETTER_DIR_PREFIX,
                        constants.AUTOMATE_DEADLETTER_DIR_PREFIX + '-processed',
                        constants.PROCESS_JSON.OUTPUT_PREFIX,
                        constants.PROCESS_JSON.OUTPUT_PREFIX_VAL,
                        constants.PROCESS_JSON.HALT_OVER_RIDE,
                        haltOverRide,
                        constants.PROCESS_JSON.DEALER_ADDRESS,
                        dealerAddress,
                        "--uuid", uniqueId,
                        "--performed-by", userName,
                        "--exception-report", true,
                        "--company_ids", companyIds
                    ]
                }

                const processJson = spawn("bash",
                    spawnInputArray, {
                    cwd: constants.PROCESS_JSON.PROCESS_CMD_PATH,
                    env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
                }).on('error', function (err) {
                    console.log("error ::", err);
                    segment.saveSegment(`error: ${err}`);
                });

                console.log(`AutoMate : Start processing of extraction > ${basename}`);
                segment.saveSegment(`AutoMate : Start processing of extraction > ${basename}`);
                segment.saveSegmentFailure(`Start processing of extraction > ${basename}`, storeCode);
                process.stdin.pipe(processJson.stdin);

                processJson.stdout.on("data", async (data) => {
                    console.log(`stdout: ${data}`);
                    stdOutArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));


                    data = data.toString('utf8');


                      if(data.includes('Total Ros Count:-')){
                            segment.saveSegment(`Total Ros Count55555555555,${data}`);
                            console.log('file generated',data.split(':')[1]);
                            segment.saveSegment(`Total Ro90999999999,${data.split(':')[1]}`);
                            totalRoCount = data.split(':-')[1];
                            segment.saveSegment(`totalRoCount666666,${totalRoCount}`);
                     
                       }else{
                            console.log("failed to generate1212 file")
                      } 

                    if (data.includes('Processor status')) {
                        segment.saveSegment('Processor statu for UI', data);
                        console.log('file generated', data.split(':')[1]);
                        processorStatus = data.split(':')[1];
                        segment.saveSegment('processorStatus', processorStatus);
                        await SetProcessJobStatus.setProcessJobStatusForRunningJob(basename, processorStatus);
                    } else {
                        console.log("failed to generate file")
                    }


                    await job.touch();
                    segment.saveSegment(`stdout: ${data}`);
                    segment.saveSegmentFailure(`stdout: ${data}`, storeCode);
                });

                processJson.stderr.on("data", async (data) => {
                    console.log(`stderr: ${data}`);
                    stdErrorArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                    await job.touch();
                    segment.saveSegment(`stderr: ${data}`);
                    segment.saveSegmentFailure(`stderr: ${data}`, storeCode);
                });

                processJson.on("close", async (code) => {
                    const filePath = '/home/<USER>/tmp/du-etl-dms-automate-extractor-work/exception_tag/All_exception_details.csv';
                    if (fs.existsSync(filePath)) {
                      fs.createReadStream(filePath)
                      .pipe(csv1())
                      .on('data', (row) => {
                      const type = row['Type'];
                       if (type) {
                          exceptionTypeCounts[type] = (exceptionTypeCounts[type] || 0) + 1;
                        }
                    })
                     .on('end', () => {
                     console.log("exceptionTypeCounts", exceptionTypeCounts);
                     segment.saveSegment(`exceptionTypeCounts: ${JSON.stringify(exceptionTypeCounts)}`);
                  });
               } else {
                   console.error(`File not found: ${filePath}`);
                   segment.saveSegment(`Error: File not found at path ${filePath}`);
                }

                    var message = "n/a";
                    var status = false;
                    if (code == constants.STATUS_CODE.SUCCESS) {
                        status = true;
                        message = "Success";
                    } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                        message = "Extraction failed, general death";
                        job.fail(new Error(`Error: ${message}`));
                    } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                        message = "Extraction failed, moved to dead-letter path";
                        job.fail(new Error(`Error: ${message}`));
                    }

                    if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_PROCESSING_FAILED)) {
                        message = "Extraction failed, Zip File Processing Failed,  ";
                        status = false;
                    }
                    var deadLetterPath = `${process.env.AUTOMATE_WORK_DIR}/dead-letter-processed`;
                    var errResp = `Moving input to dead-letter bin: ${deadLetterPath}`;
                    if (stdOutArray && stdOutArray.includes(errResp)) {
                        message += errResp
                        status = false;
                    }


                    console.log(stdErrorArray);
                    if (stdErrorArray && !haltOverRide) {

                        console.log(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT);
                        console.log(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_DEAD);

                        stdErrorArray.forEach((v, i) => {
                            if (
                                v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT)
                            ) {
                                message = "Halt";
                                haltIdentifier = true;
                                status = false;
                            }

                            if (
                                v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_DEAD)
                            ) {
                                message = "Dead";
                                haltIdentifier = true;
                                status = false;
                            }


                        });

                        try {
                            if (
                                stdOutArray &&
                                stdOutArray.includes(errResp) &&
                                message == "Halt" &&
                                !haltOverRide
                            ) {
                                let deadLetterFilePath = deadLetterPath + "/" + basename;
                                let haltFilePath =
                                    "/home/<USER>/tmp/du-etl-dms-automate-extractor-work/scheduler-temp/halt/" +
                                    basename;
                                if (fs.existsSync(deadLetterFilePath)) {
                                    fs.copyFile(deadLetterFilePath, haltFilePath, (err) => {
                                        if (err) {
                                            console.log('err', err);
                                        }
                                        console.log(`${deadLetterFilePath} was copied to ${haltFilePath}`);
                                    });
                                } else {
                                    console.log(`${deadLetterFilePath} not exist!`);
                                }
                            } else {
                                console.log('Not a Halt process')
                            }
                        } catch (err) {
                            console.log(err);
                        }
                    }

                    console.log(`AutoMate : JSON processing job for Store ${storeName} exited with code ${code}`);
                    segment.saveSegment(`AutoMate : JSON processing job for Store ${storeName} exited with code ${code}`);
                    segment.saveSegmentFailure(`AutoMate : JSON processing job for Store ${storeName} exited with code ${code}`, storeCode);

                    var extractionErrorResponse;
                    var errorWarnningMessage;
                    var warningObj = {};
                    warningObj.scheduled_by = userName;
                    
                    if (testData) {
                        warningObj.testData = true;
                        warningObj.userName = userName;
                    }
                    if (extractionId) {
                        extractionErrorResponse = await extractionError.displayErrorLogWithSpecific(extractionId, 'AutoMate');
                        console.log('extractionErrorResponse:', extractionErrorResponse);
                        if (extractionErrorResponse.status) {
                            let resp = JSON.parse(JSON.stringify(extractionErrorResponse.response))
                            let tmpDescritionArray = [];
                            resp.forEach(e => {
                                tmpDescritionArray.push(e.description);
                            });
                            errorWarnningMessage = tmpDescritionArray.join(", ");
                        }
                    }
                    console.log('errorWarnningMessage:', errorWarnningMessage);

                    if (errorWarnningMessage) {
                        if (errorWarnningMessage.length > 0) {
                            warningObj.errorwarningMessage = errorWarnningMessage;
                        }
                    }

                    let miscExceptionCsvFilePath = constants.MISC_EXCEPTION_CSV_FILE_PATH;
                    let jsonMiscExceptionArray, jsonMiscExceptionMessage, miscExceptionCount = 0;

                    try {
                        if (fs.existsSync(miscExceptionCsvFilePath)) {
                            segment.saveSegment(`The misc exception csv File exists:${miscExceptionCsvFilePath}`);
                            segment.saveSegmentFailure(`The misc exception csv File exists:${miscExceptionCsvFilePath}`, storeCode);
                            jsonMiscExceptionArray = await csv().fromFile(miscExceptionCsvFilePath);

                            if (jsonMiscExceptionArray) {
                                if (jsonMiscExceptionArray.length) {
                                    segment.saveSegment(`jsonMiscExceptionArray.length:${jsonMiscExceptionArray.length}`);
                                    segment.saveSegmentFailure(`jsonMiscExceptionArray.length:${jsonMiscExceptionArray.length}`, storeCode);

                                    jsonMiscExceptionMessage = jsonMiscExceptionArray[jsonMiscExceptionArray.length - 1]['RONumber'];
                                    if (jsonMiscExceptionMessage) {
                                        miscExceptionCount = parseInt(jsonMiscExceptionMessage.split(",")[0].split(":").reverse()[0].trim());
                                    }
                                    segment.saveSegment(`jsonMiscExceptionMessage: ${jsonMiscExceptionMessage}`);
                                    segment.saveSegmentFailure(`jsonMiscExceptionMessage: ${jsonMiscExceptionMessage}`, storeCode);
                                    segment.saveSegment(`miscExceptionCount: ${miscExceptionCount}`);
                                    segment.saveSegmentFailure(`miscExceptionCount: ${miscExceptionCount}`, storeCode);

                                }
                            }

                        }

                    } catch (err) {

                    }

                    let failureDirectory = process.cwd() + '/logs/AutoMate/failure/';
                    let failurelogFile = failureDirectory + storeCode + '.log';

                    var fetchGroupAndStoreName = (job.attrs.data.inputFile).split('-');
                    var groupName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[0] : '';
                    var storeName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[1] : '';
                    segment.saveSegment(`miscExceptionCount : ${miscExceptionCount}`);

                    warningObj.miscExceptionCount = miscExceptionCount;
                    if (miscExceptionCount) warningObj.miscExceptionCsvFilePath = miscExceptionCsvFilePath;

                    var mailTemplateReplacementValues = {
                        dmsType: constants.JOB_TYPE,
                        processTypes: constants.PROCESS_JSON.JOB_NAME,
                        subject: `Process JSON for ${groupName} - ${storeName} Completed`,
                        warningObj: warningObj,
                        thirdPartyUsername: dealerId,
                        storeCode: storeName,
                        groupCode: groupName
                    };
                    var mailBody = {
                        fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                        toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                        ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                        attachedfailurelogFile: failurelogFile
                    }

                    let displayMessage;
                    
                    job.attrs.data.processorUniqueId = '';
                    job.attrs.data.testData = testData;
                     
                    if(uniqueId && uniqueId!=undefined){
                      job.attrs.data.processorUniqueId = uniqueId;
                     }


                    if (status) {
                        clearInterval(touch);
                        var opDataFileEtl = path.join(constants.PROCESS_JSON.AUTOMATE_ETL_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                        var opDataFileDist = path.join(constants.PROCESS_JSON.AUTOMATE_DIST_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                        opDataFileEtl = opDataFileEtl.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
                        var outputFile = opDataFileDist + ' & ' + opDataFileEtl;
                        job.attrs.data.outputFile = outputFile;
                        job.attrs.data.status = status;
                        job.attrs.data.message = message;
                        job.attrs.data.miscExceptionCount = miscExceptionCount;
                        job.attrs.data.address = dealerAddress;
                        segment.saveSegment(`Job saved to DB ${JSON.stringify(job)}`);
                        segment.saveSegmentFailure(`Job saved to DB ${JSON.stringify(job)}`, storeCode);


                        await job.save();
                        done();

                        displayMessage = `Completed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                        mailTemplateReplacementValues.message = displayMessage;
                        mailTemplateReplacementValues.status = 'Success';
                        mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                        // Send notification after process json job completed
                        mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);

                    } else {
                        // Portal update for process json failed
                        const directoryPath = '/home/<USER>/tmp/du-etl-dms-automate-extractor-work/scheduler-temp/automate-zip-eti/'; 
                        const fileName = basename;                                 
                        const filePath = path.join(directoryPath, fileName);
                        segment.saveSegment(`Autosoft : filePath inpObj Error - ${fileName}`);
            
                        if (fs.existsSync(filePath)) {
                            fs.unlink(filePath, (err) => {
                                if (err) {
                                    segment.saveSegment(`Autosoft : Error deleting file - ${err}`);
                                    console.error('Error deleting file:', err);
                                } else {
                                    segment.saveSegment(`Autosoft : File deleted successfully - ${filePath}`);
                                    console.log('File deleted successfully:', filePath);
                                }
                            });
                        } else {
                            console.log('File does not exist:', filePath);
                        }


                        clearInterval(touch);
                        let todayDate;
                        let attPayload = {};
                        let projectID;
                        let secondProjectID;
                        let inpObjProject;
                        let inpObjSecondProject;
                        let secondProjectIdList;
                        let projectIdList;
                        try {
                            todayDate = new Date().toISOString().slice(0, 10);
                            attPayload = agendaObject[extractedObjectIndex];
                            projectID = attPayload.hasOwnProperty('projectId') ? attPayload.projectId : "";
                            projectIdList = attPayload.hasOwnProperty('projectIds') ? attPayload.projectIds.split("*") : "";
                            secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ? attPayload.secondProjectIdList.split("*") : "";
                            segment.saveSegment(`AUTOMATET : projectIdList - ${projectIdList}`);
                            segment.saveSegment(`AUTOMATE : secondProjectIdList - ${secondProjectIdList}`);
                            attPayload['inProjectId'] = projectID;

                            secondProjectID = attPayload.hasOwnProperty('secondProjectId') ? attPayload.secondProjectId : "";
                            attPayload.in_is_update_retrieve_ro = attPayload.hasOwnProperty('solve360Update') ? attPayload.solve360Update : "";

                            attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                            attPayload.in_retrive_ro_request_on = todayDate;
                            job.attrs.data.address = dealerAddress;
                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                            console.log(inpObjProject, "******** INP OBJJJJJ ***********");
                            if (secondProjectIdList.length > 0) {
                                inpObjSecondProject = commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********");
                            }
                        } catch (err) {
                            console.log(JSON.stringify(err));
                            segment.saveSegment(`AutoMate : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                        }
                        segment.saveSegment(`AutoMate : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                        segment.saveSegment(`AutoMate : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                        try {
                            segment.saveSegment(`AutoMate : doPayloadAction - ${JSON.stringify(inpObjProject)}`);
                            let parsedData = JSON.parse(inpObjProject.inData);
                            let projectIdList = parsedData.projectIds.split("*");
                            if (projectIdList) {
                                for (const id of projectIdList) {
                                    if (id != undefined && id != '') {
                                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                        portalUpdate.doPayloadAction(inpObjProject);
                                        console.log(`AutoMate Schedule portal call with Project Id FAILURE${id}`);
                                        segment.saveSegment(`AutoMate Schedule portal call with Project Id FAILURE${id}`);


                                    }
                                }
                            }


                            if (secondProjectIdList.length > 0) {
                                segment.saveSegment(`AutoMate : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);

                                if (secondProjectIdList) {
                                    for (const id of secondProjectIdList) {
                                        if (id != undefined && id != '') {
                                            inpObjSecondProject = commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                            portalUpdate.doPayloadAction(inpObjSecondProject);
                                            console.log(`AutoMate Schedule portal call with Second Project Id Failed${id}`);
                                            segment.saveSegment(`AutoMate Schedule portal call with Second Project Id Failed${id}`);

                                        }
                                    }
                                }



                            }
                        } catch (error) {
                            console.log("Error:", error);
                            segment.saveSegment(`AutoMate : doPayloadAction Error - ${JSON.stringify(error)}`);
                        }
                        //code end for portal update for process json failed
                        job.attrs.data.miscExceptionCount = miscExceptionCount;



                        await job.fail(new Error(`Error: ${message}`));
                        done();

                        if (haltIdentifier) {

                            if (message == 'Halt') {
                                displayMessage = `Halted ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                mailTemplateReplacementValues.message = displayMessage;
                                mailTemplateReplacementValues.status = 'Halted';
                                mailTemplateReplacementValues.subject = `Process JSON for ${groupName} - ${storeName} Halted`;
                            }
                        } else {
                            if (haltOverRide) {
                                mailTemplateReplacementValues.resumeUser = resumeUser ? resumeUser : '';
                            }
                            displayMessage = `Failed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Failed';
                            mailTemplateReplacementValues.subject = `Process JSON for ${groupName} - ${storeName} Failed`;
                        }
                        mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                        // Send notification for failed process xml job
                        mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                        mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                        // Send notification for failed process json job
                        segment.saveSegmentFailure(displayMessage, storeCode);
                        await segment.sleep(2000);
                        mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);

                    }

                    console.log(`Call for next job selection`);
                    segment.saveSegment(`Call method for SharePoint data upload`);
                    segment.saveSegment(`Call for next job selection`);
                    var basenameCheck = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                    var distFile = path.join(constants.PROCESS_JSON.AUTOMATE_BUNDLE_DIR, basenameCheck)
                    if (status && fs.existsSync(distFile)) {
                        let rerunFlag;
                        if (doProxyFromPgDump) {
                            rerunFlag = 'RERUN';
                        }
                        basename = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                        if(totalRoCount && totalRoCount!=undefined){
                                updateSolve360Data.totalRoCount = totalRoCount;
                               }else{
                                 updateSolve360Data.totalRoCount = 0;
                              }
                        if(exceptionTypeCounts){
                                updateSolve360Data.exceptionTypeCounts = exceptionTypeCounts;
                        }else{
                                 updateSolve360Data.exceptionTypeCounts = null;
                         }     
                        await distributeFile(basename, rerunFlag, updateSolve360Data, warningObj, job.attrs._id);
                    } else {
                        // Process JSON Job Fail ....
                        segment.saveSegment(`Call for next job selection`);
                        await doNextProcess();
                    }
                });
            } else {
                /**
                * Remove the Initial/recheck schedules
                */
                if (job.attrs.data.operation == "recheck") {
                    job.remove(err => {
                        segment.saveSegment(`Inside Job remove function`);
                        segment.saveSegment(`job: ${JSON.stringify(job)}`);
                        if (!err) {
                            segment.saveSegment(`Job removed successfully`);
                            console.log("Initial/recheck schedule for Automate Process JSON job successfully removed");
                        } else {
                            segment.saveSegment(`Job removal process have error : ${JSON.stringify(err)}`);
                        }
                    });
                }
                done();
                await doNextProcess();
            }
        });

    return agenda;
}

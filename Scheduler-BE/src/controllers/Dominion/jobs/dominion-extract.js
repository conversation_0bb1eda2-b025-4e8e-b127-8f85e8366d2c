"use strict";

const constants = require("../constants");
const util = require("../util");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const DominionJobManager = require("../DominionJobManager");
const { spawn } = require("child_process");
const moment = require("moment-timezone");
const fs = require("fs");
const segment = require("../../SEGMENT/Dominion/segmentManager");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const { v4: uuidv4 } = require('uuid');
const manageScheduleField = require('../../../../src/common/util');


/**
 * Function to find the unique stores in an array of stores
 */
Array.prototype.unique = function () {
    var a = this.concat();
    for (var i = 0; i < a.length; ++i) {
        for (var j = i + 1; j < a.length; ++j) {
            if (a[i].dealerID === a[j].dealerID)
                a.splice(j--, 1);
        }
    }
    return a;
};

/**
 * Function to perform Dominion-Extract
 */
module.exports = function DominionExtractJOB(agenda) {
    var storeCode = '';
    let extractionId
    var dealerID;
    var partyID;
    var groupCode; 
    var uniqueFailLogName;
    console.log(
        `Dominion-Extract job started: JobName: ${constants.DOMINION.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.DOMINION.CONCURRENCY}`
    );
    segment.saveSegment(`Dominion-Extract job started: JobName: ${constants.DOMINION.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.DOMINION.CONCURRENCY}`);
    agenda.define(constants.DOMINION.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.DOMINION.CONCURRENCY },
        async (job, done) => {
            extractionId = job.attrs._id;
            const att = job.attrs.data;
            const storeDataArray = att.storeDataArray.reverse();
            var i = 0;
            var projectId,userName,projectType,secondaryProjectId,secondaryProjectType,inLaborProjectType,inLaborProjectId,inPartsProjectType,inPartsProjectId;
            var solve360Update, buildProxies;
            var warningObj = {};

            let processFileName;
            async function extract(att, job) {
                dealerID = att.dealerID;
                groupCode = att.mageGroupCode;
                storeCode = att.mageStoreCode;
                projectId = att.projectId;
                userName = att.userName;
                projectType = att.projectType || null;              
                secondaryProjectType = att.secondaryProjectType || null;
                secondaryProjectId = att.secondProjectId;
                if (projectType && projectType.toLowerCase().startsWith("labor")) {
                    inLaborProjectType = projectType;
                    inLaborProjectId = projectId;
                } else if (secondaryProjectType && secondaryProjectType.toLowerCase().startsWith("labor")) {
                    inLaborProjectType = secondaryProjectType;
                    inLaborProjectId = secondaryProjectId;
                }
                  
                  // Check if projectType or secondaryProjectType starts with "parts"
                  if (projectType && projectType.toLowerCase().startsWith("parts")) {
                    inPartsProjectType = projectType;
                    inPartsProjectId = projectId;
                  } else if (secondaryProjectType && secondaryProjectType.toLowerCase().startsWith("parts")) {
                    inPartsProjectType = secondaryProjectType;
                    inPartsProjectId = secondaryProjectId;
                  }
                solve360Update = att.solve360Update;
                buildProxies = att.buildProxies;
                uniqueFailLogName = dealerID + '-' + storeCode;
                console.log('projectId', projectId);
                console.log('solve360Update', solve360Update);
                console.log('extractionId:', extractionId)
                segment.saveSegment(`Dominion: Extraction Job Started: ${JSON.stringify(att)}`);
                segment.saveSegmentFailure(`Dominion: Extraction Job Started: ${JSON.stringify(att)}`, uniqueFailLogName);
                if (att.dealerID && att.startDate && att.endDate) {
                    if (!fs.existsSync(constants.DOMINION_DEADLETTER_DIR_PREFIX + '-extracted')) {
                        fs.mkdirSync(constants.DOMINION_DEADLETTER_DIR_PREFIX + '-extracted');
                    }
                    var options = [
                        constants.DOMINION.PULL_OP,
                        constants.DOMINION.OPT_DEALER_ID, att.dealerID,
                        // constants.DOMINION.OPT_PARTY_ID, att.partyID,
                        constants.DOMINION.OPT_START_DATE, att.startDate,
                        constants.DOMINION.OPT_END_DATE, att.endDate,
                        constants.DOMINION.OPT_DEADLETTER_DIR_PREFIX,
                        constants.DOMINION_DEADLETTER_DIR_PREFIX + '-extracted',
                        constants.DOMINION.OPT_MAGE_GROUP_CODE, att.mageGroupCode,
                        constants.DOMINION.OPT_MAGE_STORE_CODE, att.mageStoreCode,
                        '--stateCode', att.stateCode,
                        '--extractionID',extractionId
                    ];
                    
                    options.push(constants.DOMINION.OPT_ZIP_PATH, att.zipPath ? att.zipPath : constants.DOMINION_SCHEDULER_ETI_DIR);
                    options.push(constants.DOMINION.OPT_ZAP_AFTER_ZIP);

                    options.push(constants.DOMINION.OPT_BUNDLE);
                    options.push(att.jobType);
                    segment.saveSegment(`Dominion: Extraction Job Started: ${JSON.stringify(att)}`);
                    segment.saveSegmentFailure(`Dominion: Extraction Job Started: ${JSON.stringify(att)}`, uniqueFailLogName);
                    //Mock Server
                    let isMockServer = constants.DOMINION.ENV_MOCKSERVER; 
                    if(isMockServer == "true"){
                        let groupCode = att.mageGroupCode;
                        let sourceFolder = constants.DOMINION.MOCKSERVER_SOURCE_FOLDER_PATH +'dominion/'; 
                        let destinationFolder = constants.DOMINION.MOCKSERVER_DESTINATION_FOLDER_PATH_DOMINION;
                        //DCDAutomotiveHoldings-CDJR_TIL-NH-INITIAL-SVD012601-20231002115008.zip
                        const mageGroupCodeDIR =  groupCode.replace(/ +/g, "");
                        const mageStorecodeDIR =  storeCode.replace(/ +/g, "");
                        const stateCodeDIR = att.stateCode;
                        const jobTypeDIR = att.jobType ;
                        const enterpriseCodeDir = att.dealerID;
                        const zipFileName = mageGroupCodeDIR +'-'+ mageStorecodeDIR +'-'+ stateCodeDIR +'-'+ jobTypeDIR.toUpperCase(); +'-'+ enterpriseCodeDir +'-';
                        console.log('-------zipFileName-------',zipFileName);
                        
                        fs.readdir(sourceFolder, function (err, files) {
                            if (err) {
                                console.log('DOMINION : Unable to scan directory');
                                segment.saveSegment(`DOMINION : Unable to scan directory`,err);
                            } 
                            const matchedResults = [];
                            let searchResult;
                            files.forEach(function (file) {
                                if(file.includes(zipFileName)) {
                                    matchedResults.push(file);
                                }
                            });
                            console.log('DOMINION : files',matchedResults);
                            if(matchedResults.length > 0) {
                                searchResult = (matchedResults.sort().reverse())[0];
                                att.startTime = new Date(moment().utc());
                                fs.copyFile(sourceFolder+searchResult, destinationFolder+'/'+searchResult, (err) => {
                                    if (err) {
                                        console.log('DOMINION : Unable to copy file');
                                        segment.saveSegment(`DOMINION : Unable to copy file`,err);
                                    } else {
                                        att.endTime = new Date(moment().utc());
                                        att.uniqueId = util.generateUniqueId();
                                        att.status = true;
                                        att.mockServer = true;
                                        att.message = "Success";
                                        let oldStoreArray = job.attrs.data.storeDataArray;
                                        let newStoreArray = [att];
                                        oldStoreArray.map(data => {
                                            if (data.enterpriseCode === newStoreArray[0].enterpriseCode) {
                                                data = newStoreArray;
                                            }
                                        });
                                        var _storeArray = oldStoreArray;
                                        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                                        job.attrs.data.storeDataArray = _storeArray;
                                        job.save();
                                        done();
                                    }
                                });
                            }else{
                            let warningObj={};
                            segment.saveSegment(`DOMINION : Test job failed`);
                            console.log('DOMINION : Test job failed');
                            att.startTime = new Date(moment().utc());
                            att.endTime = new Date(moment().utc());
                            att.uniqueId = util.generateUniqueId();
                            att.status = false;
                            att.mockServer = true;
                            att.message = "Failed";
                            job.fail(new Error(`DOMINION :Test job failed`));
                            job.save()
                            
                            let failureDirectory = process.cwd() + '/logs/Dominion/failure/';
                            let failurelogFile = failureDirectory + storeCode + '.log';
                            let mailTemplateReplacementValues = {
                                dmsType: constants.JOB_TYPE,
                                processTypes: constants.PROCESS_JSON.JOB_NAME,
                                subject: `Test Extraction Job for ${groupCode} - ${storeCode} Failed`,
                                warningObj: warningObj,
                                thirdPartyUsername: att.projectId,
                                storeCode: storeCode,
                                groupCode: groupCode
                            };
                            let mailBody = {
                                fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                                toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                                ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                                attachedfailurelogFile:failurelogFile
                            }
                            var displayMessage = `Test Failed ${constants.JOB_TYPE} ${constants.DOMINION.JOB_NAME} job for group ${groupCode} and store ${storeCode}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Failed';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure('Extraction status: Extraction Failed', storeCode);
                            // Send notification after  cdk extraction job completed
                            mailSender.sendMail(mailBody, constants.DOMINION.JOB_NAME);
                        }
                    });
                    } else {
                    const child = spawn(constants.DOMINION.EXTRACT_CMD, options);
                    var startTime = new Date(moment().utc());
                    att.startTime = startTime;
                    var oldStoreArray = job.attrs.data.storeDataArray;
                    var newStoreArray = [att];
                    oldStoreArray.map(data => {
                        if (data.dealerID === newStoreArray[0].dealerID) {
                            data = newStoreArray;
                        }
                    });
                    var _storeArray = oldStoreArray;
                    // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                    job.attrs.data.storeDataArray = _storeArray;
                    segment.saveSegment(`Extraction Job Data: ${JSON.stringify(_storeArray)}`);
                    segment.saveSegmentFailure(`Extraction Job Data: ${JSON.stringify(_storeArray)}`, uniqueFailLogName);
                    att.uniqueId = util.generateUniqueId();
                    await job.save();
                    process.stdin.pipe(child.stdin)
                    var compareString = "";
                    var status = true;
                    var message = "n/a";
                    var unsubscribedDms = false;

                    child.stdout.on("data", async (data) => {
                        await job.touch();
                        compareString = data.toString('utf8');
                        if (compareString.search("error:") != -1) {
                            message = data.toString('utf8')
                        }
                        console.log(`stdout: ${data}`);
                        data = data.toString('utf8');
                        
                        if (data.includes("Request failed with status code 401")) {
                            unsubscribedDms=true;
                        }
                        if (data.includes("Output zip file successfully generated @path")) {
                                                                                    
                                               console.log("data",data);
                                               const pathMatch = data.match(/@path: (\/.*?\.zip)/);
                                               console.log("pathMatch",pathMatch);
                                               segment.saveSegment(`pathMatch: ${pathMatch}`);
                                              if (pathMatch && pathMatch[1]) 
                                                  {
                                                   const filePath = pathMatch[1];
                                                   const match = filePath.match(/[^/]+\.zip$/);
                                                      if (match) {
                                                          processFileName = match[0];
                                                          
                                                          if(processFileName){
                                                            await manageScheduleField.processCompanyData(job, userName, processFileName, filePath, constants.DMS);
                                                            att.processFileName = processFileName.trim();
                                                            await job.save();
                                                        }else{
                                                            processFileName = null;
                                                        }
                                                                                                  
                                                          console.log("Extracted filename:", processFileName);
                                                                                             
                                                      } else {
                                                                                                
                                                      console.log("Failed to extract filename from path:", filePath);
                                                                                               
                                                  }
                                                   
                                              } else {
                                              
                                                  console.log("Failed to find the file path in the log data");
                                              
                                              }
                                               } else {
                                               console.log("Failed to generate file");
                                             }
                        segment.saveSegment(`stdout: ${data}`);
                        segment.saveSegmentFailure(`stdout: ${data}`, uniqueFailLogName);
                    });

                    child.stderr.on("data", async (data) => {
                        await job.touch();
                        compareString = data.toString('utf8');
                        if (compareString.search("error:") != -1) {
                            message = data.toString('utf8')
                        }
                        console.log(`stderr: ${data}`);
                        segment.saveSegment(`stderr: ${data}`);
                        segment.saveSegmentFailure(`stderr: ${data}`, uniqueFailLogName);
                    });

                    child.on("close", async (code) => {
                        if (code == constants.STATUS_CODE.SUCCESS) {
                            status = true;
                            message = "Success";
                        } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                            status = false;
                            message = "Extraction failed, general death";
                        } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                            status = false;
                            message = "Extraction failed, moved to dead-letter path";
                        }
                        segment.saveSegment(`Job close: ${message}`);
                        segment.saveSegmentFailure(`Job close: ${message}`, uniqueFailLogName);

                        let failureDirectory = process.cwd() + '/logs/Dominion/failure/';
                        let failurelogFile = failureDirectory + uniqueFailLogName + '.log';

                        var groupName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageGroupCode : '';
                        var storeName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageStoreCode : '';

                        var mailTemplateReplacementValues = {
                            dmsType: constants.JOB_TYPE,
                            processTypes: constants.PROCESS_JSON.JOB_NAME,
                            subject: `Extraction Job for ${groupName} - ${storeName} Completed`,
                            warningObj: warningObj,
                            thirdPartyUsername: dealerID,
                            storeCode: storeCode,
                            groupCode: groupCode
                        };
                        var mailBody = {
                            fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                            toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                            ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                            attachedfailurelogFile:failurelogFile
                        }
                        
                        if (status) {
                            // Send notification
                            var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.DOMINION.JOB_EXTRACT_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Success';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure('Extraction status: Extraction completed', uniqueFailLogName);
                            // Send notification after  Dominion extraction job completed
                            mailSender.sendMail(mailBody, constants.DOMINION.JOB_NAME);
                        } else {
                            // Send notification
                            var displayMessage = `Failed ${constants.JOB_TYPE} ${constants.DOMINION.JOB_EXTRACT_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.subject = `Extraction Job for ${groupName} - ${storeName} Failed`;
                            mailTemplateReplacementValues.status = 'Failed';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            if(unsubscribedDms){
                            const UserInput = {
                                inLaborProjectId :inLaborProjectId,
                                inLaborProjectType :inLaborProjectType,
                                inPartsProjectType:inPartsProjectType,
                                inPartsProjectId:inPartsProjectId,                               
                                inIsInSales :true,
                                inSalesComment : appConstants.SALES_TAG_COMMENT,                               
                                inUpdatedBy :userName
                              }
                              segment.saveSegment(`DOMINION Extraction - sales tag creation: ${JSON.stringify(UserInput)}`);
                            try {
                                const SendSalesTagDetails = await manageScheduleField.sendNotificationCall(UserInput,constants.DOMINION.JOB_EXTRACT_NAME);
                                segment.saveSegment(`DOMINION Extraction - sales tag creation: ${JSON.stringify(SendSalesTagDetails)}`);
                              } catch (salesError) {                                
                                segment.saveSegment(`DOMINION Extraction - sales tag creation Error: ${salesError}`);
                              }
                            }
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure('Extraction status: Extraction failed', uniqueFailLogName);
                            // Send notification for failed Dominion extraction
                            await segment.sleep(2000);
                            mailSender.sendMail(mailBody, constants.DOMINION.JOB_NAME);

                            // Portal update for extraction failed
                            let todayDate;
                            let attPayload = {};
                            let projectID;
                            let secondProjectID;
                            let inpObjProject;
                            let inpObjSecondProject;
                            let secondProjectIdList;
                            let projectIdList;
                            try{
                            todayDate = new Date().toISOString().slice(0, 10);
                            attPayload = att;
                            projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                            projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                            secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                            segment.saveSegment(`DOMINION : projectIdList - ${projectIdList}`);
                            segment.saveSegment(`DOMINION : secondProjectIdList - ${secondProjectIdList}`);
                            attPayload['inProjectId'] =  projectID;

                            secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                            attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                            attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                            attPayload.in_retrive_ro_request_on = todayDate;
                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectID, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                            console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                            if(secondProjectIdList.length>0){
                                inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectID, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                            }
                            } catch(err){
                            console.log(JSON.stringify(err));
                            segment.saveSegment(`Dominion : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                            }
                            segment.saveSegment(`Dominion : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                            segment.saveSegment(`Dominion : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                            try {
                                if(secondProjectIdList.length>0){
                                    for(let i=0;i<secondProjectIdList.length;i++){
                                        if(secondProjectIdList[i]!=undefined && secondProjectIdList[i]!= ''){
                                            inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList[i], attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                            console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                                            portalUpdate.doPayloadAction(inpObjSecondProject);
                                        }
                                       
                                    }
                                   
                                }
    
                                if(projectIdList.length>0){
                                    for(let i=0;i<projectIdList.length;i++){
                                        if(projectIdList[i]!=undefined && projectIdList[i]!=''){
                                                  
                                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIdList[i], attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                            segment.saveSegment(`DealerTrack : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                                            portalUpdate.doPayloadAction(inpObjProject);
                                        }
                                 
                                    }
                                   
                                }
                            } catch(error) {
                                console.log("Error:", error);
                                segment.saveSegment(`Dominion : doPayloadAction Error - ${JSON.stringify(error)}`); 
                            }
                            //code end for portal update for extraction failed
                        }

                        att.endTime = new Date(moment().utc());
                        att.uniqueId ? att.uniqueId : util.generateUniqueId();
                        att.status = status;
                        att.message = message;
                        var oldStoreArray = job.attrs.data.storeDataArray;
                        var newStoreArray = [att];
                        oldStoreArray.map(data => {
                            if (data.dealerID === newStoreArray[0].dealerID) {
                                data = newStoreArray;
                            }
                        });
                        var _storeArray = oldStoreArray;
                        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                        job.attrs.data.storeDataArray = _storeArray;
                        // if(processFileName){
                        //     att.processFileName = processFileName.trim();
                        // }else{
                        //     processFileName = null;
                        // }
                        await job.save();
                        console.log(`Dominion : Extraction process for store ${att.mageStoreCode} exited code ${code}`);
                        segment.saveSegment(`Dominion : Extraction process for store ${att.mageStoreCode} exited code ${code}`);
                        i++;
                        if (i < storeDataArray.length) {
                            // Check time frame
                            if (att.runNow) {
                                segment.saveSegment(`Dominion : runNow`);
                                await extract(storeDataArray[i], job);
                            } else if (util.checkExtractTimeFrame()) {
                                segment.saveSegment(`Dominion : Check time frame and start extraction ${JSON.stringify(storeDataArray[i])}`);
                                await extract(storeDataArray[i], job);
                            } else {
                                const newDataArray = storeDataArray;
                                try {
                                    DominionJobManager.scheduleDominionExtractJob(DominionJobManager.createScheduleObject(job, newDataArray.slice(i)), true);
                                    job.attrs.data.storeDataArray = storeDataArray.slice(0, i);
                                    job.fail(new Error(`Dominion : Time exceeded, remaining stores scheduled to next day.`));
                                    segment.saveSegment(`Dominion : Time exceeded, remaining stores scheduled to next day.`);
                                    await job.save();
                                    //done();
                                } catch (error) {
                                    console.error(error);
                                    segment.saveSegment(`Error : ${error.toString()}`);
                                }
                            }
                        } else {
                            done();
                        }
                    });
                }
                } else {
                    console.error("Dominion : Store data Extraction attributes not defined");
                    segment.saveSegment("Dominion : Store data Extraction attributes not defined");
                }
            }
            if (att.runNow) { // Check whether it is for run now or not, if yes, no need to check time frame
                segment.saveSegment(`Dominion : runNow : Check whether it is for run now or not, if yes, no need to check time frame ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else if (util.checkExtractTimeFrame()) {
                segment.saveSegment(`Dominion : Check time frame and start extraction ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else { // Auto schedule full Group wise schedule for tomorrow
                segment.saveSegment(`Dominion : Auto schedule full Group wise schedule for tomorrow`);
                DominionJobManager.scheduleDominionExtractJob(DominionJobManager.createScheduleObject(job), true);
                job.remove();
            }
        });

    agenda.on("start", job => {
        console.log(`Dominion : Job ${job.attrs.name}_${job.attrs._id} starting`);
    });

    agenda.on("complete", job => {
        console.log(`Dominion : Job ${job.attrs.name}_${job.attrs._id} finished`);
    });

    agenda.on("fail", (err, job) => {
        console.log(`Dominion : Job ${job.attrs.name}_${job.attrs._id} failed with error: ${err.message} `);
    });
    return agenda;
}

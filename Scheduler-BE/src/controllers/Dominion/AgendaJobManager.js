const constants = require("./constants");
const segment = require("../SEGMENT/Dominion/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeDominionProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "dominion-process-json") {
      initializeDominionProcessJSON = true
    }
    if (type.split('-')[0] == 'dominion') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the dealerTrack-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeDominionProcessJSON) {
    try {
      // Initialize Dominion Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("Dominion :  Process JSON schedule started");
      segment.saveSegment("Dominion :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("Dominion Extraction Processing Not Enabled - Pass Job Type dealerTrack-process-json To Enable");
    segment.saveSegment("Dominion Extraction Processing Not Enabled - Pass Job Type dealerTrack-process-json To Enable");
  }
  return true;
}

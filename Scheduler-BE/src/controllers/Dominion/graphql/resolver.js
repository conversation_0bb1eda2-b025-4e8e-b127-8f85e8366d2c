const DominionJobManager = require("../DominionJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all Dominion-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllDominionExtractJobs(_, args) {
      return DominionJobManager.getAllDominionExtractJobs();
    },

    /**
     * Query to get all Automate Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllDominionProcessJSONJobs(_, args) {
      return DominionJobManager.getAllDominionProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule Dominion-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleDominionExtractJob(_, args) {
      return DominionJobManager.scheduleDominionExtractJob(args.input);
    },

    /**
     * Mutation to run a Dominion-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowDominionExtractJobByStore(_, args) {
      return DominionJobManager.runNowDominionExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a Dominion-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelDominionExtractJobByStore(_, args) {
      return DominionJobManager.cancelDominionExtractJobByStore(args.input);
    },
  },
  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;
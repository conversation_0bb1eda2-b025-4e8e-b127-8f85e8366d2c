const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");
module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeProcessXML = false;

  jobTypes.forEach(type => {
    if (type == "cdkflex-process-xml") {
      initializeProcessXML = true
    }
    if (type.split('-')[0] == 'cdkflex') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the process-xml job
  // and the rest of the operations will be done by the Job itself.
  if (initializeProcessXML) {
    try {
      // Initialize Process XML job
      await agenda.now(constants.PROCESS_XML.JOB_NAME, { operation: "start" });
      console.log("CDKFLEX : Process XML schedule started");
      segment.saveSegment("CDKFLEX : Process XML schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("CDKFLEX Extraction Processing Not Enabled - Pass Job Type CDKFLEX-process-xml To Enable");
    segment.saveSegment("CDKFLEX Extraction Processing Not Enabled - Pass Job Type CDKFLEX-process-xml To Enable");
  }
  return true;
}

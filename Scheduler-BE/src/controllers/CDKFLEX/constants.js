// Read all env values
const fs = require("fs");
const path = require("path");

/**
 * Configuration file (.env) path
 */
const CDKFLEX_ENV_PATH = path.join(process.env.HOME + "/.cdkflex/.env"); // Hidden folder in user's HOME path

if (!fs.existsSync(CDKFLEX_ENV_PATH)) {
    console.error(`Configuration file ${CDKFLEX_ENV_PATH} not accessible or exist; Please run  ../install script`);
    process.exit(1);
}
const result = require("dotenv").config({ path: CDKFLEX_ENV_PATH });
if (result.error) {
    throw result.error;
}
const TEMP_PATH = path.join(process.env.CDKFLEX_WORK_DIR + "/scheduler-temp");
if (!fs.existsSync(TEMP_PATH)) {
    fs.mkdirSync(TEMP_PATH);
}
if (!fs.existsSync(TEMP_PATH + '/extract')) {
    fs.mkdirSync(TEMP_PATH + '/extract');
}
if (!fs.existsSync(path.join(TEMP_PATH, 'cdkflex-zip-eti'))) {
    fs.mkdirSync(path.join(TEMP_PATH, 'cdkflex-zip-eti'));
}

const env = process.env;

module.exports = {
    JOB_PRIORITY: {
        HIGHEST: "highest",
        HIGH: "high",
        LOW: "low",
        LOWEST: "lowest",
        NORMAL: "normal"
    },

    CDKFLEX_SCHEDULER_ETI_DIR: path.join(TEMP_PATH, 'cdkflex-zip-eti'),
    DEADLETTER_DIR_PREFIX: env.CDKFLEX_DEADLETTER_DIR_PREFIX,
    JOB_TYPE: 'CDKFLEX',
    CORE_RETURN_EXCEPTION_CSV_FILE_PATH: env.CDKFLEX_CORE_RETURN_EXCEPTION_CSV_FILE_PATH,
    CORE_CHARGE_EXCEPTION_CSV_FILE_PATH: env.CDKFLEX_CORE_CHARGE_EXCEPTION_CSV_FILE_PATH,
    CORE_RETURN_NOT_EQUAL_TO_CORE_CHARGE_EXCEPTION_CSV_FILE_PATH: env.CDKFLEX_CORE_RETURN_NOT_EQUAL_TO_CORE_CHARGE_EXCEPTION_CSV_FILE_PATH,
    CORE_CHARGE_WITH_NO_SALE:env.CDKFLEX_CORE_CHARGE_WITH_NO_SALE,
    INVALID_CORE_COST_SALE_MISMATCH: env.CDKFLEX_INVALID_CORE_COST_SALE_MISMATCH,
    INVALID_CORE_AMOUNT_MISMATCH: env.CDKFLEX_INVALID_CORE_AMOUNT_MISMATCH,
    /**
    * CDKFLEX_EXTRACT job related parameters
    */
    CDKFLEX_EXTRACT: {
        JOB_NAME: "CDKFLEX_EXTRACT",
        JOB_TYPE_INITIAL: "initial",
        JOB_TYPE_REFRESH: "refresh",
        JOB_TYPE_ON_DEMAND: "ondemand",
        CONCURRENCY: 20, // Can run upto CONCURRENCY no of stores in parallel
        EXTRACT_CMD: "cdkflex-requestor",
        PULL_OP: "pull",
        OPT_DEALER_ID: "--dealerID",
        OPT_START_DATE: "--startDate",
        OPT_END_DATE: "--endDate",
        OPT_OUTPUT_PATH: "--outputDir",
        OPT_ZIP_PATH: "--zipPath",
        OPT_OPEN: "--open",
        OPT_CLOSED: "--closed",
        OPT_CUSTOMER: "--customer",
        OPT_DELTA_DATE: "--deltaDate",
        OPT_RO_WIP: "--rowip",
        OPT_CLOSED_CRITERIA_MONTHLY: "monthly",
        OPT_CLOSED_CRITERIA_WEEKLY: "weekly",
        OPT_CLOSED_CRITERIA_ALL: "all",
        OPT_CLOSED_CRITERIA_CURRENT: "current",
        OPT_MODEL: "--model",
        OPT_EMPLOYEE: "--employee",
        OPT_CUSTOMER: "--customer",
        OPT_ZAP_AFTER_ZIP: "--zapAfterDist",
        OPT_DEADLETTER_DIR_PREFIX: "--deadLetter",
        OPT_MAGE_GROUP_CODE: "--mageGroupCode",
        OPT_MAGE_STORE_CODE: "--mageStoreCode",
        OPT_BUNDLE: "--bundle",
        HCUST_BULK: "HCUST_Bulk",
        HCUST_DELTA: "HCUST_Delta",
        START_TIME: env.EXTRACT_START_TIME ? env.EXTRACT_START_TIME : "22:00",
        END_TIME: env.EXTRACT_END_TIME ? env.EXTRACT_END_TIME : "4:30",
        TIME_ZONE: env.EXTRACT_ZONE ? env.EXTRACT_ZONE : "America/Chicago",
        EXTRACT_FILE_PATH: TEMP_PATH + "/extract",
        ZIP_PATH: env.EXTRACT_ZIP_PATH,
        POOL_END_CHECKING: 30, // 30 minutes
        POOL_ADD: 1, // 5 minutes
        //For MOCK SERVER ,
        MOCKSERVER_SOURCE_FOLDER_PATH: "/home/<USER>/mock-server/",
        MOCKSERVER_DESTINATION_FOLDER_PATH_CDKFLEX: "/home/<USER>/tmp/du-etl-dms-cdkflex-extractor-work/scheduler-temp/cdkflex-zip-eti/",
        ENV_MOCKSERVER: env.ENV_MOCKSERVER
    },
    SCHEDULE: {
        SUCCESS: "Job schedule saved successfully",
        OVERRIDE_SUCCESS: "Job schedule updated successfully",
        CANCEL_EXISTING: "Already existing schedule canceled and new ",
        STARTED: "Job started",
        CANCELED: "Job canceled",
        NO_MATCHING: "No matching job found"
    },
    PROCESS_XML: {
        JOB_NAME: "CDKFLEX_PROCESS_XML",
        CONCURRENCY: 1, // Can run upto CONCURRENCY no of Process XML jobs parallel,
        PROCESS_CMD: "process-xml",
        PROCESS_CMD_PATH: process.env.DU_ETL_HOME + "/DU-DMS/DMS-CDKFLEX/Processor-Application",
        DISTRIBUTE_CMD_PATH: process.env.DU_ETL_HOME + "/DU-DMS/DMS-CDKFLEX",
        PORSCHE_STORE_LABEL:"PORSCHE",
        OPT_CLOSED: "--closed",
        OPT_INPUT_DIR: "--input-dir",
        OPT_INPUT_ZIP: "--input-zip",
        OPT_BUNDLE_DIR: "--bundle-dir",
        OPT_BUNDLE_ID: "--bundle-id",
        OPT_PERFORM_ZIP: "--zip",
        OPT_PERFORM_NO_ZIP: "--no-zip",
        OPT_ZAP_INPUT: "--zap-input",
        OPT_BUILD_PROXY_RO: "--build-proxies",
        OPT_NO_BUILD_PROXY_RO: "--no-build-proxies",
        OPT_BUILD_PROXY_USING: "--build-proxies-using",
        SHOW_ACCOUNTING_IN_PROXY: "--show-accounting-in-proxy",
        HIDE_ACCOUNTING_IN_PROXY: "--hide-accounting-in-proxy",
        OPT_PAY_TYPE_FILTER: "--pay-type-filter",
        OPT_PORSCHE_STORE:"--porsche-store",
        TIME_GAP: "after 60 seconds",
        BUNDLE_DIR: process.env.CDKFLEX_BUNDLE_DIR,
        DIST_DIR: process.env.CDKFLEX_DIST_DIR,
        ETL_DIR: process.env.CDKFLEX_ETL_DIR,
        OPT_DEADLETTER_DIR_PREFIX: "--dead-letter",
        OUTPUT_PREFIX:"--output-prefix",
        HALT_OVER_RIDE:"--halt-over-ride",
        OUTPUT_PREFIX_VAL: process.env.CDKFLEX_PROCESS_XML_OUTPUT_DIR_PREFIX,
        REPLACE_STRING: {
            FROM:'.zip',
            TO:'-ETL.zip',
            DO_PROXY_FROM:'-RERUN',
            DO_PROXY_TO:'-DO-PROXY-ETL',
            DIR_PREFIX:'PROC-',
            CSV:'.csv'
        },
        ERROR_CHECKING_LABELS: {
            ZIP_FILE_EXIST_CHECK: 'Zip File Must Exist',
            ZIP_FILE_PROCESSING_FAILED: 'Zip File Processing Failed',
            PROCESSOR_HALT:'Process is HALT!',
            PROCESSOR_DEAD:'Process is DEAD!',
        }
    },
    DO_PROXY_MSG:{
        COPY_FILE: 'Error while copying the file!',
        FILE_MISSING:'Source file missing',
        FAILED_TO_CREATE_JOB:'Error while do the Process XML Job!',
        JOB_CREATION_SUCCESS:'Job Created Successfully'
    },
    STATUS_CODE: {
        SUCCESS: 0,
        GENERAL_DEATH: 1,
        DEADLETTER_PATH: 2
    }
}

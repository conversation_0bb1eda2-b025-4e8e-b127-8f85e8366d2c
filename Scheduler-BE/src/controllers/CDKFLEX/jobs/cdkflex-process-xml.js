"use strict";

const constants = require("../constants");
const util = require("../util");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const { spawn } = require("child_process");
const path = require('path');
const fs = require("fs");
const moment = require("moment-timezone");
const segment = require("../../SEGMENT/CDKFLEX/segmentManager");
const sharePoint = require("../../../routes/sharePoint");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const stripAnsi = require('strip-ansi');
const extractionError = require('../../../common/extractionError');

var Agenda = require("../../agenda");
const csv=require('csvtojson');
/**
 * Function to perform processing of XML file downloaded through CDKFLEX-Extract job
 */
module.exports = async function ProcessXmlJOB(agenda) {

    var distributeFile = async function (fileName, rerunFlag, updateSolve360Data, warningObj) {
        var stdErrorArray;
        var distDir = path.join(process.env.CDKFLEX_DIST_DIR, fileName);
        var etlDir = path.join(process.env.CDKFLEX_ETL_DIR, fileName);
        etlDir = etlDir.replace(constants.PROCESS_XML.REPLACE_STRING.FROM, constants.PROCESS_XML.REPLACE_STRING.TO);
        var filePath = path.join(process.env.CDKFLEX_BUNDLE_DIR, fileName);
        const distributeFile = spawn("bash",
            [
                'send-bundle-live-hpdog', filePath, rerunFlag
            ], {
            cwd: constants.PROCESS_XML.DISTRIBUTE_CMD_PATH,
            env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
        }).on('error', function (err) {
            console.log("error :", err);
            segment.saveSegment(`error: ${err}`);
        });
        console.log(`Start processing of distribution`);
        segment.saveSegment(`Start processing distribution`);
        process.stdin.pipe(distributeFile.stdin);
        distributeFile.stdout.on("data", async (data) => {
            console.log(`stdout: ${data}`);
            segment.saveSegment(`stdout: ${data}`);
        });

        distributeFile.stderr.on("data", async (data) => {
            console.log(`stderr: ${data}`);
            stdErrorArray += data.toString() + ' ';
            segment.saveSegment(`stderr: ${data}`);
        });

        distributeFile.on("close", async (code) => {
            var message = "n/a";
            var status = false;
            if (code == constants.STATUS_CODE.SUCCESS) {
                status = true;
                message = "Success";
            } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                message = "Distribution failed, general death";
            } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                message = "Distribution failed";
            }
            segment.saveSegment(`close: ${message}`);
            if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_XML.ERROR_CHECKING_LABELS.ZIP_FILE_EXIST_CHECK)) {
                status = false;
                message = "Distribution failed. Zip File Must Exist";
                segment.saveSegment(message);
                }
            /**
              * Upload files to SharePoint
              */
            if (status) {
                sharePoint.initSharePoint(distDir, constants.JOB_TYPE, rerunFlag, updateSolve360Data, warningObj, 0);//Upload dist directory zip file to sharepoint
            }
            await doNextProcess();
        });
    }

    var doNextProcess = async function () {
        await segment.sleep(5000);
        segment.saveSegment(`Call for findOldestZipFile method to check if any stores remaining to process`);
        const extractZip = await util.findOldestZipFile(constants.CDKFLEX_SCHEDULER_ETI_DIR);
        if (extractZip) {
            console.log(`Found one Store extraction > ${extractZip} to process now`);
            segment.saveSegment(`Found one Store extraction > ${extractZip} to process now`);
            try {
                var createdAt = extractZip.slice(0, extractZip.length - 4).split("-").reverse()[0];
                var storeIdArrayPos = 0;
                if (extractZip.includes(constants.PROCESS_XML.REPLACE_STRING.DO_PROXY_FROM)) {
                    storeIdArrayPos = 1;
                }
                var createdAt = extractZip.slice(0, extractZip.length - 4).split("-").reverse()[storeIdArrayPos];
                await agenda.now(constants.PROCESS_XML.JOB_NAME, {
                    inputFile: extractZip,
                    createdAt: createdAt,
                    operation: "xml-processing"
                });
                console.log(`Process XML schedule started with file > ${extractZip}`);
                segment.saveSegment(`Process XML schedule started with file > ${extractZip}`);
            } catch (error) {
                console.error(error);
            }

        } else {
            console.log(`CDKFLEX: No Store's zip file to process now, will check ${constants.PROCESS_XML.TIME_GAP}`);
            //segment.saveSegment(`CDKFLEX: No Store's zip file to process now, will check ${constants.PROCESS_XML.TIME_GAP}`);
            try {
                await agenda.schedule(`${constants.PROCESS_XML.TIME_GAP}`, constants.PROCESS_XML.JOB_NAME, { operation: "recheck" });
                console.log(`CDKFLEX: Process XML schedule will run ${constants.PROCESS_XML.TIME_GAP}`);
                //segment.saveSegment(`CDKFLEX: Process XML schedule will run ${constants.PROCESS_XML.TIME_GAP}`);
            } catch (error) {
                console.error(error);
            }
        }
    }

    console.log(
        `Process XML job started: JobName: ${constants.PROCESS_XML.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_XML.CONCURRENCY}`
    );
    segment.saveSegment(`Process XML job started: JobName: ${constants.PROCESS_XML.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_XML.CONCURRENCY}`);

    agenda.define(constants.PROCESS_XML.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.PROCESS_XML.CONCURRENCY },
        async (job, done) => {
            const att = job.attrs.data;
            var extractZip = null;
            var stdErrorArray = [];
            var stdOutArray = [];
            var inpFile = att.inputFile ? path.join(constants.CDKFLEX_SCHEDULER_ETI_DIR, att.inputFile) : '';
            if (att.inputFile) {
                if (fs.existsSync(inpFile)) {
                    if (!fs.existsSync(constants.DEADLETTER_DIR_PREFIX + '-processed')) {
                        fs.mkdirSync(constants.DEADLETTER_DIR_PREFIX + '-processed');
                    }
                    extractZip = att.inputFile;
                    let basename = path.basename(extractZip);
                    var doProxyFromPgDump = false;
                    let dealerId;
                    // if (basename.includes(constants.PROCESS_XML.REPLACE_STRING.DO_PROXY_TO)) {
                    if (basename.includes(constants.PROCESS_XML.REPLACE_STRING.DO_PROXY_FROM)) {
                        doProxyFromPgDump = true;
                        dealerId = basename.split("-").reverse()[2];
                    } else {
                        dealerId = basename.split("-").reverse()[1];
                        doProxyFromPgDump = false;
                    }
                    job.attrs.data.storeID = !doProxyFromPgDump ? basename.split("-").reverse()[1] : basename.split("-").reverse()[2];
                    let storeName = job.attrs.data.storeID;
                   
                    let mageGroupCode = basename.split("-")[0];
                    let mageStoreCode =  basename.split("-")[1];
                    let storeCode = dealerId + '-' + mageStoreCode;

                    console.log('DealerId:', dealerId);
                    segment.saveSegment(`DealerId : ${dealerId}`);
                    console.log('Groupname:', mageGroupCode);
                    segment.saveSegment(`Groupname : ${mageGroupCode}`);
                    console.log('storeName:',mageStoreCode);
                    segment.saveSegment(`storeName : ${mageStoreCode}`);

                    let jobsTmp = await Agenda.jobs( {
                        $and: [
                            { "data.storeDataArray.dealerId": dealerId },
                            { "name": constants.CDKFLEX_EXTRACT.JOB_NAME },
                            {"data.storeDataArray.mageStoreCode":mageStoreCode} 
                        ]
                    });
                    
                    let projectId = '';
                    let secondProjectId = '';
                    let userName = '';
                    let solve360Update = '';
                    let updateSolve360Data; 
                    let buildProxies;
                    let includeMetaData;
                    let extractionId;
                    let showAccountingInProxy;

                    let haltIdentifier = false;
                    let haltOverRide = false;

                    let resumeUser;
                    let mageManufacturer;
                    let isPorscheStore;
                   

                    let agendaObject;
                    let extractedFileTimeStamp;
                    let extractedFileCreationDate;
                    let extractedObjectIndex;

                    let projectIds;
                    let secondProjectIdList;
                    let uniqueId;
                    let companyIds;

                    try{
                        extractedFileTimeStamp = basename.split("-").reverse()[0].replace(".zip", "");
                        segment.saveSegment(`extractedFileTimeStamp : ${extractedFileTimeStamp}`);
                        extractedFileCreationDate =  moment(extractedFileTimeStamp, "YYYYMMDDhhmmss").format("YYYY-MM-DD");
                        segment.saveSegment(`extractedFileCreationDate : ${extractedFileCreationDate}`);
                    } catch(err){
                      console.log(err);
                      segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                    }
                    
                    segment.saveSegment(`jobsTmp : ${JSON.stringify(jobsTmp)}`);
                    segment.saveSegment(`jobsTmp[0] : ${JSON.stringify(jobsTmp[0])}`);
                    segment.saveSegment(`Dealer ID : ${dealerId}`);

                    if(jobsTmp[0]){
                        if(jobsTmp[0].hasOwnProperty("attrs")){
                            extractionId = jobsTmp[0].attrs._id;
                            try{
                                segment.saveSegment(`jobsTmp[0].attrs.data.storeDataArray : ${JSON.stringify(jobsTmp[0].attrs.data.storeDataArray)}`);
                                agendaObject = jobsTmp[0].attrs.data.storeDataArray;
                                agendaObject = agendaObject.filter(function (el) {
                                    return el.dealerId == dealerId && el.mageStoreCode == mageStoreCode;
                                });
                                segment.saveSegment(`agendaObject : ${JSON.stringify(agendaObject)}`);

                                extractedObjectIndex = 0;
                                if(agendaObject.length > 0){ 
                                    agendaObject =  agendaObject.sort((a,b) => b.endTime > a.endTime);
                                    extractedObjectIndex = agendaObject.findIndex(
                                        obj => moment(obj.endTime, "YYYY-MM-DDTHH:mm:ss.SSS[Z]").format("YYYY-MM-DD") == extractedFileCreationDate
                                    );
                                }

                                if(extractedObjectIndex < 0){
                                    extractedObjectIndex = 0;
                                }
                                
                                segment.saveSegment(`Sorted agenda object : ${JSON.stringify(agendaObject)}`);
                                segment.saveSegment(`extractedObjectIndex : ${extractedObjectIndex}`);
                                segment.saveSegment(`Extracted agenda object : ${JSON.stringify(agendaObject[extractedObjectIndex])}`);


                                if(agendaObject[extractedObjectIndex].hasOwnProperty("projectId")){
                                    projectId = agendaObject[extractedObjectIndex].projectId;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectId")){
                                    secondProjectId = agendaObject[extractedObjectIndex].secondProjectId;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("solve360Update")){
                                    solve360Update = agendaObject[extractedObjectIndex].solve360Update;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("buildProxies")){
                                    buildProxies = agendaObject[extractedObjectIndex].buildProxies;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("includeMetaData")){
                                    includeMetaData = agendaObject[extractedObjectIndex].includeMetaData;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("userName")){
                                    userName = agendaObject[extractedObjectIndex].userName;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("extractAccountingData")){
                                    showAccountingInProxy = agendaObject[extractedObjectIndex].extractAccountingData;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("mageManufacturer")){
                                    mageManufacturer = agendaObject[extractedObjectIndex].mageManufacturer;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("projectIds")){
                                    projectIds = agendaObject[extractedObjectIndex].projectIds;
                                    projectIds =  projectIds.split("*");
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectIdList")){
                                    secondProjectIdList = agendaObject[extractedObjectIndex].secondProjectIdList;
                                    secondProjectIdList = secondProjectIdList.split("*");
                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("uniqueId")){
                                    uniqueId = agendaObject[extractedObjectIndex].uniqueId;
                                    uniqueId+='-'+Date.now();
                               
                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("companyIds")){
                                    console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                                    companyIds = agendaObject[extractedObjectIndex].companyIds;
                                    companyIds =  companyIds.replace(new RegExp('\\*', 'g'), ',')
                                    console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",companyIds);
                                    // companyIds = companyIds.replace(/,\s*$/, "");
                                }
                                

                                if(mageManufacturer == constants.PORSCHE_STORE_LABEL){
                                    isPorscheStore = true;
                                } else{
                                    isPorscheStore = false; 
                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("haltOverRide")){
                                    haltOverRide = agendaObject[extractedObjectIndex].haltOverRide;
                                }

                            } catch(err){
                                console.log(err);
                                segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                            }
                        }
                    }
        
                    console.log('projectId:', projectId);
                    segment.saveSegment(`projectId : ${projectId}`);
                    
                    console.log('secondProjectId:', secondProjectId);
                    segment.saveSegment(`secondProjectId : ${secondProjectId}`);

                    console.log('userName:', userName);
                    segment.saveSegment(`userName : ${userName}`);

                    console.log('solve360Update:', solve360Update);
                    segment.saveSegment(`solve360Update : ${solve360Update}`);

                    console.log('buildProxies:', buildProxies);
                    segment.saveSegment(`buildProxies : ${buildProxies}`);

                    console.log('includeMetaData:', includeMetaData);
                    segment.saveSegment(`includeMetaData : ${includeMetaData}`);

                    console.log('showAccountingInProxy:', showAccountingInProxy);
                    segment.saveSegment(`showAccountingInProxy : ${showAccountingInProxy}`);

                    console.log('extractionId:', extractionId);
                    segment.saveSegment(`extractionId : ${extractionId}`);

                    console.log('haltOverRide:', haltOverRide);
                    segment.saveSegment(`haltOverRide : ${haltOverRide}`);

                    console.log('projectIds:', projectIds);
                    segment.saveSegment(`projectIds : ${projectIds}`);

                    console.log('secondProjectIdList:', secondProjectIdList);
                    segment.saveSegment(`secondProjectIdList : ${secondProjectIdList}`);

                    console.log('uniqueId:', uniqueId);
                    segment.saveSegment(`uniqueId : ${uniqueId}`);

                    console.log('companyIds:', companyIds);
                    segment.saveSegment(`companyIds : ${companyIds}`);

                    if(haltOverRide && !doProxyFromPgDump){
                        resumeUser = `${userName}`;
                        console.log('resumeUser:', resumeUser);
                        segment.saveSegment(`resumeUser : ${resumeUser}`);
                    }

                    updateSolve360Data = {projectId:projectId, secondProjectId:secondProjectId, userName:userName, solve360Update:solve360Update, thirdPartyUsername:dealerId, storeCode: mageStoreCode, dmsType: constants.JOB_TYPE, groupCode:mageGroupCode, resumeUser: resumeUser ? resumeUser : '',projectIds:projectIds,secondProjectIdList:secondProjectIdList,uniqueId:uniqueId};

                    segment.saveSegment(`updateSolve360Data : ${JSON.stringify(updateSolve360Data)}`);
                    console.log(updateSolve360Data);

                    let buildProxiesDecider;
                    if(buildProxies || doProxyFromPgDump){
                        buildProxiesDecider = constants.PROCESS_XML.OPT_BUILD_PROXY_RO; 
                    } else{
                        buildProxiesDecider = constants.PROCESS_XML.OPT_NO_BUILD_PROXY_RO;
                    }

                    let accountingProxyDecider;
                    if(showAccountingInProxy){
                        accountingProxyDecider = constants.PROCESS_XML.SHOW_ACCOUNTING_IN_PROXY; 
                    } else{
                        accountingProxyDecider = constants.PROCESS_XML.HIDE_ACCOUNTING_IN_PROXY;
                    }
                    segment.saveSegment(`accountingProxyDecider : ${accountingProxyDecider}`);

                    if(mageManufacturer == constants.PROCESS_XML.PORSCHE_STORE_LABEL){
                        isPorscheStore = true;
                    } else{
                        isPorscheStore = false; 
                    }

                    console.log('mageManufacturer:', mageManufacturer);
                    segment.saveSegment(`mageManufacturer : ${mageManufacturer}`);

                    console.log('isPorscheStore:', isPorscheStore);
                    segment.saveSegment(`isPorscheStore : ${isPorscheStore}`);


                    await job.save();
                    if (doProxyFromPgDump) {
                        var payTypeFilterFileName = basename.replace(constants.PROCESS_XML.REPLACE_STRING.FROM, constants.PROCESS_XML.REPLACE_STRING.CSV);
                        var searchFile = '';
                        var inpStr = payTypeFilterFileName.split('/').reverse();
                        var inpStrSplit = inpStr[0].split(storeName);
                        searchFile = `${inpStrSplit[0]}${storeName}`;
                        const payTypeFileNameBase = await util.findPayTypeCsvFile(constants.PROCESS_XML.DIST_DIR, searchFile);
                        let payTypeFileName = extractZip.replace(constants.PROCESS_XML.REPLACE_STRING.DIR_PREFIX, '');
                        payTypeFileName = extractZip.replace(constants.PROCESS_XML.REPLACE_STRING.FROM, constants.PROCESS_XML.REPLACE_STRING.CSV);

                        if (fs.existsSync(payTypeFileName)) {
                            console.log("File exist");
                            payTypeFileName = payTypeFileName;
                        } else {
                            console.log("File not exist");
                            payTypeFileName = payTypeFileNameBase;
                        }
                        console.log("payTypeFileNameBase", payTypeFileNameBase);
                        console.log("payTypeFileName", payTypeFileName);
                        //basename = basename.replace(constants.PROCESS_XML.REPLACE_STRING.DO_PROXY_FROM, '');
                      spawnInputArray = [
                            constants.PROCESS_XML.PROCESS_CMD,
                            constants.PROCESS_XML.OPT_BUNDLE_DIR, constants.PROCESS_XML.BUNDLE_DIR,
                            constants.PROCESS_XML.OPT_BUILD_PROXY_USING, path.join(constants.CDKFLEX_SCHEDULER_ETI_DIR, extractZip),
                            constants.PROCESS_XML.OPT_PAY_TYPE_FILTER, path.join('/home/<USER>/tmp/du-etl-dms-cdkflex-extractor-work/manual/dist/', payTypeFileName),
                            
                            constants.PROCESS_XML.OPT_ZAP_INPUT,
                            constants.PROCESS_XML.OPT_DEADLETTER_DIR_PREFIX, constants.DEADLETTER_DIR_PREFIX + '-processed',
                            constants.PROCESS_XML.OUTPUT_PREFIX, constants.PROCESS_XML.OUTPUT_PREFIX_VAL,
                            accountingProxyDecider,
                            "--uuid",uniqueId,
                            "--company_ids",companyIds
                        ];
                    } else {

                        if (haltOverRide) {
                            resumeUser = `Resume by ${userName}`;
                            // Portal update for process xml Resume
                            let todayDate;
                            let attPayload = {};
                            let projectID;
                            let secondProjectID;
                            let inpObjProject;
                            let inpObjSecondProject;
                            try {
                              todayDate = new Date().toISOString().slice(0, 10);
                              attPayload = agendaObject[extractedObjectIndex];
                              projectID = attPayload.hasOwnProperty("projectId")
                                ? attPayload.projectId
                                : "";
                              attPayload["inProjectId"] = projectID;
                          
                              secondProjectID = attPayload.hasOwnProperty("secondProjectId")
                                ? attPayload.secondProjectId
                                : "";
                              attPayload.in_is_update_retrieve_ro = attPayload.hasOwnProperty(
                                "solve360Update"
                              )
                                ? attPayload.solve360Update
                                : "";
                          
                              attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;
                              attPayload.in_retrive_ro_request_on = todayDate;
                              inpObjProject = commonUtil.getinpObjFordoPayloadAction(
                                projectID,
                                attPayload,
                                todayDate,
                                constantsCommon.PAYLOAD_IN_ACTION.RESUME
                              );
                              console.log(inpObjProject, "******** INP OBJJJJJ ***********");
                              if (secondProjectID) {
                                inpObjSecondProject = commonUtil.getinpObjFordoPayloadAction(
                                  secondProjectID,
                                  attPayload,
                                  todayDate,
                                  constantsCommon.PAYLOAD_IN_ACTION.RESUME
                                );
                                console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********");
                              }
                            } catch (err) {
                              console.log(JSON.stringify(err));
                              segment.saveSegment(
                                `CDKFLEX : doPayloadAction inpObj Error - ${JSON.stringify(err)}`
                              );
                            }
                            segment.saveSegment(
                              `CDKFLEX : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`
                            );
                            segment.saveSegment(
                              `CDKFLEX : doPayloadAction inpObjSecondProject - ${JSON.stringify(
                                inpObjSecondProject
                              )}`
                            );
                          
                            try {
                              segment.saveSegment(
                                `CDKFLEX : doPayloadAction - ${JSON.stringify(inpObjProject)}`
                              );
                              

                              let  projectIdList =  inpObjProject.inProjectId.split("*");
                              if(projectIdList){
                                   for(const id of projectIdList){
                                      if(id){
                                          inpObjProject.inProjectId = id;
                                          portalUpdate.doPayloadAction(inpObjProject);
                                          console.log(`CDKFLEX Schedule portal call with Project Id FAILURE${id}`);
                  
                                      }
                                   }
                              } 


                             if (secondProjectID) {
                                 segment.saveSegment(
                                  `CDKFLEX : doPayloadAction for secondProjectID - ${JSON.stringify(
                                    inpObjSecondProject
                                  )}`
                                );

                                let parsedData = JSON.parse(inpObjSecondProject.inData);
                                let secondProjectIdList =  parsedData.secondProjectIdList.split("*");
                                if(secondProjectIdList){
                                    for(const id of secondProjectIdList){
                                       if(id){
                                           inpObjSecondProject.inProjectId = id;
                                           portalUpdate.doPayloadAction(inpObjSecondProject);
    
                                           console.log(`CDKFLEX Schedule portal call with Second Project Id FAILURE${id}`);
                   
                                       }
                                    }
                               } 

                              }
                            } catch (error) {
                              console.log("Error:", error);
                              segment.saveSegment(
                                `CDKFLEX : doPayloadAction Error - ${JSON.stringify(error)}`
                              );
                            }
                            //code end for portal update for process xml Resume
                        }



                          
                        var spawnInputArray = [
                            constants.PROCESS_XML.PROCESS_CMD,
                            constants.PROCESS_XML.OPT_BUNDLE_DIR, constants.PROCESS_XML.BUNDLE_DIR,
                            constants.PROCESS_XML.OPT_INPUT_ZIP, path.join(constants.CDKFLEX_SCHEDULER_ETI_DIR, extractZip),
                            constants.PROCESS_XML.OPT_ZAP_INPUT,
                            constants.PROCESS_XML.OPT_PERFORM_ZIP,
                            buildProxiesDecider,
                            // constants.PROCESS_XML.OPT_BUILD_PROXY_RO,
                            constants.PROCESS_XML.OPT_DEADLETTER_DIR_PREFIX,
                            constants.DEADLETTER_DIR_PREFIX + '-processed',
                            constants.PROCESS_XML.OUTPUT_PREFIX,
                            constants.PROCESS_XML.OUTPUT_PREFIX_VAL,
                            accountingProxyDecider,
                            constants.PROCESS_XML.HALT_OVER_RIDE,
                            haltOverRide,
                            "--uuid",uniqueId,
                            "--performed-by",userName,
                            "--exception-report",true,
                            "--company_ids",companyIds
                        ];
                    }


                    if (isPorscheStore){
                        spawnInputArray.push(constants.PROCESS_XML.OPT_PORSCHE_STORE)
                    }

                    const processXml = spawn("bash",
                        spawnInputArray, {
                        cwd: constants.PROCESS_XML.PROCESS_CMD_PATH,
                        env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
                    }).on('error', function (err) {
                        console.log("error ::", err);
                        segment.saveSegment(`error: ${err}`);
                    });
                    console.log(`Start processing of extraction > ${basename}`);
                    segment.saveSegment(`Start processing of extraction > ${basename}`);
                    segment.saveSegmentFailure(`Start processing of extraction > ${basename}`, storeCode);
                    process.stdin.pipe(processXml.stdin);

                    processXml.stdout.on("data", async (data) => {
                        console.log(`stdout: ${data}`);
                        stdOutArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                        await job.touch();
                        segment.saveSegment(`stdout: ${data}`);
                        segment.saveSegmentFailure(`stdout: ${data}`, storeCode);
                    });
                    processXml.stderr.on("data", async (data) => {
                        console.log(`stderr: ${data}`);
                        stdErrorArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                        await job.touch();
                        segment.saveSegment(`stderr: ${data}`);
                        segment.saveSegmentFailure(`stderr: ${data}`, storeCode);

                        try{
                            if(!data.includes('Beginning zip processing in') && data){
                                if (fs.existsSync(path.join(constants.CDKFLEX_SCHEDULER_ETI_DIR, extractZip))) {
                                    fs.copyFile(path.join(constants.CDKFLEX_SCHEDULER_ETI_DIR, extractZip), deadLetterPath+ "/" + basename, (err) => {
                                        if (err) {
                                            console.log(err);
                                            segment.saveSegment(`Error in input file to dead letter: ${err}`);
                                            segment.saveSegmentFailure(`Error in input file to dead letter: ${err}`, storeCode);
                                        }
                                        console.log(`${ path.join(constants.CDKFLEX_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`);
                                        segment.saveSegment(`${ path.join(constants.CDKFLEX_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`);
                                        segment.saveSegmentFailure(`${ path.join(constants.CDKFLEX_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`, storeCode);
                                    });
                    
                                    // fs.unlink(path.join(constants.CDKFLEX_SCHEDULER_ETI_DIR, extractZip), function (err) {
                                    //     if (err){
                                    //         console.log(err);
                                    //         segment.saveSegment(`Error in delete input file : ${err}`);
                                    //         segment.saveSegmentFailure(`Error in delete input file : ${err}`, storeCode);
                                    //     } 
                                    // });
                                }
                            }
                        } catch(err){
                            console.log(err)
                        }

                    });

                    processXml.on("close", async (code) => {
                        var message = "n/a";
                        var status = false;
                        if (code == constants.STATUS_CODE.SUCCESS) {
                            status = true;
                            message = "Success";
                        } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                            message = "Extraction failed, general death";
                            await job.fail(new Error(`Error: ${message}`));
                        } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                            message = "Extraction failed, moved to dead-letter path";
                            await job.fail(new Error(`Error: ${message}`));
                        }
                        if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_XML.ERROR_CHECKING_LABELS.ZIP_FILE_PROCESSING_FAILED)) {
                            message = "Extraction failed, Zip File Processing Failed,  ";
                            status = false;
                        }
                        var deadLetterPath = `${process.env.CDKFLEX_WORK_DIR}/dead-letter-processed`;
                        var errResp = `Moving input to dead-letter bin: ${deadLetterPath}`;
                        if (stdOutArray && stdOutArray.includes(errResp)) {
                            message += errResp
                            status = false;
                        }

                        /////////////////////////////Code for Halt and Resume Processor/////////////////////////////////////////////////////////////////////////////

                        console.log(stdErrorArray);
                        if (stdErrorArray && !haltOverRide) {

                            console.log(constants.PROCESS_XML.ERROR_CHECKING_LABELS.PROCESSOR_HALT);
                            console.log(constants.PROCESS_XML.ERROR_CHECKING_LABELS.PROCESSOR_DEAD);

                            stdErrorArray.forEach((v, i) => {
                                if (
                                    v.includes(constants.PROCESS_XML.ERROR_CHECKING_LABELS.PROCESSOR_HALT)
                                ) {
                                    message = "Halt";
                                    haltIdentifier = true;
                                    status = false;
                                }

                                if (
                                    v.includes(constants.PROCESS_XML.ERROR_CHECKING_LABELS.PROCESSOR_DEAD)
                                ) {
                                    message = "Dead";
                                    haltIdentifier = true;
                                    status = false;
                                }


                            });

                            try {
                                if (
                                    stdOutArray &&
                                    stdOutArray.includes(errResp) &&
                                    message == "Halt" &&
                                    !haltOverRide
                                ) {
                                    let deadLetterFilePath = deadLetterPath + "/" + basename;
                                    let haltFilePath =
                                        "/home/<USER>/tmp/du-etl-dms-cdkflex-extractor-work/scheduler-temp/halt/" +
                                        basename;
                                    if (fs.existsSync(deadLetterFilePath)) {
                                        fs.copyFile(deadLetterFilePath, haltFilePath, (err) => {
                                            if (err) throw err;
                                            console.log(`${deadLetterFilePath} was copied to ${haltFilePath}`);
                                        });
                                    } else {
                                        console.log(`${deadLetterFilePath} not exist!`);
                                    }
                                } else{
                                    console.log('Not a Halt process')
                                }
                            } catch (err) {
                                console.log(err);
                            }
                        }

                        /////////////////////////////Code for Halt and Resume Processor/////////////////////////////////////////////////////////////////////////////



                        console.log(`XML processing job: ${message}`);
                        segment.saveSegment(`XML processing job: ${message}`);
                        segment.saveSegmentFailure(`XML processing job: ${message}`, storeCode);
                        console.log(`XML processing job for Store ${storeName} exited with code ${code}`);
                        segment.saveSegment(`XML processing job for Store ${storeName} exited with code ${code}`);
                        segment.saveSegmentFailure(`XML processing job for Store ${storeName} exited with code ${code}`, storeCode);
                        // job.attrs.data.outputFile = path.join(constants.PROCESS_XML.BUNDLE_DIR, basename);
                        
                        let failureDirectory = process.cwd() + '/logs/CDKFLEX/failure/';
                        let failurelogFile = failureDirectory + storeCode + '.log'; 
                        
                        var extractionErrorResponse;
                        var errorWarnningMessage;
                        var warningObj = {};
                        warningObj.scheduled_by = userName;

                        //Code for fetch extraction error from mongodb
                        try{
                            if(extractionId){
                                let uniqueExtractionId = extractionId+dealerId;
    
                                segment.saveSegment(`uniqueExtractionId: ${uniqueExtractionId}`);
                                segment.saveSegmentFailure(`uniqueExtractionId: ${uniqueExtractionId}`);

                                extractionErrorResponse = await extractionError.displayErrorLogWithSpecific(uniqueExtractionId, 'CDKFLEX');
                                
                                if(extractionErrorResponse.status){
                                    let resp = JSON.parse(JSON.stringify(extractionErrorResponse.response))
                                    let tmpDescritionArray = [];
                                    resp.forEach(e => {
                                        tmpDescritionArray.push(e.description);
                                        // tmpDescritionArray.push(e.ro_pull_type+" : "+e.description);
                                    });
                                    // errorWarnningMessage = tmpDescritionArray.join(", ");
                                    errorWarnningMessage = tmpDescritionArray;
                                }
                            }
                            segment.saveSegment(`errorWarnningMessage: ${errorWarnningMessage}`);
                            segment.saveSegmentFailure(`errorWarnningMessage: ${errorWarnningMessage}`); 
                            console.log('errorWarnningMessage:',errorWarnningMessage);
                        } catch(error){
                            segment.saveSegment(`Extraction error fetch error: ${JSON.stringify(error)}`);
                            segment.saveSegmentFailure(`Extraction error fetch error: ${JSON.stringify(error)}`); 
                        }


                        if(errorWarnningMessage){
                            if(errorWarnningMessage.length > 0){
                                warningObj.errorwarningMessage = errorWarnningMessage;
                            }
                        }


                        let coreReturnExceptionCsvFilePath = constants.CORE_RETURN_EXCEPTION_CSV_FILE_PATH;
                        let coreChargeExceptionCsvFilePath = constants.CORE_CHARGE_EXCEPTION_CSV_FILE_PATH;
                        let coreReturnNotEqualCoreChargeExceptionFilePath = constants.CORE_RETURN_NOT_EQUAL_TO_CORE_CHARGE_EXCEPTION_CSV_FILE_PATH;
                        let coreChargeWithNoSaleCsvFilePath = constants.CORE_CHARGE_WITH_NO_SALE;

                        let invalidCoreCostSaleMismatchFilePath = constants.INVALID_CORE_COST_SALE_MISMATCH;
                        let invalidCoreAmountMismatchFilePath = constants.INVALID_CORE_AMOUNT_MISMATCH;



                        let jsonCoreReturnArray, jsonCoreReturnMessage, coreReturnExceptionCount = 0;
                        let jsonCoreChargeArray, jsonCoreChargeMessage, coreChargeExceptionCount = 0;
                        let jsonCoreReturnNotEqualCoreChargeArray, jsonCoreReturnNotEqualCoreChargeMessage, coreReturnNotEqualCoreChargeExceptionCount = 0;
                        let coreChargeWithNoSaleArray, coreChargeWithNoSaleCount = 0;


                        let invalidCoreCostSaleMismatchArray, invalidCoreCostSaleMismatchCount = 0; 
                        let invalidCoreAmountMismatchArray, invalidCoreAmountMismatchCount = 0;

                        try{
                            if (fs.existsSync(coreReturnExceptionCsvFilePath)) {

                                console.log(`The core Return Exception Csv File exists: ${coreReturnExceptionCsvFilePath}`);
                                segment.saveSegment(`The core Return Exception Csv File exists:${coreReturnExceptionCsvFilePath}`);
                                segment.saveSegmentFailure(`The core Return Exception Csv File exists:${coreReturnExceptionCsvFilePath}`, storeCode);

                                jsonCoreReturnArray = await csv().fromFile(coreReturnExceptionCsvFilePath);
                          
                                if(jsonCoreReturnArray){
                                  if(jsonCoreReturnArray.length){

                                    console.log(`jsonCoreReturnArray.length:${jsonCoreReturnArray.length}`);
                                    segment.saveSegment(`jsonCoreReturnArray.length:${jsonCoreReturnArray.length}`);
                                    segment.saveSegmentFailure(`jsonCoreReturnArray.length:${jsonCoreReturnArray.length}`, storeCode);

                                    jsonCoreReturnMessage = jsonCoreReturnArray[jsonCoreReturnArray.length-1]['RO Number'];
                                    if(jsonCoreReturnMessage){
                                        coreReturnExceptionCount = parseInt(jsonCoreReturnMessage.split(":").reverse()[0].trim());
                                    }
                                    console.log(`jsonCoreReturnMessage: ${jsonCoreReturnMessage}`);
                                    segment.saveSegment(`jsonCoreReturnMessage: ${jsonCoreReturnMessage}`);
                                    segment.saveSegmentFailure(`jsonCoreReturnMessage: ${jsonCoreReturnMessage}`, storeCode);

                                    console.log(`coreReturnExceptionCount: ${coreReturnExceptionCount}`);
                                    segment.saveSegment(`coreReturnExceptionCount: ${coreReturnExceptionCount}`);
                                    segment.saveSegmentFailure(`coreReturnExceptionCount: ${coreReturnExceptionCount}`, storeCode);
               
                                  }
                                }
                            }

                            if (fs.existsSync(coreChargeExceptionCsvFilePath)) {
                                
                                console.log(`The core Charge Exception Csv File exists: ${coreChargeExceptionCsvFilePath}`);
                                segment.saveSegment(`The core Charge Exception Csv File exists: ${coreChargeExceptionCsvFilePath}`);
                                segment.saveSegmentFailure(`The core Charge Exception Csv File exists: ${coreChargeExceptionCsvFilePath}`, storeCode);

                                jsonCoreChargeArray = await csv().fromFile(coreChargeExceptionCsvFilePath);
                                if(jsonCoreChargeArray){
                                  if(jsonCoreChargeArray.length){

                                    console.log(`jsonCoreChargeArray.length: ${jsonCoreChargeArray.length}`);
                                    segment.saveSegment(`jsonCoreChargeArray.length: ${jsonCoreChargeArray.length}`);
                                    segment.saveSegmentFailure(`jsonCoreChargeArray.length: ${jsonCoreChargeArray.length}`, storeCode);

                                    jsonCoreChargeMessage = jsonCoreChargeArray[jsonCoreChargeArray.length-1]['RO Number'];
                                  
                                    if(jsonCoreChargeMessage){                                   
                                        coreChargeExceptionCount = parseInt(jsonCoreChargeMessage.split(":").reverse()[0].trim());
                                    }

                                    console.log(`jsonCoreChargeMessage: ${jsonCoreChargeMessage}`);
                                    segment.saveSegment(`jsonCoreChargeMessage: ${jsonCoreChargeMessage}`);
                                    segment.saveSegmentFailure(`jsonCoreChargeMessage: ${jsonCoreChargeMessage}`, storeCode);

                                    console.log(`coreChargeExceptionCount: ${coreChargeExceptionCount}`);
                                    segment.saveSegment(`coreChargeExceptionCount: ${coreChargeExceptionCount}`);
                                    segment.saveSegmentFailure(`coreChargeExceptionCount: ${coreChargeExceptionCount}`, storeCode);
                       
                                  }
                                }
                            }

                            if (fs.existsSync(coreReturnNotEqualCoreChargeExceptionFilePath)) {

                                console.log(`The core Return not equal to core Charge Exception Csv File exists:${coreReturnNotEqualCoreChargeExceptionFilePath}`);
                                segment.saveSegment(`The core Return not equal to core Charge Exception Csv File exists:${coreReturnNotEqualCoreChargeExceptionFilePath}`);
                                segment.saveSegmentFailure(`The core Return not equal to core Charge Exception Csv File exists:${coreReturnNotEqualCoreChargeExceptionFilePath}`, storeCode);

                                jsonCoreReturnNotEqualCoreChargeArray = await csv().fromFile(coreReturnNotEqualCoreChargeExceptionFilePath);
                                
                                if(jsonCoreReturnNotEqualCoreChargeArray){
                                  console.log(jsonCoreReturnNotEqualCoreChargeArray.length);
                                  if(jsonCoreReturnNotEqualCoreChargeArray.length){

                                    console.log(`jsonCoreReturnNotEqualCoreChargeArray.length: ${jsonCoreReturnNotEqualCoreChargeArray.length}`)
                                    segment.saveSegment(`jsonCoreReturnNotEqualCoreChargeArray.length: ${jsonCoreReturnNotEqualCoreChargeArray.length}`);
                                    segment.saveSegmentFailure(`jsonCoreReturnNotEqualCoreChargeArray.length: ${jsonCoreReturnNotEqualCoreChargeArray.length}`, storeCode);
                                    
                                    jsonCoreReturnNotEqualCoreChargeMessage = jsonCoreReturnNotEqualCoreChargeArray[jsonCoreReturnNotEqualCoreChargeArray.length-1]['RO Number'];
                                   
                                    if(jsonCoreReturnNotEqualCoreChargeMessage){
                                        coreReturnNotEqualCoreChargeExceptionCount = parseInt(jsonCoreReturnNotEqualCoreChargeMessage.split(":").reverse()[0].trim());
                                    }

                                    console.log(`jsonCoreReturnNotEqualCoreChargeMessage: ${jsonCoreReturnNotEqualCoreChargeMessage}`);
                                    segment.saveSegment(`jsonCoreReturnNotEqualCoreChargeMessage: ${jsonCoreReturnNotEqualCoreChargeMessage}`);
                                    segment.saveSegmentFailure(`jsonCoreReturnNotEqualCoreChargeMessage: ${jsonCoreReturnNotEqualCoreChargeMessage}`, storeCode);

                                    console.log(`coreReturnNotEqualCoreChargeExceptionCount: ${coreReturnNotEqualCoreChargeExceptionCount}`);
                                    segment.saveSegment(`coreReturnNotEqualCoreChargeExceptionCount: ${coreReturnNotEqualCoreChargeExceptionCount}`);
                                    segment.saveSegmentFailure(`coreReturnNotEqualCoreChargeExceptionCount: ${coreReturnNotEqualCoreChargeExceptionCount}`, storeCode);
                                 
                                  }
                                }
                            }

                            if (fs.existsSync(coreChargeWithNoSaleCsvFilePath)) {
                                
                                console.log(`The core charge with no sale Exception Csv File exists: ${coreChargeWithNoSaleCsvFilePath}`);
                                segment.saveSegment(`The core charge with no sale Exception Csv File exists: ${coreChargeWithNoSaleCsvFilePath}`);
                                segment.saveSegmentFailure(`The core charge with no sale Exception Csv File exists: ${coreChargeWithNoSaleCsvFilePath}`, storeCode);
                                
                                coreChargeWithNoSaleArray = await csv().fromFile(coreChargeWithNoSaleCsvFilePath);

                                if(coreChargeWithNoSaleArray){

                                    console.log(`coreChargeWithNoSaleArray.length: ${coreChargeWithNoSaleArray.length}`);
                                    segment.saveSegment(`coreChargeWithNoSaleArray.length: ${coreChargeWithNoSaleArray.length}`);
                                    segment.saveSegmentFailure(`coreChargeWithNoSaleArray.length: ${coreChargeWithNoSaleArray.length}`, storeCode);
                                    
                                    coreChargeWithNoSaleCount = coreChargeWithNoSaleArray.length;
                                }

                                console.log(`coreChargeWithNoSaleCount: ${coreChargeWithNoSaleCount}`);
                                segment.saveSegment(`coreChargeWithNoSaleCount: ${coreChargeWithNoSaleCount}`);
                                segment.saveSegmentFailure(`coreChargeWithNoSaleCount: ${coreChargeWithNoSaleCount}`, storeCode);
                            }

                            if (fs.existsSync(invalidCoreCostSaleMismatchFilePath)) {
                                
                                console.log(`The invalid Core Cost Sale Mismatch File Csv File exists: ${invalidCoreCostSaleMismatchFilePath}`);
                                segment.saveSegment(`The invalid Core Cost Sale Mismatch File Csv File exists: ${invalidCoreCostSaleMismatchFilePath}`);
                                segment.saveSegmentFailure(`The invalid Core Cost Sale Mismatch File Csv File exists: ${invalidCoreCostSaleMismatchFilePath}`, storeCode);
                                
                                invalidCoreCostSaleMismatchArray = await csv().fromFile(invalidCoreCostSaleMismatchFilePath);

                                if(invalidCoreCostSaleMismatchArray){

                                    console.log(`invalidCoreCostSaleMismatchArray.length: ${invalidCoreCostSaleMismatchArray.length}`);
                                    segment.saveSegment(`invalidCoreCostSaleMismatchArray.length: ${invalidCoreCostSaleMismatchArray.length}`);
                                    segment.saveSegmentFailure(`invalidCoreCostSaleMismatchArray.length: ${invalidCoreCostSaleMismatchArray.length}`, storeCode);
                                    
                                    invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                                }

                                console.log(`invalidCoreCostSaleMismatchCount: ${invalidCoreCostSaleMismatchCount}`);
                                segment.saveSegment(`invalidCoreCostSaleMismatchCount: ${invalidCoreCostSaleMismatchCount}`);
                                segment.saveSegmentFailure(`invalidCoreCostSaleMismatchCount: ${invalidCoreCostSaleMismatchCount}`, storeCode);
                            }


                            if (fs.existsSync(invalidCoreAmountMismatchFilePath)) {
                                
                                console.log(`The invalid Core Amount Mismatch File Csv File exists: ${invalidCoreAmountMismatchFilePath}`);
                                segment.saveSegment(`The invalid Core Amount Mismatch File Csv File exists: ${invalidCoreAmountMismatchFilePath}`);
                                segment.saveSegmentFailure(`The invalid Core Amount Mismatch File Csv File exists: ${invalidCoreAmountMismatchFilePath}`, storeCode);
                                
                                invalidCoreAmountMismatchArray = await csv().fromFile(invalidCoreAmountMismatchFilePath);

                                if(invalidCoreAmountMismatchArray){

                                    console.log(`invalidCoreAmountMismatchArray.length: ${invalidCoreAmountMismatchArray.length}`);
                                    segment.saveSegment(`invalidCoreAmountMismatchArray.length: ${invalidCoreAmountMismatchArray.length}`);
                                    segment.saveSegmentFailure(`invalidCoreAmountMismatchArray.length: ${invalidCoreAmountMismatchArray.length}`, storeCode);
                                    
                                    invalidCoreAmountMismatchCount = invalidCoreAmountMismatchArray.length;
                                }

                                console.log(`invalidCoreAmountMismatchCount: ${invalidCoreAmountMismatchCount}`);
                                segment.saveSegment(`invalidCoreAmountMismatchCount: ${invalidCoreAmountMismatchCount}`);
                                segment.saveSegmentFailure(`invalidCoreAmountMismatchCount: ${invalidCoreAmountMismatchCount}`, storeCode);
                            }

                        } catch(err){
                            console.log(err);
                            segment.saveSegment(`CDKFLEX : Read operation  of exception.csv have error ${err}`);
                            segment.saveSegmentFailure(`CDKFLEX : Read operation  of exception.csv have error ${err}`, storeCode);
                        }

                        var fetchGroupAndStoreName = (job.attrs.data.inputFile).split('-');
                        var groupName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[0] : '';
                        var storeName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[1] : '';

                        // coreReturnExceptionCount = 10;
                        // coreChargeExceptionCount = 20;
                        // coreReturnNotEqualCoreChargeExceptionCount = 40;
                        // coreChargeWithNoSaleCount = 30;

                        console.log('coreReturnExceptionCount:', coreReturnExceptionCount);
                        segment.saveSegment(`coreReturnExceptionCount : ${coreReturnExceptionCount}`);

                        console.log('coreChargeExceptionCount:', coreChargeExceptionCount);
                        segment.saveSegment(`coreChargeExceptionCount : ${coreChargeExceptionCount}`);

                        console.log('coreReturnNotEqualCoreChargeExceptionCount:', coreReturnNotEqualCoreChargeExceptionCount);
                        segment.saveSegment(`coreReturnNotEqualCoreChargeExceptionCount : ${coreReturnNotEqualCoreChargeExceptionCount}`);

                        console.log('coreChargeWithNoSaleCount:', coreChargeWithNoSaleCount);
                        segment.saveSegment(`coreChargeWithNoSaleCount : ${coreChargeWithNoSaleCount}`);

                        warningObj.coreReturnExceptionCount = coreReturnExceptionCount;
                        if(coreReturnExceptionCount) warningObj.coreReturnExceptionCsvFilePath = coreReturnExceptionCsvFilePath;

                        warningObj.coreChargeExceptionCount = coreChargeExceptionCount;
                        if(coreChargeExceptionCount) warningObj.coreChargeExceptionCsvFilePath = coreChargeExceptionCsvFilePath;

                        warningObj.coreReturnNotEqualCoreChargeExceptionCount = coreReturnNotEqualCoreChargeExceptionCount;
                        if(coreReturnNotEqualCoreChargeExceptionCount) warningObj.coreReturnNotEqualCoreChargeExceptionFilePath = coreReturnNotEqualCoreChargeExceptionFilePath;

                        warningObj.coreChargeWithNoSaleCount = coreChargeWithNoSaleCount;
                        if(coreChargeWithNoSaleCount) warningObj.coreChargeWithNoSaleCsvFilePath = coreChargeWithNoSaleCsvFilePath;


                        warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchCount;
                        if(invalidCoreCostSaleMismatchCount) warningObj.invalidCoreCostSaleMismatchFilePath = invalidCoreCostSaleMismatchFilePath;


                        warningObj.invalidCoreAmountMismatchCount = invalidCoreAmountMismatchCount;
                        if(invalidCoreAmountMismatchCount) warningObj.invalidCoreAmountMismatchFilePath = invalidCoreAmountMismatchFilePath;



                         
                        var mailTemplateReplacementValues = {
                            dmsType: constants.JOB_TYPE,
                            processTypes: constants.PROCESS_XML.JOB_NAME,
                            subject: `Process XML for ${groupName} - ${storeName} Completed`,
                            warningObj: warningObj,
                            thirdPartyUsername: dealerId,
                            storeCode: storeName,
                            groupCode: groupName
                        };

                        var mailBody = {
                            fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                            toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                            ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                            attachedfailurelogFile:failurelogFile
                        }
                      if (status) {
                            var dmsType = constants.JOB_TYPE.toLowerCase();
                            var opDataFileEtl = path.join(`/etl/etl-vagrant/etl-${dmsType}/${dmsType}-zip/`, `${constants.PROCESS_XML.OUTPUT_PREFIX_VAL}${basename}`);
                            var opDataFileDist = path.join(constants.PROCESS_XML.DIST_DIR, constants.PROCESS_XML.OUTPUT_PREFIX_VAL + basename)
                            opDataFileEtl = opDataFileEtl.replace(constants.PROCESS_XML.REPLACE_STRING.FROM, constants.PROCESS_XML.REPLACE_STRING.TO);
                            var outputFile = opDataFileDist + ' & ' + opDataFileEtl;
                            job.attrs.data.outputFile = outputFile;
                            job.attrs.data.status = status;
                            job.attrs.data.message = message;
                            job.attrs.data.warningMessage = warningObj;

                            job.attrs.data.coreReturnExceptionCount =  coreReturnExceptionCount;
                            job.attrs.data.coreChargeExceptionCount =  coreChargeExceptionCount;
                            job.attrs.data.coreReturnNotEqualCoreChargeExceptionCount =  coreReturnNotEqualCoreChargeExceptionCount;
                            job.attrs.data.coreChargeWithNoSaleCount =  coreChargeWithNoSaleCount;

                            job.attrs.data.invalidCoreCostSaleMismatchCount =  invalidCoreCostSaleMismatchCount;
                            job.attrs.data.invalidCoreAmountMismatchCount =  invalidCoreAmountMismatchCount;

                            segment.saveSegment(`Job saved to DB ${JSON.stringify(job)}`);
                            segment.saveSegmentFailure(`Job saved to DB ${JSON.stringify(job)}`, storeCode);
                            await job.save();
                            done();
                            // CDKFLEX Process XML Completed - send notification mail                            
                            var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.PROCESS_XML.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Success';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            // Send notification after process xml job completed
                            mailSender.sendMail(mailBody, constants.PROCESS_XML.JOB_NAME);
                        } else {

                             // Portal update for process xml failed or halt or dead
                            let todayDate;
                            let attPayload = {};
                            let projectID;
                            let secondProjectID;
                            let inpObjProject;
                            let inpObjSecondProject;
                            try{
                            todayDate = new Date().toISOString().slice(0, 10);
                            attPayload = agendaObject[extractedObjectIndex];
                            projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                            attPayload['inProjectId'] =  projectID;

                            secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                            attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                            attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                            attPayload.in_retrive_ro_request_on = todayDate;
                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                            console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                            if(secondProjectID){
                                inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);                                console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                            }
                            } catch(err){
                            console.log(JSON.stringify(err));
                            segment.saveSegment(`CDKFLEX : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                            }
                            segment.saveSegment(`CDKFLEX : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                            segment.saveSegment(`CDKFLEX : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                            try {
                                segment.saveSegment(`CDKFLEX : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                                


                                let  projectIdList =  inpObjProject.inProjectId.split("*");
                                if(projectIdList){
                                     for(const id of projectIdList){
                                        if(id){
                                            inpObjProject.inProjectId = id;
                                            portalUpdate.doPayloadAction(inpObjProject);
                                            console.log(`CDKFLEX Schedule portal call with Project Id FAILURE${id}`);
                    
                                        }
                                     }
                                } 

                                if(secondProjectID){
                                segment.saveSegment(`CDKFLEX : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                                
                                let parsedData = JSON.parse(inpObjSecondProject.inData);
                                let secondProjectIdList =  parsedData.secondProjectIdList.split("*");
                            if(secondProjectIdList){
                                for(const id of secondProjectIdList){
                                   if(id){
                                       inpObjSecondProject.inProjectId = id;
                                       portalUpdate.doPayloadAction(inpObjSecondProject);

                                       console.log(`CDKFLEX Schedule portal call with Second Project Id FAILURE${id}`);
               
                                   }
                                }
                           } 
                                }
                            } catch(error) {
                                console.log("Error:", error);
                                segment.saveSegment(`CDKFLEX : doPayloadAction Error - ${JSON.stringify(error)}`); 
                            }
                            //code end for portal update for process xml failed
                            job.attrs.data.warningMessage = warningObj;

                            job.attrs.data.coreReturnExceptionCount =  coreReturnExceptionCount;
                            job.attrs.data.coreChargeExceptionCount =  coreChargeExceptionCount;
                            job.attrs.data.coreReturnNotEqualCoreChargeExceptionCount =  coreReturnNotEqualCoreChargeExceptionCount;
                            job.attrs.data.coreChargeWithNoSaleCount =  coreChargeWithNoSaleCount;

                            job.attrs.data.invalidCoreCostSaleMismatchCount =  invalidCoreCostSaleMismatchCount;
                            job.attrs.data.invalidCoreAmountMismatchCount =  invalidCoreAmountMismatchCount;

                            await job.fail(new Error(`Error: ${message}`));
                            done();
                            // CDKFLEX Process XML Failed - send notification mail
                            
                            let displayMessage ;
                            if(haltIdentifier){
                                if(message == 'Halt'){
                                    displayMessage = `Halted ${constants.JOB_TYPE} ${constants.PROCESS_XML.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                    mailTemplateReplacementValues.message = displayMessage;
                                    mailTemplateReplacementValues.status = 'Halted';
                                    mailTemplateReplacementValues.subject = `Process XML  for ${groupName} - ${storeName} Halted`;
                                } else if(message == 'Dead'){
                                    displayMessage = `Held ${constants.JOB_TYPE} ${constants.PROCESS_XML.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                    mailTemplateReplacementValues.message = displayMessage;
                                    mailTemplateReplacementValues.status = 'Held';
                                    mailTemplateReplacementValues.subject = `Process XML  for ${groupName} - ${storeName} Held`;
                                }
                            } else{
                                if(haltOverRide){
                                    mailTemplateReplacementValues.resumeUser = resumeUser ? resumeUser : '';
                                }
                                displayMessage = `Failed ${constants.JOB_TYPE} ${constants.PROCESS_XML.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                mailTemplateReplacementValues.message = displayMessage;
                                mailTemplateReplacementValues.status = 'Failed';
                                mailTemplateReplacementValues.subject = `Process XML  for ${groupName} - ${storeName} Failed`;   
                            }
                           
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            // Send notification for failed process xml job
                            mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                            mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                            segment.saveSegmentFailure(displayMessage, storeCode);
                            await segment.sleep(2000);
                            mailSender.sendMail(mailBody, constants.PROCESS_XML.JOB_NAME);
                        }
                        console.log(`Call for next job selection`);
                        segment.saveSegment(`Call method for SharePoint data upload`);
                        segment.saveSegment(`Call for next job selection`);
                        var basenameCheck = constants.PROCESS_XML.OUTPUT_PREFIX_VAL + basename;
                        var distFile = path.join(constants.PROCESS_XML.BUNDLE_DIR, basenameCheck)
                     if (status && fs.existsSync(distFile)) {
                            console.log('Just start the distribute function');
                            console.log('distFile', distFile);
                            basename = constants.PROCESS_XML.OUTPUT_PREFIX_VAL + basename;
                            console.log('doProxyFromPgDump:', doProxyFromPgDump);
                            let rerunFlag;
                            if(doProxyFromPgDump){
                                rerunFlag='RERUN'; 
                            }
                            await distributeFile(basename, rerunFlag, updateSolve360Data, warningObj);
                        } else {
                            // Process XML Job Fail ....
                            segment.saveSegment(`Call for next job selection`);
                            await doNextProcess();
                        }
                    });
                }
            } else {
                done();
                /**
                * Remove the Initial/recheck schedules
                */
                job.remove(err => {
                    if (!err) {
                        console.log("Initial/recheck schedule for Process XML job successfully removed");
                        segment.saveSegment("Initial/recheck schedule for Process XML job successfully removed");
                    }
                });
                await doNextProcess();
            }
        });

    return agenda;
}

const TekionApiJobManager = require("../TekionApiJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all TekionApi-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllTekionApiExtractJobs(_, args) {
      return TekionApiJobManager.getAllTekionApiExtractJobs();
    },

    /**
     * Query to get all Tekionapi Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllTekionApiProcessJSONJobs(_, args) {
      return TekionApiJobManager.getAllTekionApiProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule TekionApi-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleTekionApiExtractJob(_, args) {
      return TekionApiJobManager.scheduleTekionApiExtractJob(args.input);
    },

    /**
     * Mutation to run a TekionApi-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowTekionApiExtractJobByStore(_, args) {
      return TekionApiJobManager.runNowTekionApiExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a TekionApi-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelTekionApiExtractJobByStore(_, args) {
      return TekionApiJobManager.cancelTekionApiExtractJobByStore(args.input);
    },
    
        /**
     * Mutation to create a Process XML job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */


    createProxyWithSqlDump(_, args) {
      return TekionApiJobManager.createProxyWithSqlDump(args.input);
    },
  },


  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;

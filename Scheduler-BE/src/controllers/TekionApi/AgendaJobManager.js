const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeTekionapiProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "tekionapi-process-json") {
      initializeTekionapiProcessJSON = true
    }
    if (type.split('-')[0] == 'tekionapi') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the tekionapi-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeTekionapiProcessJSON) {
    try {
      // Initialize Tekionapi Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("Tekionapi :  Process JSON schedule started");
      segment.saveSegment("Tekionapi :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("TekionApi Extraction Processing Not Enabled - Pass Job Type tekionapi-process-json To Enable");
    segment.saveSegment("TekionApi Extraction Processing Not Enabled - Pass Job Type tekionapi-process-json To Enable");
  }
  return true;
}

"use strict";

const moment = require("moment-timezone");
const constants = require("./constants");

const fs = require("fs");
const path = require("path");
const util = require("util");
const mv = require("mv");
const unZipper = require("unzipper");
const agendaDbModel = require("../../model/agendaDb");
const readdirAsync = util.promisify(fs.readdir);
const statAsync = util.promisify(fs.stat);

function setStartTimeForUser(inTime) {
    var retTime = inTime.hour(constants.DEALERBUILT.START_TIME.split(":")[0]);
    retTime = retTime.minute(constants.DEALERBUILT.START_TIME.split(":")[1]);
    retTime = retTime.second(0);
    retTime = retTime.millisecond(0);
    retTime = retTime.utc();
    return retTime;
}

function getZonedTime(inTime) {
    var retTime = moment().utc();
    retTime = retTime.tz(constants.DEALERBUILT.TIME_ZONE)
        .hour(inTime.split(":")[0])
        .minute(inTime.split(":")[1])
        .second(0)
        .millisecond(0)
        .utc()
    return retTime;
}

function getTimeFrame() {
    var startTime = getZonedTime(constants.DEALERBUILT.START_TIME);
    var endTime = getZonedTime(constants.DEALERBUILT.END_TIME);
    if (startTime.isAfter(endTime)) {
    /**
     * This is main condition for checking the time-frame.
     * If the time-frame start time is greater than the
     * end time, then it should be yesterday's night time.
     * So we should subtract one day from the start time.
     */
    if (moment().tz(constants.DEALERBUILT.TIME_ZONE).format('A') == "AM") {
    startTime = startTime.subtract(1, "days");
    } else if (moment().tz(constants.DEALERBUILT.TIME_ZONE).format('A') == "PM") {
    endTime = endTime.add(1, "days");
    }
    }
    return {
        startTime: startTime,
        endTime: endTime
    };
}

/**
 * Function to check the time frame for data extraction which is set in the env file
 */
function checkExtractTimeFrame() {
    var timeFrame = getTimeFrame();
    var startTime = timeFrame.startTime;
    var endTime = timeFrame.endTime;
    var currentUTCDate = moment().utc();
    return currentUTCDate.isAfter(startTime) && currentUTCDate.isBefore(endTime);
}

/**
 * Function to convert GraphQLDate string to common date format
 * @param {string} dateStr GraphQL GraphQLDateTime string
 */
function toDate(dateStr) {
    var inDate = moment(dateStr).format("YYYY-MM-DDTHH:mm:ssZZ");
    var timeFrame = getTimeFrame();
    var startTime = timeFrame.startTime;
    var endTime = timeFrame.endTime;
    var userTimeInput = moment(inDate).utc().tz(constants.DEALERBUILT.TIME_ZONE);
    userTimeInput = userTimeInput.utc();
    var currentUTCDate = moment().utc().tz(constants.DEALERBUILT.TIME_ZONE);
    currentUTCDate = currentUTCDate.utc(); // Convert user input to UTC

    var ret = "";
    if (userTimeInput.isSame(currentUTCDate, "year") &&
        userTimeInput.isSame(currentUTCDate, "month") &&
        userTimeInput.isSame(currentUTCDate, "day")) { // back date entry not permitted
        if (userTimeInput.isBetween(startTime, endTime)) {
            /**
             * Add POOL_END_CHECKING minutes to user input time and check if same or before
             * with timeFrame end time value. If yes, add POOL_ADD minutes to user input time
             * otherwise reschedule to next day.
             */
            if (moment(userTimeInput).add(constants.DEALERBUILT.POOL_END_CHECKING, "minutes").isSameOrBefore(endTime)) {
            ret = userTimeInput.add(constants.DEALERBUILT.POOL_ADD, "seconds");
            } else {
            ret = startTime.add(1, "days")
            }

        } else if (userTimeInput.isBefore(startTime)) {

            ret = startTime;
        } else if (userTimeInput.isAfter(endTime)) {

            ret = startTime.add(1, "days");

        }
        ret = userTimeInput.add(constants.DEALERBUILT.POOL_ADD, "seconds");
    } else if (userTimeInput.isAfter(currentUTCDate)) {
        ret = setStartTimeForUser(userTimeInput);
    } else {
        throw ("Back date entry");
    }
    return ret;

}

// moment usage samples for reference
// console.log(`Asia: ${moment.tz(moment(), "Asia/Karachi").format("DD/MM/YYYY HH:MM")}`);
// console.log(`America/Chicago: ${moment.tz(moment(), "America/Chicago").format("DD/MM/YYYY HH:MM")}`);


/**
 * Function to get schedule for tomorrow in case of auto scheduling
 */
function getScheduleForTomorrow() {
    var nextScheduleOfToday = getTimeFrame().startTime;
    var nextScheduleOfTomorrow = nextScheduleOfToday.add(1, "days");
    return nextScheduleOfTomorrow;
}

/**
 * Find the oldest zip file (FIFO order of store data extracted) from the zipPath,
 * extract the zip and return the path to ROs.json file.
 */

async function getOldestZipFileAndReturnROFolder(zipPath) {
    return new Promise(async (resolve, reject) => {
        const basePath = path.join(zipPath);
        const files = await readdirAsync(basePath);
        var zipFileList = files.filter(function (e) {
            return path.extname(e).toLowerCase() === ".zip"
        });
        const stats = await Promise.all(
            zipFileList.map((filename) =>
                statAsync(path.join(basePath, filename))
                    .then((stat) => ({ filename, stat }))
            )
        );
        const sortedZipFiles = stats.sort((a, b) =>  // Sort by older file
            a.stat.mtime.getTime() - b.stat.mtime.getTime()
        ).map((stat) => stat.filename);

        if (sortedZipFiles.length > 0) {
            const fileNameToProcess = sortedZipFiles[0];
            var zipToProcess = path.join(basePath + "/" + fileNameToProcess);
            const extractFolder = path.join(basePath + "/" + fileNameToProcess.slice(0, fileNameToProcess.length - 4));// Remove '.zip' from the zip file
            console.log(`Extracting store details from zip file ${zipToProcess} to ${extractFolder}`);
            // unzip the file
            await fs.createReadStream(path.join(basePath + "/" + fileNameToProcess))
                .pipe(unZipper.Extract({ path: extractFolder }))
                .on('close', function () {
                    // move zip to processed location
                    const processedDir = path.join(basePath + "/processed");
                    if (!fs.existsSync(processedDir)) {
                        fs.mkdirSync(processedDir);
                    }
                    const zipPathToMove = processedDir + "/" + fileNameToProcess;
                    mv(zipToProcess, zipPathToMove, { clobber: true }, function (err) {
                        // done. If 'dest/file' exists, overwrites it
                        if (err)
                            console.error(err)
                        console.log(`${zipToProcess} moved to ${zipPathToMove}`);
                    });
                    // send path to the ROs.xml& OpenRO.xml file after verifying the existence of the file
                    resolve(extractFolder);
                });
        } else {
            resolve(null);
        }
    });
}

async function findOldestZipFile(zipPath) {
    return new Promise(async (resolve, reject) => {
        const basePath = path.join(zipPath);
        const files = await readdirAsync(basePath);
        var zipFileList = files.filter(function (e) {
            return path.extname(e).toLowerCase() === ".zip"
        });
        const stats = await Promise.all(
            zipFileList.map((filename) =>
                statAsync(path.join(basePath, filename))
                    .then((stat) => ({ filename, stat }))
            )
        );
        const sortedZipFiles = stats.sort((a, b) =>  // Sort by older file
            a.stat.mtime.getTime() - b.stat.mtime.getTime()
        ).map((stat) => stat.filename);

        let newSortedFiles =[];

        if (sortedZipFiles.length > 0) {
         for(let i=0;i<sortedZipFiles.length;i++){
            let fileNameToProcess = sortedZipFiles[i];
            let queueList1 = await agendaDbModel.fetchJobFromProcessorQueue("dealerbuilt_queue");
            let queueResponse1 =queueList1.response;
    
            let flag =true;
            // Create an array of promises for addToProcessorQueue
            for (let i = 0; i < queueResponse1.length; i++) {
                let currentItem = queueResponse1[i];
                console.log(`Item ${i + 1}:`);
                console.log(`_id: ${currentItem._id}`);
                console.log(`extractionId: ${currentItem.extractionId}`);
                console.log(`fileName: ${currentItem.fileName}`);
                console.log(`priority: ${currentItem.priority}`); 
                console.log('---');
               
                if(currentItem.fileName==fileNameToProcess){
                    flag =false;
                    
                  }
                }
            if(flag){
                newSortedFiles.push(sortedZipFiles[i]);
            }
        }
     }
            const addToQueuePromises = newSortedFiles.map(async (fileNameToProcess) => {
                let uniqueExtractionId = Date.now();
                let parentName = await agendaDbModel.fetchParentName(fileNameToProcess.trim());
                let queueItem = { "extractionId": uniqueExtractionId, "fileName": fileNameToProcess, "priority": 7,parentName:parentName.response };
                return agendaDbModel.addToProcessorQueue(queueItem,"dealerbuilt_queue",(result)=>{
                
                });
            });
    
            // Wait for all addToProcessorQueue promises to complete
    
    
            try {
                await Promise.all(addToQueuePromises);
            } catch (error) {
                console.error("Error adding items to queue:", error);
                reject(error); // Reject the main promise in case of an error
                return;
            }
    
    
         let queueList = await agendaDbModel.fetchJobFromProcessorQueue("dealerbuilt_queue")
         let queueResponse =queueList.response;
         let nextFile ='';
        //  for (let i = 0; i < queueResponse.length; i++) {
        //      for(let j=0;j<queueResponse.length;j++){
    
             
        //     let currentItem = queueResponse[j];
        //     console.log(`Item ${i + 1}:`);
        //     console.log(`_id: ${currentItem._id}`);
        //     console.log(`extractionId: ${currentItem.extractionId}`);
        //     console.log(`fileName: ${currentItem.fileName}`);
        //     console.log(`priority: ${currentItem.proirity}`); 
        //     console.log('---');
           
        //     if(currentItem.proirity==1){
        //        nextFile = currentItem.fileName;
              
        //     }else if(currentItem.proirity==2){
    
        //     }
        // }
        //   }
        if(queueResponse[0]){
            nextFile = queueResponse[0].fileName;
        }
    
          if(nextFile){
            console.log('test@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@2');
            let deleteStatus = await agendaDbModel.deleteJobFromProcessorQueue(nextFile,"dealerbuilt_queue");
            resolve(nextFile)
          }else{
            resolve(null);
          }
    });
}

async function updateProcessorQueue(zipPath,currentRunningFile) {
    return new Promise(async (resolve, reject) => {
        const basePath = path.join(zipPath);
        const files = await readdirAsync(basePath);
        var zipFileList = files.filter(function (e) {
            return path.extname(e).toLowerCase() === ".zip"
        });
        const stats = await Promise.all(
            zipFileList.map((filename) =>
                statAsync(path.join(basePath, filename))
                    .then((stat) => ({ filename, stat }))
            )
        );
        const sortedZipFiles = stats.sort((a, b) =>  // Sort by older file
            a.stat.mtime.getTime() - b.stat.mtime.getTime()
        ).map((stat) => stat.filename);

    
    let newSortedFiles =[];

    if (sortedZipFiles.length > 0) {
     for(let i=0;i<sortedZipFiles.length;i++){
        let fileNameToProcess = sortedZipFiles[i];
        let queueList1 = await agendaDbModel.fetchJobFromProcessorQueue("dealerbuilt_queue");
        let queueResponse1 =queueList1.response;

        let flag =true;
        // Create an array of promises for addToProcessorQueue
        for (let i = 0; i < queueResponse1.length; i++) {
            let currentItem = queueResponse1[i];
            if(fileNameToProcess == currentRunningFile || currentItem.fileName ==fileNameToProcess){
                flag =false;
                
              }
            }
        if(flag){
            newSortedFiles.push(sortedZipFiles[i]);
        }
    }
 }
        const addToQueuePromises = newSortedFiles.map(async (fileNameToProcess) => {
            let uniqueExtractionId = Date.now();
            let parentName = await agendaDbModel.fetchParentName(fileNameToProcess.trim());
            let queueItem = { "extractionId": uniqueExtractionId, "fileName": fileNameToProcess, "priority": 7,parentName:parentName.response};
            return agendaDbModel.addToProcessorQueue(queueItem,"dealerbuilt_queue",(result)=>{
            
            });
        });

        // Wait for all addToProcessorQueue promises to complete


        try {
            await Promise.all(addToQueuePromises);
            if(fs.existsSync(`/home/<USER>/tmp/du-etl-dms-dealerbuilt-extractor-work/scheduler-temp/dealerbuilt-zip-eti/${currentRunningFile}`)){
                await agendaDbModel.deleteJobFromProcessorQueue(currentRunningFile,"dealerbuilt_queue");
                console.log("Already existing running file has removed!!!!!!!!!!!!!!!!!!!!!!!");

          }
            resolve(true)
        } catch (error) {
            reject(error); // Reject the main promise in case of an error
            return;
        }


    });
}


/**
 * Find the names of the zip files to extract and return with store names (slice from zip name).
 */

async function getQueuedStoresAndZipNames(zipPath) {
    return new Promise(async (resolve, reject) => {
        const basePath = path.join(zipPath);
        const files = await readdirAsync(basePath);
        var zipFileList = files.filter(function (e) {
            return path.extname(e).toLowerCase() === ".zip"
        });
        const stats = await Promise.all(
            zipFileList.map((filename) =>
                statAsync(path.join(basePath, filename))
                    .then((stat) => ({ filename, stat }))
            )
        );
        const sortedZipFiles = stats.sort((a, b) =>  // Sort by older file
            a.stat.mtime.getTime() - b.stat.mtime.getTime()
        ).map((stat) => stat.filename);

        if (sortedZipFiles.length > 0) {
            var retArray = [];
            sortedZipFiles.forEach(sortedZipFile => {
                const zipToProcess = path.join(basePath + "/" + sortedZipFile);
                const storeID = sortedZipFile.split("-").reverse()[1];
                retArray.push({ storeID: storeID, fileToProcess: zipToProcess });
            });
            resolve(retArray);
        } else {
            resolve([]);
        }
    });
}

function checkAccountingCsvExist(accountingCsvFileName){

}

function generateUniqueId() {
    const now = new Date();

    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    const millisecond = String(now.getMilliseconds()).padStart(3, '0');

    const randomNumber = String(Math.floor(Math.random() * 1000)).padStart(3, '0');

    const uniqueId = `db${year}${month}${day}${hour}${minute}${second}${millisecond}${randomNumber}`;

    return uniqueId;
}


module.exports = {
    toDate: toDate,
    checkExtractTimeFrame: checkExtractTimeFrame,
    getScheduleForTomorrow: getScheduleForTomorrow,
    getOldestZipFileAndReturnROFolder: getOldestZipFileAndReturnROFolder,
    findOldestZipFile: findOldestZipFile,
    getTimeFrame: getTimeFrame,
    getQueuedStoresAndZipNames: getQueuedStoresAndZipNames,
    updateProcessorQueue:updateProcessorQueue,
    generateUniqueId:generateUniqueId
}

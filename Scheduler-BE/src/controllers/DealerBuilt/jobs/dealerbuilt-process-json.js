"use strict";

const constants = require("../constants");
const util = require("../util");
const moment = require("moment-timezone");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const { spawn } = require("child_process");
const path = require('path');
const fs = require("fs");
const segment = require("../../SEGMENT/DealerBuilt/segmentManager");
const sharePoint = require("../../../routes/sharePoint");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const stripAnsi = require('strip-ansi');
var Agenda = require("../../agenda");
const extractionError = require('../../../../src/common/extractionError');
var accounting_csv_directory = '/etl/accounting_in/';
const SetProcessJobStatus = require('../../../model/setProcessJobStatus');
/**
 * Function to perform processing of XML file downloaded through DealerBuilt-Extract job
 */
module.exports = async function ProcessXmlJOB(agenda) {

    var distributeFile = async function (fileName, rerunFlag , updateSolve360Data, warningObj, jobId) {
        var stdErrorArray;
        var distDir = path.join(process.env.DEALERBUILT_DIST_DIR, fileName);
        var etlDir = path.join(process.env.DEALERBUILT_ETL_DIR, fileName);
        etlDir = etlDir.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
        var filePath = path.join(process.env.DEALERBUILT_BUNDLE_DIR, fileName);
        const distributeFile = spawn("bash",
            [
                'send-bundle-live-hpdog', filePath
            ], {
                cwd: constants.PROCESS_JSON.DEALERBUILT_DISTRIBUTE_CMD_PATH,
                env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
            }).on('error', function (err) {
                console.log("error :", err);
                segment.saveSegment(`error: ${err}`);
            });
        console.log(`DealerBuilt: Start processing of distribution`);
        segment.saveSegment(`DealerBuilt:  processing distribution`);
        process.stdin.pipe(distributeFile.stdin);
        distributeFile.stdout.on("data", async (data) => {
            console.log(`stdout: ${data}`);
            segment.saveSegment(`stdout: ${data}`);
        });

        distributeFile.stderr.on("data", async (data) => {
            console.log(`stderr: ${data}`);
            stdErrorArray += data.toString() + ' ';
            segment.saveSegment(`stderr: ${data}`);
        });

        distributeFile.on("close", async (code) => {
            var message = "n/a";
            var status = false;
            if (code == constants.STATUS_CODE.SUCCESS) {
                status = true;
                message = "Success";
            } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                message = "Distribution failed, general death";
            } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                message = "Distribution failed";
            }
            segment.saveSegment(`close: ${message}`);
            if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_EXIST_CHECK)) {
                status = false;
                message = "Distribution failed. Zip File Must Exist";
                segment.saveSegment(message);
            }
            /**
              * Upload files to SharePoint
              */
            if (status) {
                sharePoint.initSharePoint(distDir, constants.JOB_TYPE, rerunFlag , updateSolve360Data, warningObj, 0, jobId);//Upload dist directory zip file to sharepoint
            }
            await doNextProcess();
        });
    }

    var doNextProcess = async function () {
        await segment.sleep(5000);
        const extractZip = await util.findOldestZipFile(constants.DEALERBUILT_SCHEDULER_ETI_DIR);
        // let existAccountingcsv = false;
        // let accountingCsvFilepath;
        // let mageStoreCode;
        // let basename;
        // if(extractZip){
        //     basename = path.basename(extractZip);
        //     mageStoreCode =  basename.split("-")[1];
        //     accountingCsvFilepath = accounting_csv_directory + mageStoreCode + '.txt';
        //     console.log(accountingCsvFilepath);
        //     if (fs.existsSync(accountingCsvFilepath)) {
        //         existAccountingcsv = true;
        //     }
        // }
        // if (extractZip && existAccountingcsv) {
        if (fs.existsSync(`/home/<USER>/tmp/du-etl-dms-dealerbuilt-extractor-work/scheduler-temp/dealerbuilt-zip-eti/${extractZip}`)) {
            console.log(`file Name ${extractZip} exists!`);
        } else {
            console.log(`file Name ${extractZip} does not exists`);
        }
        if (extractZip && fs.existsSync(`/home/<USER>/tmp/du-etl-dms-dealerbuilt-extractor-work/scheduler-temp/dealerbuilt-zip-eti/${extractZip}`)) {
            console.log(`DealerBuilt : Found one Store extraction > ${extractZip} to process now`);
            segment.saveSegment(`DealerBuilt : Found one Store extraction > ${extractZip} to process now`);
            try {
                var createdAt = extractZip.slice(0, extractZip.length - 4).split("-").reverse()[0];
                await agenda.now(constants.PROCESS_JSON.JOB_NAME, {
                    inputFile: extractZip,
                    createdAt: createdAt,
                    // accountingCsvFile: accountingCsvFilepath,
                    operation: "json-processing"
                });
                console.log(`DealerBuilt : Process JSON schedule started with file > ${extractZip}`);
                segment.saveSegment(`DealerBuilt : Process JSON schedule started with file > ${extractZip}`);
            } catch (error) {
                console.error(error);
            }

        } else {
            console.log(`DealerBuilt : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            //segment.saveSegment(`DealerBuilt : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            try {
                await agenda.schedule(`${constants.PROCESS_JSON.TIME_GAP}`, constants.PROCESS_JSON.JOB_NAME, { operation: "recheck" });
                console.log(`DealerBuilt : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
                //segment.saveSegment(`DealerBuilt : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
            } catch (error) {
                console.error(error);
            }
        }
    }

    console.log(
        `DealerBuilt : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`
    );
    segment.saveSegment(`DealerBuilt : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`);
    agenda.define(constants.PROCESS_JSON.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.PROCESS_JSON.CONCURRENCY,lockLifetime: 3 * 60 * 60 * 1000 },
        async (job, done) => {
            const att = job.attrs.data;
            const touch = setInterval(() => job.touch(), 1 * 60 * 1000);
            var extractZip = null;
            var accountingCsvFile = null;
            var stdErrorArray = [];
            var stdOutArray = [];
            var uniqueFailLogName;

            var inpFile = att.inputFile ? path.join(constants.DEALERBUILT_SCHEDULER_ETI_DIR, att.inputFile) : '';

            if (att.inputFile && fs.existsSync(inpFile)) {
                if (!fs.existsSync(constants.DEALERBUILT_DEADLETTER_DIR_PREFIX + '-processed')) {
                    fs.mkdirSync(constants.DEALERBUILT_DEADLETTER_DIR_PREFIX + '-processed');
                }
                extractZip = att.inputFile;
                accountingCsvFile = att.accountingCsvFile;
                let basename = path.basename(extractZip);
                job.attrs.data.storeID = basename.split("-").reverse()[1];
                let storeName = job.attrs.data.storeID;

                let locationId = basename.split("-").reverse()[1];
                let mageGroupCode = basename.split("-")[0];
                let mageStoreCode =  basename.split("-")[1];

                

                uniqueFailLogName = locationId + '-' + mageStoreCode;

                console.log('Groupname:', mageGroupCode);
                segment.saveSegment(`Groupname : ${mageGroupCode}`);
                console.log('Location Id:', locationId);
                segment.saveSegment(`Location Id: : ${locationId}`);
                console.log('storeName:',mageStoreCode);
                segment.saveSegment(`storeName : ${mageStoreCode}`);

                var jobsTmp = await Agenda.jobs( {
                    $and: [
                        { "data.storeDataArray.locationId": locationId },
                        { "name": constants.DEALERBUILT.JOB_NAME},
                        {"data.storeDataArray.mageStoreCode":mageStoreCode}  
                    ]
                });
                
                var projectId = '';
                var secondProjectId = '';
                var userName = '';
                var solve360Update = '';
                var updateSolve360Data; 
                var buildProxies;
                var extractionId;
                var accountingCSVFilePath;
                var dealerAddress; 
                 
                let agendaObject;
                let extractedFileTimeStamp;
                let extractedFileCreationDate;
                let extractedObjectIndex;

                let mageManufacturer;
                let isPorscheStore;

                let projectIds;
                let secondProjectIdList;
                let uniqueId;
                let companyIds;
                let companyObj;
		        let testData;

                try{
                    extractedFileTimeStamp = basename.split("-").reverse()[0].replace(".zip", "");
                    segment.saveSegment(`extractedFileTimeStamp : ${extractedFileTimeStamp}`);
                    extractedFileCreationDate =  moment(extractedFileTimeStamp, "YYYYMMDDhhmmss").format("YYYY-MM-DD");
                    segment.saveSegment(`extractedFileCreationDate : ${extractedFileCreationDate}`);
                } catch(err){
                    console.log(err);
                    segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                }
                
                segment.saveSegment(`jobsTmp : ${JSON.stringify(jobsTmp)}`);
                segment.saveSegment(`jobsTmp[jobsTmp.length-1] : ${JSON.stringify(jobsTmp[jobsTmp.length-1])}`);
                segment.saveSegment(`Location Id : ${locationId}`);

                if(jobsTmp[jobsTmp.length-1]){
                    if(jobsTmp[jobsTmp.length-1].hasOwnProperty("attrs")){
                        extractionId = jobsTmp[jobsTmp.length-1].attrs._id;
                        try{
                            segment.saveSegment(`jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : ${JSON.stringify(jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray)}`);
                            agendaObject = jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray;
                            // && el.mageGroupCode == mageGroupCode
                            agendaObject = agendaObject.filter(function (el) {
                                return el.locationId == locationId && el.mageStoreCode == mageStoreCode;
                            });
                            segment.saveSegment(`agendaObject : ${JSON.stringify(agendaObject)}`);
                            extractedObjectIndex = 0;
                            if(agendaObject.length > 0){ 
                                agendaObject =  agendaObject.sort((a,b) => b.endTime > a.endTime);
                                extractedObjectIndex = agendaObject.findIndex(
                                    obj => moment(obj.endTime, "YYYY-MM-DDTHH:mm:ss.SSS[Z]").format("YYYY-MM-DD") == extractedFileCreationDate
                                );
                            }

                            if(extractedObjectIndex < 0){
                                extractedObjectIndex = 0;
                            }
                            
                            segment.saveSegment(`Sorted agenda object : ${JSON.stringify(agendaObject)}`);
                            segment.saveSegment(`extractedObjectIndex : ${extractedObjectIndex}`);
                            segment.saveSegment(`Extracted agenda object : ${JSON.stringify(agendaObject[extractedObjectIndex])}`);


                            if(agendaObject[extractedObjectIndex].hasOwnProperty("projectId")){
                                projectId = agendaObject[extractedObjectIndex].projectId;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectId")){
                                secondProjectId = agendaObject[extractedObjectIndex].secondProjectId;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("solve360Update")){
                                solve360Update = agendaObject[extractedObjectIndex].solve360Update;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("buildProxies")){
                                buildProxies = agendaObject[extractedObjectIndex].buildProxies;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("includeMetaData")){
                                includeMetaData = agendaObject[extractedObjectIndex].includeMetaData;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("userName")){
                                userName = agendaObject[extractedObjectIndex].userName;
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("accountingCSVFilePath")){
                               accountingCSVFilePath = agendaObject[extractedObjectIndex].accountingCSVFilePath;
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("dealerAddress")){
                                dealerAddress = agendaObject[extractedObjectIndex].dealerAddress;
                                dealerAddress = dealerAddress?dealerAddress.replace(/\n/g, "~"):'';
                            } 
                            
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("mageManufacturer")){
                                mageManufacturer = agendaObject[extractedObjectIndex].mageManufacturer;
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("projectIds")){
                                projectIds = agendaObject[extractedObjectIndex].projectIds;
                                projectIds =  projectIds.split("*");
                            }
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectIdList")){
                                secondProjectIdList = agendaObject[extractedObjectIndex].secondProjectIdList;
                                secondProjectIdList = secondProjectIdList.split("*");
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("uniqueId")){
                                uniqueId = agendaObject[extractedObjectIndex].uniqueId;
                                uniqueId+='-'+Date.now();
                           
                            }

                            if(agendaObject[extractedObjectIndex].hasOwnProperty("companyIds")){
                                console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                                companyIds = agendaObject[extractedObjectIndex].companyIds;
                                companyIds =  companyIds.replace(new RegExp('\\*', 'g'), ',')
                                console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",companyIds);
                                // companyIds = companyIds.replace(/,\s*$/, "");
                            }
                            
                            if(agendaObject[extractedObjectIndex].hasOwnProperty("companyObj")){
                                companyObj = JSON.parse(agendaObject[extractedObjectIndex].companyObj);
                                console.log("companyObj?????????????????????????????????????????????????????????",companyObj);
                            }
                        if(agendaObject[extractedObjectIndex].hasOwnProperty("testData")){
                            testData = agendaObject[extractedObjectIndex].testData;
                        }

                        } catch(err){
                            console.log(err);
                            segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                        }
                    }
                }

                updateSolve360Data = {projectId:projectId, secondProjectId:secondProjectId, userName:userName, solve360Update:solve360Update, thirdPartyUsername:locationId, storeCode: mageStoreCode, dmsType: constants.JOB_TYPE, groupCode:mageGroupCode,projectIds:projectIds,secondProjectIdList:secondProjectIdList,uniqueId:uniqueId,testData:testData,companyObj:companyObj};
                console.log(updateSolve360Data);
                segment.saveSegment(`updateSolve360Data : ${updateSolve360Data}`);

                console.log('projectId:', projectId);
                segment.saveSegment(`projectId : ${projectId}`);

                console.log('secondProjectId:', secondProjectId);
                segment.saveSegment(`secondProjectId : ${secondProjectId}`);

                console.log('userName:', userName);
                segment.saveSegment(`userName : ${userName}`);

                console.log('solve360Update:', solve360Update);
                segment.saveSegment(`solve360Update : ${solve360Update}`);

                console.log('buildProxies:', buildProxies);
                segment.saveSegment(`buildProxies : ${buildProxies}`);
                
                console.log('extractionId:', extractionId);
                segment.saveSegment(`extractionId : ${extractionId}`);

                console.log('accountingCSVFilePath:', accountingCSVFilePath);
                segment.saveSegment(`accountingCSVFilePath : ${accountingCSVFilePath}`);

                console.log('dealerAddress:', dealerAddress);
                segment.saveSegment(`dealerAddress : ${dealerAddress}`);

                console.log('uniqueId:', uniqueId);
                segment.saveSegment(`uniqueId : ${uniqueId}`);

                console.log('companyIds:', companyIds);
                segment.saveSegment(`companyIds : ${companyIds}`);

                console.log('companyObj:', companyObj);
                segment.saveSegment(`companyObj : ${companyObj}`);

 		console.log('testData:', testData);
                segment.saveSegment(`testData : ${testData}`);

                if(mageManufacturer == constants.DEALERBUILT.PORSCHE_STORE_LABEL){
                    isPorscheStore = true;
                } else{
                    isPorscheStore = false; 
                }

                console.log('mageManufacturer:', mageManufacturer);
                segment.saveSegment(`mageManufacturer : ${mageManufacturer}`);

                console.log('isPorscheStore:', isPorscheStore);
                segment.saveSegment(`isPorscheStore : ${isPorscheStore}`);

                let buildProxiesDecider;
                if(buildProxies){
                    buildProxiesDecider = constants.PROCESS_JSON.OPT_BUILD_PROXY_RO; 
                } else{
                    buildProxiesDecider = constants.PROCESS_JSON.OPT_NO_BUILD_PROXY_RO;
                }
                  
                await job.save();
                const processJson = spawn("bash",
                    [
                        constants.PROCESS_JSON.PROCESS_CMD,
                        constants.PROCESS_JSON.OPT_BUNDLE_DIR, constants.PROCESS_JSON.DEALERBUILT_BUNDLE_DIR,
                        constants.PROCESS_JSON.OPT_INPUT_ZIP, path.join(constants.DEALERBUILT_SCHEDULER_ETI_DIR, extractZip),
                        constants.PROCESS_JSON.OPT_ZAP_INPUT,
                        constants.PROCESS_JSON.OPT_PERFORM_ZIP,
                        // constants.PROCESS_JSON.OPT_NO_BUILD_PROXY_RO,
                        buildProxiesDecider,
                        isPorscheStore ? constants.PROCESS_JSON.OPT_PORSCHE_STORE : '',
                        constants.PROCESS_JSON.OPT_DEADLETTER_DIR_PREFIX,
                        constants.DEALERBUILT_DEADLETTER_DIR_PREFIX + '-processed',
                        constants.PROCESS_JSON.OUTPUT_PREFIX,
                        constants.PROCESS_JSON.OUTPUT_PREFIX_VAL, 
                        constants.PROCESS_JSON.ACCOUNTING_FILE,
                        accountingCSVFilePath,
                        constants.PROCESS_JSON.DEALER_ADDRESS,
                        dealerAddress,
                        "--uuid",uniqueId,
                        "--performed-by",userName,
                        "--exception-report",true,
                        "--company_ids",companyIds
                    ], {
                        cwd: constants.PROCESS_JSON.PROCESS_CMD_PATH,
                        env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
                    }).on('error', function (err) {
                        console.log("error ::", err);
                        segment.saveSegment(`error: ${err}`);
                    });
                console.log(`DealerBuilt : Start processing of extraction > ${basename}`);
                segment.saveSegment(`DealerBuilt : Start processing of extraction > ${basename}`);
                segment.saveSegmentFailure(`DealerBuilt : Start processing of extraction > ${basename}`, uniqueFailLogName);
                process.stdin.pipe(processJson.stdin);

                processJson.stdout.on("data", async (data) => {
                    console.log(`stdout: ${data}`);
                    stdOutArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                    await job.touch();

                    data = data.toString('utf8');
                    let processorStatus;

                    if(data.includes('Processor status')){
                         segment.saveSegment('Processor statu for UI',data);
                         console.log('file generated',data.split(':')[1]);
                         processorStatus = data.split(':')[1];
                         segment.saveSegment('processorStatus',processorStatus);
                         await SetProcessJobStatus.setProcessJobStatusForRunningJob(basename,processorStatus);
                       }else{
                       console.log("failed to generate file")
                      }

                    segment.saveSegment(`stdout: ${data}`);
                    segment.saveSegmentFailure(`stdout: ${data}`, uniqueFailLogName);
                });

                processJson.stderr.on("data", async (data) => {
                    console.log(`stderr: ${data}`);
                    stdErrorArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                    await job.touch();
                    segment.saveSegment(`stderr: ${data}`);
                    segment.saveSegmentFailure(`stderr: ${data}`, uniqueFailLogName);
                });

                processJson.on("close", async (code) => {
                    var message = "n/a";
                    var status = false;
                    clearInterval(touch);
                    if (code == constants.STATUS_CODE.SUCCESS) {
                        status = true;
                        message = "Success";
                    } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                        message = "Extraction failed, general death";
                        job.fail(new Error(`Error: ${message}`));
                    } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                        message = "Extraction failed, moved to dead-letter path";
                        job.fail(new Error(`Error: ${message}`));
                    }
                    if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_PROCESSING_FAILED)) {
                        message = "Extraction failed, Zip File Processing Failed,  ";
                        status = false;
                    }
                    var deadLetterPath = `${process.env.DEALERBUILT_WORK_DIR}/dead-letter-processed`;
                    var errResp = `Moving input to dead-letter bin: ${deadLetterPath}`;
                    if (stdOutArray && stdOutArray.includes(errResp)) {
                        message += errResp
                        status = false;
                    }
                    console.log(`DealerBuilt : JSON processing job for Store ${storeName} exited with code ${code}`);
                    segment.saveSegment(`DealerBuilt : JSON processing job for Store ${storeName} exited with code ${code}`);
                    segment.saveSegmentFailure(`DealerBuilt : JSON processing job for Store ${storeName} exited with code ${code}`, uniqueFailLogName)

                    var extractionErrorResponse;
                    var errorWarnningMessage;
                    var warningObj = {};
                    warningObj.scheduled_by = userName;
                        if(testData){
                            warningObj.testData = true;
                            warningObj.userName = userName;
                        }
                    if(extractionId){
                        extractionErrorResponse = await extractionError.displayErrorLogWithSpecific(extractionId, 'DealerBuilt');
                        console.log('extractionErrorResponse:',extractionErrorResponse);
                        if(extractionErrorResponse.status){
                            let resp = JSON.parse(JSON.stringify(extractionErrorResponse.response))
                            let tmpDescritionArray = [];
                            resp.forEach(e => {
                                //tmpDescritionArray.push(e.ro_pull_type+" : "+e.description);
                                tmpDescritionArray.push(e.description);
                                });
                            errorWarnningMessage = tmpDescritionArray.join(", ");
                        }
                    } 
                    console.log('errorWarnningMessage:',errorWarnningMessage);

                    if(errorWarnningMessage){
                        if(errorWarnningMessage.length > 0){
                            warningObj.errorwarningMessage = errorWarnningMessage;
                        }
                    }

                    let failureDirectory = process.cwd() + '/logs/DealerBuilt/failure/';
                    let failurelogFile = failureDirectory + uniqueFailLogName + '.log';

                    var fetchGroupAndStoreName = (job.attrs.data.inputFile).split('-');
                    var groupName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[0] : '';
                    var storeName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[1] : '';
                    
                    var mailTemplateReplacementValues = {
                        dmsType: constants.JOB_TYPE,
                        processTypes: constants.PROCESS_JSON.JOB_NAME,
                        subject: `Process JSON for ${groupName} - ${storeName} Completed`,
                        warningObj: warningObj,
                        thirdPartyUsername: locationId,
                        storeCode: storeName,
                        groupCode: groupName
                    };
                    var mailBody = {
                        fromAddress: appConstants.NOTIFICATION.FROMADDRESS,
                        toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                        ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                        attachedfailurelogFile:failurelogFile
                    }
                   
                    if (status) {
                        var opDataFileEtl = path.join(constants.PROCESS_JSON.DEALERBUILT_ETL_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                        var opDataFileDist = path.join(constants.PROCESS_JSON.DEALERBUILT_DIST_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                        opDataFileEtl = opDataFileEtl.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
                        var outputFile = opDataFileDist + ' & ' + opDataFileEtl;
                        job.attrs.data.outputFile = outputFile;
                        job.attrs.data.status = status;
                        job.attrs.data.message = message;
                        segment.saveSegment(`Job saved to DB ${JSON.stringify(job)}`);
                        segment.saveSegmentFailure(`Job saved to DB ${JSON.stringify(job)}`, uniqueFailLogName);
                        await job.save();
                        done();
                        var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                        mailTemplateReplacementValues.message = displayMessage;
                        mailTemplateReplacementValues.status = 'Success';
                        mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                        // Send notification after process json job completed
                        mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);
                    } else {

                        // Portal update for process json failed
                       const directoryPath = '/home/<USER>/tmp/du-etl-dms-dealerbuilt-extractor-work/scheduler-temp/dealerbuilt-zip-eti/'; 
                       const fileName = basename;                                 
                       const filePath = path.join(directoryPath, fileName);
                       segment.saveSegment(`DEALERBUILT : filePath inpObj Error - ${fileName}`);
                       if (fs.existsSync(filePath)) {
                          fs.unlink(filePath, (err) => {
                              if(err) {
                               segment.saveSegment(`DEALERBUILT : Error deleting file - ${err}`);
                               console.error('Error deleting file:', err);
                               } else {
                               segment.saveSegment(`DEALERBUILT : File deleted successfully - ${filePath}`);
                               console.log('File deleted successfully:', filePath);
                               }
                           });
                        } else {
                            console.log('File does not exist:', filePath);
                        }

                        let todayDate;
                        let attPayload = {};
                        let projectID;
                        let secondProjectID;
                        let inpObjProject;
                        let inpObjSecondProject;
                        let projectIdList;
                        let secondProjectIdList;
                        try{
                        todayDate = new Date().toISOString().slice(0, 10);
                        attPayload = agendaObject[extractedObjectIndex];
                        projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                        projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                        secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                        attPayload['inProjectId'] =  projectID;

                        secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                        attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                        attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                        attPayload.in_retrive_ro_request_on = todayDate;
                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                        console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                        if(secondProjectIdList.length>0){
                            inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                            console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                        }
                        } catch(err){
                        console.log(JSON.stringify(err));
                        segment.saveSegment(`DealerBuilt : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                        }
                        segment.saveSegment(`DealerBuilt : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                        segment.saveSegment(`DealerBuilt : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                        try {
                            segment.saveSegment(`DealerBuilt : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   

                            // let parsedData = JSON.parse(inpObjProject.inData);
                            // let projectIdList =  parsedData.projectIds.split("*");
                            if(projectIdList){
                                 for(const id of projectIdList){
                                    if(id!=undefined && id!=''){
                                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                        portalUpdate.doPayloadAction(inpObjProject);
                                        console.log(`DealerBuilt Schedule portal call with Project Id FAILURE${id}`);
                
                                    }
                                 }
                            } 
                            
                            if(secondProjectIdList.length>0){
                            segment.saveSegment(`DealerBuilt : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                            
                            // let parsedData = JSON.parse(inpObjSecondProject.inData);
                            // let secondProjectIdList =  parsedData.secondProjectIdList.split("*");
                            if(secondProjectIdList){
                                for(const id of secondProjectIdList){
                                   if(id!=undefined && id!=''){
                                    inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                       portalUpdate.doPayloadAction(inpObjSecondProject);
                                       console.log(`DealerBuilt Schedule portal call with Second Project Id FAILURE${id}`);
               
                                   }
                                }
                           } 
                            }
                        } catch(error) {
                            console.log("Error:", error);
                            segment.saveSegment(`DealerBuilt : doPayloadAction Error - ${JSON.stringify(error)}`); 
                        }
                        //code end for portal update for process json failed


                        await job.fail(new Error(`Error: ${message}`));
                        done();
                        var displayMessage = `Failed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                        mailTemplateReplacementValues.message = displayMessage;
                        mailTemplateReplacementValues.status = 'Failed';
                        mailTemplateReplacementValues.subject = `Process JSON for ${groupName} - ${storeName} Failed`;
                        mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                        mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                        mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                        segment.saveSegmentFailure(displayMessage, uniqueFailLogName);
                        // Send notification for failed process json job
                        await segment.sleep(2000);
                        // Send notification for failed process json job
                        mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);
                    }
                    console.log(`Call for next job selection`);
                    segment.saveSegment(`Call method for SharePoint data upload`);
                    segment.saveSegment(`Call for next job selection`);
                    var basenameCheck = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                    var distFile = path.join(constants.PROCESS_JSON.DEALERBUILT_BUNDLE_DIR, basenameCheck)
                    if (status && fs.existsSync(distFile)) {
                        basename = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                        // await distributeFile(basename, null, updateSolve360Data, errorWarnningMessage);
                        await distributeFile(basename, null, updateSolve360Data, warningObj, job.attrs._id);

                    } else {
                        // Process JSON Job Fail ....
                        segment.saveSegment(`Call for next job selection`);
                        await doNextProcess();
                    }
                });
            } else {               
                /**
                * Remove the Initial/recheck schedules
                */
                if(job.attrs.data.operation=="recheck") {
                    job.remove(err => {
                    segment.saveSegment(`Inside Job remove function`);
                    segment.saveSegment(`job: ${JSON.stringify(job)}`);
                    if (!err) {
                        segment.saveSegment(`Job removed successfully`);
                        console.log("Initial/recheck schedule for dealerbuilt Process JSON job successfully removed");
                    } else{
                        segment.saveSegment(`Job removal process have error : ${JSON.stringify(err)}`);
                    }
                  });
              }
              done();
              await doNextProcess();
            }
        });

    return agenda;
}

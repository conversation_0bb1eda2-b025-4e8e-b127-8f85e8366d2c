"use strict";

const constants = require("../constants");
const util = require("../util");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const DealerBuiltJobManager = require("../DealerBuiltJobManager");
const { spawn } = require("child_process");
const moment = require("moment-timezone");
const fs = require("fs");
const segment = require("../../SEGMENT/DealerBuilt/segmentManager");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const { v4: uuidv4 } = require('uuid');
const manageScheduleField = require('../../../../src/common/util');


/**
 * Function to find the unique stores in an array of stores
 */
Array.prototype.unique = function () {
    var a = this.concat();
    for (var i = 0; i < a.length; ++i) {
        for (var j = i + 1; j < a.length; ++j) {
            if (a[i].locationId === a[j].locationId)
                a.splice(j--, 1);
        }
    }
    return a;
};

/**
 * Function to perform DealerBuilt-Extract
 */
module.exports = function DealerBuiltExtractJOB(agenda) {
    console.log(
        `DealerBuilt-Extract job started: JobName: ${constants.DEALERBUILT.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.DEALERBUILT.CONCURRENCY}`
    );
    var storeCode = '';
    let extractionId;
    var LocationId;
    var groupCode; 
    var uniqueFailLogName;
    segment.saveSegment(`DealerBuilt-Extract job started: JobName: ${constants.DEALERBUILT.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.DEALERBUILT.CONCURRENCY}`);
    agenda.define(constants.DEALERBUILT.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.DEALERBUILT.CONCURRENCY },
        async (job, done) => {
            extractionId = job.attrs._id;
            const att = job.attrs.data;
            const storeDataArray = att.storeDataArray.reverse();
            var i = 0;
            var projectId,userName,projectType,secondaryProjectId,secondaryProjectType,inLaborProjectType,inLaborProjectId,inPartsProjectType,inPartsProjectId;
            var solve360Update, buildProxies;
            var warningObj = {};
            let processFileName;
            let groupCodeConfig = '';
            async function extract(att, job) {
                LocationId = att.LocationId;
                groupCode = att.mageGroupCode;
                storeCode = att.mageStoreCode;
                projectId = att.projectId;
                userName = att.userName;
                groupCodeConfig = att.groupCode;
                projectType = att.projectType || null;              
                secondaryProjectType = att.secondaryProjectType || null;
                secondaryProjectId = att.secondProjectId;
                if (projectType && projectType.toLowerCase().startsWith("labor")) {
                    inLaborProjectType = projectType;
                    inLaborProjectId = projectId;
                } else if (secondaryProjectType && secondaryProjectType.toLowerCase().startsWith("labor")) {
                    inLaborProjectType = secondaryProjectType;
                    inLaborProjectId = secondaryProjectId;
                }
                  
                  // Check if projectType or secondaryProjectType starts with "parts"
                  if (projectType && projectType.toLowerCase().startsWith("parts")) {
                    inPartsProjectType = projectType;
                    inPartsProjectId = projectId;
                  } else if (secondaryProjectType && secondaryProjectType.toLowerCase().startsWith("parts")) {
                    inPartsProjectType = secondaryProjectType;
                    inPartsProjectId = secondaryProjectId;
                  }
                solve360Update = att.solve360Update;
                buildProxies = att.buildProxies;
                uniqueFailLogName = LocationId + '-' + storeCode;
                segment.saveSegment(`Dealerbuilt: Extraction Job Started: ${JSON.stringify(att)}`);
                segment.saveSegmentFailure(`DealerBuilt: Extraction Job Started: ${JSON.stringify(att)}`, uniqueFailLogName);
                if (att.locationId && att.startDate && att.endDate) {
                    if (!fs.existsSync(constants.DEALERBUILT_DEADLETTER_DIR_PREFIX + '-extracted')) {
                        fs.mkdirSync(constants.DEALERBUILT_DEADLETTER_DIR_PREFIX + '-extracted');
                    }
                    var options = [
                        constants.DEALERBUILT.PULL_OP,
                        constants.DEALERBUILT.OPT_LOCATION_ID, att.locationId,
                        constants.DEALERBUILT.OPT_SOURCE_ID, att.sourceId,
                        constants.DEALERBUILT.OPT_ACT_STORE_ID, att.activityStoreId,
                        constants.DEALERBUILT.OPT_START_DATE, att.startDate,
                        constants.DEALERBUILT.OPT_END_DATE, att.endDate,
                        constants.DEALERBUILT.OPT_DEADLETTER_DIR_PREFIX,
                        constants.DEALERBUILT_DEADLETTER_DIR_PREFIX + '-extracted',
                        constants.DEALERBUILT.OPT_MAGE_GROUP_CODE, att.mageGroupCode,
                        constants.DEALERBUILT.OPT_MAGE_STORE_CODE, att.mageStoreCode,
                        '--stateCode', att.stateCode
                    ];
                    // The use of --stateCode here is intentional; redirecting via a constant named arguments
                    // to external functions is more annoying than helpful; mainly due to verbosity
                    // repeating "constants" just is and having to namespace the variable ends up being
                    // redundant since the command invocation itself provides the necessary namespacing; once

                    options.push(constants.DEALERBUILT.OPT_ZIP_PATH, att.zipPath ? att.zipPath : constants.DEALERBUILT_SCHEDULER_ETI_DIR);
                    options.push(constants.DEALERBUILT.OPT_ZAP_AFTER_ZIP);

                    options.push(constants.DEALERBUILT.OPT_BUNDLE);
                    options.push(att.jobType);
                    segment.saveSegment(`Dealerbuilt: Extraction Job Started: ${JSON.stringify(att)}`);
                    segment.saveSegmentFailure(`DealerBuilt: Extraction Job Started: ${JSON.stringify(att)}`, uniqueFailLogName);
                    //Mock Server
                    let isMockServer = constants.DEALERBUILT.ENV_MOCKSERVER; 
                    if(isMockServer == "true"){
                        let groupCode = att.mageGroupCode;
                        let sourceFolder = constants.DEALERBUILT.MOCKSERVER_SOURCE_FOLDER_PATH +'dealerbuilt/'; 
                        let destinationFolder = constants.DEALERBUILT.MOCKSERVER_DESTINATION_FOLDER_PATH_DEALERBUILT;
                        //SewellFamilyofCo-FO-TX-INITIAL-28901-20231116065334.zip
                        const mageGroupCodeDIR =  groupCode.replace(/ +/g, "");
                        const mageStorecodeDIR =  storeCode.replace(/ +/g, "");
                        const stateCodeDIR = att.stateCode;
                        const jobTypeDIR = att.jobType ;
                        const enterpriseCodeDir = att.locationId;
                        const zipFileName = mageGroupCodeDIR +'-'+ mageStorecodeDIR +'-'+ stateCodeDIR +'-'+ jobTypeDIR.toUpperCase(); +'-'+ enterpriseCodeDir +'-';
                        console.log('-------zipFileName-------',zipFileName);
                        
                        fs.readdir(sourceFolder, function (err, files) {
                            if (err) {
                                console.log('DEALERBUILT : Unable to scan directory');
                                segment.saveSegment(`DEALERBUILT : Unable to scan directory`,err);
                            } 
                            const matchedResults = [];
                            let searchResult;
                            files.forEach(function (file) {
                                if(file.includes(zipFileName)) {
                                    matchedResults.push(file);
                                }
                            });
                            console.log('DEALERBUILT : files',matchedResults);
                            if(matchedResults.length > 0) {
                                searchResult = (matchedResults.sort().reverse())[0];
                                att.startTime = new Date(moment().utc());
                                fs.copyFile(sourceFolder+searchResult, destinationFolder+'/'+searchResult, (err) => {
                                    if (err) {
                                        console.log('DEALERBUILT : Unable to copy file');
                                        segment.saveSegment(`DEALERBUILT : Unable to copy file`,err);
                                    } else {
                                        att.endTime = new Date(moment().utc());
                                        att.uniqueId = util.generateUniqueId();
                                        att.status = true;
                                        att.mockServer = true;
                                        att.message = "Success";
                                        let oldStoreArray = job.attrs.data.storeDataArray;
                                        let newStoreArray = [att];
                                        oldStoreArray.map(data => {
                                            if (data.enterpriseCode === newStoreArray[0].enterpriseCode) {
                                                data = newStoreArray;
                                            }
                                        });
                                        var _storeArray = oldStoreArray;
                                        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                                        job.attrs.data.storeDataArray = _storeArray;
                                        job.save();
                                        done();
                                    }
                                });
                            }else{
                            let warningObj={};
                            segment.saveSegment(`DEALERBUILT : Test job failed`);
                            console.log('DEALERBUILT : Test job failed');
                            att.startTime = new Date(moment().utc());
                            att.endTime = new Date(moment().utc());
                            att.uniqueId = util.generateUniqueId();
                            att.status = false;
                            att.mockServer = true;
                            att.message = "Failed";
                            job.fail(new Error(`DEALERBUILT :Test job failed`));
                            job.save()
                            
                            let failureDirectory = process.cwd() + '/logs/DealerBuilt/failure/';
                            let failurelogFile = failureDirectory + storeCode + '.log';
                            let mailTemplateReplacementValues = {
                                dmsType: constants.JOB_TYPE,
                                processTypes: constants.PROCESS_JSON.JOB_NAME,
                                subject: `Test Extraction Job for ${groupCode} - ${storeCode} Failed`,
                                warningObj: warningObj,
                                thirdPartyUsername: att.projectId,
                                storeCode: storeCode,
                                groupCode: groupCode
                            };
                            let mailBody = {
                                fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                                toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                                ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                                attachedfailurelogFile:failurelogFile
                            }
                            var displayMessage = `Test Failed ${constants.JOB_TYPE} ${constants.DEALERBUILT.JOB_NAME} job for group ${groupCode} and store ${storeCode}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Failed';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure('Extraction status: Extraction Failed', storeCode);
                            // Send notification after  cdk extraction job completed
                            mailSender.sendMail(mailBody, constants.DEALERBUILT.JOB_NAME);
                        }
                    });
                    } else {
                    const child = spawn(constants.DEALERBUILT.EXTRACT_CMD, options);
                    var startTime = new Date(moment().utc());
                    att.startTime = startTime;
                    var oldStoreArray = job.attrs.data.storeDataArray;
                    var newStoreArray = [att];
                    oldStoreArray.map(data => {
                        if (data.locationId === newStoreArray[0].locationId && data.mageStoreCode === newStoreArray[0].mageStoreCode) {
                            data = newStoreArray;
                        }
                    });
                    var _storeArray = oldStoreArray;
                    // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                    job.attrs.data.storeDataArray = _storeArray;
                    segment.saveSegment(`Extraction Job Data: ${JSON.stringify(_storeArray)}`);
                    segment.saveSegmentFailure(`Extraction Job Data: ${JSON.stringify(_storeArray)}`, uniqueFailLogName);
                    att.uniqueId = util.generateUniqueId();
                    await job.save();
                    process.stdin.pipe(child.stdin)
                    var compareString = "";
                    var status = true;
                    var message = "n/a";
                    var unsubscribedDms = false;

                    child.stdout.on("data", async (data) => {
                        await job.touch();
                        compareString = data.toString('utf8');
                        if (compareString.search("error:") != -1) {
                            message = data.toString('utf8')
                        }
                        console.log(`stdout: ${data}`);
                        data = data.toString('utf8');
                        if (data.includes(" You are not authorized to perform this operation")) {
                            unsubscribedDms=true;
                        }
                         if (data.includes("Output zip file successfully generated @path")) {
                                                                                     
                                                console.log("data",data);
                                                const pathMatch = data.match(/@path: (\/.*?\.zip)/);
                                                console.log("pathMatch",pathMatch);
                                                segment.saveSegment(`pathMatch: ${pathMatch}`);
                                               if (pathMatch && pathMatch[1]) 
                                                   {
                                                    const filePath = pathMatch[1];
                                                    const match = filePath.match(/[^/]+\.zip$/);
                                                       if (match) {
                                                           processFileName = match[0];
                                                           if(processFileName){
                                                            await manageScheduleField.processCompanyData(job, userName, processFileName, filePath, constants.DMS, groupCodeConfig);
                                                            att.processFileName = processFileName.trim();
                                                            await job.save();
                                                        }else{
                                                            processFileName = null;
                                                        }
                                                                                                   
                                                           console.log("Extracted filename:", processFileName);
                                                                                              
                                                       } else {
                                                                                                 
                                                       console.log("Failed to extract filename from path:", filePath);
                                                                                                
                                                   }
                                                    
                                               } else {
                                               
                                                   console.log("Failed to find the file path in the log data");
                                               
                                               }
                                                } else {
                                                console.log("Failed to generate file");
                                              }
                        segment.saveSegment(`stdout: ${data}`);
                        segment.saveSegmentFailure(`stdout: ${data}`, uniqueFailLogName);
                    });

                    child.stderr.on("data", async (data) => {
                        await job.touch();
                        compareString = data.toString('utf8');
                        if (compareString.search("error:") != -1) {
                            message = data.toString('utf8')
                        }
                        console.log(`stderr: ${data}`);
                        segment.saveSegment(`stderr: ${data}`);
                        segment.saveSegmentFailure(`stderr: ${data}`, uniqueFailLogName);
                    });

                    child.on("close", async (code) => {
                        if (code == constants.STATUS_CODE.SUCCESS) {
                            status = true;
                            message = "Success";
                        } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                            status = false;
                            message = "Extraction failed, general death";
                        } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                            status = false;
                            message = "Extraction failed, moved to dead-letter path";
                        }
                        segment.saveSegment(`Job close: ${message}`);
                        segment.saveSegmentFailure(`Job close: ${message}`, uniqueFailLogName);

                        let failureDirectory = process.cwd() + '/logs/DealerBuilt/failure/';
                        let failurelogFile = failureDirectory + uniqueFailLogName + '.log';

                        var groupName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageGroupCode : '';
                        var storeName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageStoreCode : '';

                    
                        var mailTemplateReplacementValues = {
                            dmsType: constants.JOB_TYPE,
                            processTypes: constants.PROCESS_JSON.JOB_NAME,
                            subject: `Extraction Job for ${groupName} - ${storeName} Completed`,
                            warningObj: warningObj
                        };
                        var mailBody = {
                            fromAddress: appConstants.NOTIFICATION.FROMADDRESS,
                            toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                            ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                            attachedfailurelogFile:failurelogFile
                        }
                        
                        if (status) {
                            // Send notification
                            var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.DEALERBUILT.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Success';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure('Extraction status: Extraction completed', uniqueFailLogName);
                            // Send notification after  Dealerbuilt extraction job completed
                            mailSender.sendMail(mailBody, constants.DEALERBUILT.JOB_NAME);
                        } else {
                            // Send notification
                            mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                            mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                            var displayMessage = `Failed ${constants.JOB_TYPE} ${constants.DEALERBUILT.JOB_EXTRACT_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.subject = `Extraction Job for ${groupName} - ${storeName} Failed`;
                            mailTemplateReplacementValues.status = 'Failed';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            if(unsubscribedDms){
                            const UserInput = {
                                inLaborProjectId :inLaborProjectId,
                                inLaborProjectType :inLaborProjectType,
                                inPartsProjectType:inPartsProjectType,
                                inPartsProjectId:inPartsProjectId,                               
                                inIsInSales :true,
                                inSalesComment : appConstants.SALES_TAG_COMMENT,                               
                                inUpdatedBy :userName
                              }
                              segment.saveSegment(`DOMINION Extraction - sales tag creation: ${JSON.stringify(UserInput)}`);
                            try {
                                const SendSalesTagDetails = await manageScheduleField.sendNotificationCall(UserInput,constants.DOMINION.JOB_EXTRACT_NAME);
                                segment.saveSegment(`DOMINION Extraction - sales tag creation: ${JSON.stringify(SendSalesTagDetails)}`);
                              } catch (salesError) {                                
                                segment.saveSegment(`DOMINION Extraction - sales tag creation Error: ${salesError}`);
                              }
                            }
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure('Extraction status: Extraction failed', uniqueFailLogName);
                            // Send notification for failed Dealerbuilt extraction
                            mailSender.sendMail(mailBody, constants.DEALERBUILT.JOB_NAME);

                            // Portal update for extraction failed
                            let todayDate;
                            let attPayload = {};
                            let projectID;
                            let secondProjectID;
                            let inpObjProject;
                            let inpObjSecondProject;
                            let secondProjectIdList;
                            let projectIdList;
                            try{
                            todayDate = new Date().toISOString().slice(0, 10);
                            attPayload = att;
                            projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                            projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                            secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                            segment.saveSegment(`DEALERBUILT : projectIdList - ${projectIdList}`);
                            segment.saveSegment(`DEALERBUILT : secondProjectIdList - ${secondProjectIdList}`);

                            attPayload['inProjectId'] =  projectID;

                            secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                            attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                            attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                            attPayload.in_retrive_ro_request_on = todayDate;
                            // inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectID, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                            // console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                            // if(secondProjectID){
                            //     inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectID, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                            //     console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                            // }
                            } catch(err){
                            console.log(JSON.stringify(err));
                            segment.saveSegment(`DealerTrack : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                            }
                            segment.saveSegment(`DealerTrack : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                            segment.saveSegment(`DealerTrack : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                            try {
                                       
                                if(secondProjectIdList.length>0){
                                    for(let i=0;i<secondProjectIdList.length;i++){
                                        if(secondProjectIdList[i]!=undefined && secondProjectIdList[i]!= ''){
                                            inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList[i], attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                            console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                                            portalUpdate.doPayloadAction(inpObjSecondProject);
                                        }
                                       
                                    }
                                   
                                }
    
                                if(projectIdList.length>0){
                                    for(let i=0;i<projectIdList.length;i++){
                                        if(projectIdList[i]!=undefined && projectIdList[i]!=''){
                                                  
                                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIdList[i], attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                            segment.saveSegment(`DealerTrack : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                                            portalUpdate.doPayloadAction(inpObjProject);
                                        }
                                 
                                    }
                                   
                                }
                            } catch(error) {
                                console.log("Error:", error);
                                segment.saveSegment(`DealerTrack : doPayloadAction Error - ${JSON.stringify(error)}`); 
                            }
                            //code end for portal update for extraction failed
                        }

                        att.endTime = new Date(moment().utc());
                        att.uniqueId ? att.uniqueId : util.generateUniqueId();
                        att.status = status;
                        att.message = message;
                        var oldStoreArray = job.attrs.data.storeDataArray;
                        var newStoreArray = [att];
                        oldStoreArray.map(data => {
                            if (data.locationId === newStoreArray[0].locationId && data.mageStoreCode === newStoreArray[0].mageStoreCode) {
                                data = newStoreArray;
                            }
                        });
                        var _storeArray = oldStoreArray;
                        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                        job.attrs.data.storeDataArray = _storeArray;
                        // if(processFileName){
                        //     att.processFileName = processFileName.trim();
                        // }else{
                        //     processFileName = null;
                        // }
                        await job.save();
                        console.log(`DealerBuilt : Extraction process for store ${att.locationId} exited code ${code}`);
                        segment.saveSegment(`DealerBuilt : Extraction process for store ${att.locationId} exited code ${code}`);
                        i++;
                        if (i < storeDataArray.length) {
                            // Check time frame
                            if (att.runNow) {
                                segment.saveSegment(`DealerBuilt : runNow`);
                                await extract(storeDataArray[i], job);
                            } else if (util.checkExtractTimeFrame()) {
                                segment.saveSegment(`DealerBuilt : Check time frame and start extraction ${JSON.stringify(storeDataArray[i])}`);
                                await extract(storeDataArray[i], job);
                            } else {
                                const newDataArray = storeDataArray;
                                try {
                                    DealerBuiltJobManager.scheduleDealerBuiltExtractJob(DealerBuiltJobManager.createScheduleObject(job, newDataArray.slice(i)), true);
                                    job.attrs.data.storeDataArray = storeDataArray.slice(0, i);
                                    job.fail(new Error(`DealerBuilt : Time exceeded, remaining stores scheduled to next day.`));
                                    segment.saveSegment(`DealerBuilt : Time exceeded, remaining stores scheduled to next day.`);
                                    await job.save();
                                    //done();
                                } catch (error) {
                                    console.error(error);
                                    segment.saveSegment(`Error : ${error.toString()}`);
                                }
                            }
                        } else {
                            done();
                        }
                    });
                }
                } else {
                    console.error("DealerBuilt : Store data Extraction attributes not defined");
                    segment.saveSegment("DealerBuilt : Store data Extraction attributes not defined");
                }
            }
            if (att.runNow) { // Check whether it is for run now or not, if yes, no need to check time frame
                segment.saveSegment(`DealerBuilt : runNow : Check whether it is for run now or not, if yes, no need to check time frame ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else if (util.checkExtractTimeFrame()) {
                segment.saveSegment(`DealerBuilt : Check time frame and start extraction ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else { // Auto schedule full Group wise schedule for tomorrow
                segment.saveSegment(`DealerBuilt : Auto schedule full Group wise schedule for tomorrow`);
                DealerBuiltJobManager.scheduleDealerBuiltExtractJob(DealerBuiltJobManager.createScheduleObject(job), true);
                job.remove();
            }
        });

    agenda.on("start", job => {
        console.log(`DealerBuilt : Job ${job.attrs.name}_${job.attrs._id} starting`);
    });

    agenda.on("complete", job => {
        console.log(`DealerBuilt : Job ${job.attrs.name}_${job.attrs._id} finished`);
    });

    agenda.on("fail", (err, job) => {
        console.log(`DealerBuilt : Job ${job.attrs.name}_${job.attrs._id} failed with error: ${err.message} `);
    });
    return agenda;
}

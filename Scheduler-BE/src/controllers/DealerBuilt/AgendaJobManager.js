const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  var initializeDealerBuiltProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "dealerbuilt-process-json") {
      initializeDealerBuiltProcessJSON = true
    }
    if (type.split('-')[0] == 'dealerbuilt') {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the dealerbuilt-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializeDealerBuiltProcessJSON) {
    try {
      // Initialize DealerBuilt Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("DealerBuilt :  Process JSON schedule started");
      segment.saveSegment("DealerBuilt :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("DealerBuilt Extraction Processing Not Enabled - Pass Job Type dealerbuilt-process-json To Enable");
    segment.saveSegment("DealerBuilt Extraction Processing Not Enabled - Pass Job Type dealerbuilt-process-json To Enable");
  }
  return true;
}

const DealerBuiltJobManager = require("../DealerBuiltJobManager");
const validator = require("validator");

var resolvers = {
  Query: {

    /**
    * Query to get all DealerBuilt-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllDealerBuiltExtractJobs(_, args) {
      return DealerBuiltJobManager.getAllDealerBuiltExtractJobs();
    },

    /**
     * Query to get all Automate Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllDealerBuiltProcessJSONJobs(_, args) {
      return DealerBuiltJobManager.getAllDealerBuiltProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule DealerBuilt-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleDealerBuiltExtractJob(_, args) {
      return DealerBuiltJobManager.scheduleDealerBuiltExtractJob(args.input);
    },

    /**
     * Mutation to run a DealerBuilt-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowDealerBuiltExtractJobByStore(_, args) {
      return DealerBuiltJobManager.runNowDealerBuiltExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a DealerBuilt-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelDealerBuiltExtractJobByStore(_, args) {
      return DealerBuiltJobManager.cancelDealerBuiltExtractJobByStore(args.input);
    },
  },
  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      var parts = value.split("-")
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      var dateStr = JSON.parse(JSON.stringify(ast)).value;
      var parts = dateStr.split("-")
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;

var express = require('express');
var app = express();
var router = express.Router();
var fs = require("fs");
var bodyParser = require('body-parser');
var path = require('path');
const csv = require('csv-parser');
const csvWriter = require('csv-write-stream')
const writer = csvWriter();
const util = require("../common/util");
const agendaDbModel = require("../model/agendaDb");
const payTypeHistory = require('../common/payTypeHistory');
const agenda = require('../controllers/agenda');
let uniqueId;
let dmsType;

const fileTempPath = process.env.PAY_TYPE_TEMP_PATH;// Used for to keep extracted files to a temp directory
const fileName = process.env.PAY_TYPE_FILE_NAME;
const writeJson = (processFilePath, payTypeHistoryArray) => {
    console.log("*******************processFilePath", processFilePath,payTypeHistoryArray);
    return new Promise((resolve, reject) => {
        try {
            var result = [];
            var res = [];
            fs.createReadStream(processFilePath)
                .pipe(csv())
                .on('data', (data) => {
                    console.log("*******************", data);
                    try{
                       // let tmp_pay_type = data['Pay Type'].split(",");Updated due to data format
                        let tmp_pay_type = data['2'].split(",");
                        for (let i = 0; i < tmp_pay_type.length; i++) {
                            let pay_type_tmp;
                            let target_type_tmp;
                            let historyPayType;
                            if(tmp_pay_type[i].trim().charAt(0)=='i' || tmp_pay_type[i].trim().charAt(0)=='I'){
                                pay_type_tmp = 'INTERNAL';
                                target_type_tmp = 'Internal';
                            } else if(tmp_pay_type[i].trim().charAt(0)=='w' || tmp_pay_type[i].trim().charAt(0)=='W') {
                                pay_type_tmp = 'WARRANTY';
                                target_type_tmp = 'Warranty';
                            } else if(tmp_pay_type[i].trim().charAt(0)=='c' || tmp_pay_type[i].trim().charAt(0)=='C') {
                                pay_type_tmp = 'CUSTOMER';
                                target_type_tmp = 'Customer';
                            }
    
                            if(payTypeHistoryArray){
                                if(payTypeHistoryArray.length > 0){
                                    historyPayType = payTypeHistoryArray.filter((element, index) => {
                                        if(element['Pay Type'].trim() == tmp_pay_type[i].trim()){
                                          return element;
                                        }
                                    });
                                }
                            }
                           
                            if(historyPayType){
                                if(historyPayType.length > 0){
                                    let element = historyPayType[0];
                                    result.push({ filename: tmp_pay_type[i].trim(), insurance: element['Insurance'] , target_type: element['Target Type'], selected: true, pay_type: element['Label'], type: "content" });
    
                                } else{
                                    result.push({ filename: tmp_pay_type[i].trim(), insurance: (data['Insurance'] ? data['Insurance'] : 'both'), target_type: (data['Target Type'] ? data['Target Type'] : target_type_tmp), selected: false, pay_type: (data['Label'] ? data['Label'] : ''), type: "content" });
                                }
                            } else{
                                result.push({ filename: tmp_pay_type[i].trim(), insurance: (data['Insurance'] ? data['Insurance'] : 'both'), target_type: (data['Target Type'] ? data['Target Type'] : target_type_tmp), selected: false, pay_type: (data['Label'] ? data['Label'] : ''), type: "content" });
                            }
    
                        }
                    }catch(err){
                        resolve(err);
                    }
                    
                })
                .on('end', () => {
                    res = util._unique(result, 'filename');
                    resolve(res);
                });
        } catch (err) {
            reject(err);
        }
    });
}

router.post("/", async function (req, res, next) {

       var filePath = req.body.filePath;
       let newFilename = filePath.split('/');
       let pathIdentifier =  newFilename[4];
    let dealerId, payTypeHistoryResponse, payTypeHistoryArray;
   
    var tmpArr = filePath.split("/");
    var tmpDirectory = tmpArr[tmpArr.length - 1].replace(".zip", "");
    var newPayTypes = [];
    var existPayTypes = [];
    var distFileName = filePath.split('/').reverse()[0];
    dealerId = distFileName.split('-').reverse()[1];
     if(pathIdentifier =='du-etl-dms-cdk3pa-extractor-work')
     {
        var filePath = path.join(process.env.DIST_DIR, distFileName).trim();
        dmsType ='CDK3PA';

     }
     else if(pathIdentifier == 'du-etl-dms-cdkflex-extractor-work')
     {
        var filePath = path.join('/home/<USER>/tmp/du-etl-dms-cdkflex-extractor-work/manual/dist', distFileName).trim();
        dmsType ='CDKFLEX';
     }

    console.log("FilePath:========================================", filePath);
    console.log("DealerId:======================================", dealerId);
    uniqueId = dealerId;


    try{
        payTypeHistoryResponse =  await payTypeHistory.getPayTypeHistory(dealerId);
        if(payTypeHistoryResponse.status){
            if(payTypeHistoryResponse.response[0]){
                if(payTypeHistoryResponse.response[0].hasOwnProperty("csvContent")){
                    payTypeHistoryArray = payTypeHistoryResponse.response[0].csvContent; 
                }
            }
           
        }
    } catch(err){
        console.log(err);
    }
   

    console.log('###########################################')
    console.log(payTypeHistoryArray);
    console.log('###########################################')
    if (fs.existsSync(filePath)) {
        (async () => {
            await util.unzipFile(filePath);
            console.log("Reading file started");
            // var processFilePath = fileTempPath + tmpDirectory + '/processing-result/' + fileName;
            var processFilePath = `${fileTempPath}/processing-result/${fileName}`;
            // var clearDirectoryPath = fileTempPath + tmpDirectory;
            var clearDirectoryPath = fileTempPath;
            try {
                await writeJson(processFilePath, payTypeHistoryArray).then(result => {
                    util.removeDirForce(clearDirectoryPath);
                    // result[0].type = 'head';
                    result.push({ filename: tmpDirectory, selected: false, type: "fileName" });
                    result.push({ filename: path.dirname(filePath), selected: false, type: "directoryName" });
                    newPayTypes = result;
                    let checkExistDirectory = path.dirname(filePath).trim() + '/' + tmpDirectory.trim() + '.csv';
                    if (fs.existsSync(checkExistDirectory.replace('PROC-', ''))) {
                        console.log('File already exist');
                        console.log(checkExistDirectory);
                        (async () => {
                            await writeJson(checkExistDirectory.replace('PROC-', '')).then(result => {
                                existPayTypes = result;
                                newPayTypes.forEach((e1) => existPayTypes.forEach((e2) => {
                                    if (e1.filename === e2.filename) {
                                        e1.selected = true;
                                        e1.insurance = e2.insurance;
                                        e1.pay_type = e2.pay_type;
                                        e1.from_type = e2.from_type;
                                        e1.target_type = e2.target_type;
                                    }
                                }
                                ));
                            });
                            console.log(newPayTypes);
                            console.log('End of File');
                            res.json({ status: true, message: 'File exist', result: newPayTypes });
                        })();
                    } else {
                        console.log('File not exists!');
                        console.log(newPayTypes);
                        console.log('End of File');
                        res.json({ status: true, message: 'File exist', result: newPayTypes });
                    }
                });
            } catch(err) {
                res.json({ status: true, message: 'File Not exist', result: '' });
            }
        })();
    } else {
        console.log('File not exist');
        res.json({ status: false, message: 'File not exists!', result: '' });
    }
});

router.post('/writeFilesTypes', async function(req, res) {


    let enableAccountingProxy;
    let enabledualProxy;
    var jsonData = req.body;

    jsonData.forEach(function (data) {
        if (data.type == 'accountingProxy') {
            enableAccountingProxy = data.accountingProxy;
        }
    });

    jsonData.forEach(function(data){

        if(data.type == 'dualProxy')
        {
            enabledualProxy = data.dualProxy;
        }
    })

    if(dmsType == 'CDK3PA')
    {
    
        let testAccountingProxy = await agendaDbModel.updateAccountingProxy(uniqueId,enableAccountingProxy,enabledualProxy);
    }
    else if(dmsType == 'CDKFLEX'){

        let testAccountingProxy = await agendaDbModel.updateAccountingProxyForCdkFlex(uniqueId,enableAccountingProxy);
    }
  
    var writeString = '';
    var writeFileName;
    var writeDirectoryName;
    jsonData.forEach(function (data) {
        if (data.type == 'fileName') {
            writeFileName = (data.filename).trim() + '.csv';
        }
        if (data.type == 'directoryName') {
            writeDirectoryName = data.filename + '/';
        }
    });
    try {
        let file_name = writeFileName.replace('PROC-', '').trim();
        var writer = csvWriter({ headers: ["Label", "Pay Type", "Insurance", "From Type", "Target Type"] })
        writer.pipe(fs.createWriteStream(writeDirectoryName + file_name))
        jsonData.forEach(function (data) {
            if (data.selected && data.type != 'filename' && data.type != 'directoryName') {
                let from_type;
                if(data.filename.trim().charAt(0) ==='c' || data.filename.trim().charAt(0) ==='C'){
                    from_type= 'Customer';
                }
                else if(data.filename.trim().charAt(0) ==='w' || data.filename.trim().charAt(0) ==='W'){
                    from_type= 'Warranty';
                }
                else if(data.filename.trim().charAt(0) ==='i' || data.filename.trim().charAt(0) ==='I'){
                    from_type= 'Internal'; 
                }
                writer.write([data.pay_type, data.filename.trim(), data.insurance, from_type, data.target_type]);
            }
        });
        writer.end();
        res.json({ status: true, message: 'Writing finished', fileName: file_name });
    } catch (err) {
        console.error(err);
        res.json({ status: true, message: 'Writing Failed!' });
    }
});

router.post('/updateDealerAddress',async(req,res)=>{
    let mageStoreCode;
    let dealerAddress;
    let dealerId;

    console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>INSIDE UPDATE DEALER ADDRESS>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
    console.log(req.body);
    const data =req.body;

    data.forEach(function (data) {
        if (data.uniqueId) {
             mageStoreCode =data.uniqueId;
        }
        if (data.dealerAddress ) {
            dealerAddress =data.dealerAddress;
        }
        if (data.dealerId ) {
            dealerId =data.dealerId;
        }
    });
    console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>UNIQUE ID>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>');
    console.log('mageStoreCode: ' +mageStoreCode);
    console.log('dealerAddress: ' +dealerAddress);
    console.log('dealerId',dealerId);
    let result = await agendaDbModel.updateDealerAddressForAutomate(dealerId,dealerAddress);

    if(result)
    {
        res.json({status:'success'});
    }
    else
    {
        res.json({status:null});
    }
    // {},
    // { uniqueId: 'IDEAL_GM' },
    // { dealerAddress: 'test address' } ]
  
})


router.post('/updateDealerTrackDealerAddress',async(req,res)=>{
    let mageStoreCode;
    let dealerAddress;
    let dealerId;
    const data =req.body;
   data.forEach(function (data) {
        if (data.uniqueId) {
             mageStoreCode =data.uniqueId;
        }
        if (data.dealerAddress ) {
            dealerAddress =data.dealerAddress.replace(/\n/g, "~");;
        }
        if (data.dealerId ) {
            dealerId =data.dealerId;
        }
    });
    let result = await agendaDbModel.updateDealerAddressForDealerTrack(dealerId,dealerAddress);

    if(result)
    {
        res.json({status:'success'});
    }
    else
    {
        res.json({status:null});
    }
    // {},
    // { uniqueId: 'IDEAL_GM' },
    // { dealerAddress: 'test address' } ]
  
})




module.exports = router;

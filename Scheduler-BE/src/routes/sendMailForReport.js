const fs = require('fs');
const handlebars = require('handlebars');
const appConstants = require('../common/constants');
var API_KEY = process.env.MAILGUN_API_KEY;
var DOMAIN = process.env.MAILGUN_DOMAIN;
const moment = require('moment-timezone');

let  fromAddress = appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER;
let  toAddress = appConstants.NOTIFICATION.TOADDRESS;
let  ccAddress = appConstants.NOTIFICATION.CCADDRESS;

const mailgun = require('mailgun-js')({
    apiKey: API_KEY,
    domain: DOMAIN
});
const path = require('path');

function getCurrentDate() {
    const date = moment.tz("America/New_York");
    const month = date.format('MM');
    const day = date.format('DD');
    const year = date.format('YYYY');

    return `${month}/${day}/${year}`;
}



const sendScheduleReport = (mailBody) => {
       
  return new Promise((resolve, reject) => {
        fs.readFile(path.resolve(__dirname, 'emailTemplate/schedulerReport.html'), 'utf8', (err, templateData) => {
            if (err) {
                console.error(err);
                reject(err);
                return;
            }

         
   const filePath = path.join(__dirname, 'public', 'images', 'Armatus-logo.svg');


          handlebars.registerHelper('eq', function(a, b) {
                return a === b;
            });

            const template = handlebars.compile(templateData);

            let date  = getCurrentDate();
            console.log("current date??????????????????????????????????????????????????/",date);
            mailBody.date = date;
            const data = {
                from: `${fromAddress}`,
                to: `${toAddress}`,
                cc: `${ccAddress}`,
                subject: `Daily Scheduler Status Report ${date}`,
                html: template({data:mailBody}) 
            };
            mailgun.messages().send(data, (error, body) => {
                if (error) {
                    console.error(error);
                    reject(error);
                } else {
                    console.log(body);
                    resolve(body);
                }
            });
        });
    });
}

const sendPendingApprovalReport = (mailBody) => {
    console.log("mailBody&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&",mailBody);
    
   return new Promise((resolve, reject) => {
         fs.readFile(path.resolve(__dirname, 'emailTemplate/pendingApproval.html'), 'utf8', (err, templateData) => {
             if (err) {
                 console.error(err);
                 reject(err);
                 return;
             }
 
          
    const filePath = path.join(__dirname, 'public', 'images', 'Armatus-logo.svg');
 
 
           handlebars.registerHelper('eq', function(a, b) {
                 return a === b;
             });
 
             const template = handlebars.compile(templateData);
 
             const data = {
                 from: `${fromAddress}`,
                 to: `${toAddress}`,
                 cc: `${ccAddress}`,
                 subject: `Pending Make Approval Report – Week of ${moment().format("MMMM D, YYYY")}`,
                 html: template({data:mailBody}) 
             };
             mailgun.messages().send(data, (error, body) => {
                 if (error) {
                     console.error(error);
                     reject(error);
                 } else {
                     console.log(body);
                     resolve(body);
                 }
             });
         });
     });
 }

module.exports = {
     sendScheduleReport:sendScheduleReport,
     sendPendingApprovalReport:sendPendingApprovalReport
    };

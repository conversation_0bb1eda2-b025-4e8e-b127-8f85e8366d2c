var express = require("express");
const path = require("path");
const { promisify } = require('util');
const os = require('os');
const homeDir = os.homedir();
const cron = require('node-cron');
const appConstants = require('../common/constants');
const routes = require('express').Router();
var bodyParser = require("body-parser");
routes.use(bodyParser.json());
routes.use(bodyParser.urlencoded({ extended: true }));
const ENV_PATH = path.join(process.env.HOME + "/.scheduler/.env");
require("dotenv").config({ path: ENV_PATH });
const fs = require('fs');
const { exec } = require('child_process');
const copyFile = promisify(fs.copyFile);
const unlink = promisify(fs.unlink);
const execPromise = promisify(exec);
const { logger } = require("../logger/schedulerLogger");
const rp = require('request-promise');
const doDbConnection = require('./dbConnection');
const sendMailReport = require('./mailImportReminder');
const { generateConfigFile } = require('./configFileGenerator');
executeOnNodeRestart();
// Cron expression to run every 10 seconds
const cronExpression = '*/10 * * * * *';
cron.schedule(cronExpression, async() => {
    await processDumpFile();
    console.log('This job runs every 10 seconds');
});

function buildSchedulerImportQuery(schedulerId, inputData, inPerformedBy, companyId) {
    const formattedInputData = JSON.stringify(inputData).replace(/\\/g, '\\\\').replace(/"/g, '\\"');
    return `mutation{
        addSchedulerIdToProcessQueue(input:{
        inSchedulerId:"${schedulerId}",
        inputData:"${formattedInputData}",
        inCreatedBy:"${inPerformedBy}",
        inCompanyId:"${companyId}"
        })
        {
        json
        }
    }`;
}

function buildSchedulerQueueQuery() {
    return `query {
        getSchedulerIdForImportProcess
    }`;
}

function buildSchedulerStartedImportQuery(inQueueId, inSchedulerId) {
    return `mutation{
        startSchedulerImportProcess(input:{
        inSchedulerId:"${inSchedulerId}",
        inQueueId:"${inQueueId}"
        })
        {
        json
        }
    }`;
}

function buildSchedulerRestartImportQuery() {
    return `mutation{
        restartSchedulerImportProcess(input:{
        })   
        {
        json
        }
    }`;
}

function buildSchedulerCompleteImportQuery(inQueueId, inSchedulerId, inProcessComment, status,companyId) {
    return `mutation{
        schedulerImportProcessQueueUpdation(input:{
        inSchedulerId:"${inSchedulerId}",
        inQueueId:"${inQueueId}",
        inProcessComment:"""${inProcessComment}""",
        inIsCompleted:${status},
        inCompanyId:"${companyId}",
        
        })
        {
        json
        }
    }`;
}

function getOptions(URI, query) {
    return {
        method: 'POST',
        uri: URI,
        body: JSON.stringify({ query: query }),
        headers: {
            'Content-Type': 'application/json',
        },
    };
}

function getTimestamp() {
    const date = new Date();
    return date.toISOString().replace(/[-:]/g, '').replace('T', '_').replace('Z', '');
}
routes.post('/', async(req, res) => {
    req.setTimeout(1800000);
    logger.error(`JOB SAVE`);
    try {
        if (req.body) {
            const { dms, fileName, companyId, inPerformedBy, inPerformedOn, inProjectId, mode, storeName } = req.body;
            const { schedulerId, companyImportId, dumpFileName, showAllStatus, mageGrpData } = req.body;
            const tmpFolderPath = path.join(homeDir, 'scheduler-import-tmp-files'); //'../../src/tmpSchemafile/schema_dump.sql';
            const tmpDumpFolderPath = path.join(tmpFolderPath, `tmpdump_${getTimestamp()}_${companyImportId}_${schedulerId}`);
            // Create importdata object with additional field processkey
            let importdata = {
                    dumpFileName: dumpFileName,
                    tmpDumpFolderPath: tmpDumpFolderPath,
                    companyId: companyImportId,
                    dms: dms,
                    inPerformedBy: inPerformedBy,
                    inPerformedOn: inPerformedOn,
                    projectId: inProjectId,
                    mode: mode,
                    storeName: storeName,
                    showAllStatus: showAllStatus,
                    mageGrpData: mageGrpData,
                    inCreatedBy: inPerformedBy

                }
                ///Mutation for all save
            console.log("companyId@Finish$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$", companyId);
            console.log("companyImportId@Finish$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$", companyImportId);

            const schedulerImportQuery = buildSchedulerImportQuery(schedulerId, importdata, inPerformedBy, companyImportId);
            logger.info(schedulerImportQuery, importdata, "schedulerImportQuery")
            const schedulerImportOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, schedulerImportQuery);
            logger.info(schedulerImportOptions, "schedulerImportOptions==========================")
            rp(schedulerImportOptions)
                .then(async(response) => {
                    logger.info(response, "schedulerImportOptionsresponse==========================")
                    if (!response) {
                        logger.error(`ADD_SCHEDULERID_PROCESS_QUEUE,No response data from GraphQL query`);
                        return res.status(200).json({
                            status: "failed",
                            message: `No response from GraphQL query:
                        ${response}`
                        });
                    }
                    logger.info(JSON.stringify(response), "buildSchedulerImportQueryupdatestatus==============")
                    try {
                        const responseData = JSON.parse(response);
                        if (responseData && responseData.data) {
                            const parsedData = JSON.parse(responseData.data.addSchedulerIdToProcessQueue.json);
                            if (parsedData) {
                                let { status } = parsedData;
                                if (status && status === "success") {
                                    console.log(`ResponseStatus:${responseData,parsedData}`);
                                    logger.info(`ADD_SCHEDULERID_PROCESS_QUEUE Successfully added: ${responseData}`);
                                    /**  ********************Selection status  ************************ */
                                    /** ************************End Section *************************** */
                                    const sourceFolderPath = mode == "Manual" ? appConstants.MANUALFILEIMPORTPATH : '/etl/audit-import-halt';
                                    
                                    const sourceFilePath = path.join(sourceFolderPath, dumpFileName);
                                    const tmpConfigFilePath = path.join(tmpDumpFolderPath, dumpFileName);
                                    if (fs.existsSync(sourceFilePath)) {
                                        if (fs.existsSync(tmpConfigFilePath)) {
                                            console.log(`File ${dumpFileName} already exists in temporary folder. Skipping copy.`);
                                            logger.info(`File ${dumpFileName} already exists in temporary folder. Skipping copy.`);
                                            await unlink(tmpConfigFilePath);
                                        }
                                        if (!fs.existsSync(tmpDumpFolderPath)) {
                                            logger.info("**********tmpDumpFolderPath", tmpDumpFolderPath);
                                            // fs.mkdirSync(tmpDumpFolderPath);
                                            fs.chmodSync(homeDir, '755');
                                            fs.mkdirSync(tmpDumpFolderPath, { recursive: true });
                                        } else {
                                            console.log("((((((((((((((((");
                                        }
                                        try {
                                            await copyFile(sourceFilePath, tmpConfigFilePath);
                                            logger.info(`File ${dumpFileName} copied to temporary folder.`);
                                            const command = `unzip -o ${tmpConfigFilePath} -d ${tmpDumpFolderPath}`;
                                            await execPromise(command);
                                            logger.info(`File ${dumpFileName} unzipped in temporary folder.`);
                                            console.log(`File ${dumpFileName} unzipped in temporary folder.`);
                                        } catch (error) {
                                            console.error('Error copying or unzipping file:', error);
                                            logger.error('Error copying or unzipping file:', error);
                                            return;
                                        }
                                        logger.info({ status: "success", message: 'Request has been queued and should be completed within a few minutes....' });

                                        res.status(200).send({ status: "success", message: 'Request has been queued and should be completed within a few minutes....' });
                                    } else {
                                        res.status(200).send({ status: "failed", message: 'Source file Not Exist.' });
                                        logger.error({ status: "failed", message: 'sourceFilePath Not Exist.' });

                                        console.log({ 'status': 'failed', 'message': 'Source file Not Exist.' });
                                    }

                                } else {
                                    logger.error(`ADD_SCHEDULERID_PROCESS_QUEUE Error response status: ${status}`);
                                    res.status(200).json({ status: "failed", message: `ADD_SCHEDULERID_PROCESS_QUEUE Error response status: ${response}` });
                                }
                            } else {
                                logger.error(`ADD_SCHEDULERID_PROCESS_QUEUE Error response no status: ${parsedData}`);
                            }

                        } else {
                            logger.error(`ADD_SCHEDULERID_PROCESS_QUEUE Error no response no data: ${response}`);
                        }

                    } catch (error) {
                        logger.error(`ADD_SCHEDULERID_PROCESS_QUEUE Error parsing response: ${error}`);
                        res.status(200).send(`Error parsing response: ${error}`);
                    }
                })
                .catch(error => {
                    logger.error(`ADD_SCHEDULERID_PROCESS_QUEUE Error parsing response: ${error}`);
                    console.log(`ADD_SCHEDULERID_PROCESS_QUEUE Error parsing response: ${error}`)
                    res.status(200).json({ status: "failedProcess", errors: [error.message] });
                });

        } else {
            logger.error('Error:Request body is null');
            //res.status(200).send({ 'status': 'failed', 'message': 'Error:Request body is null' });
        }
    } catch (error) {
        logger.error('Error:', error);

        console.error('Error:', error);
        //res.status(200).send({ 'status': 'failed', 'message': 'Error:', error });
    }
});

async function processDumpFile() {

    logger.error(`STEP INSIDE CRON EXECUTION`);
    schedulerQueueImportQuery = buildSchedulerQueueQuery();
    const schedulerQueueImportOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, schedulerQueueImportQuery);
    let queueId;
    let schedulerId;
    let projectId; // Ensure projectId is also accessible in catch block
    let inputData;
    logger.info('schedulerQueueImportOptions:', schedulerQueueImportOptions)

    rp(schedulerQueueImportOptions)
        .then(async(response) => {
            logger.info(`STEP 1 schedulerQueueImportOptions Response: ${response}`);
            if (!response) {
                logger.error(`STEP-ERRORCASE 1 GET_SCHEDULER_IMPORT_QUERY No response data from GraphQL query`);
            } else {
                const responseData = JSON.parse(response);
                const schedulerDataArray = JSON.parse(responseData.data.getSchedulerIdForImportProcess);
                logger.info(`STEP 2 schedulerDataArray`, JSON.stringify(schedulerDataArray));
                // Check if any item has started_on not null and completed_on null
                // Filter the objects where 'started_on' is not null and 'completed_on' is null
                if (schedulerDataArray && schedulerDataArray.length) {
                    let schedulerData = schedulerDataArray.filter(item => item.started_on != null && item.completed_on == null);
                    logger.info(`STEP 3 GET_SCHEDULER_IMPORT_QUERY DATA: ${schedulerDataArray}`);
                    logger.info(`STEP 3.1 GET_SCHEDULER_IMPORT_QUERY DATA: ${schedulerData}`);
                    if (schedulerData.length == 0) { // need to process
                        logger.info(`STEP 4`);
                        let inPerformedBy;
                        let inPerformedOn;
                        let dms;
                        let obj;
                        let companyId;
                        try {
                            obj = schedulerDataArray[0];
                            inputData = JSON.parse(schedulerDataArray[0].input_data);
                            inPerformedBy = inputData.inPerformedBy;
                            inPerformedOn = inputData.inPerformedOn;
                            projectId = inputData.projectId;
                            dms = inputData.dms;
                            companyId = inputData.companyId;

                        } catch (err) {
                            inputData = null;
                            logger.error(`INPUTDATA PARSING FAILED: ${err}`);
                        }
                        logger.info(`STEP 5`, JSON.stringify(inputData));
                        logger.info(`STEP 5.1`, JSON.stringify(obj));
                        if (inputData) {
                            queueId = obj.queue_id;
                            schedulerId = obj.scheduler_id;
                            logger.info(`STEP 6 ${queueId} @ ${schedulerId}`);
                            ///Mutation for all save
                            schedulerStartedImportQuery = buildSchedulerStartedImportQuery(queueId, schedulerId);
                            console.log(schedulerStartedImportQuery, "schedulerImportQuery")
                            const schedulerStartedImportOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, schedulerStartedImportQuery);
                            rp(schedulerStartedImportOptions)
                                .then(async(response) => {
                                    if (!response) {
                                        logger.info(`STEP 6.1 ERROR CASE ${queueId}`);
                                        //need to clear job
                                        logger.error(`ADD_STARTED_SCHEDULERID_PROCESS_QUEUE,No response data from GraphQL query`);
                                        const processComment = `ADD_STARTED_SCHEDULERID_PROCESS_QUEUE,No response data from GraphQL query`;
                                        await mailSendImport(inputData, processComment, queueId, schedulerId, false)
                                        return

                                    }
                                    console.log(JSON.stringify(response), "buildSchedulerStartedImportQueryupdatestatus==============")
                                    try {
                                        const responseData = JSON.parse(response);
                                        logger.info(`STEP 7  ${queueId}   @ ${JSON.stringify(responseData)}`);
                                        if (responseData && responseData.data) {
                                            const parsedData = JSON.parse(responseData.data.startSchedulerImportProcess.json);
                                            logger.info(`STEP 8  ${queueId}   @ ${JSON.stringify(parsedData)}`);
                                            if (parsedData) {
                                                let { status } = parsedData;
                                                if (status && status == "success") {
                                                    logger.info(`STEP 9  ${queueId}`);
                                                    console.log(`ResponseStatus:${responseData,parsedData}`);
                                                    logger.info(`STEP 9.1 ADD_STARTED_SCHEDULERID_PROCESS_QUEUE Successfully added: ${responseData}`);
                                                    completeProcessFile(schedulerId, queueId, inputData);
                                                } else {
                                                    logger.info(`STEP 8.1 ERROR CASE  ${queueId}`);
                                                    const processComment = `ADD_STARTED_SCHEDULERID_PROCESS_QUEUE FAILED Error response status: ${status}`;
                                                    await mailSendImport(inputData, processComment, queueId, schedulerId, false)
                                                    logger.error(`ADD_STARTED_SCHEDULERID_PROCESS_QUEUE FAILED Error response status: ${status}`);
                                                }
                                            } else {
                                                logger.info(`STEP 8.2 ERROR CASE  ${queueId}`);
                                                logger.error(`ADD_STARTED_SCHEDULERID_PROCESS_QUEUE parsedData`);
                                            }
                                        } else {
                                            logger.info(`STEP 8.3 ERROR CASE  ${queueId}`);
                                            logger.error(`ADD_STARTED_SCHEDULERID_PROCESS_QUEUE Error no response no data: ${responseData}`);
                                        }
                                    } catch (error) {
                                        logger.info(`STEP 8.4 ERROR CASE  ${queueId}`);
                                        logger.error(`ADD_STARTED_SCHEDULERID_PROCESS_QUEUE Error parsing response: ${error}`);
                                        const processComment = `ADD_STARTED_SCHEDULERID_PROCESS_QUEUE Error parsing response: ${error}`;
                                        console.log(`Error while creating folder: ${error}`);
                                        logger.info(`Error while creating folder: ${error}`)
                                        await mailSendImport(inputData, processComment, queueId, schedulerId, false)
                                    }
                                })
                                .catch(async error => {
                                    logger.info(`STEP 8.5 ERROR CASE  ${queueId}`);
                                    logger.error(`ADD_STARTED_SCHEDULERID_PROCESS_QUEUE Error parsing response: ${error}`);
                                    const processComment = `ADD_STARTED_SCHEDULERID_PROCESS_QUEUE Error parsing response: ${error}`;
                                    await mailSendImport(inputData, processComment, queueId, schedulerId, false)
                                });
                        } else {
                            logger.error(`INPUTDATA PARSING FAILED : `);
                        }
                    }
                }
            }
        })
        .catch(async error => {
            logger.error(`STEP ERROR CASE 2 GET_SCHEDULER_IMPORT_QUERY FAILED TO LOAD QUERY: ${error}`);
        });
}
async function completeProcessFile(schedulerId, queueId, inputData) {
    const dumpFileName = inputData.dumpFileName;
    // const dumpFileName = inputData.dumpFileName;
    const tmpDumpFolderPath = inputData.tmpDumpFolderPath;
    const companyId = inputData.companyId;
    inputData.sourceCompanyId = companyId;
    let dms = inputData.dms;
    const inPerformedBy = inputData.inPerformedBy;
    const inPerformedOn = inputData.inPerformedOn;
    const projectId = inputData.projectId;
    const mode = inputData.mode;
    const storeName = inputData.storeName;
    const showAllStatus = inputData.showAllStatus;
    // const mageState = inputData.mageGrpData.state;
    // const mageGroupCode = inputData.mageGrpData.mageGroupCode;
    // const mageGroupName = inputData.mageGrpData.mageGroupName;
    // const mageStoreName = inputData.mageGrpData.mageStoreName;
    // const mageProjectId = inputData.mageGrpData.mageProjectId;
    // const mageSecondaryId = inputData.mageGrpData.mageSecondaryId;
    // const mageManufacturer = inputData.mageGrpData.mageManufacturer;
    let schedulerDataId = schedulerId.replace(/-/g, '');
    let schemaName = `du_dms_${schedulerDataId}_${companyId}`;
    logger.info(`STEP 10 completeProcessFile: ${schedulerId} - queueId: ${queueId} - SchemaName: ${schemaName}- companyId: ${companyId}`);
    const tmpFolderPath = path.join(homeDir, 'scheduler-import-tmp-files');
    logger.info("tmpFolderPath:", tmpFolderPath);
    logger.info(`STEP 10.1 ${queueId}   @ ${dumpFileName}`);
    if (dumpFileName) {
        const sourceFolderPath = mode == "Manual" ? appConstants.MANUALFILEIMPORTPATH : '/etl/audit-import-halt';
        console.log(sourceFolderPath, tmpDumpFolderPath, "sourceFilePath")
        const sourceFilePath = path.join(sourceFolderPath, dumpFileName);
        const backupFolderPath = '/etl/audit-import-halt-bkup';
        const destinationFilePath = path.join(backupFolderPath, dumpFileName);
        logger.info("completeProcessFile sourceFilePath", sourceFilePath);
        logger.info(`STEP 11 ${queueId}   @ ${sourceFilePath}   #  ${tmpDumpFolderPath}`);
        if (fs.existsSync(sourceFilePath)) {
            logger.info(`STEP 12 ${queueId}`);
            logger.info("STEP 12.1 completeProcessFile sourceFilePath exist case", sourceFilePath)
            const tmpConfigFilePath = path.join(tmpDumpFolderPath, dumpFileName);
            logger.info("tmpConfigFilePath ===========", sourceFilePath)
            try {
                logger.info(`STEP 13 ${queueId}`);
                if (fs.existsSync(tmpConfigFilePath)) {
                    logger.info(`STEP 13.1 File ${dumpFileName} already exists in temporary folder. Skipping copy.`)
                    await unlink(tmpConfigFilePath);
                }
                if (!fs.existsSync(tmpDumpFolderPath)) {
                    fs.chmodSync(homeDir, '755');
                    fs.mkdirSync(tmpDumpFolderPath, { recursive: true });
                    logger.info(`STEP 13.2 tmpDumpFolderPath already exists in temporary folder. make dir. ${tmpDumpFolderPath}`)
                }
            } catch (err) {
                logger.info(`STEP ERROR CASE 13.2 ${queueId}`);
                const processComment = `Error while creating folder: ${err}`;
                logger.info(`STEP 13.1 Error while creating folder: ${err}`)
                await mailSendImport(inputData, processComment, queueId, schedulerId, false,companyId)
                return;
            }
            try {
                await copyFile(sourceFilePath, tmpConfigFilePath);
                logger.info(`STEP 14 ${queueId} completeProcessFile File ${dumpFileName} copied to temporary folder. ${queueId}`)
                const command = `unzip -o ${tmpConfigFilePath} -d ${tmpDumpFolderPath}`;
                await execPromise(command);
                logger.info(`File ${dumpFileName} unzipped in temporary folder.`);
            } catch (error) {
                logger.info(`STEP 14.1 ERROR CASE ${queueId}`);
                console.error('Error copying or unzipping file:', error);
                logger.error('Error copying or unzipping file:', error);
                const processComment = `Error copying or unzipping file: ${error}`;
                await mailSendImport(inputData, processComment, queueId, schedulerId, false)
                return;
            }
            logger.info(`STEP 15 completeProcessFile tmpDumpFolderPath ${queueId}`, tmpDumpFolderPath);
            const files = fs.readdirSync(tmpDumpFolderPath);
            logger.info(`files to zip: ${files}`)
            logger.info("completeProcessFile FILES", files);
            logger.info(`STEP 15.1 ${queueId} completeProcessFile FILES LIST`, JSON.stringify(files), queueId);

            let pgDumpConfigFile = files.find(file => file.endsWith('.bash'));
            console.log(files, pgDumpConfigFile, "dumpfilesssssssssssssss")
            logger.info(`STEP 15.2 ${queueId} completeProcessFile pgDumpConfigFile ${pgDumpConfigFile}`);
            if (pgDumpConfigFile) {
                logger.info(`STEP 16 ${queueId}`);
                try {
                    if (!fs.existsSync(tmpFolderPath)) {
                        fs.chmodSync(homeDir, '755');
                        fs.mkdirSync(tmpFolderPath, { recursive: true });
                        logger.info(`tmpFolderPath make dir if not exist case: ${tmpFolderPath}`)
                        console.log(`tmpFolderPath make dir if not exist case: ${tmpFolderPath}`)

                    }
                    if (showAllStatus == "showAll") {
                        const newConfigFileName = `config_${companyId}.bash`;
                        inputData.schedulerId = schedulerId; // Assign the desired schedulerId
                            // const currentDate = new Date();
                            // const year = currentDate.getFullYear(); // Get current year
                            // const month = (currentDate.getMonth() + 1).toString().padStart(2, '0'); // Get current month (pad with leading zero if necessary)
                        const configContent = await generateConfigFile(inputData);
                        // Define the content as a string
                        if (!fs.existsSync(tmpDumpFolderPath)) {
                            fs.mkdirSync(tmpDumpFolderPath, { recursive: true });
                            logger.info(`Created missing directory: ${tmpDumpFolderPath}`);
                            console.log(`Created missing directory: ${tmpDumpFolderPath}`);
                        }
                        const oldConfigFilePath = path.join(tmpDumpFolderPath, pgDumpConfigFile);
                        if (fs.existsSync(oldConfigFilePath)) {
                            try {
                                fs.unlinkSync(oldConfigFilePath);
                                console.log('Old config file removed:', oldConfigFilePath);
                                logger.info(`STEP 16.1 Old config file removed: ${oldConfigFilePath}`);
                            } catch (error) {
                                console.error('Error removing old config file:', error);
                                logger.error(`Error removing old config file: ${error.message}`);
                            }
                        }
                        // Create or rename the new config file
                        const newConfigFilePath = path.join(tmpDumpFolderPath, newConfigFileName); // Create the file in tmpDumpFolderPath
                        try {
                            fs.writeFileSync(newConfigFilePath, configContent);
                            pgDumpConfigFile = newConfigFileName; // Assign new config file name to the variable

                            console.log('New config file created:', newConfigFilePath);
                            logger.info(`STEP 16.2 New config file created: ${newConfigFilePath}`);
                            const finalPath = path.join(tmpDumpFolderPath, newConfigFileName);
                            console.log(finalPath, "finalPath=============")
                            if (newConfigFilePath !== finalPath) {
                                fs.renameSync(newConfigFilePath, finalPath);
                                console.log('File moved to:', finalPath);
                                logger.info(`File moved to: ${finalPath}`);
                            }
                        } catch (error) {
                            console.error('Error creating new config file:', error);
                            logger.error(`Error creating new config file: ${error.message}`);
                        }
                    }
                    console.log('pgDumpConfigFile updateddddddddddddddddddddddddd:', pgDumpConfigFile);
                    fs.rename(`${tmpDumpFolderPath}/${pgDumpConfigFile}`, `${tmpFolderPath}/${pgDumpConfigFile}`, async(err) => {
                        logger.info(`STEP 17 ${queueId}`);
                        if (err) {
                            logger.info(`STEP 17.1 ERROR CASE ${queueId}`);
                            console.log({ 'status': 'failed', 'message': 'Error moving file:', err });
                            logger.error(`STEP 17.1 ${queueId} Error moving file:`, err);
                            console.error('Error moving bash file:', err);
                            const processComment = `Error moving bash file: ${err}`;
                            await mailSendImport(inputData, processComment, queueId, schedulerId, false)
                        } else {
                            logger.info(`STEP 18 ${queueId}`);
                            logger.info({ 'status': 'success', 'message': 'Bash file moved successfully.' });
                            logger.info('Bash file moved successfully.', schemaName);
                            const pool = await doDbConnection(schemaName);
                            const dumpFile = files.find(file => path.extname(file) == '.pgdump');
                            const tsvDumpFile = files.find(file => path.extname(file) == '.tsv');
                            if (tsvDumpFile) {
                                logger.info(`STEP 18.0 ${queueId} completeProcessFile Error:TSV dump file found in provided files ${schemaName}`);
                                fs.rename(`${tmpDumpFolderPath}/${tsvDumpFile}`, `${tmpFolderPath}/${tsvDumpFile}`, async(err) => {
                                    if (err) {
                                        logger.info(`STEP 18.0 ${queueId} completeProcessFile Error moving tsvDumpFile  ${schemaName}`);
                                        logger.error(`STEP 18.0 ${queueId} completeProcessFile Error moving tsvDumpFile: ${err}`);
                                    } else {
                                        logger.info(`STEP 18.0 ${queueId} completeProcessFile Move tsvDumpFile successfully  ${schemaName}`);
                                    }
                                });
                            } else {
                                logger.info(`STEP 18.0 ${queueId} completeProcessFile Error: No TSV dump file found in provided files ${schemaName}`);
                            }
                            logger.info(`STEP 18.1 ${queueId} completeProcessFile schemaName ${schemaName}`);
                            logger.info(`STEP 18.2 ${queueId} completeProcessFile dumpFile ${dumpFile}, tmpFolderPath-> ${tmpFolderPath}`);
                            logger.info(`STEP 18.3 ${queueId} === POOL OPTIONS ${JSON.stringify(pool.options)}`);
                            const pgDumpCommand = `PGPASSWORD=${pool.options.password} pg_dump -h ${pool.options.host} -p ${pool.options.port} -U ${pool.options.user} -d ${pool.options.database} -n ${schemaName} --format=custom -f ${tmpFolderPath}/${dumpFile}`;
                            logger.info(`STEP 18.4 ${queueId} completeProcessFile pgDumpCommand ${pgDumpCommand}`);
                            logger.info(`STEP 19 ${queueId}`);
                            exec(pgDumpCommand, async(error, stdout, stderr) => {
                                logger.info(`STEP 20 ${queueId}`);
                                let processComment = '';
                                if (error) {
                                    logger.info(`STEP 20.1 ERROR CASE ${queueId}`);
                                    logger.error(`STEP 20.2 ${queueId} Error: ${error.message}`);
                                    processComment = `Error pgDump: ${error.message}`;
                                    await mailSendImport(inputData, processComment, queueId, schedulerId, false)
                                    return;
                                }
                                if (stderr) {
                                    logger.error(`STEP 20.3 ERROR CASE ${queueId} Error: ${stderr}`);
                                    processComment = `Error pgDump: ${stderr}`;
                                    await mailSendImport(inputData, processComment, queueId, schedulerId, false)
                                    return;
                                }
                                const dumpFilePath = path.join(tmpFolderPath, dumpFile);
                                let tsvFilePath;
                                if (tsvDumpFile) {
                                    logger.info(`completeProcessFile final tsvDumpFile exists ${tmpFolderPath}^^^^^^^^^^^${tsvDumpFile} `);
                                    tsvFilePath = path.join(tmpFolderPath, tsvDumpFile);
                                    // Set the file permissions for the tsv file
                                    try {
                                        fs.chmodSync(tsvFilePath, '755');
                                    } catch (err) {
                                        logger.error(`completeProcessFile final Error setting permissions tsv ${tsvFilePath} `);
                                        console.log(`completeProcessFile final Error setting permissions tsv ${tsvFilePath}:`, err);
                                    }
                                }
                                logger.info(`completeProcessFile final dumpFilePath ${dumpFilePath}=====${tsvFilePath} `);
                                const tmpDumpFilePath = path.join(tmpFolderPath, `${dumpFile}.tmp`);
                                const configFilePath = path.join(tmpFolderPath, pgDumpConfigFile);
                                logger.info(`completeProcessFile dumpFilePath ${dumpFilePath}**${tsvDumpFile}`);
                                logger.info(`completeProcessFile tmpDumpFilePath ${tmpDumpFilePath}`);
                                logger.info(`completeProcessFile configFilePath ${configFilePath}`);
                                const dumpZipFileName = dumpFileName.slice(0, -4);
                                const randomNumber = Math.floor(Math.random() * 10000); // Generates a random number (0-9999)
                                const zipFilePath = `/etl/audit-import/${dumpZipFileName}-${getTimestamp()}-${randomNumber}.zip`;
                                logger.info(`STEP 20.4 ${queueId} completeProcessFile Destination folder created successfully:`, dumpFilePath, tmpDumpFilePath, path.join(process.cwd()));
                                logger.info(`STEP 20.5 ${queueId} : ${zipFilePath}`);
                                // Prepare the list of files to zip
                                const filesToZip = [configFilePath, dumpFilePath];
                                // Add tsvFilePath to the array only if it exists
                                if (tsvFilePath) {
                                    filesToZip.push(tsvFilePath);
                                }
                                const filesToZipString = filesToZip.map(file => `"${file.trim()}"`).join(' ');
                                const zipCommand = `zip -jr ${zipFilePath} ${filesToZipString}`;
                                logger.info(`STEP 21.0 zipCommand  ${zipCommand}****${filesToZip}`);
                                exec(zipCommand, async(error, stdout, stderr) => {
                                    logger.info(`STEP 21  ${queueId}`);
                                    if (error) {
                                        console.log({ 'status': 'failed', 'message': `Error zipping files: ${error.message}` });
                                        logger.error(`STEP 22  ${queueId} Error zipping files: ${error.message}`);
                                        const processComment = `Error zipping files: ${error.message}`;
                                        await mailSendImport(inputData, processComment, queueId, schedulerId, false)
                                        return;
                                    }
                                    if (stderr) {
                                        console.log({ 'status': 'failed', 'message': `stdError: ${stderr}` });
                                        const processComment = `Error zipping files: ${stderr}`
                                        logger.error(`STEP 23  ${queueId} Error zipping files: ${stderr}`);
                                        await mailSendImport(inputData, processComment, queueId, schedulerId, false)
                                        return;
                                    }
                                    const processComment = "File zipped successfully"
                                    logger.info(`STEP 24  ${queueId} Files zipped successfully.`);
                                    scheduleCompleteImport(queueId, schedulerId, processComment, true,companyId)
                                    if (mode != "Manual" ){
                                        try {
                                            // Ensure backup folder exists
                                            if (!fs.existsSync(backupFolderPath)) {
                                                fs.mkdirSync(backupFolderPath, { recursive: true });
                                            }
                                            // Move the file
                                            fs.rename(sourceFilePath, destinationFilePath, (err) => {
                                                logger.info("completeProcessFile destinationFilePath", destinationFilePath);
                                                if (err) {
                                                    logger.error('Error moving backupfile:', err);
                                                    console.log('Error moving backupfile:', err);
                                                } else {
                                                    logger.info(`${dumpFileName} has been moved to ${backupFolderPath}`);
                                                    console.log(`${dumpFileName} has been moved to ${backupFolderPath}`);
                                                }
                                            });
                                        } catch (err) {
                                            logger.error('Error moving the backup file:', err);
                                        }
                                    }
                                    try {
                                        fs.promises.rmdir(tmpDumpFolderPath, { recursive: true });
                                        try {
                                            // List all files in the folder
                                            const files = await fs.readdir(tmpFolderPath);
                                            const folderName = `tmpdump_${getTimestamp()}_${companyId}_${schedulerId}`;
                                            logger.info(`folderName "${folderName}" `)
                                                // Check if the file you want to remove exists
                                            if (files.includes(folderName)) {
                                                const folderPath = path.join(tmpFolderPath, folderName);
                                                // Remove the folder (and its contents, if using Node.js v14.14.0+)
                                                fs.promises.rmdir(folderPath, { recursive: true });
                                                logger.info('Removed files:')
                                                console.log(`Removed file: ${filePath}`);
                                            } else {
                                                logger.info(`File "${folderName}" not found in ${tmpFolderPath}`)
                                            }
                                        } catch (err) {
                                            logger.info(`Error: ${err}`);
                                        }
                                        // fs.promises.rmdir(tmpFolderPath, { recursive: true });
                                    } catch (rmdirError) {
                                        logger.error('STEP 16.1 Error removing directories:', rmdirError)

                                    }
                                });
                                logger.info('Error removing directories tmpFolderPath and tmpDumpFolderPath:', tmpFolderPath, tmpDumpFolderPath)
                            });
                        }
                    });
                } catch (error) {
                    logger.error(`STEP 16.2  ${queueId} Error during zip operation:`, error);
                    const processComment = `Error during zip operation:${error}`;
                    await mailSendImport(inputData, processComment, queueId, schedulerId, false)
                    return;
                }
            } else {
                logger.error('STEP 15.2 Bash file Not Exist.');
                const processComment = 'Bash file Not Exist.';
                await mailSendImport(inputData, processComment, queueId, schedulerId, false)
            }
        } else {
            logger.info(`STEP 12 ERROR CASE ${queueId}`);
            logger.error('sourceFilePath in complete file status Not Exist.');
            const processComment = 'Source file Not Exist.';
            await mailSendImport(inputData, processComment, queueId, schedulerId, false)
        }
    } else {
        logger.info(`STEP 10.1 ERROR CASE ${queueId}`);
        logger.error('Dumpfile  Not Exist.');
        const processComment = 'Source file Not Exist.';
        await mailSendImport(inputData, processComment, queueId, schedulerId, false)
    }
}

function scheduleCompleteImport(queueId, schedulerId, processComment, status,companyId) {
    logger.info("********************scheduleCompleteImport***************************************")
    logger.info(`STEP 26  ${queueId} ${schedulerId}  ${processComment}  ${status} companyId=${companyId}`);
    logger.info("********************scheduleCompleteImport***************************************")
    schedulerCompleteImportQuery = buildSchedulerCompleteImportQuery(queueId, schedulerId, processComment, status,companyId);
    const schedulerCompleteImportOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, schedulerCompleteImportQuery);
    logger.info(`STEP 27  ${queueId} schedulerCompleteImportOptions:`, schedulerCompleteImportOptions)
    try {
        rp(schedulerCompleteImportOptions)
            .then(async(response) => {
                logger.info(`STEP 27.0  ${queueId} schedulerCompleteImportQuery:`, schedulerCompleteImportQuery);
                if (!response) {
                    logger.error(`ADD_COMPLETE_SCHEDULERID_PROCESS_QUEUE,No response data from GraphQL query`);

                }
                logger.info("=================================================")
                logger.info(`STEP 27.1  ${queueId}  buildSchedulerCompleteImportQueryupdatestatus:`, JSON.stringify(response))
                logger.info("=================================================")
                try {
                    const responseData = JSON.parse(response);
                    if (responseData && responseData.data) {
                        const parsedData = JSON.parse(responseData.data.schedulerImportProcessQueueUpdation.json);
                        if (parsedData) {
                            let { status } = parsedData;
                            logger.info(status, "responseData==============")
                            if (status && status == "success") {
                                logger.info(`ADD_COMPLETE_SCHEDULERID_PROCESS_QUEUE Successfully added: ${responseData}`);
                            } else {
                                logger.error(`ADD_COMPLETE_SCHEDULERID_PROCESS_QUEUE Error response status: ${status}`);
                            }
                        } else {
                            logger.error(`ADD_COMPLETE_SCHEDULERID_PROCESS_QUEUE Error no response status: ${parsedData}`);
                        }

                    } else {
                        logger.error(`ADD_COMPLETE_SCHEDULERID_PROCESS_QUEUE Error no response: ${responseData}`);
                    }
                } catch (error) {
                    logger.error(`ADD_COMPLETE_SCHEDULERID_PROCESS_QUEUE Error parsing response: ${error}`);
                }
            })
            .catch(error => {
                logger.error(`ADD_COMPLETE_SCHEDULERID_PROCESS_QUEUE Error parsing response: ${error}`);
            });
    } catch (err) {
        logger.error(err, "ADD_COMPLETE_SCHEDULERID_PROCESS_QUEUE");
    }
}
async function schedulerFailImport(inDms, inProjectId, inPerformedBy, inPerformedOn) {
    logger.info(inDms, inProjectId, inPerformedBy, inPerformedOn, 'schedulerFailImport=====================')
    const createRequestBody = (projectId) => ({
        inData: {},
        inProjectId: projectId,
        inSource: "SchedulerImport",
        inAction: "fail",
        inPerformedOn: inPerformedOn,
        inAssignee: inPerformedBy,
        inPerformedBy: inPerformedBy,
        inDms: inDms,
        inSubmissionId: ""
    });

    const sendApiRequest = async(body) => {
        const apiOptions = {
            headers: {
                'Content-Type': 'application/json'
            },
            url: appConstants.conf.PAYLOAD_URI,
            json: true,
            body: body
        };

        try {
            const apiResponse = await rp(apiOptions);
            logger.info(`POST_PORTAL_API Response from API endpoint: ${JSON.stringify(apiResponse)}`);
            return { status: "success", data: apiResponse };
        } catch (apiError) {
            logger.error(`POST_PORTAL_API Error from API endpoint: ${apiError}`);
            return { status: "error", apiError: apiError.message };
        }
    };
    if (Array.isArray(inProjectId) && inProjectId.length > 0) {
        logger.info("Processing multiple project IDs...");
        const requests = inProjectId.map(projectId => {
            const body = createRequestBody(projectId);
            return sendApiRequest(body);
        });
        const results = await Promise.all(requests);
        logger.info("All API requests processed:", results);
    } else {
        logger.info("Processing a single project ID...");
        const body = createRequestBody(inProjectId);
        const result = await sendApiRequest(body);
        logger.info("Single API request processed:", result);
    }
}

function executeOnNodeRestart() {
    schedulerRestartImportQuery = buildSchedulerRestartImportQuery();
    logger.info(schedulerRestartImportQuery, "schedulerImportQuery")
    const scheduleRestartImportOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, schedulerRestartImportQuery);
    rp(scheduleRestartImportOptions)
        .then(async(response) => {
            if (!response) {
                logger.error(`RESTART_COMPLETE_SCHEDULERID_PROCESS_QUEUE,No response data from GraphQL query`);
            }
            logger.info(JSON.stringify(response), "buildSchedulerRestartQueryupdatestatus==============")
            try {
                const responseData = JSON.parse(response);
                if (responseData && responseData.data) {
                    const parsedData = JSON.parse(responseData.data.restartSchedulerImportProcess.json);
                    if (parsedData) {
                        let { status } = parsedData;
                        if (status && status == "success") {
                            logger.info(`RESTART_SCHEDULERID__PROCESS_QUEUE Successfully restarted: ${responseData}`);
                        } else {
                            logger.error(`RESTART_SCHEDULERID_PROCESS_QUEUE Error response status: ${status}`);
                        }
                    } else {
                        logger.error(`RESTART_SCHEDULERID_PROCESS_QUEUE No response status: ${parsedData}`);
                    }
                } else {
                    logger.error(`RESTART_SCHEDULERID_PROCESS_QUEUE No response : ${responseData}`);
                }
            } catch (error) {
                logger.error(`RESTART_SCHEDULERID_PROCESS_QUEUE Error parsing response: ${error}`);
            }
        })
        .catch(error => {
            logger.error(`RESTART_SCHEDULERID_PROCESS_QUEUE Error parsing response: ${error}`);
        });

}
async function mailSendImport(inputData, processComment, queueId, schedulerId, status) {
    logger.info('STEP  29 last mail sending:', inputData, processComment, queueId, schedulerId, status);

    let inPerformedBy = inputData.inPerformedBy;
    let inPerformedOn = inputData.inPerformedOn;
    let projectId = inputData.projectId;
    let dms = inputData.dms;
    let companyId =inputData.companyId;
    if (projectId) {
        logger.info('STEP  29.1  fail mail sending:', projectId)
        await schedulerFailImport(dms, projectId, inPerformedBy, inPerformedOn)
    }
    scheduleCompleteImport(queueId, schedulerId, processComment, status,companyId)
        //sent mail for fail status
    sendMailReport.runSchedulerReports(inputData);
}
module.exports = routes;

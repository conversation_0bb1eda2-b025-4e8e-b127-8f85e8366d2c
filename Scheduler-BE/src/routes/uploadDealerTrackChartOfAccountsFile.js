const routes = require("express").Router();
const multer = require("multer");
const constants = require("../controllers/DealerTrack/constants");
const segment = require("../controllers/SEGMENT/DealerTrack/segmentManager");

const storage = multer.diskStorage({
    destination: function (req, file, cb) {
      cb(null, constants.CHART_OF_ACCOUNTS_FILE_UPLOAD_DIRECTORY);
    },
    filename: function (req, file, cb) {
      const sanitizedFileName = file.originalname.replace(/\s+/g, "_");
      cb(null, `${Date.now()}_${sanitizedFileName}`);
    },
  });
  
  //var upload = multer({ dest: "uploads/" });
  
var upload = multer({ storage: storage });

routes.post("/", upload.single("file"), function (req, res, next) {
    const file = req.file;
    if (file) {
      segment.saveSegment(`DealerTrack:COA FILE ${req.file} UPLOADED`);
      console.log('coa file?????????????????????????????????????????????????????????????????????????/',req.file);
      res.json(req.file);
    } else{
      segment.saveSegment(`DealerTrack:COA FILE UPLOAD FAILED`);
      throw "error";
    } 
    
});


module.exports = routes;

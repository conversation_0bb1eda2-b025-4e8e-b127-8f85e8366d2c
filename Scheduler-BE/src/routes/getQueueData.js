const routes = require("express").Router();
const fs = require("fs");
const bodyParser = require("body-parser");
const { arch } = require("os");
const { MongoClient } = require('mongodb');
routes.get("/", async function (req, res) {
  try {
    const uri = 'mongodb://localhost:27017/processor_queue';
    const client = new MongoClient(uri);

    try {
      await client.connect();
      const db = client.db('processor_queue');

      const [cdkQueueData, dealerTrackQueueData,reynoldsQueueData,automateQueueData,autoSoftQueueData,dealerBuiltQueue,dominionQueueData,tekionapiQueueData] = await Promise.all([
        db.collection('cdk3pa_queue').find({}).toArray(),
        db.collection('dealertrack_queue').find({}).toArray(),
        db.collection('reynolds_queue').find({}).toArray(),
        db.collection('automate_queue').find({}).toArray(),
        db.collection('autosoft_queue').find({}).toArray(),
        db.collection('dealerbuilt_queue').find({}).toArray(),
        db.collection('dominion_queue').find({}).toArray(),
        db.collection('tekionapi_queue').find({}).toArray()
      ]);
  
      res.json({
        cdkQueueData: cdkQueueData,
        dealerTrackQueueData: dealerTrackQueueData,
        reynoldsQueueData: reynoldsQueueData,
        automateQueueData: automateQueueData,
        autoSoftQueueData: autoSoftQueueData,
        dealerBuiltQueue:dealerBuiltQueue,
        dominionQueueData:dominionQueueData,
        tekionapiQueueData:tekionapiQueueData


      });
    } catch (error) {
      console.error('Error fetching data:', error);
      res.status(500).json({ error: 'Failed to fetch data' });
    } finally {
      await client.close();
    }
    
  } catch (error) {

    res.status(201).send({ status: false, data: JSON.stringify(error) });
  }
});

module.exports = routes;

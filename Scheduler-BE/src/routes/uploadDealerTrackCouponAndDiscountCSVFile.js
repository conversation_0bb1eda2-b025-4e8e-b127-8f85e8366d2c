const routes = require("express").Router();
const multer = require("multer");
const constants = require("../controllers/DealerTrack/constants");
const segment = require("../controllers/SEGMENT/DealerTrack/segmentManager");
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
      cb(null, constants.COUPON_AND_DISCOUNT_CSV_FILE_UPLOAD_DIRECTORY);
    },
    filename: function (req, file, cb) {
      cb(null, `${Date.now()}_${file.originalname}`);
    },
  });
  
  //var upload = multer({ dest: "uploads/" });
  
var upload = multer({ storage: storage });

routes.post("/", upload.single("file"), function (req, res, next) {
    const file = req.file;
    if (file) {
      segment.saveSegment(`DealerTrack:Coupon and discount FILE ${req.file} UPLOADED`);
      res.json(req.file);
    } else 
    {
      segment.saveSegment(`DealerTrack:Coupon and discount FILE UPLOAD FAILED`);
      throw "error";
    }
  
});


module.exports = routes;

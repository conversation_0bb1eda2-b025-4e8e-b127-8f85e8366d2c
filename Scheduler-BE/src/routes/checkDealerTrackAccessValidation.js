const routes = require("express").Router();
const bodyParser = require("body-parser");
routes.use(bodyParser.urlencoded({ extended: false }));
const checkAccessValidationController = require("../controllers/DealerTrack/checkAccessValidation");
routes.post('/', function (req, res) {
    let payLoad = req.body;
    checkAccessValidationController.checkAccessValidation(payLoad, (resObj) =>{
        res.status(201).send(resObj);
    })
});
module.exports = routes;

const express = require("express");
const router = express.Router();
const rp = require("request-promise");
const appConstants = require("../common/constants");

const getUserNameQuery = (schedulerId, companyID) => `
  query {
    getSchedulerPreImportStatusBySchedulerIdCompanyId(
      inSchedulerId: "${schedulerId}",
      inCompanyId: "${companyID}"
    )
  }
`;

const getMutation = ({ in_scheduler_id, in_company_id, in_use_by }) => `
  mutation {
    updateUseByInPreImportStatus(input: {
      inSchedulerId: "${in_scheduler_id}"
      inCompanyId: "${in_company_id}"
      inUseBy: "${in_use_by}"
    }) {
      json
    }
  }
`;


const getGraphQLOptions = (query) => ({
  method: 'POST',
  uri: appConstants.conf.GRAPHQL_SCHEDULER_URI,
  body: { query },
  headers: { 'Content-Type': 'application/json' },
  json: true,
});

const parseGraphQLResponse = (response, key) => {
  try {
    return JSON.parse(response.data[key]);
  } catch (error) {
    console.error("Error parsing GraphQL response:", error.message);
    return null;
  }
};

const getUserName = async (req, res) => {
    try {
        const { schedulerId, companyId, userName } = req.body;
    
        if (!schedulerId || !companyId) {
          return res.status(400).json({
            status: false,
            message: "schedulerId and companyId are required",
          });
        }
    
        const schedulerQuery = getUserNameQuery(schedulerId, companyId);
        const schedulerOptions = getGraphQLOptions(schedulerQuery);
        console.info("Fetching Scheduler Status...");
    
        const response = await rp(schedulerOptions);
        const resultArray = parseGraphQLResponse(response, "getSchedulerPreImportStatusBySchedulerIdCompanyId");
    
        if (Array.isArray(resultArray) && resultArray.length > 0) {
          const { resumed_by, use_by } = resultArray[0];
    
          console.info("Scheduler Status:", { resumed_by, use_by });
         if(use_by){
                 return res.status(200).json({
                    status: true,
                    message: "Scheduler status updated successfully",
                    data: { use_by: use_by },
             });
          }else{ 

            return res.status(200).json({
                status: true,
                message: "Scheduler status updated successfully",
                data: { use_by: null },
         });
             
          }
        }
    
        // }
    
        //   if (!use_by) {
        //     // const schedulerMutation = getMutation({
        //     //   in_scheduler_id: schedulerId,
        //     //   in_company_id: companyId,
        //     //   in_use_by: userName,
        //     // });
    
        //     // const mutationOptions = getGraphQLOptions(schedulerMutation);
        //     // console.info("Updating Scheduler Status...");
    
        //     // await rp(mutationOptions);
    
        //     return res.status(200).json({
        //       status: true,
        //       message: "Scheduler status updated successfully",
        //       data: { use_by: userName },
        //     });
        //   } else {
        //     return res.status(200).json({
        //       status: true,
        //       message: "Scheduler already in use",
        //       data: { use_by },
        //     });
        //   }
        // } else {
        
        // }
      } catch (err) {
        console.error("Error in Scheduler Import Handler:", err.message);
        return res.status(500).json({
          status: false,
          message: "Internal Server Error",
        });
      }

}
// router.post("/getUserName", async (req, res) => {

// });

const updateUserName = async (req, res) => {
    const { schedulerId, companyId, userName } = req.body;

        const schedulerMutation = getMutation({
          in_scheduler_id: schedulerId,
          in_company_id: companyId,
          in_use_by: userName,
        });

        console.log("updateuserName req.body$$$$$$$$$$$$$$$$$$$$",req.body);

        console.log("updateuserName Mutation$$$$$$$$$$$$$$$$$$$$",schedulerMutation);

        const mutationOptions = getGraphQLOptions(schedulerMutation);
        console.info("Updating Scheduler Status...");
         try{
            await rp(mutationOptions);

            return res.status(200).json({
            status: true,
            message: "Scheduler status updated successfully",
            data: { use_by: userName },
          });
  
         }catch(err){
            return res.status(500).json({
                status: true,
                message: "Internal Server Error!",
               
              });
         }
     
  }

module.exports = { getUserName, updateUserName };

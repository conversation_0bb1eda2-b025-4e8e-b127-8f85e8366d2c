const routes = require("express").Router();
const appConstants = require('../common/constants');
const { postgraphile } = require("postgraphile");
const SCHEMA_NAMES = [appConstants.conf.SOLVE360_SCHEMA_NAME];
const DUPORTAL_SCHEMAS = [appConstants.conf.DUPORTAL_SCHEMA_NAME];

routes.use("/", (req, res, next) => {
   console.log('Viewer Custom GraphQL Test');
   delete req.body.operationName; 
   next();

});
routes.use(
  "/",
  postgraphile(appConstants.conf.SOLVE360_DATABASE_CONNECTION_URI, SCHEMA_NAMES, {
    pgSettings: {
      statement_timeout: "1200000",
    },
    externalUrlBase: "/graphqlCustom",
    graphqlRoute: "/",
    graphiql: false,
    graphiqlRoute: "/graphiql",
    watchPg: true,
  })
);

routes.use("/duportal", postgraphile(appConstants.conf.SOLVE360_DATABASE_CONNECTION_URI, DUPORTAL_SCHEMAS, {
  pgSettings: {
    statement_timeout: "1200000",
  },
  externalUrlBase: "/graphqlCustom",
  graphqlRoute: "/",
  graphiql: false,
  graphiqlRoute: "/graphiql",
  watchPg: true,
})
);

module.exports = routes;





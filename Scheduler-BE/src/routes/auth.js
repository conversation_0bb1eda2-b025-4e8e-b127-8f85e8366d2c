const rp = require("request-promise");
const routes = require("express").Router();
const authFilter = require("./filters/authFilter");
const appConstants = require("../common/constants");
const { logger } = require("../logger/schedulerLogger");
routes.use(
 "/",
 (req, res, next) => {
  console.log("req-----------", req);
  if (req.userGroups) {
   console.log("req.userGroups", req.userGroups);
   // build group info query
   let query = authFilter.getGroupInfoQueryString(req.userGroups);
   req.groupInfoQuery = query;
   logger.info(
    `USER_GROUP_INFO_QUERY [${req.jwtToken.message.name}]: Successfully created user group info query`
   );
   next();
  } else {
   logger.error(
    `USER_GROUP_INFO_QUERY [${req.jwtToken.message.name}]: No user groups available:`
   );
   res.status(400).json({ errors: [appConstants.HAS_NO_GROUPS_MESSAGE] });
  }
 },
 (req, res, next) => {
  const options = {
   method: "POST",
   uri: appConstants.conf.SCHEDULER_GRAPHQL_URI,
   body: JSON.stringify({ query: req.groupInfoQuery }),
   headers: {
    "Content-Type": "application/json",
   },
  };
  rp(options)
   .then((success) => {
    console.log("****groupInfoQuery*****req", req);
    console.log("****groupInfoQuery*****res", res);
    logger.info(
     `USER_GROUP_INFO_QUERY [${req.jwtToken.message.name}]: ${success}`
    );
    res.send(success);
   })
   .catch((error) => {
    logger.info(
     `USER_GROUP_INFO_QUERY [${req.jwtToken.message.name}]: `,
     error
    );
    res.status(400).json({
     errors: [
      {
       status: "[ERR: 4001]",
       message: `API call to Scheduler GraphQL failed ${
        error.message ? `(Hint: ${error.message})` : ``
       }`,
      },
     ],
    });
   });
 }
);
module.exports = routes;

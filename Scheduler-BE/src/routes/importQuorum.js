var express = require("express");
const path = require("path");
// const { response } = require("../../app");
const routes = require('express').Router();
var bodyParser = require("body-parser");
routes.use(bodyParser.json());
routes.use(bodyParser.urlencoded({ extended: true }));
// require("dotenv").load();
const ENV_PATH = path.join(process.env.HOME + "/.scheduler/.env");
require("dotenv").config({ path: ENV_PATH });
const doDbConnection = require('./dbConnection'); // Import the function
const { logger } = require("../logger/schedulerLogger");

// Define your Express route
routes.post('/', async(req, res) => {
    req.setTimeout(1800000);
    if (req.body) {
        let requestDataMsg;
        const { schedulerId, companyImportId, operationName } = req.body;
        const { makeDataList } = req.body;
        const { currentMake, newMake } = req.body;
        const makes = [];
        makes.push({ currentMake, newMake });
        const currentMakeList = makes[0].currentMake;
        const newMakeList = makes[0].newMake;
        let schedulerDataId = schedulerId.replace(/-/g, '');
        let schemaName = `du_dms_${schedulerDataId}_${companyImportId}`;
        const { payListData } = req.body;
        const { departmentList } = req.body;
        const { makeListData } = req.body;
        const { selectedPayList } = req.body;
        const { departmentSelList } = req.body;
        const { makeSelListData } = req.body;
        const { sequenceSelListData } = req.body;
        const { inManufacturerList } = req.body;
        const { inMakeList } = req.body;
        const { partPayType } = req.body;
        const { laborPayType } = req.body;

        try {
            // Call the function to establish the database connection with the specified schema
            const pool = await doDbConnection(schemaName);
            // Perform database operations using the pool
            const client = await pool.connect();
            await client.query(`SET search_path TO ${schemaName}`);
            //common select schema result with get all fucntions
            switch (operationName) {
                case "getFetchData":
                    if (schemaName && companyImportId && schedulerId) {
                        sendCommonResponse({ schemaResult: "Connected Schema" }, res);
                    }
                    break;
                case "allManufacturers":
                    const queryAllManufacturers = {
                        text: `SELECT COALESCE(renamed_name,UPPER("Make"))  AS make, count("Make") 
                        FROM  ${schemaName}.quorum_invoicemaster
                        LEFT JOIN ${schemaName}.etl_makerenames_detail ON UPPER("Make") = UPPER(original_name)
                        WHERE NULLIF("Make", '') IS NOT NULL
                        GROUP BY make
                        ORDER BY make`
                    };
                    executeQuery(queryAllManufacturers, "makeAllData", res);
                    break;
                case "storePayType":
                    const queryStorePayType = {
                        text: `WITH store_paytype AS ( 
                            SELECT distinct "Payment Type"  AS paytype FROM quorum_partsdetail 
                            UNION
                            SELECT distinct "Payment Type" As paytype FROM quorum_labordetail
                        )
    
                        SELECT COALESCE(paytype, 'NA')AS paytype,
                                NULL AS description,
                            CASE
                            WHEN paytype ~ '^C' THEN 'Customer'
                            WHEN paytype ~ '^W' THEN 'Warranty'
                            WHEN paytype ~ '^I' THEN 'Internal'
                            ELSE 'Other'
                        END AS base_paytype
                        FROM store_paytype group by paytype`
                    };
                    executeQuery(queryStorePayType, "paytypeAllData", res);
                    break;
                case "allDepartments":
                    const departmentData = [{
                        dept: 'NA',
                        is_allowed: true,
                        description: "desc"
                    }];
                    sendCommonResponse({ departmentAllData: departmentData }, res);
                    break;
                case "addOtherMake":
                    if (currentMakeList && newMakeList) {
                        const insertQueryAddOtherMake = {
                            text: `INSERT INTO etl_makerenames_detail ("original_name", "renamed_name","is_default") VALUES ($1, $2,false);`,
                            values: [currentMakeList, newMakeList]
                        };
                        executeQuery(insertQueryAddOtherMake, "insertOtherMakeData", res);
                    }
                    break;
                case "saveAllMake":
                    try {
                        if (makeDataList) {
                            // First Query: Truncate table
                            const truncateQuery = { text: `TRUNCATE table etl_manufacturer_detail;` };
                            await client.query(truncateQuery);
                            // Second Query: Insert data
                            const insertSaveAllMakeQuery = {
                                text: `INSERT INTO etl_manufacturer_detail (manufacturer, valid_makes_array)
                                    SELECT manufacturer, "makeList" from  json_to_recordset($1::json) as t(manufacturer text, "makeList" text[]);`,
                                values: [JSON.stringify(makeDataList)]
                            };
                            await client.query(insertSaveAllMakeQuery);
                            // Send success response after both queries executed successfully
                            res.status(200).json({ status: "success", message: "Data inserted successfully." });
                            logger.info("Quorum-saveAllMake:Data inserted successfully:");
                            client.release();
                        } else {
                            // Handle case where makeDataList is not defined
                            res.status(200).json({ status: "success", message: "No data to insert." });
                            logger.info("Quorum-saveAllMake:No Data inserted successfully:");
                        }
                    } catch (error) {
                        // Handle error
                        logger.error("Quorum-Error executing queries:", error);
                        res.status(200).json({ status: "error", message: "Error executing queries:", error });
                    }
                    break;
                case "invoiceAllSequence":
                    const queryInvoice = {
                        text: `WITH seq AS ( 
                            select
                            "RO Number" as ro_number,
                            "Date Opened" as open_date,
                            "Date Closed" AS close_date,
                                ( REGEXP_MATCHES("RO Number", '^(\\d+)\\D*$',''))[1] ::integer as ro_num_int
                            FROM quorum_invoicemaster rd 
                            ORDER BY 3, 1
                        )
                        , seq_1 AS (
                            SELECT *,
                                COALESCE(lead(ro_num_int) OVER seq_range > ro_num_int + 100, true) AS shift_seq,
                                COALESCE(lag(ro_num_int) OVER seq_range < ro_num_int - 100, true)  AS new_seq 
                            FROM seq
                            WINDOW seq_range AS (ORDER BY ro_num_int, ro_number) 
                        )
                        , start_end_seq AS (
                            SELECT *
                            FROM seq_1
                            WHERE shift_seq OR new_seq
                        )
                        , wind_seq AS (
                            SELECT
                                ro_number                                              AS start_ro,
                                lead(ro_number) OVER(ORDER BY ro_num_int, ro_number)   AS end_ro,
                                ro_num_int                                             AS start_ro_int,
                                lead(ro_num_int) OVER(ORDER BY ro_num_int, ro_number)  AS end_ro_int,
                                open_date                                              AS start_ro_date,
                                lead(open_date) OVER(ORDER BY ro_num_int, ro_number)   AS end_ro_date,
                                lead(shift_seq) OVER(ORDER BY ro_num_int, ro_number)   AS end_shift_seq,
                                shift_seq,
                                new_seq
                            FROM start_end_seq
                        )
                        , audit_range AS(
                                SELECT
                                    ROW_NUMBER() OVER(ORDER BY start_ro_date, end_ro_date, start_ro_int) AS range_seq,
                                    start_ro,
                                    CASE WHEN shift_seq AND new_seq 
                                        THEN start_ro 
                                    ELSE end_ro END AS end_ro,
                                    start_ro_date,
                                    CASE WHEN shift_seq AND new_seq 
                                        THEN start_ro_date
                                    ELSE end_ro_date END AS end_ro_date
                                FROM wind_seq
                                WHERE new_seq
                                ORDER BY start_ro_date, end_ro_date, start_ro_int
                        )
                        , derivatives3 AS (      
                                SELECT 
                                    GENERATE_SERIES((start_ro :: integer) ,(COALESCE(end_ro, start_ro))::integer) AS ro_number,
                                    start_ro_date, end_ro_date,start_ro,end_ro,range_seq
                                FROM audit_range 
                        )
                        , derivatives4 AS (      
                            SELECT
                                der.range_seq AS range_seq,ci.ro_number,
                                ci.open_date,ci.close_date, der.ro_number AS int_ro_number,der.start_ro_date, der.end_ro_date,der.start_ro,der.end_ro
                            FROM derivatives3 der
                            INNER JOIN seq ci ON  der.ro_number=ci.ro_num_int
                            )

                            SELECT range_seq,der.start_ro,der.end_ro,der.start_ro_date,der.end_ro_date, 
                                jsonb_agg(jsonb_build_object('ro_no', der.ro_number, 'open_date', der.open_date,'close_date', der.close_date)) AS ro_list,
                                jsonb_agg(jsonb_build_object('ro_no', der.ro_number, 'open_date', der.open_date,'close_date', der.close_date))FILTER(where der.ro_number ~ '[a-zA-Z]') AS suffix_ro_list,
                                jsonb_array_length(jsonb_agg(jsonb_build_object('ro_no', der.ro_number, 'open_date', der.open_date,'close_date', der.close_date))) AS cnt_ro_list,
                                jsonb_array_length(jsonb_agg(jsonb_build_object('ro_no', der.ro_number, 'open_date', der.open_date,'close_date', der.close_date))FILTER(where der.ro_number ~ '[a-zA-Z]')) AS cnt_suffix_list
                            FROM derivatives4 der
                            GROUP  BY range_seq,der.start_ro,der.end_ro,der.start_ro_date,der.end_ro_date`
                    };
                    executeQuery(queryInvoice, "invoiceAllData", res);
                    break;
                case "invoiceROSequence":
                    const queryInvoiceRO = {
                        text: `
                        WITH ro_listing AS (
                            SELECT 
                                *,
                                list.number_part - lag(list.number_part) OVER (ORDER BY list.number_part) - 1                  AS gap_size
                            FROM (
                                    SELECT 
                                        rd."RO Number"																			AS ro_number,
                                        length(rd."RO Number")                                                                   AS ro_number_len,
                                        (( SELECT regexp_matches(rd."RO Number", '^(\\d+)'::text) AS regexp_matches))[1]::integer AS number_part,
                                        (( SELECT regexp_matches(rd."RO Number", '(\\D*)$'::text) AS regexp_matches))[1]          AS code_part,
                                        COALESCE(NULLIF((( SELECT regexp_matches(rd."RO Number", '^(\\d*)(\\d{2})(\\D*)$'::text) AS regexp_matches))[1], ''::text), '0'::text) AS prefix_group,
                                        rd."Date Opened"  																			AS open_date,
                                        rd."Date Closed"																			AS close_date
                                    FROM quorum_invoicemaster rd 
                                    ORDER BY "RO Number"
                                )list
                        )
                        , ro_grouping AS (
                            SELECT 
                                ranges.prefix_group,
                                ranges.min,
                                ranges.max,
                                ranges.lag,
                                ranges.lead,
                                ranges.region,
                                ranges.range,
                                (ranges.prefix_group || ranges.range[1])::integer AS range_start,
                                (ranges.prefix_group || ranges.range[2])::integer AS range_end
                            FROM ( 
                                    SELECT 
                                        region.prefix_group,
                                        region.min,
                                        region.max,
                                        region.lag,
                                        region.lead,
                                        region.region,
                                            CASE
                                                WHEN region.region = 'Single'::text THEN ARRAY[region.min, region.max]
                                                WHEN region.region = 'Start'::text THEN ARRAY[region.min, '99'::text]
                                                WHEN region.region = 'End'::text THEN ARRAY['00'::text, region.max]
                                                WHEN region.region = 'Middle'::text THEN ARRAY['00'::text, '99'::text]
                                                ELSE NULL::text[]
                                            END AS range
                                        FROM (
                                                SELECT src.prefix_group,
                                                            src.min,
                                                            src.max,
                                                            src.lag,
                                                            src.lead,
                                                                CASE
                                                                    WHEN src.lag > 1 AND src.lead > 1 THEN 'Single'::text
                                                                    WHEN src.lag > 1 AND src.lead = 1 THEN 'Start'::text
                                                                    WHEN src.lag = 1 AND src.lead > 1 THEN 'End'::text
                                                                    WHEN src.lag = 1 AND src.lead = 1 THEN 'Middle'::text
                                                                    ELSE NULL::text
                                                                END AS region
                                                FROM (
                                                        SELECT 
                                                            rl.prefix_group,
                                                            lpad((min(rl.number_part) - rl.prefix_group::integer * 100)::text, 2, '0'::text) AS min,
                                                            lpad((max(rl.number_part) - rl.prefix_group::integer * 100)::text, 2, '0'::text) AS max,
                                                            COALESCE(rl.prefix_group::integer - lag(rl.prefix_group) OVER (ORDER BY rl.prefix_group)::integer, 999)  AS lag,
                                                            COALESCE(lead(rl.prefix_group) OVER (ORDER BY rl.prefix_group)::integer - rl.prefix_group::integer, 999) AS lead
                                                        FROM ro_listing rl
                                                        GROUP BY rl.prefix_group
                                                    )  src
                                            ) region
                                    ) ranges
                        )
                        , sequence_master AS (
                            SELECT 
                                gs.ro_number::text AS ro_number,
                                gs.ro_number       AS ro_number_int
                            FROM ro_grouping rg,
                            LATERAL generate_series(rg.range_start, rg.range_end) gs(ro_number)
                        )
                        , ro_range AS(
                            SELECT 
                                sm.ro_number,
                                COALESCE(rl.ro_number_len, length(ro_number))             AS ro_num_len,
                                COALESCE(rl.number_part, sm.ro_number_int)                AS number_part,
                                COALESCE(rl.code_part, ''::text)                          AS code_part,
                                rl.gap_size,
                                rl.open_date,
                                rl.close_date,
                                rl.number_part IS NOT NULL AS is_present
                            FROM sequence_master sm
                                FULL JOIN ro_listing rl USING (ro_number)
                        )
                        , ronum_detail AS (
        
                            SELECT
                                (((regexp_matches("RO Number", '^(\\d+)\\D*$')) [1])::integer) AS ro_num
                            FROM quorum_invoicemaster rd
                        )
                        , ro_min_max AS (
        
                            SELECT
                                min(ro_num) AS min_ro_int,
                                max(ro_num) AS max_ro_int
                            FROM ronum_detail rd
                        )
                        , ro_number_integer AS (
        
                            SELECT 
                                generate_series(min_ro_int, max_ro_int) AS ro_int
                            FROM ro_min_max
                        )
                        , ro_number_list AS (
                            SELECT 
                                *,
                                ROW_NUMBER() OVER (ORDER BY COALESCE(rr.number_part, ro_number::integer), code_part) AS sequence
                            FROM ro_range rr
                                LEFT OUTER JOIN ro_number_integer rn
                                    ON rn.ro_int = COALESCE(rr.number_part, rr.ro_number::integer)
                        )
                        , ros_final AS (
                            SELECT 
                                (number_part / 100)         AS ro_group,
                                min(number_part) OVER ()    AS first_ro_num,
                                max(number_part) OVER ()    AS last_ro_num,
                                ro_number,
                                open_date,
                                close_date,
                                number_part,
                                code_part,
                                is_present,
                                open_date IS NOT NULL AND close_date IS NULL     AS is_open,
                                open_date IS NOT NULL AND close_date IS NOT NULL AS is_closed,
                                open_date IS NULL AND close_date IS NULL         AS is_void
                            FROM ro_number_list
                        )
                        , ro_counts_summary AS (
                            SELECT 
                                sum(CASE WHEN is_present AND code_part = ''
                                        THEN 1
                                    ELSE 0 END)                               AS present_ro_cnt,
                                sum(CASE WHEN is_present
                                        THEN 0
                                    ELSE 1 END)                               AS missing_ro_cnt,
                                sum(CASE WHEN code_part <> ''
                                        THEN 1
                                    ELSE 0 END)                               AS bonus_ro_cnt,
                                max(number_part::integer) -
                                min(number_part::integer) + 1                 AS total_ro_cnt,
                                min(number_part)                              AS start_ro,
                                max(number_part)                              AS end_ro,
                                array_agg(DISTINCT CASE WHEN NOT is_present
                                                        THEN number_part
                                                    ELSE NULL END)            AS missing_ros,
                                min(open_date)                                AS min_open_date,
                                max(open_date)                                AS max_open_date,
                                count(ro_number) FILTER(WHERE is_open)        AS open_ro_count,
                                count(ro_number) FILTER(WHERE is_closed)      AS closed_ro_count,
                                count(ro_number) FILTER(WHERE is_void)        AS void_ro_count,
                                ro_group
                            FROM ros_final
                            GROUP BY  ro_group
                            ORDER BY  ro_group
                        )
                        select * from ro_counts_summary;`
                    };
                    executeQuery(queryInvoiceRO, "invoiceROData", res);
                    break;
                case "payListData":
                    try {
                        const jsonData = payListData;
                        if (jsonData) {
                            const truncateAllPayTypeQuery = {
                                text: `TRUNCATE table etl_paytype_detail;`
                            };
                            await client.query(truncateAllPayTypeQuery);
                            let paytypeArray = [];
                            // Main logic
                            for (const obj of payListData) {
                                const categoryFirstChar = obj.category ? (obj.category === 'WARRANTY' ? 'W' : obj.category[0]) : '';
                                let paytypeParts = {
                                    'type': categoryFirstChar,
                                    'paytype': obj.paytype,
                                    'project_type': "parts",
                                    'store': obj.company_id,
                                    'is_allowed': obj.is_parts_allowed ? true : false,
                                    is_default: true
                                };
                                paytypeArray.push(paytypeParts);
                                let paytypeLabor = {
                                    'type': categoryFirstChar,
                                    'paytype': obj.paytype,
                                    'project_type': "labor",
                                    'store': obj.company_id,
                                    'is_allowed': obj.is_labor_allowed ? true : false,
                                    is_default: true
                                };
                                paytypeArray.push(paytypeLabor);

                            }
                            const insertAllPayTypeQuery = {
                                text: `INSERT INTO etl_paytype_detail (type, paytype, project_type, store, is_allowed, is_default)
                                            SELECT json_data->>'type' AS type, 
                                            json_data->>'paytype' AS paytype,
                                            json_data->>'project_type' AS project_type,
                                            json_data->>'store' AS store,
                                            (json_data->>'is_allowed')::boolean AS is_allowed,
                                            (json_data->>'is_default')::boolean AS is_default
                                            FROM json_array_elements($1) AS json_data;`,
                                values: [JSON.stringify(paytypeArray)]
                            };
                            await client.query(insertAllPayTypeQuery);
                            res.status(200).json({ status: "success", message: "Data inserted successfully." });
                            logger.info("Quorum-payListData: Data inserted successfully:");
                            client.release();
                        } else {
                            // Handle case where jsonData is not defined
                            res.status(200).json({ status: "success", message: "No data to insert." })
                            logger.info("Quorum-payListData: No data to insert.");
                        }
                    } catch (error) {
                        // Handle error
                        res.status(200).json({ status: "error", message: "Error executing queries:", error });
                        logger.error("Quorum-Error executing queries:error");

                    }
                    break;
                case "allDepartmentsList":
                    const jsonDepartmentData = departmentList;
                    try {
                        if (jsonDepartmentData) {
                            const truncateQuery = { text: `TRUNCATE table etl_department_detail;` };
                            await client.query(truncateQuery)

                            let departmentDataList = [];
                            // Main logic
                            for (const obj of jsonDepartmentData) {
                                let deptData = {
                                    'department_name': obj.dept,
                                    'is_allowed': obj.isAllowed ? true : false,
                                    'is_default': true
                                };
                                departmentDataList.push(deptData);
                            }
                            const insertAllDepartmentQuery = {
                                text: `INSERT INTO etl_department_detail (department_name, is_allowed, is_default)
                                        SELECT json_data->>'department_name' AS department_name, 
                                        (json_data->>'is_allowed')::boolean AS is_allowed,
                                        (json_data->>'is_default')::boolean AS is_default
                                        FROM json_array_elements($1) AS json_data;`,
                                values: [JSON.stringify(departmentDataList)]
                            };
                            await client.query(insertAllDepartmentQuery);
                            // Send success response
                            res.status(200).json({ status: "success", message: "Data inserted successfully." });
                            logger.info("Quorum-Data inserted successfully");
                            client.release();
                        }
                    } catch (error) {
                        // Handle error
                        res.status(200).json({ status: "error", message: "Error executing queries:", error });
                        logger.error("Quorum-Error executing queries:", error);
                    }
                    break;
                case "allMakesList":
                    try {
                        const jsonMakesData = makeListData;
                        if (jsonMakesData) {
                            // Truncate Query
                            const truncateQuery = { text: `TRUNCATE table etl_all_manufacturer_detail;` };
                            await client.query(truncateQuery)
                                // Insert Query
                            let makeDataList = [];
                            for (const obj of jsonMakesData) {
                                let makeData = {
                                    'manufacturer': obj.manufacturer,
                                    'valid_makes_array': obj.makeList.join(', '),
                                };
                                makeDataList.push(makeData);
                            }
                            const insertAllManufacturerQuery = {
                                text: `INSERT INTO etl_all_manufacturer_detail (manufacturer, valid_makes_array)
                                SELECT manufacturer, string_to_array(valid_makes_array, ', ') from  json_to_recordset($1::json) as t(manufacturer text, valid_makes_array text);`,
                                values: [JSON.stringify(makeDataList)]
                            };
                            //await executeQuery(insertAllManufacturerQuery, "insertManufacturerListData", res);
                            await client.query(insertAllManufacturerQuery);
                            const updateMakeListQuery = {
                                text: `
                                WITH updates AS (
                                    SELECT manufacturer, string_to_array(valid_makes_array, ', ') AS valid_makes_array
                                    FROM json_to_recordset($1::json) AS t(manufacturer text, valid_makes_array text)
                                  )
                                  UPDATE etl_manufacturer_detail
                                  SET valid_makes_array = updates.valid_makes_array
                                  FROM updates
                                  WHERE etl_manufacturer_detail.manufacturer = updates.manufacturer;
                                  `,
                                values: [JSON.stringify(makeDataList)]
                            };
                            //await executeQuery(insertAllManufacturerQuery, "insertManufacturerListData", res);
                            await client.query(updateMakeListQuery);
                            // Send success response
                            res.status(200).json({ status: "success", message: "Data inserted successfully." });
                            logger.info("Quorum-allMakesList:Data inserted successfully");
                            client.release();
                        } else {
                            // Handle case where jsonMakesData is not defined
                            res.status(200).json({ status: "success", message: "No data to insert." });
                            logger.info("Quorum-allMakesList:No data to insert.");

                        }
                    } catch (error) {
                        // Handle error
                        res.status(200).json({ status: "error", message: "Error executing queries:", error });
                        logger.error("Quorum-Error executing queries:", error);
                    }
                    break;
                case "addMakeList":
                    try {
                        if (inManufacturerList && inMakeList) {
                            const updateUnassignMakeQuery = {

                                text: `WITH updates AS (
                                    SELECT manufacturer, (valid_makes_array) AS valid_makes_array
                                    FROM ${schemaName}.etl_all_manufacturer_detail
                                    WHERE manufacturer = $1
                                )
                                UPDATE ${schemaName}.etl_all_manufacturer_detail
                                SET valid_makes_array = (SELECT array_agg(makes)
                                                         FROM (SELECT unnest(u.valid_makes_array::text[]) as makes
                                                               UNION
                                                               SELECT unnest(ARRAY[$2]))sub)
                                FROM updates u
                                WHERE ${schemaName}.etl_all_manufacturer_detail.manufacturer = $1
                                RETURNING *;`,
                                values: [inManufacturerList, inMakeList]
                            };
                            await client.query(updateUnassignMakeQuery);
                            const updateQuery = {
                                text: `
                                    WITH updates AS (
                                        SELECT manufacturer, (valid_makes_array) AS valid_makes_array
                                        FROM etl_manufacturer_detail
                                        WHERE manufacturer = $1
                                    )
                                    UPDATE etl_manufacturer_detail
                                    SET valid_makes_array = (SELECT array_agg(makes)
                                                             FROM (SELECT unnest(u.valid_makes_array::text[]) as makes
                                                                   UNION
                                                                   SELECT unnest(ARRAY[$2]))sub)
                                    FROM updates u
                                    WHERE etl_manufacturer_detail.manufacturer = $1
                                    RETURNING *;
                                `,
                                values: [inManufacturerList, inMakeList]
                            };
                            await client.query(updateQuery);
                            logger.info("Quorum-addMakeList: Data inserted successfully.");
                            res.status(200).json({ status: "success", message: "Data inserted successfully." });
                            client.release();
                        } else {
                            // Handle case where jsonMakesData is not defined
                            logger.error("Quorum-addMakeList:No data to insert.");
                            res.status(200).json({ status: "success", message: "No data to insert." });
                        }
                    } catch (error) {
                        // Handle error
                        logger.error("Quorum-addMakeList: Error executing queries:", error);
                        res.status(200).json({ status: "error", message: "Error executing queries:", error });
                    }
                    break;
                case "allSelSavePaytype":
                    const jsonSelData = selectedPayList;
                    try {
                        if (jsonSelData) {
                            let insertPayListSelQuery = '';
                            const truncateSelPayTypeQuery = {
                                text: `TRUNCATE table etl_paytype_detail;`
                            };
                            await client.query(truncateSelPayTypeQuery);
                            let paytypeSelArray = [];
                            // Main logic
                            for (const obj of selectedPayList) {
                                const categoryFirstChar = obj.category ? (obj.category === 'WARRANTY' ? 'W' : obj.category[0]) : '';
                                let paytypeSelParts = {
                                    'type': categoryFirstChar,
                                    'paytype': obj.paytype,
                                    'project_type': "parts",
                                    'store': obj.company_id,
                                    'is_allowed': obj.is_parts_allowed ? true : false,
                                    is_default: true
                                };
                                paytypeSelArray.push(paytypeSelParts);
                                let paytypeSelLabor = {
                                    'type': categoryFirstChar,
                                    'paytype': obj.paytype,
                                    'project_type': "labor",
                                    'store': obj.company_id,
                                    'is_allowed': obj.is_labor_allowed ? true : false,
                                    is_default: true
                                };
                                paytypeSelArray.push(paytypeSelLabor);
                            }
                            const insertSelPayTypeQuery = {
                                text: `INSERT INTO etl_paytype_detail (type, paytype, project_type, store, is_allowed, is_default)
                                       SELECT json_data->>'type' AS type, 
                                         json_data->>'paytype' AS paytype,
                                        json_data->>'project_type' AS project_type,
                                        json_data->>'store' AS store,
                                        (json_data->>'is_allowed')::boolean AS is_allowed,
                                       (json_data->>'is_default')::boolean AS is_default
                                       FROM json_array_elements($1) AS json_data;`,
                                values: [JSON.stringify(paytypeSelArray)]
                            };
                            await client.query(insertSelPayTypeQuery);
                            res.status(200).json({ status: "success", message: "Data inserted successfully." });
                            logger.info("Quorum-allSelSavePaytype:Data inserted successfully.");
                            client.release();
                        } else {
                            res.status(200).json({ status: "success", message: "No data to insert." });
                            logger.info("Quorum-allSelSavePaytype:No data to insert.");
                        }
                    } catch (error) {
                        // Handle error
                        res.status(200).json({ status: "error", message: "Error executing queries:", error });
                        logger.error("Quorum-Error executing queries:", error);
                    }
                    break;
                case "selDepartmentsList":
                    const jsonSelDeptData = departmentSelList;
                    try {
                        if (jsonSelDeptData) {
                            const truncateDeptQuery = {
                                text: `TRUNCATE table etl_department_detail;`
                            };
                            await client.query(truncateDeptQuery);
                            let departmentSelDataList = [];
                            // Main logic
                            for (const obj of jsonSelDeptData) {
                                let deptData = {
                                    'department_name': obj.dept,
                                    'is_allowed': obj.is_allowed,
                                    'is_default': true
                                };
                                departmentSelDataList.push(deptData);
                            }


                            // Step 1: Filter out objects with department_name set to undefined
                            const filteredDeptList = departmentSelDataList.filter(item => item.department_name !== undefined);

                            // Step 2: Remove duplicates based on department_name
                            const uniqueDeptList = Array.from(new Set(filteredDeptList.map(item => item.department_name)))
                                .map(department_name => {
                                    return filteredDeptList.find(item => item.department_name === department_name);
                                });
                            const insertSelDepartmentQuery = {
                                text: `INSERT INTO etl_department_detail (department_name, is_allowed, is_default)
                                    SELECT json_data->>'department_name' AS department_name, 
                                    (json_data->>'is_allowed')::boolean AS is_allowed,
                                    (json_data->>'is_default')::boolean AS is_default
                                    FROM json_array_elements($1) AS json_data;`,
                                values: [JSON.stringify(uniqueDeptList)]
                            };
                            await client.query(insertSelDepartmentQuery);
                            res.status(200).json({ status: "success", message: "Data inserted successfully." });
                            logger.info("Quorum-selDepartmentsList:Data inserted successfully.");
                            client.release();
                        }
                    } catch (error) {
                        // Handle error
                        res.status(200).json({ status: "error", message: "Error executing queries:", error });
                        logger.error("Quorum-selDepartmentsList-Error executing queries:", error);
                    }
                    break;
                case "makeSelList":
                    try {
                        const jsonMakeListData = makeSelListData;
                        if (!jsonMakeListData) {
                            res.status(200).json({ status: "success", message: "No data provided." });
                            logger.error("Quorum-makeSelList-No data provided.");
                            return;
                        }
                        // Truncate query
                        const truncateQuery = { text: `TRUNCATE table etl_manufacturer_detail;` };
                        // await executeQuery(truncateQuery, "truncateManufacturerSelListData", res);
                        await client.query(truncateQuery);
                        // Insert query
                        let makeDataSelList = [];
                        for (const obj of jsonMakeListData) {
                            let makeData = {
                                'manufacturer': obj.manufacturer,
                                'valid_makes_array': obj.makeList.join(', '),
                            };
                            makeDataSelList.push(makeData);
                        }
                        const insertSelManufacturerQuery = {
                            text: `INSERT INTO etl_manufacturer_detail (manufacturer, valid_makes_array)
                                SELECT manufacturer, string_to_array(valid_makes_array, ', ') from  json_to_recordset($1::json) as t(manufacturer text, valid_makes_array text);`,
                            values: [JSON.stringify(makeDataSelList)]
                        };
                        await client.query(insertSelManufacturerQuery);
                        // Send success response
                        res.status(200).json({ status: "success", message: "Data inserted successfully." });
                        logger.info("Quorum-makeSelList-Data inserted successfully.");
                        client.release();
                    } catch (error) {
                        // Handle error
                        res.status(200).json({ status: "error", message: "Error executing queries:", error });
                        logger.error("Quorum-makeSelList-Error executing queries:", error);

                    }
                    break;
                case "InvoiceSeqSelList":
                    const jsonSeqListData = sequenceSelListData;
                    try {
                        if (jsonSeqListData) {
                            const truncateSeqSelQuery = {
                                text: `TRUNCATE table etl_sequence_detail;`
                            };
                            await client.query(truncateSeqSelQuery);
                            let invoiceSelDataList = [];

                            // Main logic
                            for (const obj of jsonSeqListData) {
                                let invoiceData = {
                                    'start_ro': obj.start_ro,
                                    'end_ro': obj.end_ro,
                                };
                                invoiceSelDataList.push(invoiceData);
                            }
                            const insertSelInvoiceQuery = {
                                text: `INSERT INTO etl_sequence_detail (start_ro, end_ro)
                                SELECT json_data->>'start_ro' AS start_ro, 
                                json_data->>'end_ro' AS end_ro
                                FROM json_array_elements($1) AS json_data`,
                                values: [JSON.stringify(invoiceSelDataList)]
                            };
                            await client.query(insertSelInvoiceQuery);
                            res.status(200).json({ status: "success", message: "Data inserted successfully." });
                            logger.info("Quorum-InvoiceSeqSelList-Data inserted successfully.");
                            client.release();
                        }
                    } catch (error) {
                        // Handle error
                        res.status(200).json({ status: "error", message: "Error executing queries:", error });
                        logger.error("Quorum-InvoiceSeqSelList-Error executing queries: ", error);
                    }
                    break;
                case "PayAllPartsList":
                    const queryPartPayType = {
                        text: `SELECT p."RO Number" as ro_number, "Part Number" as part_no,  "Part Description" as part_desc, "Qty Sold" as quantity,
                        "Part Unit Price" as sale, "Part Unit Cost" as cost, p."Payment Type" as pay_type
                        FROM ${schemaName}.quorum_partsdetail p
                        where lower(p."Payment Type") = LOWER('${partPayType}') AND "Part Unit Cost"::numeric <> 0 AND  "Qty Sold"::numeric <> 0;`
                    };
                    executeQuery(queryPartPayType, "paytypeAllPartListData", res);
                    break;
                case "PayAllLaborList":
                    const queryLaborPayType = {
                        text: `SELECT "RO Number" as ro_number,"Op Code" as op_code,  "Op Description/Complaint" as opcode_desc,
                        "Labour Amount" as sale, "Technician Hours" as hours, "Payment Type" as pay_type
                        FROM ${schemaName}.quorum_labordetail where lower("Payment Type") = LOWER('${ laborPayType}') AND "Technician Hours"::numeric <> 0;`
                    };
                    executeQuery(queryLaborPayType, "paytypeAllLaborListData", res);
                    break;
                case "partSummaryList":
                    const queryPartSummary = {
                        text: `SELECT 
                        "Payment Type" AS pay_type, 
                        TO_CHAR(SUM("Part Unit Price"::numeric * "Qty Sold"::numeric) / SUM("Part Unit Cost"::numeric * "Qty Sold"::numeric), 'FM999999999.0000') AS markup,
                        ROUND(SUM("Part Unit Price"::numeric * "Qty Sold"::numeric), 2) AS sale,
                        ROUND(SUM("Part Unit Cost"::numeric * "Qty Sold"::numeric),2) AS cost,
                        COUNT("Payment Type") AS quantity,
                        COUNT(DISTINCT p."RO Number") AS count
                    FROM 
                    ${schemaName}.quorum_partsdetail p
                    WHERE 
                        "Part Unit Cost"::numeric <> 0 
                        and  "Qty Sold"::numeric <> 0 
                        AND "Payment Type" !~'^I'
                        AND nullif("Payment Type",'') is not NULL
                    GROUP BY 
                        "Payment Type";`
                    };
                    executeQuery(queryPartSummary, "paytypeAllPartSummary", res);
                    break;
                case "laborSummaryList":
                    const queryLaborSummary = {
                        text: `SELECT 
                        "Payment Type" AS pay_type, 
                        TO_CHAR(SUM("Labour Amount"::numeric ) / SUM("Technician Hours"::numeric), 'FM999999999.00') AS rate,
                        SUM("Labour Amount"::numeric) AS sale,
                        SUM("Technician Hours"::numeric) AS hours,
                        COUNT("Payment Type") AS quantity,
                        COUNT(DISTINCT "RO Number") AS count
                    FROM 
                    ${schemaName}.quorum_labordetail 
                    WHERE 
                        "Technician Hours"::numeric <> 0 
                        AND "Payment Type" !~'^I'
                        AND nullif("Payment Type",'') is not NULL
                    GROUP BY 
                        "Payment Type";`
                    };
                    executeQuery(queryLaborSummary, "paytypeAllLaborSummary", res);
                    break;
                default:
                    res.status(200).send({ status: "error", message: "Invalid operation name" });
                    logger.info("Invalid operation name");
                    break;
            }
            async function executeQuery(query, responseDataKey, res) {
                try {
                    const result = await client.query(query);
                    const responseData = {
                        [responseDataKey]: result.rows
                    };
                    res.status(200).send({ status: "success", data: responseData });
                    logger.info("success:", responseData);
                    client.release();
                } catch (error) {
                    res.status(200).send({ status: "error", message: error });
                    logger.error("error:", error);
                }
            }

            function sendCommonResponse(data, res) {
                res.status(200).send({ status: "success", data });
                client.release();
            }
            // Release the client back to the pool
        } catch (error) {
            res.status(200).send({ status: "error", message: 'Error:', error });
            logger.error("error:", error);
        }
    } else {
        res.status(200).send({ status: "error", message: "Request body is null" });
        logger.info("Request body is null");

    }
});
module.exports = routes;

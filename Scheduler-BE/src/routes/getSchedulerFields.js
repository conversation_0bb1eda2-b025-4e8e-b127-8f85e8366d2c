const express = require("express");
const routes = express.Router();
const { logger } = require("../logger/schedulerLogger");
const segment = require("../controllers/SEGMENT/segmentManager");
const scheduleManager = require("../controllers/scheduleFields/scheduleManager");

routes.use("/", async (req, res) => {
  if (Object.keys(req.body).length === 0) {
    logger.error("PBS-Request body is null");
    segment.saveSegment(`Request body is null ${req.body}`);
    return res.status(400).send({ status: "error", message: "Request body is null" });
  }
  const { companyId } = req.body;
  if (!companyId) {
    segment.saveSegment(`Request body is null ${companyId}`);
    return res.status(400).json({
      status: 400,
      message: "Invalid companyId: It must be a non-zero, non-null value."
    });
  }
  try {
    const responseData = await scheduleManager.getScheduleFieldDataByStoreId(companyId);
    if (responseData) {
      return res.status(responseData.status).send(responseData);
    } else {
      return res.status(responseData.status).send({ status: 500, message: "Error processing the request" });
    }
  } catch (error) {
    logger.error("Error in addScheduleFields:", error);
    segment.saveSegment(`Request body is null ${JSON.stringify(error)}`);
    return res.status(500).send({ status: 500, message: "Error processing the request" });
  }
});

module.exports = routes;

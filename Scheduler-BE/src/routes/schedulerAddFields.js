const express = require("express");
const routes = express.Router();
const { logger } = require("../logger/schedulerLogger");
const rp = require("request-promise");
const appConstants = require("../common/constants");
const segment = require("../controllers/SEGMENT/segmentManager");
const axios = require("axios"); // Make sure to install axios
var SOLVE360_USER_NAME = appConstants.SOLVE360_USER_NAME;
var SOLVE360_TOKEN = appConstants.SOLVE360_TOKEN;
const request = require('request');
const constants = require("../common/constants");

function getCredentials() {
  return (
    "Basic " +
    Buffer.from(SOLVE360_USER_NAME + ":" + SOLVE360_TOKEN).toString("base64")
  );
}
async function getSolve360Option(type, id, obj) {  
  var body = JSON.stringify(obj);
  var options = {
    method: "PUT",
    uri: appConstants.SOLVE360_API_URI + "/" + type + "/" + id,
    body: body,
    headers: {
      "Content-Type": "application/json",
      Authorization: getCredentials(),
      ACCEPT: "application/json",
    },
  };
  segment.saveSegment(` API Payload Option: ${JSON.stringify(options)}`);
  return options;
}
const formatDate = (dateStr) => {
  const [month, day, year] = dateStr.split("-");
  const date = new Date(year, month - 1, day);
  return date.toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" });
};
// Function to add in SOLVE360
async function manageStoreGroups(UserInput) {
  const {
    inCompanyId = "",
    inThirdPartyUserName = "",
    inEnterpriseSourceId = "",
    inServerStore = "",
    inBranch = "",
    inCancelledDate = "",
    inMageStoreCode = "",
    inBillingCode = "",
    inMageStCode = "",
    inMageStoreName = "",
    inMageGroupCode = "",
    inMageGroupName = "",
    inUpdatedBy = ""
  } = UserInput;

  const inActionBy = inUpdatedBy;
  const inAuthToken = appConstants.conf.IN_AUTH_TOKEN;
  const fmtCancelledDate = inCancelledDate ? formatDate(inCancelledDate) : "";

  const inCustomFields = {
    custom18732299: inMageGroupCode,
    custom18732301: inMageGroupName,
    custom18732300: inMageStoreCode,
    custom21903875: inBillingCode,
    custom18732302: inMageStoreName,
    custom18186503: inThirdPartyUserName,
    custom22708704: inEnterpriseSourceId,
    custom22708705: inServerStore,
    custom24699370: inBranch,
    custom18319932:fmtCancelledDate
  };

  const optionsSolveCall = await getSolve360Option("companies", inCompanyId, inCustomFields);

  const bodySolveUpdateAPI = {
    solve360ApiType: "companies",
    currentFunctionName: "function to manage store groups",
    actionBy: inActionBy,
    id: inCompanyId || null,
    application: "SCHEDULER",
    authToken: inAuthToken,
    options: optionsSolveCall,
  };

  const config = {
    method: "POST",
    url: appConstants.conf.SOLVE_API_URL,
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    body: bodySolveUpdateAPI,
    json: true,
  };

  try {
    console.log("Manage DMS Fields : Solve360 internal API Payload", JSON.stringify(config));
    segment.saveSegment(`Manage DMS Fields : Solve360 internal API Payload: ${JSON.stringify(config)}`);

    const response = await rp(config);
    segment.saveSegment(`Manage DMS Fields : Solve360 internal API Response: ${JSON.stringify(response)}`);
    if (response.data && response.data.status === "success") {
      return {
        status: true,
        message: response.message || "Success",
      };
    } else {
      return {
        status: false,
        message: response.message || "Unknown error",
      };
    }
  } catch (error) {
    segment.saveSegment(`Manage DMS Fields : Solve360 internal API Error: ${JSON.stringify(error)}`);
    console.error("Error during manage store groups API call:", error);
    throw ({
      status: false,
      message:  `Manage DMS Fields : SALES API Response: ${JSON.stringify(error)}`,
    });
  }
}
  async function sendDmsDetails(inputFields) {

  const {
    inCompanyId = "",
    inThirdPartyUserName = "",
    inEnterpriseSourceId = "",
    inServerStore = "",
    inBranch = "",
    inBillingCode = "",
    inCancelledDate = ""
  } = inputFields;    
  const storeDetails = [
    {
      company_id: inCompanyId,
      thirdparty_user: inThirdPartyUserName,
      enterprise_or_source_id: inEnterpriseSourceId,
      servername_or_store: inServerStore,
      branch: inBranch,
      cancelled_date: inCancelledDate,
      billing_code: inBillingCode
    },
  ];
  const apiParams = JSON.stringify(storeDetails);
  const options = {
    method: "POST",
    maxBodyLength: Infinity,
    url: constants.conf.SCHEDULER_UPDATES_URL,
    headers: {
      "inrequestkey": "dms_details_update", // Custom header from the curl command
      "Content-Type": "application/json",
    },
    data: apiParams, // Use the stringified data
  };
  
  console.log(`Manage DMS Fields : SALES API Payload: ${JSON.stringify(options)}`);
  segment.saveSegment(`Manage DMS Fields : SALES API Payload: ${JSON.stringify(options)}`);
  
  try {
    const apiResponse = await axios(options);
    console.log(`Manage DMS Fields : SALES API Response: ${JSON.stringify(apiResponse.data.status)}`);
    segment.saveSegment(`Manage DMS Fields : SALES API Response: ${JSON.stringify(apiResponse.data.status)}`);
    if (apiResponse.data && apiResponse.data.status === "Success") {
      return {
        status: true,
        message: apiResponse.data.message || "Success",
      };
    } else {
      return {
        status: false,
        message: apiResponse.data.message || "Unknown error",
      };
    }   
  } catch (error) {
    console.error(`Manage DMS Fields :  SALES API Error : ${error.message}`);
    segment.saveSegment(`Manage DMS Fields :  SALES API Error : ${error.message}`);
    throw ({
      status: false,
      message:  `Manage DMS Fields : SALES API Response: ${error}`,
    });
  }
}
async function sendNotificationCall(UserInput) {
  segment.saveSegment("inside sendNotificationCall:");
  console.log("inside sendNotificationCall:");
  try {
    const {
      inPartsProjectId = "",
      inPartsProjectType = "",
      inLaborProjectId = "",
      inLaborProjectType = "",
      inIsInSales = "",
      inSalesComment = "",
      inStageName = "",
      inUpdatedBy = ""
    } = UserInput;
    
    segment.saveSegment("inside sendNotificationCall 222:");
    
    const authKey = Buffer.from(appConstants.conf.PAYLOAD_ENC_KEY).toString('base64');
    
    // Helper function to send the request with the provided payload
    const sendPayloadRequest = async (payload) => {
      const options = {
        headers: {
          'authKey': authKey
        },
        url: appConstants.conf.SALES_TAG_URI,
        json: true,
        body: payload
      };
      segment.saveSegment(`Manage DMS Fields : Add SALESTAG - Options: ${JSON.stringify(options)}`);
      return new Promise((resolve, reject) => {
        request.post(options, (err, res, body) => {
          if (err) {
            console.log(err);
            segment.saveSegment(`Manage DMS Fields : Add SALESTAG - Error: ${JSON.stringify(err)}`);
            reject({
              status: false,
              message: `Manage DMS Fields : Add SALESTAG - Error: ${JSON.stringify(err)}`
            });
          } else {
            try {
              segment.saveSegment(`Manage DMS Fields : Add SALESTAG - Status: ${res.statusCode}`);
              segment.saveSegment(`Manage DMS Fields : Add SALESTAG - body: ${JSON.stringify(body)}`);
              resolve({
                status: true,
                message: body.data ? body.data.message : "Success"
              });
            } catch (err) {
              segment.saveSegment(`Manage DMS Fields : Add SALESTAG Error - ${JSON.stringify(err)}`);
              reject({
                status: false,
                message: "Error processing the response"
              });
            }
          }
        });
      });
    };

    // Initialize responseData object
    let responseData = {};

    // Create and send Parts payload if inPartsProjectId exists
    if (inPartsProjectId) {
      const inPartsPayload = {
        "inProjectId": inPartsProjectId,
        "inProjectType": inPartsProjectType,
        "inIsInSales": inIsInSales,
        "inSalesComment": inSalesComment,
        "inStageName": inStageName,
        "inUpdatedBy": inUpdatedBy
      };
      segment.saveSegment("Sending Parts Payload");
      const SalesTagPartsResponse = await sendPayloadRequest(inPartsPayload);
      if (SalesTagPartsResponse) {
        responseData.SalesTagPartsResponse = SalesTagPartsResponse;
      }
    }

    // Create and send Labor payload if inLaborProjectId exists
    if (inLaborProjectId) {
      const inLaborPayload = {
        "inProjectId": inLaborProjectId,
        "inProjectType": inLaborProjectType,
        "inIsInSales": inIsInSales,
        "inSalesComment": inSalesComment,
        "inStageName": inStageName,
        "inUpdatedBy": inUpdatedBy
      };
      segment.saveSegment("Sending Labor Payload");
      const SalesTagLaborResponse = await sendPayloadRequest(inLaborPayload);
      if (SalesTagLaborResponse) {
        responseData.SalesTagLaborResponse = SalesTagLaborResponse;
      }
    }

    return responseData;

  } catch (error) {
    segment.saveSegment(
      "Error during manage store groups API call:",
      error.response ? error.response.data : error.message
    );
    return {
      status: false,
      message: error.response ? error.response.data : error.message || "Unknown error"
    };
  }
}

// Function to store in FOPC
async function sendStoreDetails(storeDetails) {
  const apiParams = JSON.stringify({
    in_store_details: storeDetails,
  });
  const options = {
    method: "POST",
    maxBodyLength: Infinity,
    url: appConstants.conf.UPDATE_STORE_DETAILS_URL,
    headers: {
      "Content-Type": "application/json",
      Authorization: appConstants.conf.UPDATE_STORE_AUTH_KEY,
    },
    data: storeDetails,
  };
  console.log(`Manage DMS Fields : FOPC API payload: ${JSON.stringify(options)}`);
  segment.saveSegment(`Manage DMS Fields : FOPC API payload: ${JSON.stringify(options)}`);
  const apiResponse = await axios(options);
  console.log(
    `Manage DMS Fields : API Response from FOPC: ${JSON.stringify(apiResponse.data)}`
  );
  segment.saveSegment(
    `Manage DMS Fields : API Response from FOPC: ${JSON.stringify(apiResponse.data)}`
  );
  if(apiResponse.data.status == 'failed'){
    return {
      status: false,
      message: apiResponse.data.message || "Unknown error"
  };
  }else{
    return {
      status: true,
      message: apiResponse.data.message || "Success"
  };
  }
 // return apiResponse.data;
}
async function addScheduleFields(UserInput) {
  const {
    inCompanyId = "",
    inThirdPartyUserName = "",
    inEnterpriseSourceId = "",
    inServerStore = "",
    inBranch = "",
    inCancelledDate = "",
    inMageStoreCode = "",
    inBillingCode = "",
    inMageStoreName = "",
    inMageGroupCode = "",
    inMageGroupName = "",
    inUpdatedBy = "",
    inIsFopcStore = false
  } = UserInput;  
  const cancelledDateTimestamp = inCancelledDate ?  new Date(inCancelledDate).toISOString() : "";
  const insertStoreCredentialMutation = buildInsertStoreCredentialMutation(
    inCompanyId,
    inThirdPartyUserName,
    inEnterpriseSourceId,
    inServerStore,
    inBranch,
    cancelledDateTimestamp,
    inMageStoreCode,
    inBillingCode,
    inMageStoreName,
    inMageGroupCode,
    inMageGroupName,
    inUpdatedBy,
    inIsFopcStore
  );
  const schedulerImportOptions = getOptions(
    appConstants.conf.GRAPHQL_SCHEDULER_URI,
    insertStoreCredentialMutation
  );
  try {
    segment.saveSegment(schedulerImportOptions);
    const response = await rp(schedulerImportOptions);
    if (!response) {
      logger.error("Manage DMS Fields : No response data from INSERT DMS field GraphQL query");
      segment.saveSegment(`Manage DMS Fields : Error in INSERT DMS field GraphQL query: ${error}`);
      return { status: "failed", message: `Manage DMS Fields : Error in INSERT DMS field GraphQL query: ${error}` }
      //throw new Error("Manage DMS Fields : No response from INSERT DMS field GraphQL query");
    }
    const responseData = JSON.parse(response);
    const jsonString = responseData.data.insertStoreCredentialDetails.json;
    const parsedData = JSON.parse(jsonString);
    if (parsedData && parsedData.status) {
      const storeDetails = JSON.stringify({
        in_store_details: {
          storeID: inCompanyId,
          "Third-PartyUsername": inThirdPartyUserName,
          "Enterprise/SourceID": inEnterpriseSourceId,
          "ServerName/Store": inServerStore,
          BranchID: inBranch,
        },
      });
      //FOPC API Call
      if (inIsFopcStore) {
        try {
          const fopcAPIResponse = await sendStoreDetails(storeDetails);
          if (fopcAPIResponse) {
            responseData.fopcAPIResponse = fopcAPIResponse;
          }
        } catch (salesError) {
          responseData.fopcAPIResponse = {
            status: false,
            message: `Manage DMS Fields : Error in FOPC: ${salesError}`
          }
        }
      }
      //Solve360 Internal API Call
      try {
      const manageStoreGroupsResponse = await manageStoreGroups(UserInput);
      if (manageStoreGroupsResponse) {
        responseData.manageStoreGroups = manageStoreGroupsResponse;
      }
    } catch (storeError) {
      responseData.manageStoreGroups = {
        status: false,
        message: `Manage DMS Fields : Error in SOLVE360: ${storeError}`
      }
    }
      //Sales API Call
      try {
      const salesDMSUpdate = await sendDmsDetails(UserInput);
      if (salesDMSUpdate) {
        // Add manageStoreGroupsResponse to responseData
        responseData.salesDMSUpdate = salesDMSUpdate;
      }
    } catch (salesError) {
      responseData.salesDMSUpdate = {
        status: false,
        message: `Manage DMS Fields : Error in SALES: ${salesError}`
      }
    }
    try {
      const SendSalesTagDetails = await sendNotificationCall(UserInput);
      if (SendSalesTagDetails) {
        console.log("33333333333333333333333333333")

        // Add manageStoreGroupsResponse to responseData
        responseData.SendSalesTagDetails = SendSalesTagDetails;
      }
    } catch (salesError) {
      responseData.SendSalesTagDetails = {
        status: false,
        message: `Manage DMS Fields : Error in SALES: ${salesError}`
      }
    }
     console.log(responseData)
      return responseData;
    }
  } catch (error) {
    console.error(`Manage DMS Fields : Error in INSERT DMS field: ${error}`);
    segment.saveSegment(`Manage DMS Fields : Error in INSERT DMS field: ${error}`);
    //throw new Error("Manage DMS Fields : Error in INSERT DMS field");
    return { status: "warning", message: `Manage DMS Fields : Error in INSERT DMS field: ${error}`}
  }
}
function getOptions(URI, query) {
  return {
    method: "POST",
    uri: URI,
    body: JSON.stringify({ query: query }),
    headers: {
      "Content-Type": "application/json",
    },
  };
}
// Function to build the GraphQL mutation
function buildInsertStoreCredentialMutation(
  companyId,
  thirdPartyUserName,
  enterpriseSourceId,
  serverStore,
  branch,
  cancelledDate,
  mageStoreCode,
  billingCode,
  mageStoreName,
  mageGroupCode,
  mageGroupName,
  updatedBy,
  inIsFopcStore
) {
  return `mutation {
     insertStoreCredentialDetails(input: {
       inCompanyId: "${companyId}",
       inThirdPartyUserName: "${thirdPartyUserName}",
       inEnterpriseSourceId: "${enterpriseSourceId}",
       inServerStore: "${serverStore}",
       inBranch: "${branch}",
       ${cancelledDate ? `inCancelledDate: "${cancelledDate}",` : ""}
       inMageStoreCode: "${mageStoreCode}",
       inBillingCode  : "${billingCode}",
       inMageStoreName: "${mageStoreName}",
       inMageGroupCode: "${mageGroupCode}",
       inMageGroupName: "${mageGroupName}",
       inUpdatedBy: "${updatedBy}",
       inIsFopcStore: ${inIsFopcStore}
     }) {
       json
     }
   }`;
}
routes.use("/", async (req, res) => {
  if (!req.body) {
    logger.error("PBS-Request body is null");
    return res
      .status(400)
      .send({ status: "failed", message: "Request body is null" });
  }
  
  const payLoad = req.body;

  try {
    // Mock response data for testing (this should come from the addScheduleFields function in actual use)
    const responseData = await addScheduleFields(payLoad);
    if (responseData && responseData.data) {
      const jsonString = responseData.data.insertStoreCredentialDetails.json;
      const parsedData = JSON.parse(jsonString);
      if (parsedData && parsedData.status) {
        segment.saveSegment(
          `Manage DMS Fields : Response From addScheduleFields ${JSON.stringify(parsedData)}`
        );

        // Initialize the response payload
        const responsePayload = {
          status: "success",
          message: "Success",
          API: {
            FOPC: "Success",
            Sales: "Success",
            Portal: "Success",
          },
        };

        // Check FOPC API response
        if (responseData.fopcAPIResponse && responseData.fopcAPIResponse.status === false) {
          responsePayload.API.FOPC = "Failed";
          responsePayload.status = "warning";
        }

        // Check Manage Store Groups response
        if (responseData.manageStoreGroups && responseData.manageStoreGroups.status === false) {
          responsePayload.API.Portal = "Failed";
          responsePayload.status = "warning";
        }

        // Check Sales DMS Update response
        if (responseData.salesDMSUpdate && responseData.salesDMSUpdate.status === false) {
          responsePayload.API.Sales = "Failed";
          responsePayload.status = "warning";
        }

        // Conditionally add SalesTagLabor if it exists and has a value
        if (responseData.SendSalesTagDetails?.SalesTagLaborResponse) {
          const laborStatus = responseData.SendSalesTagDetails.SalesTagLaborResponse.status;
          if (laborStatus === false) {
            responsePayload.API.SalesTagLabor = "Failed";
            responsePayload.status = "warning";
          } else if (laborStatus === true) {
            responsePayload.API.SalesTagLabor = "Success";
          }
        }

        // Conditionally add SalesTagParts if it exists and has a value
        if (responseData.SendSalesTagDetails?.SalesTagPartsResponse) {
          const partsStatus = responseData.SendSalesTagDetails.SalesTagPartsResponse.status;
          if (partsStatus === false) {
            responsePayload.API.SalesTagParts = "Failed";
            responsePayload.status = "warning";
          } else if (partsStatus === true) {
            responsePayload.API.SalesTagParts = "Success";
          }
        }

        // Send the response payload
        return res.status(200).send(responsePayload);
      }
    } else {
      const isFailed = responseData.status === "failed";
      const status = isFailed ? 500 : 200;
      const message = responseData?.message || "Unexpected response format";
      return res.status(status).send({ status: responseData.status || "warning", message });
    }

    // If no valid response, send error
    segment.saveSegment(`Manage DMS Fields : Unexpected Response From addScheduleFields`);
    return res.status(500).send({ status: "failed", message: "Unexpected response format" });

  } catch (error) {
    // Log the error and return a failure response
    segment.saveSegment(
      `Manage DMS Fields : Error in addScheduleFields ${JSON.stringify(error)}`
    );
    logger.error("Error in addScheduleFields:", error);
    return res.status(500).send({ status: "failed", message: "Error processing the request" });
  }
});



module.exports = routes;

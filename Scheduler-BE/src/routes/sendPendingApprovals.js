const cron = require('node-cron');
const rp = require('request-promise');
const moment = require('moment');
// const { gql } = require('@apollo/client');
const appConstants = require("../common/constants");
const sendMailReport = require('./sendMailForReport');
const { resolve } = require('path');
const getPendingMakeList = `
  query {
    getAllMakeApprovals
  }
`;

const getGraphQLOptions = (query) => ({
  method: 'POST',
  uri: appConstants.conf.GRAPHQL_SCHEDULER_URI,
  body: { query },
  headers: { 'Content-Type': 'application/json' },
  json: true,
});

async function fetchPendingApprovals() {
  const schedulerQuery = getPendingMakeList;
  const queryOptions = getGraphQLOptions(schedulerQuery);

  console.info("Fetching Pending Manufacturer Approvals...");

  try {
    const response = await rp(queryOptions);
    console.log("Pending Approvals Response:", response.data.getAllMakeApprovals);
    return(response.data.getAllMakeApprovals);
   
  } catch (err) {
    console.error("Error fetching pending approvals:", err.message || err);
  }
}

cron.schedule(
  '0 6 * * 5', // 6:00 AM every Friday
  async () => {
    try {
      console.log('Running Pending Approvals Job...');
      
      let templateData = await fetchPendingApprovals();
      templateData = JSON.parse(templateData);
      console.log("templateData >>>", templateData);

      if (!Array.isArray(templateData)) {
        console.error("templateData is not an array");
        return;
      }

      let pendingItems = templateData.filter(item => item.is_approved === false);
      console.log("pendingItems >>>", pendingItems);
      pendingItems = pendingItems.map((item) => {
        return {
          ...item,
          created_at: moment(item.created_at).format('MM-DD-YYYY HH:mm:ss'),
        };
      });

      if (pendingItems.length === 0) {
        console.log("No pending approvals found.");
        return;
      }

      console.log("Sending email with pending items...");
      await sendMailReport.sendPendingApprovalReport(pendingItems);
      console.log("Email sent.");
    } catch (err) {
      console.error("Error in Pending Approvals Job:", err);
    }
  },
  {
    timezone: 'America/New_York',
  }
);


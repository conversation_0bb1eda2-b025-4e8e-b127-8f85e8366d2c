const rp = require('request-promise');
const routes = require('express').Router();
const appConstants = require('../common/constants');
const bodyParser = require('body-parser');
const {frontendLogger}  = require('../logger/schedulerLogger');

routes.use(bodyParser.json()); // support json encoded bodies
routes.use(bodyParser.urlencoded({ extended: true })); // support encoded bodies

routes.use('/', (req, res, next) => {
  doProcess(req, res);
});

function doProcess(req, res) {  

  // frontendLogger.info(`ACTIVITY_LOG_FE_PORTAL_INFO : INSIDE FRONTEND LOGGER ACTION FUNCTION`);
    try {
      let inData = req.body;      
      let length = Object.keys(inData).length;
      if (length !== 0) {
        const { properties } = inData;
        if(properties && properties.activityName ){
          console.log('------log data length-------',length)
          const propertiesString = JSON.stringify(properties);
          frontendLogger.info(`${propertiesString},`);
          res.status(200).json({
            message: 'Success',
          });
        } else {
          res.status(200).json({
            message: 'No log found in properties',
          });
          return; // Return to avoid the code below from executing
        }
        
      } else {
        res.status(200).json({
          message: 'No data Found in logger',
        });
      }
    } catch (error) {
      console.log(error);
    }
  }


module.exports = routes;

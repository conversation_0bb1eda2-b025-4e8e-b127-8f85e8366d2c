// dbConnection.js

const { Pool } = require('pg');
const { logger } = require("../logger/schedulerLogger");

async function doDbConnection(schemaName) {
    try {
        const pool = new Pool({
            user: process.env.DB_IMPORT_USER_NAME,
            host: process.env.DB_IMPORT_HOST_NAME,
            database: process.env.DB_IMPORT_DATABASE_NAME,
            password: process.env.DB_IMPORT_PASSWORD,
            port: process.env.DB_IMPORT_PORT,
            schema: schemaName
        });
        return pool;
    } catch (error) {
        res.status(200).send({ 'status': 'failed', 'message': 'Error establishing database connection:', error });
        logger.error('Error establishing database connection:', error);
        throw error; // Throw the error to handle it in the caller function
    }
}

module.exports = doDbConnection;

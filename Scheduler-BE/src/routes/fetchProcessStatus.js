const routes = require("express").Router();
const fs = require("fs");
const express = require("express");
const bodyParser = require("body-parser");
const { arch } = require("os");
const agendaDb = require('../model/agendaDb');
// routes.use(bodyParser.urlencoded({ extended: false }));
routes.use(express.json());
routes.post("/", async function (req, res) {

  console.log('INSIDE GET EXTRACT JOBS',req.body);
  let payLoad;
  try {
    if (req.body) {
      payLoad = req.body;
      console.log("EXTRACT JOBS PAYLOAD",payLoad.mongoId);
      let ProcessJobData = await agendaDb.fetchRecordByUniqueId(payLoad.mongoId);

      console.log("Process Job data$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",ProcessJobData);
      res.status(200).send({ status: true, data: ProcessJobData });
   
    } else {     
      res.status(201).send({ status: false, data: "Empty request body" });
    }
  } catch (error) {
    res.status(201).send({ status: false, data: JSON.stringify(error) });
  }
});
module.exports = routes;

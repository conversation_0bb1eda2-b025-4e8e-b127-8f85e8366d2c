const routes = require("express").Router();
const rp = require("request-promise");
const ReynoldsJobManager = require("../controllers/Reynolds/ReynoldsJobManager");
let listData = {
  data: {
    allEtlJobExtractImports: {
      edges: [
        {
          node: {
            companyName: "Auto Netspective QA Reynolds Test Store 1",
            projectId: "*********",
            mageGroupCode: "AUTONETSGRP1",
            mageGroupName: "AUTONETSPECTIVE REY QA GRP-1",
            mageStoreCode: "AUTONETSTO1REY",
            mageStoreName: "AUTONETSPECTIVE REY QA STO-1",
            state: "AK",
            thirdPartyUsername: "49901",
            dmsCode: "Reynolds",
            etlDms: "Reynolds",
            errors: "",
            companyId: "*********",
            address: null,
            dealerbuiltSourceId: "246",
            dealerbuiltStoreId: "499",
            __typename: "EtlJobExtractImport",
          },
          __typename: "EtlJobExtractImportsEdge",
        },
        {
          node: {
            companyName: "Auto Netspective QA Reynolds Test Store 2",
            projectId: "*********",
            mageGroupCode: "AUTONETSGRP2",
            mageGroupName: "AUTONETSPECTIVE REY QA GRP-2",
            mageStoreCode: "AUTONETSTO2REY",
            mageStoreName: "AUTONETSPECTIVE REY QA STO-2",
            state: "AK",
            thirdPartyUsername: "49902",
            dmsCode: "Reynolds",
            etlDms: "Reynolds",
            errors: "",
            companyId: "*********",
            address: null,
            dealerbuiltSourceId: "246",
            dealerbuiltStoreId: "499",
            __typename: "EtlJobExtractImport",
          },
          __typename: "EtlJobExtractImportsEdge",
        },
        {
          node: {
            companyName: "Auto Netspective QA Reynolds Test Store 3",
            projectId: "*********",
            mageGroupCode: "AUTONETSGRP3",
            mageGroupName: "AUTONETSPECTIVE REY QA GRP-3",
            mageStoreCode: "AUTONETSTO3REY",
            mageStoreName: "AUTONETSPECTIVE REY QA STO-3",
            state: "AK",
            thirdPartyUsername: "49903",
            dmsCode: "Reynolds",
            etlDms: "Reynolds",
            errors: "",
            companyId: "*********",
            address: null,
            dealerbuiltSourceId: "246",
            dealerbuiltStoreId: "499",
            __typename: "EtlJobExtractImport",
          },
          __typename: "EtlJobExtractImportsEdge",
        },
      ],
      __typename: "EtlJobExtractImportsConnection",
    },
  },
};
let scheduleObj = {
  jobSchedule: "2021-03-30T11:42:36.000Z",
  jobData: {
    groupName: "Auto NETSPECTIVE REY QA GRP-8",
    storeDataArray: [
      {
        locationId: "49905",
        sourceId: "246",
        activityStoreId: "499",
        projectId: "*********",
        secondProjectId: "",
        solve360Update: false,
        buildProxies: true,
        userName: "<EMAIL>",
        inputFilePath:
          "/home/<USER>/Reynolds_input/QAGroup_QAStore-QAState-INITIAL-8641-20210310093331.zip",
        startDate: "10/01/2020",
        endDate: "03/30/2021",
        closedROOption: "monthly",
        jobType: "initial",
        mageGroupCode: "AUTONETSPECTIVE REY QA GRP-8",
        mageStoreCode: "AUTONETSTO8REY",
        stateCode: "AK",
      },
    ],
  },
};
routes.post("/", function (req, res) {
  console.log("Trigger*******");
  ReynoldsJobManager.scheduleReynoldsExtractJob(scheduleObj);
});

//   const options = {
//     method: 'POST',
//     uri: 'https://devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint',
//     headers: {
//         'Content-Type': 'application/json',
//     },
//     body: JSON.stringify(bodyParams)
// };

module.exports = routes;

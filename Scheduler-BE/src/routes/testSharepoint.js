var fs = require('fs');
var path = require('path');
var sp = require('@pnp/sp').sp;
var SPFetchClient = require("@pnp/nodejs").SPFetchClient;
var ProgressBar = require('progress');
const appConstants = require('../common/constants');
// const folderUrl = 'Shared Documents'; // Web relative target folder
const folderUrl = 'Documents/production/Scheduler'; // Web relative target folder
const rp = require('request-promise');
const segment = require("../controllers/SEGMENT/segmentManager");
const shConstants = require("../common/constants");


async function initSharePoint(filePath) {
    // console.log("Started file upload:",retryCount);
    segment.saveSegment(`Started SharePoint file upload functionality `);
    try {
        const cryptr = new Cryptr(appConstants.conf.SHAREPOINT_API_PRIVATE_SECRET_KEY);
        const encrypted = cryptr.encrypt(appConstants.conf.SHAREPOINT_API_PUBLIC_SECRET_KEY);
        
         const fileName = path.parse(filePath).name + path.parse(filePath).ext;
        console.log("ENCRYPTED KEY :::::::::::::::::: ", encrypted);
        var bodyParams = {
            fileName: fileName,
            filePath: filePath,
            secretKey: encrypted,
            dmsType: 'CDK3PA',
           
        };

        const options = {
            method: 'POST',
            uri: shConstants.SHARE_POINT_API_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(bodyParams)
        };
        await rp(options)
            .then(async (parsedBody) => {
                segment.saveSegment(`File uploaded successfully to SharePoint {${fileName}}`);
            })
            .catch(async (error) => {
                segment.saveSegment(`File uploaded failed to SharePoint {${fileName}},error:${error}`);
                console.log('File Upload Failed!!!!!!!!!!!!!!!!!!!!!!', error.toString());

                // await  retrySharePointUpload(filePath, dmsType, rerunFlag, updateSolve360Data, warningObj, retryCount, error);
                let fileName = filePath ? filePath.split("/").pop(-1) : '';
        
                segment.saveSegment(`Failed to upload file to SharePoint {${fileName}} ${error.toString()}`);
            });
    } catch (error) {
        let fileName = filePath ? filePath.split("/").pop(-1) : '';
        segment.saveSegment(`File uploaded failed to SharePoint1 {${filePath}},error:${error}`);
        console.log('File Upload Failed1!!!!!!!!!!!!!!!!!!!!!!', error.toString());
      

        segment.saveSegment(`Failed to upload file to SharePoint {${fileName}} ${error.toString()}`);
    }
}

let filePath ='/data/etl/tmp/dealertrack-test/'

initSharePoint(filePath)
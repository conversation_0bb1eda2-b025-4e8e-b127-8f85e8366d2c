const routes = require("express").Router();
const appConstants = require('../common/constants');
const { postgraphile } = require("postgraphile");
const SCHEMA_NAMES = ["process_data", "scheduler_portal","core","analytics"];
routes.use("/", (req, res, next) => {
 console.log("Viewer Custom GraphQL Test");
 delete req.body.operationName;
 next();
});

routes.use(
 "/",
 postgraphile(appConstants.conf.SCHEDULER_DATABASE_CONNECTION_URI, SCHEMA_NAMES, {
   pgSettings: {
     statement_timeout: "1200000",
   },
   externalUrlBase: "/schedulerCustom",
   graphqlRoute: "/",
   graphiql: false,
   graphiqlRoute: "/graphiql",
   watchPg: true,
 })
);
module.exports = routes;

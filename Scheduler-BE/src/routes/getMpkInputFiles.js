const routes = require("express").Router();
const fs = require("fs");
const bodyParser = require("body-parser");
const { arch } = require("os");
routes.use(bodyParser.urlencoded({ extended: false }));
routes.post("/", function(req, res) {
    let payLoad;
    let fileList = [];
    let archive = [];
    try {
        if (req.body) {
            payLoad = req.body;
            // console.log(payLoad);
            if (payLoad.hasOwnProperty("folderPath")) {
                if (payLoad.folderPath) {
                    if (fs.existsSync(payLoad.folderPath)) {
                        // console.log("Directory exists.");
                        fs.readdirSync(payLoad.folderPath).forEach((file) => {
                            console.log(file);
                            if (file != 'archive') {
                                fileList.push({ filename: file });
                            }
                        });
                        // console.log(fileList);
                        if (fs.existsSync(payLoad.folderPath + 'archive')) {
                            fs.readdirSync(payLoad.folderPath + 'archive').forEach((file) => {
                                if (file != 'archive') {
                                    archive.push({ filename: file });
                                }

                            });
                        }

                        res.status(201).send({ status: true, data: fileList, archiveData: archive });
                    } else {
                        res
                            .status(201)
                            .send({ status: false, data: "folderPath not exist" });
                    }
                } else {
                    res.status(201).send({ status: false, data: "Empty folderPath" });
                }
            } else {
                res
                    .status(201)
                    .send({ status: false, data: "folderPath key not found" });
            }
        } else {
            res.status(201).send({ status: false, data: "Empty request body" });
        }
    } catch (error) {
        // console.log(JSON.stringify(error));
        res.status(201).send({ status: false, data: JSON.stringify(error) });
    }
});
module.exports = routes;
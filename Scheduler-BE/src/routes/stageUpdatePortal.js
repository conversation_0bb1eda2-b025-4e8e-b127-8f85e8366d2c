const routes = require("express").Router();
const axios = require("axios");
const {logger} = require("../logger/schedulerLogger");
const appConstants = require("../common/constants");
/**
 * Function that return URL options
 * @param {object} req - Http Request object
 * @param {string} forwardURI - URI string
 */

routes.use("/", (req, res, next) => {
//  console.log("req-------------------------------", req.body);
 const options = {
  method: "POST",
  url: appConstants.conf.PAYLOAD_URI,
  headers: { "Content-Type": "application/json" },
  data: req.body,
 };
//  console.log("options-------------------------------", options);
 axios(options)
  .then((response) => {
   // console.log("response---------", response.data);
   logger.info(
    `POST_PORTAL_API [${req.jwtToken.message.unique_name}]: API call to portal success` 
   );
   res.send({ status: "success", data: response.data });
  })
  .catch((error) => {
   logger.error(
    `POST_PORTAL_API [${req.jwtToken.message.unique_name}]: API call to portal failed`,
    error
   );
   res.status(400).json({ errors: [error] });
  });
});
module.exports = routes;

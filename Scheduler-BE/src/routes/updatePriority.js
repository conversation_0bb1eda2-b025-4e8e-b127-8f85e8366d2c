const routes = require("express").Router();
const fs = require("fs");
const bodyParser = require("body-parser");
const { arch } = require("os");
const { MongoClient } = require('mongodb');
const agendaDb = require('../model/agendaDb');

routes.post("/", async function (req, res) {
  console.log("INSIDE UPDATE PRIORITY ORDER!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");

  let payLoad = req.body;


  if(payLoad){
    let fileName = payLoad.fileName;
    let dms = payLoad.dms;
    let priority = Number(payLoad.priority);

    console.log("fileName",fileName);
    console.log("dms",dms);
    console.log("priority",priority);

    try {
        let res =  await agendaDb.updateProcessorJobPriority(fileName, priority,dms);
        if(res.status){
            res.status(200).send({ status: true, message:'Quue Updated' });
        }
      } catch (err) {
          res.status(201).send({ status: true, message:'Failed to Update queue' });
      }
  }else{
    res.status(201).send({ status: true, message:'Insufficient data' });
  }

 
    
});


module.exports = routes;

<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Sharepoint File Upload</title>
</head>

<body>
    
    {{#if scheduled_by_exist_flag}}
    <p>Scheduled By :{{scheduled_by}}</p>
    {{/if}}

    {{#if pre_import_halt_exist}}
    <p>Pre Import Halt Detected:</p>
    {{/if}}

    {{#if paytypeHalt_exist_flag}}
    <p>paytype Halt: {{paytype_halt}}</p>
    {{/if}}

    {{#if invSequenceHalt_exist_flag}}
    <p>Inv Sequence Halt: {{inv_sequence_halt}}</p>
    {{/if}}

    {{#if makeHalt_exist_flag}}
    <p>Make Halt: {{make_halt}}</p>
    {{/if}}

    {{#if departmentHalt_exist_flag}}
    <p>Department Halt: {{department_halt}}</p>
    {{/if}}

    {{#if test_data_flag}}
    <p style="color: red;">Scheduled via Save File Only</p>
    {{/if}}


    
    <p style="font-size: 16px; font-weight: normal;"> Proxy File Upload Status : {{proxy_file_upload_status}}</p>
    <p style="font-size: 16px; font-weight: normal;"> Proxy File Name: {{proxy_file_name}}</p>
    <p style="font-size: 16px; font-weight: normal;"> Proxy SharePoint URL: <a href="{{proxy_url}}">{{proxy_url}}</a></p>
    <p style="margin-top:50px;"></p>

   
    {{#if isRerun}}
        <p>ETL file moved to EFS path ({{efs_path}})</p>
    {{/if}}

   {{#if resume_user_message_exist_flag}}
    <p><mark><span style="font-weight: bold;">Resumed By:</span> {{resume_user_message}} </mark></p>
    {{/if}}

    <!-- {{#if coupon_and_discount_file_not_uploaded_warning_message_exist_flag}}
        <p><mark> {{coupon_and_discount_file_not_uploaded_warning_message}} </mark></p>
    {{/if}} -->

    {{#if coa_exception_warning_message_exist_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{coa_exception_warning_message}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> COA Data is Missing</p> 
    <p><span style="font-weight: bold;">Exception description:</span> The API returns an error that the COA (Chart of Accounts) is not available.</p>
    {{/if}}

    {{#if customer_exception_warning_message_exist_flag}}
        <p><mark> {{customer_exception_warning_message}} </mark></p>
    {{/if}}
    
    {{#if ro_account_description_message_exist_flag}}  
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{ro_account_description_warning_message}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Customer Name showing as "acc description"</p> 
    <p><span style="font-weight: bold;">Exception description:</span> There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing</p>
    {{/if}}

    {{#if warning_message_exist_flag}}  
            <p>Errors:</p>
        <ul>
            {{#each warning_message}}
            <li>
                <mark>  {{this}}   </mark>
            </li>
            {{/each}}
        </ul>
        {{#if dealertrack_identify_flag}} 
        <!-- <p>So unable to buid proxy.</p> -->
        {{/if}}
    {{/if}}

    {{#if skip_error_count_message_exist_flag}}
    <p>Errors skipped input : {{skip_error_count}}</p>
    {{/if}}

    {{#if closed_rodetail_warning_message_exist_flag}}  
            <p>Error in Closed RO Detail API</p>
        <ul>
            {{#each closed_rodetail_warning_message}}
            <li>
                <mark> {{this}} </mark>
            </li>
            {{/each}}
        </ul>
    {{/if}}

    {{#if vehicle_warning_message_exist_flag}}  
            <p>Error in Vehicle Detail API</p>
        <ul>
            {{#each vehicle_warning_message}}
            <li>
                <mark> {{this}} </mark>
            </li>
            {{/each}}
        </ul>
    {{/if}}

    {{#if customer_warning_message_exist_flag}}  
    <p>Error in Customer Detail API</p>
    <ul>
        {{#each customer_warning_message}}
        <li>
            <mark> {{this}}  </mark>
        </li>
        {{/each}}
    </ul>
    {{/if}}

    {{#if gldetail_warning_message_exist_flag}}  
        <p>Error in GLDetail Lookup API</p>
    <ul>
        {{#each gldetail_warning_message}}
        <li>
            <mark> {{this}}  </mark>
        </li>
        {{/each}}
    </ul>
    {{/if}}

    {{#if coupon_and_discount_message_exist_flag}}  
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{coupon_and_discount_warning_message}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> CouponDiscountBasis Amount Mismatch Exception</p> 
    <p><span style="font-weight: bold;">Exception description:</span> The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation).</p>
    {{/if}}

    {{#if core_return_not_equal_to_core_charge_exception_exist_flag}}
    <p><mark>Core Charge and Core Return mismatch: {{core_return_not_equal_core_charge_exception_count}} </mark></p>
    {{/if}}

    {{#if core_return_exception_exist_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{core_return_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Core Return without Core Charge</p> 
    <p><span style="font-weight: bold;">Exception description:</span> List of ROs that have a core return, but no core charge.</p>
    {{/if}}

    {{#if core_charge_exception_exist_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{core_charge_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Core Charge without Core Return</p> 
    <p><span style="font-weight: bold;">Exception description:</span> List of ROs that have a core charge, with no core return.</p>
    {{/if}}

    {{#if core_charge_with_no_sale_exception_exist_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{core_charge_with_no_sale_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Core Charge with no Sale</p> 
    <p><span style="font-weight: bold;">Exception description:</span> List of ROs that have a core charge with no sale amount.</p>
    {{/if}}
   
    {{#if gl_ro_not_found_exception_exist_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{gl_ro_not_found_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> GL Missing Total RO count</p> 
    <p><span style="font-weight: bold;">Exception description:</span> List of ROs with missing GL data.</p>
    {{#if isFailed}}
    {{else}}     
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> If the RO count is above 100 missing GL, please stop to verify the reason that a high volume of ROs are missing GL. If related to a buy/sell of the store in the past 6 months, and all impacted ROs are prior to the buy/sell date, okay to resume. If all ROs are missing GL, a new RLT should be obtained from the client.</span></p>
    {{/if}}
    {{/if}}

    {{#if invalid_core_cost_sale_mismatch_exist_flag}}
        <p><mark>Invalid Core Cost-Sale mismatch: {{invalid_core_cost_sale_mismatch_count}} </mark></p>
    {{/if}}

    {{#if invalid_core_amount_mismatch_exist_flag}}
        <p><mark>Invalid Core Amount mismatch: {{invalid_core_amount_mismatch_count}} </mark></p>
    {{/if}}

    {{#if misc_exception_exist_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{misc_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Misc. exception mismatch</p> 
    <p><span style="font-weight: bold;">Exception description:</span> Mismatch with the MISC. amount in line items and in the total section</p>
    {{/if}}
    
    {{#if multi_labor_exception_exist_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{multiLaborExceptionCount}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Multi labor Exception</p> 
    <p><span style="font-weight: bold;">Exception description:</span> Multiple labor in a job</p>
    {{/if}}
    
<!--     
    {{#if invalid_misc_paytype_flag}}
    <p><mark>Invalid misc paytype count: {{invalid_misc_paytype_count}} </mark></p>
    {{/if}} -->
    
    {{#if punch_time_missing_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception percentage: {{punch_time_missing_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span>% of ROs with technician punch time missing</p> 
    <p><span style="font-weight: bold;">Exception description:</span> The percentage of ROs with no tech punch time present.</p>
    {{/if}}

   
    {{#if suffixed_invoices_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{suffixed_invoices_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Suffixed Invoices Count</p> 
    <p><span style="font-weight: bold;">Exception description:</span> List of ROs that have a suffix (i.e., 'SX') on the RO number.</p>
    {{/if}}


    {{#if company_no_not_matching_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{company_no_not_matching_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Company Number Not matching Count</p> 
    <p><span style="font-weight: bold;">Exception description:</span> As a precaution, we are ensuring that we have retrieved data from the correct company.</p>
    {{/if}}
     
    {{#if grouped_team_work_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{grouped_team_work_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Grouped Team Work Count</p> 
    <p><span style="font-weight: bold;">Exception description:</span> We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.</p>
    {{/if}}

    {{#if split_job_exception_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{split_job_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Split Job Exception Count</p> 
    <p><span style="font-weight: bold;">Exception description:</span> Labor with split jobs</p>
    {{/if}}

    {{#if new_line_type_exception_exist_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{new_line_type_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Total Count of a new 'Line Type' Detected</p> 
    <p><span style="font-weight: bold;">Exception description:</span> DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.</p>
    {{/if}}

    {{#if chart_of_accounts_file_path_exist_flag}}
    <p><mark> COA Successfully Uploaded </mark></p>
    {{/if}}

    {{#if exception_closed_invoices_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{exception_closed_invoices_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data</p> 
    <p><span style="font-weight: bold;">Exception description:</span> List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.</p>
    {{/if}}
    
    {{#if extra_ro_in_xml_exception_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{extra_ro_in_xml_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Extra ROs present in the RCI raw data</p> 
    <p><span style="font-weight: bold;">Exception description:</span> List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.</p>
     {{/if}}

     {{#if im_opened_closed_rci_ros_flag}}
     <br><br>
     <p><span style="font-weight: bold;"> Exception count: {{im_Opened_Closed_Rci_Ros_Count}}</span></p>
     <p><span style="font-weight: bold;">Exception name:</span> ROs open on the client-provided invoice master, but closed in the RCI data</p> 
     <p><span style="font-weight: bold;">Exception description:</span> List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.</p>
    {{/if}}


    {{#if Sale_Zero_Cost_NonZero_Parts_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{Sale_Zero_Cost_NonZero_Parts_Count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Parts Sale is Zero or Cost Non-Zero Parts</p> 
    <p><span style="font-weight: bold;">Exception description:</span> Parts with zero sale, non-zero cost</p>
   {{/if}}

   {{#if less_special_discount_exception_flag }}
   <br><br>
   <p><span style="font-weight: bold;"> Exception count: {{less_special_discount_count}}</span></p>
   <p><span style="font-weight: bold;">Exception name:</span> Less Special Discount Count</p> 
   <p><span style="font-weight: bold;">Exception description:</span> RO with Less Special Discounts. Parts and Labor discounts gets in labor line</p>
  {{/if}}

    {{#if negative_coupon_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{negative_coupon_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Negative Coupon Count</p> 
    <p><span style="font-weight: bold;">Exception description:</span> List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.</p>
    {{/if}}

    {{#if labor_with_zero_sale_nonzero_cost_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{labor_with_zero_sale_nonzero_cost_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Labor with Zero Sale, Nonzero Cost</p> 
    <p><span style="font-weight: bold;">Exception description:</span> If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.</p>
    {{/if}}

    {{#if gl_missing_ros_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{gl_missing_ros_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> GL Missing Total RO count</p> 
    <p><span style="font-weight: bold;">Exception description:</span> List of ROs with missing GL data.</p>
    {{/if}}


    {{#if part_description_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{part_description_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Part Description Array Length > 2</p> 
    <p><span style="font-weight: bold;">Exception description:</span> Parts having multiple descriptions as array</p>
    {{/if}}


    {{#if coupon_discount_basis_amount_mismatch_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{coupon_discount_basis_amount_mismatch_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span>Coupons/discounts Exception Count</p> 
    <p><span style="font-weight: bold;">Exception description:</span>Coupon numbers present in the RO raw data are not found in the Coupon and Discount API.</p>
    {{/if}}

    {{#if labor_with_no_paytype_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold;line-height:0%;">Exception count:</span> {{labor_with_no_paytype_exception_count}}</p>
    <p><span style="font-weight: bold;line-height:0%;">Exception name:</span> Labor with no paytype</p> 
    <p><span style="font-weight: bold;line-height:0%;">Exception description:</span> Labor with a Null paytype.</p>
    {{/if}}

    {{#if parts_excluded_from_history_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold;line-height:0%;">Exception count:</span> {{parts_excluded_from_history_exception_count}}</p>
    <p><span style="font-weight: bold;line-height:0%;">Exception name:</span> Parts Excluded From History Exception Count</p> 
    <p><span style="font-weight: bold;line-height:0%;">Exception description:</span> Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.</p>
    {{/if}}

    {{#if lost_sale_parts_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold; line-height: normal;"> Exception count: {{lost_sale_parts_exception_count}} </span></p>
    <p><span style="font-weight: bold;">Exception name:</span>Lost Parts Sale</p> 
    <p><span style="font-weight: bold;">Exception description:</span>Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this</p>
    {{/if}}
    
    {{#if part_details_null_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{part_details_null_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span>Part Details Missing</p> 
    <p><span style="font-weight: bold;">Exception description:</span>Parts that do not have a part number or description.</p>
    {{/if}}
    


    
</body>

</html>

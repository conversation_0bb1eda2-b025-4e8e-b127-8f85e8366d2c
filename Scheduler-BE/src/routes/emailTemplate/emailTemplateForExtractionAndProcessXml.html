<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Completed {{dms_type}} {{process_types}}</title>
</head>

<body>
       
    {{#if scheduled_by_exist_flag}}
    <p>Scheduled By :{{scheduled_by}}</p>
    {{/if}}

    
    {{#if resume_user_message_exist_flag}}
        <br><br>
        <p><mark><span style="font-weight: bold;">Resumed By:</span> {{resume_user_message}} </mark></p>
    {{/if}}

    {{#if dealer_not_subscribed_message_exist_flag}}
        <p><mark> {{dealer_not_subscribed_message}} </mark></p>
    {{/if}}

    {{#if test_data_flag}}
    <p style="color: red;">Scheduled via Save File Only</p>
    {{/if}}

    <p style="font-size: 16px; font-weight: normal;"> {{display_message}} </p>

    <!-- {{#if coupon_and_discount_file_not_uploaded_warning_message_exist_flag}}
        <p><mark> {{coupon_and_discount_file_not_uploaded_warning_message}} </mark></p>
    {{/if}} -->

    {{#if coa_exception_warning_message_exist_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{coa_exception_warning_message}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> COA Data is Missing</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> The API returns an error that the COA (Chart of Accounts) is not available.</p>
    
    {{#if isFailed}}
    {{else}}
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> Do not resume HALT. Please re-run with COA file to resolve this exception</span></p>
    {{/if}}
    {{/if}}

    {{#if customer_exception_warning_message_exist_flag}}
        <p><mark> {{customer_exception_warning_message}} </mark></p>
    {{/if}}

    {{#if ro_account_description_message_exist_flag}} 
    
       <br><br>
       <p><span style="font-weight: bold;"> Exception Count: {{ro_account_description_warning_message}}</span></p>
       <p><span style="font-weight: bold;">Exception Name:</span> Customer Name showing as "acc description"</p> 
       <p><span style="font-weight: bold;">Exception Description:</span> There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing</p>
       {{#if isFailed}}
       {{else}}
       <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> Do not resume HALT. Please re-run with COA file to resolve this exception</span></p>
       {{/if}}
    {{/if}}

    {{#if warning_message_exist_flag}}  
            <p>Errors:</p>
        <ul>
            {{#each warning_message}}
            <li>
                <mark> {{this}} </mark>
            </li>
            {{/each}}
        </ul>
        {{#if dealertrack_identify_flag}} 
        <!-- <p>So unable to buid proxy.</p> -->
        {{/if}}
    {{/if}}

    {{#if skip_error_count_message_exist_flag}}
    <p>Errors skipped input : {{skip_error_count}}</p>
    {{/if}}

    {{#if closed_rodetail_warning_message_exist_flag}}  
            <p>Error in Closed RO Detail API</p>
        <ul>
            {{#each closed_rodetail_warning_message}}
            <li>
                <mark> {{this}} </mark>
            </li>
            {{/each}}
        </ul>
    {{/if}}

    {{#if vehicle_warning_message_exist_flag}}  
            <p>Error in Vehicle Detail API</p>
        <ul>
            {{#each vehicle_warning_message}}
            <li>
                <mark> {{this}}  </mark>
            </li>
            {{/each}}
        </ul>
    {{/if}}

    {{#if customer_warning_message_exist_flag}}  
    <p>Error in Customer Detail API</p>
    <ul>
        {{#each customer_warning_message}}
        <li>
            <mark> {{this}}  </mark>
        </li>
        {{/each}}
    </ul>
    {{/if}}

    {{#if gldetail_warning_message_exist_flag}}  
        <p>Error in GLDetail Lookup API</p>
    <ul>
        {{#each gldetail_warning_message}}
        <li>
            <mark> {{this}}  </mark>
        </li>
        {{/each}}
    </ul>
    {{/if}}

    {{#if coupon_and_discount_message_exist_flag}}  
        <!-- <p>Exceptions in coupons/discounts: </p>
        <p><mark> {{coupon_and_discount_warning_message}}  </mark></p> -->

        <br><br>
        <p><span style="font-weight: bold;"> Exception Count: {{coupon_and_discount_warning_message}}</span></p>
        <p><span style="font-weight: bold;">Exception Name:</span> CouponDiscountBasis Amount Mismatch Exception</p> 
        <p><span style="font-weight: bold;">Exception Description:</span> The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation).</p>
        {{#if isFailed}}
        {{else}}
        <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT, but need Actual ROs to confirm. Please request a minimum of 5 RO examples from the Dealer.</span></p>
        {{/if}}
    {{/if}}

    {{#if core_return_not_equal_to_core_charge_exception_exist_flag}}
    <p><mark>Core Charge and Core Return mismatch: {{core_return_not_equal_core_charge_exception_count}} </mark></p>
    {{/if}}

    {{#if core_return_exception_exist_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{core_return_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Core Return without Core Charge</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> List of ROs that have a core return, but no core charge.</p>
    {{#if isFailed}}
    {{else}}
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT</span></p>
    {{/if}}
    {{/if}}


    {{#if core_charge_exception_exist_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{core_charge_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Core Charge without Core Return</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> List of ROs that have a core charge, with no core return.</p>
    {{#if isFailed}}
    {{else}}
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT</span></p>
    {{/if}}
    {{/if}}

    {{#if core_charge_with_no_sale_exception_exist_flag}}
  
    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{core_charge_with_no_sale_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Core Charge with no Sale</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> List of ROs that have a core charge with no sale amount.</p>
    {{#if isFailed}}
    {{else}}
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT</span></p>
    {{/if}}
    {{/if}}

    {{#if gl_ro_not_found_exception_exist_flag}}

    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{gl_ro_not_found_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> GL Missing Total RO count</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> List of ROs with missing GL data.</p>
    {{#if isFailed}}
    {{else}}     
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> If the RO count is above 100 missing GL, please stop to verify the reason that a high volume of ROs are missing GL. If related to a buy/sell of the store in the past 6 months, and all impacted ROs are prior to the buy/sell date, okay to resume. If all ROs are missing GL, a new RLT should be obtained from the client.</span></p>
    {{/if}}

    {{/if}}

    {{#if invoice_missing_exception_exist_flag}}

    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{invoice_missing_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Invoice Missing Count</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.</p>
    {{#if isFailed}}
    {{else}}
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT</span></p>
    {{/if}}
    {{/if}}

    {{#if invalid_core_cost_sale_mismatch_exist_flag}}
        <p><mark>Invalid Core Cost-Sale mismatch: {{invalid_core_cost_sale_mismatch_count}} </mark></p>
    {{/if}}

    {{#if invalid_core_amount_mismatch_exist_flag}}
        <p><mark>Invalid Core Amount mismatch: {{invalid_core_amount_mismatch_count}} </mark></p>
    {{/if}}

    {{#if misc_exception_exist_flag}}
    
    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{misc_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Misc. exception mismatch</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> Mismatch with the MISC. amount in line items and in the total section</p>
    {{#if isFailed}}
    {{else}}
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT, but need Actual ROs to confirm. Please request a minimum of 5 RO examples from the Dealer.</span></p>
    {{/if}}
    {{/if}}

       
    {{#if multi_labor_exception_exist_flag}}
    
    <br><br>
    <p><span style="font-weight: bold;"> Exception count: {{multiLaborExceptionCount}}</span></p>
    <p><span style="font-weight: bold;">Exception name:</span> Multi labor Exception</p> 
    <p><span style="font-weight: bold;">Exception description:</span> Multiple labor in a job</p>
    {{#if isFailed}}
    {{else}}
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> Need Proxy Review</span></p>
    {{/if}}
    {{/if}}


    <!-- {{#if invalid_misc_paytype_flag}}
    <p><mark>Invalid misc paytype count: {{invalid_misc_paytype_count}} </mark></p>
    {{/if}} -->


    {{#if punch_time_missing_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception Percentage: {{punch_time_missing_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> % of ROs with technician punch time missing</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> The percentage of ROs with no tech punch time present.</p>
    {{#if isFailed}}
    {{else}}
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume HALT. Add MGMT Review tag for Chrysler, Alfa Romeo, FIAT and Maserati Projects</span></p>
    {{/if}}
    {{/if}}



    {{#if inventory_ro_flag}}
    <p><mark>Inventory RO Count: {{inventory_ro_count}} </mark></p>
    <p><mark>Dealer Address : {{dealerAddress}} </mark></p>
    {{/if}}


    {{#if suffixed_invoices_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{suffixed_invoices_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Suffixed Invoices Count</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> List of ROs that have a suffix (i.e., 'SX') on the RO number.</p>
    {{#if isFailed}}
    {{else}}
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT</span></p>
    {{/if}}
    {{/if}}
   
    {{#if company_no_not_matching_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{company_no_not_matching_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Company Number Not matching Count</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> As a precaution, we are ensuring that we have retrieved data from the correct company.</p>
    {{#if isFailed}}
    {{else}}
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT, but need Actual ROs to confirm. Please request a minimum of 5 RO examples from the Dealer.</span></p>
    {{/if}}
    {{/if}}

    {{#if grouped_team_work_flag}}
    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{grouped_team_work_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Grouped Team Work Count</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.</p>
    {{#if isFailed}}
    {{else}}   
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT, but need Actual ROs to confirm. Please request a minimum of 5 RO examples from the Dealer.</span></p>
    {{/if}}
   {{/if}}

    {{#if split_job_exception_flag}}

    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{split_job_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Split Job Exception Count</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> Labor with split jobs</p>
    {{#if isFailed}}
    {{else}} 
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT, but need to request the actual RO if in the range and qualified.</span></p>
    {{/if}}
    {{/if}}
    
    {{#if new_line_type_exception_exist_flag}}

    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{new_line_type_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Total Count of a new 'Line Type' Detected</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.</p>
    {{#if isFailed}}
    {{else}}  
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> Do not resume HALT. We need actual ROs to verify the data and will need to re-run the extraction once Netspective confirms the code has been updated. Please request a minimum of 5 RO examples from the Dealer.</span></p>
    {{/if}}
    {{/if}}
     
    {{#if chart_of_accounts_file_path_exist_flag}}
    <p><mark> COA Successfully Uploaded </mark></p>
    {{/if}}
    
    {{#if exception_closed_invoices_flag}}

    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{exception_closed_invoices_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.</p>
    {{#if isFailed}}
    {{else}}     
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT</span></p>
    {{/if}}
    {{/if}}
    
    {{#if extra_ro_in_xml_exception_flag}}
   
    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{extra_ro_in_xml_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Extra ROs present in the RCI raw data</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.</p>
    {{#if isFailed}}
    {{else}}  
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT</span></p>
    {{/if}}
    {{/if}}

     {{#if im_opened_closed_rci_ros_flag}}

     <br><br>
     <p><span style="font-weight: bold;"> Exception Count: {{im_Opened_Closed_Rci_Ros_Count}}</span></p>
     <p><span style="font-weight: bold;">Exception Name:</span> ROs open on the client-provided invoice master, but closed in the RCI data</p> 
     <p><span style="font-weight: bold;">Exception Description:</span> List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.</p>
     {{#if isFailed}}
     {{else}} 
     <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT</span></p>
     {{/if}}
    {{/if}}


    {{#if Sale_Zero_Cost_NonZero_Parts_flag}}

     <br><br>
     <p><span style="font-weight: bold;"> Exception Count: {{Sale_Zero_Cost_NonZero_Parts_Count}}</span></p>
     <p><span style="font-weight: bold;">Exception Name:</span> Parts Sale is Zero or Cost Non-Zero Parts</p> 
     <p><span style="font-weight: bold;">Exception Description:</span> Parts with zero sale, non-zero cost</p>
     {{#if isFailed}}
     {{else}}     
     <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT, but need Actual ROs to confirm. Please request a minimum of 5 RO examples from the Dealer.</span></p>
     {{/if}}
   {{/if}}

   {{#if gl_Deatil_Extraction_Error_Count_flag}}
   <p><mark> GL Error Count: {{gl_Deatil_Extraction_Error_Count}} </mark></p>
  {{/if}}


  {{#if less_special_discount_exception_flag }}

  <br><br>
  <p><span style="font-weight: bold;"> Exception Count: {{less_special_discount_count}}</span></p>
  <p><span style="font-weight: bold;">Exception Name:</span> Less Special Discount Count</p> 
  <p><span style="font-weight: bold;">Exception Description:</span> RO with Less Special Discounts. Parts and Labor discounts gets in labor line</p>
  {{#if isFailed}}
  {{else}} 
  <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT, but need Actual ROs to confirm. Please request a minimum of 5 RO examples from the Dealer.</span></p>
  {{/if}}
 {{/if}}

    {{#if negative_coupon_exception_flag }}

    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{negative_coupon_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Negative Coupon Count</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.</p>
    {{#if isFailed}}
    {{else}} 
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT, but need Actual ROs to confirm. Please request a minimum of 5 RO examples from the Dealer.</span></p>
    {{/if}}
    {{/if}}

    {{#if labor_with_zero_sale_nonzero_cost_exception_flag }}

    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{labor_with_zero_sale_nonzero_cost_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Labor with Zero Sale, Nonzero Cost</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.</p>
    {{#if isFailed}}
    {{else}}   
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT, but need Actual ROs to confirm. Please request a minimum of 5 RO examples from the Dealer.</span></p>
    {{/if}}
    {{/if}}

    {{#if gl_missing_ros_exception_flag }}

    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{gl_missing_ros_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> GL Missing Total RO count</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> List of ROs with missing GL data.</p>
    {{#if isFailed}}
    {{else}}     
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> If the RO count is above 100 missing GL, please stop to verify the reason that a high volume of ROs are missing GL. If related to a buy/sell of the store in the past 6 months, and all impacted ROs are prior to the buy/sell date, okay to resume. If all ROs are missing GL, a new RLT should be obtained from the client.</span></p>
    {{/if}}

    {{/if}}

    {{#if part_description_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{part_description_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span> Part Description Array Length > 2</p> 
    <p><span style="font-weight: bold;">Exception Description:</span> Parts having multiple descriptions as array</p>
    {{#if isFailed}}
    {{else}} 
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> Do not resume HALT. We need the Coupon and Discount CSV file to rerun, or we need to repull the data and confirm that the pulled data does not have this exception.</span></p>
    {{/if}}
    {{/if}}

    {{#if coupon_discount_basis_amount_mismatch_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{coupon_discount_basis_amount_mismatch_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span>Coupons/discounts Exception Count</p> 
    <p><span style="font-weight: bold;">Exception Description:</span>Coupon numbers present in the RO raw data are not found in the Coupon and Discount API.</p>
    {{#if isFailed}}
    {{else}} 
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> Do not resume HALT. We need the Coupon and Discount CSV file to rerun, or we need to repull the data and confirm that the pulled data does not have this exception.</span></p>
    {{/if}}
    {{/if}}

    {{#if labor_with_no_paytype_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold;line-height:0%;">Exception Count:</span> {{labor_with_no_paytype_exception_count}}</p>
    <p><span style="font-weight: bold;line-height:0%;">Exception Name:</span> Labor with no paytype</p> 
    <p><span style="font-weight: bold;line-height:0%;">Exception Description:</span> Labor with a Null paytype.</p>
    {{#if isFailed}}
    {{else}}     
    <p><span style="font-weight: bold;line-height:0%;">Resolution:</span><span style="background-color:yellow;"> Do not resume HALT. We need actual ROs to verify the data and will need to re-run the extraction once Netspective confirms the code has been updated. Please request a minimum of 5 RO examples from the Dealer.</span></p>
    {{/if}}
    {{/if}}

    <!-- {{#if parts_excluded_from_history_exception_flag }}
    <p><mark style="font-size:bold;"> Exception Count:</mark>{{parts_excluded_from_history_exception_count}}</p>
    <p><mark style="font-size:bold;">Exception Name:</mark>Parts Excluded From History Exception Count</p> 
    <p><mark style="font-size:bold;">Exception Description:</mark>Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.</p>
    <p><mark style="font-size:bold;">Exception_key:<mark>parts_excluded_from_history</p>
    <p><mark style="font-size:bold;">Resolution:</mark><mark style="background-color: yellow;">OK to resume this HALT, but need Actual ROs to confirm. Please request a minimum of 5 RO examples from the Dealer.</mark></p>

    {{/if}} -->

    {{#if parts_excluded_from_history_exception_flag}}
    <br><br>
    <p><span style="font-weight: bold;line-height:0%;">Exception Count:</span> {{parts_excluded_from_history_exception_count}}</p>
    <p><span style="font-weight: bold;line-height:0%;">Exception Name:</span> Parts Excluded From History Exception Count</p> 
    <p><span style="font-weight: bold;line-height:0%;">Exception Description:</span> Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'.</p>
    {{#if isFailed}}
    {{else}}
        <p><span style="font-weight: bold;line-height:0%;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT, but need Actual ROs to confirm. Please request a minimum of 5 RO examples from the Dealer.</span></p>
    {{/if}}
    {{/if}}


    

    {{#if lost_sale_parts_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold; line-height: normal;"> Exception Count: {{lost_sale_parts_exception_count}} </span></p>
    <p><span style="font-weight: bold;">Exception Name:</span>Lost Parts Sale</p> 
    <p><span style="font-weight: bold;">Exception Description:</span>Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this</p>
    {{#if isFailed}}
    {{else}}      
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT, but need Actual ROs to confirm. We will need to request all of the qualified ROs in our range from the Dealer.</span></p>
    {{/if}}
    {{/if}}



    {{#if part_details_null_exception_flag }}
    <br><br>
    <p><span style="font-weight: bold;"> Exception Count: {{part_details_null_exception_count}}</span></p>
    <p><span style="font-weight: bold;">Exception Name:</span>Part Details Missing</p> 
    <p><span style="font-weight: bold;">Exception Description:</span>Parts that do not have a part number or description.</p>
    {{#if isFailed}}
    {{else}} 
    <p><span style="font-weight: bold;">Resolution:</span><span style="background-color:yellow;"> OK to resume this HALT, but need Actual ROs to confirm. We will need to request all of the qualified ROs in our range from the Dealer.</span></p>
    {{/if}}
    {{/if}}

    


    
    
    
</html>

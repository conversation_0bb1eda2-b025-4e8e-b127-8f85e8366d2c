<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Sharepoint File Upload</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #425f88;
            text-align: center;
            padding: 8px;
        }
        th {
            background-color:#425f88;
            color: white;
        }
        /* Green text color for success message */
        td.success {
            color: green;
        }
        /* Red text color for failed message */
        td.failed {
            color: red;
        }
        /* Violet text color for halt message */
        td.halt {
            color: #270559;
        }
        td.running {
            background-color: yellow;
        }
        td.scheduled {
            background-color: aqua;
        }
    </style>
</head>
<body>
    <!-- <div style="background-color: #f2f2f2;padding: 5px;">
        <img src="https://www.dealeruplift.com/wp-content/uploads/2019/10/Armatus-logo-new.svg">
    </div> -->
    <br>
     Hi,
    <br><br>
     Scheduler Report on  {{data.date}}
     <br><br>
    <table>
        <thead>
            <tr>
                <th>DMS</th>
                <th>Store Name</th>
                <th>Extraction Date Range</th>
                <th>Extraction Status</th>
                <th>Processor Status</th>
            </tr>
        </thead>
        <tbody>
            {{#each data}}
            <tr>
                <td>{{name}}</td>
                <td>{{parentName}}</td>
                <td>{{startDate}}-{{endDate}}</td>
                <td class="{{#if (eq message 'Success')}}success{{else if (eq message 'Halt')}}halt{{else if (eq message 'Running')}}running{{else if (eq message 'Scheduled')}}scheduled{{else}}failed{{/if}}">{{message}}</td>
                <td class="{{#if (eq processJobStatus 'Failed')}}failed{{else if (eq processJobStatus 'Halt')}}halt{{else if (eq processJobStatus 'Running')}}running{{else}}success{{/if}}">{{processJobStatus}}</td>
            </tr>
            {{/each}}
        </tbody>
    </table>
    <br><br>
    Thanks,<br>
    ADU-Scheduler

</body>
</html>

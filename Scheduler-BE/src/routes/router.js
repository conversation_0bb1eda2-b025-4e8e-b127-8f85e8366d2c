const constants = require("../common/constants");
const rp = require('request-promise');
var express = require("express");
var router = express.Router();
var bodyParser = require("body-parser");
var { graphqlExpress, graphiqlExpress } = require("graphql-server-express");
const SegmentSchema = require("../controllers/SEGMENT/graphql/schema");
const azureJWT = require("azure-jwt-verify");
const { v4: uuidv4 } = require('uuid');
const agendaDbModel = require('../model/agendaDb');

var s360 = require("./s360");
var payTypes = require("./payTypes");
var checkDealerTrackAccessValidation = require("./checkDealerTrackAccessValidation");
const getDealerBuiltAccountingCSVFiles = require("./getDealerBuiltAccountingCSVFiles");
const getReynoldsInputFiles = require("./getReynoldsInputFiles");
const getAutosoftInputFiles = require("./getAutosoftInputFiles");
const getPbsInputFiles = require("./getPbsInputFiles");
const getUcsInputFiles = require("./getUcsInputFiles");
const getMpkInputFiles = require("./getMpkInputFiles");
const getTekionInputFiles = require("./getTekionInputFiles");
const getQuorumInputFiles = require("./getQuorumInputFiles");
const getMockServerInputFiles = require("./getMockServerInputFiles");
const getExtractJob = require("./getExtarctJob");
const graphqlCustom = require("./graphqlCustom");
const portal = require("./portal");
const schedulerCustom = require("./schedulerCustom");
const authRoutes = require('./auth');
var api = require("../../src/controllers/api");
const { logger } = require('../logger/schedulerLogger');
const appConstants = require("../common/constants");


const triggerScheduleReynolds = require("./triggerScheduleReynolds");
const uploadDealerTrackCouponAndDiscountCSVFile = require("./uploadDealerTrackCouponAndDiscountCSVFile");
const uploadDealerTrackChartOfAccountsFile = require("./uploadDealerTrackChartOfAccountsFile");
const importCDK = require("../../src/routes/importCdk");
const fileImportCDK = require("../../src/routes/fileImportCdk")
const getFileCDK = require("../../src/routes/getFileCdk");
const cdkDumpFile = require("../../src/routes/cdkDumpFile")

const sharepointAuth = require("./sharePoint");
const fs = require('fs');
const Cryptr = require('cryptr');
const segment = require("../controllers/SEGMENT/segmentManager");

var solve360Update = require("./solve360Update");
var mailSender = require("./mailSender");
const env = process.env;
const haltAndResumeController = require("../common/haltAndResume");
const importDealerBuilt = require("../../src/routes/importDealerBuilt");
const importRCI = require("../../src/routes/importRCI");
const importDealerTrack = require("../../src/routes/importDealerTrack");
const importAutomate = require("../../src/routes/importAutomate");
const importTekion = require("../../src/routes/importTekion");
const importPBS = require("../../src/routes/importPBS");
const importUCS = require("../../src/routes/importUCS");
const importMPK = require("../../src/routes/importMPK");
const importTekionAPI = require("../../src/routes/importTekionAPI");
const importAutosoft = require("../../src/routes/importAutosoft");
const importDominionVue = require("../../src/routes/importDominionVue");
const importADAM = require("../../src/routes/importADAM");
const importQuorum = require("../../src/routes/importQuorum");
const importReynolds = require("../../src/routes/importReynolds");
const schedulerImportDetails = require("../../src/routes/schedulerImportDetails");
const stageUpdatePortal = require("../../src/routes/stageUpdatePortal");
const removeQueueItem = require('../routes/removeQueueItem');

const updatePriority = require('../routes/updatePriority');
const schedulerAddFields = require("../../src/routes/schedulerAddFields");
const getSchedulerFields = require("../../src/routes/getSchedulerFields");
const getGroups = require('../../src/routes/getGroups');
const getQueueData = require('../routes/getQueueData');
const reQueue = require('../routes/reQueue');
// const saveUser = require('../routes/saveUser');
const { getUserName, updateUserName } = require('../routes/saveUser'); 
const { updateMake,updateManufacturer } = require('../routes/addMakesAsGlobal'); 
const {getMakeList} = require('../routes/makeList');
const fetchJobFromProcessorQueue = require("../routes/fetchProcessStatus");


/**
 * Dashboard for managing the Agenda Jobs.
 * Comment following 2 lines to disable the dashboard option
 */
var agendashboard = require("./agendashboard");


// For Segmentation
router.use(
    "/segment/graphql",
    bodyParser.json(),
    graphqlExpress({ schema: SegmentSchema })
);
router.use(
    "/segment/graphiql",
    graphiqlExpress({ endpointURL: "/scheduler/segment/graphql" })
);
// router.use("/", router);
/**
 * Upload files to SharePoint
 *
 */

router.use("/triggerScheduleReynolds", triggerScheduleReynolds);
// router.use("/getDealerBuiltAccountingCSVFiles", getDealerBuiltAccountingCSVFiles);

router.use("/getSchedulerFields", (req, res, next) => {
    const schedulerToken = req.headers['x-scheduler-token'];
    if (schedulerToken) {
        req.token = Buffer.from(schedulerToken, 'base64').toString('utf8');
        if (req.token !== appConstants.conf.IN_SCHEDULER_TOKEN) {
            logger.error('JWT_TOKEN_PARSE_ERROR [Unknown]: ', appConstants.SCHEDULER_TOKEN_ERROR);
            res.status(400).json({ errors: [appConstants.SCHEDULER_TOKEN_ERROR] });
        } else {
            next();
        }
    }
});

router.use("/getSchedulerFields", getSchedulerFields);


function getImportHaltStatus(schedulerId) {
    return `query {
        getSchedulerPreImportStatusBySchedulerId(inSchedulerId: "${schedulerId}")
    }`;
}

function getExceptionKey() {
  return `query {
    allExceptionMasters {
      nodes {
        exceptionKey
        exceptionName
        exceptionDescription
        isRelevant
      }
    }
  }`;
}



function insertSchedulerImportDetails(inSubmissionUuid, inSchedulerId, inSchedulerId, inCompanyId, inProjectIdList) {
    return `mutation{
    insertSchedulerImportDetails(input:{
    inSubmissionUuid: ${inSubmissionUuid},
    inSchedulerId: ${inSchedulerId},
    inCompanyId: ${inCompanyId},  
    inProjectIdList:${inProjectIdList}
  })
  {
    json
  }
}`
}

function getOptions(URI, query) {
    return {
        method: 'POST',
        uri: URI,
        body: JSON.stringify({ query: query }),
        headers: {
            'Content-Type': 'application/json',
        },
    };
}




router.post("/publicFileUploadToSharePoint", async(req, res, next) => {
    console.log("req.body>>>>>>>>>>>>>?", req.body);
    segment.saveSegment(`req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>${req.body}`);
    var filePath = req.body.filePath;
    var secretKey = req.body.secretKey;
    var isRerun = req.body.isRerun;
    var projectId = req.body.projectId;
    var secondProjectId = req.body.secondProjectId;
    var updateRetreiveROinSolve = req.body.solve360Update;
    var userName = req.body.userName;
    var thirdPartyUsername = req.body.thirdPartyUsername;
    var storeCode = req.body.storeCode;
    var groupCode = req.body.groupCode;
    var dmsType = req.body.dmsType;
    var resumeUser = req.body.resumeUser;
    var exceptions = req.body.exceptions;
    var projectIds = req.body.projectIds;
    var secondProjectIdList = req.body.secondProjectIdList;
    var inSchedulerId = req.body.inSchedulerId;
    var testData = req.body.testData;
    var companyObj = req.body.companyObj;
    var processFileName = req.body.fileName;
    var totalRoCount = req.body.totalRoCount;
    var exceptionTypeCounts = req.body.exceptionTypeCounts;
    let isAnyRelevant = false;
    var sharedFolderPath = sharepointAuth.sharePointFileUploadDir(
        req.body.dmsType
    );
    var mailTemplateReplacementValues = {
        fileName: filePath,
        filePath: filePath,
        dmsType: sharedFolderPath,
        warningObj: req.body.warningObj,
        status: "Success",
        thirdPartyUsername: thirdPartyUsername,
        storeCode: storeCode,
        groupCode: groupCode,
        dmsType: dmsType,
        resumeUser: resumeUser,
    };
    var mailBody = {
        fromAddress: appConstants.NOTIFICATION.FROMADDRESS,
        toAddress: appConstants.NOTIFICATION.TOADDRESS,
        ccAddress: appConstants.NOTIFICATION.CCADDRESS,
    };
    console.log(">>>>>--------------------", filePath, secretKey);

    let importFileExist = false;
    let importHaltFilePath = '';

    if (dmsType.toLowerCase() == 'cdk3pa') {
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception/halt-import.txt';
        importFileExist = await isHaltFileExist(importHaltFilePath);
    } else if (dmsType.toLowerCase() == 'automate') {
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-automate-extractor-work/exception/halt-import.txt';
        importFileExist = isHaltFileExist(importHaltFilePath);
    } else if (dmsType.toLowerCase() == 'dealerbuilt') {
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-dealerbuilt-extractor-work/exception/halt-import.txt';
        importFileExist = isHaltFileExist(importHaltFilePath);
    } else if (dmsType.toLowerCase() == 'dealertrack') {
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/halt-import.txt';
        importFileExist = isHaltFileExist(importHaltFilePath);
    } else if  (['reynoldsrci', 'ucs'].includes(dmsType.toLowerCase())) {
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/halt-import.txt';
        importFileExist = isHaltFileExist(importHaltFilePath);
    } else if (dmsType.toLowerCase() == 'adam') {
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-adam-extractor-work/exception/halt-import.txt';
        importFileExist = isHaltFileExist(importHaltFilePath);
    } else if (dmsType.toLowerCase() == 'dominion') {
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-dominionvue-extractor-work/exception/halt-import.txt';
        importFileExist = isHaltFileExist(importHaltFilePath);
    } else if (dmsType.toLowerCase() == 'autosoft') {
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-autosoft-extractor-work/exception/halt-import.txt';
        importFileExist = isHaltFileExist(importHaltFilePath);
    } else if (dmsType.toLowerCase() == 'tekion') {
        console.log()
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-tekion-extractor-work/exception/halt-import.txt';
        importFileExist = isHaltFileExist(importHaltFilePath);
    } else if (dmsType.toLowerCase() == 'quorum') {
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-quorum-extractor-work/exception/halt-import.txt';
        importFileExist = isHaltFileExist(importHaltFilePath);
    } else if (dmsType.toLowerCase() == 'pbs') {
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-pbs-extractor-work/exception/halt-import.txt';
        importFileExist = isHaltFileExist(importHaltFilePath);
    }else if (dmsType.toLowerCase() == 'fortellis') {
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/halt-import.txt';
        importFileExist = isHaltFileExist(importHaltFilePath);
    }else if (dmsType.toLowerCase() == 'tekionapi') {
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-tekionapi-extractor-work/exception/halt-import.txt';
        importFileExist = isHaltFileExist(importHaltFilePath);


    }

    function isHaltFileExist(filePath) {
        // return new Promise((resolve,reject)=>{
        if (fs.existsSync(filePath)) {
            console.log('File exists.');
            unLinkFile(filePath)
            return true;
        } else {
            return false;
        }
        // })

    }

    function unLinkFile(filePath) {
        return new Promise((resolve, reject) => {
            fs.unlink(filePath, (err) => {
                if (err) {
                    console.error('Error deleting the file:', err);
                    segment.saveSegment(`Error deleting the file::${err}`);
                    reject(false)
                } else {
                    console.log('File deleted successfully!');
                    resolve(true);
                }
            });
        })
    }

    if (filePath && secretKey) {
        console.log(">>>>>--------------------", filePath, secretKey);
        console.log(">>>>>--------------------", filePath, fs);

        if (fs.existsSync(filePath)) {
            console.log("existsSync>>>>>--------------------", fs.existsSync(filePath));
            console.log("SHAREPOINT_API_PRIVATE_SECRET_KEY>>>>>--------------------", appConstants.conf.SHAREPOINT_API_PRIVATE_SECRET_KEY);

            try {
                segment.saveSegment(`Dist file exist for sharepoint upload`);
                console.log("SHAREPOINT_API_PRIVATE_SECRET_KEY>>>>>--------------------", appConstants.conf.SHAREPOINT_API_PRIVATE_SECRET_KEY);
                const cryptr = new Cryptr(
                    appConstants.conf.SHAREPOINT_API_PRIVATE_SECRET_KEY
                );
                console.log(">>>>>cryptr", cryptr);

                const decryptedKeyValue = cryptr.decrypt(secretKey);
                console.log(">>>>>decryptedKeyValue", decryptedKeyValue);
                segment.saveSegment(
                    `decryptedKeyValue:${decryptedKeyValue} and share point public secret key: ${appConstants.conf.SHAREPOINT_API_PUBLIC_SECRET_KEY}`
                );
                if (
                    decryptedKeyValue === appConstants.conf.SHAREPOINT_API_PUBLIC_SECRET_KEY
                ) {
                    segment.saveSegment(
                        `decryptedKeyValue and share point public secret key are equal`
                    );
                    console.log(">>>>>filePathsharedFolderPath", filePath, sharedFolderPath);

                    //  var uploadResult = await sharepointAuth.uploadFile(
                    //   filePath,
                    //   sharedFolderPath
                    //  );

                    uploadResult = await sharepointAuth.uploadFileUsingRclone(filePath, sharedFolderPath)
                    console.log(">>>>>uploadResult", uploadResult);

                    segment.saveSegment(`Upload result is: ${JSON.stringify(uploadResult)}`);
                    if (uploadResult) {
                        console.log("test11111111111111111111111111111111111111111111111111111");
                        segment.saveSegment(`Sharepoint upload completed`);
                        console.log("Success: Sharepoint upload success");
                        console.log("projectId:", projectId);
                        segment.saveSegment(`projectId: ${projectId}`);
                        console.log("secondProjectId:", secondProjectId);
                        segment.saveSegment(`secondProjectId: ${secondProjectId}`);
                        console.log("userName:", userName);
                        segment.saveSegment(`userName: ${userName}`);
                        console.log("isRerun:", isRerun);
                        segment.saveSegment(`isRerun: ${isRerun}`);
                        console.log("UPDATE_SOLVE360:", env.UPDATE_SOLVE360);
                        segment.saveSegment(`UPDATE_SOLVE360: ${env.UPDATE_SOLVE360}`);
                        console.log("solve360Update", updateRetreiveROinSolve);
                        segment.saveSegment(`solve360Update: ${solve360Update}`);
                        console.log("projectids", projectIds);
                        segment.saveSegment(`projectIds: ${projectIds}`);
                        console.log("secondProjectIdList", secondProjectIdList);
                        segment.saveSegment(`secondProjectIdList: ${secondProjectIdList}`);
                        console.log("dmsType", dmsType);
                        segment.saveSegment(`dmsType: ${dmsType}`);
                        console.log("exceptions", exceptions);
                        segment.saveSegment(`exceptions: ${exceptions}`);
                        console.log("inSchedulerId", inSchedulerId);
                        segment.saveSegment(`inSchedulerId: ${inSchedulerId}`);
                        console.log("testData", testData);
                        segment.saveSegment(`testData: ${testData}`);
                        console.log("companyObj", companyObj);
                        segment.saveSegment(`companyObj: ${companyObj}`);
                        console.log("processFileName", processFileName);
                        segment.saveSegment(`processFileName: ${processFileName}`);
                        console.log("totalRoCount", totalRoCount);
                        segment.saveSegment(`totalRoCount: ${totalRoCount}`);
                        console.log("exceptionTypeCounts", exceptionTypeCounts);
                        segment.saveSegment(`exceptionTypeCounts: ${exceptionTypeCounts}`);

                        

                        //Update solve 360 pulled date
                        console.log("env.UPDATE_SOLVE360>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", env.UPDATE_SOLVE360);
                        console.log("projectId.length>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", projectId.length);
                        console.log("testData.length>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", testData);
                        console.log("companyObj>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", companyObj);

                        var schedulerQuery = getImportHaltStatus(inSchedulerId);
                        var exceptionQuery = getExceptionKey();
                        const schedulerOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, schedulerQuery);
                        const exceptionQueryOpt = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, exceptionQuery);
                        var importHaltStatus;

                        rp(schedulerOptions)
                            .then((response) => {
                                console.log('Response:', response);
                                segment.saveSegment(`'Import Halt status response!!!${response}`);
                                importHaltStatus = response


                                console.log("importHaltStatus????????????????????????????????????????????????????????????????????", importHaltStatus);
                                segment.saveSegment(`ImportHaltStatus:${importHaltStatus}`);
                                // Process the response here
                            })
                            .catch((err) => {
                                console.error('Error:', err);

                                console.log("importHaltStatus Error????????????????????????????????????????????????????????????????????", err);
                                segment.saveSegment(`ImportHaltStatusErr:${err}`);
                            });



                   rp(exceptionQueryOpt)
  .then((response) => {
    console.log('Raw exceptionQueryOpt Response:', response);

    // If response is a string, parse it
    let parsedResponse = response;
    if (typeof response === 'string') {
      try {
        parsedResponse = JSON.parse(response);
      } catch (err) {
        console.error("❌ Failed to parse response JSON:", err);
        segment.saveSegment(`❌ Failed to parse response JSON: ${response}`);
        return;
      }
    }

    // Validate parsed response structure
    const masterData = parsedResponse?.data?.allExceptionMasters?.nodes;
    if (!Array.isArray(masterData)) {
      console.error("❌ Invalid GraphQL response: missing 'allExceptionMasters.nodes'");
      segment.saveSegment("❌ Invalid GraphQL structure");
      return;
    }

    segment.saveSegment(`✅ Parsed GraphQL data: ${JSON.stringify(masterData)}`);
    segment.saveSegment(`✅ Input exceptionTypeCounts: ${JSON.stringify(exceptionTypeCounts)}`);

    const transformedObj = Object.entries(exceptionTypeCounts).reduce((acc, [key, value]) => {
      const match = masterData.find(item => item.exceptionKey === key);
      if (match) {
        acc[match.exceptionName] = value;
        if (match.isRelevant) {
          isAnyRelevant = true;
        }
      }
      return acc;
    }, {});

    console.log("✅ Transformed Object:", transformedObj);
    segment.saveSegment(`✅ Transformed Object: ${JSON.stringify(transformedObj)}`);
    segment.saveSegment(`✅ isAnyRelevant: ${isAnyRelevant}`);

    // Save final result in a variable
    // const importHaltStatus = {
    //   transformedObj,
    //   isAnyRelevant,
    //   rawResponse: parsedResponse,
    // };

    // console.log("✅ importHaltStatus:", importHaltStatus);
    // segment.saveSegment(`✅ importHaltStatus: ${JSON.stringify(importHaltStatus)}`);
    exceptionTypeCounts = transformedObj;
        segment.saveSegment(`✅ exceptionTypeCounts: ${JSON.stringify(exceptionTypeCounts)}`);
    // Use `importHaltStatus` below if needed

  })
  .catch((err) => {
    console.error("❌ Error while querying exception master:", err);
    segment.saveSegment(`❌ ImportHaltStatus Error: ${err.message || err}`);
  });





                        await segment.sleep(5000);

                        segment.saveSegment(`inSchedulerId=${inSchedulerId}projectIds${projectIds}secondProjectIdList${secondProjectIdList}importHaltStatus${importHaltStatus}importFileExist${importFileExist}`)


                        if (projectId && !testData) {
                            if (process.env.UPDATE_SOLVE360) {
                                console.log("Begin solve360 update");
                                try {
                                    const status = await solve360Update.updateDate(projectId, userName, updateRetreiveROinSolve, isRerun);
                                    console.log("Solve 360 Update status:", status);
                                } catch (err) {
                                    console.error("Solve 360 Update error:", err);
                                }
                            } else {
                                console.log("UPDATE_SOLVE360 is false in env");
                            }


                            if (projectIds.length > 0 && !testData) {
                                for (const id of projectIds) {
                                    if (id) {
                                        try {
                                            console.log(`Inside portal update complete with project id:${id}`);
                                            if (exceptions) {
                                                await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, exceptions, inSchedulerId,false,false,{});
                                                segment.saveSegment(`exceptionTypeCounts1%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%${exceptionTypeCounts}`);
                                                await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, exceptions, inSchedulerId,false,isAnyRelevant,exceptionTypeCounts);

                                                if (importHaltStatus.length > 0 && importFileExist == true) {
                                                    await segment.sleep(2000);
                                                    await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, exceptions, inSchedulerId, true);
                                                     segment.saveSegment(`exceptionTypeCounts2%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%${exceptionTypeCounts}`);
                                                    await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, exceptions, inSchedulerId, true,isAnyRelevant,exceptionTypeCounts);
                                                }
                                            } else {
                                                await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, null, inSchedulerId,false,false,{});
                                                 segment.saveSegment(`exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%${exceptionTypeCounts}`);
                                                 await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, null, inSchedulerId,true,isAnyRelevant,exceptionTypeCounts);

                                                if (importHaltStatus.length > 0 && importFileExist == true) {
                                                    await segment.sleep(2000);
                                                    await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, exceptions, inSchedulerId, true,false,{});
                                                    segment.saveSegment(`exceptionTypeCounts4%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%${exceptionTypeCounts}`);
                                                    await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, exceptions, inSchedulerId, true,isAnyRelevant,exceptionTypeCounts);
                                                }
                                                
                                            }
                                        } catch (err) {
                                            console.error("doPayloadActionComplete error:", err);
                                        }
                                    }
                                }
                            }
                        } else {
                            console.log("ProjectId not found or testData is true");
                        }
                        console.log("second project id list@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@", secondProjectIdList);
                        console.log("testData!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", testData);
                        if (secondProjectIdList && !testData) {
                            console.log("test322222222222222222222222222222222222222222222222222222");
                            if (process.env.UPDATE_SOLVE360) {
                                console.log("Begin solve360 update for second projectId");
                                try {
                                    const status = await solve360Update.updateDate(secondProjectId, userName, updateRetreiveROinSolve, isRerun);
                                    console.log("Solve 360 Update status for second projectId:", status);
                                } catch (err) {
                                    console.error("Solve 360 Update error for second projectId:", err);
                                }
                            } else {
                                console.log("UPDATE_SOLVE360 is false in env");
                            }

                            segment.saveSegment(`dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@${dmsType}`);


                            if (secondProjectIdList.length > 0) {
                                console.log("test44444444444444444444444444444444444444444444444444444444444444");
                                for (const id of secondProjectIdList) {
                                    console.log("test555555555555555555555555555555555555555", id);
                                    if (id && id !== undefined) {
                                        try {
                                            console.log(`Inside portal update complete with second project id:${id}`);
                                            await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, null, inSchedulerId);
                                            await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, null, inSchedulerId,isAnyRelevant,exceptionTypeCounts);
                                            await segment.sleep(2000);
                                        } catch (err) {
                                            console.error("doPayloadActionComplete error for second projectIdList:", err);
                                            segment.saveSegment(`PORTAL CALL WITH SECOND PROJECT ID HAS ERROR${err}`);
                                        }
                                        console.log(`importHaltStatus.length > ${importHaltStatus.length}`);
                                        console.log(`importFileExist> ${importFileExist}`);
                                        segment.saveSegment(`importHaltStatus.length > ${importHaltStatus.length}`);
                                        segment.saveSegment(`importFileExist> ${importFileExist}`);
                                        if (importHaltStatus.length > 0 && importFileExist == true) {
                                            console.log(`test2@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@`);
                                            segment.saveSegment(`test2@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@`)
                                            try {
                                                console.log(`Inside portal update complete with second project id:${id}`);
                                                await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, null, inSchedulerId, true);
                                                await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, null, inSchedulerId, true,isAnyRelevant,exceptionTypeCounts);
                                            } catch (err) {
                                                console.error("doPayloadActionComplete error for second projectIdList:", err);
                                                segment.saveSegment(`IMPORT HALT WITH SECOND PROJECT ID HAS ERROR${err}`);
                                            }
                                        }
                                    }

                                    console.log("second project id has error$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$");


                                }
                            }

                        } else {
                            console.log("Second ProjectId not found");
                        }



                        try {
                         
                            segment.saveSegment(`companyObj111:${companyObj}`);
                            console.log("companyObj111", companyObj);
                            if (companyObj) {
                                companyObj.map((item) => {
                                    let inSubmissionUuid = uuidv4();
                                    let inSchedulerIdvalue = inSchedulerId;
                                    let inCompanyId = item.companyId;
                                    let projectidList = [];
                                    if (item.projectId) {
                                        projectidList.push(item.projectId);
                                    }
                                    if (item.secondProjectId) {
                                        projectidList.push(item.secondProjectId);
                                    }
                                    console.log("inSubmissionUuid", inSubmissionUuid);
                                    console.log("inSchedulerId", inSchedulerIdvalue);
                                    console.log("inCompanyId", inCompanyId);
                                    console.log("projectidList", projectidList);
                                    //     const mutation = gql`
                                    //     mutation updateScheduleDetails($input: UpdateScheduleDetailsInput!) {
                                    //       updateScheduleDetails(input: $input) {
                                    //         json
                                    //       }
                                    //     }
                                    //   `;

                                    //     const mutation = `
                                    //     mutation {
                                    //       updateScheduleDetails(input: {
                                    //         inSchedulerId: ${inSchedulerIdvalue}
                                    //         inCompanyId: ${inCompanyId}
                                    //         inProjectIdList: ${projectidList}
                                    //         inCreatedBy: ${userName}
                                    //         inDms: ${dmsType}
                                    //       }) {
                                    //         json
                                    //       }
                                    //     }
                                    //   `;

                                    //   const variables = {
                                    //     input: {

                                    //       inSchedulerId: inSchedulerIdvalue ,
                                    //       inCompanyId: inCompanyId,  // BigInt as a string
                                    //       inProjectIdList: projectidList,  // BigInt array elements as strings
                                    //       inCreatedBy: userName,
                                    //       inDms:dmsType

                                    //     }
                                    //   };
                                    let reportFileName = processFileName.replace(/\.zip$/, '-REPORT.zip');
                                     console.log("report filename$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",reportFileName);
                                    if (typeof totalRoCount === 'string') {
  const trimmed = totalRoCount.trim(); // remove whitespace and \n
  const parsed = parseInt(trimmed, 10);
  if (!isNaN(parsed)) {
    totalRoCleaned = parsed;
  }
}

// If it's already a number
if (typeof totalRoCount === 'number' && !isNaN(totalRoCount)) {
  totalRoCleaned = totalRoCount;
}




                                    const mutation =
                                        `
                                                 mutation {
                                                    updateScheduleDetails(input: {
                                                    inSchedulerId: "${inSchedulerIdvalue}"
                                                    inCompanyId: "${inCompanyId}"
                                                    inProjectIdList: ["${projectidList.join('", "')}"]
                                                    inCreatedBy: "${userName}"
                                                    inDms: "${dmsType}"
                                                    inStatus:"complete"
                                                    inBundleFileName:"${processFileName}"
                                                    inReportFileName:"${reportFileName}"
                                                    inTotalRos:"${totalRoCleaned}"
                                                     }) {
                                                      json
                                                    }
                                                 }
                                               `;


                                    const options = {
                                        method: 'POST',
                                        uri: appConstants.conf.GRAPHQL_SCHEDULER_URI,
                                        body: {
                                            query: mutation

                                        },
                                        json: true // Automatically stringifies the body to JSON
                                    };

                                    console.log("insert Options$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$", options);
                                    segment.saveSegment(`ImportHaltStatus OPTIONS:${options}`);


                                    rp(options)
                                        .then((response) => {
                                            console.log('Response:', response);



                                            console.log("insert status????????????????????????????????????????????????????????????????????", response);
                                            segment.saveSegment(`ImportHaltStatus:${response}`);
                                            // Process the response here
                                        })
                                        .catch((err) => {
                                            console.error('Error:', err);

                                            console.log("insert status Error????????????????????????????????????????????????????????????????????", err);
                                            segment.saveSegment(`ImportHaltStatusErr:${err}`);
                                        });

                                })
                            }
                        } catch (err) {
                            segment.saveSegment(
                                `Solve 360 Update or portal Update have error:${JSON.stringify(err)}`
                            );
                            console.log(err);
                        }

                        try {
                            var fileName = filePath ? filePath.split("/").pop(-1) : "";
                            mailTemplateReplacementValues.fileName = fileName;
                            mailTemplateReplacementValues.isRerun = isRerun;
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            var statusObj = await mailSender.sendMail(
                                mailBody,
                                appConstants.NOTIFICATION_TYPES.SHARE_POINT
                            );
                            segment.saveSegment(`Email sent response: ${JSON.stringify(statusObj)}`);
                            segment.saveSegment(`Email sent status: ${statusObj.status}`);
                            if (statusObj.status == "success") {
                                console.log(
                                    "Notification mail send successfully",
                                    JSON.stringify(mailTemplateReplacementValues)
                                );
                                segment.saveSegment(
                                    `Notification mail send successfully ${JSON.stringify(
          mailTemplateReplacementValues
         )}`
                                );
                            } else {
                                console.log(
                                    "Failed to send Notification mail",
                                    JSON.stringify(mailTemplateReplacementValues)
                                );
                                segment.saveSegment(
                                    `Failed to send Notification mail ${JSON.stringify(
          mailTemplateReplacementValues
         )}`
                                );
                            }
                        } catch (err) {
                            console.log("Error: Failed to send Mail", err);
                            segment.saveSegment(err.toString());
                        }
                        res.status(200).json({
                            errors: [{
                                status: "Success",
                                message: "File uploaded successfully to SharePoint",
                            }, ],
                        });
                    }
                } else {
                    res.status(400).json({
                        errors: [{ status: "Failure", message: "Api authentication failed" }],
                    });
                }
            } catch (err) {
                res.status(400).json({
                    errors: [{ status: "Failure", message: "Token Mismatch Error" }],
                });
            }
        } else {
            res.status(400).json({
                errors: [
                    { status: "Failure", message: "Requested file path doesn't exists!" },
                ],
            });
        }
    } else {
        res.status(400).json({
            errors: [{ status: "Failure", message: "Missing required parameters!" }],
        });
    }
});

// router.post("/localFileUploadToSharePoint", async (req, res, next) => {
//  var filePath = req.body.filePath;
//  var sharedFolderPath = req.body.dmsType;
//  if (filePath) {
//   if (fs.existsSync(filePath)) {
//    var uploadResult = await sharepointAuth.uploadFile(
//     filePath,
//     sharedFolderPath
//    );
//    if (
//     uploadResult &&
//     uploadResult.data &&
//     uploadResult.data.ServerRelativeUrl
//    ) {
//     console.log("Success:", uploadResult);
//     res.status(200).json({
//      errors: [
//       {
//        status: "Success",
//        message: "File uploaded successfully to SharePoint",
//       },
//      ],
//     });
//    } else {
//     console.log("Failed:", uploadResult);
//     res.status(400).json({
//      errors: [
//       {
//        status: "Failure",
//        message: "Error while uploading files to SharePoint",
//       },
//      ],
//     });
//    }
//   } else {
//    res.status(400).json({
//     errors: [
//      { status: "Failure", message: "Requested file path doesn't exists!" },
//     ],
//    });
//   }
//  } else {
//   res.status(400).json({
//    errors: [{ status: "Failure", message: "Missing required parameters!" }],
//   });
//  }
// });



//#region Authentication 

// Read the token
router.use('/', (req, res, next) => {
    req.setTimeout(1800000);
    let token = '';
    if (req.headers.authorization && req.headers.authorization.split(' ')[0] === 'Bearer') {
        token = req.headers.authorization.split(' ')[1];
        req.token = token;
        next();
    } else {
        logger.error('JWT_TOKEN_PARSE_ERROR [Unknown]: ', appConstants.JWT_TOKEN_ERROR);
        res.status(400).json({ errors: [appConstants.JWT_TOKEN_ERROR] });
    }
});

// Validate the token
router.use('/', (req, res, next) => {
    const config = {
        JWK_URI: appConstants.JWK_URI,
        ISS: appConstants.ISS,
        AUD: appConstants.conf.AUD
    };
    // console.log('****config*****', config);
    // console.log('****req.token*****', req.token);

    let i = 1;
    doApiCallValidateJwtToken();

    function doApiCallValidateJwtToken() {
        //  console.log(':::::::::::TOKEN::::::::::::', req.token, config);

        azureJWT
            .verify(req.token, config)
            .then(response => {
                const result = JSON.parse(response);
                logger.info(`JWT_TOKEN_OBJJJJJJJJJ [${response}]`);
                req.jwtToken = result;
                //    console.log('****jwtToken*****',result);
                logger.info(`JWT_TOKEN_VALIDITY [${req.jwtToken.message.name}]: JWT Token successfully validated`);
                if (i !== 1) {
                    logger.info(`JWT_TOKEN_VALIDITY [${req.jwtToken.message.name}]: API call to JWT_TOKEN_VALIDITY success in ${i}th attempt`);
                }
                i = 1;
                next();

            }).catch(error => {
                console.log('****error*****', error);
                if (i < 4) { // retry twise and throw if the error still exist
                    logger.error(`JWT_TOKEN_VALIDITY [Unknown]: Retrying ${i}th time due to : `, error);
                    i++;
                    doApiCallValidateJwtToken();
                } else {
                    logger.error('JWT_TOKEN_VALIDITY [Unknown]: ', error);
                    res.status(400).json({ errors: [{ status: '[ERR: 5001]', message: JSON.parse(error).message && JSON.parse(error).message.message ? JSON.parse(error).message.message : 'Token Error' }] });
                }
            });
    }
});

// Find the access token if the no of groups are greater than 5
router.use('/', (req, res, next) => {
            if (req.jwtToken.message.hasgroups) {
                const options = {
                    method: 'POST',
                    uri: appConstants.OTB_URL,
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        Connection: 'keep-alive'
                    },
                    formData: {
                        grant_type: appConstants.OTB_GRANT_TYPE,
                        client_id: appConstants.conf.AUD,
                        client_secret: appConstants.conf.CLIENT_SECRET,
                        client_assertion_type: appConstants.OTB_CLIENT_ASSERTION_TYPE,
                        requested_token_use: appConstants.OTB_REQ_TOKEN_USE,
                        scope: appConstants.OTB_SCOPE,
                        assertion: req.token,
                    },
                };
                let i = 1;
                doApiCallGetAccessToken();
                // eslint-disable-next-line no-inner-declarations
                function doApiCallGetAccessToken() {
                    rp(options)
                        .then(response => {
                            if (i !== 1) {
                                logger.info(`ACCESS_TOKEN_REQUEST [${req.jwtToken.message.name}]: Azure Graph API call for ACCESS_TOKEN_REQUEST success in ${i}th attempt`);
                            }
                            i = 1;
                            const result = JSON.parse(response);
                            req.accessToken = result.access_token;
                            logger.info(`ACCESS_TOKEN_REQUEST [${req.jwtToken.message.name}]: Access token request success`);
                            next();
                        }).catch(error => {
                                if (i < 4) { // retry twise and throw if the error still exist
                                    logger.error(`USER_GROUP_REQUEST [${req.jwtToken.message.name}]: Retrying ${i}th time due to : `, error);
                                    i++;
                                    doApiCallGetAccessToken();
                                } else {
                                    logger.error(`ACCESS_TOKEN_REQUEST [${req.jwtToken.message.name}]: `, error);
                                    res.status(400).json({
                                                errors: [{
                                                            status: '[ERR: 4001]',
                                                            message: `API call to get Access token failed ${error.message ? `(Hint: ${error.message})` : ''}`,
                       }]
                   });
               }
           });
   }
} else if (req.jwtToken.message.groups) {
   req.userGroups = req.jwtToken.message.groups;
   next();
} else {
   res.status(400).json({ errors: [appConstants.HAS_NO_GROUPS_MESSAGE] });
}
});

// Find the groups from access token
router.use('/', (req, res, next) => {
if (req.accessToken) {
   const options = {
       method: 'GET',
       uri: appConstants.GRAPH_URL,
       headers: {
           'Content-Type': 'application/json',
           'Authorization': 'Bearer ' + req.accessToken
       }
   };
   let i = 1;
   doApiCall();
   // eslint-disable-next-line no-inner-declarations
   function doApiCall() {
       rp(options)
           .then(success => {
               if (i !== 1) {
                   logger.info(`USER_GROUP_REQUEST [${req.jwtToken.message.name}]: Azure Graph API call for user group info success in ${i}th attempt`);
               }
               i = 1;
               const result = JSON.parse(success);
               const groups = [];
               result.value.forEach(element => {
                   groups.push(element.id);
               });
               req.userGroups = groups;
               next();
           }).catch(error => {
               if (i < 4) { // retry twise and throw if the error still exist
                   logger.error(`USER_GROUP_REQUEST [${req.jwtToken.message.name}]: Retrying ${i}th time due to : `, error);
                   i++;
                   doApiCall();
               } else {
                   logger.error(`USER_GROUP_REQUEST [${req.jwtToken.message.name}]: `, error);
                   res.status(400).json({
                       errors: [{
                           status: '[ERR: 4001]',
                           message: `Azure Graph API call for user group info failed ${error.message ? `(Hint: ${error.message})` : ''}`,
                       }]
                   });
               }
           });
   }
} else {
   next();
}
});

//#endregion Authentication 
// router.use("/getDealerBuiltAccountingCSVFiles", getDealerBuiltAccountingCSVFiles);
router.use("/auth", authRoutes);
router.use("/schedulerCustom", schedulerCustom);
router.use("/graphqlCustom", graphqlCustom);
router.use("/portal", portal);
router.use("/fileImportCdk", fileImportCDK);
router.use("/getFileCDK", getFileCDK);
router.use("/cdkImport", importCDK);
router.use("/cdkDumpFile", cdkDumpFile);
router.use("/s360", s360);
router.use("/payTypes", payTypes);
router.use("/getDealerBuiltAccountingCSVFiles", getDealerBuiltAccountingCSVFiles);
router.use("/checkDealerTrackAccessValidation", checkDealerTrackAccessValidation);
router.use("/getReynoldsInputFiles", getReynoldsInputFiles);
router.use("/getAutosoftInputFiles", getAutosoftInputFiles);
router.use('/getTekionInputFiles',getTekionInputFiles);
router.use('/getPbsInputFiles',getPbsInputFiles);
router.use('/getQuorumInputFiles',getQuorumInputFiles);
router.use("/upload-dealerTrack-discount-file", uploadDealerTrackCouponAndDiscountCSVFile);
router.use('/getMockServerInputFiles',getMockServerInputFiles);
router.use("/dealerTrackImport", importDealerTrack);
router.use("/reynoldsImport", importRCI);
router.use("/dealerBuiltImport", importDealerBuilt);
router.use("/automateImport", importAutomate);
router.use("/tekionImport", importTekion);
router.use("/pbsImport", importPBS);
router.use("/ucsImport", importUCS);
router.use("/mpkImport", importMPK);
router.use("/importTekionAPI", importTekionAPI);
router.use("/autoSoftImport", importAutosoft);
router.use("/importMPK", importMPK);
router.use("/importDominionVue", importDominionVue);
router.use("/importADAM", importADAM);
router.use("/importQuorum", importQuorum);
router.use("/importReynolds", importReynolds);
router.use("/schedulerImportDetails", schedulerImportDetails);
router.use("/getExtractJob",getExtractJob);
router.use("/stageUpdatePortal", stageUpdatePortal);
router.use('/removeQueueItem',removeQueueItem);
//router.use('/removeProcessingItem',removeProcessingItem);
router.use("/schedulerAddFields", schedulerAddFields);
router.use("/getQueueData", getQueueData);
router.use("/updatePriority", updatePriority);
router.use('/reQueue',reQueue);
router.use('/getUserName',getUserName);
router.use('/updateUserName',updateUserName);
router.use('/updateMake',updateMake);
router.use('/updateManufacturer',updateManufacturer);
router.use('/getMakeList',getMakeList);


router.use('/fetchProcessStatus',fetchJobFromProcessorQueue);
// router.use("/dashboard", agendashboard);
router.use("/dashboard",(req,res)=>{
    res.status(404).json({ message: "Page not found" });
})

router.use("/resumeProcessor", function (req, res) {
 let payLoad = req.body;
 let validationFlag = false;
 let validationMessage;

 console.log("++++++++++++++++++++++++++++++++++++++++++");
 console.log("payLoad:", payLoad);
 console.log("++++++++++++++++++++++++++++++++++++++++++");
 segment.saveSegment(`Resume Processor ${JSON.stringify(payLoad)}`);
//  segment.saveSegment(`Resume Processor unique Id::${payLoad.processorUniqueId.split('-').slice(0, 5).join('-')}`);
 if (!payLoad.hasOwnProperty("extractFile")) {
  validationFlag = true;
  validationMessage += "Extraction file key not found ";
 } else {
  if (!payLoad.extractFile) {
   validationFlag = true;
   validationMessage += "Extraction file key is empty";
  }
 }

 if (!payLoad.hasOwnProperty("dms")) {
  validationFlag = true;
  validationMessage += "dms key not found ";
 } else {
  if (!payLoad.dms) {
   validationFlag = true;
   validationMessage += "dms is empty";
  }
 }

 if (validationFlag) {
  res.status(401).send({ status: false, message: validationMessage });
 } else {
  haltAndResumeController.haltAndResume(payLoad, (resObj) => {
   res.status(201).send(resObj);
  });
 }
});

router.use(
 "/upload-dealerTrack-chart-of-accounts-file",
 uploadDealerTrackChartOfAccountsFile
);

router.use("/api", function (req, res, next) {
 try {
  if (req.body && req.body.variables) {
   const xss = require("xss");
   let html = xss(JSON.stringify(req.body.variables));
   // console.log(html);
   req.body.variables = JSON.parse(html);
   // console.log(req.body.variables);

   let list = [];
   html = xss(req.body.variables, {
    onTagAttr: function (tag, name, value, isWhiteAttr) {
     if (tag === "img" && name === "src") {
      // Use the built-in friendlyAttrValue function to escape attribute
      // values. It supports converting entity tags such as &lt; to printable
      // characters such as <
      list.push(xss.friendlyAttrValue(value));
     }
     // Return nothing, means keep the default handling measure
    },
   });
   // console.log("image list:\n%s", list.join(", "));
  }
  next();
 } catch (err) {
  console.log(JSON.stringify(err));
 }
});

router.use("/reRunDealerTrackProcessor", function (req, res) {
 let payLoad = req.body;
 let validationFlag = false;
 let validationMessage;

 console.log("++++++++++++++++++++++++++++++++++++++++++");
 console.log("reRunDealerTrackProcessor");
 console.log("payLoad:", payLoad);
 console.log("++++++++++++++++++++++++++++++++++++++++++");

 if (!payLoad.hasOwnProperty("extractFile")) {
  validationFlag = true;
  validationMessage += "Extraction file key not found ";
 } else {
  if (!payLoad.extractFile) {
   validationFlag = true;
   validationMessage += "Extraction file key is empty";
  }
 }

 if (!payLoad.hasOwnProperty("dms")) {
  validationFlag = true;
  validationMessage += "dms key not found ";
 } else {
  if (!payLoad.dms) {
   validationFlag = true;
   validationMessage += "dms is empty";
  }
 }

 if (validationFlag) {
  res.status(401).send({ status: false, message: validationMessage });
 } else {
  reRunDealerTrackProcessorController.reRunProcessor(payLoad, (resObj) => {
   res.status(201).send(resObj);
  });
 }
});
router.use("/changePriority", async function (req, res) {
    try {
        let payLoad = req.body;
        let validationFlag = false;
        let validationMessage = "";
        let dmsType = payLoad.dms;
        console.log('payLoad:', payLoad);
        let fileNameForPriorityChange = payLoad.fileNameForPriorityChange;
        let priority = parseInt(payLoad.priority, 10);
  
        if (!payLoad.hasOwnProperty('fileNameForPriorityChange')) {
            validationFlag = true;
            validationMessage += 'fileNameForPriorityChange not found ';
        }
  
        if (!payLoad.hasOwnProperty('dms')) {
            validationFlag = true;
            validationMessage += 'dms key not found ';
        }
  
        if (validationFlag) {
            console.log('Validation Error');
            res.status(401).send({ status: false, message: validationMessage });
        } else {
            let resObj;
            switch (dmsType.toLowerCase()) {
                case 'cdk3pa':
                    resObj = await agendaDbModel.updateProcessorJobPriority(fileNameForPriorityChange, priority, "cdk3pa_queue");
                    break;
                case 'dealerbuilt':
                    resObj = await agendaDbModel.updateProcessorJobPriority(fileNameForPriorityChange, priority, "dealerbuilt_queue");
                    break;
                case 'adam':
                    resObj = await agendaDbModel.updateProcessorJobPriority(fileNameForPriorityChange, priority, "adam_queue");
                    break;
                case 'reynolds':
                    resObj = await agendaDbModel.updateProcessorJobPriority(fileNameForPriorityChange, priority, "reynolds_queue");
                    break;
                case 'autosoft':
                    resObj = await agendaDbModel.updateProcessorJobPriority(fileNameForPriorityChange, priority, "autosoft_queue");
                    break;
                case 'dominion':
                    resObj = await agendaDbModel.updateProcessorJobPriority(fileNameForPriorityChange, priority, "dominion_queue");
                    break;
                case 'automate':
                    resObj = await agendaDbModel.updateProcessorJobPriority(fileNameForPriorityChange, priority, "automate_queue");
                    break;
                case 'dealertrack':
                    resObj = await agendaDbModel.updateProcessorJobPriority(fileNameForPriorityChange, priority, "dealertrack_queue");
                    break;
                case 'fortellis':
                    resObj = await agendaDbModel.updateProcessorJobPriority(fileNameForPriorityChange, priority, "fortellis_queue");
                    break;
                case 'tekionapi':
                    resObj = await agendaDbModel.updateProcessorJobPriority(fileNameForPriorityChange, priority, "tekionapi_queue");
                    break;
                default:
                    res.status(400).send({ status: false, message: "Invalid dmsType" });
                    return;
            }
            console.log('Change priority status', resObj);
            res.status(201).send(resObj);
        }
    } catch (error) {
        console.error("An error occurred:", error);
        res.status(500).send({ status: false, message: "Internal Server Error" });
    }
  });
router.use("/api", api);

module.exports = router;
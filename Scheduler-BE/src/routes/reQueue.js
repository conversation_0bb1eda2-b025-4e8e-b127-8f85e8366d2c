const routes = require("express").Router();
const fs = require("fs");
const path = require("path");
// routes.use(bodyParser.urlencoded({ extended: false }));
routes.post("/", async function (req, res) {
     let payLoad = req.body;
      if(payLoad){
        
        if(payLoad.dms && payLoad.filePath){
          let pathObj = generateFilePath(payLoad.dms, payLoad.filePath);
          console.log("path^^^^^^^^^^^^^^^^^^^^^^^^^^", pathObj);
      
          // Ensure paths are properly sanitized
          let source = pathObj.source.trim(); // Trim and update source
          console.log("Trimmed Source Path:", source);
          console.log("path.basename(source):", path.basename(source));

              
          const originalFilename = path.basename(source);

          const newTimestamp = new Date().toISOString().replace(/[-T:.Z]/g, '').slice(0, 14);
          
          // Remove the file extension
          const fileExtension = path.extname(originalFilename);
          const baseFilename = path.basename(originalFilename, fileExtension);
          
          // Attach the new timestamp directly to the base filename
          const newFilename = `${baseFilename}${newTimestamp}${fileExtension}`;
          
          console.log('Original Filename:', originalFilename);
          console.log('New Filename:', newFilename);
          



      
          let destination = pathObj.destination.trim(); // Trim destination as well
          const destinationFilePath = path.join(destination, newFilename);
      
          // Attempt file copy
          fs.copyFile(source, destinationFilePath, (err) => {
              if (err) {
                  console.error("Error during file copy:", err);
                  res.status(201).send({ status: false, data: 'Something Went Wrong' });
              } else {
                  console.log('File copied successfully!');
                  res.status(200).send({ status: true, data: 'Requeue Successful' });
              }
          });
        }else{
          res.status(201).send({ status: false, data: 'Something Went Wrong'  });
        }
      }
  
});

function generateFilePath(dms,fileName){
   let filePath = {};
    if(dms.toLowerCase() == 'cdk3pa'){
       filePath.source = `/home/<USER>/tmp/du-etl-dms-${dms}-extractor-work/requeue/${fileName}`;
       filePath.destination = `/home/<USER>/tmp/du-etl-dms-${dms}-extractor-work/scheduler-temp/cdk-zip-eti/`;
    }else{
      filePath.source = `/home/<USER>/tmp/du-etl-dms-${dms}-extractor-work/requeue/${fileName}`;
      filePath.destination =`/home/<USER>/tmp/du-etl-dms-${dms}-extractor-work/scheduler-temp/${dms}-zip-eti/`
    }
    return filePath;
}


module.exports = routes;

const routes = require("express").Router();
const fs = require("fs");
const express = require("express");
const bodyParser = require("body-parser");
const { arch } = require("os");
const agendaDb = require('../model/agendaDb');
// routes.use(bodyParser.urlencoded({ extended: false }));
routes.use(express.json());
routes.post("/", async function (req, res) {

  console.log('INSIDE GET EXTRACT JOBS');
  let payLoad;
  try {
    if (req.body) {
      payLoad = req.body;
      console.log("EXTRACT JOBS PAYLOAD",);
      
      let extractJobdata = await agendaDb.fetchExtractJobData(payLoad.id);
      console.log("extractJobdata@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",extractJobdata);
      if (
        extractJobdata.status &&
        extractJobdata.response.data &&
        extractJobdata.response.data.storeDataArray
      ) {
        const storeDataArray = extractJobdata.response.data.storeDataArray;
        const mageStoreCodeToMatch = payLoad.mageStoreCode; 
        const matchedStore = storeDataArray.find(
          (store) => store.mageStoreCode === mageStoreCodeToMatch
        );
      
        if (matchedStore) {
          const { companyObj, testData, parentName, mageGroupCode, mageStoreCode } =
            matchedStore;
      
          console.log("MongoDB result:", extractJobdata);
          res.status(200).send({
            status: true,
            data: companyObj,
            testData,
            parentName,
            mageGroupCode,
            mageStoreCode,
          });
        } else {
          res.status(404).send({ status: false, data: "No matching mageStoreCode found" });
        }
      } else {
        res.status(400).send({ status: false, data: "Empty request body" });
      }
    } else {     
      res.status(201).send({ status: false, data: "Empty request body" });
    }
  } catch (error) {
    res.status(201).send({ status: false, data: JSON.stringify(error) });
  }
});
module.exports = routes;

const express = require("express");
const routes = express.Router();
const appConstants = require("../common/constants");
const { postgraphile } = require("postgraphile");

const SCHEMA_NAMES = ["invoice_master"];
const AUDIT_DATABASE_CONNECTION_URI = appConstants.conf.AUDIT_DATABASE_CONNECTION_URI;

routes.use(
  "/",
  (req, res, next) => {
    try {
      postgraphile(
        AUDIT_DATABASE_CONNECTION_URI,
        SCHEMA_NAMES,
        {
          pgSettings: {
            statement_timeout: "1200000",
          },
          graphqlRoute: "/",
          graphiql: true,
          graphiqlRoute: "/auditgraphiql",
          watchPg: true,
          enhanceGraphiql: true,
        }
      )(req, res, next);
    } catch (error) {
      console.error("Postgraphile Middleware Error:", error);
      res.status(500).json({ error: "Internal Server Error" });
    }
  }
);

routes.use((err, req, res, next) => {
  console.error("Unhandled Error:", err);
  res.status(500).json({ error: "Something went wrong!" });
});

module.exports = routes;

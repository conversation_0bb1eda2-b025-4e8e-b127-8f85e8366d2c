const fs = require('fs');
const path = require('path');

/**
 * Generates a configuration file and returns its content.
 * @param {Object} inputData - The input data for the config file.
 * @returns {Promise<string>} - A promise that resolves to the file content.
 */
function generateConfigFile(inputData) {
    return new Promise((resolve, reject) => {
        const {
            dms,
            schedulerId,
            sourceCompanyId,
            mageGrpData: {
                state: state,
                mageGroupCode,
                mageGroupName,
                mageStoreName,
		 mageStoreCode,
                mageProjectId,
                mageSecondaryId,
                mageManufacturer,
                mageProjectName,
                mageProjectType,
                secondaryProjectType,
                secondaryProjectName,
		 companyId,
            }
        } = inputData;
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
	if (dms == "Dominion / VUE") {
	  dms = "DominionVue";
	} else if (dms == "Dominion / ACS") {
	  dms = "DominionVue";
	} else if (dms == "Auto/Mate") {
	  dms = "AutoMate";
	}

        const configContent = `
# Bash Source Test Config File
export DMS_BASE='${dms}'
source "$DU_ETL_HOME/DU-DMS/DMS-${dms}/${dms}.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="\${DU_ETL_ETI_DIR_${dms}}"
PROMPT_FOR_FILE='true'

#ETL  DMS: ${dms}
#Base DMS: ${dms} (optional)

DMS="${dms}"
DATE_PART='${year}${month}'

GROUP_CODE="${mageGroupCode}"
GROUPNAME="${mageGroupName}"
STORENAME="${mageStoreName}"
STORE_PART="${mageStoreCode}"
MFG="${mageManufacturer}"
STATE='${state}'

if [[ "\$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="\$GROUP_CODE"
    SERVICE_NAME=\${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=\${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="\${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=\${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=\${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="\${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[\$GROUP_CODE]\${STORE_PART}+\${DATE_PART}_\${MFG}"

COMPANY_ID="${companyId}"
SOURCE_COMPANY_ID="${sourceCompanyId}"
PROJECT_ID="${mageProjectId}"
PROJECT_TYPE="${mageProjectType}"
PROJECT_NAME="${mageProjectName}"
SECONDARY_PROJECT_ID="${mageSecondaryId}"
SECONDARY_PROJECT_TYPE="${secondaryProjectType}"
SECONDARY_PROJECT_NAME="${secondaryProjectName}"
IMPORTED_BY=''
SCHEDULER_ID=${schedulerId}`;
        resolve(configContent);
    });
}

module.exports = { generateConfigFile };

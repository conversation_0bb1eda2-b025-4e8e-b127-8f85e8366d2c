var constant = require("../common/constants");
const rp = require("request-promise");
var SOLVE360_API_URI = constant.SOLVE360_API_URI;
var SOLVE360_USER_NAME = constant.SOLVE360_USER_NAME;
var SOLVE360_TOKEN = constant.SOLVE360_TOKEN;
var SOLVE360_API_URI_NEW = constant.SOLVE360NEWURI;
const segment = require("../controllers/SEGMENT/segmentManager");

const request = require('request');
function getCredentials() {
  return (
    "Basic " +
    Buffer.from(SOLVE360_USER_NAME + ":" + SOLVE360_TOKEN).toString("base64")
  );
}

function getURI(type, id) {
  var uri = "";
  if (id) {
    uri = SOLVE360_API_URI + "/" + type + "/" + id;
  } else {
    uri = SOLVE360_API_URI + "/" + type;
  }
  return uri;
}

function getOption(type, id, obj) {
  var requestMethod = "PUT";
  var body = JSON.stringify(obj);
  var options = {
    method: requestMethod,
    uri: getURI(type, id),
    body: body,
    headers: {
      "Content-Type": "application/json",
      Authorization: getCredentials(),
      ACCEPT: "application/json",
    },
  };
  return options;
}

function updateDate(projectblog, userName, solve360Update, isRerun) {
  segment.saveSegment(`Inside updateDate in solve360`);
  console.log('projectblog:',projectblog);//ProjectID
  segment.saveSegment(`ProjectID ; ${projectblog}`);
  console.log('userName:',userName);
  segment.saveSegment(`userName ; ${userName}`);
  console.log('solve360Update:',solve360Update);
  segment.saveSegment(`solve360Update flag ; ${solve360Update}`);
  console.log('typeof:',typeof solve360Update);
  console.log('isRerun:',isRerun);
  segment.saveSegment(`isRerun flag ; ${isRerun}`);
  try {
    let solve360Name = constant.DU_USERS[userName]
      ? constant.DU_USERS[userName]
      : "";
    var todayDate = new Date().toISOString().slice(0, 10);
    if (isRerun == "RERUN") {
      var transformedObj = {
        custom9163777: todayDate
      };
    } else {
      if (solve360Update) {
        if (solve360Name != "") {
          var transformedObj = {
            custom9163777: todayDate, // data pulled
            custom21375100: todayDate,// parts ro pulled on
            custom21374629: todayDate,// labor ro pulled on
            custom21374166 : todayDate, // labor ro imported on
            custom21374164 : todayDate,// parts ro imported on 
            custom21374628 : solve360Name, //Labor ROs Pull Assigned To
            custom21375099 : solve360Name,//Parts ROs Pull Assigned To
            custom21374165 : solve360Name,//Labor ROs Import Assigned To
            custom21374631 : solve360Name,//Parts ROs Import Assigned To
            custom21374630 : todayDate,//Labor ROs Pull Requested On
            custom21375098 : todayDate,//Parts ROs Pull Requested On
            custom19370039: constant.PULLED_VIA,
            custom18478132: solve360Name,
          };
        } else {
          var transformedObj = {
            custom9163777: todayDate,
            custom21375100: todayDate,
            custom21374629: todayDate,
            custom21374166 : todayDate,
            custom21374164 : todayDate,
            custom21374630 : todayDate,
            custom21375098 : todayDate,
            custom19370039: constant.PULLED_VIA,
          };
        }
      } else {
        if (solve360Name != "") {
          var transformedObj = {
            custom9163777: todayDate,
            custom19370039: constant.PULLED_VIA,
            custom18478132: solve360Name,
          };
        } else {
          var transformedObj = {
            custom9163777: todayDate,
            custom19370039: constant.PULLED_VIA,
          };
        }
      }
    }
    segment.saveSegment(`transformedObj of project ID ${projectblog} : ${JSON.stringify(transformedObj)}`);
    // return rp(getOption("companies", projectblog, transformedObj)); 
     //Solve 360 API General functions : START
     let optionsSolveCall = getOption("projectblogs", projectblog, transformedObj);
     segment.saveSegment(`optionsSolveCall ; ${optionsSolveCall}`);
     console.log("optionsSolveCall----",optionsSolveCall);
     let bodySolveUpdateAPI = {
         "solve360ApiType": "projectblogs",   
         "currentFunctionName":"scheduler solve update",
         "actionBy": userName,
         "id":projectblog,
         "application":"SCHEDULER",
         "authToken": "",
         "options": optionsSolveCall
     };
     console.log("bodySolveUpdateAPI----",bodySolveUpdateAPI);
     // URL to send the request to generic function for solve update
     const solve360InternalURL = SOLVE360_API_URI_NEW;
     const options = {
       method: 'POST',
       uri: solve360InternalURL,
       body: bodySolveUpdateAPI,
       json: true 
   };
 
 
   console.log("optionsSolveCall  options----",options);
   segment.saveSegment(`optionsSolveCall ; ${options}`);
     // Sending a POST request
     rp(options)
         .then(response => {
             console.log('Response:', response);
             segment.saveSegment(`optionsSolveCall ; ${response}`);
             return response
         })
         .catch(error => {
             console.error('Error:', error);
             segment.saveSegment(`optionsSolveCall error ; ${error}`);
         });
 
   //Solve 360 API General functions : END
  } catch (err) {
    console.log(err);
  }
}

 /** Send infor to portal BE  Complete*/
function doPayloadActionComplete(projectblog, userName, updateRetreiveROinSolve,exceptions,inSchedulerId,importHaltStatus = null,isException,exceptionTypeCounts){
  segment.saveSegment(`Inside doPayloadActionComplete in solve360`);
  console.log('projectblog:',projectblog);//ProjectID
  segment.saveSegment(`ProjectID ; ${projectblog}`);
  console.log('userName:',userName);
  segment.saveSegment(`userName ; ${userName}`);
  console.log('doPayloadActionComplete(exceptions):',exceptions);
  segment.saveSegment(`doPayloadActionComplete(exceptions) ; ${exceptions}`);
  console.log('doPayloadActionComplete(inSchedulerId):',inSchedulerId);
  segment.saveSegment(`doPayloadActionComplete(inSchedulerId) ; ${inSchedulerId}`);
  segment.saveSegment(`importHaltStatus(inSchedulerId) ; ${importHaltStatus}`);
   segment.saveSegment(`exceptionTypeCounts(inSchedulerId) ; ${exceptionTypeCounts}`);
  let  todayDate = new Date().toISOString().slice(0, 10);
  try{
    const attPayload = {};
    attPayload.in_data_pulled_via = constant.PULLED_VIA;
    attPayload.in_retrive_ro_request_on = todayDate;
    attPayload.in_is_update_retrieve_ro = updateRetreiveROinSolve;
    attPayload.exceptions = exceptions;

    let inpObj={};
    if(isException){
          inpObj = {
      inProjectId : projectblog,
      inSource : importHaltStatus?'SchedulerImport':constant.PULLED_VIA,
      inAction : "exceptiontag",
      inData  : JSON.stringify(attPayload),
      inCreatedBy : userName, 
      inIsException:true,
      inNote:exceptionTypeCounts,
      inUpdatedBy:userName
    };
    }else{
       inpObj = {
      inProjectId : projectblog,
      inSource : importHaltStatus?'SchedulerImport':constant.PULLED_VIA,
      inAction : importHaltStatus?'halt':constant.PAYLOAD_IN_ACTION.COMPLETE,
      inAssignee  : userName,
      inPerformedOn : todayDate,
      inPerformedBy : userName,
      inData  : JSON.stringify(attPayload),
      inCreatedBy : userName,
      inSchedulerId: inSchedulerId,   
    };
    }
     
    segment.saveSegment(`doPayloadAction complete  call - : ${JSON.stringify(inpObj)}`);
    doPayloadAction(inpObj);
  } catch(err){
    segment.saveSegment(`doPayloadActionComplete Error - ${JSON.stringify(err)}`);
    console.log(err);
  }
}

function doPayloadAction(inpObj) {
  segment.saveSegment(`Inside doPayloadAction - : ${JSON.stringify(inpObj)}`);
  try {
    const encKey = Buffer.from(constant.conf.PAYLOAD_ENC_KEY).toString('base64');
    var body ={};
   
    if (inpObj.hasOwnProperty("inIsException")) {
             
      body={
        "inProjectId" : inpObj.inProjectId,
        "inSource" : inpObj.inSource,
        "inAction" : inpObj.inAction,
        "inCreatedBy"  : inpObj.inCreatedBy,
        // "inPerformedOn" : inpObj.inPerformedOn,
        // "inPerformedBy" : inpObj.inPerformedBy,
        "inData"  : inpObj.inData,
        "inCreatedBy" : inpObj.inUpdatedBy,
        "inNote": JSON.stringify(inpObj.inNote),
        "inIsException":inpObj.inIsException,
        "inIsSource":inpObj.inSource,
        "inProcessKey":"exceptionManagement"

      }

     }else{
           if(inpObj.inAction=="complete"){
      body={
        "inProjectId" : inpObj.inProjectId,
        "inSource" : inpObj.inSource,
        "inAction" : inpObj.inAction,
        "inAssignee"  : inpObj.inAssignee,
        "inPerformedOn" : inpObj.inPerformedOn,
        "inPerformedBy" : inpObj.inPerformedBy,
        "inData"  : inpObj.inData,
        "inCreatedBy" : inpObj.inCreatedBy,
        "inSchedulerId": inpObj.inSchedulerId,
        "inProcessKey": 'updateSchedulerData'
      }

    }else{

      body={
        "inProjectId" : inpObj.inProjectId,
        "inSource" : inpObj.inSource,
        "inAction" : inpObj.inAction,
        "inAssignee"  : inpObj.inAssignee,
        "inPerformedOn" : inpObj.inPerformedOn,
        "inPerformedBy" : inpObj.inPerformedBy,
        "inData"  : inpObj.inData,
        "inCreatedBy" : inpObj.inCreatedBy,
      }

    } 
     }

  
    const options = {
    headers: {
        'authKey': encKey
    },
    url: constant.conf.PAYLOAD_URI,
    json: true,
    body:body
    };
    console.log(options, "=================== ::::::::: =========");
    request.post(options, (err, res, body) => {
        if (err) {
            console.log(err);
            segment.saveSegment(`doPayloadAction - Error: ${JSON.stringify(err)}`);
        }
        try{
          segment.saveSegment(`doPayloadAction - Status: ${res.statusCode}`);
          console.log(`Status: ${res.statusCode}`);
          segment.saveSegment(`doPayloadAction - body: ${JSON.stringify(body)}`);
          console.log(body, "OP BODY FROM SERVER");
        } catch(err){
          segment.saveSegment(`doPayloadAction Error - ${JSON.stringify(err)}`);
        }
      
    });
  } catch(error) {
    console.log(`Error: ${error}`);
    segment.saveSegment(`doPayloadAction Error - ${JSON.stringify(error)}`);
  }

}

module.exports = { 
  updateDate: updateDate,
  doPayloadAction: doPayloadAction,
  doPayloadActionComplete: doPayloadActionComplete 
 };

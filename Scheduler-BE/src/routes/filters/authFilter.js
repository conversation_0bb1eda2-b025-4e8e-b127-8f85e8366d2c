"use strict";

/**
 * Function to build the group info query string from group ids
 * @param {array[string]} groupIds - User's group ids
 */
function getGroupInfoQueryString(groupIds) {
  return (
    "query{getUserMenuInfo(inUserGroupIds:" + JSON.stringify(groupIds) + ")}"
  );
}

/**
 * Function to build the group info query string from group ids
 * @param {array[string]} groupIds - User's group ids
 */
function getIsValidQueryString(groupIds, GraphQLOperation) {
  return (
    "query{isValidOperation(inUserGroupIds:" +
    JSON.stringify(groupIds) +
    ",inOperationName:" +
    JSON.stringify(GraphQLOperation) +
    ")}"
  );
}

/**
 * Function that return URL options
 * @param {object} req - Http Request object
 * @param {string} forwardURI - URI string
 */
function getOptions(req, forwardURI) {
  return {
    method: "POST",
    uri: forwardURI,
    body: JSON.stringify(req.body),
    headers: {
      "Content-Type": "application/json"
    }
  };
}

module.exports = {
  getGroupInfoQueryString,
  getIsValidQueryString,
  getOptions
};

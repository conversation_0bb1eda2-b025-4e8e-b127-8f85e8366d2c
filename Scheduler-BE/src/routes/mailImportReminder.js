const fs = require('fs');
const handlebars = require('handlebars');
const appConstants = require('../common/constants');
var API_KEY = process.env.MAILGUN_API_KEY;
var DOMAIN = process.env.MAILGUN_DOMAIN;
const moment = require('moment-timezone');
const express = require('express');
const routes = express.Router();
const mailgun = require('mailgun-js')({
    apiKey: API_KEY,
    domain: DOMAIN
});
const path = require('path');
const rp = require('request-promise');

function sendAdminScheduleReport(mailBody) {
    console.log(mailBody, "mailBody===============")
    let fromAddress = appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER;
    let toAddress = appConstants.NOTIFICATION.TOADDRESS;
    let ccAddress = appConstants.NOTIFICATION.CCADDRESS;
    return new Promise((resolve, reject) => {
        fs.readFile(path.resolve(__dirname, 'emailTemplate/schedulerImportReport.html'), 'utf8', (err, templateData) => {
            if (err) {
                console.error(err);
                reject(err);
                return;
            }
            handlebars.registerHelper('eq', (a, b) => a === b);
            handlebars.registerHelper('or', (v1, v2) => v1 || v2);
            const template = handlebars.compile(templateData);
            const data = {
                from: fromAddress,
                to: toAddress,
                cc: ccAddress,
                subject: `${mailBody.mode} Import Failed for ${mailBody.storeName}`,
                html: template(mailBody)
            };
            mailgun.messages().send(data, (error, body) => {
                if (error) {
                    console.log('Error sending scheduler details:', error)
                        // logger.error('Error sending scheduler details:', error);
                    reject(error);
                } else {
                    console.log(body);
                    resolve(body);
                }
            });
        });
    });
}
async function runSchedulerReports(inputData) {
    try {

        await sendAdminScheduleReport(inputData);
        console.log('Scheduler import status sent successfully')
            //logger.info('Scheduler report sent successfully');
    } catch (error) {
        console.error('Error running scheduler reports:', error.message);
    }
}
module.exports = {
    runSchedulerReports,
    sendAdminScheduleReport
};
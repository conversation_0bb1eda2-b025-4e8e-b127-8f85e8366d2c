const rp = require('request-promise');
const routes = require('express').Router();
const appConstants = require('../common/constants');
const {logger} = require('../logger/schedulerLogger');
/**
   * Function that return URL options
   * @param {object} req - Http Request object
   * @param {string} forwardURI - URI string
   */
function getOptions(req, forwardURI) {
  return {
    method: 'POST',
    uri: forwardURI,
    body: JSON.stringify(req.body),
    headers: {
      'Content-Type': 'application/json',
    },
  };
}

routes.use('/', (req, res, next) => {
    const options = getOptions(req, appConstants.conf.GRAPHQL_s360_URI);
    rp(options)
      .then(response => {
        logger.info(`S360_GRAPHQL_REQUEST [${req.jwtToken.message.unique_name}]: API call to Solve360 GraphQL success`)
        res.send(response);
      }).catch((error) => {
        logger.error(`S360_GRAPHQL_REQUEST [${req.jwtToken.message.unique_name}]: API call to Solve360 GraphQL failed`, error)
        res.status(400).json({ errors: [error] });
      });
});
module.exports = routes;

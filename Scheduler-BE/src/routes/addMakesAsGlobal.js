const express = require("express");
const router = express.Router();
const rp = require("request-promise");
const appConstants = require("../common/constants");

const getUserNameQuery = (schedulerId, companyID) => `
  query {
    getSchedulerPreImportStatusBySchedulerIdCompanyId(
      inSchedulerId: "${schedulerId}",
      inCompanyId: "${companyID}"
    )
  }
`;

// const getMutationAdmin = ({ inManufacturer, inMakeList,userName}) => `
//   mutation {
//     updateMakeList(input: {
//       inManufacturer: "${inManufacturer}"
//       inMakeList: "${inMakeList}"
//       inUserName: "${userName}"
//     }) {
//       json
//     }
//   }
// `;


const getMutationAdmin = (UpdateMakeListInput) => `
  mutation updateMakeList($input: UpdateMakeListInput!) {
    updateMakeList(input: $input) {
      json
    }
  }
`;


const getMutationForMakeApproval = ({ inManufacturer, inMakeList,userName,inCompanyName}) => `
  mutation {
    insertMakeApproval (input: {
      inManufacturer: "${inManufacturer}"
      inMake: "${inMakeList}"
      inCreatedBy: "${userName}"
      inCompanyName:"${inCompanyName}"
    }) {
      json
    }
  }
`;

const getMutationForManufacturer = ({ currentMake, newMake,userName}) => `
  mutation {
    insertMakeRule(input: {
      inOriginalName: "${currentMake}"
      inRenamedName: "${newMake}"
      inCreatedBy: "${userName}"
    }) {
      json
    }
  }
`;

const getGraphQLOptions = (query) => ({
  method: 'POST',
  uri: appConstants.conf.GRAPHQL_SCHEDULER_URI,
  body: { query },
  headers: { 'Content-Type': 'application/json' },
  json: true,
});





const updateMake = async (req, res) => {
    const { inManufacturerList, inMakeList, userName,isAdmin,companyName } = req.body;
    console.log("isAdmin$$$$$$$$$$$$$$$$$$$$",isAdmin);
        let schedulerMutation;
        if(isAdmin){
          console.log("Admin user$$$$$$$$$$$$$$$$$$$$")
            let UpdateMakeListInput =[
              {
                inManufacturer: inManufacturerList,
                inMakeList: inMakeList,
                userName: userName,
              }
            ]
          schedulerMutation = getMutationAdmin(UpdateMakeListInput);
        }else{
          console.log("Not An Admin user$$$$$$$$$$$$$$$$$$$$")
          console.log("Not Admin user@@@@@@@")
          schedulerMutation = getMutationForMakeApproval({
            inManufacturer: inManufacturerList,
            inMakeList: inMakeList,
            userName: userName,
            inCompanyName:companyName
          });
        }
        

        
        const mutationOptions = getGraphQLOptions(schedulerMutation);
      

        console.info("Updating make..");
         try{
            let response =  await rp(mutationOptions);



            console.log("Mutaion response$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",response);
            let jsonString;
            if(isAdmin){
              jsonString  = response?.data?.updateMakeList?.json;
            }else{
              jsonString  = response?.data?.insertMakeApproval?.json;
            }
            const jsonData = JSON.parse(jsonString);
        
            const status = jsonData.status;
            const message = jsonData.message;
        
            console.log("Status:", status);
            console.log("Message:", message);
 
           if(status=="success"){
           return res.status(200).json({
               status: status,
               message: message,
               data: { use_by: userName },
             });
             
           }else{
            console.log("Mutaion response$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",response);
             return res.status(200).json({
               status: status,
               message: message,
               data: { use_by: userName },
             });
 
           }





          //   return res.status(200).json({
          //   status: true,
          //   message: "Scheduler status updated successfully",
          //   data: { use_by: userName },
          // });
  
         }catch(err){
          console.log("Mutaion response$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",err);
            return res.status(500).json({
                status: false,
                message: "Internal Server Error!",
               
              });
         }
     
}


const updateManufacturer = async (req, res) => {
    console.log("updateManufacturer%%%%%%%%%%%%%%%%%%%%%%",req.body);
    const { currentMake, newMake, userName } = req.body;
        const schedulerMutation = getMutationForManufacturer({
          currentMake: currentMake,
          newMake: newMake,
          userName: userName,
        });
     const mutationOptions = getGraphQLOptions(schedulerMutation);
        console.info("Updating Manufacturer..");
         try{
          let response =   await rp(mutationOptions);
           console.log("Mutaion response$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",response);

           const jsonString = response?.data?.insertMakeRule?.json;
           const jsonData = JSON.parse(jsonString);
       
           const status = jsonData.status;
           const message = jsonData.message;
       
           console.log("Status:", status);
           console.log("Message:", message);

          if(status=="success"){
          return res.status(200).json({
              status:status ,
              message: message,
              data: { use_by: userName },
            });
            
          }else{
            return res.status(200).json({
              status: status,
              message: message,
              data: { use_by: userName },
            });

          }

  
         }catch(err){
            return res.status(500).json({
                status: false,
                message: "Internal Server Error!",
               
              });
         }
     
}

module.exports = { updateMake,updateManufacturer };

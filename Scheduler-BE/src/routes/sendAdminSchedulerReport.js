const cron = require('node-cron');
const sendMailReport = require('./mailAdminReminder');
const moment = require('moment-timezone');
const momentDate = require('moment');

function getUSEasternTime(hour, minute) {
    return moment.tz({ hour, minute }, 'America/New_York').utc();
}
const time = getUSEasternTime(23, 59); // 11:59 PM ET
// Cron expression to run weekly on Monday at 11:59 PM ET
const cronExpression = `${time.minute()} ${time.hour()} * * 1`; // 0 represents Monday
cron.schedule(cronExpression, async() => {
    // Get the current date and subtract 7 days
    const inStartDate = momentDate().subtract(7, 'days').format('DD-MM-YYYY');

    await sendMailReport.runSchedulerReports(inStartDate);
});
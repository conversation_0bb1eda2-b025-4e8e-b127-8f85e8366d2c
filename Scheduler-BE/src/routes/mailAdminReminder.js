const fs = require('fs');
const handlebars = require('handlebars');
const appConstants = require('../common/constants');
var API_KEY = process.env.MAILGUN_API_KEY;
var DOMAIN = process.env.MAILGUN_DOMAIN;
const moment = require('moment-timezone');
const express = require('express');
const routes = express.Router();
const mailgun = require('mailgun-js')({
    apiKey: API_KEY,
    domain: DOMAIN
});
const path = require('path');
const rp = require('request-promise');
// const logger = require("../logger/schedulerLogger");
const momentDate = require('moment');

function getCurrentDate() {
    const date = moment.tz("America/New_York");
    const month = date.format('MM');
    const day = date.format('DD');
    const year = date.format('YYYY');

    return `${month}/${day}/${year}`;
}

function sendAdminScheduleReport(mailBody) {
    console.log(mailBody, "mailBody===============")
    let fromAddress = appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER;
    let toAddress = appConstants.NOTIFICATION.TOADDRESS;
    let ccAddress = appConstants.NOTIFICATION.CCADDRESS;
    return new Promise((resolve, reject) => {
        fs.readFile(path.resolve(__dirname, 'emailTemplate/schedulerAdminReport.html'), 'utf8', (err, templateData) => {
            if (err) {
                console.error(err);
                reject(err);
                return;
            }
            handlebars.registerHelper('eq', (a, b) => a === b);
            handlebars.registerHelper('or', (v1, v2) => v1 || v2);
            const template = handlebars.compile(templateData);
            // Get the current date and subtract 7 days
            const startDate = momentDate().subtract(7, 'days').format('MM-DD-YYYY');

            let date = getCurrentDate();
            mailBody.date = startDate;
            const data = {
                from: fromAddress,
                to: toAddress,
                cc: ccAddress,
                subject: `File Import Status Report ${date}`,
                html: template({ data: mailBody })
            };
            mailgun.messages().send(data, (error, body) => {
                if (error) {
                    console.log('Error sending scheduler details:', error)
                        // logger.error('Error sending scheduler details:', error);
                    reject(error);
                } else {
                    console.log(body);
                    resolve(body);
                }
            });
        });
    });
}
async function runSchedulerReports(queueId, schedulerId, inputData) {
    try {

        await sendAdminScheduleReport(inputData);
        console.log('Scheduler report sent successfully')
            //logger.info('Scheduler report sent successfully');
    } catch (error) {
        console.error('Error running scheduler reports:', error.message);
    }
}

// Example usage:
// Replace with actual values for inCompanyId and inDms
// Get the current date and subtract 7 days
module.exports = {
    runSchedulerReports,
    sendAdminScheduleReport
};

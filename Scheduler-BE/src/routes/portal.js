const express = require('express');
const { postgraphile } = require('postgraphile');
const { Pool } = require('pg');
const appConstants = require('../common/constants');

const SCHEMA_NAMES = ['app__data_master', 'core', 'duportal', 's360_core_fd', 'middletier_solve360_data_fd', 'analytics', 'email_manager', 'security', 'administration'];
const router = express.Router();

// Your PostgreSQL connection URI from constants
const dbPortal = appConstants.conf.PORTAL_DATABASE_CONNECTION_URI;

// Set up the PostgreSQL pool for connection
const pgPool = new Pool({
  connectionString: dbPortal,
});

router.use('/', (req, _res, next) => {
  req.setTimeout(1800000);
  delete req.body.operationName;
  next();
});

router.use(
  '/',
  postgraphile(pgPool, SCHEMA_NAMES, {
    // working PostGraphile options
    // graphiql: true,
    // enhanceGraphiql: true,
    // watchPg: true,
    // dynamicJson: true,

    pgSettings: {
      statement_timeout: '1200000',
    },
    externalUrlBase: '/portalCustom',
    graphqlRoute: '/',
    graphiql: false,
    graphiqlRoute: '/graphiql',
    enhanceGraphiql: true,
    watchPg: true,
    dynamicJson: true,  
   
  })
);

module.exports = router;





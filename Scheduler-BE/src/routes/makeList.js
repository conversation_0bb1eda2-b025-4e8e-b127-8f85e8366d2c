const express = require("express");
const router = express.Router();
const rp = require("request-promise");
const appConstants = require("../common/constants");

const getmakeListQuery = () => `
 query {
  getMakeList {
    edges {
      node {
        manufacturer
        make
      }
    }
  }
}

`;




const getMutation = ({ in_scheduler_id, in_company_id, in_use_by }) => `
  mutation {
    updateUseByInPreImportStatus(input: {
      inSchedulerId: "${in_scheduler_id}"
      inCompanyId: "${in_company_id}"
      inUseBy: "${in_use_by}"
    }) {
      json
    }
  }
`;


const getGraphQLOptions = (query) => ({
  method: 'POST',
  uri: appConstants.conf.GRAPHQL_SCHEDULER_URI,
  body: { query },
  headers: { 'Content-Type': 'application/json' },
  json: true,
});

const parseGraphQLResponse = (response, key) => {
  try {
    return JSON.parse(response.data[key]);
  } catch (error) {
    console.error("Error parsing GraphQL response:", error.message);
    return null;
  }
};

const getMakeList = async (req, res) => {
    try {

        const schedulerQuery = getmakeListQuery();
        const schedulerOptions = getGraphQLOptions(schedulerQuery);
        console.info("Fetching Scheduler Status...");
    
        const response = await rp(schedulerOptions);
        console.log("response Make List&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&",response);

        // const resultArray = parseGraphQLResponse(response, "getSchedulerPreImportStatusBySchedulerIdCompanyId");
    
         if(response){
            res.status(200).json({status:true,data:response});
         }
    
      
      } catch (err) {
        console.error("Error in Scheduler Import Handler:", err.message);
        return res.status(500).json({
          status: false,
          message: "Internal Server Error",
        });
      }

}


const updateMakeList = async (req, res) => {
    const { schedulerId, companyId, userName } = req.body;

        const schedulerMutation = getMutation({
          in_scheduler_id: schedulerId,
          in_company_id: companyId,
          in_use_by: userName,
        });

        console.log("updateuserName req.body$$$$$$$$$$$$$$$$$$$$",req.body);

        console.log("updateuserName Mutation$$$$$$$$$$$$$$$$$$$$",schedulerMutation);

        const mutationOptions = getGraphQLOptions(schedulerMutation);
        console.info("Updating Scheduler Status...");
         try{
            await rp(mutationOptions);

            return res.status(200).json({
            status: true,
            message: "Scheduler status updated successfully",
            data: { use_by: userName },
          });
  
         }catch(err){
            return res.status(500).json({
                status: true,
                message: "Internal Server Error!",
               
              });
         }
     
  }

module.exports = { getMakeList,updateMakeList };

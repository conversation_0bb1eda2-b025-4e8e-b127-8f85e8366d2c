const routes = require("express").Router();
const fs = require("fs");
const express = require("express");
const bodyParser = require("body-parser");
const { arch } = require("os");

// routes.use(bodyParser.urlencoded({ extended: false }));
routes.use(express.json());
routes.post("/", async function (req, res) {
 
  console.log('INSIDE REMOVE PROCESS JOB ',req.body);
  let payLoad;
  try {
    if (req.body) {
      payLoad = req.body;
      console.log("EXTRACT JOBS PAYLOAD",payLoad);
      let filePath = payLoad.filePath
      if (filePath) {     
        fs.unlink(filePath, (err) => {
            if (err) {
              console.error(`Error deleting file: ${err.message}`);
              res.status(201).send({ status: false, data: "Error deleting file" });
            } else {
              console.log(`File deleted successfully: ${filePath}`);
              res.status(200).send({status: true,message:"File reomved from queue"});  
            }
          });   
             
    
      }else{       
        res.status(201).send({ status: false, data: "Empty request body" });
      }
    } else {     
      res.status(201).send({ status: false, data: "Empty request body" });
    }
  } catch (error) {
    res.status(201).send({ status: false, data: JSON.stringify(error) });
  }
});


module.exports = routes;

const routes = require("express").Router();
const fs = require("fs");
const bodyParser = require("body-parser");
routes.use(bodyParser.urlencoded({ extended: false }));
routes.post("/", function (req, res) {
  let payLoad;
  let fileList = {
    nonArchive: [],
    archive: []
}
  try {
    if (req.body) {
      payLoad = req.body;
      console.log("payLoad=========/.",payLoad);
      if (payLoad.hasOwnProperty("folderPath")) {
        if (payLoad.folderPath) {
          // console.log(payLoad.folderPath);
          if (fs.existsSync(payLoad.folderPath)) {
            // console.log("Directory exists.");
            fs.readdirSync(payLoad.folderPath).forEach((file) => {
              console.log(file);
              if(file != 'archive'){
                fileList['nonArchive'].push({ filename: file });
              }
            });

            fs.readdirSync(payLoad.folderPath+'/archive').forEach((file) => {
              console.log(file);
              fileList['archive'].push({ filename: file });
            });

            // console.log(fileList);
            res.status(201).send({ status: true, data: fileList });
          } else {
            res
              .status(201)
              .send({ status: false, data: "folderPath not exist" });
          }
        } else {
          res.status(201).send({ status: false, data: "Empty folderPath" });
        }
      } else {
        res
          .status(201)
          .send({ status: false, data: "folderPath key not found" });
      }
    } else {
      res.status(201).send({ status: false, data: "Empty request body" });
    }
  } catch (error) {
    // console.log(JSON.stringify(error));
    res.status(201).send({ status: false, data: JSON.stringify(error) });
  }
});
module.exports = routes;

var express = require("express");
const { Pool } = require('pg');
const path = require("path");
const routes = require('express').Router();
var bodyParser = require("body-parser");
routes.use(bodyParser.json());
routes.use(bodyParser.urlencoded({ extended: true }));
const fs = require('fs');
const appConstants = require("../common/constants");
const ENV_PATH = path.join(process.env.HOME + "/.scheduler/.env");

require("dotenv").config({ path: ENV_PATH });
const { logger } = require("../logger/schedulerLogger");

routes.post('/', async (req, res) => {
    req.setTimeout(1800000);
    const { mode } = req.body;
    let COPYFILEIMPORTPATH = mode === "fromURI"
        ? "/etl/audit-import-halt"
        : appConstants.MANUALFILEIMPORTPATH;

    const readFilesFromDirectory = (directoryPath) => {
        return new Promise((resolve, reject) => {
            fs.readdir(directoryPath, (err, files) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(files);
                }
            });
        });
    };

    try {
        const files = await readFilesFromDirectory(COPYFILEIMPORTPATH);
        const fileData = files.map(fileName => ({ fileName }));

        logger.info('Reading files:', fileData);
        res.status(200).json({ status: "success", data: fileData });
    } catch (error) {
        logger.error('Error fetching files from directory:', error);
        res.status(200).send('Error fetching files from directory:', error);
    }
});

module.exports = routes;

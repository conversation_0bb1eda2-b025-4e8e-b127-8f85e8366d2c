var express = require("express");
const path = require("path");
// const { response } = require("../../app");
const routes = require('express').Router();
const fs = require('fs');
const os = require('os');
const homeDir = os.homedir();
const appConstants = require("../common/constants");

// require("dotenv").load();
const ENV_PATH = path.join(process.env.HOME + "/.scheduler/.env");
// process.env.FILEIMPORTPATH; // Temporary folder for copy ETL file(pg_dump) from dist to cdk-zip-eti
require("dotenv").config({ path: ENV_PATH });
const { promisify } = require('util');
const { exec } = require('child_process');
// Promisify necessary functions
const copyFile = promisify(fs.copyFile);
const unlink = promisify(fs.unlink);
const execPromise = promisify(exec);
// Define paths
const sourceFolderPath = appConstants.MANUALFILEIMPORTPATH; //path.join(__dirname, "/etl/audit-import-halt/config");
const dbName = "DU-ETL";
const { logger } = require("../logger/schedulerLogger");
// Define a function to establish the database connection
const doDbConnection = require('./dbConnection'); // Import the function
routes.post('/', async(req, res) => {
    try {
        if (req.body) {
            let { dms, fileName, schedulerId, companyId } = req.body;
            schedulerId = schedulerId.replace(/-/g, '');
            const tmpFolderPath = path.join(homeDir, 'scheduler-import-tmp-files', `tmp_${getTimestamp()}_${companyId}_${schedulerId}`);
            console.log("--------------tmpFolderPath", tmpFolderPath);

            function getTimestamp() {
                const date = new Date();
                return date.toISOString().replace(/[-:]/g, '').replace('T', '_').replace('Z', '');
            }
            // Establish database connection
            const schemaNames = `du_dms_${schedulerId}_${companyId}`;

            // Establish database connection
            const pool = await doDbConnection(schemaNames);
            const client = await pool.connect();
            // Construct source and destination file paths
            const sourceFilePath = path.join(sourceFolderPath, fileName);
            const tmpFilePath = path.join(tmpFolderPath, fileName);
            if (!fs.existsSync(tmpFolderPath)) {
                fs.chmodSync(homeDir, '755');
                fs.mkdirSync(tmpFolderPath, { recursive: true });
            }
            if (fs.existsSync(sourceFilePath)) {
                // Check if the file already exists in the temporary folder
                if (fs.existsSync(tmpFilePath)) {
                    logger.error(`File ${fileName} already exists in temporary folder. Skipping copy.`);
                    // Unlink (delete) the existing file
                    await unlink(tmpFilePath);
                }
                //fs.mkdirSync(tmpFolderPath);
                // Copy the file to the temporary folder
                await copyFile(sourceFilePath, tmpFilePath);
                logger.error(`File ${fileName} copied to temporary folder.`);
                // Unzip the file in the temporary folder
                const command = `unzip -o ${tmpFilePath} -d ${tmpFolderPath}`; // `-o` option to overwrite existing files
                await execPromise(command);
                logger.info(`File ${fileName} unzipped in temporary folder.`);
                const files = fs.readdirSync(tmpFolderPath);
                // Find the first file with a .pgdump extension
                const pgDumpFile = files.find(file => file.endsWith('.pgdump'));
                if (pgDumpFile) {
                    try {
                        // If a .pgdump file is found, construct the full path to the file
                        const pgDumpFilePath = path.join(tmpFolderPath, pgDumpFile);
                        logger.info(`Found PostgreSQL dump file: ${pgDumpFilePath}`);
                        const parts = fileName.split('_');
                        // Extract the alphanumeric string from the second part
                        let schedulerId = parts[parts.length - 2];
                        schedulerId = schedulerId.replace(/-/g, '');
                        // Extract the numeric string from the third part
                        const companyId = parts[parts.length - 1].split('-')[0];

                        // Construct the schema name based on the extracted alphanumeric string and numeric string
                        const schemaName = `du_dms_${schedulerId}_${companyId}`;
                        // Create the schema using the constructed schema name---need to check
                        const createSchemaQuery = `drop schema if exists ${schemaName} cascade; create schema if not exists ${schemaName} ;`;
                        // Execute the CREATE SCHEMA query
                        try {
                            await pool.query(createSchemaQuery);
                            logger.info(`Schema ${schemaName} created successfully.`);
                        } catch (error) {
                            logger.info('Error creating schema:', error);
                            // Handle the error appropriately
                        }
                        // Restore the PostgreSQL dump file
                        const restoreCommand = `PGPASSWORD=${pool.options.password} pg_restore --clean -d ${pool.options.database} -U ${pool.options.user} -h ${pool.options.host} -p ${pool.options.port} -Fc -n ${schemaName} ${pgDumpFilePath}`;
                        // console.log(`restoreCommand: ${restoreCommand}`);
                        logger.info(`restoreCommand: ${restoreCommand}`);
                        try {
                            await execPromise(restoreCommand);
                            logger.info('Database restored successfully.');
                        } catch (error) {
                            logger.error('Error restoring database:', error);
                            // Handle the error appropriately
                        }
                        // const queryUnassignMake = {
                        //     text: `update ${schemaName}.etl_head_detail
                        // set  "Make" = 'FORD'||ROUND(RANDOM()*10000),  "MakeDesc" = 'FORD'||ROUND(RANDOM()*10000)
                        // where "RONumber" ~ '00$'`
                        // };
                        // await client.query(queryUnassignMake);
                        await fs.promises.rmdir(tmpFolderPath, { recursive: true });
                        // Remove the temporary folder and its contents
                        const responsePayload = {
                            status: "success",
                            message: 'File fetched, unzipped, and PostgreSQL dump file restored successfully.',
                            data: {
                                companyId: companyId,
                                schedulerId: schedulerId
                            }
                        };

                        // Send the response with the success message and data
                        res.status(200).send(responsePayload);
                        logger.info(`PostgreSQL dump file ${pgDumpFilePath} restored successfully.`);
                    } catch (error) {
                        res.status(200).send({ 'status': 'failed', 'message': 'Error while processing this step' });
                        logger.error('Error while processing this step.');
                        console.log("*********** ERROR 1111 ***********", error);
                    }
                } else {
                    logger.error('No PostgreSQL dump file found in temporary folder.');
                    // console.error('No PostgreSQL dump file found in temporary folder.');
                    res.status(200).send({ 'status': 'failed', 'message': 'No PostgreSQL dump file found in temporary folder.' });
                    // Handle the case where no .pgdump file is found
                }
            } else {
                res.status(200).send({ 'status': 'failed', 'message': 'Source File Not Exist.' });
                logger.error('Source File Not Exist.');

            }
        } else {
            logger.info(`Error:Request body is null`);

            res.status(200).send('Error:Request body is null');
        }
    } catch (error) {
        // console.error('Error:', error);
        res.status(200).send({ 'status': 'failed', 'message': 'Error while processing this step' });
        logger.error('Error while processing this step.');
        console.log("*********** ERROR 1111 ***********", error);

    }
});


module.exports = routes;

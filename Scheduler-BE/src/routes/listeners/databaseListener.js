const { Client } = require("pg");
const appConstants = require("../../common/constants");
const { logger } = require("../../logger/schedulerLogger");

module.exports = {
  startListeners: () => {
    try {
      const listeners = appConstants.conf.DATABASE_LISTENERS;
      const listenerNames = Object.keys(listeners);
      listenerNames.forEach((listenerName) => {
        const client = new Client(listeners[listenerName]);
        startListener(listenerName, client);
      });
    } catch (error) {
      logger.error("startListeners" + error.toString());
    }
  },
};

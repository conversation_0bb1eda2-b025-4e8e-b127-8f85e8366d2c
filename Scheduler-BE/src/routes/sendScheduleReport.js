
const MongoClient = require("mongodb").MongoClient;
const ObjectId = require('mongodb').ObjectID;
const dbName = 'agenda';
const cron = require('node-cron');
const sendMailReport = require('./sendMailForReport');
const moment = require('moment-timezone');

function getUSEasternTime(hour, minute) {
    return moment.tz({ hour, minute }, 'America/New_York').utc();
}

const time = getUSEasternTime(23, 59); // 11:59 PM ET
const cronExpression = `${time.minute()} ${time.hour()} * * *`;

cron.schedule(cronExpression, async () => {
    let scheduleReport = await generateReport();
    await sendMailReport.sendScheduleReport(scheduleReport);
});

// cron.schedule('*/1 * * * *', async () => {
//     console.log('CRON JOB STARTED!!!!!!!!!!!!!!!!!!!!!!!!!');
//     let scheduleReport = await generateReport();
//     console.log("Schedule Report >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>", scheduleReport);
//     await sendMailReport.sendScheduleReport(scheduleReport);
// });



function generateReport(db) {
    const nameMap = {
        'CDK_EXTRACT': 'CDK3PA',
    };

    return new Promise((resolve, reject) => {
        MongoClient.connect('mongodb://localhost:', function(err, client) {
            if (err) {
                console.error('Error occurred while connecting to MongoDB', err);
                reject(err);
                return;
            }
            console.log('Connected successfully to MongoDB');
            const startOfDay = moment.tz("America/New_York").startOf('day').toDate();
            const endOfDay = moment.tz("America/New_York").endOf('day').toDate();
            console.log("startOfDay:::::::::::::::::::::::::::::::::::::::::",startOfDay);
            console.log("endOfDay:::::::::::::::::::::::::::::::::::::::::",endOfDay);
            const db = client.db(dbName);

            try {
                db.collection('agendaJobs').aggregate([
                     {
                        $match: {
                            lastRunAt: {
                                $gte: startOfDay,
                                $lt: endOfDay
                            },
                            name: { $in: ["CDK_EXTRACT", "DEALERTRACK", "DOMINION", "REYNOLDS", "AUTOMATE", "AUTOSOFT", "DEALERBUILT", "ADAM"] }
                        }
                    },
                    { $unwind: "$data.storeDataArray" },
                    {
                        $project: {
                            _id: 0,
                            name: 1,
                            startDate:"$data.storeDataArray.startDate",
                            endDate:"$data.storeDataArray.endDate",
                            processJobStatus: "$data.storeDataArray.processJobStatus",
                            mageStoreCode: "$data.storeDataArray.mageStoreCode",
                            message: "$data.storeDataArray.message",
                            parentName: "$data.storeDataArray.parentName",
                        }
                    }

                ]).toArray((err, result) => {
                    if (err) {
                        console.error('Error occurred while generating report', err);
                        reject(err);
                        return;
                    }

                    result.forEach((item) => {
                        if (nameMap[item.name]) {
                            item.name = nameMap[item.name];
                        }
                    });

                    console.log('Report generated successfully:', result);
                    resolve(result);
                });
            } catch (err) {
                console.log("Error In Fetching data!");
                reject(err);
            }

            client.close((err) => {
                if (err) {
                    console.error('Error occurred while closing the MongoDB connection', err);
                } else {
                    console.log('MongoDB connection closed successfully');
                }
            });
        });
    });
}





module.exports = {generateReport}

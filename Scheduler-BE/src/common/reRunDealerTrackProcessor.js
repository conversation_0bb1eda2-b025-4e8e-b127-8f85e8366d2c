const agendaDbModel = require("../model/agendaDb");
const path = require("path");
const fs = require("fs");

exports.reRunProcessor = async (inpObj, cb) => {

    try {
        const inputFileName = inpObj.extractFile;
        const dmsName = inpObj.dms;
        let chartOfAccountsFilePath;

        console.log('reRunProcessor controller')
        console.log("inputFileName:", inputFileName);
        console.log("dmsName:", dmsName);

        let reRunFilePath,
            targetFilePath,
            basename,
            uniqueIdentifier,
            mageStoreCode,
            extractJobName,
            processorJobName,
            haltOverRideUpdateStatus;

        basename = path.basename(inputFileName);
        uniqueIdentifier = basename.split("-").reverse()[1];
        mageStoreCode = basename.split("-")[1];

        if (dmsName.toLowerCase().trim() == "dealertrack") {
            reRunFilePath =
                "/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/scheduler-temp/rerun/" +
                inputFileName;

            targetFilePath =
                "/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/scheduler-temp/dealertrack-zip-eti/" +
                basename;

            extractJobName = "DEALERTRACK";
            processorJobName = "DEALERTRACK-PROCESS-JSON";

            chartOfAccountsFilePath = inpObj.chartOfAccountsFilePath;

            console.log("basename:", basename);
            console.log("reRunFilePath:", reRunFilePath);
            console.log("targetFilePath:", targetFilePath);
            console.log("uniqueIdentifier:", uniqueIdentifier);
            console.log("mageStoreCode:", mageStoreCode);
            console.log("extractJobName:", extractJobName);
            console.log("processorJobName:", processorJobName);
            console.log("chartOfAccountsFilePath:", chartOfAccountsFilePath);

            haltOverRideUpdateStatus = await agendaDbModel.updateHaltOverRideDealerTrack(
                uniqueIdentifier,
                extractJobName,
                mageStoreCode,
                chartOfAccountsFilePath
            );
        } else {
            console.log("Unknown DMS!");
            cb({
                status: false,
                response: "Unknown DMS!",
            });
        }

        console.log(
            "haltOverRideUpdateStatus.status:",
            haltOverRideUpdateStatus.status
        );

        if (haltOverRideUpdateStatus.status) {
            console.log("Success", haltOverRideUpdateStatus.status);
            console.log("Test............")
            try {
                if (fs.existsSync(reRunFilePath)) {
                    fs.copyFileSync(reRunFilePath, targetFilePath);
                    console.log("Done");
                    cb({
                        status: true,
                        response: "success",
                    });
                } else {
                    cb({
                        status: false,
                        response: `${reRunFilePath} not exist`,
                    });
                }
            } catch (err) {
                console.log(err);
                cb({
                    status: false,
                    response: err,
                });
            }
        } else {
            cb({
                status: false,
                response: "error",
            });
        }
    } catch (err) {
        console.log(err);
    }
};

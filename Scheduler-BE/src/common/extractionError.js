const express = require("express");
const app = express();
const MongoClient = require("mongodb").MongoClient;
module.exports = class Errorlog {
  constructor() {
  }
  static saveErrorLog(errObj, cb) {
    MongoClient.connect(
      "mongodb://localhost:27017/extraction_error_db",
      function (err, client) {
        if (err) {
          console.log(err);
          throw err;
        }
        const db = client.db("extraction_error_db");
        db.collection("extraction_error_log").insertOne(errObj, function (
          err,
          res
        ) {
          if (err) throw err;
          cb("record inserted");
        });
      }
    );
  }

  static displayErrorLog(cb) {
    MongoClient.connect(
      "mongodb://localhost:27017/extraction_error_db",
      function (err, client) {
        if (err) throw err;
        const db = client.db("extraction_error_db");
        db.collection("extraction_error_log")
          .find()
          .toArray(function (err, result) {
            if (err) throw err;
            cb(result);
          });
      }
    );
  }

  static displayErrorLogWithSpecific(extractionID, dmsType) {
    return new Promise((resolve, reject) => {
      console.log(extractionID);
      console.log(dmsType);
      try {
        MongoClient.connect(
          "mongodb://localhost:27017/extraction_error_db",
          function (err, client) {
            if (err) throw err;
            const db = client.db("extraction_error_db");
            db.collection("extraction_error_log")
              .find({extractionId:extractionID.toString().trim(),dms_type:dmsType})
              .toArray(function (err, result) {
                if (err) {
                  console.log(err);
                  resolve({ status: false, response: JSON.stringify(err) });
                }
                console.log(result);
                resolve({ status: true, response: result });
              });
          }
        );
      } catch (err) {
        console.log(err);
        resolve({ status: false, response: JSON.stringify(err) });
      }
    });
  }

  static displayGlMissingErrors(extractionID, dmsType) {
    return new Promise((resolve, reject) => {
      console.log(extractionID);
      console.log(dmsType);
      try {
        MongoClient.connect(
          "mongodb://localhost:27017/extraction_error_db",
          function (err, client) {
            if (err) throw err;
            const db = client.db("extraction_error_db");
            db.collection("gl_missing_error_log")
              .find({extractionId:extractionID.toString().trim(),dms_type:dmsType})
              .toArray(function (err, result) {
                if (err) {
                  console.log(err);
                  resolve({ status: false, response: JSON.stringify(err) });
                }
                console.log(result);
                resolve({ status: true, response: result });
              });
          }
        );
      } catch (err) {
        console.log(err);
        resolve({ status: false, response: JSON.stringify(err) });
      }
    });
  }

  static displayDealerTrackGeneralExtractionError(extractionID, extractionErrorType) {
    return new Promise((resolve) => {
      console.log(extractionID);
      try {
        MongoClient.connect(
          "mongodb://localhost:27017/extraction_error_db",
          function (err, client) {
            if (err) throw err;
            const db = client.db("extraction_error_db");
            db.collection("delertrack_extraction_error")
              .find({extractionId:extractionID.toString().trim(), errorType:extractionErrorType})
              .toArray(function (err, result) {
                if (err) {
                  console.log(err);
                  resolve({ status: false, response: JSON.stringify(err) });
                }
                console.log(result);
                resolve({ status: true, response: result });
              });
          }
        );
      } catch (err) {
        console.log(err);
        resolve({ status: false, response: JSON.stringify(err) });
      }
    });
  }

  static displayErrorDealerNotSubscribedCDK3PA(extractionID, dmsType) {
    return new Promise((resolve, reject) => {
      console.log(extractionID);
      console.log(dmsType);
      try {
        MongoClient.connect(
          "mongodb://localhost:27017/extraction_error_db",
          function (err, client) {
            if (err) throw err;
            const db = client.db("extraction_error_db");
            db.collection("extraction_error_subscription_log")
              .find({extractionId:extractionID.toString().trim(),dms_type:dmsType})
              .toArray(function (err, result) {
                if (err) {
                  console.log(err);
                  resolve({ status: false, response: JSON.stringify(err) });
                }
                console.log(result);
                resolve({ status: true, response: result });
              });
          }
        );
      } catch (err) {
        console.log(err);
        resolve({ status: false, response: JSON.stringify(err) });
      }
    });
  }
};



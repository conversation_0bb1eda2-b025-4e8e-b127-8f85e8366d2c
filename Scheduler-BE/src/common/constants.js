const path = require("path");
const ENV_PATH = path.join(process.env.HOME + "/.scheduler/.env");
require("dotenv").config({ path: ENV_PATH });
//require("dotenv").config();

/**
 * Read the tenant id from the .env file
 */

/**
 * Read the Solve360 configuration based on the environment varible 'NODE_ENV'.
 */
function getEnv() {
    const conf = {};
    conf.DEBUG = true;
    if (process.env.DEBUG === "false") {
        conf.DEBUG = false;
    }
    /**
     * Read the AzureAD  TenantID, ClientID and Client Secret from .env file
     */
    conf.TENANT_ID = process.env.TENANT_ID;
    conf.AUD = process.env.CLIENT_ID;
    conf.CLIENT_SECRET = process.env.CLIENT_SECRET;
    conf.SHAREPOINT_CLIENT_ID = process.env.SHAREPOINT_CLIENT_ID;
    conf.SHAREPOINT_CLIENT_SECRET = process.env.SHAREPOINT_CLIENT_SECRET;
    conf.GRAPHQL_s360_URI = process.env.GRAPHQL_s360_URI;
    conf.SPAUTH_SITEURL = process.env.SPAUTH_SITEURL;
    conf.SHAREPOINT_API_PUBLIC_SECRET_KEY =
        process.env.SHAREPOINT_API_PUBLIC_SECRET_KEY;
    conf.SHAREPOINT_API_PRIVATE_SECRET_KEY =
        process.env.SHAREPOINT_API_PRIVATE_SECRET_KEY;
    conf.PAYLOAD_URI = process.env.PAYLOAD_URI;
    conf.PAYLOAD_ENC_KEY = process.env.PAYLOAD_ENC_KEY;
    conf.SALES_TAG_URI = process.env.SALES_TAG_URI;
    conf.SOLVE360_DATABASE_CONNECTION_URI =
        process.env.SOLVE360_DATABASE_CONNECTION_URI;
    conf.SOLVE360_SCHEMA_NAME = process.env.SOLVE360_SCHEMA_NAME;
    conf.DUPORTAL_SCHEMA_NAME = process.env.DUPORTAL_SCHEMA_NAME;
    conf.SCHEDULER_DATABASE_CONNECTION_URI =
        process.env.SCHEDULER_DATABASE_CONNECTION_URI;
    conf.SCHEDULER_SCHEMA_NAME = process.env.SCHEDULER_SCHEMA_NAME;
    conf.SCHEDULER_PORTAL_SCHEMA_NAME = process.env.SCHEDULER_PORTAL_SCHEMA_NAME;
    conf.SCHEDULER_CORE_SCHEMA_NAME = process.env.SCHEDULER_CORE_SCHEMA_NAME;
    conf.SCHEDULER_GRAPHQL_URI = process.env.SCHEDULER_GRAPHQL_URI;
    conf.GRAPHQL_SCHEDULER_URI = process.env.GRAPHQL_SCHEDULER_URI;
    conf.UPDATE_STORE_DETAILS_URL = process.env.UPDATE_STORE_DETAILS_URL;
    conf.UPDATE_STORE_AUTH_KEY= process.env.UPDATE_STORE_AUTH_KEY;
    conf.IN_ACTION_BY = process.env.IN_ACTION_BY;
    conf.IN_AUTH_TOKEN = process.env.IN_AUTH_TOKEN;
    conf.SOLVE_API_URL = process.env.SOLVE_API_URL;
    conf.AUTH_HEADER = process.env.AUTH_HEADER;  
    conf.IN_SCHEDULER_TOKEN = process.env.IN_SCHEDULER_TOKEN;
    conf.SCHEDULER_UPDATES_URL = process.env.SCHEDULER_UPDATES_URL;
    conf.GRAPHQL_DUPORTAL_URI = process.env.GRAPHQL_DUPORTAL_URI;
    conf.PORTAL_DATABASE_CONNECTION_URI= process.env.PORTAL_DATABASE_CONNECTION_URI;
    conf.AUDIT_DATABASE_CONNECTION_URI= process.env.AUDIT_DATABASE_CONNECTION_URI;
    conf.GRAPHQL_AUDIT_URI = process.env.GRAPHQL_AUDIT_URI;
    return conf;
}

const constants = {
    conf: getEnv(),
    JWK_URI: "https://login.microsoftonline.com/common/discovery/v2.0/keys",
    ISS: 'https://login.microsoftonline.com/' + process.env.TENANT_ID + '/v2.0',
    OTB_URL: "https://login.microsoftonline.com/" +
        process.env.TENANT_ID +
        "/oauth2/v2.0/token",
    OTB_URL2: "https://accounts.accesscontrol.windows.net/" +
        process.env.TENANT_ID +
        "/tokens/OAuth/2",
    OTB_RESOURCE_URI: "https://graph.windows.net",
    OTB_RESOURCE_URI2: "https://dealeruplift.sharepoint.com",
    OTB_SCOPE: "openid",
    OTB_GRANT_TYPE: "urn:ietf:params:oauth:grant-type:jwt-bearer",
    OTB_CLIENT_ASSERTION_TYPE: "urn:ietf:params:oauth:client-assertion-type:jwt-bearer",
    OTB_REQ_TOKEN_USE: "on_behalf_of",
    GRAPH_URL: 'https://graph.microsoft.com/v1.0/me/memberOf',
    MANUALFILEIMPORTPATH : "/etl/scheduler-manual-import",
    SHAREPOINT_RESOURCE_URI: "dealeruplift.sharepoint.com",
    SHARE_POINT_API_URL: process.env.SHARE_POINT_FILE_UPLOAD_API_URL,
    GRAPH_API_END: "/getMemberGroups?api-version=1.6",
    SOLVE360_API_URI: "https://secure.solve360.com",
    SOLVE360_USER_NAME:process.env.SOLVE360_USER_NAME,
    SOLVE360_TOKEN:process.env.SOLVE360_TOKEN,
    SOLVE360NEWURI:process.env.SOLVE360_NEW_URI,
    PULLED_VIA: "Scheduler",
    SALES_TAG_COMMENT: "Need DMS Access Granted",
    PAYLOAD_IN_ACTION: {
        ASSIGN: "assign",
        COMPLETE: "complete",
        CANCEL: "cancel",
        FAIL: "fail",
        HALT: "halt",
        RESUME: "resume",
    },
    DU_USERS: {
        "<EMAIL>": "Jordan Jankowski",
        "<EMAIL>": "Peggy Gilbert",
        "<EMAIL>": "Ryan Hammer",
        "<EMAIL>": "David Johnston",
    },
    HAS_GROUPS: "hasGroups",
    HAS_NO_GROUPS: "hasNoGroups",
    HAS_NO_GROUPS_MESSAGE: {
        status: "[ERR: 5001]",
        message: "Access denied! [User have no group membership]",
    },
    HAS_NO_VALID_GROUP_MESSAGE: {
        status: "[ERR: 5001]",
        message: "No access privilege; Valid group does not exist",
    },
    TOKEN_ERROR: "InvalidToken",
    JWT_TOKEN_ERROR: {
        status: "[ERR: 5001]",
        message: "Invalid JWT token",
    },
    SCHEDULER_TOKEN_ERROR: {
        status: "[ERR: 5002]",
        message: "Invalid or missing Base64 scheduler token",
    },
    ACCESS_TOKEN_ERROR: "AccessTokenRequestError",
    ACCESS_TOKEN_ERROR_MESSAGE: {
        status: "[ERR: 5001]",
        message: "Access denied, Please login again and try",
    },
    NOTIFICATION: {
        FROMADDRESS: process.env.SERVER === "production" ?
            "ETL Notifier <<EMAIL>>" : "ETL Notifier <<EMAIL>>",
        FROMADDRESS_SCHEDULER_MESSENGER: process.env.SERVER === "production" ?
            "Scheduler Messenger <<EMAIL>>" : "Scheduler Messenger <<EMAIL>>",
        TOADDRESS: process.env.SERVER === "production" ? [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ] : [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"

        ],
        CCADDRESS: process.env.SERVER === "production" ? [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ] : [""],
    },
    NOTIFICATION_TO_JIRA: {
        TOADDRESS: [
         "<EMAIL>"         
        ],
        CCADDRESS: [
         "<EMAIL>",
         "<EMAIL>",       
        ],
    },
    NOTIFICATION_SANDBOX: {
        TOADDRESS: [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ],
        CCADDRESS: ["<EMAIL>",  "<EMAIL>",
            "<EMAIL>"],
    },
    NOTIFICATION_TYPES: {
        SHARE_POINT: "SHAREPOINT_UPLOAD",
        PROCESS_XML: "PROCESS_XML",
        CDK_EXTRACT: "CDK_EXTRACT",
    },
    REPLACE_STRING: {
        FROM: ".zip",
        TO: "-ETL.zip",
    },
    CREATE_JIRA_TICKET_ON_UPLOAD_FAIL:process.env.CREATE_JIRA_TICKET_ON_UPLOAD_FAIL
};
module.exports = constants;

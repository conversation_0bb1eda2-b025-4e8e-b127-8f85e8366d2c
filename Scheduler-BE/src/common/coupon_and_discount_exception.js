const Pool = require("pg").Pool;
const pool = new Pool({
    user: "postgres",
    host: "************",
    database: "DU-ETL",
    password: "postgres",
    port: 5432,
  });

function getCouponNumberException() {
    console.log("Get Coupon Exception details");
    try {
      return new Promise((resolve, reject) => {
        // FETCH FIRST 30 ROW ONLY
        pool.query(`SELECT DISTINCT ON ("CouponNumber") "CouponNumber" FROM du_dms_dealertrack_model.etl_labor_detail WHERE "CouponNumber" NOT IN (SELECT "Record Key" FROM du_dms_dealertrack_model.etl_coupon_and_discount)`,
          (error, results) => {
            if (error) {
              console.log(error);
              resolve(error);
            }
            try {
              resolve(results.rows);
            } catch (err) {
              console.log(err);
              resolve(err);
            }
          }
        );
      });
    } catch (error) {
      console.log(error);
      resolve(error);
    }
  }


  module.exports = {
    getCouponNumberException: getCouponNumberException
}
  


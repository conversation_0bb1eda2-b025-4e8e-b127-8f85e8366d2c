const agendaDbModel = require("../model/agendaDb");
const path = require("path");
const fs = require("fs");

exports.haltAndResume = async (inpObj, cb) => {
    try {
        const inputFileName = inpObj.extractFile;
        const dmsName = inpObj.dms;
        let chartOfAccountsFilePath;
        let processJobId;
        console.log("inputFileName:", inputFileName);
        console.log("dmsName:", dmsName);

        let haltFilePath,
            targetFilePath,
            basename,
            uniqueIdentifier,
            mageStoreCode,
            extractJobName,
            processorJobName,
            haltOverRideUpdateStatus,
            deleteHaltProcessorStatus;

        basename = path.basename(inputFileName);
        uniqueIdentifier = basename.split("-").reverse()[1];
        mageStoreCode = basename.split("-")[1];

        if (dmsName.toLowerCase().trim() == "dealertrack") {
            haltFilePath =
                "/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed/" +
                inputFileName;

            targetFilePath =
                "/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/scheduler-temp/dealertrack-zip-eti/" +
                basename;

            extractJobName = "DEALERTRACK";
            processorJobName = "DEALERTRACK-PROCESS-JSON";

            chartOfAccountsFilePath = inpObj.chartOfAccountsFilePath;
            processJobId = inpObj.processJobId;

            console.log("basename:", basename);
            console.log("haltFilePath:", haltFilePath);
            console.log("targetFilePath:", targetFilePath);
            console.log("uniqueIdentifier:", uniqueIdentifier);
            console.log("mageStoreCode:", mageStoreCode);
            console.log("extractJobName:", extractJobName);
            console.log("processorJobName:", processorJobName);
            console.log("chartOfAccountsFilePath:", chartOfAccountsFilePath);
            console.log("processJobId:", processJobId);
            let processorUniqueId = inpObj.processorUniqueId.split('-')[0];
            haltOverRideUpdateStatus = await agendaDbModel.updateHaltOverRideDealerTrack(
                uniqueIdentifier,
                extractJobName,
                mageStoreCode,
                chartOfAccountsFilePath,
                processJobId,
                processorUniqueId
            );
        } else if (dmsName.toLowerCase().trim() == "cdk3pa") {
            haltFilePath =
                "/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/dead-letter-processed/" +
                inputFileName;

            targetFilePath =
                "/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/" +
                basename;

            extractJobName = "CDK_EXTRACT";
            processorJobName = "PROCESS_XML";

            console.log("basename:", basename);
            console.log("haltFilePath:", haltFilePath);
            console.log("targetFilePath:", targetFilePath);
            console.log("uniqueIdentifier:", uniqueIdentifier);
            console.log("mageStoreCode:", mageStoreCode);
            console.log("extractJobName:", extractJobName);
            console.log("processorJobName:", processorJobName);
            let processorUniqueId = inpObj.processorUniqueId.split('-')[0];

            let fetchResult, storeDataArray, extractIndex = 0;
            fetchResult = await agendaDbModel.fetchRecordByUniqueIdCDK(uniqueIdentifier);

            console.log('++++++++++++++++++++++++++++++++++++++++++++')
            console.log('fetchResult:', JSON.stringify(fetchResult));
            console.log('++++++++++++++++++++++++++++++++++++++++++++')

            if (fetchResult.response[0]) {
                if (fetchResult.response[0].hasOwnProperty("data")) {
                    if (fetchResult.response[0].data) {
                        if (fetchResult.response[0].data.hasOwnProperty("storeDataArray")) {
                            if (fetchResult.response[0].data.storeDataArray) {
                                storeDataArray = fetchResult.response[0].data.storeDataArray;
                            }
                        }
                    }
                }
            }

            console.log('++++++++++++++++++++++++++++++++++++++++++++')
            console.log('storeDataArray:', storeDataArray);
            console.log('++++++++++++++++++++++++++++++++++++++++++++')

            if (storeDataArray) {
                if (storeDataArray.length > 0) {
                    extractIndex = storeDataArray.findIndex(function (item, i) {
                        return item.dealerId == uniqueIdentifier
                    });
                }
            }

            console.log('********************************************')
            console.log('extractIndex:', extractIndex);
            console.log('********************************************')


            haltOverRideUpdateStatus = await agendaDbModel.updateHaltOverRideCDK3PA(
                uniqueIdentifier,
                extractJobName,
                mageStoreCode,
                extractIndex,
                processorUniqueId
            );
        } else if (dmsName.toLowerCase().trim() == "cdkflex") {
            haltFilePath =
                "/home/<USER>/tmp/du-etl-dms-cdkflex-extractor-work/dead-letter-processed/" +
                inputFileName;

            targetFilePath =
                "/home/<USER>/tmp/du-etl-dms-cdkflex-extractor-work/scheduler-temp/cdkflex-zip-eti/" +
                basename;

            extractJobName = "CDKFLEX_EXTRACT";
            processorJobName = "CDKFLEX_PROCESS_XML";

            console.log("basename:", basename);
            console.log("haltFilePath:", haltFilePath);
            console.log("targetFilePath:", targetFilePath);
            console.log("uniqueIdentifier:", uniqueIdentifier);
            console.log("mageStoreCode:", mageStoreCode);
            console.log("extractJobName:", extractJobName);
            console.log("processorJobName:", processorJobName);

            let fetchResult, storeDataArray, extractIndex = 0;
            fetchResult = await agendaDbModel.fetchRecordByUniqueIdCDK(uniqueIdentifier);

            console.log('++++++++++++++++++++++++++++++++++++++++++++')
            console.log('fetchResult:', JSON.stringify(fetchResult));
            console.log('++++++++++++++++++++++++++++++++++++++++++++')

            if (fetchResult.response[0]) {
                if (fetchResult.response[0].hasOwnProperty("data")) {
                    if (fetchResult.response[0].data) {
                        if (fetchResult.response[0].data.hasOwnProperty("storeDataArray")) {
                            if (fetchResult.response[0].data.storeDataArray) {
                                storeDataArray = fetchResult.response[0].data.storeDataArray;
                            }
                        }
                    }
                }
            }

            console.log('++++++++++++++++++++++++++++++++++++++++++++')
            console.log('storeDataArray:', storeDataArray);
            console.log('++++++++++++++++++++++++++++++++++++++++++++')

            if (storeDataArray) {
                if (storeDataArray.length > 0) {
                    extractIndex = storeDataArray.findIndex(function (item, i) {
                        return item.dealerId == uniqueIdentifier
                    });
                }
            }

            console.log('********************************************')
            console.log('extractIndex:', extractIndex);
            console.log('********************************************')


            haltOverRideUpdateStatus = await agendaDbModel.updateHaltOverRideCDK3PA(
                uniqueIdentifier,
                extractJobName,
                mageStoreCode,
                extractIndex
            );
        } else if (dmsName.toLowerCase().trim() == "automate") {
            haltFilePath =
                "/home/<USER>/tmp/du-etl-dms-automate-extractor-work/dead-letter-processed/" +
                inputFileName;

            targetFilePath =
                "/home/<USER>/tmp/du-etl-dms-automate-extractor-work/scheduler-temp/automate-zip-eti/" +
                basename;

            extractJobName = "AUTOMATE";
            processorJobName = "AUTOMATE-PROCESS-JSON";

            console.log("basename:", basename);
            console.log("haltFilePath:", haltFilePath);
            console.log("targetFilePath:", targetFilePath);
            console.log("uniqueIdentifier:", uniqueIdentifier);
            console.log("mageStoreCode:", mageStoreCode);
            console.log("extractJobName:", extractJobName);
            console.log("processorJobName:", processorJobName);
            let processorUniqueId =  inpObj.processorUniqueId.split('-')[0];

            haltOverRideUpdateStatus = await agendaDbModel.updateHaltOverRideAutoMate(
                uniqueIdentifier,
                extractJobName,
                mageStoreCode,
                processorUniqueId
            );

        } else if (dmsName.toLowerCase().trim() == "reynolds") {
            haltFilePath =
                "/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/dead-letter-processed/" +
                inputFileName;

            targetFilePath =
                "/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/" +
                basename;

            extractJobName = "REYNOLDS";
            processorJobName = "REYNOLDSRCI-PROCESS-JSON";

            // chartOfAccountsFilePath =  inpObj.chartOfAccountsFilePath;

            console.log("basename:", basename);
            console.log("haltFilePath:", haltFilePath);
            console.log("targetFilePath:", targetFilePath);
            console.log("uniqueIdentifier:", uniqueIdentifier);
            console.log("mageStoreCode:", mageStoreCode);
            console.log("extractJobName:", extractJobName);
            console.log("processorJobName:", processorJobName);
            let processorUniqueId = inpObj.processorUniqueId.split('-')[0];

            // console.log("chartOfAccountsFilePath:", chartOfAccountsFilePath);
            haltOverRideUpdateStatus = await agendaDbModel.updateHaltOverRideReynoldsRCI(
                uniqueIdentifier,
                extractJobName,
                mageStoreCode,
                processorUniqueId
                // chartOfAccountsFilePath
            );
        } else if (dmsName.toLowerCase().trim() == "autosoft") {
            haltFilePath =
                "/home/<USER>/tmp/du-etl-dms-autosoft-extractor-work/dead-letter-processed/" +
                inputFileName;

            targetFilePath =
                "/home/<USER>/tmp/du-etl-dms-autosoft-extractor-work/scheduler-temp/autosoft-zip-eti/" +
                basename;

            extractJobName = "AUTOSOFT";
            processorJobName = "AUTOSOFT-PROCESS-JSON";
            console.log('inputFileName>>>>>>>>>>>>>>>>>',inputFileName);
            let processorUniqueId =  inpObj.processorUniqueId.split('-')[0];
            haltOverRideUpdateStatus = await agendaDbModel.updateHaltOverRideAutoSoft(
                inputFileName,
                extractJobName,
                processorUniqueId
            );
        } else if (dmsName.toLowerCase().trim() == "dominion") {
            haltFilePath =
                "/home/<USER>/tmp/du-etl-dms-dominionvue-extractor-work/dead-letter-processed/" +
                inputFileName;

            targetFilePath =
                "/home/<USER>/tmp/du-etl-dms-dominionvue-extractor-work/scheduler-temp/dominionvue-zip-eti/" +
                basename;

            extractJobName = "DOMINION";
            processorJobName = "DOMINION-PROCESS-JSON";
            console.log("basename:", basename);
            console.log("haltFilePath:", haltFilePath);
            
console.log("targetFilePath:", targetFilePath);
            console.log("uniqueIdentifier:", uniqueIdentifier);
            console.log("mageStoreCode:", mageStoreCode);
            console.log("extractJobName:", extractJobName);
            console.log("processorJobName:", processorJobName);
            let processorUniqueId =  inpObj.processorUniqueId.split('-')[0];
            haltOverRideUpdateStatus = await agendaDbModel.updateHaltOverRideDominion(
                uniqueIdentifier,
                extractJobName,
                mageStoreCode,
                processorUniqueId
            );
        } else if (dmsName.toLowerCase().trim() == "tekionapi") {
            haltFilePath =
                "/home/<USER>/tmp/du-etl-dms-tekionapi-extractor-work/dead-letter-processed/" +
                inputFileName;

            targetFilePath =
                "/home/<USER>/tmp/du-etl-dms-tekionapi-extractor-work/scheduler-temp/tekionapi-zip-eti/" +
                basename;

            extractJobName = "TEKIONAPI";
            processorJobName = "TEKIONAPI-PROCESS-JSON";

            console.log("basename:", basename);
            console.log("haltFilePath:", haltFilePath);
            console.log("targetFilePath:", targetFilePath);
            console.log("uniqueIdentifier:", uniqueIdentifier);
            console.log("mageStoreCode:", mageStoreCode);
            console.log("extractJobName:", extractJobName);
            console.log("processorJobName:", processorJobName);
            let processorUniqueId =  inpObj.processorUniqueId.split('-')[0];
            haltOverRideUpdateStatus = await agendaDbModel.updateHaltOverRideTekionAPI(
                uniqueIdentifier,
                extractJobName,
                mageStoreCode,
                processorUniqueId
            );
        } else if (dmsName.toLowerCase().trim() == "fortellis") {
            haltFilePath =
                "/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/dead-letter-processed/" +
                inputFileName;

            targetFilePath =
                "/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/" +
                basename;

            extractJobName = "FORTELLIS";
            processorJobName = "FORTELLIS-PROCESS-JSON";
            console.log("basename:", basename);
            console.log("haltFilePath:", haltFilePath);
            console.log("targetFilePath:", targetFilePath);
            console.log("uniqueIdentifier:", uniqueIdentifier);
            console.log("mageStoreCode:", mageStoreCode);
            console.log("extractJobName:", extractJobName);
            console.log("processorJobName:", processorJobName);
            let processorUniqueId = inpObj.processorUniqueId.split('-')[0];
            haltOverRideUpdateStatus = await agendaDbModel.updateHaltOverRideFortellis(
                uniqueIdentifier,
                extractJobName,
                mageStoreCode,
                processorUniqueId
                // chartOfAccountsFilePath
            );
        }else if (dmsName.toLowerCase().trim() == "fortellis") {
            haltFilePath =
                "/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/dead-letter-processed/" +
                inputFileName;

            targetFilePath =
                "/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/" +
                basename;

            extractJobName = "FORTELLIS";
            processorJobName = "FORTELLIS-PROCESS-JSON";
            console.log("basename:", basename);
            console.log("haltFilePath:", haltFilePath);
            console.log("targetFilePath:", targetFilePath);
            console.log("uniqueIdentifier:", uniqueIdentifier);
            console.log("mageStoreCode:", mageStoreCode);
            console.log("extractJobName:", extractJobName);
            console.log("processorJobName:", processorJobName);
            let processorUniqueId =  inpObj.processorUniqueId.split('-')[0];
            haltOverRideUpdateStatus = await agendaDbModel.updateHaltOverRideFortellis(
                uniqueIdentifier,
                extractJobName,
                mageStoreCode,
                processorUniqueId
            );
        }
        else {
            console.log("Unknown DMS!");
            cb({
                status: false,
                response: "Unknown DMS!",
            });
        }

        console.log(
            "haltOverRideUpdateStatus.status:",
            haltOverRideUpdateStatus.status
        );

        if (haltOverRideUpdateStatus.status) {
            console.log("Success", haltOverRideUpdateStatus.status);
            console.log('haltfilepath', haltFilePath);
            console.log('targetfilepath', targetFilePath);

            try {
                if (fs.existsSync(haltFilePath)) {
                    console.log('haltfilepath', haltFilePath);
                    console.log('targetfilepath', targetFilePath);
                    fs.copyFileSync(haltFilePath, targetFilePath);

                    if (dmsName == 'DealerTrack') {
                        try {

                            let newFilename = inputFileName.replace(/\.[^/.]+$/, +Date.now() + ".zip");

                            fs.rename(targetFilePath, '/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/scheduler-temp/dealertrack-zip-eti/' + newFilename, function (err) {
                                if (err) {

                                    throw err;

                                }
                                else {
                                    cb({
                                        status: true,
                                        response: "success",
                                    });
                                }

                                
            
                  });
                           }
                           catch(err)
                           {
                                    console.log(err);   
                           }
                    }
                    else
                    {
                         try {
                        deleteHaltProcessorStatus = await agendaDbModel.removeHaltProcessorJob(
                            basename,
                            processorJobName
                        );
                      
                             
                        if( deleteHaltProcessorStatus.status){
                    
        cb({
                                status: true,
                                response: "success",
                            });



                        }
                        }
                        catch (err) {
                            console.log(err);
                        }
                    }
                 


                } else {
                    cb({
                        status: false,
                        response: `${haltFilePath} not exist`,
                    });
                }
            } catch (err) {
                console.log(err);
                cb({
                    status: false,
                    response: err,
                });
            }
        } else {
            cb({
                status: false,
                response: "error",
            });
        }
    } catch (err) {
        console.log(err);
    }
};

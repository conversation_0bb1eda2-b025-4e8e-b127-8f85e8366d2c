const MongoClient = require("mongodb").MongoClient;
module.exports = class PayTypeHistory {
  constructor() {}
  static savePayType(payTypeObj, cb) {
    return new Promise((resolve, reject) => {
      MongoClient.connect(
        "mongodb://localhost:27017/pay_type_db",
        function (err, client) {
          if (err) {
            console.log(err);
            resolve(err);
          }
          const db = client.db("pay_type_db");
          db.collection("pay_type_table").insertOne(
            payTypeObj,
            function (err, res) {
              if (err) {
                console.log(err);
                resolve(err);
              }
              resolve("record inserted");
            }
          );
        }
      );
    });
  }

  static getPayTypeHistory(dealerId) {
    return new Promise((resolve, reject) => {
      console.log(dealerId);
      try {
        MongoClient.connect(
          "mongodb://localhost:27017/pay_type_db",
          function (err, client) {
            if (err) throw err;
            const db = client.db("pay_type_db");
            db.collection("pay_type_table")
              .find({ dealerId: dealerId }).sort({'_id':-1}).limit(1)
              .toArray(function (err, result) {
                if (err) {
                  console.log(err);
      
                  resolve({ status: false, response: JSON.stringify(err) });
                }
                console.log(result);
                resolve({ status: true, response: result });
              });
          }
        );
      } catch (err) {
        console.log(err);
        resolve({ status: false, response: JSON.stringify(err) });
      }
    });
  }
};

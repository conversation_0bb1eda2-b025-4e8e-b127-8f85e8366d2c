const winston = require("winston");
const DailyRotateFile = require("winston-daily-rotate-file");
const path = require("path");
const currentDate = new Date();
const currentDateFormatted = currentDate.toISOString().split("T")[0]; // Format the current date as YYYY-MM-DD

// Backend logger
const logger = winston.createLogger({
  level: "debug",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new DailyRotateFile({
      filename: "logs/Common-BE/Scheduler-%DATE%.log",
      datePattern: "YYYY-MM-DD-HH", // Create a new log file every day
      dirname: path.join("logs/Common-BE", currentDateFormatted), // Create a folder with current date inside 'logs/BE'
      maxFiles: "90d", // Keep logs for 90 days
    }),
  ],
});

// Frontend logger
const frontendLogger = winston.createLogger({
  level: "debug",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new DailyRotateFile({
      filename: "logs/FE/Scheduler-FE-%DATE%.log",
      datePattern: "YYYY-MM-DD-HH", // Create a new log file every day
      dirname: path.join("logs/FE", currentDateFormatted), // Create a folder with current date inside 'logs/FE'
      maxFiles: "90d", // Keep logs for 90 days
    }),
  ],
});

// export { logger, frontendLogger };
module.exports = {
  logger,
  frontendLogger,
};

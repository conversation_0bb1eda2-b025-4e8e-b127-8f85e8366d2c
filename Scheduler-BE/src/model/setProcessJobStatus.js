

const MongoClient = require("mongodb").MongoClient;
const ObjectId = require('mongodb').ObjectID;

module.exports = class SetProcessJobStatus{

     constructor(){};

     static setProcessJobStatusForCdk3pa(dealerId,mageStoreCode,message){
        console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",dealerId);
        console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",mageStoreCode);
        console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",message);
        return new Promise((resolve,reject)=>{
            try {
                MongoClient.connect(
                  "mongodb://localhost:27017/agenda",
                  function (err, client) {
                    if (err) throw err;
                    const db = client.db("agenda");
                    db.collection("agendaJobs").updateOne(
                      {
                        "data.storeDataArray.dealerId": dealerId,
                        name: 'CDK_EXTRACT',
                        "data.storeDataArray.mageStoreCode": mageStoreCode
                      },
        
                      { 
                        $set: {
                             
                            "data.storeDataArray.$.processJobStatus": message
                        
                        } 
                      },
                      
                      function (err, res) {
                        if (err) {
                            console.log("Error Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",err);
                          resolve({ status: false, response: JSON.stringify(err) });
                        }
                        console.log("Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",res);
                        resolve({ status: true, response: res });
                      }
                    );
                  }
                );
              } catch (err) {
                console.log("oooooooooooooooooooooooooooo",err);
                resolve({ status: false, response: JSON.stringify(err) });
              } 
        })
     }
     static setProcessJobStatusForRunningJob(inputFile, processorStatus) {
      inputFile = inputFile.trim();
      processorStatus = processorStatus.trim();
      console.log("INSIDE setProcessJob running StatusForCdk3pa:", inputFile, processorStatus);
    
      return new Promise((resolve, reject) => {
        try {
          MongoClient.connect(
            "mongodb://localhost:27017/agenda",
            function (err, client) {
              if (err) throw err;
    
              const db = client.db("agenda");
              const collection = db.collection("agendaJobs");
              const currentUtcDate = new Date();
    
              collection.update(
                {
                  "data.inputFile": inputFile,
                },
                {
                  $set: {
                    processorRunningStatus: processorStatus,
                    processorStatusUpdatedAt: currentUtcDate
                  },
                },
                (updateErr, result) => {
                  if (updateErr) {
                    console.error("Update error:", updateErr);
                    reject(updateErr);
                  } else {
                    console.log("result%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%",result);
                    resolve(result);
                  }
                }
              );
            }
          );
        } catch (err) {
          console.error("Error:", err);
          resolve({ status: false, response: JSON.stringify(err) });
        }
      });
    }
    

     static setProcessJobStatusForAutoMate(dealerId,mageStoreCode,message){
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",dealerId);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",mageStoreCode);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",message);
      return new Promise((resolve,reject)=>{
          try {
              MongoClient.connect(
                "mongodb://localhost:27017/agenda",
                function (err, client) {
                  if (err) throw err;
                  const db = client.db("agenda");
                  db.collection("agendaJobs").updateOne(
                    {
                      "data.storeDataArray.dealerId": dealerId,
                      name: 'AUTOMATE',
                      "data.storeDataArray.mageStoreCode": mageStoreCode
                    },
      
                    { 
                      $set: {
                           
                          "data.storeDataArray.$.processJobStatus": message
                      
                      } 
                    },
                    
                    function (err, res) {
                      if (err) {
                          console.log("Error Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",err);
                        resolve({ status: false, response: JSON.stringify(err) });
                      }
                      console.log("Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",res);
                      resolve({ status: true, response: res });
                    }
                  );
                }
              );
            } catch (err) {
              console.log("oooooooooooooooooooooooooooo",err);
              resolve({ status: false, response: JSON.stringify(err) });
            } 
      })
     }

     static setProcessJobStatusForAdam(mageStoreCode,message){
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",dealerId);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",mageStoreCode);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",message);
      return new Promise((resolve,reject)=>{
          try {
              MongoClient.connect(
                "mongodb://localhost:27017/agenda",
                function (err, client) {
                  if (err) throw err;
                  const db = client.db("agenda");
                  db.collection("agendaJobs").updateOne(
                    {
                      "data.storeDataArray.dealerId": dealerId,
                      name: 'ADAM',
                      "data.storeDataArray.mageStoreCode": mageStoreCode
                    },
      
                    { 
                      $set: {
                           
                          "data.storeDataArray.$.processJobStatus": message
                      
                      } 
                    },
                    
                    function (err, res) {
                      if (err) {
                          console.log("Error Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",err);
                        resolve({ status: false, response: JSON.stringify(err) });
                      }
                      console.log("Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",res);
                      resolve({ status: true, response: res });
                    }
                  );
                }
              );
            } catch (err) {
              console.log("oooooooooooooooooooooooooooo",err);
              resolve({ status: false, response: JSON.stringify(err) });
            } 
      })
     }
     

     static setProcessJobStatusForAutoSoft(mageStoreCode,message){
      // console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",dealerId);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",mageStoreCode);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",message);
      return new Promise((resolve,reject)=>{
          try {
              MongoClient.connect(
                "mongodb://localhost:27017/agenda",
                function (err, client) {
                  if (err) throw err;
                  const db = client.db("agenda");
                  db.collection("agendaJobs").updateOne(
                    {
                      // "data.storeDataArray.locationId": dealerId,
                      name: 'AUTOSOFT',
                      "data.storeDataArray.mageStoreCode": mageStoreCode
                    },
      
                    { 
                      $set: {
                           
                          "data.storeDataArray.$.processJobStatus": message
                      
                      } 
                    },
                    
                    function (err, res) {
                      if (err) {
                          console.log("Error Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",err);
                        resolve({ status: false, response: JSON.stringify(err) });
                      }
                      console.log("Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",res);
                      resolve({ status: true, response: res });
                    }
                  );
                }
              );
            } catch (err) {
              console.log("oooooooooooooooooooooooooooo",err);
              resolve({ status: false, response: JSON.stringify(err) });
            } 
      })
     }

     static setProcessJobStatusForDealerBuilt(dealerId,mageStoreCode,message){
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",dealerId);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",mageStoreCode);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",message);
      return new Promise((resolve,reject)=>{
          try {
              MongoClient.connect(
                "mongodb://localhost:27017/agenda",
                function (err, client) {
                  if (err) throw err;
                  const db = client.db("agenda");
                  db.collection("agendaJobs").updateOne(
                    {
                      "data.storeDataArray.dealerId": dealerId,
                      name: 'DEALERBUILT',
                      "data.storeDataArray.mageStoreCode": mageStoreCode
                    },
      
                    { 
                      $set: {
                           
                          "data.storeDataArray.$.processJobStatus": message
                      
                      } 
                    },
                    
                    function (err, res) {
                      if (err) {
                          console.log("Error Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",err);
                        resolve({ status: false, response: JSON.stringify(err) });
                      }
                      console.log("Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",res);
                      resolve({ status: true, response: res });
                    }
                  );
                }
              );
            } catch (err) {
              console.log("oooooooooooooooooooooooooooo",err);
              resolve({ status: false, response: JSON.stringify(err) });
            } 
      })
     }

     static setProcessJobStatusForDealerTrack(dealerId,mageStoreCode,message){``
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",dealerId);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",mageStoreCode);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",message);
      return new Promise((resolve,reject)=>{
          try {
              MongoClient.connect(
                "mongodb://localhost:27017/agenda",
                function (err, client) {
                  if (err) throw err;
                  const db = client.db("agenda");
                  db.collection("agendaJobs").updateOne(
                    {
                      "data.storeDataArray.enterpriseCode": dealerId,
                      name: 'DEALERTRACK',
                      "data.storeDataArray.mageStoreCode": mageStoreCode
                    },
      
                    { 
                      $set: {
                           
                          "data.storeDataArray.$.processJobStatus": message
                      
                      } 
                    },
                    
                    function (err, res) {
                      if (err) {
                          console.log("Error Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",err);
                        resolve({ status: false, response: JSON.stringify(err) });
                      }
                      console.log("Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",res);
                      resolve({ status: true, response: res });
                    }
                  );
                }
              );
            } catch (err) {
              console.log("oooooooooooooooooooooooooooo",err);
              resolve({ status: false, response: JSON.stringify(err) });
            } 
      })
     }

     static setProcessJobStatusForDominion(dealerId,mageStoreCode,message){
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",dealerId);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",mageStoreCode);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",message);
      return new Promise((resolve,reject)=>{
          try {
              MongoClient.connect(
                "mongodb://localhost:27017/agenda",
                function (err, client) {
                  if (err) throw err;
                  const db = client.db("agenda");
                  db.collection("agendaJobs").updateOne(
                    {
                      "data.storeDataArray.dealerID": dealerId,
                      name: 'DOMINION',
                      "data.storeDataArray.mageStoreCode": mageStoreCode
                    },
      
                    { 
                      $set: {
                           
                          "data.storeDataArray.$.processJobStatus": message
                      
                      } 
                    },
                    
                    function (err, res) {
                      if (err) {
                          console.log("Error Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",err);
                        resolve({ status: false, response: JSON.stringify(err) });
                      }
                      console.log("Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",res);
                      resolve({ status: true, response: res });
                    }
                  );
                }
              );
            } catch (err) {
              console.log("oooooooooooooooooooooooooooo",err);
              resolve({ status: false, response: JSON.stringify(err) });
            } 
      })
     }

     static setProcessJobStatusForReynolds(dealerId,mageStoreCode,message){
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",dealerId);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",mageStoreCode);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",message);
      return new Promise((resolve,reject)=>{
          try {
              MongoClient.connect(
                "mongodb://localhost:27017/agenda",
                function (err, client) {
                  if (err) throw err;
                  const db = client.db("agenda");
                  db.collection("agendaJobs").updateOne(
                    {
                      "data.storeDataArray.locationId": dealerId,
                      name: 'REYNOLDS',
                      "data.storeDataArray.mageStoreCode": mageStoreCode
                    },
      
                    { 
                      $set: {
                           
                          "data.storeDataArray.$.processJobStatus": message
                      
                      } 
                    },
                    
                    function (err, res) {
                      if (err) {
                          console.log("Error Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",err);
                        resolve({ status: false, response: JSON.stringify(err) });
                      }
                      console.log("Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",res);
                      resolve({ status: true, response: res });
                    }
                  );
                }
              );
            } catch (err) {
              console.log("oooooooooooooooooooooooooooo",err);
              resolve({ status: false, response: JSON.stringify(err) });
            } 
      })
     }
 
     static setProcessJobUploadStatus(jobId="66b9e00603cc8a260a952055",uploadStatus){
      console.log("INSIDE setProcessJobUploadStatus:::::::::::::::::::::::::::::",jobId);
      console.log("INSIDE setProcessJobUploadStatus:::::::::::::::::::::::::::::",uploadStatus);
      return new Promise((resolve,reject)=>{
          try {
              MongoClient.connect(
                "mongodb://localhost:27017/agenda",
                function (err, client) {
                  if (err) throw err;
                  const db = client.db("agenda");
                  db.collection("agendaJobs").updateOne(
                    {
                      _id: jobId                    
                    },      
                    { 
                      $set: {                           
                        uploadStatus                      
                      } 
                    },
                    
                    function (err, res) {
                      if (err) {
                          console.log("Error Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",err);
                        resolve({ status: false, response: JSON.stringify(err) });
                      }
                      console.log("Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",res);
                      resolve({ status: true, response: res });
                    }
                  );
                }
              );
            } catch (err) {
              console.log("Error Saving Processor Status>>>>>>>>>>>>>>>>>>>>>>>>>>",err);
              resolve({ status: false, response: JSON.stringify(err) });
            } 
      })
     }

     static setProcessFileName(dealerId,mageStoreCode,fileName,dms){
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",dealerId);
      console.log("INSIDE setProcessJobStatusForCdk3pa:::::::::::::::::::::::::::::",mageStoreCode);
      console.log("INSIDE fileName:::::::::::::::::::::::::::::",fileName);
      console.log("INSIDE dms:::::::::::::::::::::::::::::",dms);
      return new Promise((resolve,reject)=>{
          try {
              MongoClient.connect(
                "mongodb://localhost:27017/agenda",
                function (err, client) {
                  if (err) throw err;
                  const db = client.db("agenda");
                  db.collection("agendaJobs").updateOne(
                    {
                      "data.storeDataArray.dealerId": dealerId,
                      name: dms,
                      "data.storeDataArray.mageStoreCode": mageStoreCode
                    },
      
                    { 
                      $set: {
                           
                          "data.storeDataArray.$.processFileName": fileName
                      
                      } 
                    },
                    
                    function (err, res) {
                      if (err) {
                          console.log("Error Saving Processor fileName>>>>>>>>>>>>>>>>>>>>>>>>>>",err);
                        resolve({ status: false, response: JSON.stringify(err) });
                      }
                      console.log("Saving Processor filename>>>>>>>>>>>>>>>>>>>>>>>>>>",res);
                      resolve({ status: true, response: res });
                    }
                  );
                }
              );
            } catch (err) {
              console.log("oooooooooooooooooooooooooooo",err);
              resolve({ status: false, response: JSON.stringify(err) });
            } 
      })
   }


}



  

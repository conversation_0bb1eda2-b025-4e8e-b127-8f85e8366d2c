# Scheduler-BE
The Scheduler back-end module of the DU-ETL suite

### Requirements

* Node (v8.0.0 or higher)
* mongoDB (v4.0.2)
* DU-ETL
* Setup Requestor Application for different DMSs in DU-ETL

### Installation
* Clone this repository

* Change directory to Scheduler-BE

    ```sh
    $ cd Scheduler-BE
    ```
* Install the dependencies

    ```sh
    $ npm install
    ```
* Create a .env file in the ~/.scheduler folder and paste the following content with proper values in it.

    ```
    # MongoDB connection details to save Agenda schedules

    MONGO_HOST='localhost'
    MONGO_PORT='27017'
    MONGO_DB='<database>'
    MONGO_USER='<username>'
    MONGO_PASS='<password>'

    ```
* Start the server

    ```sh
    $ JOB_TYPES=<dms>-extract,<dms>-process-<xml|json> npm start
    ```

### Heavy hitting server

For a heavy hitting server, run Scheduler Application and back-end jobs as separate services

* To run Scheduler Application service

    ```sh
    $ npm start
    ```

* To run CDK-Extract Job service

    ```sh
    $ JOB_TYPES=cdk-extract src/worker.js
    ```
* To run Process-XML Job service

    ```sh
    $ JOB_TYPES=cdk-process-xml src/worker.js
    ```
* To run Automate-Extract Job service

    ```sh
    $ JOB_TYPES=automate-extract src/worker.js
    ```
* To run Automate Process-JSON Job service

    ```sh
    $ JOB_TYPES=automate-process-json src/worker.js
    ```


### GraphQL Documentations and Scheduler Dashboard

Once the Scheduler Application is up and running (on port 3000 by default), you can access
the GraphQL documentation in http://localhost:3000/scheduler/graphiql and  Scheduler Dashboard
can be accessed from http://localhost:3000/scheduler/dashboard.

var express = require("express");
var path = require("path");
var favicon = require("serve-favicon");
var logger = require("morgan");
var cookieParser = require("cookie-parser");
var bodyParser = require("body-parser");
var cors = require("cors");
var index = require("./src/routes/index");
var auth = require("./src/routes/auth");
const graphqlCustom = require("./src/routes/graphqlCustom");
const fs = require("fs");
const api = require("./src/controllers/api");
var payTypes = require("./src/routes/payTypes");
var s360 = require("./src/routes//s360");
var checkDealerTrackAccessValidation = require("./src/routes/checkDealerTrackAccessValidation");
// const getDealerBuiltAccountingCSVFiles = require("./src/routes/getDealerBuiltAccountingCSVFiles");
const getReynoldsInputFiles = require("./src/routes/getReynoldsInputFiles");
const getAutosoftInputFiles = require("./src/routes/getAutosoftInputFiles");
const getPbsInputFiles = require("./src/routes/getPbsInputFiles");
const getQuorumInputFiles = require("./src/routes/getQuorumInputFiles");
const getUcsInputFiles = require("./src/routes/getUcsInputFiles");
const getTekionInputFiles = require("./src/routes/getTekionInputFiles");
const getMpkInputFiles = require("./src/routes/getMpkInputFiles");
const getMockServerInputFiles = require("./src/routes/getMockServerInputFiles");
const schedulerCustom = require("./src/routes/schedulerCustom");
const schedulerCustomApi = require("./src/routes/schedulerImportDetails");
var routes = require("./src/routes/router");
const activityLogFrontend = require("./src/routes/activityLogFrontend");
const segment = require("./src/controllers/SEGMENT/segmentManager");
const auditCustom = require("./src/routes/auditCustom");
// const uploadDealerTrackCouponAndDiscountCSVFile = require("./uploadDealerTrackCouponAndDiscountCSVFile");

/**
 * Run worker thread inside the Scheduler app if user pass
 * environment variable JOB_TYPES
 */
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  segment.saveSegment(`Application crashed!!!!!!!!!!!!!!!${err}`);
  segment.saveSegment(`Application crashed!!!!!!!!!!!!!!!${JSON.stringify(err)}`);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  segment.saveSegment(`Unhandled Rejection at: ${promise} reason:${reason}`);
  segment.saveSegment(`Unhandled Rejection at: ${JSON.stringify(promise)} reason:${JSON.stringify(reason)}`);
});

require("./src/worker");
require('./src/routes/sendScheduleReport');
require('./src/routes/sendPendingApprovals');

var app = express();
app.set("minVersion", "TLSv1.2");
app.set("maxVersion", "TLSv1.3");

// CORS options
const corsOptions = {
 origin(origin, callback) {
  callback(null, true);
 },
 credentials: true,
};

const allowCrossDomain = (req, res, next) => {
 res.header('Access-Control-Allow-Origin', '*');
 res.header('Access-Control-Allow-Methods', 'GET,POST,OPTIONS,PUT');
 res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
 next();
};

// app.use(cors(corsOptions));
// app.use(allowCrossDomain);

// view engine setup
app.set("views", path.join(__dirname, "views"));
app.set("view engine", "pug");
app.use(cors(corsOptions));
app.use(allowCrossDomain);

// uncomment after placing your favicon in /public
// app.use(favicon(path.join(__dirname, "public", "favicon.ico")));
/**
 * Logging modules
 */
var rfs = require("rotating-file-stream");
var logDirectory = path.join(__dirname, "logs");

// ensure log directory exists
fs.existsSync(logDirectory) || fs.mkdirSync(logDirectory);

// create a rotating write stream
var accessLogStream = rfs("access.log", {
 interval: "1d", // rotate daily
 path: logDirectory,
});
if (process.env.ENV == "production") {
 app.use(logger("combined", { stream: accessLogStream }));
} else {
 app.use(logger("dev"));
}
// app.use(bodyParser.json());
// app.use(bodyParser.urlencoded({ extended: false }));

app.use(express.json({ limit: "1000mb" }));
app.use(
  express.urlencoded({ limit: "1000mb", extended: true, parameterLimit: 1500000 })
);

app.use(cookieParser());
app.use(express.static(path.join(__dirname, "public")));
// app.use("/", index);
app.use('/scheduler/status', (req, res, next) => {
  res.status(200).json({ status: "Scheduler Application Running" });
});
app.use("/scheduler/activityLog", activityLogFrontend);
app.use('/scheduler/auditCustom',auditCustom);
app.use("/scheduler", routes);
app.use("/schedulerCustom", schedulerCustom);
app.use("/schedulerCustomApi", schedulerCustomApi);
app.use("/getMockServerInputFiles", getMockServerInputFiles);
// catch 404 and forward to error handler
app.use(function (req, res, next) {
 var err = new Error("Not Found");
 err.status = 404;
 next(err);
});

// error handler
app.use(function (err, req, res, next) {
 // set locals, only providing error in development
 res.locals.message = err.message;
 res.locals.error = req.app.get("env") === "development" ? err : {};

 // render the error page
 res.status(err.status || 500);
 res.render("error");
});

module.exports = app;

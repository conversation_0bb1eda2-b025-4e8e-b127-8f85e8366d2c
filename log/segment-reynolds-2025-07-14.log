

Mon Jul 14 2025 05:54:35 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:54:35 GMT+0000 : processorStatus
Mon Jul 14 2025 05:54:35 GMT+0000 : stdout: Processor status: Processing Started

Mon Jul 14 2025 05:54:36 GMT+0000 : stdout: HALT_OVER_RIDE:false

Mon Jul 14 2025 05:54:36 GMT+0000 : stdout: [1;32mProcessing Input Zip Archive: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip[0m

Mon Jul 14 2025 05:54:36 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:54:36 GMT+0000 : processorStatus
Mon Jul 14 2025 05:54:36 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Started

Mon Jul 14 2025 05:54:37 GMT+0000 : stdout: [1;36mUnzipping Input to Work /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp[0m

Mon Jul 14 2025 05:54:37 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:54:37 GMT+0000 : processorStatus
Mon Jul 14 2025 05:54:37 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Completed

Mon Jul 14 2025 05:54:38 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:54:38 GMT+0000 : processorStatus
Mon Jul 14 2025 05:54:38 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Started

Mon Jul 14 2025 05:54:39 GMT+0000 : stdout: [1;36mCreating Schema from Models[0m

Mon Jul 14 2025 05:54:40 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:54:40 GMT+0000 : processorStatus
Mon Jul 14 2025 05:54:40 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Completed

Mon Jul 14 2025 05:54:41 GMT+0000 : stdout: [1;36mSaving UUID to model[0m

Mon Jul 14 2025 05:54:41 GMT+0000 : stdout: [1;36mGenerating JQ Transforms[0m

Mon Jul 14 2025 05:54:41 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:54:41 GMT+0000 : processorStatus
Mon Jul 14 2025 05:54:41 GMT+0000 : stdout: Processor status: 3/16 Iterating Over Zip File Contents Started

Mon Jul 14 2025 05:54:45 GMT+0000 : stdout: [1;36mIterating Over Zip File Contents[0m

Mon Jul 14 2025 05:54:45 GMT+0000 : stdout: [1;36mClosed RO JSON[0m

Mon Jul 14 2025 05:54:45 GMT+0000 : stderr: [1;36mBeginning zip processing in /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/jsonconversions ./reynolds0.xml
Mon Jul 14 2025 05:54:45 GMT+0000 : stderr: [0m

Mon Jul 14 2025 05:54:45 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Mon Jul 14 2025 05:54:45 GMT+0000 : stdout: [1;36mConvert ./reynolds0.xml to json file[0m

Mon Jul 14 2025 05:54:45 GMT+0000 : stdout: Mon Jul 14 05:54:45 UTC 2025 Found # 29

Mon Jul 14 2025 05:54:45 GMT+0000 : stdout: Mon Jul 14 05:54:45 UTC 2025 Transform Begin

Mon Jul 14 2025 05:54:45 GMT+0000 : stdout: RO_COUNT is greater than 1

Mon Jul 14 2025 05:54:45 GMT+0000 : stdout: Mon Jul 14 05:54:45 UTC 2025 Transform End

Mon Jul 14 2025 05:54:45 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:54:45 GMT+0000 : processorStatus
Mon Jul 14 2025 05:54:45 GMT+0000 : stdout: Processor status: 4/16 Loading Individual ROs Started

Mon Jul 14 2025 05:54:46 GMT+0000 : stdout: [1;36mLoading Individual ROs (can take a while)[0m

Mon Jul 14 2025 05:54:46 GMT+0000 : stdout: Mon Jul 14 05:54:46 UTC 2025

Mon Jul 14 2025 05:54:46 GMT+0000 : stdout: [1;36mReady to Load remaining ROs[0m

Mon Jul 14 2025 05:54:56 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:54:56 GMT+0000 : processorStatus
Mon Jul 14 2025 05:54:56 GMT+0000 : stdout: Mon Jul 14 05:54:56 UTC 2025

Mon Jul 14 2025 05:54:56 GMT+0000 : stdout: [1;36mIndividual ROs Imported[0m
Processor status: 4/16 Loading Individual ROs Completed

Mon Jul 14 2025 05:54:57 GMT+0000 : stdout: [93m
[1mNo CCC Exception

Mon Jul 14 2025 05:54:57 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:54:57 GMT+0000 : processorStatus
Mon Jul 14 2025 05:54:57 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Started

Mon Jul 14 2025 05:54:58 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Mon Jul 14 2025 05:54:58 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Mon Jul 14 2025 05:54:59 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:54:59 GMT+0000 : processorStatus
Mon Jul 14 2025 05:54:59 GMT+0000 : stdout: ROs:

Mon Jul 14 2025 05:54:59 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Completed

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: ROs:

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: [1;36mCreating exclusion reports
Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: [0m

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: [1;36mGenerating exception report[0m

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: [1;36mEnumerating ROs[0m

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout:  count_all | count_non_numeric 
-----------+-------------------
        29 |                 0
(1 row)


Mon Jul 14 2025 05:55:00 GMT+0000 : Total Ros Count55555555555,Total Ros Count:-    29

Mon Jul 14 2025 05:55:00 GMT+0000 : Total Ro90999999999,-    29

Mon Jul 14 2025 05:55:00 GMT+0000 : totalRoCount666666,    29

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: Total Ros Count:-    29

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: im_count:     0
im_exception:     0
im_count less than zero

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: misc_ro_count:    117
estimate:    117

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: suffixed_invoices_count:1
punch time missing count :34.38 

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: misc_ro_count_numeric: 117
misc_ro_count:   117
misc_ro_count_numeric:117

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: suffixed_invoices_count_numeric:0

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: [1;36mFinding Missing RO  with respective to Invoice master CSV file[0m

Mon Jul 14 2025 05:55:00 GMT+0000 : stdout: [1;36mPersisting and Exporting Data and Schema[0m

Mon Jul 14 2025 05:55:01 GMT+0000 : stdout: [1;36mCreating New Status File![0m

Mon Jul 14 2025 05:55:01 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:01 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:01 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Started

Mon Jul 14 2025 05:55:02 GMT+0000 : stdout: [1;36mDetecting Open/Void RO data and reporting[0m

Mon Jul 14 2025 05:55:02 GMT+0000 : stdout: -----COPY RCI REPORT FILE TO AUDIT DIRECTORY----------PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335-REPORT.zip-----

Mon Jul 14 2025 05:55:02 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:02 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:02 GMT+0000 : stdout:   adding: invoice-master.csv
Mon Jul 14 2025 05:55:02 GMT+0000 : stdout:  (deflated 86%)

Mon Jul 14 2025 05:55:02 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Completed

Mon Jul 14 2025 05:55:03 GMT+0000 : stdout: [1;36mChecking for Missing ROs in Original Raw Data[0m

Mon Jul 14 2025 05:55:03 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:03 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:03 GMT+0000 : stdout: [1;36mNo missing RO#s found in extraction data[0m

Mon Jul 14 2025 05:55:03 GMT+0000 : stdout: Processor status: 7/16 Generate Config File Started

Mon Jul 14 2025 05:55:04 GMT+0000 : stdout: COMPANY_ID: *********

Mon Jul 14 2025 05:55:04 GMT+0000 : stdout:  Scanning for configuration files in: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result
 Config directory exists, searching for config_*.bash files...

Mon Jul 14 2025 05:55:04 GMT+0000 : stdout:  Found 1 config files: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result/config_*********.bash

Mon Jul 14 2025 05:55:04 GMT+0000 : stdout:  Examining config file: config_*********.bash

Mon Jul 14 2025 05:55:04 GMT+0000 : stdout:    Found COMPANY_ID: '*********'

Mon Jul 14 2025 05:55:04 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:04 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:04 GMT+0000 : stdout:  Added COMPANY_ID: ********* from config_*********.bash

Mon Jul 14 2025 05:55:04 GMT+0000 : stdout:  Total detected company IDs: 1
 Company IDs: *********
 Single company ID detected: *********
 Single company mode - COMPANY_ID='*********'
Processor status: 8/16 Load Fron Scheduler DB Started

Mon Jul 14 2025 05:55:05 GMT+0000 : stdout: [1;36mLoad data in Scheduler import database[0m

Mon Jul 14 2025 05:55:05 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 05:55:05 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 05:55:05 GMT+0000 : stdout: COPY 49

Mon Jul 14 2025 05:55:05 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 05:55:05 GMT+0000 : stdout: COPY 182

Mon Jul 14 2025 05:55:05 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 05:55:05 GMT+0000 : stdout: COPY 182

Mon Jul 14 2025 05:55:06 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 05:55:06 GMT+0000 : stdout: TRUNCATE TABLE

Mon Jul 14 2025 05:55:06 GMT+0000 : stdout: TRUNCATE TABLE

Mon Jul 14 2025 05:55:06 GMT+0000 : stdout: COMPANY_BRAND: GM

Mon Jul 14 2025 05:55:06 GMT+0000 : stdout: COPY 2

Mon Jul 14 2025 05:55:06 GMT+0000 : stdout: INSERT 0 1

Mon Jul 14 2025 05:55:06 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 05:55:06 GMT+0000 : stdout: COPY 63

Mon Jul 14 2025 05:55:06 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:06 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:06 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 05:55:06 GMT+0000 : stdout: Processor status: 8/16 Load Fron Scheduler DB Completed

Mon Jul 14 2025 05:55:07 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:07 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:07 GMT+0000 : stdout: Processor status: 9/16 Compressing Directory Started

Mon Jul 14 2025 05:55:08 GMT+0000 : stdout: 
zip error: Nothing to do! (try: zip -q -jmr import-files.zip . -i ./import-files)

Mon Jul 14 2025 05:55:08 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:08 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:08 GMT+0000 : stdout: Processor status: 9/16 Compressing Directory Completed

Mon Jul 14 2025 05:55:09 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:09 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:09 GMT+0000 : stdout: Processor status: 10/16 Generate Exception Analysis Started

Mon Jul 14 2025 05:55:10 GMT+0000 : stdout: [1;36m05:55:10 : Ready to Generate Exception Analysis Report[0m

Mon Jul 14 2025 05:55:10 GMT+0000 : stdout: CSV file '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result/All_exception_details.csv' already exists.

Mon Jul 14 2025 05:55:10 GMT+0000 : stdout: /home/<USER>/tmp/du-etl-dms-automate-extractor-work/exception_tag/All_exception_details.csv

Mon Jul 14 2025 05:55:10 GMT+0000 : stdout: File copied to /home/<USER>/tmp/du-etl-dms-automate-extractor-work/exception_tag/All_exception_details.csv

Mon Jul 14 2025 05:55:10 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:10 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:10 GMT+0000 : stdout: [1;36m05:55:10 : Done Generating Exception Analysis Report[0m

Mon Jul 14 2025 05:55:10 GMT+0000 : stdout: Processor status: 10/16 Generate Exception Analysis Completed

Mon Jul 14 2025 05:55:11 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:11 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:11 GMT+0000 : stdout: Processor status: 11/16 Loading Exception Analysis Started

Mon Jul 14 2025 05:55:12 GMT+0000 : stdout: [1;36m05:55:12 : Loading Exception Analysis Report[0m

Mon Jul 14 2025 05:55:12 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:12 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:12 GMT+0000 : stdout: [1;36m05:55:12 : Done Loading Exception Analysis Report[0m

Mon Jul 14 2025 05:55:12 GMT+0000 : stdout: Processor status: 11/16 Loading Exception Analysis Completed

Mon Jul 14 2025 05:55:13 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:13 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:13 GMT+0000 : stdout: Processor status: 12/16 Generating Proxy Repair Orders per Request Started

Mon Jul 14 2025 05:55:14 GMT+0000 : stdout:  Starting parallel proxy generation for multiple companies
 Company IDs: *********,

Mon Jul 14 2025 05:55:14 GMT+0000 : stdout:  Launching 1 parallel proxy generation jobs...
🔹 Launching proxy generation job for COMPANY_ID=*********

Mon Jul 14 2025 05:55:14 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:14 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:14 GMT+0000 : stdout:  Waiting for all 1 proxy generation jobs to complete...

Mon Jul 14 2025 05:55:14 GMT+0000 : stderr: /home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-library.bash-mixin: line 41: BASE_SRC_SCHEMA: unbound variable

Mon Jul 14 2025 05:55:14 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Mon Jul 14 2025 05:55:14 GMT+0000 : stdout:  All proxy generation jobs completed successfully!
Processor status: 12/16 Generating Proxy Repair Orders per Request Completed

Mon Jul 14 2025 05:55:15 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:15 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:15 GMT+0000 : stdout: Processor status: 13/16 Extracting Text ROs to TSV Started

Mon Jul 14 2025 05:55:16 GMT+0000 : stdout: [1;36m05:55:16 : Ready to Extract Text ROs to TSV[0m

Mon Jul 14 2025 05:55:16 GMT+0000 : stderr: Traceback (most recent call last):
  File "/home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/src/extract/python/./extract-text-proxy-to-tsv.py", line 14, in <module>

Mon Jul 14 2025 05:55:16 GMT+0000 : stderr:     for ro_file in os.listdir(proxy_file_path):
FileNotFoundError: [Errno 2] No such file or directory: '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice/text'

Mon Jul 14 2025 05:55:16 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Mon Jul 14 2025 05:55:16 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Mon Jul 14 2025 05:55:16 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:16 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:16 GMT+0000 : stdout: [1;36m05:55:16 : Done Extracting Text ROs to TSV[0m

Mon Jul 14 2025 05:55:16 GMT+0000 : stdout: Processor status: 13/16 Extracting Text ROs to TSV Completed

Mon Jul 14 2025 05:55:17 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:17 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:17 GMT+0000 : stdout: Processor status: 14/16 Compressing Proxy Directory Started

Mon Jul 14 2025 05:55:18 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:18 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:18 GMT+0000 : stdout: [1;36mcompress /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice[0m

Mon Jul 14 2025 05:55:18 GMT+0000 : stdout: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice

Mon Jul 14 2025 05:55:18 GMT+0000 : stderr: /home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-proxyinvoice.bash-mixin: line 151: cd: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice: No such file or directory
/home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-proxyinvoice.bash-mixin: line 181: cd: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice: No such file or directory

Mon Jul 14 2025 05:55:18 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Mon Jul 14 2025 05:55:18 GMT+0000 : stdout: Processor status: 14/16 Compressing Directory Completed

Mon Jul 14 2025 05:55:19 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:19 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:19 GMT+0000 : stdout: Processor status: 15/16 Pre-import Halt Detection Started

Mon Jul 14 2025 05:55:20 GMT+0000 : stdout: [1;36m
Mon Jul 14 2025 05:55:20 GMT+0000 : stdout: Generating Store Specific Rules[0m

Mon Jul 14 2025 05:55:20 GMT+0000 : stdout: Paytype import halt checking

Mon Jul 14 2025 05:55:20 GMT+0000 : stdout: Department import halt checking

Mon Jul 14 2025 05:55:20 GMT+0000 : stdout: Inv Seq halt checking

Mon Jul 14 2025 05:55:21 GMT+0000 : stdout: Unassigned Make import halt checking

Mon Jul 14 2025 05:55:21 GMT+0000 : stdout: paytype_halt     true
inv_seq_halt     true
make_halt        false

Mon Jul 14 2025 05:55:21 GMT+0000 : stdout: deptment_halt    false

Mon Jul 14 2025 05:55:21 GMT+0000 : stderr: rm: cannot remove '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/halt-import.txt'
Mon Jul 14 2025 05:55:21 GMT+0000 : stderr: : No such file or directory

Mon Jul 14 2025 05:55:21 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Mon Jul 14 2025 05:55:21 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Mon Jul 14 2025 05:55:21 GMT+0000 : stdout: COMPANY_ID: *********

Mon Jul 14 2025 05:55:21 GMT+0000 : stdout: [1;36mInserting Halt status to database[0m

Mon Jul 14 2025 05:55:21 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:21 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:21 GMT+0000 : stdout: Processor status: 15/16 Pre-import Halt Detection Completed

Mon Jul 14 2025 05:55:22 GMT+0000 : stdout: INPUT_TYPE ::: json
OLD_SCHEMA1 ::: du_dms_reynoldsrci_model

Mon Jul 14 2025 05:55:22 GMT+0000 : stdout: COMPANY_ID: *********

Mon Jul 14 2025 05:55:22 GMT+0000 : stdout: NEW_SCHEMA ::: du_dms_rc202507140553350038771752472475722_*********

Mon Jul 14 2025 05:55:22 GMT+0000 : stdout: OLD_SCHEMA ::: du_dms_reynoldsrci_model
NEW_SCHEMA ::: du_dms_rc202507140553350038771752472475722_*********
[1;36mProcessing schema duplication[0m

Mon Jul 14 2025 05:55:22 GMT+0000 : stderr: NOTICE:  schema "du_dms_rc202507140553350038771752472475722_*********" does not exist, skipping

Mon Jul 14 2025 05:55:22 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Mon Jul 14 2025 05:55:38 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:38 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:38 GMT+0000 : stdout: Dump file created for new Schema: 'du_dms_rc202507140553350038771752472475722_*********'

Mon Jul 14 2025 05:55:38 GMT+0000 : stdout: OLD_SCHEMA2 ::: du_dms_rc202507140553350038771752472475722_*********
Processor status: 16/16 Moving Work to Bundle Directory Started

Mon Jul 14 2025 05:55:39 GMT+0000 : stdout: [1;36mMoving all input files under the store to archive[0m

Mon Jul 14 2025 05:55:39 GMT+0000 : stdout: replacedElement:*_199001288329074__01_01_1_*.gz
199001288329074__01_01_1

Mon Jul 14 2025 05:55:39 GMT+0000 : stdout: archiveFilePath:/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/archive
inputFilePath:/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/*_199001288329074__01_01_1_*.gz
The inputFilePath does not contain 'archive@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@'.

Mon Jul 14 2025 05:55:39 GMT+0000 : stdout: [1;36mMoving Work to Bundle Directory[0m

Mon Jul 14 2025 05:55:42 GMT+0000 : stdout: [1;36mDistribute Function Result: 0[0m

Mon Jul 14 2025 05:55:42 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:42 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:42 GMT+0000 : stdout: Processor status: 16/16 Moving Work to Bundle Directory Completed

Mon Jul 14 2025 05:55:43 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:55:43 GMT+0000 : processorStatus
Mon Jul 14 2025 05:55:43 GMT+0000 : stdout: Processor status: Processing Completed

Mon Jul 14 2025 05:55:44 GMT+0000 : stdout: [1;36mClearing working directory and remove input if requested[0m

Mon Jul 14 2025 05:55:44 GMT+0000 : stdout: [1;32mZapping Input Zip File As Requested[0m

Mon Jul 14 2025 05:55:44 GMT+0000 : The invalid Core Cost Sale Mismatch File Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv
Mon Jul 14 2025 05:55:44 GMT+0000 : invalidmiscpaytypeArray.length: 17
Mon Jul 14 2025 05:55:44 GMT+0000 : invalidmiscpaytypeCount: 17
Mon Jul 14 2025 05:55:44 GMT+0000 : The estimate Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/estimate.csv
Mon Jul 14 2025 05:55:44 GMT+0000 : estimateArray.length: 94
Mon Jul 14 2025 05:55:44 GMT+0000 : invalidmiscpaytypeCount: 17
Mon Jul 14 2025 05:55:44 GMT+0000 : The Punch Time Missing Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing_percentage.txt
Mon Jul 14 2025 05:55:44 GMT+0000 : punchTimeMissingCount: undefined
Mon Jul 14 2025 05:55:44 GMT+0000 : The suffixedInvoices Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/suffixed-invoices.csv
Mon Jul 14 2025 05:55:44 GMT+0000 : suffixedInvoicesCount: undefined
Mon Jul 14 2025 05:55:44 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/exception-closed-invoices.csv
Mon Jul 14 2025 05:55:45 GMT+0000 : exceptionClosedInvoicesCount: undefined
Mon Jul 14 2025 05:55:45 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Mon Jul 14 2025 05:55:45 GMT+0000 : extraRoInXmlExceptionCount: undefined
Mon Jul 14 2025 05:55:45 GMT+0000 : The Extra Ro in Xml Exception File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Mon Jul 14 2025 05:55:45 GMT+0000 : extraRoInXmlExceptionCount: undefined
Mon Jul 14 2025 05:55:45 GMT+0000 : The imOpendedClosedRciRos File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/im_opened_closed_rci_ros.csv
Mon Jul 14 2025 05:55:45 GMT+0000 : imOpenedClosedRciRosCount: undefined
Mon Jul 14 2025 05:55:45 GMT+0000 : The deletedRosFilepath File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/removed_ros.csv
Mon Jul 14 2025 05:55:45 GMT+0000 : deletedRoscount: undefined
Mon Jul 14 2025 05:55:45 GMT+0000 : Reynolds : JSON processing job for Store undefined exited with code 0
Mon Jul 14 2025 05:55:45 GMT+0000 : Job saved to DB {"_id":"68749b9bce552b1ec03fcfa8","name":"REYNOLDSRCI-PROCESS-JSON","data":{"inputFile":"QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip","createdAt":"_01_01_20250714055335","operation":"json-processing","storeID":"936351325615505","inputFilePath1":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","processorUniqueId":"rc20250714055335003877-1752472475722","outputFile":"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip & /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/etl/PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335-ETL.zip","status":true,"message":"Success","warningMessage":{"scheduled_by":"<EMAIL>","invalidmiscpaytypeCount":17,"invalidmiscpaytypeFilePath":"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv","estimateCount":94,"punchTimeMissingCount":"34.38\n","PUNCH_TIME_MISSING_FILEPATH":"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv"},"invalidmiscpaytypeCount":17,"estimateCount":94,"punchTimeMissingCount":"34.38\n","suffixedInvoicesCsvData":""},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":"2025-07-14T05:55:44.988Z","type":"normal","nextRunAt":null,"lastRunAt":"2025-07-14T05:54:35.716Z"}
Mon Jul 14 2025 05:56:08 GMT+0000 : Call method for SharePoint data upload
Mon Jul 14 2025 05:56:08 GMT+0000 : Call for next job selection
Mon Jul 14 2025 05:56:08 GMT+0000 : Reynolds:  processing distribution
Mon Jul 14 2025 05:56:08 GMT+0000 : stdout: [1;36mDistributing ETL Data: PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip[0m

Mon Jul 14 2025 05:56:08 GMT+0000 : stdout: [1;36mDistributing ETL Data: PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip[0m

Mon Jul 14 2025 05:56:08 GMT+0000 : stdout: This store has import halt exception

Mon Jul 14 2025 05:56:10 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14"}
Mon Jul 14 2025 05:56:10 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":""}
Mon Jul 14 2025 05:56:10 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":""}
Mon Jul 14 2025 05:56:10 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:56:10.002Z"}]
Mon Jul 14 2025 05:56:10 GMT+0000 : Reynolds:storeIdentification : 199001288329074__01_01_1
Mon Jul 14 2025 05:56:10 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive
Mon Jul 14 2025 05:56:10 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Mon Jul 14 2025 05:56:10 GMT+0000 : Reynolds:gunzip done! : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Mon Jul 14 2025 05:56:10 GMT+0000 : Reynolds:Unzip Completed done!
Mon Jul 14 2025 05:56:10 GMT+0000 : Reynolds:unzip webhook input files completed
Mon Jul 14 2025 05:56:10 GMT+0000 : processCompanyData ...999 job:[object Object]userName:<EMAIL>:QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zipdestinationFile : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip
Mon Jul 14 2025 05:56:10 GMT+0000 : Job close: n/a
Mon Jul 14 2025 05:56:10 GMT+0000 : Reynolds : Extraction process for store QASREY523 exited code 0
Mon Jul 14 2025 05:56:12 GMT+0000 : stdout: Zip file /etl/audit-import-halt/PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335_rc20250714055335003877-1752472475722_*********-ETL.zip copied to manual import dir /etl/scheduler-manual-import

Mon Jul 14 2025 05:56:12 GMT+0000 : stdout: [1;36mDistributing Full Bundle[0m

Mon Jul 14 2025 05:56:12 GMT+0000 : stdout: '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip' -> '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250714055335.zip'

Mon Jul 14 2025 05:56:12 GMT+0000 : stdout: [1;32mBundle Distribution Completed[0m

Mon Jul 14 2025 05:56:12 GMT+0000 : stdout: [1;36mMoving Bundle to Archive[0m

Mon Jul 14 2025 05:56:12 GMT+0000 : close: Success
Mon Jul 14 2025 05:56:12 GMT+0000 : Reynolds : Found one Store extraction > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip to process now
Mon Jul 14 2025 05:56:12 GMT+0000 : Reynolds : Process JSON schedule started with file > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip
Mon Jul 14 2025 05:56:12 GMT+0000 : Groupname : QAREY52
Mon Jul 14 2025 05:56:12 GMT+0000 : Location Id: : 936351325615505
Mon Jul 14 2025 05:56:12 GMT+0000 : storeName : QASREY523
Mon Jul 14 2025 05:56:12 GMT+0000 : extractedFileTimeStamp : _01_01_20250714055610
Mon Jul 14 2025 05:56:12 GMT+0000 : extractedFileCreationDate : Invalid date
Mon Jul 14 2025 05:56:12 GMT+0000 : jobsTmp : [{"_id":"68709dd5eb5a72065669813e","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:15:03.002Z","uniqueId":"rc20250711051503002053","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip","endTime":"2025-07-11T05:15:03.080Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:15:03.000Z","lastFinishedAt":"2025-07-11T05:15:03.085Z","type":"normal","nextRunAt":"2025-07-14T05:56:12.356Z"},{"_id":"68709e7feb5a72065669815e","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:17:53.002Z","uniqueId":"rc20250711051753002067","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip","endTime":"2025-07-11T05:17:53.059Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:17:53.000Z","lastFinishedAt":"2025-07-11T05:17:53.060Z","type":"normal","nextRunAt":"2025-07-14T05:56:12.356Z"},{"_id":"68709f98eb5a720656698197","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:22:34.003Z","uniqueId":"rc20250711052234003879","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip","endTime":"2025-07-11T05:22:34.086Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:22:34.001Z","lastFinishedAt":"2025-07-11T05:22:34.087Z","type":"normal","nextRunAt":"2025-07-14T05:56:12.356Z"},{"_id":"6870a4b75d85ea3b100554fd","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:44:25.003Z","uniqueId":"rc20250711054425004693","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip","endTime":"2025-07-11T05:44:25.086Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:44:25.001Z","lastFinishedAt":"2025-07-11T05:44:25.087Z","type":"normal","nextRunAt":"2025-07-14T05:56:12.356Z"},{"_id":"68748f1415acc0ab8f03ba4e","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":true,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":true,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:31:19.159Z","uniqueId":"rc20250714053119160624","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714053119.zip","endTime":"2025-07-14T05:31:19.261Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-14T05:31:19.146Z","lastFinishedAt":"2025-07-14T05:31:19.274Z","type":"normal","nextRunAt":"2025-07-14T05:56:12.356Z"},{"_id":"687497dbce552b1ec03fcec4","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:38:37.003Z","uniqueId":"rc20250714053837003663","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714053837.zip","endTime":"2025-07-14T05:38:37.093Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-14T05:38:37.000Z","lastFinishedAt":"2025-07-14T05:38:37.094Z","type":"normal","nextRunAt":"2025-07-14T05:56:12.356Z"},{"_id":"6874995cce552b1ec03fcf20","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:45:02.003Z","uniqueId":"rc20250714054502003802","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714054502.zip","endTime":"2025-07-14T05:45:02.085Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-14T05:45:02.000Z","lastFinishedAt":"2025-07-14T05:45:02.086Z","type":"normal","nextRunAt":"2025-07-14T05:56:12.356Z"},{"_id":"687499c6ce552b1ec03fcf36","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:46:48.002Z","uniqueId":"rc20250714054648002250","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714054648.zip","endTime":"2025-07-14T05:46:48.079Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-14T05:46:48.000Z","lastFinishedAt":"2025-07-14T05:46:48.080Z","type":"normal","nextRunAt":"2025-07-14T05:56:12.356Z"},{"_id":"68749a46ce552b1ec03fcf55","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:48:55.002Z","uniqueId":"rc20250714054855002319","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714054855.zip","endTime":"2025-07-14T05:48:55.061Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-14T05:48:55.000Z","lastFinishedAt":"2025-07-14T05:48:55.062Z","type":"normal","nextRunAt":"2025-07-14T05:56:12.356Z"},{"_id":"68749b37ce552b1ec03fcf92","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:52:57.003Z","uniqueId":"rc20250714055257003683","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055257.zip","endTime":"2025-07-14T05:52:57.069Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-14T05:52:57.001Z","lastFinishedAt":"2025-07-14T05:52:57.070Z","type":"normal","nextRunAt":"2025-07-14T05:56:12.356Z"},{"_id":"68749bf8ce552b1ec03fcfc0","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:56:10.002Z","uniqueId":"rc20250714055610003227","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip","endTime":"2025-07-14T05:56:10.094Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-14T05:56:10.000Z","lastFinishedAt":"2025-07-14T05:56:10.095Z","type":"normal","nextRunAt":"2025-07-14T05:56:12.356Z"}]
Mon Jul 14 2025 05:56:12 GMT+0000 : Location Id : 936351325615505
Mon Jul 14 2025 05:56:12 GMT+0000 : jobsTmp[jobsTmp.length-1] : {"_id":"68749bf8ce552b1ec03fcfc0","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:56:10.002Z","uniqueId":"rc20250714055610003227","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip","endTime":"2025-07-14T05:56:10.094Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-14T05:56:10.000Z","lastFinishedAt":"2025-07-14T05:56:10.095Z","type":"normal","nextRunAt":"2025-07-14T05:56:12.356Z"}
Mon Jul 14 2025 05:56:12 GMT+0000 : jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:56:10.002Z","uniqueId":"rc20250714055610003227","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip","endTime":"2025-07-14T05:56:10.094Z","status":true,"message":"n/a"}]
Mon Jul 14 2025 05:56:12 GMT+0000 : agendaObject : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:56:10.002Z","uniqueId":"rc20250714055610003227","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip","endTime":"2025-07-14T05:56:10.094Z","status":true,"message":"n/a"}]
Mon Jul 14 2025 05:56:12 GMT+0000 : Sorted agenda object : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:56:10.002Z","uniqueId":"rc20250714055610003227","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip","endTime":"2025-07-14T05:56:10.094Z","status":true,"message":"n/a"}]
Mon Jul 14 2025 05:56:12 GMT+0000 : extractedObjectIndex : 0
Mon Jul 14 2025 05:56:12 GMT+0000 : Extracted agenda object : {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/14/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-14","invoiceMasterCSVFilePath":"","startTime":"2025-07-14T05:56:10.002Z","uniqueId":"rc20250714055610003227","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip","endTime":"2025-07-14T05:56:10.094Z","status":true,"message":"n/a"}
Mon Jul 14 2025 05:56:12 GMT+0000 : updateSolve360Data : [object Object]
Mon Jul 14 2025 05:56:12 GMT+0000 : projectId : *********
Mon Jul 14 2025 05:56:12 GMT+0000 : secondProjectId : 
Mon Jul 14 2025 05:56:12 GMT+0000 : userName : <EMAIL>
Mon Jul 14 2025 05:56:12 GMT+0000 : solve360Update : false
Mon Jul 14 2025 05:56:12 GMT+0000 : buildProxies : true
Mon Jul 14 2025 05:56:12 GMT+0000 : fopcStore : null
Mon Jul 14 2025 05:56:12 GMT+0000 : extractionId : 68749bf8ce552b1ec03fcfc0
Mon Jul 14 2025 05:56:12 GMT+0000 : invoiceMasterCSVFilePath : 
Mon Jul 14 2025 05:56:12 GMT+0000 : inputStoreName : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1
Mon Jul 14 2025 05:56:12 GMT+0000 : etlDMSType : ReynoldsRCI
Mon Jul 14 2025 05:56:12 GMT+0000 : switchBranch : true
Mon Jul 14 2025 05:56:12 GMT+0000 : haltOverRide : false
Mon Jul 14 2025 05:56:12 GMT+0000 : customBranchName : null
Mon Jul 14 2025 05:56:12 GMT+0000 : uniqueId : rc20250714055610003227-1752472572357
Mon Jul 14 2025 05:56:12 GMT+0000 : companyIds : *********,*********,
Mon Jul 14 2025 05:56:12 GMT+0000 : testData : false
Mon Jul 14 2025 05:56:12 GMT+0000 : companyObj : [object Object],[object Object]
Mon Jul 14 2025 05:56:12 GMT+0000 : mageManufacturer : GM
Mon Jul 14 2025 05:56:12 GMT+0000 : isPorscheStore : undefined
Mon Jul 14 2025 05:56:12 GMT+0000 : Reynolds : Start processing of extraction > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip
Mon Jul 14 2025 05:56:12 GMT+0000 : stdout: UUID: rc20250714055610003227-1752472572357

Mon Jul 14 2025 05:56:12 GMT+0000 : stdout: PERFORMED_BY: <EMAIL>
EXCEPTION_REPORT: true

Mon Jul 14 2025 05:56:12 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:12 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:12 GMT+0000 : stdout: Processor status: Processing Started

Mon Jul 14 2025 05:56:13 GMT+0000 : stdout: HALT_OVER_RIDE:false

Mon Jul 14 2025 05:56:13 GMT+0000 : stdout: [1;32mProcessing Input Zip Archive: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip[0m

Mon Jul 14 2025 05:56:13 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:13 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:13 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Started

Mon Jul 14 2025 05:56:14 GMT+0000 : stdout: [1;36mUnzipping Input to Work /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp[0m

Mon Jul 14 2025 05:56:14 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:14 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:14 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Completed

Mon Jul 14 2025 05:56:15 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:15 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:15 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Started

Mon Jul 14 2025 05:56:16 GMT+0000 : stdout: [1;36mCreating Schema from Models[0m

Mon Jul 14 2025 05:56:16 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:16 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:16 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Completed

Mon Jul 14 2025 05:56:17 GMT+0000 : stdout: [1;36mSaving UUID to model[0m

Mon Jul 14 2025 05:56:17 GMT+0000 : stdout: [1;36mGenerating JQ Transforms[0m

Mon Jul 14 2025 05:56:17 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:17 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:17 GMT+0000 : stdout: Processor status: 3/16 Iterating Over Zip File Contents Started

Mon Jul 14 2025 05:56:21 GMT+0000 : stdout: [1;36mIterating Over Zip File Contents[0m

Mon Jul 14 2025 05:56:21 GMT+0000 : stdout: [1;36mClosed RO JSON[0m

Mon Jul 14 2025 05:56:21 GMT+0000 : stderr: [1;36mBeginning zip processing in /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/jsonconversions ./reynolds0.xml[0m

Mon Jul 14 2025 05:56:21 GMT+0000 : stdout: [1;36mConvert ./reynolds0.xml to json file
Mon Jul 14 2025 05:56:21 GMT+0000 : stdout: [0m

Mon Jul 14 2025 05:56:22 GMT+0000 : stdout: Mon Jul 14 05:56:22 UTC 2025 Found # 29

Mon Jul 14 2025 05:56:22 GMT+0000 : stdout: Mon Jul 14 05:56:22 UTC 2025 Transform Begin

Mon Jul 14 2025 05:56:22 GMT+0000 : stdout: RO_COUNT is greater than 1

Mon Jul 14 2025 05:56:22 GMT+0000 : stdout: Mon Jul 14 05:56:22 UTC 2025 Transform End

Mon Jul 14 2025 05:56:22 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:22 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:22 GMT+0000 : stdout: Processor status: 4/16 Loading Individual ROs Started

Mon Jul 14 2025 05:56:23 GMT+0000 : stdout: [1;36mLoading Individual ROs (can take a while)[0m

Mon Jul 14 2025 05:56:23 GMT+0000 : stdout: Mon Jul 14 05:56:23 UTC 2025

Mon Jul 14 2025 05:56:23 GMT+0000 : stdout: [1;36mReady to Load remaining ROs[0m

Mon Jul 14 2025 05:56:33 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:33 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:33 GMT+0000 : stdout: Mon Jul 14 05:56:33 UTC 2025

Mon Jul 14 2025 05:56:33 GMT+0000 : stdout: [1;36mIndividual ROs Imported[0m
Processor status: 4/16 Loading Individual ROs Completed

Mon Jul 14 2025 05:56:34 GMT+0000 : stdout: [93m
[1mNo CCC Exception

Mon Jul 14 2025 05:56:34 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:34 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:34 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Started

Mon Jul 14 2025 05:56:35 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Mon Jul 14 2025 05:56:35 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Mon Jul 14 2025 05:56:35 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:35 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:35 GMT+0000 : stdout: ROs:

Mon Jul 14 2025 05:56:35 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Completed

Mon Jul 14 2025 05:56:36 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Mon Jul 14 2025 05:56:36 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Mon Jul 14 2025 05:56:36 GMT+0000 : stdout: ROs:

Mon Jul 14 2025 05:56:36 GMT+0000 : stdout: [1;36mCreating exclusion reports[0m

Mon Jul 14 2025 05:56:37 GMT+0000 : stdout: [1;36mGenerating exception report[0m

Mon Jul 14 2025 05:56:37 GMT+0000 : stdout: [1;36mEnumerating ROs[0m

Mon Jul 14 2025 05:56:37 GMT+0000 : stdout:  count_all | count_non_numeric 
-----------+-------------------
        29 |                 0
(1 row)


Mon Jul 14 2025 05:56:37 GMT+0000 : Total Ros Count55555555555,Total Ros Count:-    29

Mon Jul 14 2025 05:56:37 GMT+0000 : Total Ro90999999999,-    29

Mon Jul 14 2025 05:56:37 GMT+0000 : totalRoCount666666,    29

Mon Jul 14 2025 05:56:37 GMT+0000 : stdout: Total Ros Count:-    29

Mon Jul 14 2025 05:56:37 GMT+0000 : stdout: im_count:     0
im_exception:     0
im_count less than zero

Mon Jul 14 2025 05:56:37 GMT+0000 : stdout: misc_ro_count:    117

Mon Jul 14 2025 05:56:37 GMT+0000 : stdout: estimate:    117
suffixed_invoices_count:1
punch time missing count :34.38 

Mon Jul 14 2025 05:56:37 GMT+0000 : stdout: misc_ro_count_numeric: 117
misc_ro_count:   117

Mon Jul 14 2025 05:56:37 GMT+0000 : stdout: misc_ro_count_numeric:117
suffixed_invoices_count_numeric:0
[1;36mFinding Missing RO  with respective to Invoice master CSV file[0m

Mon Jul 14 2025 05:56:37 GMT+0000 : stdout: [1;36m
Mon Jul 14 2025 05:56:37 GMT+0000 : stdout: Persisting and Exporting Data and Schema[0m

Mon Jul 14 2025 05:56:38 GMT+0000 : stdout: [1;36mCreating New Status File![0m

Mon Jul 14 2025 05:56:38 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:38 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:38 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Started

Mon Jul 14 2025 05:56:39 GMT+0000 : stdout: [1;36mDetecting Open/Void RO data and reporting[0m

Mon Jul 14 2025 05:56:39 GMT+0000 : stdout: -----COPY RCI REPORT FILE TO AUDIT DIRECTORY----------PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610-REPORT.zip-----

Mon Jul 14 2025 05:56:39 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:39 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:39 GMT+0000 : stdout:   adding: invoice-master.csv
Mon Jul 14 2025 05:56:39 GMT+0000 : stdout:  (deflated 86%)
Processor status: 6/16 Detecting Open/Void RO data and reporting Completed

Mon Jul 14 2025 05:56:40 GMT+0000 : stdout: [1;36mChecking for Missing ROs in Original Raw Data[0m

Mon Jul 14 2025 05:56:40 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:40 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:40 GMT+0000 : stdout: [1;36mNo missing RO#s found in extraction data[0m

Mon Jul 14 2025 05:56:40 GMT+0000 : stdout: Processor status: 7/16 Generate Config File Started

Mon Jul 14 2025 05:56:41 GMT+0000 : stdout: COMPANY_ID: *********

Mon Jul 14 2025 05:56:41 GMT+0000 : stdout: COMPANY_ID: *********
 Scanning for configuration files in: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result
 Config directory exists, searching for config_*.bash files...

Mon Jul 14 2025 05:56:41 GMT+0000 : stdout:  Found 2 config files: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result/config_*********.bash /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result/config_*********.bash

Mon Jul 14 2025 05:56:41 GMT+0000 : stdout:  Examining config file: config_*********.bash

Mon Jul 14 2025 05:56:41 GMT+0000 : stdout:    Found COMPANY_ID: '*********'

Mon Jul 14 2025 05:56:41 GMT+0000 : stdout:  Added COMPANY_ID: ********* from config_*********.bash

Mon Jul 14 2025 05:56:41 GMT+0000 : stdout:  Examining config file: config_*********.bash

Mon Jul 14 2025 05:56:41 GMT+0000 : stdout:    Found COMPANY_ID: '*********'

Mon Jul 14 2025 05:56:41 GMT+0000 : stdout:  Added COMPANY_ID: ********* from config_*********.bash
 Total detected company IDs: 2
 Company IDs: ********* *********

Mon Jul 14 2025 05:56:41 GMT+0000 : Processor statu for UI
Mon Jul 14 2025 05:56:41 GMT+0000 : processorStatus
Mon Jul 14 2025 05:56:41 GMT+0000 : stdout:  Multiple company IDs detected: 2 companies
 Setting COMPANY_IDS='*********,*********'

Mon Jul 14 2025 05:56:41 GMT+0000 : stdout:  Setting PERFORM_PROXY_RO_BUILD='true'
 Parallel proxy generation enabled for companies: *********,*********
Processor status: 8/16 Load Fron Scheduler DB Started

Mon Jul 14 2025 05:56:42 GMT+0000 : stdout: [1;36mLoad data in Scheduler import database[0m

Mon Jul 14 2025 05:56:42 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 05:56:42 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 05:56:42 GMT+0000 : stdout: COPY 49

Mon Jul 14 2025 05:56:42 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 05:56:42 GMT+0000 : stdout: COPY 182

Mon Jul 14 2025 05:56:42 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 05:56:42 GMT+0000 : stdout: COPY 182

Mon Jul 14 2025 05:56:42 GMT+0000 : stdout: CREATE TABLE

Mon Jul 14 2025 05:56:42 GMT+0000 : stdout: TRUNCATE TABLE

Mon Jul 14 2025 05:56:42 GMT+0000 : stdout: TRUNCATE TABLE

Mon Jul 14 2025 05:56:42 GMT+0000 : stdout: COMPANY_BRAND: GM

Mon Jul 14 2025 05:56:43 GMT+0000 : stdout: COPY 2

Mon Jul 14 2025 05:56:43 GMT+0000 : stdout: INSERT 0 49

Mon Jul 14 2025 05:56:43 GMT+0000 : stderr: [1;31m
Mon Jul 14 2025 05:56:43 GMT+0000 : stderr: COMPANY_BRAND is empty or does not exist[0m

Mon Jul 14 2025 05:56:43 GMT+0000 : stdout: [1;33mMoving input to dead-letter bin: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/dead-letter-processed[0m

Mon Jul 14 2025 05:56:43 GMT+0000 : stderr: cp: cannot stat '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip': No such file or directory

Mon Jul 14 2025 05:56:43 GMT+0000 : The invalid Core Cost Sale Mismatch File Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv
Mon Jul 14 2025 05:56:43 GMT+0000 : invalidmiscpaytypeArray.length: 17
Mon Jul 14 2025 05:56:43 GMT+0000 : invalidmiscpaytypeCount: 17
Mon Jul 14 2025 05:56:43 GMT+0000 : The estimate Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/estimate.csv
Mon Jul 14 2025 05:56:43 GMT+0000 : estimateArray.length: 94
Mon Jul 14 2025 05:56:43 GMT+0000 : The Punch Time Missing Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing_percentage.txt
Mon Jul 14 2025 05:56:43 GMT+0000 : invalidmiscpaytypeCount: 17
Mon Jul 14 2025 05:56:43 GMT+0000 : punchTimeMissingCount: undefined
Mon Jul 14 2025 05:56:43 GMT+0000 : The suffixedInvoices Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/suffixed-invoices.csv
Mon Jul 14 2025 05:56:43 GMT+0000 : suffixedInvoicesCount: undefined
Mon Jul 14 2025 05:56:43 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/exception-closed-invoices.csv
Mon Jul 14 2025 05:56:43 GMT+0000 : exceptionClosedInvoicesCount: undefined
Mon Jul 14 2025 05:56:43 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Mon Jul 14 2025 05:56:43 GMT+0000 : extraRoInXmlExceptionCount: undefined
Mon Jul 14 2025 05:56:43 GMT+0000 : The Extra Ro in Xml Exception File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Mon Jul 14 2025 05:56:43 GMT+0000 : extraRoInXmlExceptionCount: undefined
Mon Jul 14 2025 05:56:43 GMT+0000 : The imOpendedClosedRciRos File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/im_opened_closed_rci_ros.csv
Mon Jul 14 2025 05:56:43 GMT+0000 : imOpenedClosedRciRosCount: undefined
Mon Jul 14 2025 05:56:43 GMT+0000 : The deletedRosFilepath File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/removed_ros.csv
Mon Jul 14 2025 05:56:43 GMT+0000 : deletedRoscount: undefined
Mon Jul 14 2025 05:56:43 GMT+0000 : Reynolds : JSON processing job for Store undefined exited with code 1
Mon Jul 14 2025 05:56:43 GMT+0000 : Autosoft : filePath inpObj Error - QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip
Mon Jul 14 2025 05:56:43 GMT+0000 : Reynolds : doPayloadAction inpObjProject - {"inProjectId":["*********","*********",""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-14","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/14/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-14\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-14T05:56:10.002Z\",\"uniqueId\":\"rc20250714055610003227\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip\",\"endTime\":\"2025-07-14T05:56:10.094Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Mon Jul 14 2025 05:56:43 GMT+0000 : Reynolds : doPayloadAction inpObjSecondProject - {"inProjectId":[""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-14","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/14/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-14\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-14T05:56:10.002Z\",\"uniqueId\":\"rc20250714055610003227\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip\",\"endTime\":\"2025-07-14T05:56:10.094Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Mon Jul 14 2025 05:56:43 GMT+0000 : Reynolds : doPayloadAction - {"inProjectId":["*********","*********",""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-14","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/14/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-14\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-14T05:56:10.002Z\",\"uniqueId\":\"rc20250714055610003227\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250714055610.zip\",\"endTime\":\"2025-07-14T05:56:10.094Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Mon Jul 14 2025 05:56:43 GMT+0000 : REYNOLDS Schedule portal call with Project Id FAILURE*********
Mon Jul 14 2025 05:56:43 GMT+0000 : REYNOLDS Schedule portal call with Project Id FAILURE*********
Mon Jul 14 2025 05:56:45 GMT+0000 : Call method for SharePoint data upload
Mon Jul 14 2025 05:56:45 GMT+0000 : Call for next job selection
Mon Jul 14 2025 05:56:45 GMT+0000 : Call for next job selection
